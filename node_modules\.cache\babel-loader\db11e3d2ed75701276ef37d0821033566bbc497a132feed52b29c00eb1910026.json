{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, withCtx as _withCtx, createBlock as _createBlock, resolveDynamicComponent as _resolveDynamicComponent, Transition as _Transition } from \"vue\";\nconst _hoisted_1 = {\n  class: \"app-container\"\n};\nconst _hoisted_2 = {\n  class: \"header\"\n};\nconst _hoisted_3 = {\n  class: \"container mx-auto px-4 py-3 flex justify-between items-center\"\n};\nconst _hoisted_4 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_5 = {\n  class: \"text-blue-600 mr-2\"\n};\nconst _hoisted_6 = {\n  class: \"p-2 text-gray-500 hover:text-gray-700\"\n};\nconst _hoisted_7 = {\n  class: \"container mx-auto px-4 py-6\"\n};\nconst _hoisted_8 = {\n  class: \"nav-tabs\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_router_link = _resolveComponent(\"router-link\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"header\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"text-2xl\"\n  })]), _cache[0] || (_cache[0] = _createElementVNode(\"h1\", {\n    class: \"text-xl font-semibold\"\n  }, \"密码管理系统\", -1 /* HOISTED */))]), _createElementVNode(\"div\", null, [_createElementVNode(\"button\", _hoisted_6, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'cog']\n  })])])])]), _createElementVNode(\"main\", _hoisted_7, [_createCommentVNode(\" 导航标签 \"), _createElementVNode(\"nav\", _hoisted_8, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.routes, route => {\n    return _openBlock(), _createBlock(_component_router_link, {\n      key: route.path,\n      to: route.path,\n      class: \"nav-item\",\n      \"active-class\": \"active\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', route.meta.icon],\n        class: \"mr-2\"\n      }, null, 8 /* PROPS */, [\"icon\"]), _createElementVNode(\"span\", null, _toDisplayString(route.meta.title), 1 /* TEXT */)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"to\"]);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 路由视图 \"), _createVNode(_component_router_view, null, {\n    default: _withCtx(({\n      Component\n    }) => [_createVNode(_Transition, {\n      name: \"fade\",\n      mode: \"out-in\"\n    }, {\n      default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(Component)))]),\n      _: 2 /* DYNAMIC */\n    }, 1024 /* DYNAMIC_SLOTS */)]),\n    _: 1 /* STABLE */\n  })])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_createVNode", "_component_font_awesome_icon", "icon", "_hoisted_6", "_hoisted_7", "_createCommentVNode", "_hoisted_8", "_Fragment", "_renderList", "$options", "routes", "route", "_createBlock", "_component_router_link", "key", "path", "to", "default", "_withCtx", "meta", "_toDisplayString", "title", "_", "_component_router_view", "Component", "_Transition", "name", "mode", "_resolveDynamicComponent"], "sources": ["D:\\demo\\ooo\\pass\\src\\App.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <header class=\"header\">\r\n            <div class=\"container mx-auto px-4 py-3 flex justify-between items-center\">\r\n                <div class=\"flex items-center\">\r\n                    <div class=\"text-blue-600 mr-2\">\r\n                        <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"text-2xl\" />\r\n                    </div>\r\n                    <h1 class=\"text-xl font-semibold\">密码管理系统</h1>\r\n                </div>\r\n                <div>\r\n                    <button class=\"p-2 text-gray-500 hover:text-gray-700\">\r\n                        <font-awesome-icon :icon=\"['fas', 'cog']\" />\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </header>\r\n\r\n        <main class=\"container mx-auto px-4 py-6\">\r\n            <!-- 导航标签 -->\r\n            <nav class=\"nav-tabs\">\r\n                <router-link v-for=\"route in routes\" :key=\"route.path\" :to=\"route.path\" class=\"nav-item\"\r\n                    active-class=\"active\">\r\n                    <font-awesome-icon :icon=\"['fas', route.meta.icon]\" class=\"mr-2\" />\r\n                    <span>{{ route.meta.title }}</span>\r\n                </router-link>\r\n            </nav>\r\n\r\n            <!-- 路由视图 -->\r\n            <router-view v-slot=\"{ Component }\">\r\n                <transition name=\"fade\" mode=\"out-in\">\r\n                    <component :is=\"Component\" />\r\n                </transition>\r\n            </router-view>\r\n        </main>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: 'App',\r\n    computed: {\r\n        routes() {\r\n            return this.$router.options.routes.filter(route => route.meta && route.meta.title)\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style>\r\nbody {\r\n    font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\r\n    background-color: #f8f9fa;\r\n    color: #333;\r\n}\r\n\r\n.header {\r\n    background-color: white;\r\n    border-bottom: 1px solid #e5e7eb;\r\n}\r\n\r\n.nav-tabs {\r\n    display: flex;\r\n    border-bottom: 1px solid #e5e7eb;\r\n    margin-bottom: 1.5rem;\r\n}\r\n\r\n.nav-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px 16px;\r\n    border-bottom: 2px solid transparent;\r\n    color: #666;\r\n    text-decoration: none;\r\n}\r\n\r\n.nav-item.active {\r\n    color: #2563eb;\r\n    border-bottom-color: #2563eb;\r\n    font-weight: 500;\r\n}\r\n\r\n.nav-item:hover:not(.active) {\r\n    color: #1e40af;\r\n    background-color: #f3f4f6;\r\n}\r\n\r\n/* 过渡效果 */\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n    transition: opacity 0.2s ease;\r\n}\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n    opacity: 0;\r\n}\r\n</style>"], "mappings": ";;EACSA,KAAK,EAAC;AAAe;;EACdA,KAAK,EAAC;AAAQ;;EACbA,KAAK,EAAC;AAA+D;;EACjEA,KAAK,EAAC;AAAmB;;EACrBA,KAAK,EAAC;AAAoB;;EAMvBA,KAAK,EAAC;AAAuC;;EAO3DA,KAAK,EAAC;AAA6B;;EAEhCA,KAAK,EAAC;AAAU;;;;;uBAnB7BC,mBAAA,CAkCM,OAlCNC,UAkCM,GAjCFC,mBAAA,CAcS,UAdTC,UAcS,GAbLD,mBAAA,CAYM,OAZNE,UAYM,GAXFF,mBAAA,CAKM,OALNG,UAKM,GAJFH,mBAAA,CAEM,OAFNI,UAEM,GADFC,YAAA,CAAoEC,4BAAA;IAAhDC,IAAI,EAAE,qBAAqB;IAAEV,KAAK,EAAC;kCAE3DG,mBAAA,CAA6C;IAAzCH,KAAK,EAAC;EAAuB,GAAC,QAAM,qB,GAE5CG,mBAAA,CAIM,cAHFA,mBAAA,CAES,UAFTQ,UAES,GADLH,YAAA,CAA4CC,4BAAA;IAAxBC,IAAI,EAAE;EAAc,G,SAMxDP,mBAAA,CAgBO,QAhBPS,UAgBO,GAfHC,mBAAA,UAAa,EACbV,mBAAA,CAMM,OANNW,UAMM,I,kBALFb,mBAAA,CAIcc,SAAA,QAzB9BC,WAAA,CAqB6CC,QAAA,CAAAC,MAAM,EAAfC,KAAK;yBAAzBC,YAAA,CAIcC,sBAAA;MAJwBC,GAAG,EAAEH,KAAK,CAACI,IAAI;MAAGC,EAAE,EAAEL,KAAK,CAACI,IAAI;MAAEvB,KAAK,EAAC,UAAU;MACpF,cAAY,EAAC;;MAtBjCyB,OAAA,EAAAC,QAAA,CAuBoB,MAAmE,CAAnElB,YAAA,CAAmEC,4BAAA;QAA/CC,IAAI,UAAUS,KAAK,CAACQ,IAAI,CAACjB,IAAI;QAAGV,KAAK,EAAC;yCAC1DG,mBAAA,CAAmC,cAAAyB,gBAAA,CAA1BT,KAAK,CAACQ,IAAI,CAACE,KAAK,iB;MAxB7CC,CAAA;;oCA4BYjB,mBAAA,UAAa,EACbL,YAAA,CAIcuB,sBAAA;IAjC1BN,OAAA,EAAAC,QAAA,CA8BgB,CAEa;MAHMM;IAAS,OAC5BxB,YAAA,CAEayB,WAAA;MAFDC,IAAI,EAAC,MAAM;MAACC,IAAI,EAAC;;MA9B7CV,OAAA,EAAAC,QAAA,CA+BoB,MAA6B,E,cAA7BN,YAAA,CAA6BgB,wBA/BjD,CA+BoCJ,SAAS,I;MA/B7CF,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}