{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, createVNode as _createVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, normalizeStyle as _normalizeStyle, TransitionGroup as _TransitionGroup, withCtx as _withCtx } from \"vue\";\nconst _hoisted_1 = {\n  class: \"notification-container\"\n};\nconst _hoisted_2 = {\n  class: \"p-4\"\n};\nconst _hoisted_3 = {\n  class: \"flex items-start\"\n};\nconst _hoisted_4 = {\n  class: \"flex-shrink-0\"\n};\nconst _hoisted_5 = {\n  class: \"ml-3 w-0 flex-1\"\n};\nconst _hoisted_6 = {\n  class: \"text-sm font-medium text-gray-900 dark:text-white\"\n};\nconst _hoisted_7 = {\n  class: \"mt-1 text-sm text-gray-500 dark:text-gray-400\"\n};\nconst _hoisted_8 = {\n  key: 0,\n  class: \"mt-3 flex space-x-2\"\n};\nconst _hoisted_9 = [\"onClick\"];\nconst _hoisted_10 = {\n  class: \"ml-4 flex-shrink-0 flex\"\n};\nconst _hoisted_11 = [\"onClick\"];\nconst _hoisted_12 = {\n  key: 0,\n  class: \"mt-3\"\n};\nconst _hoisted_13 = {\n  class: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createCommentVNode(\" 通知容器 \"), _createElementVNode(\"div\", _hoisted_1, [_createCommentVNode(\" 通知列表 \"), _createVNode(_TransitionGroup, {\n    name: \"notification\",\n    tag: \"div\",\n    class: \"fixed top-4 right-4 z-50 space-y-2\"\n  }, {\n    default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.notifications, notification => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: notification.id,\n        class: _normalizeClass([\"notification-item max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden\", $options.getNotificationClass(notification.type)])\n      }, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" 图标 \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", {\n        class: _normalizeClass([\"w-8 h-8 rounded-full flex items-center justify-center\", $options.getIconBgClass(notification.type)])\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $options.getNotificationIcon(notification.type)],\n        class: _normalizeClass([$options.getIconClass(notification.type), \"text-sm\"])\n      }, null, 8 /* PROPS */, [\"icon\", \"class\"])], 2 /* CLASS */)]), _createCommentVNode(\" 内容 \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"p\", _hoisted_6, _toDisplayString(notification.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_7, _toDisplayString(notification.message), 1 /* TEXT */), _createCommentVNode(\" 操作按钮 \"), notification.actions && notification.actions.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(notification.actions, action => {\n        return _openBlock(), _createElementBlock(\"button\", {\n          key: action.label,\n          onClick: $event => $options.handleAction(notification, action),\n          class: _normalizeClass([\"text-xs font-medium px-3 py-1 rounded-md transition-colors\", action.primary ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'])\n        }, _toDisplayString(action.label), 11 /* TEXT, CLASS, PROPS */, _hoisted_9);\n      }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 关闭按钮 \"), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"button\", {\n        onClick: $event => $options.removeNotification(notification.id),\n        class: \"bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'times'],\n        class: \"text-sm\"\n      })], 8 /* PROPS */, _hoisted_11)])]), _createCommentVNode(\" 进度条（用于自动消失的通知） \"), notification.autoClose && notification.duration ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", {\n        class: _normalizeClass([\"h-1 rounded-full transition-all duration-100 ease-linear\", $options.getProgressBarClass(notification.type)]),\n        style: _normalizeStyle({\n          width: $options.getProgressWidth(notification) + '%'\n        })\n      }, null, 6 /* CLASS, STYLE */)])])) : _createCommentVNode(\"v-if\", true)])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))]),\n    _: 1 /* STABLE */\n  })])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_Fragment", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_createVNode", "_TransitionGroup", "name", "tag", "default", "_withCtx", "_renderList", "$data", "notifications", "notification", "id", "_normalizeClass", "$options", "getNotificationClass", "type", "_hoisted_2", "_hoisted_3", "_hoisted_4", "getIconBgClass", "_component_font_awesome_icon", "icon", "getNotificationIcon", "getIconClass", "_hoisted_5", "_hoisted_6", "_toDisplayString", "title", "_hoisted_7", "message", "actions", "length", "_hoisted_8", "action", "label", "onClick", "$event", "handleAction", "primary", "_hoisted_9", "_hoisted_10", "removeNotification", "_hoisted_11", "autoClose", "duration", "_hoisted_12", "_hoisted_13", "getProgressBarClass", "style", "_normalizeStyle", "width", "getProgressWidth", "_"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\NotificationSystem.vue"], "sourcesContent": ["<template>\n  <!-- 通知容器 -->\n  <div class=\"notification-container\">\n    <!-- 通知列表 -->\n    <transition-group name=\"notification\" tag=\"div\" class=\"fixed top-4 right-4 z-50 space-y-2\">\n      <div\n        v-for=\"notification in notifications\"\n        :key=\"notification.id\"\n        class=\"notification-item max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden\"\n        :class=\"getNotificationClass(notification.type)\"\n      >\n        <div class=\"p-4\">\n          <div class=\"flex items-start\">\n            <!-- 图标 -->\n            <div class=\"flex-shrink-0\">\n              <div class=\"w-8 h-8 rounded-full flex items-center justify-center\" :class=\"getIconBgClass(notification.type)\">\n                <font-awesome-icon \n                  :icon=\"['fas', getNotificationIcon(notification.type)]\" \n                  :class=\"getIconClass(notification.type)\"\n                  class=\"text-sm\"\n                />\n              </div>\n            </div>\n            \n            <!-- 内容 -->\n            <div class=\"ml-3 w-0 flex-1\">\n              <p class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                {{ notification.title }}\n              </p>\n              <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n                {{ notification.message }}\n              </p>\n              \n              <!-- 操作按钮 -->\n              <div v-if=\"notification.actions && notification.actions.length > 0\" class=\"mt-3 flex space-x-2\">\n                <button\n                  v-for=\"action in notification.actions\"\n                  :key=\"action.label\"\n                  @click=\"handleAction(notification, action)\"\n                  class=\"text-xs font-medium px-3 py-1 rounded-md transition-colors\"\n                  :class=\"action.primary \n                    ? 'bg-blue-600 text-white hover:bg-blue-700' \n                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'\"\n                >\n                  {{ action.label }}\n                </button>\n              </div>\n            </div>\n            \n            <!-- 关闭按钮 -->\n            <div class=\"ml-4 flex-shrink-0 flex\">\n              <button\n                @click=\"removeNotification(notification.id)\"\n                class=\"bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                <font-awesome-icon :icon=\"['fas', 'times']\" class=\"text-sm\" />\n              </button>\n            </div>\n          </div>\n          \n          <!-- 进度条（用于自动消失的通知） -->\n          <div v-if=\"notification.autoClose && notification.duration\" class=\"mt-3\">\n            <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1\">\n              <div \n                class=\"h-1 rounded-full transition-all duration-100 ease-linear\"\n                :class=\"getProgressBarClass(notification.type)\"\n                :style=\"{ width: getProgressWidth(notification) + '%' }\"\n              ></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </transition-group>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'NotificationSystem',\n  data() {\n    return {\n      notifications: [],\n      nextId: 1,\n      timers: new Map()\n    }\n  },\n  \n  mounted() {\n    // 监听全局通知事件\n    this.$root.$on('show-notification', this.showNotification)\n    this.$root.$on('show-success', (message, title = '成功') => {\n      this.showNotification({ type: 'success', title, message })\n    })\n    this.$root.$on('show-error', (message, title = '错误') => {\n      this.showNotification({ type: 'error', title, message })\n    })\n    this.$root.$on('show-warning', (message, title = '警告') => {\n      this.showNotification({ type: 'warning', title, message })\n    })\n    this.$root.$on('show-info', (message, title = '信息') => {\n      this.showNotification({ type: 'info', title, message })\n    })\n  },\n  \n  beforeUnmount() {\n    // 清理事件监听器和定时器\n    this.$root.$off('show-notification')\n    this.$root.$off('show-success')\n    this.$root.$off('show-error')\n    this.$root.$off('show-warning')\n    this.$root.$off('show-info')\n    \n    this.timers.forEach(timer => clearInterval(timer.interval))\n  },\n  \n  methods: {\n    showNotification(options) {\n      const notification = {\n        id: this.nextId++,\n        type: options.type || 'info',\n        title: options.title || '',\n        message: options.message || '',\n        actions: options.actions || [],\n        autoClose: options.autoClose !== false,\n        duration: options.duration || 5000,\n        createdAt: Date.now(),\n        progress: 100\n      }\n      \n      this.notifications.push(notification)\n      \n      // 设置自动关闭\n      if (notification.autoClose) {\n        this.startAutoClose(notification)\n      }\n      \n      // 限制通知数量\n      if (this.notifications.length > 5) {\n        this.removeNotification(this.notifications[0].id)\n      }\n    },\n    \n    removeNotification(id) {\n      const index = this.notifications.findIndex(n => n.id === id)\n      if (index > -1) {\n        this.notifications.splice(index, 1)\n        \n        // 清理定时器\n        if (this.timers.has(id)) {\n          clearInterval(this.timers.get(id).interval)\n          this.timers.delete(id)\n        }\n      }\n    },\n    \n    startAutoClose(notification) {\n      const startTime = Date.now()\n      const interval = setInterval(() => {\n        const elapsed = Date.now() - startTime\n        const progress = Math.max(0, 100 - (elapsed / notification.duration) * 100)\n        \n        notification.progress = progress\n        \n        if (progress <= 0) {\n          this.removeNotification(notification.id)\n        }\n      }, 50)\n      \n      this.timers.set(notification.id, { interval, startTime })\n    },\n    \n    handleAction(notification, action) {\n      if (action.handler) {\n        action.handler(notification)\n      }\n      \n      if (action.closeOnClick !== false) {\n        this.removeNotification(notification.id)\n      }\n    },\n    \n    getNotificationClass(type) {\n      const classes = {\n        success: 'border-l-4 border-green-500',\n        error: 'border-l-4 border-red-500',\n        warning: 'border-l-4 border-yellow-500',\n        info: 'border-l-4 border-blue-500'\n      }\n      return classes[type] || classes.info\n    },\n    \n    getNotificationIcon(type) {\n      const icons = {\n        success: 'check-circle',\n        error: 'exclamation-circle',\n        warning: 'exclamation-triangle',\n        info: 'info-circle'\n      }\n      return icons[type] || icons.info\n    },\n    \n    getIconClass(type) {\n      const classes = {\n        success: 'text-green-600 dark:text-green-400',\n        error: 'text-red-600 dark:text-red-400',\n        warning: 'text-yellow-600 dark:text-yellow-400',\n        info: 'text-blue-600 dark:text-blue-400'\n      }\n      return classes[type] || classes.info\n    },\n    \n    getIconBgClass(type) {\n      const classes = {\n        success: 'bg-green-100 dark:bg-green-900/30',\n        error: 'bg-red-100 dark:bg-red-900/30',\n        warning: 'bg-yellow-100 dark:bg-yellow-900/30',\n        info: 'bg-blue-100 dark:bg-blue-900/30'\n      }\n      return classes[type] || classes.info\n    },\n    \n    getProgressBarClass(type) {\n      const classes = {\n        success: 'bg-green-500',\n        error: 'bg-red-500',\n        warning: 'bg-yellow-500',\n        info: 'bg-blue-500'\n      }\n      return classes[type] || classes.info\n    },\n    \n    getProgressWidth(notification) {\n      return notification.progress || 0\n    }\n  }\n}\n</script>\n\n<style scoped>\n.notification-container {\n  pointer-events: none;\n}\n\n.notification-item {\n  pointer-events: auto;\n}\n\n/* 通知动画 */\n.notification-enter-active {\n  transition: all 0.3s ease-out;\n}\n\n.notification-leave-active {\n  transition: all 0.3s ease-in;\n}\n\n.notification-enter-from {\n  opacity: 0;\n  transform: translateX(100%);\n}\n\n.notification-leave-to {\n  opacity: 0;\n  transform: translateX(100%);\n}\n\n.notification-move {\n  transition: transform 0.3s ease;\n}\n\n/* 响应式设计 */\n@media (max-width: 640px) {\n  .notification-item {\n    max-width: calc(100vw - 2rem);\n    margin: 0 1rem;\n  }\n}\n</style>\n"], "mappings": ";;EAEOA,KAAK,EAAC;AAAwB;;EASxBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAe;;EAWrBA,KAAK,EAAC;AAAiB;;EACvBA,KAAK,EAAC;AAAmD;;EAGzDA,KAAK,EAAC;AAA+C;;EA7BtEC,GAAA;EAkCkFD,KAAK,EAAC;;mBAlCxF;;EAkDiBA,KAAK,EAAC;AAAyB;oBAlDhD;;EAAAC,GAAA;EA6DsED,KAAK,EAAC;;;EAC3DA,KAAK,EAAC;AAAsD;;;uBA9D7EE,mBAAA,CAAAC,SAAA,SACEC,mBAAA,UAAa,EACbC,mBAAA,CAuEM,OAvENC,UAuEM,GAtEJF,mBAAA,UAAa,EACbG,YAAA,CAoEmBC,gBAAA;IApEDC,IAAI,EAAC,cAAc;IAACC,GAAG,EAAC,KAAK;IAACV,KAAK,EAAC;;IAJ1DW,OAAA,EAAAC,QAAA,CAMQ,MAAqC,E,kBADvCV,mBAAA,CAkEMC,SAAA,QAvEZU,WAAA,CAM+BC,KAAA,CAAAC,aAAa,EAA7BC,YAAY;2BADrBd,mBAAA,CAkEM;QAhEHD,GAAG,EAAEe,YAAY,CAACC,EAAE;QACrBjB,KAAK,EARbkB,eAAA,EAQc,uJAAuJ,EACrJC,QAAA,CAAAC,oBAAoB,CAACJ,YAAY,CAACK,IAAI;UAE9ChB,mBAAA,CA2DM,OA3DNiB,UA2DM,GA1DJjB,mBAAA,CA8CM,OA9CNkB,UA8CM,GA7CJnB,mBAAA,QAAW,EACXC,mBAAA,CAQM,OARNmB,UAQM,GAPJnB,mBAAA,CAMM;QANDL,KAAK,EAfxBkB,eAAA,EAeyB,uDAAuD,EAASC,QAAA,CAAAM,cAAc,CAACT,YAAY,CAACK,IAAI;UACzGd,YAAA,CAIEmB,4BAAA;QAHCC,IAAI,UAAUR,QAAA,CAAAS,mBAAmB,CAACZ,YAAY,CAACK,IAAI;QACnDrB,KAAK,EAlBxBkB,eAAA,EAkB0BC,QAAA,CAAAU,YAAY,CAACb,YAAY,CAACK,IAAI,GAChC,SAAS;qEAKrBjB,mBAAA,QAAW,EACXC,mBAAA,CAsBM,OAtBNyB,UAsBM,GArBJzB,mBAAA,CAEI,KAFJ0B,UAEI,EAAAC,gBAAA,CADChB,YAAY,CAACiB,KAAK,kBAEvB5B,mBAAA,CAEI,KAFJ6B,UAEI,EAAAF,gBAAA,CADChB,YAAY,CAACmB,OAAO,kBAGzB/B,mBAAA,UAAa,EACFY,YAAY,CAACoB,OAAO,IAAIpB,YAAY,CAACoB,OAAO,CAACC,MAAM,Q,cAA9DnC,mBAAA,CAYM,OAZNoC,UAYM,I,kBAXJpC,mBAAA,CAUSC,SAAA,QA7CzBU,WAAA,CAoCmCG,YAAY,CAACoB,OAAO,EAA9BG,MAAM;6BADfrC,mBAAA,CAUS;UARND,GAAG,EAAEsC,MAAM,CAACC,KAAK;UACjBC,OAAK,EAAAC,MAAA,IAAEvB,QAAA,CAAAwB,YAAY,CAAC3B,YAAY,EAAEuB,MAAM;UACzCvC,KAAK,EAvCvBkB,eAAA,EAuCwB,4DAA4D,EAC1DqB,MAAM,CAACK,OAAO,G;4BAInBL,MAAM,CAACC,KAAK,gCA5CjCK,UAAA;0CAAAzC,mBAAA,e,GAiDYA,mBAAA,UAAa,EACbC,mBAAA,CAOM,OAPNyC,WAOM,GANJzC,mBAAA,CAKS;QAJNoC,OAAK,EAAAC,MAAA,IAAEvB,QAAA,CAAA4B,kBAAkB,CAAC/B,YAAY,CAACC,EAAE;QAC1CjB,KAAK,EAAC;UAENO,YAAA,CAA8DmB,4BAAA;QAA1CC,IAAI,EAAE,gBAAgB;QAAE3B,KAAK,EAAC;0BAvDlEgD,WAAA,E,KA4DU5C,mBAAA,oBAAuB,EACZY,YAAY,CAACiC,SAAS,IAAIjC,YAAY,CAACkC,QAAQ,I,cAA1DhD,mBAAA,CAQM,OARNiD,WAQM,GAPJ9C,mBAAA,CAMM,OANN+C,WAMM,GALJ/C,mBAAA,CAIO;QAHLL,KAAK,EAhErBkB,eAAA,EAgEsB,0DAA0D,EACxDC,QAAA,CAAAkC,mBAAmB,CAACrC,YAAY,CAACK,IAAI;QAC5CiC,KAAK,EAlEtBC,eAAA;UAAAC,KAAA,EAkEiCrC,QAAA,CAAAsC,gBAAgB,CAACzC,YAAY;QAAA;4CAlE9DZ,mBAAA,e;;IAAAsD,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}