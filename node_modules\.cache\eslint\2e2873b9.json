[{"D:\\demo\\ooo\\pass\\src\\main.js": "1", "D:\\demo\\ooo\\pass\\src\\App.vue": "2", "D:\\demo\\ooo\\pass\\src\\router\\index.js": "3", "D:\\demo\\ooo\\pass\\src\\store\\index.js": "4", "D:\\demo\\ooo\\pass\\src\\views\\PasswordPolicies.vue": "5", "D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue": "6", "D:\\demo\\ooo\\pass\\src\\views\\ScheduledTasks.vue": "7", "D:\\demo\\ooo\\pass\\src\\components\\PasswordStrengthMeter.vue": "8", "D:\\demo\\ooo\\pass\\src\\components\\StatusBadge.vue": "9", "D:\\demo\\ooo\\pass\\src\\components\\CustomCheckbox.vue": "10", "D:\\demo\\ooo\\pass\\src\\components\\BaseModal.vue": "11"}, {"size": 1054, "mtime": 1745393141704, "results": "12", "hashOfConfig": "13"}, {"size": 2608, "mtime": 1745390772690, "results": "14", "hashOfConfig": "13"}, {"size": 1092, "mtime": 1745389534898, "results": "15", "hashOfConfig": "13"}, {"size": 12674, "mtime": 1745395657985, "results": "16", "hashOfConfig": "13"}, {"size": 16724, "mtime": 1745393320344, "results": "17", "hashOfConfig": "13"}, {"size": 42298, "mtime": 1745395632017, "results": "18", "hashOfConfig": "13"}, {"size": 20744, "mtime": 1745394543431, "results": "19", "hashOfConfig": "13"}, {"size": 2124, "mtime": 1745389651932, "results": "20", "hashOfConfig": "13"}, {"size": 639, "mtime": 1745389673039, "results": "21", "hashOfConfig": "13"}, {"size": 512, "mtime": 1745389662414, "results": "22", "hashOfConfig": "13"}, {"size": 5242, "mtime": 1745394639794, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "16pt2e3", {"filePath": "26", "messages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "28"}, {"filePath": "29", "messages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "31"}, {"filePath": "32", "messages": "33", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "28"}, {"filePath": "42", "messages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "28"}, {"filePath": "44", "messages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "28"}, {"filePath": "46", "messages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\demo\\ooo\\pass\\src\\main.js", [], "D:\\demo\\ooo\\pass\\src\\App.vue", [], [], "D:\\demo\\ooo\\pass\\src\\router\\index.js", [], [], "D:\\demo\\ooo\\pass\\src\\store\\index.js", ["48", "49", "50"], "D:\\demo\\ooo\\pass\\src\\views\\PasswordPolicies.vue", [], "D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue", [], "D:\\demo\\ooo\\pass\\src\\views\\ScheduledTasks.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\PasswordStrengthMeter.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\StatusBadge.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\CustomCheckbox.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\BaseModal.vue", [], {"ruleId": "51", "severity": 2, "message": "52", "line": 211, "column": 26, "nodeType": "53", "messageId": "54", "endLine": 211, "endColumn": 32}, {"ruleId": "51", "severity": 2, "message": "52", "line": 251, "column": 36, "nodeType": "53", "messageId": "54", "endLine": 251, "endColumn": 42}, {"ruleId": "51", "severity": 2, "message": "55", "line": 274, "column": 31, "nodeType": "53", "messageId": "54", "endLine": 274, "endColumn": 39}, "no-unused-vars", "'commit' is defined but never used. Allowed unused args must match /^_/u.", "Identifier", "unusedVar", "'policyId' is defined but never used. Allowed unused args must match /^_/u."]