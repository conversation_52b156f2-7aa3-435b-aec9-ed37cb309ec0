{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, createVNode as _createVNode, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"space-y-6\"\n};\nconst _hoisted_2 = {\n  class: \"flex items-center justify-between\"\n};\nconst _hoisted_3 = {\n  class: \"flex space-x-3\"\n};\nconst _hoisted_4 = {\n  class: \"grid grid-cols-1 lg:grid-cols-2 gap-6\"\n};\nconst _hoisted_5 = {\n  class: \"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\"\n};\nconst _hoisted_6 = {\n  class: \"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\"\n};\nconst _hoisted_7 = {\n  class: \"space-y-4\"\n};\nconst _hoisted_8 = {\n  class: \"flex items-center space-x-3\"\n};\nconst _hoisted_9 = {\n  class: \"text-sm font-medium text-gray-900 dark:text-white\"\n};\nconst _hoisted_10 = {\n  class: \"text-right\"\n};\nconst _hoisted_11 = {\n  class: \"text-xs text-gray-500 dark:text-gray-400\"\n};\nconst _hoisted_12 = {\n  class: \"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\"\n};\nconst _hoisted_13 = {\n  class: \"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\"\n};\nconst _hoisted_14 = {\n  class: \"space-y-4\"\n};\nconst _hoisted_15 = {\n  class: \"flex-shrink-0 mt-1\"\n};\nconst _hoisted_16 = {\n  class: \"flex-1 min-w-0\"\n};\nconst _hoisted_17 = {\n  class: \"flex items-center justify-between\"\n};\nconst _hoisted_18 = {\n  class: \"text-sm font-medium text-gray-900 dark:text-white\"\n};\nconst _hoisted_19 = {\n  class: \"text-xs text-gray-500 dark:text-gray-400\"\n};\nconst _hoisted_20 = {\n  class: \"text-sm text-gray-600 dark:text-gray-400\"\n};\nconst _hoisted_21 = {\n  key: 0,\n  class: \"mt-1\"\n};\nconst _hoisted_22 = {\n  class: \"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800 p-6\"\n};\nconst _hoisted_23 = {\n  class: \"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\"\n};\nconst _hoisted_24 = {\n  class: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\"\n};\nconst _hoisted_25 = {\n  class: \"flex items-start space-x-3\"\n};\nconst _hoisted_26 = {\n  class: \"flex-shrink-0\"\n};\nconst _hoisted_27 = {\n  class: \"flex-1\"\n};\nconst _hoisted_28 = {\n  class: \"text-sm font-medium text-gray-900 dark:text-white\"\n};\nconst _hoisted_29 = {\n  class: \"text-xs text-gray-600 dark:text-gray-400 mt-1\"\n};\nconst _hoisted_30 = [\"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_SecurityDashboard = _resolveComponent(\"SecurityDashboard\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 页面标题 \"), _createElementVNode(\"div\", _hoisted_2, [_cache[4] || (_cache[4] = _createElementVNode(\"div\", null, [_createElementVNode(\"h1\", {\n    class: \"text-2xl font-bold text-gray-900 dark:text-white\"\n  }, \"安全概览\"), _createElementVNode(\"p\", {\n    class: \"text-gray-600 dark:text-gray-400\"\n  }, \"密码安全状态监控与分析\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.refreshData && $options.refreshData(...args)),\n    class: \"inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'sync-alt'],\n    class: _normalizeClass([\"mr-2\", {\n      'animate-spin': $data.refreshing\n    }])\n  }, null, 8 /* PROPS */, [\"class\"]), _cache[2] || (_cache[2] = _createTextVNode(\" 刷新数据 \"))]), _createElementVNode(\"button\", {\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.exportReport && $options.exportReport(...args)),\n    class: \"inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'download'],\n    class: \"mr-2\"\n  }), _cache[3] || (_cache[3] = _createTextVNode(\" 导出报告 \"))])])]), _createCommentVNode(\" 安全仪表板 \"), _createVNode(_component_SecurityDashboard, {\n    hosts: _ctx.hosts\n  }, null, 8 /* PROPS */, [\"hosts\"]), _createCommentVNode(\" 详细分析 \"), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" 密码合规性分析 \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"h3\", _hoisted_6, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-check'],\n    class: \"mr-2 text-green-500\"\n  }), _cache[5] || (_cache[5] = _createTextVNode(\" 密码合规性分析 \"))]), _createElementVNode(\"div\", _hoisted_7, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.complianceData, compliance => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: compliance.rule,\n      class: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\"\n    }, [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"w-3 h-3 rounded-full\", compliance.status === 'pass' ? 'bg-green-500' : 'bg-red-500'])\n    }, null, 2 /* CLASS */), _createElementVNode(\"span\", _hoisted_9, _toDisplayString(compliance.rule), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"text-sm font-semibold\", compliance.status === 'pass' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'])\n    }, _toDisplayString(compliance.passRate) + \"% \", 3 /* TEXT, CLASS */), _createElementVNode(\"div\", _hoisted_11, _toDisplayString(compliance.passed) + \"/\" + _toDisplayString(compliance.total), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 安全事件时间线 \"), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"h3\", _hoisted_13, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'clock'],\n    class: \"mr-2 text-blue-500\"\n  }), _cache[6] || (_cache[6] = _createTextVNode(\" 安全事件时间线 \"))]), _createElementVNode(\"div\", _hoisted_14, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.securityEvents, event => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: event.id,\n      class: \"flex items-start space-x-3\"\n    }, [_createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"w-8 h-8 rounded-full flex items-center justify-center\", event.iconBg])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', event.icon],\n      class: _normalizeClass([event.iconColor, \"text-sm\"])\n    }, null, 8 /* PROPS */, [\"icon\", \"class\"])], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"p\", _hoisted_18, _toDisplayString(event.title), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_19, _toDisplayString(event.time), 1 /* TEXT */)]), _createElementVNode(\"p\", _hoisted_20, _toDisplayString(event.description), 1 /* TEXT */), event.severity ? (_openBlock(), _createElementBlock(\"div\", _hoisted_21, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium\", $options.getSeverityClass(event.severity)])\n    }, _toDisplayString(event.severity), 3 /* TEXT, CLASS */)])) : _createCommentVNode(\"v-if\", true)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])]), _createCommentVNode(\" 推荐操作 \"), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"h3\", _hoisted_23, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'lightbulb'],\n    class: \"mr-2 text-yellow-500\"\n  }), _cache[7] || (_cache[7] = _createTextVNode(\" 安全建议 \"))]), _createElementVNode(\"div\", _hoisted_24, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.recommendations, recommendation => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: recommendation.id,\n      class: \"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700\"\n    }, [_createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"w-8 h-8 rounded-lg flex items-center justify-center\", recommendation.iconBg])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', recommendation.icon],\n      class: _normalizeClass([recommendation.iconColor, \"text-sm\"])\n    }, null, 8 /* PROPS */, [\"icon\", \"class\"])], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"h4\", _hoisted_28, _toDisplayString(recommendation.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_29, _toDisplayString(recommendation.description), 1 /* TEXT */), _createElementVNode(\"button\", {\n      onClick: $event => $options.executeRecommendation(recommendation),\n      class: \"mt-2 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium\"\n    }, \" 立即执行 \", 8 /* PROPS */, _hoisted_30)])])]);\n  }), 128 /* KEYED_FRAGMENT */))])])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "args", "$options", "refreshData", "_createVNode", "_component_font_awesome_icon", "icon", "_normalizeClass", "$data", "refreshing", "_createTextVNode", "exportReport", "_component_SecurityDashboard", "hosts", "_ctx", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_Fragment", "_renderList", "complianceData", "compliance", "rule", "_hoisted_8", "status", "_hoisted_9", "_toDisplayString", "_hoisted_10", "passRate", "_hoisted_11", "passed", "total", "_hoisted_12", "_hoisted_13", "_hoisted_14", "securityEvents", "event", "id", "_hoisted_15", "iconBg", "iconColor", "_hoisted_16", "_hoisted_17", "_hoisted_18", "title", "_hoisted_19", "time", "_hoisted_20", "description", "severity", "_hoisted_21", "getSeverityClass", "_hoisted_22", "_hoisted_23", "_hoisted_24", "recommendations", "recommendation", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "$event", "executeRecommendation", "_hoisted_30"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\SecurityOverview.vue"], "sourcesContent": ["<template>\n  <div class=\"space-y-6\">\n    <!-- 页面标题 -->\n    <div class=\"flex items-center justify-between\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">安全概览</h1>\n        <p class=\"text-gray-600 dark:text-gray-400\">密码安全状态监控与分析</p>\n      </div>\n      <div class=\"flex space-x-3\">\n        <button \n          @click=\"refreshData\"\n          class=\"inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        >\n          <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-2\" :class=\"{ 'animate-spin': refreshing }\" />\n          刷新数据\n        </button>\n        <button \n          @click=\"exportReport\"\n          class=\"inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        >\n          <font-awesome-icon :icon=\"['fas', 'download']\" class=\"mr-2\" />\n          导出报告\n        </button>\n      </div>\n    </div>\n\n    <!-- 安全仪表板 -->\n    <SecurityDashboard :hosts=\"hosts\" />\n\n    <!-- 详细分析 -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      <!-- 密码合规性分析 -->\n      <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <h3 class=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <font-awesome-icon :icon=\"['fas', 'shield-check']\" class=\"mr-2 text-green-500\" />\n          密码合规性分析\n        </h3>\n        \n        <div class=\"space-y-4\">\n          <div v-for=\"compliance in complianceData\" :key=\"compliance.rule\" class=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n            <div class=\"flex items-center space-x-3\">\n              <div class=\"w-3 h-3 rounded-full\" :class=\"compliance.status === 'pass' ? 'bg-green-500' : 'bg-red-500'\"></div>\n              <span class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ compliance.rule }}</span>\n            </div>\n            <div class=\"text-right\">\n              <div class=\"text-sm font-semibold\" :class=\"compliance.status === 'pass' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'\">\n                {{ compliance.passRate }}%\n              </div>\n              <div class=\"text-xs text-gray-500 dark:text-gray-400\">{{ compliance.passed }}/{{ compliance.total }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 安全事件时间线 -->\n      <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <h3 class=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n          <font-awesome-icon :icon=\"['fas', 'clock']\" class=\"mr-2 text-blue-500\" />\n          安全事件时间线\n        </h3>\n        \n        <div class=\"space-y-4\">\n          <div v-for=\"event in securityEvents\" :key=\"event.id\" class=\"flex items-start space-x-3\">\n            <div class=\"flex-shrink-0 mt-1\">\n              <div class=\"w-8 h-8 rounded-full flex items-center justify-center\" :class=\"event.iconBg\">\n                <font-awesome-icon :icon=\"['fas', event.icon]\" :class=\"event.iconColor\" class=\"text-sm\" />\n              </div>\n            </div>\n            <div class=\"flex-1 min-w-0\">\n              <div class=\"flex items-center justify-between\">\n                <p class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ event.title }}</p>\n                <span class=\"text-xs text-gray-500 dark:text-gray-400\">{{ event.time }}</span>\n              </div>\n              <p class=\"text-sm text-gray-600 dark:text-gray-400\">{{ event.description }}</p>\n              <div v-if=\"event.severity\" class=\"mt-1\">\n                <span class=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium\" :class=\"getSeverityClass(event.severity)\">\n                  {{ event.severity }}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 推荐操作 -->\n    <div class=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800 p-6\">\n      <h3 class=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n        <font-awesome-icon :icon=\"['fas', 'lightbulb']\" class=\"mr-2 text-yellow-500\" />\n        安全建议\n      </h3>\n      \n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        <div v-for=\"recommendation in recommendations\" :key=\"recommendation.id\" class=\"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700\">\n          <div class=\"flex items-start space-x-3\">\n            <div class=\"flex-shrink-0\">\n              <div class=\"w-8 h-8 rounded-lg flex items-center justify-center\" :class=\"recommendation.iconBg\">\n                <font-awesome-icon :icon=\"['fas', recommendation.icon]\" :class=\"recommendation.iconColor\" class=\"text-sm\" />\n              </div>\n            </div>\n            <div class=\"flex-1\">\n              <h4 class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ recommendation.title }}</h4>\n              <p class=\"text-xs text-gray-600 dark:text-gray-400 mt-1\">{{ recommendation.description }}</p>\n              <button \n                @click=\"executeRecommendation(recommendation)\"\n                class=\"mt-2 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium\"\n              >\n                立即执行\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapState } from 'vuex'\nimport SecurityDashboard from '@/components/SecurityDashboard.vue'\n\nexport default {\n  name: 'SecurityOverview',\n  components: {\n    SecurityDashboard\n  },\n  data() {\n    return {\n      refreshing: false,\n      complianceData: [\n        { rule: '密码最小长度', status: 'pass', passRate: 95, passed: 38, total: 40 },\n        { rule: '包含大写字母', status: 'pass', passRate: 88, passed: 35, total: 40 },\n        { rule: '包含特殊字符', status: 'fail', passRate: 72, passed: 29, total: 40 },\n        { rule: '定期更新', status: 'fail', passRate: 65, passed: 26, total: 40 },\n        { rule: '不重复历史密码', status: 'pass', passRate: 92, passed: 37, total: 40 }\n      ],\n      securityEvents: [\n        {\n          id: 1,\n          title: '检测到弱密码',\n          description: 'server-003 的 root 账号使用了弱密码',\n          time: '5分钟前',\n          severity: '高',\n          icon: 'exclamation-triangle',\n          iconColor: 'text-red-600',\n          iconBg: 'bg-red-100 dark:bg-red-900/30'\n        },\n        {\n          id: 2,\n          title: '密码策略更新',\n          description: '高强度策略已应用到生产环境',\n          time: '1小时前',\n          severity: '信息',\n          icon: 'shield-alt',\n          iconColor: 'text-blue-600',\n          iconBg: 'bg-blue-100 dark:bg-blue-900/30'\n        },\n        {\n          id: 3,\n          title: '批量密码更新完成',\n          description: '测试环境15台服务器密码更新成功',\n          time: '3小时前',\n          severity: '成功',\n          icon: 'check-circle',\n          iconColor: 'text-green-600',\n          iconBg: 'bg-green-100 dark:bg-green-900/30'\n        },\n        {\n          id: 4,\n          title: '密码即将过期提醒',\n          description: '5台服务器的密码将在3天内过期',\n          time: '6小时前',\n          severity: '警告',\n          icon: 'clock',\n          iconColor: 'text-yellow-600',\n          iconBg: 'bg-yellow-100 dark:bg-yellow-900/30'\n        }\n      ],\n      recommendations: [\n        {\n          id: 1,\n          title: '更新弱密码',\n          description: '发现3个弱密码，建议立即更新',\n          icon: 'key',\n          iconColor: 'text-red-600',\n          iconBg: 'bg-red-100 dark:bg-red-900/30',\n          action: 'update-weak-passwords'\n        },\n        {\n          id: 2,\n          title: '应用安全策略',\n          description: '为新主机应用标准安全策略',\n          icon: 'shield-alt',\n          iconColor: 'text-blue-600',\n          iconBg: 'bg-blue-100 dark:bg-blue-900/30',\n          action: 'apply-security-policy'\n        },\n        {\n          id: 3,\n          title: '设置定时任务',\n          description: '配置自动密码更新任务',\n          icon: 'clock',\n          iconColor: 'text-green-600',\n          iconBg: 'bg-green-100 dark:bg-green-900/30',\n          action: 'setup-scheduled-task'\n        }\n      ]\n    }\n  },\n  computed: {\n    ...mapState(['hosts'])\n  },\n  methods: {\n    async refreshData() {\n      this.refreshing = true\n      try {\n        // 模拟数据刷新\n        await new Promise(resolve => setTimeout(resolve, 1000))\n        this.$root.$emit('show-success', '数据已刷新', '刷新成功')\n      } catch (error) {\n        this.$root.$emit('show-error', '数据刷新失败')\n      } finally {\n        this.refreshing = false\n      }\n    },\n    \n    async exportReport() {\n      try {\n        // 模拟报告导出\n        this.$root.$emit('show-info', '正在生成安全报告...', '导出中')\n        await new Promise(resolve => setTimeout(resolve, 2000))\n        this.$root.$emit('show-success', '安全报告已导出到下载文件夹', '导出成功')\n      } catch (error) {\n        this.$root.$emit('show-error', '报告导出失败')\n      }\n    },\n    \n    getSeverityClass(severity) {\n      const classes = {\n        '高': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',\n        '中': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',\n        '低': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',\n        '警告': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',\n        '成功': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',\n        '信息': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'\n      }\n      return classes[severity] || classes['信息']\n    },\n    \n    executeRecommendation(recommendation) {\n      // 根据推荐操作类型执行相应的操作\n      switch (recommendation.action) {\n        case 'update-weak-passwords':\n          this.$router.push('/hosts')\n          this.$root.$emit('show-info', '已跳转到主机管理页面，请选择需要更新的主机')\n          break\n        case 'apply-security-policy':\n          this.$router.push('/policies')\n          this.$root.$emit('show-info', '已跳转到密码策略页面')\n          break\n        case 'setup-scheduled-task':\n          this.$router.push('/tasks')\n          this.$root.$emit('show-info', '已跳转到定时任务页面')\n          break\n        default:\n          this.$root.$emit('show-info', '功能开发中...')\n      }\n    }\n  }\n}\n</script>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAmC;;EAKvCA,KAAK,EAAC;AAAgB;;EAsBxBA,KAAK,EAAC;AAAuC;;EAE3CA,KAAK,EAAC;AAAgG;;EACrGA,KAAK,EAAC;AAA4E;;EAKjFA,KAAK,EAAC;AAAW;;EAEbA,KAAK,EAAC;AAA6B;;EAEhCA,KAAK,EAAC;AAAmD;;EAE5DA,KAAK,EAAC;AAAY;;EAIhBA,KAAK,EAAC;AAA0C;;EAOxDA,KAAK,EAAC;AAAgG;;EACrGA,KAAK,EAAC;AAA4E;;EAKjFA,KAAK,EAAC;AAAW;;EAEbA,KAAK,EAAC;AAAoB;;EAK1BA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAmC;;EACzCA,KAAK,EAAC;AAAmD;;EACtDA,KAAK,EAAC;AAA0C;;EAErDA,KAAK,EAAC;AAA0C;;EAzEjEC,GAAA;EA0EyCD,KAAK,EAAC;;;EAYtCA,KAAK,EAAC;AAAmJ;;EACxJA,KAAK,EAAC;AAA4E;;EAKjFA,KAAK,EAAC;AAAsD;;EAExDA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAe;;EAKrBA,KAAK,EAAC;AAAQ;;EACbA,KAAK,EAAC;AAAmD;;EAC1DA,KAAK,EAAC;AAA+C;oBAtGtE;;;;uBACEE,mBAAA,CAiHM,OAjHNC,UAiHM,GAhHJC,mBAAA,UAAa,EACbC,mBAAA,CAqBM,OArBNC,UAqBM,G,0BApBJD,mBAAA,CAGM,cAFJA,mBAAA,CAAsE;IAAlEL,KAAK,EAAC;EAAkD,GAAC,MAAI,GACjEK,mBAAA,CAA2D;IAAxDL,KAAK,EAAC;EAAkC,GAAC,aAAW,E,sBAEzDK,mBAAA,CAeM,OAfNE,UAeM,GAdJF,mBAAA,CAMS;IALNG,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,WAAA,IAAAD,QAAA,CAAAC,WAAA,IAAAF,IAAA,CAAW;IACnBV,KAAK,EAAC;MAENa,YAAA,CAAsGC,4BAAA;IAAlFC,IAAI,EAAE,mBAAmB;IAAEf,KAAK,EAb9DgB,eAAA,EAa+D,MAAM;MAAA,gBAA2BC,KAAA,CAAAC;IAAU;gEAb1GC,gBAAA,CAagH,QAExG,G,GACAd,mBAAA,CAMS;IALNG,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAS,YAAA,IAAAT,QAAA,CAAAS,YAAA,IAAAV,IAAA,CAAY;IACpBV,KAAK,EAAC;MAENa,YAAA,CAA8DC,4BAAA;IAA1CC,IAAI,EAAE,mBAAmB;IAAEf,KAAK,EAAC;gCApB/DmB,gBAAA,CAoBwE,QAEhE,G,OAIJf,mBAAA,WAAc,EACdS,YAAA,CAAoCQ,4BAAA;IAAhBC,KAAK,EAAEC,IAAA,CAAAD;EAAK,oCAEhClB,mBAAA,UAAa,EACbC,mBAAA,CAqDM,OArDNmB,UAqDM,GApDJpB,mBAAA,aAAgB,EAChBC,mBAAA,CAoBM,OApBNoB,UAoBM,GAnBJpB,mBAAA,CAGK,MAHLqB,UAGK,GAFHb,YAAA,CAAiFC,4BAAA;IAA7DC,IAAI,EAAE,uBAAuB;IAAEf,KAAK,EAAC;gCAlCnEmB,gBAAA,CAkC2F,WAEnF,G,GAEAd,mBAAA,CAaM,OAbNsB,UAaM,I,kBAZJzB,mBAAA,CAWM0B,SAAA,QAlDhBC,WAAA,CAuCoCZ,KAAA,CAAAa,cAAc,EAA5BC,UAAU;yBAAtB7B,mBAAA,CAWM;MAXqCD,GAAG,EAAE8B,UAAU,CAACC,IAAI;MAAEhC,KAAK,EAAC;QACrEK,mBAAA,CAGM,OAHN4B,UAGM,GAFJ5B,mBAAA,CAA8G;MAAzGL,KAAK,EAzCxBgB,eAAA,EAyCyB,sBAAsB,EAASe,UAAU,CAACG,MAAM;6BAC3D7B,mBAAA,CAA4F,QAA5F8B,UAA4F,EAAAC,gBAAA,CAAzBL,UAAU,CAACC,IAAI,iB,GAEpF3B,mBAAA,CAKM,OALNgC,WAKM,GAJJhC,mBAAA,CAEM;MAFDL,KAAK,EA7CxBgB,eAAA,EA6CyB,uBAAuB,EAASe,UAAU,CAACG,MAAM;wBACvDH,UAAU,CAACO,QAAQ,IAAG,IAC3B,wBACAjC,mBAAA,CAA0G,OAA1GkC,WAA0G,EAAAH,gBAAA,CAAjDL,UAAU,CAACS,MAAM,IAAG,GAAC,GAAAJ,gBAAA,CAAGL,UAAU,CAACU,KAAK,iB;sCAMzGrC,mBAAA,aAAgB,EAChBC,mBAAA,CA2BM,OA3BNqC,WA2BM,GA1BJrC,mBAAA,CAGK,MAHLsC,WAGK,GAFH9B,YAAA,CAAyEC,4BAAA;IAArDC,IAAI,EAAE,gBAAgB;IAAEf,KAAK,EAAC;gCAzD5DmB,gBAAA,CAyDmF,WAE3E,G,GAEAd,mBAAA,CAoBM,OApBNuC,WAoBM,I,kBAnBJ1C,mBAAA,CAkBM0B,SAAA,QAhFhBC,WAAA,CA8D+BZ,KAAA,CAAA4B,cAAc,EAAvBC,KAAK;yBAAjB5C,mBAAA,CAkBM;MAlBgCD,GAAG,EAAE6C,KAAK,CAACC,EAAE;MAAE/C,KAAK,EAAC;QACzDK,mBAAA,CAIM,OAJN2C,WAIM,GAHJ3C,mBAAA,CAEM;MAFDL,KAAK,EAhExBgB,eAAA,EAgEyB,uDAAuD,EAAS8B,KAAK,CAACG,MAAM;QACrFpC,YAAA,CAA0FC,4BAAA;MAAtEC,IAAI,UAAU+B,KAAK,CAAC/B,IAAI;MAAIf,KAAK,EAjErEgB,eAAA,EAiEuE8B,KAAK,CAACI,SAAS,EAAQ,SAAS;mEAG3F7C,mBAAA,CAWM,OAXN8C,WAWM,GAVJ9C,mBAAA,CAGM,OAHN+C,WAGM,GAFJ/C,mBAAA,CAAkF,KAAlFgD,WAAkF,EAAAjB,gBAAA,CAAlBU,KAAK,CAACQ,KAAK,kBAC3EjD,mBAAA,CAA8E,QAA9EkD,WAA8E,EAAAnB,gBAAA,CAApBU,KAAK,CAACU,IAAI,iB,GAEtEnD,mBAAA,CAA+E,KAA/EoD,WAA+E,EAAArB,gBAAA,CAAxBU,KAAK,CAACY,WAAW,kBAC7DZ,KAAK,CAACa,QAAQ,I,cAAzBzD,mBAAA,CAIM,OAJN0D,WAIM,GAHJvD,mBAAA,CAEO;MAFDL,KAAK,EA3E3BgB,eAAA,EA2E4B,kEAAkE,EAASL,QAAA,CAAAkD,gBAAgB,CAACf,KAAK,CAACa,QAAQ;wBACjHb,KAAK,CAACa,QAAQ,wB,KA5EnCvD,mBAAA,e;wCAqFIA,mBAAA,UAAa,EACbC,mBAAA,CA2BM,OA3BNyD,WA2BM,GA1BJzD,mBAAA,CAGK,MAHL0D,WAGK,GAFHlD,YAAA,CAA+EC,4BAAA;IAA3DC,IAAI,EAAE,oBAAoB;IAAEf,KAAK,EAAC;gCAxF9DmB,gBAAA,CAwFuF,QAEjF,G,GAEAd,mBAAA,CAoBM,OApBN2D,WAoBM,I,kBAnBJ9D,mBAAA,CAkBM0B,SAAA,QA/GdC,WAAA,CA6FsCZ,KAAA,CAAAgD,eAAe,EAAjCC,cAAc;yBAA1BhE,mBAAA,CAkBM;MAlB0CD,GAAG,EAAEiE,cAAc,CAACnB,EAAE;MAAE/C,KAAK,EAAC;QAC5EK,mBAAA,CAgBM,OAhBN8D,WAgBM,GAfJ9D,mBAAA,CAIM,OAJN+D,WAIM,GAHJ/D,mBAAA,CAEM;MAFDL,KAAK,EAhGxBgB,eAAA,EAgGyB,qDAAqD,EAASkD,cAAc,CAACjB,MAAM;QAC5FpC,YAAA,CAA4GC,4BAAA;MAAxFC,IAAI,UAAUmD,cAAc,CAACnD,IAAI;MAAIf,KAAK,EAjG9EgB,eAAA,EAiGgFkD,cAAc,CAAChB,SAAS,EAAQ,SAAS;mEAG7G7C,mBAAA,CASM,OATNgE,WASM,GARJhE,mBAAA,CAA6F,MAA7FiE,WAA6F,EAAAlC,gBAAA,CAA5B8B,cAAc,CAACZ,KAAK,kBACrFjD,mBAAA,CAA6F,KAA7FkE,WAA6F,EAAAnC,gBAAA,CAAjC8B,cAAc,CAACR,WAAW,kBACtFrD,mBAAA,CAKS;MAJNG,OAAK,EAAAgE,MAAA,IAAE7D,QAAA,CAAA8D,qBAAqB,CAACP,cAAc;MAC5ClE,KAAK,EAAC;OACP,QAED,iBA5Gd0E,WAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}