{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, vModelText as _vModelText, withDirectives as _withDirectives, vModelSelect as _vModelSelect, toDisplayString as _toDisplayString, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, vModelDynamic as _vModelDynamic, vModelCheckbox as _vModelCheckbox, createBlock as _createBlock, vModelRadio as _vModelRadio } from \"vue\";\nconst _hoisted_1 = {\n  class: \"bg-white shadow rounded-lg p-4 mb-6\"\n};\nconst _hoisted_2 = {\n  class: \"flex flex-wrap items-center justify-between\"\n};\nconst _hoisted_3 = {\n  class: \"flex space-x-3 mb-2 sm:mb-0\"\n};\nconst _hoisted_4 = {\n  class: \"flex items-center space-x-4\"\n};\nconst _hoisted_5 = {\n  class: \"flex items-center border rounded-md overflow-hidden\"\n};\nconst _hoisted_6 = {\n  class: \"relative\"\n};\nconst _hoisted_7 = {\n  class: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n};\nconst _hoisted_8 = {\n  class: \"relative\"\n};\nconst _hoisted_9 = {\n  class: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n};\nconst _hoisted_10 = {\n  key: 0,\n  class: \"bg-white rounded-lg shadow overflow-hidden\"\n};\nconst _hoisted_11 = {\n  class: \"px-4 py-3 bg-gray-50 border-b flex justify-between items-center\"\n};\nconst _hoisted_12 = {\n  class: \"text-sm text-gray-700\"\n};\nconst _hoisted_13 = {\n  class: \"font-medium\"\n};\nconst _hoisted_14 = {\n  class: \"font-medium\"\n};\nconst _hoisted_15 = {\n  class: \"flex space-x-2\"\n};\nconst _hoisted_16 = {\n  class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n};\nconst _hoisted_17 = {\n  class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n};\nconst _hoisted_18 = {\n  class: \"min-w-full divide-y divide-gray-200\"\n};\nconst _hoisted_19 = {\n  class: \"bg-gray-50\"\n};\nconst _hoisted_20 = {\n  scope: \"col\",\n  class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n};\nconst _hoisted_21 = {\n  class: \"bg-white divide-y divide-gray-200\"\n};\nconst _hoisted_22 = {\n  class: \"bg-gray-100\"\n};\nconst _hoisted_23 = {\n  colspan: \"8\",\n  class: \"px-6 py-2\"\n};\nconst _hoisted_24 = {\n  class: \"flex items-center justify-between\"\n};\nconst _hoisted_25 = {\n  class: \"font-medium text-gray-700\"\n};\nconst _hoisted_26 = [\"onClick\"];\nconst _hoisted_27 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_28 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_29 = {\n  class: \"ml-2 font-medium text-gray-900\"\n};\nconst _hoisted_30 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_31 = {\n  class: \"text-sm text-gray-900\"\n};\nconst _hoisted_32 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_33 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_34 = {\n  class: \"text-sm font-medium text-gray-900\"\n};\nconst _hoisted_35 = {\n  key: 0,\n  class: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\"\n};\nconst _hoisted_36 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_37 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_38 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_39 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_40 = {\n  key: 1,\n  class: \"ml-1\"\n};\nconst _hoisted_41 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_42 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_43 = {\n  class: \"flex-grow\"\n};\nconst _hoisted_44 = [\"type\", \"value\"];\nconst _hoisted_45 = [\"onClick\"];\nconst _hoisted_46 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_47 = {\n  class: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\"\n};\nconst _hoisted_48 = {\n  class: \"flex space-x-2\"\n};\nconst _hoisted_49 = [\"onClick\"];\nconst _hoisted_50 = [\"onClick\"];\nconst _hoisted_51 = {\n  key: 0\n};\nconst _hoisted_52 = {\n  colspan: \"8\",\n  class: \"px-6 py-10 text-center\"\n};\nconst _hoisted_53 = {\n  class: \"text-gray-500\"\n};\nconst _hoisted_54 = {\n  class: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\"\n};\nconst _hoisted_55 = {\n  class: \"px-4 py-5 sm:p-6\"\n};\nconst _hoisted_56 = {\n  class: \"flex justify-between items-start mb-4\"\n};\nconst _hoisted_57 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_58 = {\n  class: \"text-lg font-medium text-gray-900\"\n};\nconst _hoisted_59 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_60 = {\n  class: \"space-y-4\"\n};\nconst _hoisted_61 = {\n  class: \"flex justify-between items-center mb-2\"\n};\nconst _hoisted_62 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_63 = {\n  class: \"text-sm font-medium text-gray-900\"\n};\nconst _hoisted_64 = {\n  key: 0,\n  class: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\"\n};\nconst _hoisted_65 = [\"onClick\"];\nconst _hoisted_66 = {\n  class: \"mb-2\"\n};\nconst _hoisted_67 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_68 = [\"type\", \"value\"];\nconst _hoisted_69 = [\"onClick\"];\nconst _hoisted_70 = {\n  class: \"grid grid-cols-2 gap-2 text-xs\"\n};\nconst _hoisted_71 = {\n  class: \"text-gray-900\"\n};\nconst _hoisted_72 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_73 = {\n  class: \"mt-4 flex justify-center\"\n};\nconst _hoisted_74 = [\"onClick\"];\nconst _hoisted_75 = {\n  class: \"mb-4\"\n};\nconst _hoisted_76 = {\n  class: \"px-3 py-2 bg-gray-50 rounded-md\"\n};\nconst _hoisted_77 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_78 = {\n  class: \"flex space-x-3\"\n};\nconst _hoisted_79 = {\n  key: 0,\n  class: \"form-group mb-4\"\n};\nconst _hoisted_80 = [\"value\"];\nconst _hoisted_81 = {\n  class: \"mt-3\"\n};\nconst _hoisted_82 = {\n  class: \"flex justify-between mb-1\"\n};\nconst _hoisted_83 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_84 = [\"type\"];\nconst _hoisted_85 = {\n  key: 1,\n  class: \"space-y-4\"\n};\nconst _hoisted_86 = {\n  class: \"form-group\"\n};\nconst _hoisted_87 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_88 = [\"type\"];\nconst _hoisted_89 = {\n  class: \"form-group\"\n};\nconst _hoisted_90 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_91 = [\"type\"];\nconst _hoisted_92 = {\n  key: 0,\n  class: \"text-sm text-red-500 mt-1\"\n};\nconst _hoisted_93 = {\n  class: \"space-y-2 mt-4\"\n};\nconst _hoisted_94 = {\n  class: \"mb-4\"\n};\nconst _hoisted_95 = {\n  class: \"px-3 py-2 bg-gray-50 rounded-md\"\n};\nconst _hoisted_96 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_97 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_98 = {\n  class: \"relative inline-block w-10 mr-2 align-middle select-none\"\n};\nconst _hoisted_99 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_100 = [\"value\"];\nconst _hoisted_101 = {\n  class: \"form-group\"\n};\nconst _hoisted_102 = {\n  class: \"flex justify-between mb-1\"\n};\nconst _hoisted_103 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_104 = [\"type\"];\nconst _hoisted_105 = {\n  class: \"form-group\"\n};\nconst _hoisted_106 = {\n  class: \"mb-2\"\n};\nconst _hoisted_107 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_108 = {\n  class: \"form-text\"\n};\nconst _hoisted_109 = {\n  class: \"form-group\"\n};\nconst _hoisted_110 = [\"value\"];\nconst _hoisted_111 = {\n  class: \"form-group\"\n};\nconst _hoisted_112 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_113 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_114 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_115 = {\n  key: 0,\n  class: \"mt-3\"\n};\nconst _hoisted_116 = {\n  class: \"grid grid-cols-2 gap-4\"\n};\nconst _hoisted_117 = {\n  class: \"form-group\"\n};\nconst _hoisted_118 = {\n  class: \"form-group\"\n};\nconst _hoisted_119 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_120 = {\n  class: \"form-group\"\n};\nconst _hoisted_121 = [\"value\"];\nconst _hoisted_122 = {\n  class: \"form-group\"\n};\nconst _hoisted_123 = {\n  class: \"form-group\"\n};\nconst _hoisted_124 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_125 = {\n  class: \"form-group\"\n};\nconst _hoisted_126 = [\"value\"];\nconst _hoisted_127 = {\n  class: \"form-group\"\n};\nconst _hoisted_128 = {\n  class: \"form-group\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_CustomCheckbox = _resolveComponent(\"CustomCheckbox\");\n  const _component_StatusBadge = _resolveComponent(\"StatusBadge\");\n  const _component_PasswordStrengthMeter = _resolveComponent(\"PasswordStrengthMeter\");\n  const _component_BaseModal = _resolveComponent(\"BaseModal\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createCommentVNode(\" 操作按钮 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.showEmergencyReset && $options.showEmergencyReset(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'exclamation-triangle'],\n    class: \"mr-2\"\n  }), _cache[52] || (_cache[52] = _createElementVNode(\"span\", null, \"紧急重置\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.openBatchUpdateModal && $options.openBatchUpdateModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'key'],\n    class: \"mr-2\"\n  }), _cache[53] || (_cache[53] = _createElementVNode(\"span\", null, \"批量更新密码\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.openBatchApplyModal && $options.openBatchApplyModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"mr-2\"\n  }), _cache[54] || (_cache[54] = _createElementVNode(\"span\", null, \"批量应用策略\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n    onClick: _cache[3] || (_cache[3] = (...args) => _ctx.openBatchAddAccountModal && _ctx.openBatchAddAccountModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'users'],\n    class: \"mr-2\"\n  }), _cache[55] || (_cache[55] = _createElementVNode(\"span\", null, \"批量添加账号\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" 视图切换 \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1 focus:outline-none\", {\n      'bg-blue-500 text-white': $data.viewMode === 'table',\n      'bg-gray-100 text-gray-600': $data.viewMode !== 'table'\n    }]),\n    onClick: _cache[4] || (_cache[4] = $event => $data.viewMode = 'table')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'table'],\n    class: \"mr-1\"\n  }), _cache[56] || (_cache[56] = _createTextVNode(\" 表格 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1 focus:outline-none\", {\n      'bg-blue-500 text-white': $data.viewMode === 'card',\n      'bg-gray-100 text-gray-600': $data.viewMode !== 'card'\n    }]),\n    onClick: _cache[5] || (_cache[5] = $event => $data.viewMode = 'card')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'th-large'],\n    class: \"mr-1\"\n  }), _cache[57] || (_cache[57] = _createTextVNode(\" 卡片 \"))], 2 /* CLASS */)]), _createCommentVNode(\" 筛选 \"), _createElementVNode(\"div\", _hoisted_6, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.filterText = $event),\n    placeholder: \"筛选主机...\",\n    class: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.filterText]]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"text-gray-400\"\n  })])]), _createCommentVNode(\" 账号筛选 \"), _createElementVNode(\"div\", _hoisted_8, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.accountFilterText = $event),\n    placeholder: \"筛选账号...\",\n    class: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.accountFilterText]]), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'user'],\n    class: \"text-gray-400\"\n  })])]), _createCommentVNode(\" 状态筛选 \"), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.statusFilter = $event),\n    class: \"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n  }, _cache[58] || (_cache[58] = [_createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有状态\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"normal\"\n  }, \"正常\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"warning\"\n  }, \"警告\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"error\"\n  }, \"错误\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.statusFilter]]), _createCommentVNode(\" 显示密码过期选项 \"), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.expiryFilter = $event),\n    class: \"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n  }, _cache[59] || (_cache[59] = [_createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有密码\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"expired\"\n  }, \"已过期\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"expiring-soon\"\n  }, \"即将过期\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"valid\"\n  }, \"有效期内\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.expiryFilter]])])])]), _createCommentVNode(\" 主机列表 \"), _createCommentVNode(\" 表格视图 \"), $data.viewMode === 'table' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createCommentVNode(\" 账号计数和导出按钮 \"), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_cache[60] || (_cache[60] = _createTextVNode(\" 显示 \")), _createElementVNode(\"span\", _hoisted_13, _toDisplayString($options.filteredAccounts.length), 1 /* TEXT */), _cache[61] || (_cache[61] = _createTextVNode(\" 个账号 (共 \")), _createElementVNode(\"span\", _hoisted_14, _toDisplayString($options.getAllAccounts.length), 1 /* TEXT */), _cache[62] || (_cache[62] = _createTextVNode(\" 个) \"))]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"button\", _hoisted_16, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'file-export'],\n    class: \"mr-1\"\n  }), _cache[63] || (_cache[63] = _createTextVNode(\" 导出 \"))]), _createElementVNode(\"button\", _hoisted_17, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'print'],\n    class: \"mr-1\"\n  }), _cache[64] || (_cache[64] = _createTextVNode(\" 打印 \"))])])]), _createElementVNode(\"table\", _hoisted_18, [_createElementVNode(\"thead\", _hoisted_19, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", _hoisted_20, [_createVNode(_component_CustomCheckbox, {\n    modelValue: $data.selectAll,\n    \"onUpdate:modelValue\": [_cache[10] || (_cache[10] = $event => $data.selectAll = $event), $options.toggleSelectAll]\n  }, {\n    default: _withCtx(() => _cache[65] || (_cache[65] = [_createTextVNode(\" 主机名 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _cache[66] || (_cache[66] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" IP地址 \", -1 /* HOISTED */)), _cache[67] || (_cache[67] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 账号 \", -1 /* HOISTED */)), _cache[68] || (_cache[68] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 最后密码修改时间 \", -1 /* HOISTED */)), _cache[69] || (_cache[69] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码过期时间 \", -1 /* HOISTED */)), _cache[70] || (_cache[70] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码 \", -1 /* HOISTED */)), _cache[71] || (_cache[71] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 状态 \", -1 /* HOISTED */)), _cache[72] || (_cache[72] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 操作 \", -1 /* HOISTED */))])]), _createElementVNode(\"tbody\", _hoisted_21, [_createCommentVNode(\" 按主机分组显示 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.groupedAccounts, hostGroup => {\n    return _openBlock(), _createElementBlock(_Fragment, {\n      key: hostGroup.hostId\n    }, [_createCommentVNode(\" 主机分组标题行 \"), _createElementVNode(\"tr\", _hoisted_22, [_createElementVNode(\"td\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, _toDisplayString(hostGroup.hostName) + \" (\" + _toDisplayString(hostGroup.hostIp) + \")\", 1 /* TEXT */), _createElementVNode(\"div\", null, [_createElementVNode(\"button\", {\n      class: \"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\",\n      onClick: $event => $options.openAddAccountModal(hostGroup.host)\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'plus'],\n      class: \"mr-1\"\n    }), _cache[73] || (_cache[73] = _createTextVNode(\" 添加账号 \"))], 8 /* PROPS */, _hoisted_26)])])])]), _createCommentVNode(\" 账号行 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(hostGroup.accounts, (account, accountIndex) => {\n      return _openBlock(), _createElementBlock(\"tr\", {\n        key: account.id,\n        class: _normalizeClass({\n          'bg-gray-50': accountIndex % 2 === 0,\n          'hover:bg-blue-50': true\n        })\n      }, [_createElementVNode(\"td\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_createVNode(_component_CustomCheckbox, {\n        modelValue: account.host.selected,\n        \"onUpdate:modelValue\": $event => account.host.selected = $event,\n        class: \"ml-4\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"span\", _hoisted_29, _toDisplayString(account.host.name), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])])]), _createElementVNode(\"td\", _hoisted_30, [_createElementVNode(\"div\", _hoisted_31, _toDisplayString(account.host.ip), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"span\", _hoisted_34, _toDisplayString(account.username), 1 /* TEXT */), account.isDefault ? (_openBlock(), _createElementBlock(\"span\", _hoisted_35, \" 默认 \")) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"td\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, _toDisplayString(account.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_38, [_createElementVNode(\"div\", {\n        class: _normalizeClass({\n          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\n          'bg-red-100 text-red-800': $options.isPasswordExpired(account).status === 'danger' || $options.isPasswordExpired(account).status === 'expired',\n          'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(account).status === 'warning',\n          'bg-gray-100 text-gray-800': $options.isPasswordExpired(account).status === 'normal'\n        })\n      }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(account).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(account).status === 'expired' || $options.isPasswordExpired(account).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_39, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-triangle']\n      })])) : $options.isPasswordExpired(account).status === 'warning' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_40, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-circle']\n      })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]), _createElementVNode(\"td\", _hoisted_41, [_createElementVNode(\"div\", _hoisted_42, [_createElementVNode(\"div\", _hoisted_43, [_createElementVNode(\"input\", {\n        type: $data.passwordVisibility[account.id] ? 'text' : 'password',\n        value: account.password,\n        readonly: \"\",\n        class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n      }, null, 8 /* PROPS */, _hoisted_44)]), _createElementVNode(\"button\", {\n        onClick: $event => $options.togglePasswordVisibility(account.id),\n        class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $data.passwordVisibility[account.id] ? 'eye-slash' : 'eye'],\n        class: \"text-lg\"\n      }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_45)])]), _createElementVNode(\"td\", _hoisted_46, [_createVNode(_component_StatusBadge, {\n        type: account.host.status\n      }, null, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"td\", _hoisted_47, [_createElementVNode(\"div\", _hoisted_48, [_createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.openChangePasswordModal(account.host, account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'key'],\n        class: \"mr-1\"\n      }), _cache[74] || (_cache[74] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_49), _createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.copyPassword(account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'copy'],\n        class: \"mr-1\"\n      }), _cache[75] || (_cache[75] = _createTextVNode(\" 复制 \"))], 8 /* PROPS */, _hoisted_50)])])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))], 64 /* STABLE_FRAGMENT */);\n  }), 128 /* KEYED_FRAGMENT */)), _createCommentVNode(\" 无数据显示 \"), $options.filteredAccounts.length === 0 ? (_openBlock(), _createElementBlock(\"tr\", _hoisted_51, [_createElementVNode(\"td\", _hoisted_52, [_createElementVNode(\"div\", _hoisted_53, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"text-4xl mb-3\"\n  }), _cache[76] || (_cache[76] = _createElementVNode(\"p\", null, \"没有找到匹配的账号数据\", -1 /* HOISTED */))])])])) : _createCommentVNode(\"v-if\", true)])])])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 卡片视图 \"), _createElementVNode(\"div\", _hoisted_54, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredHosts, host => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: host.id,\n      class: \"bg-white overflow-hidden shadow rounded-lg\"\n    }, [_createElementVNode(\"div\", _hoisted_55, [_createCommentVNode(\" 主机头部 \"), _createElementVNode(\"div\", _hoisted_56, [_createElementVNode(\"div\", _hoisted_57, [_createVNode(_component_CustomCheckbox, {\n      modelValue: host.selected,\n      \"onUpdate:modelValue\": $event => host.selected = $event,\n      class: \"mr-2\"\n    }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"]), _createElementVNode(\"div\", null, [_createElementVNode(\"h3\", _hoisted_58, _toDisplayString(host.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_59, _toDisplayString(host.ip), 1 /* TEXT */)])]), _createVNode(_component_StatusBadge, {\n      type: host.status\n    }, null, 8 /* PROPS */, [\"type\"])]), _createCommentVNode(\" 账号列表 \"), _createElementVNode(\"div\", _hoisted_60, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(host.accounts, account => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: account.id,\n        class: _normalizeClass([\"border border-gray-200 rounded-lg p-3\", {\n          'border-green-300 bg-green-50': account.isDefault\n        }])\n      }, [_createElementVNode(\"div\", _hoisted_61, [_createElementVNode(\"div\", _hoisted_62, [_createElementVNode(\"span\", _hoisted_63, _toDisplayString(account.username), 1 /* TEXT */), account.isDefault ? (_openBlock(), _createElementBlock(\"span\", _hoisted_64, \" 默认 \")) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.openChangePasswordModal(host, account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'key'],\n        class: \"mr-1\"\n      }), _cache[77] || (_cache[77] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_65)]), _createCommentVNode(\" 密码展示 \"), _createElementVNode(\"div\", _hoisted_66, [_cache[78] || (_cache[78] = _createElementVNode(\"div\", {\n        class: \"text-xs font-medium text-gray-500 mb-1\"\n      }, \"密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_67, [_createElementVNode(\"input\", {\n        type: $data.passwordVisibility[account.id] ? 'text' : 'password',\n        value: account.password,\n        readonly: \"\",\n        class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n      }, null, 8 /* PROPS */, _hoisted_68), _createElementVNode(\"button\", {\n        onClick: $event => $options.togglePasswordVisibility(account.id),\n        class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $data.passwordVisibility[account.id] ? 'eye-slash' : 'eye'],\n        class: \"text-lg\"\n      }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_69)])]), _createCommentVNode(\" 密码信息区域 \"), _createElementVNode(\"div\", _hoisted_70, [_createElementVNode(\"div\", null, [_cache[79] || (_cache[79] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"最后修改时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_71, _toDisplayString(account.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[80] || (_cache[80] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"密码过期\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n        class: _normalizeClass({\n          'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\n          'bg-red-100 text-red-800': $options.isPasswordExpired(account).status === 'danger' || $options.isPasswordExpired(account).status === 'expired',\n          'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(account).status === 'warning',\n          'bg-gray-100 text-gray-800': $options.isPasswordExpired(account).status === 'normal'\n        })\n      }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(account).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(account).status === 'expired' || $options.isPasswordExpired(account).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_72, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-triangle']\n      })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)])])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 添加账号按钮 \"), _createElementVNode(\"div\", _hoisted_73, [_createElementVNode(\"button\", {\n      class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n      onClick: $event => $options.openAddAccountModal(host)\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'plus'],\n      class: \"mr-1\"\n    }), _cache[81] || (_cache[81] = _createTextVNode(\" 添加账号 \"))], 8 /* PROPS */, _hoisted_74)])])]);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 修改密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.changePasswordModal.show,\n    \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.changePasswordModal.show = $event),\n    title: \"修改密码\",\n    onConfirm: $options.updatePassword,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_75, [_cache[85] || (_cache[85] = _createElementVNode(\"div\", {\n      class: \"font-medium mb-2\"\n    }, \"主机信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_76, [_createElementVNode(\"div\", null, [_cache[82] || (_cache[82] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"主机名:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[83] || (_cache[83] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"IP地址:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.ip), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[84] || (_cache[84] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"账号:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentAccount.username), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_77, [_cache[88] || (_cache[88] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_78, [_createElementVNode(\"button\", {\n      onClick: _cache[11] || (_cache[11] = $event => $data.changePasswordModal.method = 'auto'),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", $data.changePasswordModal.method === 'auto' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-2\"\n    }), _cache[86] || (_cache[86] = _createTextVNode(\" 自动生成 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n      onClick: _cache[12] || (_cache[12] = $event => $data.changePasswordModal.method = 'manual'),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", $data.changePasswordModal.method === 'manual' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'edit'],\n      class: \"mr-2\"\n    }), _cache[87] || (_cache[87] = _createTextVNode(\" 手动输入 \"))], 2 /* CLASS */)])]), $data.changePasswordModal.method === 'auto' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_79, [_cache[91] || (_cache[91] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.changePasswordModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[14] || (_cache[14] = $event => $options.generatePassword())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_80);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.changePasswordModal.policyId]]), _createElementVNode(\"div\", _hoisted_81, [_createElementVNode(\"div\", _hoisted_82, [_cache[90] || (_cache[90] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      onClick: _cache[15] || (_cache[15] = $event => $options.generatePassword()),\n      type: \"button\",\n      class: \"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[89] || (_cache[89] = _createTextVNode(\" 重新生成 \"))])]), _createElementVNode(\"div\", _hoisted_83, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.generated ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.changePasswordModal.generatedPassword = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_84), [[_vModelDynamic, $data.changePasswordModal.generatedPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[17] || (_cache[17] = $event => $data.passwordVisibility.generated = !$data.passwordVisibility.generated),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.generated ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_85, [_createElementVNode(\"div\", _hoisted_86, [_cache[92] || (_cache[92] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"新密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_87, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.new ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[18] || (_cache[18] = $event => $data.changePasswordModal.newPassword = $event),\n      class: \"form-control flex-1\",\n      placeholder: \"输入新密码\"\n    }, null, 8 /* PROPS */, _hoisted_88), [[_vModelDynamic, $data.changePasswordModal.newPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[19] || (_cache[19] = $event => $data.passwordVisibility.new = !$data.passwordVisibility.new),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.new ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])]), _createElementVNode(\"div\", _hoisted_89, [_cache[93] || (_cache[93] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"确认密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_90, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.confirm ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $data.changePasswordModal.confirmPassword = $event),\n      class: _normalizeClass([\"form-control flex-1\", {\n        'border-red-500': $options.passwordMismatch\n      }]),\n      placeholder: \"再次输入新密码\"\n    }, null, 10 /* CLASS, PROPS */, _hoisted_91), [[_vModelDynamic, $data.changePasswordModal.confirmPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[21] || (_cache[21] = $event => $data.passwordVisibility.confirm = !$data.passwordVisibility.confirm),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.confirm ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])]), $options.passwordMismatch ? (_openBlock(), _createElementBlock(\"div\", _hoisted_92, \"两次输入的密码不一致\")) : _createCommentVNode(\"v-if\", true)]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.newPassword\n    }, null, 8 /* PROPS */, [\"password\"])])), _createElementVNode(\"div\", _hoisted_93, [_cache[97] || (_cache[97] = _createElementVNode(\"div\", {\n      class: \"form-label font-medium\"\n    }, \"执行选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.executeImmediately,\n      \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $data.changePasswordModal.executeImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[94] || (_cache[94] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"立即执行\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.saveHistory,\n      \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.changePasswordModal.saveHistory = $event)\n    }, {\n      default: _withCtx(() => _cache[95] || (_cache[95] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"保存历史记录\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.logAudit,\n      \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.changePasswordModal.logAudit = $event)\n    }, {\n      default: _withCtx(() => _cache[96] || (_cache[96] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"记录审计日志\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 添加账号弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.addAccountModal.show,\n    \"onUpdate:modelValue\": _cache[33] || (_cache[33] = $event => $data.addAccountModal.show = $event),\n    title: \"添加账号\",\n    onConfirm: $options.addAccount,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_94, [_cache[100] || (_cache[100] = _createElementVNode(\"div\", {\n      class: \"font-medium mb-2\"\n    }, \"主机信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_95, [_createElementVNode(\"div\", null, [_cache[98] || (_cache[98] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"主机名:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[99] || (_cache[99] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"IP地址:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.ip), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_96, [_cache[101] || (_cache[101] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"账号名称\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.addAccountModal.username = $event),\n      class: \"form-control\",\n      placeholder: \"输入账号名称\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addAccountModal.username]])]), _createElementVNode(\"div\", _hoisted_97, [_cache[103] || (_cache[103] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"设为默认账号\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_98, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.addAccountModal.isDefault = $event),\n      class: \"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.addAccountModal.isDefault]]), _cache[102] || (_cache[102] = _createElementVNode(\"label\", {\n      class: \"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"\n    }, null, -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_99, [_cache[104] || (_cache[104] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $data.addAccountModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[29] || (_cache[29] = $event => $options.generatePasswordForNewAccount())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_100);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.addAccountModal.policyId]])]), _createElementVNode(\"div\", _hoisted_101, [_createElementVNode(\"div\", _hoisted_102, [_cache[106] || (_cache[106] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      onClick: _cache[30] || (_cache[30] = $event => $options.generatePasswordForNewAccount()),\n      type: \"button\",\n      class: \"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[105] || (_cache[105] = _createTextVNode(\" 重新生成 \"))])]), _createElementVNode(\"div\", _hoisted_103, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.newAccount ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[31] || (_cache[31] = $event => $data.addAccountModal.password = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_104), [[_vModelDynamic, $data.addAccountModal.password]]), _createElementVNode(\"button\", {\n      onClick: _cache[32] || (_cache[32] = $event => $data.passwordVisibility.newAccount = !$data.passwordVisibility.newAccount),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.newAccount ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量更新密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchUpdateModal.show,\n    \"onUpdate:modelValue\": _cache[43] || (_cache[43] = $event => $data.batchUpdateModal.show = $event),\n    title: \"批量更新密码\",\n    \"confirm-text\": \"开始更新\",\n    size: \"lg\",\n    onConfirm: $options.batchUpdatePasswords,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_105, [_cache[108] || (_cache[108] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_106, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatch,\n      \"onUpdate:modelValue\": [_cache[34] || (_cache[34] = $event => $data.selectAllBatch = $event), $options.toggleSelectAllBatch]\n    }, {\n      default: _withCtx(() => _cache[107] || (_cache[107] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_107, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchUpdateModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchUpdateModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_108, \"已选择 \" + _toDisplayString($options.selectedHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_109, [_cache[109] || (_cache[109] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[35] || (_cache[35] = $event => $data.batchUpdateModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_110);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchUpdateModal.policyId]])]), _createElementVNode(\"div\", _hoisted_111, [_cache[114] || (_cache[114] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_112, [_createElementVNode(\"label\", _hoisted_113, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[36] || (_cache[36] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"immediate\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[110] || (_cache[110] = _createElementVNode(\"span\", null, \"立即执行\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_114, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[37] || (_cache[37] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"scheduled\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[111] || (_cache[111] = _createElementVNode(\"span\", null, \"定时执行\", -1 /* HOISTED */))])]), $data.batchUpdateModal.executionTime === 'scheduled' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_115, [_createElementVNode(\"div\", _hoisted_116, [_createElementVNode(\"div\", null, [_cache[112] || (_cache[112] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"日期\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"date\",\n      \"onUpdate:modelValue\": _cache[38] || (_cache[38] = $event => $data.batchUpdateModal.scheduledDate = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledDate]])]), _createElementVNode(\"div\", null, [_cache[113] || (_cache[113] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"时间\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"time\",\n      \"onUpdate:modelValue\": _cache[39] || (_cache[39] = $event => $data.batchUpdateModal.scheduledTime = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledTime]])])])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_117, [_cache[118] || (_cache[118] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[40] || (_cache[40] = $event => $data.batchUpdateModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[115] || (_cache[115] = [_createTextVNode(\" 忽略错误继续执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.detailedLog,\n      \"onUpdate:modelValue\": _cache[41] || (_cache[41] = $event => $data.batchUpdateModal.detailedLog = $event)\n    }, {\n      default: _withCtx(() => _cache[116] || (_cache[116] = [_createTextVNode(\" 记录详细日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.sendNotification,\n      \"onUpdate:modelValue\": _cache[42] || (_cache[42] = $event => $data.batchUpdateModal.sendNotification = $event)\n    }, {\n      default: _withCtx(() => _cache[117] || (_cache[117] = [_createTextVNode(\" 执行完成后发送通知 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量应用策略弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchApplyModal.show,\n    \"onUpdate:modelValue\": _cache[47] || (_cache[47] = $event => $data.batchApplyModal.show = $event),\n    title: \"批量应用密码策略\",\n    \"confirm-text\": \"应用策略\",\n    onConfirm: $options.batchApplyPolicy,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_118, [_cache[119] || (_cache[119] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_119, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchApplyModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchApplyModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_120, [_cache[120] || (_cache[120] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[44] || (_cache[44] = $event => $data.batchApplyModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name), 9 /* TEXT, PROPS */, _hoisted_121);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchApplyModal.policyId]])]), _createElementVNode(\"div\", _hoisted_122, [_cache[123] || (_cache[123] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.updateImmediately,\n      \"onUpdate:modelValue\": _cache[45] || (_cache[45] = $event => $data.batchApplyModal.updateImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[121] || (_cache[121] = [_createTextVNode(\" 立即更新密码以符合策略 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.applyOnNextUpdate,\n      \"onUpdate:modelValue\": _cache[46] || (_cache[46] = $event => $data.batchApplyModal.applyOnNextUpdate = $event)\n    }, {\n      default: _withCtx(() => _cache[122] || (_cache[122] = [_createTextVNode(\" 下次密码更新时应用 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 紧急重置密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.emergencyResetModal.show,\n    \"onUpdate:modelValue\": _cache[51] || (_cache[51] = $event => $data.emergencyResetModal.show = $event),\n    title: \"紧急密码重置\",\n    \"confirm-text\": \"立即重置\",\n    icon: \"exclamation-triangle\",\n    danger: \"\",\n    onConfirm: $options.emergencyReset,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_cache[129] || (_cache[129] = _createElementVNode(\"div\", {\n      class: \"bg-red-50 text-red-700 p-3 rounded-md mb-4\"\n    }, [_createElementVNode(\"p\", null, \"紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_123, [_cache[124] || (_cache[124] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_124, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.emergencyResetModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.emergencyResetModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_125, [_cache[125] || (_cache[125] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用紧急策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[48] || (_cache[48] = $event => $data.emergencyResetModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.emergencyPolicies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_126);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.policyId]])]), _createElementVNode(\"div\", _hoisted_127, [_cache[127] || (_cache[127] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"操作原因\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[49] || (_cache[49] = $event => $data.emergencyResetModal.reason = $event),\n      class: \"form-select\"\n    }, _cache[126] || (_cache[126] = [_createElementVNode(\"option\", {\n      value: \"security_incident\"\n    }, \"安全事件响应\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"password_leak\"\n    }, \"密码泄露\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"abnormal_access\"\n    }, \"异常访问\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"compliance\"\n    }, \"合规要求\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"other\"\n    }, \"其他原因\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.reason]])]), _createElementVNode(\"div\", _hoisted_128, [_cache[128] || (_cache[128] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"附加说明\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n      \"onUpdate:modelValue\": _cache[50] || (_cache[50] = $event => $data.emergencyResetModal.description = $event),\n      class: \"form-control\",\n      rows: \"2\",\n      placeholder: \"请输入重置原因详细说明\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.emergencyResetModal.description]])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "scope", "colspan", "_createElementBlock", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "args", "$options", "showEmergencyReset", "_createVNode", "_component_font_awesome_icon", "icon", "openBatchUpdateModal", "openBatchApplyModal", "_ctx", "openBatchAddAccountModal", "_hoisted_4", "_hoisted_5", "_normalizeClass", "$data", "viewMode", "$event", "_createTextVNode", "_hoisted_6", "type", "filterText", "placeholder", "_hoisted_7", "_hoisted_8", "accountFilterText", "_hoisted_9", "statusFilter", "value", "expiryFilter", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_toDisplayString", "filteredAccounts", "length", "_hoisted_14", "getAllAccounts", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_component_CustomCheckbox", "modelValue", "selectAll", "toggleSelectAll", "default", "_withCtx", "_", "_hoisted_21", "_Fragment", "_renderList", "groupedAccounts", "hostGroup", "hostId", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "hostName", "hostIp", "openAddAccountModal", "host", "_hoisted_26", "accounts", "account", "accountIndex", "id", "_hoisted_27", "_hoisted_28", "selected", "_hoisted_29", "name", "_hoisted_30", "_hoisted_31", "ip", "_hoisted_32", "_hoisted_33", "_hoisted_34", "username", "isDefault", "_hoisted_35", "_hoisted_36", "_hoisted_37", "lastPasswordChange", "_hoisted_38", "isPasswordExpired", "status", "text", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_hoisted_43", "passwordVisibility", "password", "readonly", "_hoisted_44", "togglePasswordVisibility", "_hoisted_45", "_hoisted_46", "_component_StatusBadge", "_hoisted_47", "_hoisted_48", "openChangePasswordModal", "_hoisted_49", "copyPassword", "_hoisted_50", "_hoisted_51", "_hoisted_52", "_hoisted_53", "_hoisted_54", "filteredHosts", "_hoisted_55", "_hoisted_56", "_hoisted_57", "_hoisted_58", "_hoisted_59", "_hoisted_60", "_hoisted_61", "_hoisted_62", "_hoisted_63", "_hoisted_64", "_hoisted_65", "_hoisted_66", "_hoisted_67", "_hoisted_68", "_hoisted_69", "_hoisted_70", "_hoisted_71", "_hoisted_72", "_hoisted_73", "_hoisted_74", "_component_BaseModal", "changePasswordModal", "show", "title", "onConfirm", "updatePassword", "loading", "processing", "_hoisted_75", "_hoisted_76", "currentHost", "currentAccount", "_hoisted_77", "_hoisted_78", "method", "_hoisted_79", "policyId", "onChange", "generatePassword", "policies", "policy", "<PERSON><PERSON><PERSON><PERSON>", "expiryDays", "_hoisted_80", "_hoisted_81", "_hoisted_82", "_hoisted_83", "generated", "generatedPassword", "_hoisted_84", "_hoisted_85", "_hoisted_86", "_hoisted_87", "new", "newPassword", "_hoisted_88", "_hoisted_89", "_hoisted_90", "confirm", "confirmPassword", "passwordMismatch", "_hoisted_91", "_hoisted_92", "_component_PasswordStrengthMeter", "_hoisted_93", "executeImmediately", "saveHistory", "logAudit", "addAccountModal", "addAccount", "_hoisted_94", "_hoisted_95", "_hoisted_96", "_hoisted_97", "_hoisted_98", "_hoisted_99", "generatePasswordForNewAccount", "_hoisted_100", "_hoisted_101", "_hoisted_102", "_hoisted_103", "newAccount", "_hoisted_104", "batchUpdateModal", "size", "batchUpdatePasswords", "_hoisted_105", "_hoisted_106", "selectAllBatch", "toggleSelectAllBatch", "_hoisted_107", "hosts", "_createBlock", "selectedHosts", "_hoisted_108", "selectedHostsCount", "_hoisted_109", "_hoisted_110", "_hoisted_111", "_hoisted_112", "_hoisted_113", "executionTime", "_hoisted_114", "_hoisted_115", "_hoisted_116", "scheduledDate", "scheduledTime", "_hoisted_117", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "batchApplyPolicy", "_hoisted_118", "_hoisted_119", "selectedHostsList", "_hoisted_120", "_hoisted_121", "_hoisted_122", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "danger", "emergencyReset", "_hoisted_123", "_hoisted_124", "_hoisted_125", "emergencyPolicies", "_hoisted_126", "_hoisted_127", "reason", "_hoisted_128", "description", "rows"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"bg-white shadow rounded-lg p-4 mb-6\">\r\n      <div class=\"flex flex-wrap items-center justify-between\">\r\n        <div class=\"flex space-x-3 mb-2 sm:mb-0\">\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\r\n            @click=\"showEmergencyReset\">\r\n            <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2\" />\r\n            <span>紧急重置</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            @click=\"openBatchUpdateModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n            <span>批量更新密码</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\r\n            @click=\"openBatchApplyModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n            <span>批量应用策略</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\r\n            @click=\"openBatchAddAccountModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'users']\" class=\"mr-2\" />\r\n            <span>批量添加账号</span>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"flex items-center space-x-4\">\r\n          <!-- 视图切换 -->\r\n          <div class=\"flex items-center border rounded-md overflow-hidden\">\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'table', 'bg-gray-100 text-gray-600': viewMode !== 'table' }\"\r\n              @click=\"viewMode = 'table'\">\r\n              <font-awesome-icon :icon=\"['fas', 'table']\" class=\"mr-1\" />\r\n              表格\r\n            </button>\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'card', 'bg-gray-100 text-gray-600': viewMode !== 'card' }\"\r\n              @click=\"viewMode = 'card'\">\r\n              <font-awesome-icon :icon=\"['fas', 'th-large']\" class=\"mr-1\" />\r\n              卡片\r\n            </button>\r\n          </div>\r\n\r\n          <!-- 筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"filterText\" placeholder=\"筛选主机...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 账号筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"accountFilterText\" placeholder=\"筛选账号...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'user']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 状态筛选 -->\r\n          <select v-model=\"statusFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有状态</option>\r\n            <option value=\"normal\">正常</option>\r\n            <option value=\"warning\">警告</option>\r\n            <option value=\"error\">错误</option>\r\n          </select>\r\n\r\n          <!-- 显示密码过期选项 -->\r\n          <select v-model=\"expiryFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有密码</option>\r\n            <option value=\"expired\">已过期</option>\r\n            <option value=\"expiring-soon\">即将过期</option>\r\n            <option value=\"valid\">有效期内</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <!-- 表格视图 -->\r\n    <div v-if=\"viewMode === 'table'\" class=\"bg-white rounded-lg shadow overflow-hidden\">\r\n      <!-- 账号计数和导出按钮 -->\r\n      <div class=\"px-4 py-3 bg-gray-50 border-b flex justify-between items-center\">\r\n        <div class=\"text-sm text-gray-700\">\r\n          显示 <span class=\"font-medium\">{{ filteredAccounts.length }}</span> 个账号\r\n          (共 <span class=\"font-medium\">{{ getAllAccounts.length }}</span> 个)\r\n        </div>\r\n        <div class=\"flex space-x-2\">\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\r\n            <font-awesome-icon :icon=\"['fas', 'file-export']\" class=\"mr-1\" />\r\n            导出\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\r\n            <font-awesome-icon :icon=\"['fas', 'print']\" class=\"mr-1\" />\r\n            打印\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <table class=\"min-w-full divide-y divide-gray-200\">\r\n        <thead class=\"bg-gray-50\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n                主机名\r\n              </CustomCheckbox>\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              IP地址\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              账号\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              最后密码修改时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码过期时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              状态\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              操作\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"bg-white divide-y divide-gray-200\">\r\n          <!-- 按主机分组显示 -->\r\n          <template v-for=\"hostGroup in groupedAccounts\" :key=\"hostGroup.hostId\">\r\n            <!-- 主机分组标题行 -->\r\n            <tr class=\"bg-gray-100\">\r\n              <td colspan=\"8\" class=\"px-6 py-2\">\r\n                <div class=\"flex items-center justify-between\">\r\n                  <div class=\"font-medium text-gray-700\">{{ hostGroup.hostName }} ({{ hostGroup.hostIp }})</div>\r\n                  <div>\r\n                    <button class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\"\r\n                      @click=\"openAddAccountModal(hostGroup.host)\">\r\n                      <font-awesome-icon :icon=\"['fas', 'plus']\" class=\"mr-1\" />\r\n                      添加账号\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n            <!-- 账号行 -->\r\n            <tr v-for=\"(account, accountIndex) in hostGroup.accounts\" :key=\"account.id\"\r\n              :class=\"{ 'bg-gray-50': accountIndex % 2 === 0, 'hover:bg-blue-50': true }\">\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <CustomCheckbox v-model=\"account.host.selected\" class=\"ml-4\">\r\n                    <span class=\"ml-2 font-medium text-gray-900\">{{ account.host.name }}</span>\r\n                  </CustomCheckbox>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-900\">{{ account.host.ip }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-500\">{{ account.lastPasswordChange || '-' }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div :class=\"{\r\n                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\r\n                  'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                  'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                  'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                }\">\r\n                  {{ isPasswordExpired(account).text }}\r\n                  <span\r\n                    v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                    class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                  </span>\r\n                  <span v-else-if=\"isPasswordExpired(account).status === 'warning'\" class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" />\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <div class=\"flex-grow\">\r\n                    <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\"\r\n                      readonly\r\n                      class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  </div>\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <StatusBadge :type=\"account.host.status\" />\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                <div class=\"flex space-x-2\">\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"openChangePasswordModal(account.host, account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                    修改密码\r\n                  </button>\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"copyPassword(account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'copy']\" class=\"mr-1\" />\r\n                    复制\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          </template>\r\n          <!-- 无数据显示 -->\r\n          <tr v-if=\"filteredAccounts.length === 0\">\r\n            <td colspan=\"8\" class=\"px-6 py-10 text-center\">\r\n              <div class=\"text-gray-500\">\r\n                <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-4xl mb-3\" />\r\n                <p>没有找到匹配的账号数据</p>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <!-- 卡片视图 -->\r\n    <div v-else class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\">\r\n      <div v-for=\"host in filteredHosts\" :key=\"host.id\" class=\"bg-white overflow-hidden shadow rounded-lg\">\r\n        <div class=\"px-4 py-5 sm:p-6\">\r\n          <!-- 主机头部 -->\r\n          <div class=\"flex justify-between items-start mb-4\">\r\n            <div class=\"flex items-center\">\r\n              <CustomCheckbox v-model=\"host.selected\" class=\"mr-2\" />\r\n              <div>\r\n                <h3 class=\"text-lg font-medium text-gray-900\">{{ host.name }}</h3>\r\n                <p class=\"text-sm text-gray-500\">{{ host.ip }}</p>\r\n              </div>\r\n            </div>\r\n            <StatusBadge :type=\"host.status\" />\r\n          </div>\r\n\r\n          <!-- 账号列表 -->\r\n          <div class=\"space-y-4\">\r\n            <div v-for=\"account in host.accounts\" :key=\"account.id\" class=\"border border-gray-200 rounded-lg p-3\"\r\n              :class=\"{ 'border-green-300 bg-green-50': account.isDefault }\">\r\n              <div class=\"flex justify-between items-center mb-2\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n                <button\r\n                  class=\"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                  @click=\"openChangePasswordModal(host, account)\">\r\n                  <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                  修改密码\r\n                </button>\r\n              </div>\r\n\r\n              <!-- 密码展示 -->\r\n              <div class=\"mb-2\">\r\n                <div class=\"text-xs font-medium text-gray-500 mb-1\">密码</div>\r\n                <div class=\"flex items-center\">\r\n                  <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\" readonly\r\n                    class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 密码信息区域 -->\r\n              <div class=\"grid grid-cols-2 gap-2 text-xs\">\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">最后修改时间</div>\r\n                  <div class=\"text-gray-900\">{{ account.lastPasswordChange || '-' }}</div>\r\n                </div>\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">密码过期</div>\r\n                  <div :class=\"{\r\n                    'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\r\n                    'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                    'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                    'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                  }\">\r\n                    {{ isPasswordExpired(account).text }}\r\n                    <span\r\n                      v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                      class=\"ml-1\">\r\n                      <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 添加账号按钮 -->\r\n          <div class=\"mt-4 flex justify-center\">\r\n            <button\r\n              class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n              @click=\"openAddAccountModal(host)\">\r\n              <font-awesome-icon :icon=\"['fas', 'plus']\" class=\"mr-1\" />\r\n              添加账号\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" @confirm=\"updatePassword\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n          <div><span class=\"font-medium\">账号:</span> {{ currentAccount.username }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-3\">\r\n          <button @click=\"changePasswordModal.method = 'auto'\"\r\n            class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n            :class=\"changePasswordModal.method === 'auto' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-2\" />\r\n            自动生成\r\n          </button>\r\n          <button @click=\"changePasswordModal.method = 'manual'\"\r\n            class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n            :class=\"changePasswordModal.method === 'manual' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n            <font-awesome-icon :icon=\"['fas', 'edit']\" class=\"mr-2\" />\r\n            手动输入\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-if=\"changePasswordModal.method === 'auto'\" class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"changePasswordModal.policyId\" class=\"form-select\" @change=\"generatePassword()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n\r\n        <div class=\"mt-3\">\r\n          <div class=\"flex justify-between mb-1\">\r\n            <label class=\"form-label\">生成的密码</label>\r\n            <button @click=\"generatePassword()\" type=\"button\"\r\n              class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n              重新生成\r\n            </button>\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.generated ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.generatedPassword\" readonly class=\"form-control flex-1 bg-gray-50\" />\r\n            <button @click=\"passwordVisibility.generated = !passwordVisibility.generated\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.generated ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else class=\"space-y-4\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.new ? 'text' : 'password'\" v-model=\"changePasswordModal.newPassword\"\r\n              class=\"form-control flex-1\" placeholder=\"输入新密码\" />\r\n            <button @click=\"passwordVisibility.new = !passwordVisibility.new\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.new ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.confirm ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.confirmPassword\" class=\"form-control flex-1\"\r\n              :class=\"{ 'border-red-500': passwordMismatch }\" placeholder=\"再次输入新密码\" />\r\n            <button @click=\"passwordVisibility.confirm = !passwordVisibility.confirm\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.confirm ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n          <div v-if=\"passwordMismatch\" class=\"text-sm text-red-500 mt-1\">两次输入的密码不一致</div>\r\n        </div>\r\n\r\n        <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n      </div>\r\n\r\n      <div class=\"space-y-2 mt-4\">\r\n        <div class=\"form-label font-medium\">执行选项</div>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          <span class=\"ml-2\">立即执行</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          <span class=\"ml-2\">保存历史记录</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          <span class=\"ml-2\">记录审计日志</span>\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 添加账号弹窗 -->\r\n    <BaseModal v-model=\"addAccountModal.show\" title=\"添加账号\" @confirm=\"addAccount\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">账号名称</label>\r\n        <input type=\"text\" v-model=\"addAccountModal.username\" class=\"form-control\" placeholder=\"输入账号名称\" />\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">设为默认账号</label>\r\n        <div class=\"relative inline-block w-10 mr-2 align-middle select-none\">\r\n          <input type=\"checkbox\" v-model=\"addAccountModal.isDefault\"\r\n            class=\"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\" />\r\n          <label class=\"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"></label>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"addAccountModal.policyId\" class=\"form-select\" @change=\"generatePasswordForNewAccount()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <div class=\"flex justify-between mb-1\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <button @click=\"generatePasswordForNewAccount()\" type=\"button\"\r\n            class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n            重新生成\r\n          </button>\r\n        </div>\r\n        <div class=\"flex items-center\">\r\n          <input :type=\"passwordVisibility.newAccount ? 'text' : 'password'\" v-model=\"addAccountModal.password\" readonly\r\n            class=\"form-control flex-1 bg-gray-50\" />\r\n          <button @click=\"passwordVisibility.newAccount = !passwordVisibility.newAccount\" type=\"button\"\r\n            class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', passwordVisibility.newAccount ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      currentAccount: {},\r\n      passwordVisibility: {\r\n        generated: false,\r\n        new: false,\r\n        confirm: false,\r\n        newAccount: false\r\n      },\r\n      viewMode: 'table',\r\n      filterText: '',\r\n      accountFilterText: '',\r\n      statusFilter: 'all',\r\n      expiryFilter: 'all',\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      },\r\n\r\n      // 添加账号弹窗\r\n      addAccountModal: {\r\n        show: false,\r\n        username: '',\r\n        password: '',\r\n        isDefault: false,\r\n        policyId: 1\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    },\r\n\r\n    // 过滤后的主机列表\r\n    filteredHosts() {\r\n      return this.hosts.filter(host => {\r\n        // 文本过滤\r\n        const textMatch = this.filterText === '' ||\r\n          host.name.toLowerCase().includes(this.filterText.toLowerCase()) ||\r\n          host.ip.includes(this.filterText);\r\n\r\n        // 状态过滤\r\n        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;\r\n\r\n        return textMatch && statusMatch;\r\n      });\r\n    },\r\n\r\n    // 获取所有账号（扁平化处理）\r\n    getAllAccounts() {\r\n      // 为每个账号添加主机引用\r\n      const accounts = [];\r\n      this.filteredHosts.forEach(host => {\r\n        host.accounts.forEach(account => {\r\n          accounts.push({\r\n            ...account,\r\n            host: host\r\n          });\r\n        });\r\n      });\r\n      return accounts;\r\n    },\r\n\r\n    // 筛选后的账号\r\n    filteredAccounts() {\r\n      return this.getAllAccounts.filter(account => {\r\n        // 账号名称筛选\r\n        const accountMatch = this.accountFilterText === '' ||\r\n          account.username.toLowerCase().includes(this.accountFilterText.toLowerCase());\r\n\r\n        // 密码过期筛选\r\n        let expiryMatch = true;\r\n        if (this.expiryFilter !== 'all') {\r\n          const expiryStatus = this.isPasswordExpired(account).status;\r\n          if (this.expiryFilter === 'expired') {\r\n            expiryMatch = expiryStatus === 'expired';\r\n          } else if (this.expiryFilter === 'expiring-soon') {\r\n            expiryMatch = expiryStatus === 'danger' || expiryStatus === 'warning';\r\n          } else if (this.expiryFilter === 'valid') {\r\n            expiryMatch = expiryStatus === 'normal';\r\n          }\r\n        }\r\n\r\n        return accountMatch && expiryMatch;\r\n      });\r\n    },\r\n\r\n    // 分组后的账号列表\r\n    groupedAccounts() {\r\n      // 按主机ID分组\r\n      const groups = {};\r\n      this.filteredAccounts.forEach(account => {\r\n        const hostId = account.host.id;\r\n        if (!groups[hostId]) {\r\n          groups[hostId] = {\r\n            hostId: hostId,\r\n            hostName: account.host.name,\r\n            hostIp: account.host.ip,\r\n            host: account.host,\r\n            accounts: []\r\n          };\r\n        }\r\n        groups[hostId].accounts.push(account);\r\n      });\r\n\r\n      // 转换为数组\r\n      return Object.values(groups);\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host, account) {\r\n      this.currentHost = host\r\n      this.currentAccount = account\r\n      this.changePasswordModal.show = true\r\n      this.changePasswordModal.generatedPassword = this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword(policy) {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policyObj ? policyObj.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      if (this.changePasswordModal && !policy) {\r\n        this.changePasswordModal.generatedPassword = password\r\n      }\r\n\r\n      return password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto'\r\n          ? this.changePasswordModal.generatedPassword\r\n          : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          accountId: this.currentAccount.id,\r\n          password: password,\r\n          policyId: this.changePasswordModal.policyId\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的 ${this.currentAccount.username} 账号密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取所选策略\r\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取紧急策略\r\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    togglePasswordVisibility(hostId) {\r\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]\r\n    },\r\n\r\n    isPasswordExpired(account) {\r\n      if (!account.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }\r\n\r\n      // 解析过期时间\r\n      const expiryDate = new Date(account.passwordExpiryDate)\r\n      const now = new Date()\r\n\r\n      // 如果已过期\r\n      if (expiryDate < now) {\r\n        return {\r\n          status: 'expired',\r\n          days: 0,\r\n          text: '已过期'\r\n        }\r\n      }\r\n\r\n      // 计算剩余天数和小时数\r\n      const diffTime = expiryDate - now\r\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))\r\n      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n\r\n      // 根据剩余时间确定状态\r\n      let status = 'normal'\r\n      if (diffDays < 7) {\r\n        status = 'danger'  // 少于7天\r\n      } else if (diffDays < 14) {\r\n        status = 'warning' // 少于14天\r\n      }\r\n\r\n      // 格式化显示文本\r\n      let text = ''\r\n      if (diffDays > 0) {\r\n        text += `${diffDays}天`\r\n      }\r\n      if (diffHours > 0 || diffDays === 0) {\r\n        text += `${diffHours}小时`\r\n      }\r\n\r\n      return { status, days: diffDays, text: `剩余${text}` }\r\n    },\r\n\r\n    openAddAccountModal(host) {\r\n      this.currentHost = host\r\n      this.addAccountModal.show = true\r\n    },\r\n\r\n    async addAccount() {\r\n      if (!this.addAccountModal.username || !this.addAccountModal.password) {\r\n        alert('请填写完整的账号信息！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('addHostAccount', {\r\n          hostId: this.currentHost.id,\r\n          username: this.addAccountModal.username,\r\n          password: this.addAccountModal.password,\r\n          policyId: this.addAccountModal.policyId,\r\n          isDefault: this.addAccountModal.isDefault\r\n        })\r\n\r\n        this.addAccountModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为主机 ${this.currentHost.name} 添加账号！`)\r\n      } catch (error) {\r\n        console.error('添加账号失败', error)\r\n        alert('添加账号失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    generatePasswordForNewAccount() {\r\n      this.addAccountModal.password = this.generatePassword()\r\n    },\r\n\r\n    // 复制密码到剪贴板\r\n    copyPassword(account) {\r\n      // 创建一个临时输入框\r\n      const tempInput = document.createElement('input');\r\n      tempInput.value = account.password;\r\n      document.body.appendChild(tempInput);\r\n      tempInput.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(tempInput);\r\n\r\n      // 显示提示\r\n      alert(`已复制 ${account.host.name} 的 ${account.username} 账号密码到剪贴板！`);\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": ";;EAGSA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAA6C;;EACjDA,KAAK,EAAC;AAA6B;;EA2BnCA,KAAK,EAAC;AAA6B;;EAEjCA,KAAK,EAAC;AAAqD;;EAgB3DA,KAAK,EAAC;AAAU;;EAGdA,KAAK,EAAC;AAAsE;;EAM9EA,KAAK,EAAC;AAAU;;EAGdA,KAAK,EAAC;AAAsE;;EA9D7FC,GAAA;EA0FqCD,KAAK,EAAC;;;EAEhCA,KAAK,EAAC;AAAiE;;EACrEA,KAAK,EAAC;AAAuB;;EACvBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EAEzBA,KAAK,EAAC;AAAgB;;EAEvBA,KAAK,EAAC;AAAsN;;EAK5NA,KAAK,EAAC;AAAsN;;EAO3NA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAAY;;EAEjBE,KAAK,EAAC,KAAK;EAACF,KAAK,EAAC;;;EA4BnBA,KAAK,EAAC;AAAmC;;EAIxCA,KAAK,EAAC;AAAa;;EACjBG,OAAO,EAAC,GAAG;EAACH,KAAK,EAAC;;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAA2B;oBArJxD;;EAmKkBA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EAEpBA,KAAK,EAAC;AAAgC;;EAI9CA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAmC;;EA/KjEC,GAAA;EAiLoBD,KAAK,EAAC;;;EAKRA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAzLrDC,GAAA;EAmMoBD,KAAK,EAAC;;;EAnM1BC,GAAA;EAsMoFD,KAAK,EAAC;;;EAKxEA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAW;oBA7MxC;oBAAA;;EAyNkBA,KAAK,EAAC;AAA6B;;EAGnCA,KAAK,EAAC;AAAmD;;EACtDA,KAAK,EAAC;AAAgB;oBA7N3C;oBAAA;;EAAAC,GAAA;AAAA;;EAgPgBE,OAAO,EAAC,GAAG;EAACH,KAAK,EAAC;;;EACfA,KAAK,EAAC;AAAe;;EAWxBA,KAAK,EAAC;AAAsD;;EAE/DA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAuC;;EAC3CA,KAAK,EAAC;AAAmB;;EAGtBA,KAAK,EAAC;AAAmC;;EAC1CA,KAAK,EAAC;AAAuB;;EAOjCA,KAAK,EAAC;AAAW;;EAGbA,KAAK,EAAC;AAAwC;;EAC5CA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAmC;;EAjRjEC,GAAA;EAmRoBD,KAAK,EAAC;;oBAnR1B;;EAgSmBA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAmB;oBAlS9C;oBAAA;;EA8SmBA,KAAK,EAAC;AAAgC;;EAGlCA,KAAK,EAAC;AAAe;;EAjT5CC,GAAA;EA8TsBD,KAAK,EAAC;;;EAUbA,KAAK,EAAC;AAA0B;oBAxU/C;;EAsVWA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAiC;;EAOzCA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAgB;;EAjWnCC,GAAA;EAiXwDD,KAAK,EAAC;;oBAjX9D;;EAyXaA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAA2B;;EAQjCA,KAAK,EAAC;AAAmB;oBAlYxC;;EAAAC,GAAA;EA6YkBD,KAAK,EAAC;;;EACXA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;oBAhZxC;;EA0ZaA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;oBA5ZxC;;EAAAC,GAAA;EAqauCD,KAAK,EAAC;;;EAMlCA,KAAK,EAAC;AAAgB;;EAgBtBA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAiC;;EAMzCA,KAAK,EAAC;AAAiB;;EAKvBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAA0D;;EAOlEA,KAAK,EAAC;AAAiB;qBAjdlC;;EA0dWA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAA2B;;EAQjCA,KAAK,EAAC;AAAmB;qBAnetC;;EAifWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAY;qBAhgB7B;;EAygBWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EAhhB1CC,GAAA;EAshBmED,KAAK,EAAC;;;EAC1DA,KAAK,EAAC;AAAwB;;EAalCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;qBA/jB7B;;EAwkBWA,KAAK,EAAC;AAAY;;EAkBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;qBApmB7B;;EA6mBWA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;;;;;;uBAvnB3BI,mBAAA,CA6nBM,cA5nBJC,mBAAA,UAAa,EACbC,mBAAA,CAmFM,OAnFNC,UAmFM,GAlFJD,mBAAA,CAiFM,OAjFNE,UAiFM,GAhFJF,mBAAA,CAyBM,OAzBNG,UAyBM,GAxBJH,mBAAA,CAKS;IAJPN,KAAK,EAAC,qNAAqN;IAC1NU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,kBAAA,IAAAD,QAAA,CAAAC,kBAAA,IAAAF,IAAA,CAAkB;MAC1BG,YAAA,CAA0EC,4BAAA;IAAtDC,IAAI,EAAE,+BAA+B;IAAEjB,KAAK,EAAC;kCACjEM,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAKS;IAJPN,KAAK,EAAC,wNAAwN;IAC7NU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAK,oBAAA,IAAAL,QAAA,CAAAK,oBAAA,IAAAN,IAAA,CAAoB;MAC5BG,YAAA,CAAyDC,4BAAA;IAArCC,IAAI,EAAE,cAAc;IAAEjB,KAAK,EAAC;kCAChDM,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAKS;IAJPN,KAAK,EAAC,8NAA8N;IACnOU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAM,mBAAA,IAAAN,QAAA,CAAAM,mBAAA,IAAAP,IAAA,CAAmB;MAC3BG,YAAA,CAAgEC,4BAAA;IAA5CC,IAAI,EAAE,qBAAqB;IAAEjB,KAAK,EAAC;kCACvDM,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAKS;IAJPN,KAAK,EAAC,2NAA2N;IAChOU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEQ,IAAA,CAAAC,wBAAA,IAAAD,IAAA,CAAAC,wBAAA,IAAAT,IAAA,CAAwB;MAChCG,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEjB,KAAK,EAAC;kCAClDM,mBAAA,CAAmB,cAAb,QAAM,qB,KAIhBA,mBAAA,CAoDM,OApDNgB,UAoDM,GAnDJjB,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNiB,UAaM,GAZJjB,mBAAA,CAKS;IALDN,KAAK,EAnCzBwB,eAAA,EAmC0B,8BAA8B;MAAA,0BACNC,KAAA,CAAAC,QAAQ;MAAA,6BAA2CD,KAAA,CAAAC,QAAQ;IAAA;IAC9FhB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAgB,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAChBX,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEjB,KAAK,EAAC;kCAtChE4B,gBAAA,CAsCyE,MAE7D,G,kBACAtB,mBAAA,CAKS;IALDN,KAAK,EAzCzBwB,eAAA,EAyC0B,8BAA8B;MAAA,0BACNC,KAAA,CAAAC,QAAQ;MAAA,6BAA0CD,KAAA,CAAAC,QAAQ;IAAA;IAC7FhB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAgB,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAChBX,YAAA,CAA8DC,4BAAA;IAA1CC,IAAI,EAAE,mBAAmB;IAAEjB,KAAK,EAAC;kCA5CnE4B,gBAAA,CA4C4E,MAEhE,G,oBAGFvB,mBAAA,QAAW,EACXC,mBAAA,CAMM,OANNuB,UAMM,G,gBALJvB,mBAAA,CAC2L;IADpLwB,IAAI,EAAC,MAAM;IAnD9B,uBAAAnB,MAAA,QAAAA,MAAA,MAAAgB,MAAA,IAmDwCF,KAAA,CAAAM,UAAU,GAAAJ,MAAA;IAAEK,WAAW,EAAC,SAAS;IAC3DhC,KAAK,EAAC;iDADoByB,KAAA,CAAAM,UAAU,E,GAEtCzB,mBAAA,CAEM,OAFN2B,UAEM,GADJlB,YAAA,CAAqEC,4BAAA;IAAjDC,IAAI,EAAE,iBAAiB;IAAEjB,KAAK,EAAC;UAIvDK,mBAAA,UAAa,EACbC,mBAAA,CAMM,OANN4B,UAMM,G,gBALJ5B,mBAAA,CAC2L;IADpLwB,IAAI,EAAC,MAAM;IA5D9B,uBAAAnB,MAAA,QAAAA,MAAA,MAAAgB,MAAA,IA4DwCF,KAAA,CAAAU,iBAAiB,GAAAR,MAAA;IAAEK,WAAW,EAAC,SAAS;IAClEhC,KAAK,EAAC;iDADoByB,KAAA,CAAAU,iBAAiB,E,GAE7C7B,mBAAA,CAEM,OAFN8B,UAEM,GADJrB,YAAA,CAAmEC,4BAAA;IAA/CC,IAAI,EAAE,eAAe;IAAEjB,KAAK,EAAC;UAIrDK,mBAAA,UAAa,E,gBACbC,mBAAA,CAMS;IA1EnB,uBAAAK,MAAA,QAAAA,MAAA,MAAAgB,MAAA,IAoE2BF,KAAA,CAAAY,YAAY,GAAAV,MAAA;IAC3B3B,KAAK,EAAC;kCACNM,mBAAA,CAAiC;IAAzBgC,KAAK,EAAC;EAAK,GAAC,MAAI,qBACxBhC,mBAAA,CAAkC;IAA1BgC,KAAK,EAAC;EAAQ,GAAC,IAAE,qBACzBhC,mBAAA,CAAmC;IAA3BgC,KAAK,EAAC;EAAS,GAAC,IAAE,qBAC1BhC,mBAAA,CAAiC;IAAzBgC,KAAK,EAAC;EAAO,GAAC,IAAE,oB,2CALTb,KAAA,CAAAY,YAAY,E,GAQ7BhC,mBAAA,cAAiB,E,gBACjBC,mBAAA,CAMS;IAnFnB,uBAAAK,MAAA,QAAAA,MAAA,MAAAgB,MAAA,IA6E2BF,KAAA,CAAAc,YAAY,GAAAZ,MAAA;IAC3B3B,KAAK,EAAC;kCACNM,mBAAA,CAAiC;IAAzBgC,KAAK,EAAC;EAAK,GAAC,MAAI,qBACxBhC,mBAAA,CAAoC;IAA5BgC,KAAK,EAAC;EAAS,GAAC,KAAG,qBAC3BhC,mBAAA,CAA2C;IAAnCgC,KAAK,EAAC;EAAe,GAAC,MAAI,qBAClChC,mBAAA,CAAmC;IAA3BgC,KAAK,EAAC;EAAO,GAAC,MAAI,oB,2CALXb,KAAA,CAAAc,YAAY,E,SAWnClC,mBAAA,UAAa,EACbA,mBAAA,UAAa,EACFoB,KAAA,CAAAC,QAAQ,gB,cAAnBtB,mBAAA,CA+JM,OA/JNoC,WA+JM,GA9JJnC,mBAAA,eAAkB,EAClBC,mBAAA,CAiBM,OAjBNmC,WAiBM,GAhBJnC,mBAAA,CAGM,OAHNoC,WAGM,G,4BAhGdd,gBAAA,CA6F2C,MAC9B,IAAAtB,mBAAA,CAA8D,QAA9DqC,WAA8D,EAAAC,gBAAA,CAAjC/B,QAAA,CAAAgC,gBAAgB,CAACC,MAAM,kB,4BA9FjElB,gBAAA,CA8F2E,UAC9D,IAAAtB,mBAAA,CAA4D,QAA5DyC,WAA4D,EAAAH,gBAAA,CAA/B/B,QAAA,CAAAmC,cAAc,CAACF,MAAM,kB,4BA/F/DlB,gBAAA,CA+FyE,MACjE,G,GACAtB,mBAAA,CAWM,OAXN2C,WAWM,GAVJ3C,mBAAA,CAIS,UAJT4C,WAIS,GAFPnC,YAAA,CAAiEC,4BAAA;IAA7CC,IAAI,EAAE,sBAAsB;IAAEjB,KAAK,EAAC;kCApGpE4B,gBAAA,CAoG6E,MAEnE,G,GACAtB,mBAAA,CAIS,UAJT6C,WAIS,GAFPpC,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEjB,KAAK,EAAC;kCAzG9D4B,gBAAA,CAyGuE,MAE7D,G,OAIJtB,mBAAA,CAyIQ,SAzIR8C,WAyIQ,GAxIN9C,mBAAA,CA6BQ,SA7BR+C,WA6BQ,GA5BN/C,mBAAA,CA2BK,aA1BHA,mBAAA,CAIK,MAJLgD,WAIK,GAHHvC,YAAA,CAEiBwC,yBAAA;IArH/BC,UAAA,EAmHuC/B,KAAA,CAAAgC,SAAS;IAnHhD,wB,sCAmHuChC,KAAA,CAAAgC,SAAS,GAAA9B,MAAA,GAAsBd,QAAA,CAAA6C,eAAe;;IAnHrFC,OAAA,EAAAC,QAAA,CAmHuF,MAEzEjD,MAAA,SAAAA,MAAA,QArHdiB,gBAAA,CAmHuF,OAEzE,E;IArHdiC,CAAA;0FAuHYvD,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,QAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,YAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,UAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,qB,KAGJM,mBAAA,CAyGQ,SAzGRwD,WAyGQ,GAxGNzD,mBAAA,aAAgB,G,kBAChBD,mBAAA,CA6FW2D,SAAA,QA7OrBC,WAAA,CAgJwCnD,QAAA,CAAAoD,eAAe,EAA5BC,SAAS;yBAhJpC9D,mBAAA,CAAA2D,SAAA;MAAA9D,GAAA,EAgJ+DiE,SAAS,CAACC;QAC7D9D,mBAAA,aAAgB,EAChBC,mBAAA,CAaK,MAbL8D,WAaK,GAZH9D,mBAAA,CAWK,MAXL+D,WAWK,GAVH/D,mBAAA,CASM,OATNgE,WASM,GARJhE,mBAAA,CAA8F,OAA9FiE,WAA8F,EAAA3B,gBAAA,CAApDsB,SAAS,CAACM,QAAQ,IAAG,IAAE,GAAA5B,gBAAA,CAAGsB,SAAS,CAACO,MAAM,IAAG,GAAC,iBACxFnE,mBAAA,CAMM,cALJA,mBAAA,CAIS;MAJDN,KAAK,EAAC,8DAA8D;MACzEU,OAAK,EAAAiB,MAAA,IAAEd,QAAA,CAAA6D,mBAAmB,CAACR,SAAS,CAACS,IAAI;QAC1C5D,YAAA,CAA0DC,4BAAA;MAAtCC,IAAI,EAAE,eAAe;MAAEjB,KAAK,EAAC;oCAzJvE4B,gBAAA,CAyJgF,QAE5D,G,iBA3JpBgD,WAAA,E,SAgKYvE,mBAAA,SAAY,G,kBACZD,mBAAA,CA2EK2D,SAAA,QA5OjBC,WAAA,CAiKkDE,SAAS,CAACW,QAAQ,EAjKpE,CAiKwBC,OAAO,EAAEC,YAAY;2BAAjC3E,mBAAA,CA2EK;QA3EsDH,GAAG,EAAE6E,OAAO,CAACE,EAAE;QACvEhF,KAAK,EAlKpBwB,eAAA;UAAA,cAkKsCuD,YAAY;UAAA;QAAA;UACpCzE,mBAAA,CAMK,MANL2E,WAMK,GALH3E,mBAAA,CAIM,OAJN4E,WAIM,GAHJnE,YAAA,CAEiBwC,yBAAA;QAvKnCC,UAAA,EAqK2CsB,OAAO,CAACH,IAAI,CAACQ,QAAQ;QArKhE,uBAAAxD,MAAA,IAqK2CmD,OAAO,CAACH,IAAI,CAACQ,QAAQ,GAAAxD,MAAA;QAAE3B,KAAK,EAAC;;QArKxE2D,OAAA,EAAAC,QAAA,CAsKoB,MAA2E,CAA3EtD,mBAAA,CAA2E,QAA3E8E,WAA2E,EAAAxC,gBAAA,CAA3BkC,OAAO,CAACH,IAAI,CAACU,IAAI,iB;QAtKrFxB,CAAA;sFA0KcvD,mBAAA,CAEK,MAFLgF,WAEK,GADHhF,mBAAA,CAA8D,OAA9DiF,WAA8D,EAAA3C,gBAAA,CAAxBkC,OAAO,CAACH,IAAI,CAACa,EAAE,iB,GAEvDlF,mBAAA,CAQK,MARLmF,WAQK,GAPHnF,mBAAA,CAMM,OANNoF,WAMM,GALJpF,mBAAA,CAA6E,QAA7EqF,WAA6E,EAAA/C,gBAAA,CAA1BkC,OAAO,CAACc,QAAQ,kBACvDd,OAAO,CAACe,SAAS,I,cAA7BzF,mBAAA,CAGO,QAHP0F,WAGO,EAFqG,MAE5G,KAnLlBzF,mBAAA,e,KAsLcC,mBAAA,CAEK,MAFLyF,WAEK,GADHzF,mBAAA,CAAgF,OAAhF0F,WAAgF,EAAApD,gBAAA,CAA1CkC,OAAO,CAACmB,kBAAkB,wB,GAElE3F,mBAAA,CAiBK,MAjBL4F,WAiBK,GAhBH5F,mBAAA,CAeM;QAfAN,KAAK,EA1L3BwB,eAAA;;qCA0LiLX,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM,iBAAiBvF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM;2CAAoEvF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM;uCAAgEvF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM;;UA1L1cxE,gBAAA,CAAAgB,gBAAA,CAgMqB/B,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEuB,IAAI,IAAG,GACrC,iBACQxF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM,kBAAkBvF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM,iB,cAD5FhG,mBAAA,CAIO,QAJPkG,WAIO,GADLvF,YAAA,CAA6DC,4BAAA;QAAzCC,IAAI,EAAE;MAA+B,G,KAE1CJ,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM,kB,cAAlDhG,mBAAA,CAEO,QAFPmG,WAEO,GADLxF,YAAA,CAA2DC,4BAAA;QAAvCC,IAAI,EAAE;MAA6B,G,KAvM3EZ,mBAAA,e,oBA2McC,mBAAA,CAaK,MAbLkG,WAaK,GAZHlG,mBAAA,CAWM,OAXNmG,WAWM,GAVJnG,mBAAA,CAIM,OAJNoG,WAIM,GAHJpG,mBAAA,CAEkG;QAF1FwB,IAAI,EAAEL,KAAA,CAAAkF,kBAAkB,CAAC7B,OAAO,CAACE,EAAE;QAA0B1C,KAAK,EAAEwC,OAAO,CAAC8B,QAAQ;QAC1FC,QAAQ,EAAR,EAAQ;QACR7G,KAAK,EAAC;8BAhN5B8G,WAAA,E,GAkNkBxG,mBAAA,CAIS;QAJAI,OAAK,EAAAiB,MAAA,IAAEd,QAAA,CAAAkG,wBAAwB,CAACjC,OAAO,CAACE,EAAE;QACjDhF,KAAK,EAAC;UACNe,YAAA,CACoBC,4BAAA;QADAC,IAAI,UAAUQ,KAAA,CAAAkF,kBAAkB,CAAC7B,OAAO,CAACE,EAAE;QAC7DhF,KAAK,EAAC;yDArN5BgH,WAAA,E,KAyNc1G,mBAAA,CAEK,MAFL2G,WAEK,GADHlG,YAAA,CAA2CmG,sBAAA;QAA7BpF,IAAI,EAAEgD,OAAO,CAACH,IAAI,CAACyB;2CAEnC9F,mBAAA,CAeK,MAfL6G,WAeK,GAdH7G,mBAAA,CAaM,OAbN8G,WAaM,GAZJ9G,mBAAA,CAKS;QAJPN,KAAK,EAAC,0NAA0N;QAC/NU,OAAK,EAAAiB,MAAA,IAAEd,QAAA,CAAAwG,uBAAuB,CAACvC,OAAO,CAACH,IAAI,EAAEG,OAAO;UACrD/D,YAAA,CAAyDC,4BAAA;QAArCC,IAAI,EAAE,cAAc;QAAEjB,KAAK,EAAC;sCAjOpE4B,gBAAA,CAiO6E,QAE3D,G,iBAnOlB0F,WAAA,GAoOkBhH,mBAAA,CAKS;QAJPN,KAAK,EAAC,sNAAsN;QAC3NU,OAAK,EAAAiB,MAAA,IAAEd,QAAA,CAAA0G,YAAY,CAACzC,OAAO;UAC5B/D,YAAA,CAA0DC,4BAAA;QAAtCC,IAAI,EAAE,eAAe;QAAEjB,KAAK,EAAC;sCAvOrE4B,gBAAA,CAuO8E,MAE5D,G,iBAzOlB4F,WAAA,E;;kCA8OUnH,mBAAA,WAAc,EACJQ,QAAA,CAAAgC,gBAAgB,CAACC,MAAM,U,cAAjC1C,mBAAA,CAOK,MAtPfqH,WAAA,GAgPYnH,mBAAA,CAKK,MALLoH,WAKK,GAJHpH,mBAAA,CAGM,OAHNqH,WAGM,GAFJ5G,YAAA,CAAqEC,4BAAA;IAAjDC,IAAI,EAAE,iBAAiB;IAAEjB,KAAK,EAAC;kCACnDM,mBAAA,CAAkB,WAAf,aAAW,qB,SAnP9BD,mBAAA,e,wBA4PID,mBAAA,CAsFM2D,SAAA;IAlVV9D,GAAA;EAAA,IA2PII,mBAAA,UAAa,EACbC,mBAAA,CAsFM,OAtFNsH,WAsFM,I,kBArFJxH,mBAAA,CAoFM2D,SAAA,QAjVZC,WAAA,CA6P0BnD,QAAA,CAAAgH,aAAa,EAArBlD,IAAI;yBAAhBvE,mBAAA,CAoFM;MApF8BH,GAAG,EAAE0E,IAAI,CAACK,EAAE;MAAEhF,KAAK,EAAC;QACtDM,mBAAA,CAkFM,OAlFNwH,WAkFM,GAjFJzH,mBAAA,UAAa,EACbC,mBAAA,CASM,OATNyH,WASM,GARJzH,mBAAA,CAMM,OANN0H,WAMM,GALJjH,YAAA,CAAuDwC,yBAAA;MAlQrEC,UAAA,EAkQuCmB,IAAI,CAACQ,QAAQ;MAlQpD,uBAAAxD,MAAA,IAkQuCgD,IAAI,CAACQ,QAAQ,GAAAxD,MAAA;MAAE3B,KAAK,EAAC;oEAC9CM,mBAAA,CAGM,cAFJA,mBAAA,CAAkE,MAAlE2H,WAAkE,EAAArF,gBAAA,CAAjB+B,IAAI,CAACU,IAAI,kBAC1D/E,mBAAA,CAAkD,KAAlD4H,WAAkD,EAAAtF,gBAAA,CAAd+B,IAAI,CAACa,EAAE,iB,KAG/CzE,YAAA,CAAmCmG,sBAAA;MAArBpF,IAAI,EAAE6C,IAAI,CAACyB;yCAG3B/F,mBAAA,UAAa,EACbC,mBAAA,CAyDM,OAzDN6H,WAyDM,I,kBAxDJ/H,mBAAA,CAuDM2D,SAAA,QApUlBC,WAAA,CA6QmCW,IAAI,CAACE,QAAQ,EAAxBC,OAAO;2BAAnB1E,mBAAA,CAuDM;QAvDiCH,GAAG,EAAE6E,OAAO,CAACE,EAAE;QAAEhF,KAAK,EA7QzEwB,eAAA,EA6Q0E,uCAAuC;UAAA,gCACzDsD,OAAO,CAACe;QAAS;UAC3DvF,mBAAA,CAcM,OAdN8H,WAcM,GAbJ9H,mBAAA,CAMM,OANN+H,WAMM,GALJ/H,mBAAA,CAA6E,QAA7EgI,WAA6E,EAAA1F,gBAAA,CAA1BkC,OAAO,CAACc,QAAQ,kBACvDd,OAAO,CAACe,SAAS,I,cAA7BzF,mBAAA,CAGO,QAHPmI,WAGO,EAFqG,MAE5G,KArRlBlI,mBAAA,e,GAuRgBC,mBAAA,CAKS;QAJPN,KAAK,EAAC,wNAAwN;QAC7NU,OAAK,EAAAiB,MAAA,IAAEd,QAAA,CAAAwG,uBAAuB,CAAC1C,IAAI,EAAEG,OAAO;UAC7C/D,YAAA,CAAyDC,4BAAA;QAArCC,IAAI,EAAE,cAAc;QAAEjB,KAAK,EAAC;sCA1RlE4B,gBAAA,CA0R2E,QAE3D,G,iBA5RhB4G,WAAA,E,GA+RcnI,mBAAA,UAAa,EACbC,mBAAA,CAWM,OAXNmI,WAWM,G,4BAVJnI,mBAAA,CAA4D;QAAvDN,KAAK,EAAC;MAAwC,GAAC,IAAE,sBACtDM,mBAAA,CAQM,OARNoI,WAQM,GAPJpI,mBAAA,CACkG;QAD1FwB,IAAI,EAAEL,KAAA,CAAAkF,kBAAkB,CAAC7B,OAAO,CAACE,EAAE;QAA0B1C,KAAK,EAAEwC,OAAO,CAAC8B,QAAQ;QAAEC,QAAQ,EAAR,EAAQ;QACpG7G,KAAK,EAAC;8BApS1B2I,WAAA,GAqSkBrI,mBAAA,CAIS;QAJAI,OAAK,EAAAiB,MAAA,IAAEd,QAAA,CAAAkG,wBAAwB,CAACjC,OAAO,CAACE,EAAE;QACjDhF,KAAK,EAAC;UACNe,YAAA,CACoBC,4BAAA;QADAC,IAAI,UAAUQ,KAAA,CAAAkF,kBAAkB,CAAC7B,OAAO,CAACE,EAAE;QAC7DhF,KAAK,EAAC;yDAxS5B4I,WAAA,E,KA6ScvI,mBAAA,YAAe,EACfC,mBAAA,CAqBM,OArBNuI,WAqBM,GApBJvI,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAwD;QAAnDN,KAAK,EAAC;MAAgC,GAAC,QAAM,sBAClDM,mBAAA,CAAwE,OAAxEwI,WAAwE,EAAAlG,gBAAA,CAA1CkC,OAAO,CAACmB,kBAAkB,wB,GAE1D3F,mBAAA,CAeM,c,4BAdJA,mBAAA,CAAsD;QAAjDN,KAAK,EAAC;MAAgC,GAAC,MAAI,sBAChDM,mBAAA,CAYM;QAZAN,KAAK,EArT7BwB,eAAA;;qCAqTqLX,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM,iBAAiBvF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM;2CAAsEvF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM;uCAAkEvF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM;;UArTldxE,gBAAA,CAAAgB,gBAAA,CA2TuB/B,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEuB,IAAI,IAAG,GACrC,iBACQxF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM,kBAAkBvF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM,iB,cAD5FhG,mBAAA,CAIO,QAJP2I,WAIO,GADLhI,YAAA,CAA6DC,4BAAA;QAAzCC,IAAI,EAAE;MAA+B,G,KA/T/EZ,mBAAA,e;sCAuUUA,mBAAA,YAAe,EACfC,mBAAA,CAOM,OAPN0I,WAOM,GANJ1I,mBAAA,CAKS;MAJPN,KAAK,EAAC,sNAAsN;MAC3NU,OAAK,EAAAiB,MAAA,IAAEd,QAAA,CAAA6D,mBAAmB,CAACC,IAAI;QAChC5D,YAAA,CAA0DC,4BAAA;MAAtCC,IAAI,EAAE,eAAe;MAAEjB,KAAK,EAAC;oCA5U/D4B,gBAAA,CA4UwE,QAE5D,G,iBA9UZqH,WAAA,E;sFAoVI5I,mBAAA,YAAe,EACfU,YAAA,CAkGYmI,oBAAA;IAvbhB1F,UAAA,EAqVwB/B,KAAA,CAAA0H,mBAAmB,CAACC,IAAI;IArVhD,uBAAAzI,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAqVwBF,KAAA,CAAA0H,mBAAmB,CAACC,IAAI,GAAAzH,MAAA;IAAE0H,KAAK,EAAC,MAAM;IAAEC,SAAO,EAAEzI,QAAA,CAAA0I,cAAc;IAAGC,OAAO,EAAE/H,KAAA,CAAAgI;;IArVnG9F,OAAA,EAAAC,QAAA,CAsVM,MAOM,CAPNtD,mBAAA,CAOM,OAPNoJ,WAOM,G,4BANJpJ,mBAAA,CAAwC;MAAnCN,KAAK,EAAC;IAAkB,GAAC,MAAI,sBAClCM,mBAAA,CAIM,OAJNqJ,WAIM,GAHJrJ,mBAAA,CAAuE,c,4BAAlEA,mBAAA,CAAqC;MAA/BN,KAAK,EAAC;IAAa,GAAC,MAAI,sBAzV7C4B,gBAAA,CAyVoD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAmI,WAAW,CAACvE,IAAI,iB,GAC9D/E,mBAAA,CAAsE,c,4BAAjEA,mBAAA,CAAsC;MAAhCN,KAAK,EAAC;IAAa,GAAC,OAAK,sBA1V9C4B,gBAAA,CA0VqD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAmI,WAAW,CAACpE,EAAE,iB,GAC7DlF,mBAAA,CAA6E,c,4BAAxEA,mBAAA,CAAoC;MAA9BN,KAAK,EAAC;IAAa,GAAC,KAAG,sBA3V5C4B,gBAAA,CA2VmD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAoI,cAAc,CAACjE,QAAQ,iB,OAIxEtF,mBAAA,CAgBM,OAhBNwJ,WAgBM,G,4BAfJxJ,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAaM,OAbNyJ,WAaM,GAZJzJ,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEF,KAAA,CAAA0H,mBAAmB,CAACa,MAAM;MACxChK,KAAK,EAnWjBwB,eAAA,EAmWkB,iFAAiF,EAC/EC,KAAA,CAAA0H,mBAAmB,CAACa,MAAM;QAClCjJ,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;oCArWjE4B,gBAAA,CAqW0E,QAEhE,G,kBACAtB,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEF,KAAA,CAAA0H,mBAAmB,CAACa,MAAM;MACxChK,KAAK,EAzWjBwB,eAAA,EAyWkB,iFAAiF,EAC/EC,KAAA,CAAA0H,mBAAmB,CAACa,MAAM;QAClCjJ,YAAA,CAA0DC,4BAAA;MAAtCC,IAAI,EAAE,eAAe;MAAEjB,KAAK,EAAC;oCA3W7D4B,gBAAA,CA2WsE,QAE5D,G,sBAIOH,KAAA,CAAA0H,mBAAmB,CAACa,MAAM,e,cAArC5J,mBAAA,CA0BM,OA1BN6J,WA0BM,G,4BAzBJ3J,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MAvXjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAmXyBF,KAAA,CAAA0H,mBAAmB,CAACe,QAAQ,GAAAvI,MAAA;MAAE3B,KAAK,EAAC,aAAa;MAAEmK,QAAM,EAAAxJ,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEd,QAAA,CAAAuJ,gBAAgB;2BAC1FhK,mBAAA,CAES2D,SAAA,QAtXnBC,WAAA,CAoXmC5C,IAAA,CAAAiJ,QAAQ,EAAlBC,MAAM;2BAArBlK,mBAAA,CAES;QAF2BH,GAAG,EAAEqK,MAAM,CAACtF,EAAE;QAAG1C,KAAK,EAAEgI,MAAM,CAACtF;0BAC9DsF,MAAM,CAACjF,IAAI,IAAG,UAAQ,GAAAzC,gBAAA,CAAG0H,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA3H,gBAAA,CAAG0H,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAtXVC,WAAA;6FAmXyBhJ,KAAA,CAAA0H,mBAAmB,CAACe,QAAQ,E,GAM7C5J,mBAAA,CAiBM,OAjBNoK,WAiBM,GAhBJpK,mBAAA,CAOM,OAPNqK,WAOM,G,4BANJrK,mBAAA,CAAuC;MAAhCN,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BM,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEd,QAAA,CAAAuJ,gBAAgB;MAAItI,IAAI,EAAC,QAAQ;MAC/C9B,KAAK,EAAC;QACNe,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;oCA9XnE4B,gBAAA,CA8X4E,QAEhE,G,KAEFtB,mBAAA,CAOM,OAPNsK,WAOM,G,gBANJtK,mBAAA,CACoG;MAD5FwB,IAAI,EAAEL,KAAA,CAAAkF,kBAAkB,CAACkE,SAAS;MAnYtD,uBAAAlK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAoYuBF,KAAA,CAAA0H,mBAAmB,CAAC2B,iBAAiB,GAAAnJ,MAAA;MAAEkF,QAAQ,EAAR,EAAQ;MAAC7G,KAAK,EAAC;4BApY7E+K,WAAA,I,iBAoYuBtJ,KAAA,CAAA0H,mBAAmB,CAAC2B,iBAAiB,E,GAChDxK,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEF,KAAA,CAAAkF,kBAAkB,CAACkE,SAAS,IAAIpJ,KAAA,CAAAkF,kBAAkB,CAACkE,SAAS;MAAE/I,IAAI,EAAC,QAAQ;MACzF9B,KAAK,EAAC;QACNe,YAAA,CAAyGC,4BAAA;MAArFC,IAAI,UAAUQ,KAAA,CAAAkF,kBAAkB,CAACkE,SAAS;MAAyB7K,KAAK,EAAC;gEAMrGI,mBAAA,CA4BM,OA5BN4K,WA4BM,GA3BJ1K,mBAAA,CAUM,OAVN2K,WAUM,G,4BATJ3K,mBAAA,CAAqC;MAA9BN,KAAK,EAAC;IAAY,GAAC,KAAG,sBAC7BM,mBAAA,CAOM,OAPN4K,WAOM,G,gBANJ5K,mBAAA,CACoD;MAD5CwB,IAAI,EAAEL,KAAA,CAAAkF,kBAAkB,CAACwE,GAAG;MAjZhD,uBAAAxK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAiZiFF,KAAA,CAAA0H,mBAAmB,CAACiC,WAAW,GAAAzJ,MAAA;MAClG3B,KAAK,EAAC,qBAAqB;MAACgC,WAAW,EAAC;4BAlZtDqJ,WAAA,I,iBAiZiF5J,KAAA,CAAA0H,mBAAmB,CAACiC,WAAW,E,GAEpG9K,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEF,KAAA,CAAAkF,kBAAkB,CAACwE,GAAG,IAAI1J,KAAA,CAAAkF,kBAAkB,CAACwE,GAAG;MAAErJ,IAAI,EAAC,QAAQ;MAC7E9B,KAAK,EAAC;QACNe,YAAA,CAAmGC,4BAAA;MAA/EC,IAAI,UAAUQ,KAAA,CAAAkF,kBAAkB,CAACwE,GAAG;MAAyBnL,KAAK,EAAC;6CAK7FM,mBAAA,CAYM,OAZNgL,WAYM,G,4BAXJhL,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CAQM,OARNiL,WAQM,G,gBAPJjL,mBAAA,CAE0E;MAFlEwB,IAAI,EAAEL,KAAA,CAAAkF,kBAAkB,CAAC6E,OAAO;MA7ZpD,uBAAA7K,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA8ZuBF,KAAA,CAAA0H,mBAAmB,CAACsC,eAAe,GAAA9J,MAAA;MAAE3B,KAAK,EA9ZjEwB,eAAA,EA8ZkE,qBAAqB;QAAA,kBAC7CX,QAAA,CAAA6K;MAAgB;MAAI1J,WAAW,EAAC;oCA/Z1E2J,WAAA,I,iBA8ZuBlK,KAAA,CAAA0H,mBAAmB,CAACsC,eAAe,E,GAE9CnL,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEF,KAAA,CAAAkF,kBAAkB,CAAC6E,OAAO,IAAI/J,KAAA,CAAAkF,kBAAkB,CAAC6E,OAAO;MAAE1J,IAAI,EAAC,QAAQ;MACrF9B,KAAK,EAAC;QACNe,YAAA,CAAuGC,4BAAA;MAAnFC,IAAI,UAAUQ,KAAA,CAAAkF,kBAAkB,CAAC6E,OAAO;MAAyBxL,KAAK,EAAC;2CAGpFa,QAAA,CAAA6K,gBAAgB,I,cAA3BtL,mBAAA,CAA+E,OAA/EwL,WAA+E,EAAhB,YAAU,KAranFvL,mBAAA,e,GAwaQU,YAAA,CAAqE8K,gCAAA;MAA7CjF,QAAQ,EAAEnF,KAAA,CAAA0H,mBAAmB,CAACiC;8CAGxD9K,mBAAA,CAWM,OAXNwL,WAWM,G,4BAVJxL,mBAAA,CAA8C;MAAzCN,KAAK,EAAC;IAAwB,GAAC,MAAI,sBACxCe,YAAA,CAEiBwC,yBAAA;MA/azBC,UAAA,EA6aiC/B,KAAA,CAAA0H,mBAAmB,CAAC4C,kBAAkB;MA7avE,uBAAApL,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA6aiCF,KAAA,CAAA0H,mBAAmB,CAAC4C,kBAAkB,GAAApK,MAAA;;MA7avEgC,OAAA,EAAAC,QAAA,CA8aU,MAA8BjD,MAAA,SAAAA,MAAA,QAA9BL,mBAAA,CAA8B;QAAxBN,KAAK,EAAC;MAAM,GAAC,MAAI,oB;MA9ajC6D,CAAA;uCAgbQ9C,YAAA,CAEiBwC,yBAAA;MAlbzBC,UAAA,EAgbiC/B,KAAA,CAAA0H,mBAAmB,CAAC6C,WAAW;MAhbhE,uBAAArL,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAgbiCF,KAAA,CAAA0H,mBAAmB,CAAC6C,WAAW,GAAArK,MAAA;;MAhbhEgC,OAAA,EAAAC,QAAA,CAibU,MAAgCjD,MAAA,SAAAA,MAAA,QAAhCL,mBAAA,CAAgC;QAA1BN,KAAK,EAAC;MAAM,GAAC,QAAM,oB;MAjbnC6D,CAAA;uCAmbQ9C,YAAA,CAEiBwC,yBAAA;MArbzBC,UAAA,EAmbiC/B,KAAA,CAAA0H,mBAAmB,CAAC8C,QAAQ;MAnb7D,uBAAAtL,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAmbiCF,KAAA,CAAA0H,mBAAmB,CAAC8C,QAAQ,GAAAtK,MAAA;;MAnb7DgC,OAAA,EAAAC,QAAA,CAobU,MAAgCjD,MAAA,SAAAA,MAAA,QAAhCL,mBAAA,CAAgC;QAA1BN,KAAK,EAAC;MAAM,GAAC,QAAM,oB;MApbnC6D,CAAA;;IAAAA,CAAA;6DAybIxD,mBAAA,YAAe,EACfU,YAAA,CAkDYmI,oBAAA;IA5ehB1F,UAAA,EA0bwB/B,KAAA,CAAAyK,eAAe,CAAC9C,IAAI;IA1b5C,uBAAAzI,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA0bwBF,KAAA,CAAAyK,eAAe,CAAC9C,IAAI,GAAAzH,MAAA;IAAE0H,KAAK,EAAC,MAAM;IAAEC,SAAO,EAAEzI,QAAA,CAAAsL,UAAU;IAAG3C,OAAO,EAAE/H,KAAA,CAAAgI;;IA1b3F9F,OAAA,EAAAC,QAAA,CA2bM,MAMM,CANNtD,mBAAA,CAMM,OANN8L,WAMM,G,8BALJ9L,mBAAA,CAAwC;MAAnCN,KAAK,EAAC;IAAkB,GAAC,MAAI,sBAClCM,mBAAA,CAGM,OAHN+L,WAGM,GAFJ/L,mBAAA,CAAuE,c,4BAAlEA,mBAAA,CAAqC;MAA/BN,KAAK,EAAC;IAAa,GAAC,MAAI,sBA9b7C4B,gBAAA,CA8boD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAmI,WAAW,CAACvE,IAAI,iB,GAC9D/E,mBAAA,CAAsE,c,4BAAjEA,mBAAA,CAAsC;MAAhCN,KAAK,EAAC;IAAa,GAAC,OAAK,sBA/b9C4B,gBAAA,CA+bqD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAmI,WAAW,CAACpE,EAAE,iB,OAIjElF,mBAAA,CAGM,OAHNgM,WAGM,G,8BAFJhM,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAAkG;MAA3FwB,IAAI,EAAC,MAAM;MArc1B,uBAAAnB,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAqcoCF,KAAA,CAAAyK,eAAe,CAACtG,QAAQ,GAAAjE,MAAA;MAAE3B,KAAK,EAAC,cAAc;MAACgC,WAAW,EAAC;mDAA3DP,KAAA,CAAAyK,eAAe,CAACtG,QAAQ,E,KAGtDtF,mBAAA,CAOM,OAPNiM,WAOM,G,8BANJjM,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAIM,OAJNkM,WAIM,G,gBAHJlM,mBAAA,CACoI;MAD7HwB,IAAI,EAAC,UAAU;MA3chC,uBAAAnB,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA2c0CF,KAAA,CAAAyK,eAAe,CAACrG,SAAS,GAAAlE,MAAA;MACvD3B,KAAK,EAAC;uDADwByB,KAAA,CAAAyK,eAAe,CAACrG,SAAS,E,iCAEzDvF,mBAAA,CAAsG;MAA/FN,KAAK,EAAC;IAAgF,4B,KAIjGM,mBAAA,CAOM,OAPNmM,WAOM,G,8BANJnM,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MAvdjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAmdyBF,KAAA,CAAAyK,eAAe,CAAChC,QAAQ,GAAAvI,MAAA;MAAE3B,KAAK,EAAC,aAAa;MAAEmK,QAAM,EAAAxJ,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEd,QAAA,CAAA6L,6BAA6B;2BACnGtM,mBAAA,CAES2D,SAAA,QAtdnBC,WAAA,CAodmC5C,IAAA,CAAAiJ,QAAQ,EAAlBC,MAAM;2BAArBlK,mBAAA,CAES;QAF2BH,GAAG,EAAEqK,MAAM,CAACtF,EAAE;QAAG1C,KAAK,EAAEgI,MAAM,CAACtF;0BAC9DsF,MAAM,CAACjF,IAAI,IAAG,UAAQ,GAAAzC,gBAAA,CAAG0H,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA3H,gBAAA,CAAG0H,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAtdVmC,YAAA;6FAmdyBlL,KAAA,CAAAyK,eAAe,CAAChC,QAAQ,E,KAO3C5J,mBAAA,CAiBM,OAjBNsM,YAiBM,GAhBJtM,mBAAA,CAOM,OAPNuM,YAOM,G,8BANJvM,mBAAA,CAAuC;MAAhCN,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BM,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEd,QAAA,CAAA6L,6BAA6B;MAAI5K,IAAI,EAAC,QAAQ;MAC5D9B,KAAK,EAAC;QACNe,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;sCA/djE4B,gBAAA,CA+d0E,QAEhE,G,KAEFtB,mBAAA,CAOM,OAPNwM,YAOM,G,gBANJxM,mBAAA,CAC2C;MADnCwB,IAAI,EAAEL,KAAA,CAAAkF,kBAAkB,CAACoG,UAAU;MAperD,uBAAApM,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAoesFF,KAAA,CAAAyK,eAAe,CAACtF,QAAQ,GAAAjF,MAAA;MAAEkF,QAAQ,EAAR,EAAQ;MAC5G7G,KAAK,EAAC;4BArelBgN,YAAA,I,iBAoesFvL,KAAA,CAAAyK,eAAe,CAACtF,QAAQ,E,GAEpGtG,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEF,KAAA,CAAAkF,kBAAkB,CAACoG,UAAU,IAAItL,KAAA,CAAAkF,kBAAkB,CAACoG,UAAU;MAAEjL,IAAI,EAAC,QAAQ;MAC3F9B,KAAK,EAAC;QACNe,YAAA,CAA0GC,4BAAA;MAAtFC,IAAI,UAAUQ,KAAA,CAAAkF,kBAAkB,CAACoG,UAAU;MAAyB/M,KAAK,EAAC;;IAxe1G6D,CAAA;6DA8eIxD,mBAAA,cAAiB,EACjBU,YAAA,CAiEYmI,oBAAA;IAhjBhB1F,UAAA,EA+ewB/B,KAAA,CAAAwL,gBAAgB,CAAC7D,IAAI;IA/e7C,uBAAAzI,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA+ewBF,KAAA,CAAAwL,gBAAgB,CAAC7D,IAAI,GAAAzH,MAAA;IAAE0H,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAAC6D,IAAI,EAAC,IAAI;IACpF5D,SAAO,EAAEzI,QAAA,CAAAsM,oBAAoB;IAAG3D,OAAO,EAAE/H,KAAA,CAAAgI;;IAhfhD9F,OAAA,EAAAC,QAAA,CAifM,MAaM,CAbNtD,mBAAA,CAaM,OAbN8M,YAaM,G,8BAZJ9M,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAIM,OAJN+M,YAIM,GAHJtM,YAAA,CAEiBwC,yBAAA;MAtf3BC,UAAA,EAofmC/B,KAAA,CAAA6L,cAAc;MApfjD,wB,sCAofmC7L,KAAA,CAAA6L,cAAc,GAAA3L,MAAA,GAAsBd,QAAA,CAAA0M,oBAAoB;;MApf3F5J,OAAA,EAAAC,QAAA,CAof6F,MAEnFjD,MAAA,UAAAA,MAAA,SAtfViB,gBAAA,CAof6F,MAEnF,E;MAtfViC,CAAA;gEAwfQvD,mBAAA,CAIM,OAJNkN,YAIM,I,kBAHJpN,mBAAA,CAEiB2D,SAAA,QA3f3BC,WAAA,CAyfyC5C,IAAA,CAAAqM,KAAK,EAAb9I,IAAI;2BAA3B+I,YAAA,CAEiBnK,yBAAA;QAFsBtD,GAAG,EAAE0E,IAAI,CAACK,EAAE;QAzf7DxB,UAAA,EAyfwE/B,KAAA,CAAAwL,gBAAgB,CAACU,aAAa,CAAChJ,IAAI,CAACK,EAAE;QAzf9G,uBAAArD,MAAA,IAyfwEF,KAAA,CAAAwL,gBAAgB,CAACU,aAAa,CAAChJ,IAAI,CAACK,EAAE,IAAArD;;QAzf9GgC,OAAA,EAAAC,QAAA,CA0fY,MAAe,CA1f3BhC,gBAAA,CAAAgB,gBAAA,CA0fe+B,IAAI,CAACU,IAAI,IAAG,IAAE,GAAAzC,gBAAA,CAAG+B,IAAI,CAACa,EAAE,IAAG,IAChC,gB;QA3fV3B,CAAA;;sCA6fQvD,mBAAA,CAAyD,KAAzDsN,YAAyD,EAApC,MAAI,GAAAhL,gBAAA,CAAG/B,QAAA,CAAAgN,kBAAkB,IAAG,MAAI,gB,GAGvDvN,mBAAA,CAOM,OAPNwN,YAOM,G,8BANJxN,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MAtgBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAkgByBF,KAAA,CAAAwL,gBAAgB,CAAC/C,QAAQ,GAAAvI,MAAA;MAAE3B,KAAK,EAAC;2BAChDI,mBAAA,CAES2D,SAAA,QArgBnBC,WAAA,CAmgBmC5C,IAAA,CAAAiJ,QAAQ,EAAlBC,MAAM;2BAArBlK,mBAAA,CAES;QAF2BH,GAAG,EAAEqK,MAAM,CAACtF,EAAE;QAAG1C,KAAK,EAAEgI,MAAM,CAACtF;0BAC9DsF,MAAM,CAACjF,IAAI,IAAG,UAAQ,GAAAzC,gBAAA,CAAG0H,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA3H,gBAAA,CAAG0H,MAAM,CAACE,UAAU,IAAG,KAC9E,uBArgBVuD,YAAA;6EAkgByBtM,KAAA,CAAAwL,gBAAgB,CAAC/C,QAAQ,E,KAO5C5J,mBAAA,CAyBM,OAzBN0N,YAyBM,G,8BAxBJ1N,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CASM,OATN2N,YASM,GARJ3N,mBAAA,CAGQ,SAHR4N,YAGQ,G,gBAFN5N,mBAAA,CAA4F;MAArFwB,IAAI,EAAC,OAAO;MA7gB/B,uBAAAnB,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA6gByCF,KAAA,CAAAwL,gBAAgB,CAACkB,aAAa,GAAAxM,MAAA;MAAEW,KAAK,EAAC,WAAW;MAACtC,KAAK,EAAC;oDAAxDyB,KAAA,CAAAwL,gBAAgB,CAACkB,aAAa,E,iCAC3D7N,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHR8N,YAGQ,G,gBAFN9N,mBAAA,CAA4F;MAArFwB,IAAI,EAAC,OAAO;MAjhB/B,uBAAAnB,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAihByCF,KAAA,CAAAwL,gBAAgB,CAACkB,aAAa,GAAAxM,MAAA;MAAEW,KAAK,EAAC,WAAW;MAACtC,KAAK,EAAC;oDAAxDyB,KAAA,CAAAwL,gBAAgB,CAACkB,aAAa,E,iCAC3D7N,mBAAA,CAAiB,cAAX,MAAI,qB,KAIHmB,KAAA,CAAAwL,gBAAgB,CAACkB,aAAa,oB,cAAzC/N,mBAAA,CAWM,OAXNiO,YAWM,GAVJ/N,mBAAA,CASM,OATNgO,YASM,GARJhO,mBAAA,CAGM,c,8BAFJA,mBAAA,CAAoC;MAA7BN,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BM,mBAAA,CAAiF;MAA1EwB,IAAI,EAAC,MAAM;MA1hBhC,uBAAAnB,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA0hB0CF,KAAA,CAAAwL,gBAAgB,CAACsB,aAAa,GAAA5M,MAAA;MAAE3B,KAAK,EAAC;mDAAtCyB,KAAA,CAAAwL,gBAAgB,CAACsB,aAAa,E,KAE5DjO,mBAAA,CAGM,c,8BAFJA,mBAAA,CAAoC;MAA7BN,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BM,mBAAA,CAAiF;MAA1EwB,IAAI,EAAC,MAAM;MA9hBhC,uBAAAnB,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA8hB0CF,KAAA,CAAAwL,gBAAgB,CAACuB,aAAa,GAAA7M,MAAA;MAAE3B,KAAK,EAAC;mDAAtCyB,KAAA,CAAAwL,gBAAgB,CAACuB,aAAa,E,WA9hBxEnO,mBAAA,e,GAoiBMC,mBAAA,CAWM,OAXNmO,YAWM,G,8BAVJnO,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Be,YAAA,CAEiBwC,yBAAA;MAxiBzBC,UAAA,EAsiBiC/B,KAAA,CAAAwL,gBAAgB,CAACyB,YAAY;MAtiB9D,uBAAA/N,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAsiBiCF,KAAA,CAAAwL,gBAAgB,CAACyB,YAAY,GAAA/M,MAAA;;MAtiB9DgC,OAAA,EAAAC,QAAA,CAsiBgE,MAExDjD,MAAA,UAAAA,MAAA,SAxiBRiB,gBAAA,CAsiBgE,YAExD,E;MAxiBRiC,CAAA;uCAyiBQ9C,YAAA,CAEiBwC,yBAAA;MA3iBzBC,UAAA,EAyiBiC/B,KAAA,CAAAwL,gBAAgB,CAAC0B,WAAW;MAziB7D,uBAAAhO,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAyiBiCF,KAAA,CAAAwL,gBAAgB,CAAC0B,WAAW,GAAAhN,MAAA;;MAziB7DgC,OAAA,EAAAC,QAAA,CAyiB+D,MAEvDjD,MAAA,UAAAA,MAAA,SA3iBRiB,gBAAA,CAyiB+D,UAEvD,E;MA3iBRiC,CAAA;uCA4iBQ9C,YAAA,CAEiBwC,yBAAA;MA9iBzBC,UAAA,EA4iBiC/B,KAAA,CAAAwL,gBAAgB,CAAC2B,gBAAgB;MA5iBlE,uBAAAjO,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA4iBiCF,KAAA,CAAAwL,gBAAgB,CAAC2B,gBAAgB,GAAAjN,MAAA;;MA5iBlEgC,OAAA,EAAAC,QAAA,CA4iBoE,MAE5DjD,MAAA,UAAAA,MAAA,SA9iBRiB,gBAAA,CA4iBoE,aAE5D,E;MA9iBRiC,CAAA;;IAAAA,CAAA;6DAkjBIxD,mBAAA,cAAiB,EACjBU,YAAA,CA8BYmI,oBAAA;IAjlBhB1F,UAAA,EAmjBwB/B,KAAA,CAAAoN,eAAe,CAACzF,IAAI;IAnjB5C,uBAAAzI,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAmjBwBF,KAAA,CAAAoN,eAAe,CAACzF,IAAI,GAAAzH,MAAA;IAAE0H,KAAK,EAAC,UAAU;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAEzI,QAAA,CAAAiO,gBAAgB;IACtGtF,OAAO,EAAE/H,KAAA,CAAAgI;;IApjBhB9F,OAAA,EAAAC,QAAA,CAqjBM,MAQM,CARNtD,mBAAA,CAQM,OARNyO,YAQM,G,8BAPJzO,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAKM,OALN0O,YAKM,I,kBAJJ5O,mBAAA,CAGiB2D,SAAA,QA3jB3BC,WAAA,CAwjByCnD,QAAA,CAAAoO,iBAAiB,EAAzBtK,IAAI;2BAA3B+I,YAAA,CAGiBnK,yBAAA;QAHkCtD,GAAG,EAAE0E,IAAI,CAACK,EAAE;QAxjBzExB,UAAA,EAyjBqB/B,KAAA,CAAAoN,eAAe,CAAClB,aAAa,CAAChJ,IAAI,CAACK,EAAE;QAzjB1D,uBAAArD,MAAA,IAyjBqBF,KAAA,CAAAoN,eAAe,CAAClB,aAAa,CAAChJ,IAAI,CAACK,EAAE,IAAArD;;QAzjB1DgC,OAAA,EAAAC,QAAA,CA0jBY,MAAe,CA1jB3BhC,gBAAA,CAAAgB,gBAAA,CA0jBe+B,IAAI,CAACU,IAAI,IAAG,IAAE,GAAAzC,gBAAA,CAAG+B,IAAI,CAACa,EAAE,IAAG,IAChC,gB;QA3jBV3B,CAAA;;wCA+jBMvD,mBAAA,CAOM,OAPN4O,YAOM,G,8BANJ5O,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCM,mBAAA,CAIS;MArkBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAikByBF,KAAA,CAAAoN,eAAe,CAAC3E,QAAQ,GAAAvI,MAAA;MAAE3B,KAAK,EAAC;2BAC/CI,mBAAA,CAES2D,SAAA,QApkBnBC,WAAA,CAkkBmC5C,IAAA,CAAAiJ,QAAQ,EAAlBC,MAAM;2BAArBlK,mBAAA,CAES;QAF2BH,GAAG,EAAEqK,MAAM,CAACtF,EAAE;QAAG1C,KAAK,EAAEgI,MAAM,CAACtF;0BAC9DsF,MAAM,CAACjF,IAAI,wBAnkB1B8J,YAAA;6EAikByB1N,KAAA,CAAAoN,eAAe,CAAC3E,QAAQ,E,KAO3C5J,mBAAA,CAQM,OARN8O,YAQM,G,8BAPJ9O,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Be,YAAA,CAEiBwC,yBAAA;MA5kBzBC,UAAA,EA0kBiC/B,KAAA,CAAAoN,eAAe,CAACQ,iBAAiB;MA1kBlE,uBAAA1O,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA0kBiCF,KAAA,CAAAoN,eAAe,CAACQ,iBAAiB,GAAA1N,MAAA;;MA1kBlEgC,OAAA,EAAAC,QAAA,CA0kBoE,MAE5DjD,MAAA,UAAAA,MAAA,SA5kBRiB,gBAAA,CA0kBoE,eAE5D,E;MA5kBRiC,CAAA;uCA6kBQ9C,YAAA,CAEiBwC,yBAAA;MA/kBzBC,UAAA,EA6kBiC/B,KAAA,CAAAoN,eAAe,CAACS,iBAAiB;MA7kBlE,uBAAA3O,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA6kBiCF,KAAA,CAAAoN,eAAe,CAACS,iBAAiB,GAAA3N,MAAA;;MA7kBlEgC,OAAA,EAAAC,QAAA,CA6kBoE,MAE5DjD,MAAA,UAAAA,MAAA,SA/kBRiB,gBAAA,CA6kBoE,aAE5D,E;MA/kBRiC,CAAA;;IAAAA,CAAA;6DAmlBIxD,mBAAA,cAAiB,EACjBU,YAAA,CAyCYmI,oBAAA;IA7nBhB1F,UAAA,EAolBwB/B,KAAA,CAAA8N,mBAAmB,CAACnG,IAAI;IAplBhD,uBAAAzI,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAolBwBF,KAAA,CAAA8N,mBAAmB,CAACnG,IAAI,GAAAzH,MAAA;IAAE0H,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAACpI,IAAI,EAAC,sBAAsB;IAACuO,MAAM,EAAN,EAAM;IAChHlG,SAAO,EAAEzI,QAAA,CAAA4O,cAAc;IAAGjG,OAAO,EAAE/H,KAAA,CAAAgI;;IArlB1C9F,OAAA,EAAAC,QAAA,CAslBM,MAEM,C,8BAFNtD,mBAAA,CAEM;MAFDN,KAAK,EAAC;IAA4C,IACrDM,mBAAA,CAA+C,WAA5C,0CAAwC,E,sBAG7CA,mBAAA,CAQM,OARNoP,YAQM,G,8BAPJpP,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAKM,OALNqP,YAKM,I,kBAJJvP,mBAAA,CAGiB2D,SAAA,QAhmB3BC,WAAA,CA6lByCnD,QAAA,CAAAoO,iBAAiB,EAAzBtK,IAAI;2BAA3B+I,YAAA,CAGiBnK,yBAAA;QAHkCtD,GAAG,EAAE0E,IAAI,CAACK,EAAE;QA7lBzExB,UAAA,EA8lBqB/B,KAAA,CAAA8N,mBAAmB,CAAC5B,aAAa,CAAChJ,IAAI,CAACK,EAAE;QA9lB9D,uBAAArD,MAAA,IA8lBqBF,KAAA,CAAA8N,mBAAmB,CAAC5B,aAAa,CAAChJ,IAAI,CAACK,EAAE,IAAArD;;QA9lB9DgC,OAAA,EAAAC,QAAA,CA+lBY,MAAe,CA/lB3BhC,gBAAA,CAAAgB,gBAAA,CA+lBe+B,IAAI,CAACU,IAAI,IAAG,IAAE,GAAAzC,gBAAA,CAAG+B,IAAI,CAACa,EAAE,IAAG,IAChC,gB;QAhmBV3B,CAAA;;wCAomBMvD,mBAAA,CAOM,OAPNsP,YAOM,G,8BANJtP,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCM,mBAAA,CAIS;MA1mBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAsmByBF,KAAA,CAAA8N,mBAAmB,CAACrF,QAAQ,GAAAvI,MAAA;MAAE3B,KAAK,EAAC;2BACnDI,mBAAA,CAES2D,SAAA,QAzmBnBC,WAAA,CAumBmCnD,QAAA,CAAAgP,iBAAiB,EAA3BvF,MAAM;2BAArBlK,mBAAA,CAES;QAFoCH,GAAG,EAAEqK,MAAM,CAACtF,EAAE;QAAG1C,KAAK,EAAEgI,MAAM,CAACtF;0BACvEsF,MAAM,CAACjF,IAAI,IAAG,UAAQ,GAAAzC,gBAAA,CAAG0H,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA3H,gBAAA,CAAG0H,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAzmBVsF,YAAA;6EAsmByBrO,KAAA,CAAA8N,mBAAmB,CAACrF,QAAQ,E,KAO/C5J,mBAAA,CASM,OATNyP,YASM,G,8BARJzP,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAMS;MArnBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA+mByBF,KAAA,CAAA8N,mBAAmB,CAACS,MAAM,GAAArO,MAAA;MAAE3B,KAAK,EAAC;sCACjDM,mBAAA,CAAiD;MAAzCgC,KAAK,EAAC;IAAmB,GAAC,QAAM,qBACxChC,mBAAA,CAA2C;MAAnCgC,KAAK,EAAC;IAAe,GAAC,MAAI,qBAClChC,mBAAA,CAA6C;MAArCgC,KAAK,EAAC;IAAiB,GAAC,MAAI,qBACpChC,mBAAA,CAAwC;MAAhCgC,KAAK,EAAC;IAAY,GAAC,MAAI,qBAC/BhC,mBAAA,CAAmC;MAA3BgC,KAAK,EAAC;IAAO,GAAC,MAAI,oB,2CALXb,KAAA,CAAA8N,mBAAmB,CAACS,MAAM,E,KAS7C1P,mBAAA,CAIM,OAJN2P,YAIM,G,8BAHJ3P,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CACuC;MA3nB/C,uBAAAK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA0nB2BF,KAAA,CAAA8N,mBAAmB,CAACW,WAAW,GAAAvO,MAAA;MAAE3B,KAAK,EAAC,cAAc;MAACmQ,IAAI,EAAC,GAAG;MAC/EnO,WAAW,EAAC;mDADKP,KAAA,CAAA8N,mBAAmB,CAACW,WAAW,E;IA1nB1DrM,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}