{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, vModelText as _vModelText, withDirectives as _withDirectives, vModelSelect as _vModelSelect, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, vModelRadio as _vModelRadio, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"bg-white shadow rounded-lg p-4 mb-6\"\n};\nconst _hoisted_2 = {\n  class: \"flex flex-wrap items-center justify-between\"\n};\nconst _hoisted_3 = {\n  class: \"flex space-x-3 mb-2 sm:mb-0\"\n};\nconst _hoisted_4 = {\n  class: \"flex items-center space-x-4\"\n};\nconst _hoisted_5 = {\n  class: \"flex items-center border rounded-md overflow-hidden\"\n};\nconst _hoisted_6 = {\n  class: \"relative\"\n};\nconst _hoisted_7 = {\n  class: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n};\nconst _hoisted_8 = {\n  class: \"bg-white rounded-lg shadow overflow-hidden\"\n};\nconst _hoisted_9 = {\n  class: \"min-w-full divide-y divide-gray-200\"\n};\nconst _hoisted_10 = {\n  class: \"bg-gray-50\"\n};\nconst _hoisted_11 = {\n  scope: \"col\",\n  class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n};\nconst _hoisted_12 = {\n  class: \"bg-white divide-y divide-gray-200\"\n};\nconst _hoisted_13 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_14 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_15 = {\n  class: \"ml-2 font-medium text-gray-900\"\n};\nconst _hoisted_16 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_17 = {\n  class: \"text-sm text-gray-900\"\n};\nconst _hoisted_18 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_19 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_20 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_21 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_22 = {\n  key: 1,\n  class: \"ml-1\"\n};\nconst _hoisted_23 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_24 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_25 = {\n  class: \"flex-grow\"\n};\nconst _hoisted_26 = [\"type\", \"value\"];\nconst _hoisted_27 = [\"onClick\"];\nconst _hoisted_28 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_29 = {\n  class: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\"\n};\nconst _hoisted_30 = [\"onClick\"];\nconst _hoisted_31 = {\n  class: \"form-group\"\n};\nconst _hoisted_32 = {\n  class: \"form-label\"\n};\nconst _hoisted_33 = {\n  class: \"form-group\"\n};\nconst _hoisted_34 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_35 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_36 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_37 = {\n  key: 0\n};\nconst _hoisted_38 = {\n  class: \"form-group\"\n};\nconst _hoisted_39 = [\"value\"];\nconst _hoisted_40 = {\n  class: \"form-group\"\n};\nconst _hoisted_41 = {\n  class: \"flex\"\n};\nconst _hoisted_42 = {\n  key: 1\n};\nconst _hoisted_43 = {\n  class: \"form-group\"\n};\nconst _hoisted_44 = {\n  class: \"form-group\"\n};\nconst _hoisted_45 = {\n  key: 0,\n  class: \"text-red-500 text-xs mt-1\"\n};\nconst _hoisted_46 = {\n  class: \"form-group\"\n};\nconst _hoisted_47 = {\n  class: \"form-group\"\n};\nconst _hoisted_48 = {\n  class: \"mb-2\"\n};\nconst _hoisted_49 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_50 = {\n  class: \"form-text\"\n};\nconst _hoisted_51 = {\n  class: \"form-group\"\n};\nconst _hoisted_52 = [\"value\"];\nconst _hoisted_53 = {\n  class: \"form-group\"\n};\nconst _hoisted_54 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_55 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_56 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_57 = {\n  key: 0,\n  class: \"mt-3\"\n};\nconst _hoisted_58 = {\n  class: \"grid grid-cols-2 gap-4\"\n};\nconst _hoisted_59 = {\n  class: \"form-group\"\n};\nconst _hoisted_60 = {\n  class: \"form-group\"\n};\nconst _hoisted_61 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_62 = {\n  class: \"form-group\"\n};\nconst _hoisted_63 = [\"value\"];\nconst _hoisted_64 = {\n  class: \"form-group\"\n};\nconst _hoisted_65 = {\n  class: \"form-group\"\n};\nconst _hoisted_66 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_67 = {\n  class: \"form-group\"\n};\nconst _hoisted_68 = [\"value\"];\nconst _hoisted_69 = {\n  class: \"form-group\"\n};\nconst _hoisted_70 = {\n  class: \"form-group\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_CustomCheckbox = _resolveComponent(\"CustomCheckbox\");\n  const _component_StatusBadge = _resolveComponent(\"StatusBadge\");\n  const _component_PasswordStrengthMeter = _resolveComponent(\"PasswordStrengthMeter\");\n  const _component_BaseModal = _resolveComponent(\"BaseModal\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createCommentVNode(\" 操作按钮 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.showEmergencyReset && $options.showEmergencyReset(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'exclamation-triangle'],\n    class: \"mr-2\"\n  }), _cache[37] || (_cache[37] = _createElementVNode(\"span\", null, \"紧急重置\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.openBatchUpdateModal && $options.openBatchUpdateModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'key'],\n    class: \"mr-2\"\n  }), _cache[38] || (_cache[38] = _createElementVNode(\"span\", null, \"批量更新密码\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.openBatchApplyModal && $options.openBatchApplyModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"mr-2\"\n  }), _cache[39] || (_cache[39] = _createElementVNode(\"span\", null, \"批量应用策略\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" 视图切换 \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1 focus:outline-none\", {\n      'bg-blue-500 text-white': $data.viewMode === 'table',\n      'bg-gray-100 text-gray-600': $data.viewMode !== 'table'\n    }]),\n    onClick: _cache[3] || (_cache[3] = $event => $data.viewMode = 'table')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'table'],\n    class: \"mr-1\"\n  }), _cache[40] || (_cache[40] = _createTextVNode(\" 表格 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1 focus:outline-none\", {\n      'bg-blue-500 text-white': $data.viewMode === 'card',\n      'bg-gray-100 text-gray-600': $data.viewMode !== 'card'\n    }]),\n    onClick: _cache[4] || (_cache[4] = $event => $data.viewMode = 'card')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'th-large'],\n    class: \"mr-1\"\n  }), _cache[41] || (_cache[41] = _createTextVNode(\" 卡片 \"))], 2 /* CLASS */)]), _createCommentVNode(\" 筛选 \"), _createElementVNode(\"div\", _hoisted_6, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.filterText = $event),\n    placeholder: \"筛选主机...\",\n    class: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.filterText]]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"text-gray-400\"\n  })])]), _createCommentVNode(\" 状态筛选 \"), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.statusFilter = $event),\n    class: \"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n  }, _cache[42] || (_cache[42] = [_createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有状态\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"normal\"\n  }, \"正常\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"warning\"\n  }, \"警告\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"error\"\n  }, \"错误\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.statusFilter]])])])]), _createCommentVNode(\" 主机列表 \"), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"table\", _hoisted_9, [_createElementVNode(\"thead\", _hoisted_10, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", _hoisted_11, [_createVNode(_component_CustomCheckbox, {\n    modelValue: $data.selectAll,\n    \"onUpdate:modelValue\": [_cache[7] || (_cache[7] = $event => $data.selectAll = $event), $options.toggleSelectAll]\n  }, {\n    default: _withCtx(() => _cache[43] || (_cache[43] = [_createTextVNode(\" 主机名 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _cache[44] || (_cache[44] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" IP地址 \", -1 /* HOISTED */)), _cache[45] || (_cache[45] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 最后密码修改时间 \", -1 /* HOISTED */)), _cache[46] || (_cache[46] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码过期时间 \", -1 /* HOISTED */)), _cache[47] || (_cache[47] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码 \", -1 /* HOISTED */)), _cache[48] || (_cache[48] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 状态 \", -1 /* HOISTED */)), _cache[49] || (_cache[49] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 操作 \", -1 /* HOISTED */))])]), _createElementVNode(\"tbody\", _hoisted_12, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, (host, index) => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: host.id,\n      class: _normalizeClass({\n        'bg-gray-50': index % 2 === 0,\n        'hover:bg-blue-50': true\n      })\n    }, [_createElementVNode(\"td\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_CustomCheckbox, {\n      modelValue: host.selected,\n      \"onUpdate:modelValue\": $event => host.selected = $event\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"span\", _hoisted_15, _toDisplayString(host.name), 1 /* TEXT */)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])])]), _createElementVNode(\"td\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, _toDisplayString(host.ip), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, _toDisplayString(host.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_20, [_createElementVNode(\"div\", {\n      class: _normalizeClass({\n        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\n        'bg-red-100 text-red-800': $options.isPasswordExpired(host).status === 'danger' || $options.isPasswordExpired(host).status === 'expired',\n        'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(host).status === 'warning',\n        'bg-gray-100 text-gray-800': $options.isPasswordExpired(host).status === 'normal'\n      })\n    }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(host).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(host).status === 'expired' || $options.isPasswordExpired(host).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_21, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'exclamation-triangle']\n    })])) : $options.isPasswordExpired(host).status === 'warning' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_22, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'exclamation-circle']\n    })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]), _createElementVNode(\"td\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"input\", {\n      type: $data.passwordVisibility[host.id] ? 'text' : 'password',\n      value: host.password,\n      readonly: \"\",\n      class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n    }, null, 8 /* PROPS */, _hoisted_26)]), _createElementVNode(\"button\", {\n      onClick: $event => $options.togglePasswordVisibility(host.id),\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility[host.id] ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_27)])]), _createElementVNode(\"td\", _hoisted_28, [_createVNode(_component_StatusBadge, {\n      type: host.status\n    }, null, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"td\", _hoisted_29, [_createElementVNode(\"button\", {\n      class: \"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n      onClick: $event => $options.openChangePasswordModal(host)\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'key'],\n      class: \"mr-1\"\n    }), _cache[50] || (_cache[50] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_30)])], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */))])])]), _createCommentVNode(\" 修改密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.changePasswordModal.show,\n    \"onUpdate:modelValue\": _cache[18] || (_cache[18] = $event => $data.changePasswordModal.show = $event),\n    title: \"修改密码\",\n    \"confirm-text\": \"确认更新\",\n    onConfirm: $options.updatePassword,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"label\", _hoisted_32, \"服务器: \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_33, [_cache[53] || (_cache[53] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"label\", _hoisted_35, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.changePasswordModal.method = $event),\n      value: \"auto\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.changePasswordModal.method]]), _cache[51] || (_cache[51] = _createElementVNode(\"span\", null, \"自动生成\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_36, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.changePasswordModal.method = $event),\n      value: \"manual\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.changePasswordModal.method]]), _cache[52] || (_cache[52] = _createElementVNode(\"span\", null, \"手动输入\", -1 /* HOISTED */))])])]), $data.changePasswordModal.method === 'auto' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_cache[54] || (_cache[54] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.changePasswordModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_39);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.changePasswordModal.policyId]])]), _createElementVNode(\"div\", _hoisted_40, [_cache[55] || (_cache[55] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_41, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $data.changePasswordModal.generatedPassword = $event),\n      class: \"form-control rounded-r-none\",\n      readonly: \"\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.changePasswordModal.generatedPassword]]), _createElementVNode(\"button\", {\n      class: \"bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded-r-md\",\n      onClick: _cache[12] || (_cache[12] = (...args) => $options.generatePassword && $options.generatePassword(...args))\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt']\n    })])]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.generatedPassword\n    }, null, 8 /* PROPS */, [\"password\"])])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_42, [_createElementVNode(\"div\", _hoisted_43, [_cache[56] || (_cache[56] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"新密码\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"password\",\n      \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.changePasswordModal.newPassword = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.changePasswordModal.newPassword]]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.newPassword\n    }, null, 8 /* PROPS */, [\"password\"])]), _createElementVNode(\"div\", _hoisted_44, [_cache[57] || (_cache[57] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"确认密码\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"password\",\n      \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.changePasswordModal.confirmPassword = $event),\n      class: _normalizeClass([\"form-control\", {\n        'border-red-500': $options.passwordMismatch\n      }])\n    }, null, 2 /* CLASS */), [[_vModelText, $data.changePasswordModal.confirmPassword]]), $options.passwordMismatch ? (_openBlock(), _createElementBlock(\"div\", _hoisted_45, \" 两次输入的密码不一致 \")) : _createCommentVNode(\"v-if\", true)])])), _createElementVNode(\"div\", _hoisted_46, [_cache[61] || (_cache[61] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.executeImmediately,\n      \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => $data.changePasswordModal.executeImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[58] || (_cache[58] = [_createTextVNode(\" 立即执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.saveHistory,\n      \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.changePasswordModal.saveHistory = $event)\n    }, {\n      default: _withCtx(() => _cache[59] || (_cache[59] = [_createTextVNode(\" 保存密码历史记录 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.logAudit,\n      \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.changePasswordModal.logAudit = $event)\n    }, {\n      default: _withCtx(() => _cache[60] || (_cache[60] = [_createTextVNode(\" 记录审计日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量更新密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchUpdateModal.show,\n    \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $data.batchUpdateModal.show = $event),\n    title: \"批量更新密码\",\n    \"confirm-text\": \"开始更新\",\n    size: \"lg\",\n    onConfirm: $options.batchUpdatePasswords,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_47, [_cache[63] || (_cache[63] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_48, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatch,\n      \"onUpdate:modelValue\": [_cache[19] || (_cache[19] = $event => $data.selectAllBatch = $event), $options.toggleSelectAllBatch]\n    }, {\n      default: _withCtx(() => _cache[62] || (_cache[62] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_49, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchUpdateModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchUpdateModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_50, \"已选择 \" + _toDisplayString($options.selectedHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_51, [_cache[64] || (_cache[64] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $data.batchUpdateModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_52);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchUpdateModal.policyId]])]), _createElementVNode(\"div\", _hoisted_53, [_cache[69] || (_cache[69] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_54, [_createElementVNode(\"label\", _hoisted_55, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[21] || (_cache[21] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"immediate\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[65] || (_cache[65] = _createElementVNode(\"span\", null, \"立即执行\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_56, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"scheduled\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[66] || (_cache[66] = _createElementVNode(\"span\", null, \"定时执行\", -1 /* HOISTED */))])]), $data.batchUpdateModal.executionTime === 'scheduled' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_57, [_createElementVNode(\"div\", _hoisted_58, [_createElementVNode(\"div\", null, [_cache[67] || (_cache[67] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"日期\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"date\",\n      \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.batchUpdateModal.scheduledDate = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledDate]])]), _createElementVNode(\"div\", null, [_cache[68] || (_cache[68] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"时间\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"time\",\n      \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.batchUpdateModal.scheduledTime = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledTime]])])])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_59, [_cache[73] || (_cache[73] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.batchUpdateModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[70] || (_cache[70] = [_createTextVNode(\" 忽略错误继续执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.detailedLog,\n      \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.batchUpdateModal.detailedLog = $event)\n    }, {\n      default: _withCtx(() => _cache[71] || (_cache[71] = [_createTextVNode(\" 记录详细日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.sendNotification,\n      \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.batchUpdateModal.sendNotification = $event)\n    }, {\n      default: _withCtx(() => _cache[72] || (_cache[72] = [_createTextVNode(\" 执行完成后发送通知 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量应用策略弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchApplyModal.show,\n    \"onUpdate:modelValue\": _cache[32] || (_cache[32] = $event => $data.batchApplyModal.show = $event),\n    title: \"批量应用密码策略\",\n    \"confirm-text\": \"应用策略\",\n    onConfirm: $options.batchApplyPolicy,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_60, [_cache[74] || (_cache[74] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_61, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchApplyModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchApplyModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_62, [_cache[75] || (_cache[75] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[29] || (_cache[29] = $event => $data.batchApplyModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name), 9 /* TEXT, PROPS */, _hoisted_63);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchApplyModal.policyId]])]), _createElementVNode(\"div\", _hoisted_64, [_cache[78] || (_cache[78] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.updateImmediately,\n      \"onUpdate:modelValue\": _cache[30] || (_cache[30] = $event => $data.batchApplyModal.updateImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[76] || (_cache[76] = [_createTextVNode(\" 立即更新密码以符合策略 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.applyOnNextUpdate,\n      \"onUpdate:modelValue\": _cache[31] || (_cache[31] = $event => $data.batchApplyModal.applyOnNextUpdate = $event)\n    }, {\n      default: _withCtx(() => _cache[77] || (_cache[77] = [_createTextVNode(\" 下次密码更新时应用 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 紧急重置密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.emergencyResetModal.show,\n    \"onUpdate:modelValue\": _cache[36] || (_cache[36] = $event => $data.emergencyResetModal.show = $event),\n    title: \"紧急密码重置\",\n    \"confirm-text\": \"立即重置\",\n    icon: \"exclamation-triangle\",\n    danger: \"\",\n    onConfirm: $options.emergencyReset,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_cache[84] || (_cache[84] = _createElementVNode(\"div\", {\n      class: \"bg-red-50 text-red-700 p-3 rounded-md mb-4\"\n    }, [_createElementVNode(\"p\", null, \"紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_65, [_cache[79] || (_cache[79] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_66, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.emergencyResetModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.emergencyResetModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_67, [_cache[80] || (_cache[80] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用紧急策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[33] || (_cache[33] = $event => $data.emergencyResetModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.emergencyPolicies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_68);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.policyId]])]), _createElementVNode(\"div\", _hoisted_69, [_cache[82] || (_cache[82] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"操作原因\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[34] || (_cache[34] = $event => $data.emergencyResetModal.reason = $event),\n      class: \"form-select\"\n    }, _cache[81] || (_cache[81] = [_createElementVNode(\"option\", {\n      value: \"security_incident\"\n    }, \"安全事件响应\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"password_leak\"\n    }, \"密码泄露\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"abnormal_access\"\n    }, \"异常访问\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"compliance\"\n    }, \"合规要求\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"other\"\n    }, \"其他原因\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.reason]])]), _createElementVNode(\"div\", _hoisted_70, [_cache[83] || (_cache[83] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"附加说明\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n      \"onUpdate:modelValue\": _cache[35] || (_cache[35] = $event => $data.emergencyResetModal.description = $event),\n      class: \"form-control\",\n      rows: \"2\",\n      placeholder: \"请输入重置原因详细说明\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.emergencyResetModal.description]])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"])]);\n}", "map": {"version": 3, "names": ["class", "scope", "key", "_createElementBlock", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "args", "$options", "showEmergencyReset", "_createVNode", "_component_font_awesome_icon", "icon", "openBatchUpdateModal", "openBatchApplyModal", "_hoisted_4", "_hoisted_5", "_normalizeClass", "$data", "viewMode", "$event", "_createTextVNode", "_hoisted_6", "type", "filterText", "placeholder", "_hoisted_7", "statusFilter", "value", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_component_CustomCheckbox", "modelValue", "selectAll", "toggleSelectAll", "default", "_withCtx", "_", "_hoisted_12", "_Fragment", "_renderList", "_ctx", "hosts", "host", "index", "id", "_hoisted_13", "_hoisted_14", "selected", "_hoisted_15", "_toDisplayString", "name", "_hoisted_16", "_hoisted_17", "ip", "_hoisted_18", "_hoisted_19", "lastPasswordChange", "_hoisted_20", "isPasswordExpired", "status", "text", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "passwordVisibility", "password", "readonly", "_hoisted_26", "togglePasswordVisibility", "_hoisted_27", "_hoisted_28", "_component_StatusBadge", "_hoisted_29", "openChangePasswordModal", "_hoisted_30", "_component_BaseModal", "changePasswordModal", "show", "title", "onConfirm", "updatePassword", "loading", "processing", "_hoisted_31", "_hoisted_32", "currentHost", "_hoisted_33", "_hoisted_34", "_hoisted_35", "method", "_hoisted_36", "_hoisted_37", "_hoisted_38", "policyId", "policies", "policy", "<PERSON><PERSON><PERSON><PERSON>", "expiryDays", "_hoisted_39", "_hoisted_40", "_hoisted_41", "generatedPassword", "generatePassword", "_component_PasswordStrengthMeter", "_hoisted_42", "_hoisted_43", "newPassword", "_hoisted_44", "confirmPassword", "passwordMismatch", "_hoisted_45", "_hoisted_46", "executeImmediately", "saveHistory", "logAudit", "batchUpdateModal", "size", "batchUpdatePasswords", "_hoisted_47", "_hoisted_48", "selectAllBatch", "toggleSelectAllBatch", "_hoisted_49", "_createBlock", "selectedHosts", "_hoisted_50", "selectedHostsCount", "_hoisted_51", "_hoisted_52", "_hoisted_53", "_hoisted_54", "_hoisted_55", "executionTime", "_hoisted_56", "_hoisted_57", "_hoisted_58", "scheduledDate", "scheduledTime", "_hoisted_59", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "batchApplyPolicy", "_hoisted_60", "_hoisted_61", "selectedHostsList", "_hoisted_62", "_hoisted_63", "_hoisted_64", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "danger", "emergencyReset", "_hoisted_65", "_hoisted_66", "_hoisted_67", "emergencyPolicies", "_hoisted_68", "_hoisted_69", "reason", "_hoisted_70", "description", "rows"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"bg-white shadow rounded-lg p-4 mb-6\">\r\n      <div class=\"flex flex-wrap items-center justify-between\">\r\n        <div class=\"flex space-x-3 mb-2 sm:mb-0\">\r\n          <button class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\" @click=\"showEmergencyReset\">\r\n            <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2\" />\r\n            <span>紧急重置</span>\r\n          </button>\r\n          <button class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\" @click=\"openBatchUpdateModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n            <span>批量更新密码</span>\r\n          </button>\r\n          <button class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\" @click=\"openBatchApplyModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n            <span>批量应用策略</span>\r\n          </button>\r\n        </div>\r\n        \r\n        <div class=\"flex items-center space-x-4\">\r\n          <!-- 视图切换 -->\r\n          <div class=\"flex items-center border rounded-md overflow-hidden\">\r\n            <button \r\n              class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{'bg-blue-500 text-white': viewMode === 'table', 'bg-gray-100 text-gray-600': viewMode !== 'table'}\"\r\n              @click=\"viewMode = 'table'\"\r\n            >\r\n              <font-awesome-icon :icon=\"['fas', 'table']\" class=\"mr-1\" />\r\n              表格\r\n            </button>\r\n            <button \r\n              class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{'bg-blue-500 text-white': viewMode === 'card', 'bg-gray-100 text-gray-600': viewMode !== 'card'}\"\r\n              @click=\"viewMode = 'card'\"\r\n            >\r\n              <font-awesome-icon :icon=\"['fas', 'th-large']\" class=\"mr-1\" />\r\n              卡片\r\n            </button>\r\n          </div>\r\n          \r\n          <!-- 筛选 -->\r\n          <div class=\"relative\">\r\n            <input \r\n              type=\"text\" \r\n              v-model=\"filterText\" \r\n              placeholder=\"筛选主机...\" \r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n            />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 状态筛选 -->\r\n          <select \r\n            v-model=\"statusFilter\" \r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\r\n          >\r\n            <option value=\"all\">所有状态</option>\r\n            <option value=\"normal\">正常</option>\r\n            <option value=\"warning\">警告</option>\r\n            <option value=\"error\">错误</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <div class=\"bg-white rounded-lg shadow overflow-hidden\">\r\n      <table class=\"min-w-full divide-y divide-gray-200\">\r\n        <thead class=\"bg-gray-50\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n                主机名\r\n              </CustomCheckbox>\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              IP地址\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              最后密码修改时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码过期时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              状态\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              操作\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"bg-white divide-y divide-gray-200\">\r\n          <tr v-for=\"(host, index) in hosts\" :key=\"host.id\"\r\n            :class=\"{ 'bg-gray-50': index % 2 === 0, 'hover:bg-blue-50': true }\">\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"flex items-center\">\r\n                <CustomCheckbox v-model=\"host.selected\">\r\n                  <span class=\"ml-2 font-medium text-gray-900\">{{ host.name }}</span>\r\n                </CustomCheckbox>\r\n              </div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"text-sm text-gray-900\">{{ host.ip }}</div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"text-sm text-gray-500\">{{ host.lastPasswordChange || '-' }}</div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div :class=\"{\r\n                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\r\n                'bg-red-100 text-red-800': isPasswordExpired(host).status === 'danger' || isPasswordExpired(host).status === 'expired',\r\n                'bg-yellow-100 text-yellow-800': isPasswordExpired(host).status === 'warning',\r\n                'bg-gray-100 text-gray-800': isPasswordExpired(host).status === 'normal'\r\n              }\">\r\n                {{ isPasswordExpired(host).text }}\r\n                <span v-if=\"isPasswordExpired(host).status === 'expired' || isPasswordExpired(host).status === 'danger'\"\r\n                  class=\"ml-1\">\r\n                  <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                </span>\r\n                <span v-else-if=\"isPasswordExpired(host).status === 'warning'\" class=\"ml-1\">\r\n                  <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" />\r\n                </span>\r\n              </div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"flex items-center\">\r\n                <div class=\"flex-grow\">\r\n                  <input :type=\"passwordVisibility[host.id] ? 'text' : 'password'\" :value=\"host.password\" readonly\r\n                    class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                </div>\r\n                <button @click=\"togglePasswordVisibility(host.id)\"\r\n                  class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                  <font-awesome-icon :icon=\"['fas', passwordVisibility[host.id] ? 'eye-slash' : 'eye']\"\r\n                    class=\"text-lg\" />\r\n                </button>\r\n              </div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <StatusBadge :type=\"host.status\" />\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n              <button\r\n                class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                @click=\"openChangePasswordModal(host)\">\r\n                <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                修改密码\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" confirm-text=\"确认更新\" @confirm=\"updatePassword\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">服务器: {{ currentHost.name }}</label>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"changePasswordModal.method\" value=\"auto\" class=\"mr-2\">\r\n            <span>自动生成</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"changePasswordModal.method\" value=\"manual\" class=\"mr-2\">\r\n            <span>手动输入</span>\r\n          </label>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-if=\"changePasswordModal.method === 'auto'\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">选择密码策略</label>\r\n          <select v-model=\"changePasswordModal.policyId\" class=\"form-select\">\r\n            <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n              {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n            </option>\r\n          </select>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <div class=\"flex\">\r\n            <input type=\"text\" v-model=\"changePasswordModal.generatedPassword\" class=\"form-control rounded-r-none\"\r\n              readonly>\r\n            <button class=\"bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded-r-md\" @click=\"generatePassword\">\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" />\r\n            </button>\r\n          </div>\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.generatedPassword\" />\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else>\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <input type=\"password\" v-model=\"changePasswordModal.newPassword\" class=\"form-control\">\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <input type=\"password\" v-model=\"changePasswordModal.confirmPassword\" class=\"form-control\"\r\n            :class=\"{ 'border-red-500': passwordMismatch }\">\r\n          <div v-if=\"passwordMismatch\" class=\"text-red-500 text-xs mt-1\">\r\n            两次输入的密码不一致\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行选项</label>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          立即执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          保存密码历史记录\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          记录审计日志\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      passwordVisibility: {},\r\n      viewMode: 'table',\r\n      filterText: '',\r\n      statusFilter: 'all',\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host) {\r\n      this.currentHost = host\r\n      this.changePasswordModal.show = true\r\n      this.changePasswordModal.generatedPassword = this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword(policy) {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policyObj ? policyObj.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      if (this.changePasswordModal && !policy) {\r\n        this.changePasswordModal.generatedPassword = password\r\n      }\r\n\r\n      return password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto'\r\n          ? this.changePasswordModal.generatedPassword\r\n          : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          password: password,\r\n          policyId: this.changePasswordModal.policyId\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取所选策略\r\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)\r\n\r\n        // 为每台主机生成并更新密码\r\n        await Promise.all(\r\n          selectedHostIds.map(async (hostId) => {\r\n            const newPassword = this.generatePassword(policy)\r\n            return this.$store.dispatch('updateHostPassword', {\r\n              hostId: hostId,\r\n              password: newPassword,\r\n              policyId: policy.id\r\n            })\r\n          })\r\n        )\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取紧急策略\r\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)\r\n\r\n        // 为每台主机生成并更新密码\r\n        await Promise.all(\r\n          selectedHostIds.map(async (hostId) => {\r\n            const newPassword = this.generatePassword(policy)\r\n            return this.$store.dispatch('updateHostPassword', {\r\n              hostId: hostId,\r\n              password: newPassword,\r\n              policyId: policy.id\r\n            })\r\n          })\r\n        )\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    togglePasswordVisibility(hostId) {\r\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]\r\n    },\r\n\r\n    isPasswordExpired(host) {\r\n      if (!host.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }\r\n\r\n      // 解析过期时间\r\n      const expiryDate = new Date(host.passwordExpiryDate)\r\n      const now = new Date()\r\n\r\n      // 如果已过期\r\n      if (expiryDate < now) {\r\n        return {\r\n          status: 'expired',\r\n          days: 0,\r\n          text: '已过期'\r\n        }\r\n      }\r\n\r\n      // 计算剩余天数和小时数\r\n      const diffTime = expiryDate - now\r\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))\r\n      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n\r\n      // 根据剩余时间确定状态\r\n      let status = 'normal'\r\n      if (diffDays < 7) {\r\n        status = 'danger'  // 少于7天\r\n      } else if (diffDays < 14) {\r\n        status = 'warning' // 少于14天\r\n      }\r\n\r\n      // 格式化显示文本\r\n      let text = ''\r\n      if (diffDays > 0) {\r\n        text += `${diffDays}天`\r\n      }\r\n      if (diffHours > 0 || diffDays === 0) {\r\n        text += `${diffHours}小时`\r\n      }\r\n\r\n      return { status, days: diffDays, text: `剩余${text}` }\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": ";;EAGSA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAA6C;;EACjDA,KAAK,EAAC;AAA6B;;EAenCA,KAAK,EAAC;AAA6B;;EAEjCA,KAAK,EAAC;AAAqD;;EAoB3DA,KAAK,EAAC;AAAU;;EAOdA,KAAK,EAAC;AAAsE;;EAoBpFA,KAAK,EAAC;AAA4C;;EAC9CA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAAY;;EAEjBC,KAAK,EAAC,KAAK;EAACD,KAAK,EAAC;;;EAyBnBA,KAAK,EAAC;AAAmC;;EAGxCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EAEpBA,KAAK,EAAC;AAAgC;;EAI9CA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAlHnDE,GAAA;EA2HkBF,KAAK,EAAC;;;EA3HxBE,GAAA;EA8H+EF,KAAK,EAAC;;;EAKrEA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAW;oBArItC;oBAAA;;EAgJgBA,KAAK,EAAC;AAA6B;;EAGnCA,KAAK,EAAC;AAAmD;oBAnJzE;;EAmKWA,KAAK,EAAC;AAAY;;EACdA,KAAK,EAAC;AAAY;;EAGtBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EA9K1CE,GAAA;AAAA;;EAsLaF,KAAK,EAAC;AAAY;oBAtL/B;;EA+LaA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EAjM3BE,GAAA;AAAA;;EA6MaF,KAAK,EAAC;AAAY;;EAMlBA,KAAK,EAAC;AAAY;;EAnN/BE,GAAA;EAuNuCF,KAAK,EAAC;;;EAMlCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAY;oBA7P7B;;EAsQWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EA7Q1CE,GAAA;EAmRmEF,KAAK,EAAC;;;EAC1DA,KAAK,EAAC;AAAwB;;EAalCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;oBA5T7B;;EAqUWA,KAAK,EAAC;AAAY;;EAkBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;oBAjW7B;;EA0WWA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;;;;;;uBApX3BG,mBAAA,CA0XM,cAzXJC,mBAAA,UAAa,EACbC,mBAAA,CA+DM,OA/DNC,UA+DM,GA9DJD,mBAAA,CA6DM,OA7DNE,UA6DM,GA5DJF,mBAAA,CAaM,OAbNG,UAaM,GAZJH,mBAAA,CAGS;IAHDL,KAAK,EAAC,qNAAqN;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,kBAAA,IAAAD,QAAA,CAAAC,kBAAA,IAAAF,IAAA,CAAkB;MAC5PG,YAAA,CAA0EC,4BAAA;IAAtDC,IAAI,EAAE,+BAA+B;IAAEhB,KAAK,EAAC;kCACjEK,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGS;IAHDL,KAAK,EAAC,wNAAwN;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAK,oBAAA,IAAAL,QAAA,CAAAK,oBAAA,IAAAN,IAAA,CAAoB;MACjQG,YAAA,CAAyDC,4BAAA;IAArCC,IAAI,EAAE,cAAc;IAAEhB,KAAK,EAAC;kCAChDK,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAGS;IAHDL,KAAK,EAAC,8NAA8N;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAM,mBAAA,IAAAN,QAAA,CAAAM,mBAAA,IAAAP,IAAA,CAAmB;MACtQG,YAAA,CAAgEC,4BAAA;IAA5CC,IAAI,EAAE,qBAAqB;IAAEhB,KAAK,EAAC;kCACvDK,mBAAA,CAAmB,cAAb,QAAM,qB,KAIhBA,mBAAA,CA4CM,OA5CNc,UA4CM,GA3CJf,mBAAA,UAAa,EACbC,mBAAA,CAiBM,OAjBNe,UAiBM,GAhBJf,mBAAA,CAOS;IANPL,KAAK,EAxBnBqB,eAAA,EAwBoB,8BAA8B;MAAA,0BACDC,KAAA,CAAAC,QAAQ;MAAA,6BAA2CD,KAAA,CAAAC,QAAQ;IAAA;IAC7Fd,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAEhBT,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEhB,KAAK,EAAC;kCA5BhEyB,gBAAA,CA4ByE,MAE7D,G,kBACApB,mBAAA,CAOS;IANPL,KAAK,EAhCnBqB,eAAA,EAgCoB,8BAA8B;MAAA,0BACDC,KAAA,CAAAC,QAAQ;MAAA,6BAA0CD,KAAA,CAAAC,QAAQ;IAAA;IAC5Fd,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAEhBT,YAAA,CAA8DC,4BAAA;IAA1CC,IAAI,EAAE,mBAAmB;IAAEhB,KAAK,EAAC;kCApCnEyB,gBAAA,CAoC4E,MAEhE,G,oBAGFrB,mBAAA,QAAW,EACXC,mBAAA,CAUM,OAVNqB,UAUM,G,gBATJrB,mBAAA,CAKE;IAJAsB,IAAI,EAAC,MAAM;IA5CzB,uBAAAjB,MAAA,QAAAA,MAAA,MAAAc,MAAA,IA6CuBF,KAAA,CAAAM,UAAU,GAAAJ,MAAA;IACnBK,WAAW,EAAC,SAAS;IACrB7B,KAAK,EAAC;iDAFGsB,KAAA,CAAAM,UAAU,E,GAIrBvB,mBAAA,CAEM,OAFNyB,UAEM,GADJhB,YAAA,CAAqEC,4BAAA;IAAjDC,IAAI,EAAE,iBAAiB;IAAEhB,KAAK,EAAC;UAIvDI,mBAAA,UAAa,E,gBACbC,mBAAA,CAQS;IA/DnB,uBAAAK,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAwDqBF,KAAA,CAAAS,YAAY,GAAAP,MAAA;IACrBxB,KAAK,EAAC;kCAENK,mBAAA,CAAiC;IAAzB2B,KAAK,EAAC;EAAK,GAAC,MAAI,qBACxB3B,mBAAA,CAAkC;IAA1B2B,KAAK,EAAC;EAAQ,GAAC,IAAE,qBACzB3B,mBAAA,CAAmC;IAA3B2B,KAAK,EAAC;EAAS,GAAC,IAAE,qBAC1B3B,mBAAA,CAAiC;IAAzB2B,KAAK,EAAC;EAAO,GAAC,IAAE,oB,2CANfV,KAAA,CAAAS,YAAY,E,SAY7B3B,mBAAA,UAAa,EACbC,mBAAA,CAyFM,OAzFN4B,UAyFM,GAxFJ5B,mBAAA,CAuFQ,SAvFR6B,UAuFQ,GAtFN7B,mBAAA,CA0BQ,SA1BR8B,WA0BQ,GAzBN9B,mBAAA,CAwBK,aAvBHA,mBAAA,CAIK,MAJL+B,WAIK,GAHHtB,YAAA,CAEiBuB,yBAAA;IA5E/BC,UAAA,EA0EuChB,KAAA,CAAAiB,SAAS;IA1EhD,wB,oCA0EuCjB,KAAA,CAAAiB,SAAS,GAAAf,MAAA,GAAsBZ,QAAA,CAAA4B,eAAe;;IA1ErFC,OAAA,EAAAC,QAAA,CA0EuF,MAEzEhC,MAAA,SAAAA,MAAA,QA5Ede,gBAAA,CA0EuF,OAEzE,E;IA5EdkB,CAAA;0FA8EYtC,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACD,KAAK,EAAC;KAAiF,QAEvG,sB,4BACAK,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACD,KAAK,EAAC;KAAiF,YAEvG,sB,4BACAK,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACD,KAAK,EAAC;KAAiF,UAEvG,sB,4BACAK,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACD,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAK,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACD,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAK,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACD,KAAK,EAAC;KAAiF,MAEvG,qB,KAGJK,mBAAA,CA0DQ,SA1DRuC,WA0DQ,I,kBAzDNzC,mBAAA,CAwDK0C,SAAA,QA3JfC,WAAA,CAmGsCC,IAAA,CAAAC,KAAK,EAnG3C,CAmGsBC,IAAI,EAAEC,KAAK;yBAAvB/C,mBAAA,CAwDK;MAxD+BD,GAAG,EAAE+C,IAAI,CAACE,EAAE;MAC7CnD,KAAK,EApGlBqB,eAAA;QAAA,cAoGoC6B,KAAK;QAAA;MAAA;QAC7B7C,mBAAA,CAMK,MANL+C,WAMK,GALH/C,mBAAA,CAIM,OAJNgD,WAIM,GAHJvC,YAAA,CAEiBuB,yBAAA;MAzGjCC,UAAA,EAuGyCW,IAAI,CAACK,QAAQ;MAvGtD,uBAAA9B,MAAA,IAuGyCyB,IAAI,CAACK,QAAQ,GAAA9B;;MAvGtDiB,OAAA,EAAAC,QAAA,CAwGkB,MAAmE,CAAnErC,mBAAA,CAAmE,QAAnEkD,WAAmE,EAAAC,gBAAA,CAAnBP,IAAI,CAACQ,IAAI,iB;MAxG3Ed,CAAA;oFA4GYtC,mBAAA,CAEK,MAFLqD,WAEK,GADHrD,mBAAA,CAAsD,OAAtDsD,WAAsD,EAAAH,gBAAA,CAAhBP,IAAI,CAACW,EAAE,iB,GAE/CvD,mBAAA,CAEK,MAFLwD,WAEK,GADHxD,mBAAA,CAA6E,OAA7EyD,WAA6E,EAAAN,gBAAA,CAAvCP,IAAI,CAACc,kBAAkB,wB,GAE/D1D,mBAAA,CAgBK,MAhBL2D,WAgBK,GAfH3D,mBAAA,CAcM;MAdAL,KAAK,EAnHzBqB,eAAA;;mCAmH2KT,QAAA,CAAAqD,iBAAiB,CAAChB,IAAI,EAAEiB,MAAM,iBAAiBtD,QAAA,CAAAqD,iBAAiB,CAAChB,IAAI,EAAEiB,MAAM;yCAAkEtD,QAAA,CAAAqD,iBAAiB,CAAChB,IAAI,EAAEiB,MAAM;qCAA8DtD,QAAA,CAAAqD,iBAAiB,CAAChB,IAAI,EAAEiB,MAAM;;QAnHpbzC,gBAAA,CAAA+B,gBAAA,CAyHmB5C,QAAA,CAAAqD,iBAAiB,CAAChB,IAAI,EAAEkB,IAAI,IAAG,GAClC,iBAAYvD,QAAA,CAAAqD,iBAAiB,CAAChB,IAAI,EAAEiB,MAAM,kBAAkBtD,QAAA,CAAAqD,iBAAiB,CAAChB,IAAI,EAAEiB,MAAM,iB,cAA1F/D,mBAAA,CAGO,QAHPiE,WAGO,GADLtD,YAAA,CAA6DC,4BAAA;MAAzCC,IAAI,EAAE;IAA+B,G,KAE1CJ,QAAA,CAAAqD,iBAAiB,CAAChB,IAAI,EAAEiB,MAAM,kB,cAA/C/D,mBAAA,CAEO,QAFPkE,WAEO,GADLvD,YAAA,CAA2DC,4BAAA;MAAvCC,IAAI,EAAE;IAA6B,G,KA/HzEZ,mBAAA,e,oBAmIYC,mBAAA,CAYK,MAZLiE,WAYK,GAXHjE,mBAAA,CAUM,OAVNkE,WAUM,GATJlE,mBAAA,CAGM,OAHNmE,WAGM,GAFJnE,mBAAA,CACkG;MAD1FsB,IAAI,EAAEL,KAAA,CAAAmD,kBAAkB,CAACxB,IAAI,CAACE,EAAE;MAA0BnB,KAAK,EAAEiB,IAAI,CAACyB,QAAQ;MAAEC,QAAQ,EAAR,EAAQ;MAC9F3E,KAAK,EAAC;4BAvI1B4E,WAAA,E,GAyIgBvE,mBAAA,CAIS;MAJAI,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAAiE,wBAAwB,CAAC5B,IAAI,CAACE,EAAE;MAC9CnD,KAAK,EAAC;QACNc,YAAA,CACoBC,4BAAA;MADAC,IAAI,UAAUM,KAAA,CAAAmD,kBAAkB,CAACxB,IAAI,CAACE,EAAE;MAC1DnD,KAAK,EAAC;uDA5I1B8E,WAAA,E,KAgJYzE,mBAAA,CAEK,MAFL0E,WAEK,GADHjE,YAAA,CAAmCkE,sBAAA;MAArBrD,IAAI,EAAEsB,IAAI,CAACiB;yCAE3B7D,mBAAA,CAOK,MAPL4E,WAOK,GANH5E,mBAAA,CAKS;MAJPL,KAAK,EAAC,0NAA0N;MAC/NS,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAAsE,uBAAuB,CAACjC,IAAI;QACpCnC,YAAA,CAAyDC,4BAAA;MAArCC,IAAI,EAAE,cAAc;MAAEhB,KAAK,EAAC;oCAvJhEyB,gBAAA,CAuJyE,QAE3D,G,iBAzJd0D,WAAA,E;wCAgKI/E,mBAAA,YAAe,EACfU,YAAA,CAwEYsE,oBAAA;IAzOhB9C,UAAA,EAiKwBhB,KAAA,CAAA+D,mBAAmB,CAACC,IAAI;IAjKhD,uBAAA5E,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAiKwBF,KAAA,CAAA+D,mBAAmB,CAACC,IAAI,GAAA9D,MAAA;IAAE+D,KAAK,EAAC,MAAM;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAE5E,QAAA,CAAA6E,cAAc;IACpGC,OAAO,EAAEpE,KAAA,CAAAqE;;IAlKhBlD,OAAA,EAAAC,QAAA,CAmKM,MAEM,CAFNrC,mBAAA,CAEM,OAFNuF,WAEM,GADJvF,mBAAA,CAA6D,SAA7DwF,WAA6D,EAAnC,OAAK,GAAArC,gBAAA,CAAGlC,KAAA,CAAAwE,WAAW,CAACrC,IAAI,iB,GAGpDpD,mBAAA,CAYM,OAZN0F,WAYM,G,4BAXJ1F,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CASM,OATN2F,WASM,GARJ3F,mBAAA,CAGQ,SAHR4F,WAGQ,G,gBAFN5F,mBAAA,CAAmF;MAA5EsB,IAAI,EAAC,OAAO;MA3K/B,uBAAAjB,MAAA,QAAAA,MAAA,MAAAc,MAAA,IA2KyCF,KAAA,CAAA+D,mBAAmB,CAACa,MAAM,GAAA1E,MAAA;MAAEQ,KAAK,EAAC,MAAM;MAAChC,KAAK,EAAC;oDAA/CsB,KAAA,CAAA+D,mBAAmB,CAACa,MAAM,E,+BACvD7F,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHR8F,WAGQ,G,gBAFN9F,mBAAA,CAAqF;MAA9EsB,IAAI,EAAC,OAAO;MA/K/B,uBAAAjB,MAAA,QAAAA,MAAA,MAAAc,MAAA,IA+KyCF,KAAA,CAAA+D,mBAAmB,CAACa,MAAM,GAAA1E,MAAA;MAAEQ,KAAK,EAAC,QAAQ;MAAChC,KAAK,EAAC;oDAAjDsB,KAAA,CAAA+D,mBAAmB,CAACa,MAAM,E,+BACvD7F,mBAAA,CAAiB,cAAX,MAAI,qB,OAKLiB,KAAA,CAAA+D,mBAAmB,CAACa,MAAM,e,cAArC/F,mBAAA,CAqBM,OA1MZiG,WAAA,GAsLQ/F,mBAAA,CAOM,OAPNgG,WAOM,G,4BANJhG,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCK,mBAAA,CAIS;MA5LnB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAwL2BF,KAAA,CAAA+D,mBAAmB,CAACiB,QAAQ,GAAA9E,MAAA;MAAExB,KAAK,EAAC;2BACnDG,mBAAA,CAES0C,SAAA,QA3LrBC,WAAA,CAyLqCC,IAAA,CAAAwD,QAAQ,EAAlBC,MAAM;2BAArBrG,mBAAA,CAES;QAF2BD,GAAG,EAAEsG,MAAM,CAACrD,EAAE;QAAGnB,KAAK,EAAEwE,MAAM,CAACrD;0BAC9DqD,MAAM,CAAC/C,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAGgD,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAjD,gBAAA,CAAGgD,MAAM,CAACE,UAAU,IAAG,KAC9E,uBA3LZC,WAAA;6EAwL2BrF,KAAA,CAAA+D,mBAAmB,CAACiB,QAAQ,E,KAO/CjG,mBAAA,CAUM,OAVNuG,WAUM,G,4BATJvG,mBAAA,CAAuC;MAAhCL,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BK,mBAAA,CAMM,OANNwG,WAMM,G,gBALJxG,mBAAA,CACW;MADJsB,IAAI,EAAC,MAAM;MAlM9B,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAkMwCF,KAAA,CAAA+D,mBAAmB,CAACyB,iBAAiB,GAAAtF,MAAA;MAAExB,KAAK,EAAC,6BAA6B;MACpG2E,QAAQ,EAAR;mDAD0BrD,KAAA,CAAA+D,mBAAmB,CAACyB,iBAAiB,E,GAEjEzG,mBAAA,CAES;MAFDL,KAAK,EAAC,sDAAsD;MAAES,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEC,QAAA,CAAAmG,gBAAA,IAAAnG,QAAA,CAAAmG,gBAAA,IAAApG,IAAA,CAAgB;QAC3FG,YAAA,CAAiDC,4BAAA;MAA7BC,IAAI,EAAE;IAAmB,G,KAGjDF,YAAA,CAA2EkG,gCAAA;MAAnDtC,QAAQ,EAAEpD,KAAA,CAAA+D,mBAAmB,CAACyB;gEAI1D3G,mBAAA,CAeM,OA3NZ8G,WAAA,GA6MQ5G,mBAAA,CAIM,OAJN6G,WAIM,G,4BAHJ7G,mBAAA,CAAqC;MAA9BL,KAAK,EAAC;IAAY,GAAC,KAAG,sB,gBAC7BK,mBAAA,CAAsF;MAA/EsB,IAAI,EAAC,UAAU;MA/MhC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA+M0CF,KAAA,CAAA+D,mBAAmB,CAAC8B,WAAW,GAAA3F,MAAA;MAAExB,KAAK,EAAC;mDAAvCsB,KAAA,CAAA+D,mBAAmB,CAAC8B,WAAW,E,GAC/DrG,YAAA,CAAqEkG,gCAAA;MAA7CtC,QAAQ,EAAEpD,KAAA,CAAA+D,mBAAmB,CAAC8B;6CAGxD9G,mBAAA,CAOM,OAPN+G,WAOM,G,4BANJ/G,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CACkD;MAD3CsB,IAAI,EAAC,UAAU;MArNhC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAqN0CF,KAAA,CAAA+D,mBAAmB,CAACgC,eAAe,GAAA7F,MAAA;MAAExB,KAAK,EArNpFqB,eAAA,EAqNqF,cAAc;QAAA,kBAC3DT,QAAA,CAAA0G;MAAgB;4CADdhG,KAAA,CAAA+D,mBAAmB,CAACgC,eAAe,E,GAExDzG,QAAA,CAAA0G,gBAAgB,I,cAA3BnH,mBAAA,CAEM,OAFNoH,WAEM,EAFyD,cAE/D,KAzNVnH,mBAAA,e,MA6NMC,mBAAA,CAWM,OAXNmH,WAWM,G,4BAVJnH,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Bc,YAAA,CAEiBuB,yBAAA;MAjOzBC,UAAA,EA+NiChB,KAAA,CAAA+D,mBAAmB,CAACoC,kBAAkB;MA/NvE,uBAAA/G,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA+NiCF,KAAA,CAAA+D,mBAAmB,CAACoC,kBAAkB,GAAAjG,MAAA;;MA/NvEiB,OAAA,EAAAC,QAAA,CA+NyE,MAEjEhC,MAAA,SAAAA,MAAA,QAjORe,gBAAA,CA+NyE,QAEjE,E;MAjORkB,CAAA;uCAkOQ7B,YAAA,CAEiBuB,yBAAA;MApOzBC,UAAA,EAkOiChB,KAAA,CAAA+D,mBAAmB,CAACqC,WAAW;MAlOhE,uBAAAhH,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAkOiCF,KAAA,CAAA+D,mBAAmB,CAACqC,WAAW,GAAAlG,MAAA;;MAlOhEiB,OAAA,EAAAC,QAAA,CAkOkE,MAE1DhC,MAAA,SAAAA,MAAA,QApORe,gBAAA,CAkOkE,YAE1D,E;MApORkB,CAAA;uCAqOQ7B,YAAA,CAEiBuB,yBAAA;MAvOzBC,UAAA,EAqOiChB,KAAA,CAAA+D,mBAAmB,CAACsC,QAAQ;MArO7D,uBAAAjH,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAqOiCF,KAAA,CAAA+D,mBAAmB,CAACsC,QAAQ,GAAAnG,MAAA;;MArO7DiB,OAAA,EAAAC,QAAA,CAqO+D,MAEvDhC,MAAA,SAAAA,MAAA,QAvORe,gBAAA,CAqO+D,UAEvD,E;MAvORkB,CAAA;;IAAAA,CAAA;6DA2OIvC,mBAAA,cAAiB,EACjBU,YAAA,CAiEYsE,oBAAA;IA7ShB9C,UAAA,EA4OwBhB,KAAA,CAAAsG,gBAAgB,CAACtC,IAAI;IA5O7C,uBAAA5E,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4OwBF,KAAA,CAAAsG,gBAAgB,CAACtC,IAAI,GAAA9D,MAAA;IAAE+D,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAACsC,IAAI,EAAC,IAAI;IACpFrC,SAAO,EAAE5E,QAAA,CAAAkH,oBAAoB;IAAGpC,OAAO,EAAEpE,KAAA,CAAAqE;;IA7OhDlD,OAAA,EAAAC,QAAA,CA8OM,MAaM,CAbNrC,mBAAA,CAaM,OAbN0H,WAaM,G,4BAZJ1H,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CAIM,OAJN2H,WAIM,GAHJlH,YAAA,CAEiBuB,yBAAA;MAnP3BC,UAAA,EAiPmChB,KAAA,CAAA2G,cAAc;MAjPjD,wB,sCAiPmC3G,KAAA,CAAA2G,cAAc,GAAAzG,MAAA,GAAsBZ,QAAA,CAAAsH,oBAAoB;;MAjP3FzF,OAAA,EAAAC,QAAA,CAiP6F,MAEnFhC,MAAA,SAAAA,MAAA,QAnPVe,gBAAA,CAiP6F,MAEnF,E;MAnPVkB,CAAA;gEAqPQtC,mBAAA,CAIM,OAJN8H,WAIM,I,kBAHJhI,mBAAA,CAEiB0C,SAAA,QAxP3BC,WAAA,CAsPyCC,IAAA,CAAAC,KAAK,EAAbC,IAAI;2BAA3BmF,YAAA,CAEiB/F,yBAAA;QAFsBnC,GAAG,EAAE+C,IAAI,CAACE,EAAE;QAtP7Db,UAAA,EAsPwEhB,KAAA,CAAAsG,gBAAgB,CAACS,aAAa,CAACpF,IAAI,CAACE,EAAE;QAtP9G,uBAAA3B,MAAA,IAsPwEF,KAAA,CAAAsG,gBAAgB,CAACS,aAAa,CAACpF,IAAI,CAACE,EAAE,IAAA3B;;QAtP9GiB,OAAA,EAAAC,QAAA,CAuPY,MAAe,CAvP3BjB,gBAAA,CAAA+B,gBAAA,CAuPeP,IAAI,CAACQ,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGP,IAAI,CAACW,EAAE,IAAG,IAChC,gB;QAxPVjB,CAAA;;sCA0PQtC,mBAAA,CAAyD,KAAzDiI,WAAyD,EAApC,MAAI,GAAA9E,gBAAA,CAAG5C,QAAA,CAAA2H,kBAAkB,IAAG,MAAI,gB,GAGvDlI,mBAAA,CAOM,OAPNmI,WAOM,G,4BANJnI,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CAIS;MAnQjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA+PyBF,KAAA,CAAAsG,gBAAgB,CAACtB,QAAQ,GAAA9E,MAAA;MAAExB,KAAK,EAAC;2BAChDG,mBAAA,CAES0C,SAAA,QAlQnBC,WAAA,CAgQmCC,IAAA,CAAAwD,QAAQ,EAAlBC,MAAM;2BAArBrG,mBAAA,CAES;QAF2BD,GAAG,EAAEsG,MAAM,CAACrD,EAAE;QAAGnB,KAAK,EAAEwE,MAAM,CAACrD;0BAC9DqD,MAAM,CAAC/C,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAGgD,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAjD,gBAAA,CAAGgD,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAlQV+B,WAAA;6EA+PyBnH,KAAA,CAAAsG,gBAAgB,CAACtB,QAAQ,E,KAO5CjG,mBAAA,CAyBM,OAzBNqI,WAyBM,G,4BAxBJrI,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BK,mBAAA,CASM,OATNsI,WASM,GARJtI,mBAAA,CAGQ,SAHRuI,WAGQ,G,gBAFNvI,mBAAA,CAA4F;MAArFsB,IAAI,EAAC,OAAO;MA1Q/B,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA0QyCF,KAAA,CAAAsG,gBAAgB,CAACiB,aAAa,GAAArH,MAAA;MAAEQ,KAAK,EAAC,WAAW;MAAChC,KAAK,EAAC;oDAAxDsB,KAAA,CAAAsG,gBAAgB,CAACiB,aAAa,E,+BAC3DxI,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHRyI,WAGQ,G,gBAFNzI,mBAAA,CAA4F;MAArFsB,IAAI,EAAC,OAAO;MA9Q/B,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8QyCF,KAAA,CAAAsG,gBAAgB,CAACiB,aAAa,GAAArH,MAAA;MAAEQ,KAAK,EAAC,WAAW;MAAChC,KAAK,EAAC;oDAAxDsB,KAAA,CAAAsG,gBAAgB,CAACiB,aAAa,E,+BAC3DxI,mBAAA,CAAiB,cAAX,MAAI,qB,KAIHiB,KAAA,CAAAsG,gBAAgB,CAACiB,aAAa,oB,cAAzC1I,mBAAA,CAWM,OAXN4I,WAWM,GAVJ1I,mBAAA,CASM,OATN2I,WASM,GARJ3I,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAoC;MAA7BL,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BK,mBAAA,CAAiF;MAA1EsB,IAAI,EAAC,MAAM;MAvRhC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAuR0CF,KAAA,CAAAsG,gBAAgB,CAACqB,aAAa,GAAAzH,MAAA;MAAExB,KAAK,EAAC;mDAAtCsB,KAAA,CAAAsG,gBAAgB,CAACqB,aAAa,E,KAE5D5I,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAoC;MAA7BL,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BK,mBAAA,CAAiF;MAA1EsB,IAAI,EAAC,MAAM;MA3RhC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA2R0CF,KAAA,CAAAsG,gBAAgB,CAACsB,aAAa,GAAA1H,MAAA;MAAExB,KAAK,EAAC;mDAAtCsB,KAAA,CAAAsG,gBAAgB,CAACsB,aAAa,E,WA3RxE9I,mBAAA,e,GAiSMC,mBAAA,CAWM,OAXN8I,WAWM,G,4BAVJ9I,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Bc,YAAA,CAEiBuB,yBAAA;MArSzBC,UAAA,EAmSiChB,KAAA,CAAAsG,gBAAgB,CAACwB,YAAY;MAnS9D,uBAAA1I,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAmSiCF,KAAA,CAAAsG,gBAAgB,CAACwB,YAAY,GAAA5H,MAAA;;MAnS9DiB,OAAA,EAAAC,QAAA,CAmSgE,MAExDhC,MAAA,SAAAA,MAAA,QArSRe,gBAAA,CAmSgE,YAExD,E;MArSRkB,CAAA;uCAsSQ7B,YAAA,CAEiBuB,yBAAA;MAxSzBC,UAAA,EAsSiChB,KAAA,CAAAsG,gBAAgB,CAACyB,WAAW;MAtS7D,uBAAA3I,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAsSiCF,KAAA,CAAAsG,gBAAgB,CAACyB,WAAW,GAAA7H,MAAA;;MAtS7DiB,OAAA,EAAAC,QAAA,CAsS+D,MAEvDhC,MAAA,SAAAA,MAAA,QAxSRe,gBAAA,CAsS+D,UAEvD,E;MAxSRkB,CAAA;uCAySQ7B,YAAA,CAEiBuB,yBAAA;MA3SzBC,UAAA,EAySiChB,KAAA,CAAAsG,gBAAgB,CAAC0B,gBAAgB;MAzSlE,uBAAA5I,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAySiCF,KAAA,CAAAsG,gBAAgB,CAAC0B,gBAAgB,GAAA9H,MAAA;;MAzSlEiB,OAAA,EAAAC,QAAA,CAySoE,MAE5DhC,MAAA,SAAAA,MAAA,QA3SRe,gBAAA,CAySoE,aAE5D,E;MA3SRkB,CAAA;;IAAAA,CAAA;6DA+SIvC,mBAAA,cAAiB,EACjBU,YAAA,CA8BYsE,oBAAA;IA9UhB9C,UAAA,EAgTwBhB,KAAA,CAAAiI,eAAe,CAACjE,IAAI;IAhT5C,uBAAA5E,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAgTwBF,KAAA,CAAAiI,eAAe,CAACjE,IAAI,GAAA9D,MAAA;IAAE+D,KAAK,EAAC,UAAU;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAE5E,QAAA,CAAA4I,gBAAgB;IACtG9D,OAAO,EAAEpE,KAAA,CAAAqE;;IAjThBlD,OAAA,EAAAC,QAAA,CAkTM,MAQM,CARNrC,mBAAA,CAQM,OARNoJ,WAQM,G,4BAPJpJ,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CAKM,OALNqJ,WAKM,I,kBAJJvJ,mBAAA,CAGiB0C,SAAA,QAxT3BC,WAAA,CAqTyClC,QAAA,CAAA+I,iBAAiB,EAAzB1G,IAAI;2BAA3BmF,YAAA,CAGiB/F,yBAAA;QAHkCnC,GAAG,EAAE+C,IAAI,CAACE,EAAE;QArTzEb,UAAA,EAsTqBhB,KAAA,CAAAiI,eAAe,CAAClB,aAAa,CAACpF,IAAI,CAACE,EAAE;QAtT1D,uBAAA3B,MAAA,IAsTqBF,KAAA,CAAAiI,eAAe,CAAClB,aAAa,CAACpF,IAAI,CAACE,EAAE,IAAA3B;;QAtT1DiB,OAAA,EAAAC,QAAA,CAuTY,MAAe,CAvT3BjB,gBAAA,CAAA+B,gBAAA,CAuTeP,IAAI,CAACQ,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGP,IAAI,CAACW,EAAE,IAAG,IAChC,gB;QAxTVjB,CAAA;;wCA4TMtC,mBAAA,CAOM,OAPNuJ,WAOM,G,4BANJvJ,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCK,mBAAA,CAIS;MAlUjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8TyBF,KAAA,CAAAiI,eAAe,CAACjD,QAAQ,GAAA9E,MAAA;MAAExB,KAAK,EAAC;2BAC/CG,mBAAA,CAES0C,SAAA,QAjUnBC,WAAA,CA+TmCC,IAAA,CAAAwD,QAAQ,EAAlBC,MAAM;2BAArBrG,mBAAA,CAES;QAF2BD,GAAG,EAAEsG,MAAM,CAACrD,EAAE;QAAGnB,KAAK,EAAEwE,MAAM,CAACrD;0BAC9DqD,MAAM,CAAC/C,IAAI,wBAhU1BoG,WAAA;6EA8TyBvI,KAAA,CAAAiI,eAAe,CAACjD,QAAQ,E,KAO3CjG,mBAAA,CAQM,OARNyJ,WAQM,G,4BAPJzJ,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Bc,YAAA,CAEiBuB,yBAAA;MAzUzBC,UAAA,EAuUiChB,KAAA,CAAAiI,eAAe,CAACQ,iBAAiB;MAvUlE,uBAAArJ,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAuUiCF,KAAA,CAAAiI,eAAe,CAACQ,iBAAiB,GAAAvI,MAAA;;MAvUlEiB,OAAA,EAAAC,QAAA,CAuUoE,MAE5DhC,MAAA,SAAAA,MAAA,QAzURe,gBAAA,CAuUoE,eAE5D,E;MAzURkB,CAAA;uCA0UQ7B,YAAA,CAEiBuB,yBAAA;MA5UzBC,UAAA,EA0UiChB,KAAA,CAAAiI,eAAe,CAACS,iBAAiB;MA1UlE,uBAAAtJ,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA0UiCF,KAAA,CAAAiI,eAAe,CAACS,iBAAiB,GAAAxI,MAAA;;MA1UlEiB,OAAA,EAAAC,QAAA,CA0UoE,MAE5DhC,MAAA,SAAAA,MAAA,QA5URe,gBAAA,CA0UoE,aAE5D,E;MA5URkB,CAAA;;IAAAA,CAAA;6DAgVIvC,mBAAA,cAAiB,EACjBU,YAAA,CAyCYsE,oBAAA;IA1XhB9C,UAAA,EAiVwBhB,KAAA,CAAA2I,mBAAmB,CAAC3E,IAAI;IAjVhD,uBAAA5E,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAiVwBF,KAAA,CAAA2I,mBAAmB,CAAC3E,IAAI,GAAA9D,MAAA;IAAE+D,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAACvE,IAAI,EAAC,sBAAsB;IAACkJ,MAAM,EAAN,EAAM;IAChH1E,SAAO,EAAE5E,QAAA,CAAAuJ,cAAc;IAAGzE,OAAO,EAAEpE,KAAA,CAAAqE;;IAlV1ClD,OAAA,EAAAC,QAAA,CAmVM,MAEM,C,4BAFNrC,mBAAA,CAEM;MAFDL,KAAK,EAAC;IAA4C,IACrDK,mBAAA,CAA+C,WAA5C,0CAAwC,E,sBAG7CA,mBAAA,CAQM,OARN+J,WAQM,G,4BAPJ/J,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CAKM,OALNgK,WAKM,I,kBAJJlK,mBAAA,CAGiB0C,SAAA,QA7V3BC,WAAA,CA0VyClC,QAAA,CAAA+I,iBAAiB,EAAzB1G,IAAI;2BAA3BmF,YAAA,CAGiB/F,yBAAA;QAHkCnC,GAAG,EAAE+C,IAAI,CAACE,EAAE;QA1VzEb,UAAA,EA2VqBhB,KAAA,CAAA2I,mBAAmB,CAAC5B,aAAa,CAACpF,IAAI,CAACE,EAAE;QA3V9D,uBAAA3B,MAAA,IA2VqBF,KAAA,CAAA2I,mBAAmB,CAAC5B,aAAa,CAACpF,IAAI,CAACE,EAAE,IAAA3B;;QA3V9DiB,OAAA,EAAAC,QAAA,CA4VY,MAAe,CA5V3BjB,gBAAA,CAAA+B,gBAAA,CA4VeP,IAAI,CAACQ,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGP,IAAI,CAACW,EAAE,IAAG,IAChC,gB;QA7VVjB,CAAA;;wCAiWMtC,mBAAA,CAOM,OAPNiK,WAOM,G,4BANJjK,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCK,mBAAA,CAIS;MAvWjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAmWyBF,KAAA,CAAA2I,mBAAmB,CAAC3D,QAAQ,GAAA9E,MAAA;MAAExB,KAAK,EAAC;2BACnDG,mBAAA,CAES0C,SAAA,QAtWnBC,WAAA,CAoWmClC,QAAA,CAAA2J,iBAAiB,EAA3B/D,MAAM;2BAArBrG,mBAAA,CAES;QAFoCD,GAAG,EAAEsG,MAAM,CAACrD,EAAE;QAAGnB,KAAK,EAAEwE,MAAM,CAACrD;0BACvEqD,MAAM,CAAC/C,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAGgD,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAjD,gBAAA,CAAGgD,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAtWV8D,WAAA;6EAmWyBlJ,KAAA,CAAA2I,mBAAmB,CAAC3D,QAAQ,E,KAO/CjG,mBAAA,CASM,OATNoK,WASM,G,4BARJpK,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CAMS;MAlXjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4WyBF,KAAA,CAAA2I,mBAAmB,CAACS,MAAM,GAAAlJ,MAAA;MAAExB,KAAK,EAAC;oCACjDK,mBAAA,CAAiD;MAAzC2B,KAAK,EAAC;IAAmB,GAAC,QAAM,qBACxC3B,mBAAA,CAA2C;MAAnC2B,KAAK,EAAC;IAAe,GAAC,MAAI,qBAClC3B,mBAAA,CAA6C;MAArC2B,KAAK,EAAC;IAAiB,GAAC,MAAI,qBACpC3B,mBAAA,CAAwC;MAAhC2B,KAAK,EAAC;IAAY,GAAC,MAAI,qBAC/B3B,mBAAA,CAAmC;MAA3B2B,KAAK,EAAC;IAAO,GAAC,MAAI,oB,2CALXV,KAAA,CAAA2I,mBAAmB,CAACS,MAAM,E,KAS7CrK,mBAAA,CAIM,OAJNsK,WAIM,G,4BAHJtK,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CACuC;MAxX/C,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAuX2BF,KAAA,CAAA2I,mBAAmB,CAACW,WAAW,GAAApJ,MAAA;MAAExB,KAAK,EAAC,cAAc;MAAC6K,IAAI,EAAC,GAAG;MAC/EhJ,WAAW,EAAC;mDADKP,KAAA,CAAA2I,mBAAmB,CAACW,WAAW,E;IAvX1DjI,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}