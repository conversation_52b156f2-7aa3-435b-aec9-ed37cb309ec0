{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, vModelRadio as _vModelRadio, withDirectives as _withDirectives, vModelSelect as _vModelSelect, vModelText as _vModelText, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"flex space-x-3 mb-6\"\n};\nconst _hoisted_2 = {\n  class: \"bg-white rounded-lg shadow overflow-hidden\"\n};\nconst _hoisted_3 = {\n  class: \"min-w-full divide-y divide-gray-200\"\n};\nconst _hoisted_4 = {\n  class: \"bg-gray-50\"\n};\nconst _hoisted_5 = {\n  scope: \"col\",\n  class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n};\nconst _hoisted_6 = {\n  class: \"bg-white divide-y divide-gray-200\"\n};\nconst _hoisted_7 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_8 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_9 = {\n  class: \"ml-2 font-medium text-gray-900\"\n};\nconst _hoisted_10 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_11 = {\n  class: \"text-sm text-gray-900\"\n};\nconst _hoisted_12 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_13 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_14 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_15 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_16 = {\n  key: 1,\n  class: \"ml-1\"\n};\nconst _hoisted_17 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_18 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_19 = {\n  class: \"flex-grow\"\n};\nconst _hoisted_20 = [\"type\", \"value\"];\nconst _hoisted_21 = [\"onClick\"];\nconst _hoisted_22 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_23 = {\n  class: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\"\n};\nconst _hoisted_24 = [\"onClick\"];\nconst _hoisted_25 = {\n  class: \"form-group\"\n};\nconst _hoisted_26 = {\n  class: \"form-label\"\n};\nconst _hoisted_27 = {\n  class: \"form-group\"\n};\nconst _hoisted_28 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_29 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_30 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_31 = {\n  key: 0\n};\nconst _hoisted_32 = {\n  class: \"form-group\"\n};\nconst _hoisted_33 = [\"value\"];\nconst _hoisted_34 = {\n  class: \"form-group\"\n};\nconst _hoisted_35 = {\n  class: \"flex\"\n};\nconst _hoisted_36 = {\n  key: 1\n};\nconst _hoisted_37 = {\n  class: \"form-group\"\n};\nconst _hoisted_38 = {\n  class: \"form-group\"\n};\nconst _hoisted_39 = {\n  key: 0,\n  class: \"text-red-500 text-xs mt-1\"\n};\nconst _hoisted_40 = {\n  class: \"form-group\"\n};\nconst _hoisted_41 = {\n  class: \"form-group\"\n};\nconst _hoisted_42 = {\n  class: \"mb-2\"\n};\nconst _hoisted_43 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_44 = {\n  class: \"form-text\"\n};\nconst _hoisted_45 = {\n  class: \"form-group\"\n};\nconst _hoisted_46 = [\"value\"];\nconst _hoisted_47 = {\n  class: \"form-group\"\n};\nconst _hoisted_48 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_49 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_50 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_51 = {\n  key: 0,\n  class: \"mt-3\"\n};\nconst _hoisted_52 = {\n  class: \"grid grid-cols-2 gap-4\"\n};\nconst _hoisted_53 = {\n  class: \"form-group\"\n};\nconst _hoisted_54 = {\n  class: \"form-group\"\n};\nconst _hoisted_55 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_56 = {\n  class: \"form-group\"\n};\nconst _hoisted_57 = [\"value\"];\nconst _hoisted_58 = {\n  class: \"form-group\"\n};\nconst _hoisted_59 = {\n  class: \"form-group\"\n};\nconst _hoisted_60 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_61 = {\n  class: \"form-group\"\n};\nconst _hoisted_62 = [\"value\"];\nconst _hoisted_63 = {\n  class: \"form-group\"\n};\nconst _hoisted_64 = {\n  class: \"form-group\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_CustomCheckbox = _resolveComponent(\"CustomCheckbox\");\n  const _component_StatusBadge = _resolveComponent(\"StatusBadge\");\n  const _component_PasswordStrengthMeter = _resolveComponent(\"PasswordStrengthMeter\");\n  const _component_BaseModal = _resolveComponent(\"BaseModal\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createCommentVNode(\" 操作按钮 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"button\", {\n    class: \"btn-outline\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.showEmergencyReset && $options.showEmergencyReset(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'exclamation-triangle'],\n    class: \"mr-2 text-red-600\"\n  }), _cache[33] || (_cache[33] = _createElementVNode(\"span\", null, \"紧急重置\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"btn-outline\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.openBatchUpdateModal && $options.openBatchUpdateModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'key'],\n    class: \"mr-2\"\n  }), _cache[34] || (_cache[34] = _createElementVNode(\"span\", null, \"批量更新密码\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"btn-outline\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.openBatchApplyModal && $options.openBatchApplyModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"mr-2\"\n  }), _cache[35] || (_cache[35] = _createElementVNode(\"span\", null, \"批量应用策略\", -1 /* HOISTED */))])]), _createCommentVNode(\" 主机列表 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"table\", _hoisted_3, [_createElementVNode(\"thead\", _hoisted_4, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", _hoisted_5, [_createVNode(_component_CustomCheckbox, {\n    modelValue: $data.selectAll,\n    \"onUpdate:modelValue\": [_cache[3] || (_cache[3] = $event => $data.selectAll = $event), $options.toggleSelectAll]\n  }, {\n    default: _withCtx(() => _cache[36] || (_cache[36] = [_createTextVNode(\" 主机名 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _cache[37] || (_cache[37] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" IP地址 \", -1 /* HOISTED */)), _cache[38] || (_cache[38] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 最后密码修改时间 \", -1 /* HOISTED */)), _cache[39] || (_cache[39] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码过期时间 \", -1 /* HOISTED */)), _cache[40] || (_cache[40] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码 \", -1 /* HOISTED */)), _cache[41] || (_cache[41] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 状态 \", -1 /* HOISTED */)), _cache[42] || (_cache[42] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 操作 \", -1 /* HOISTED */))])]), _createElementVNode(\"tbody\", _hoisted_6, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, (host, index) => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: host.id,\n      class: _normalizeClass({\n        'bg-gray-50': index % 2 === 0,\n        'hover:bg-blue-50': true\n      })\n    }, [_createElementVNode(\"td\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_CustomCheckbox, {\n      modelValue: host.selected,\n      \"onUpdate:modelValue\": $event => host.selected = $event\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"span\", _hoisted_9, _toDisplayString(host.name), 1 /* TEXT */)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])])]), _createElementVNode(\"td\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, _toDisplayString(host.ip), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, _toDisplayString(host.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_14, [_createElementVNode(\"div\", {\n      class: _normalizeClass({\n        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\n        'bg-red-100 text-red-800': $options.isPasswordExpired(host).status === 'danger' || $options.isPasswordExpired(host).status === 'expired',\n        'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(host).status === 'warning',\n        'bg-gray-100 text-gray-800': $options.isPasswordExpired(host).status === 'normal'\n      })\n    }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(host).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(host).status === 'expired' || $options.isPasswordExpired(host).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_15, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'exclamation-triangle']\n    })])) : $options.isPasswordExpired(host).status === 'warning' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_16, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'exclamation-circle']\n    })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]), _createElementVNode(\"td\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"input\", {\n      type: $data.passwordVisibility[host.id] ? 'text' : 'password',\n      value: host.password,\n      readonly: \"\",\n      class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n    }, null, 8 /* PROPS */, _hoisted_20)]), _createElementVNode(\"button\", {\n      onClick: $event => $options.togglePasswordVisibility(host.id),\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility[host.id] ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_21)])]), _createElementVNode(\"td\", _hoisted_22, [_createVNode(_component_StatusBadge, {\n      type: host.status\n    }, null, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"td\", _hoisted_23, [_createElementVNode(\"button\", {\n      class: \"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n      onClick: $event => $options.openChangePasswordModal(host)\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'key'],\n      class: \"mr-1\"\n    }), _cache[43] || (_cache[43] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_24)])], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */))])])]), _createCommentVNode(\" 修改密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.changePasswordModal.show,\n    \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.changePasswordModal.show = $event),\n    title: \"修改密码\",\n    \"confirm-text\": \"确认更新\",\n    onConfirm: $options.updatePassword,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"label\", _hoisted_26, \"服务器: \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_27, [_cache[46] || (_cache[46] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"label\", _hoisted_29, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.changePasswordModal.method = $event),\n      value: \"auto\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.changePasswordModal.method]]), _cache[44] || (_cache[44] = _createElementVNode(\"span\", null, \"自动生成\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_30, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.changePasswordModal.method = $event),\n      value: \"manual\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.changePasswordModal.method]]), _cache[45] || (_cache[45] = _createElementVNode(\"span\", null, \"手动输入\", -1 /* HOISTED */))])])]), $data.changePasswordModal.method === 'auto' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_cache[47] || (_cache[47] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.changePasswordModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_33);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.changePasswordModal.policyId]])]), _createElementVNode(\"div\", _hoisted_34, [_cache[48] || (_cache[48] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_35, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.changePasswordModal.generatedPassword = $event),\n      class: \"form-control rounded-r-none\",\n      readonly: \"\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.changePasswordModal.generatedPassword]]), _createElementVNode(\"button\", {\n      class: \"bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded-r-md\",\n      onClick: _cache[8] || (_cache[8] = (...args) => $options.generatePassword && $options.generatePassword(...args))\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt']\n    })])]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.generatedPassword\n    }, null, 8 /* PROPS */, [\"password\"])])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, [_cache[49] || (_cache[49] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"新密码\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"password\",\n      \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.changePasswordModal.newPassword = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.changePasswordModal.newPassword]]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.newPassword\n    }, null, 8 /* PROPS */, [\"password\"])]), _createElementVNode(\"div\", _hoisted_38, [_cache[50] || (_cache[50] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"确认密码\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"password\",\n      \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.changePasswordModal.confirmPassword = $event),\n      class: _normalizeClass([\"form-control\", {\n        'border-red-500': $options.passwordMismatch\n      }])\n    }, null, 2 /* CLASS */), [[_vModelText, $data.changePasswordModal.confirmPassword]]), $options.passwordMismatch ? (_openBlock(), _createElementBlock(\"div\", _hoisted_39, \" 两次输入的密码不一致 \")) : _createCommentVNode(\"v-if\", true)])])), _createElementVNode(\"div\", _hoisted_40, [_cache[54] || (_cache[54] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.executeImmediately,\n      \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $data.changePasswordModal.executeImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[51] || (_cache[51] = [_createTextVNode(\" 立即执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.saveHistory,\n      \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $data.changePasswordModal.saveHistory = $event)\n    }, {\n      default: _withCtx(() => _cache[52] || (_cache[52] = [_createTextVNode(\" 保存密码历史记录 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.logAudit,\n      \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.changePasswordModal.logAudit = $event)\n    }, {\n      default: _withCtx(() => _cache[53] || (_cache[53] = [_createTextVNode(\" 记录审计日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量更新密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchUpdateModal.show,\n    \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.batchUpdateModal.show = $event),\n    title: \"批量更新密码\",\n    \"confirm-text\": \"开始更新\",\n    size: \"lg\",\n    onConfirm: $options.batchUpdatePasswords,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_41, [_cache[56] || (_cache[56] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_42, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatch,\n      \"onUpdate:modelValue\": [_cache[15] || (_cache[15] = $event => $data.selectAllBatch = $event), $options.toggleSelectAllBatch]\n    }, {\n      default: _withCtx(() => _cache[55] || (_cache[55] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_43, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchUpdateModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchUpdateModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_44, \"已选择 \" + _toDisplayString($options.selectedHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_45, [_cache[57] || (_cache[57] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.batchUpdateModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_46);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchUpdateModal.policyId]])]), _createElementVNode(\"div\", _hoisted_47, [_cache[62] || (_cache[62] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_48, [_createElementVNode(\"label\", _hoisted_49, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"immediate\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[58] || (_cache[58] = _createElementVNode(\"span\", null, \"立即执行\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_50, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[18] || (_cache[18] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"scheduled\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[59] || (_cache[59] = _createElementVNode(\"span\", null, \"定时执行\", -1 /* HOISTED */))])]), $data.batchUpdateModal.executionTime === 'scheduled' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_51, [_createElementVNode(\"div\", _hoisted_52, [_createElementVNode(\"div\", null, [_cache[60] || (_cache[60] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"日期\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"date\",\n      \"onUpdate:modelValue\": _cache[19] || (_cache[19] = $event => $data.batchUpdateModal.scheduledDate = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledDate]])]), _createElementVNode(\"div\", null, [_cache[61] || (_cache[61] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"时间\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"time\",\n      \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $data.batchUpdateModal.scheduledTime = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledTime]])])])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_53, [_cache[66] || (_cache[66] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[21] || (_cache[21] = $event => $data.batchUpdateModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[63] || (_cache[63] = [_createTextVNode(\" 忽略错误继续执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.detailedLog,\n      \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $data.batchUpdateModal.detailedLog = $event)\n    }, {\n      default: _withCtx(() => _cache[64] || (_cache[64] = [_createTextVNode(\" 记录详细日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.sendNotification,\n      \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.batchUpdateModal.sendNotification = $event)\n    }, {\n      default: _withCtx(() => _cache[65] || (_cache[65] = [_createTextVNode(\" 执行完成后发送通知 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量应用策略弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchApplyModal.show,\n    \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $data.batchApplyModal.show = $event),\n    title: \"批量应用密码策略\",\n    \"confirm-text\": \"应用策略\",\n    onConfirm: $options.batchApplyPolicy,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_54, [_cache[67] || (_cache[67] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_55, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchApplyModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchApplyModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_56, [_cache[68] || (_cache[68] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.batchApplyModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name), 9 /* TEXT, PROPS */, _hoisted_57);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchApplyModal.policyId]])]), _createElementVNode(\"div\", _hoisted_58, [_cache[71] || (_cache[71] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.updateImmediately,\n      \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.batchApplyModal.updateImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[69] || (_cache[69] = [_createTextVNode(\" 立即更新密码以符合策略 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.applyOnNextUpdate,\n      \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.batchApplyModal.applyOnNextUpdate = $event)\n    }, {\n      default: _withCtx(() => _cache[70] || (_cache[70] = [_createTextVNode(\" 下次密码更新时应用 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 紧急重置密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.emergencyResetModal.show,\n    \"onUpdate:modelValue\": _cache[32] || (_cache[32] = $event => $data.emergencyResetModal.show = $event),\n    title: \"紧急密码重置\",\n    \"confirm-text\": \"立即重置\",\n    icon: \"exclamation-triangle\",\n    danger: \"\",\n    onConfirm: $options.emergencyReset,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_cache[77] || (_cache[77] = _createElementVNode(\"div\", {\n      class: \"bg-red-50 text-red-700 p-3 rounded-md mb-4\"\n    }, [_createElementVNode(\"p\", null, \"紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_59, [_cache[72] || (_cache[72] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_60, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.emergencyResetModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.emergencyResetModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_61, [_cache[73] || (_cache[73] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用紧急策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[29] || (_cache[29] = $event => $data.emergencyResetModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.emergencyPolicies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_62);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.policyId]])]), _createElementVNode(\"div\", _hoisted_63, [_cache[75] || (_cache[75] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"操作原因\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[30] || (_cache[30] = $event => $data.emergencyResetModal.reason = $event),\n      class: \"form-select\"\n    }, _cache[74] || (_cache[74] = [_createElementVNode(\"option\", {\n      value: \"security_incident\"\n    }, \"安全事件响应\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"password_leak\"\n    }, \"密码泄露\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"abnormal_access\"\n    }, \"异常访问\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"compliance\"\n    }, \"合规要求\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"other\"\n    }, \"其他原因\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.reason]])]), _createElementVNode(\"div\", _hoisted_64, [_cache[76] || (_cache[76] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"附加说明\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n      \"onUpdate:modelValue\": _cache[31] || (_cache[31] = $event => $data.emergencyResetModal.description = $event),\n      class: \"form-control\",\n      rows: \"2\",\n      placeholder: \"请输入重置原因详细说明\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.emergencyResetModal.description]])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"])]);\n}", "map": {"version": 3, "names": ["class", "scope", "key", "_createElementBlock", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "onClick", "_cache", "args", "$options", "showEmergencyReset", "_createVNode", "_component_font_awesome_icon", "icon", "openBatchUpdateModal", "openBatchApplyModal", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_component_CustomCheckbox", "modelValue", "$data", "selectAll", "$event", "toggleSelectAll", "default", "_withCtx", "_createTextVNode", "_", "_hoisted_6", "_Fragment", "_renderList", "_ctx", "hosts", "host", "index", "id", "_normalizeClass", "_hoisted_7", "_hoisted_8", "selected", "_hoisted_9", "_toDisplayString", "name", "_hoisted_10", "_hoisted_11", "ip", "_hoisted_12", "_hoisted_13", "lastPasswordChange", "_hoisted_14", "isPasswordExpired", "status", "text", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "type", "passwordVisibility", "value", "password", "readonly", "_hoisted_20", "togglePasswordVisibility", "_hoisted_21", "_hoisted_22", "_component_StatusBadge", "_hoisted_23", "openChangePasswordModal", "_hoisted_24", "_component_BaseModal", "changePasswordModal", "show", "title", "onConfirm", "updatePassword", "loading", "processing", "_hoisted_25", "_hoisted_26", "currentHost", "_hoisted_27", "_hoisted_28", "_hoisted_29", "method", "_hoisted_30", "_hoisted_31", "_hoisted_32", "policyId", "policies", "policy", "<PERSON><PERSON><PERSON><PERSON>", "expiryDays", "_hoisted_33", "_hoisted_34", "_hoisted_35", "generatedPassword", "generatePassword", "_component_PasswordStrengthMeter", "_hoisted_36", "_hoisted_37", "newPassword", "_hoisted_38", "confirmPassword", "passwordMismatch", "_hoisted_39", "_hoisted_40", "executeImmediately", "saveHistory", "logAudit", "batchUpdateModal", "size", "batchUpdatePasswords", "_hoisted_41", "_hoisted_42", "selectAllBatch", "toggleSelectAllBatch", "_hoisted_43", "_createBlock", "selectedHosts", "_hoisted_44", "selectedHostsCount", "_hoisted_45", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "executionTime", "_hoisted_50", "_hoisted_51", "_hoisted_52", "scheduledDate", "scheduledTime", "_hoisted_53", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "batchApplyPolicy", "_hoisted_54", "_hoisted_55", "selectedHostsList", "_hoisted_56", "_hoisted_57", "_hoisted_58", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "danger", "emergencyReset", "_hoisted_59", "_hoisted_60", "_hoisted_61", "emergencyPolicies", "_hoisted_62", "_hoisted_63", "reason", "_hoisted_64", "description", "rows", "placeholder"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"flex space-x-3 mb-6\">\r\n      <button class=\"btn-outline\" @click=\"showEmergencyReset\">\r\n        <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2 text-red-600\" />\r\n        <span>紧急重置</span>\r\n      </button>\r\n      <button class=\"btn-outline\" @click=\"openBatchUpdateModal\">\r\n        <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n        <span>批量更新密码</span>\r\n      </button>\r\n      <button class=\"btn-outline\" @click=\"openBatchApplyModal\">\r\n        <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n        <span>批量应用策略</span>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <div class=\"bg-white rounded-lg shadow overflow-hidden\">\r\n      <table class=\"min-w-full divide-y divide-gray-200\">\r\n        <thead class=\"bg-gray-50\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n                主机名\r\n              </CustomCheckbox>\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              IP地址\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              最后密码修改时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码过期时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              状态\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              操作\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"bg-white divide-y divide-gray-200\">\r\n          <tr v-for=\"(host, index) in hosts\" :key=\"host.id\"\r\n            :class=\"{ 'bg-gray-50': index % 2 === 0, 'hover:bg-blue-50': true }\">\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"flex items-center\">\r\n                <CustomCheckbox v-model=\"host.selected\">\r\n                  <span class=\"ml-2 font-medium text-gray-900\">{{ host.name }}</span>\r\n                </CustomCheckbox>\r\n              </div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"text-sm text-gray-900\">{{ host.ip }}</div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"text-sm text-gray-500\">{{ host.lastPasswordChange || '-' }}</div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div :class=\"{\r\n                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\r\n                'bg-red-100 text-red-800': isPasswordExpired(host).status === 'danger' || isPasswordExpired(host).status === 'expired',\r\n                'bg-yellow-100 text-yellow-800': isPasswordExpired(host).status === 'warning',\r\n                'bg-gray-100 text-gray-800': isPasswordExpired(host).status === 'normal'\r\n              }\">\r\n                {{ isPasswordExpired(host).text }}\r\n                <span v-if=\"isPasswordExpired(host).status === 'expired' || isPasswordExpired(host).status === 'danger'\"\r\n                  class=\"ml-1\">\r\n                  <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                </span>\r\n                <span v-else-if=\"isPasswordExpired(host).status === 'warning'\" class=\"ml-1\">\r\n                  <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" />\r\n                </span>\r\n              </div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"flex items-center\">\r\n                <div class=\"flex-grow\">\r\n                  <input :type=\"passwordVisibility[host.id] ? 'text' : 'password'\" :value=\"host.password\" readonly\r\n                    class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                </div>\r\n                <button @click=\"togglePasswordVisibility(host.id)\"\r\n                  class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                  <font-awesome-icon :icon=\"['fas', passwordVisibility[host.id] ? 'eye-slash' : 'eye']\"\r\n                    class=\"text-lg\" />\r\n                </button>\r\n              </div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <StatusBadge :type=\"host.status\" />\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n              <button\r\n                class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                @click=\"openChangePasswordModal(host)\">\r\n                <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                修改密码\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" confirm-text=\"确认更新\" @confirm=\"updatePassword\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">服务器: {{ currentHost.name }}</label>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"changePasswordModal.method\" value=\"auto\" class=\"mr-2\">\r\n            <span>自动生成</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"changePasswordModal.method\" value=\"manual\" class=\"mr-2\">\r\n            <span>手动输入</span>\r\n          </label>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-if=\"changePasswordModal.method === 'auto'\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">选择密码策略</label>\r\n          <select v-model=\"changePasswordModal.policyId\" class=\"form-select\">\r\n            <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n              {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n            </option>\r\n          </select>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <div class=\"flex\">\r\n            <input type=\"text\" v-model=\"changePasswordModal.generatedPassword\" class=\"form-control rounded-r-none\"\r\n              readonly>\r\n            <button class=\"bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded-r-md\" @click=\"generatePassword\">\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" />\r\n            </button>\r\n          </div>\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.generatedPassword\" />\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else>\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <input type=\"password\" v-model=\"changePasswordModal.newPassword\" class=\"form-control\">\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <input type=\"password\" v-model=\"changePasswordModal.confirmPassword\" class=\"form-control\"\r\n            :class=\"{ 'border-red-500': passwordMismatch }\">\r\n          <div v-if=\"passwordMismatch\" class=\"text-red-500 text-xs mt-1\">\r\n            两次输入的密码不一致\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行选项</label>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          立即执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          保存密码历史记录\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          记录审计日志\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      passwordVisibility: {},\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host) {\r\n      this.currentHost = host\r\n      this.changePasswordModal.show = true\r\n      this.changePasswordModal.generatedPassword = this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword(policy) {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policyObj ? policyObj.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      if (this.changePasswordModal && !policy) {\r\n        this.changePasswordModal.generatedPassword = password\r\n      }\r\n\r\n      return password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto'\r\n          ? this.changePasswordModal.generatedPassword\r\n          : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          password: password,\r\n          policyId: this.changePasswordModal.policyId\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取所选策略\r\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)\r\n\r\n        // 为每台主机生成并更新密码\r\n        await Promise.all(\r\n          selectedHostIds.map(async (hostId) => {\r\n            const newPassword = this.generatePassword(policy)\r\n            return this.$store.dispatch('updateHostPassword', {\r\n              hostId: hostId,\r\n              password: newPassword,\r\n              policyId: policy.id\r\n            })\r\n          })\r\n        )\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取紧急策略\r\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)\r\n\r\n        // 为每台主机生成并更新密码\r\n        await Promise.all(\r\n          selectedHostIds.map(async (hostId) => {\r\n            const newPassword = this.generatePassword(policy)\r\n            return this.$store.dispatch('updateHostPassword', {\r\n              hostId: hostId,\r\n              password: newPassword,\r\n              policyId: policy.id\r\n            })\r\n          })\r\n        )\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    togglePasswordVisibility(hostId) {\r\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]\r\n    },\r\n\r\n    isPasswordExpired(host) {\r\n      if (!host.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }\r\n\r\n      // 解析过期时间\r\n      const expiryDate = new Date(host.passwordExpiryDate)\r\n      const now = new Date()\r\n\r\n      // 如果已过期\r\n      if (expiryDate < now) {\r\n        return {\r\n          status: 'expired',\r\n          days: 0,\r\n          text: '已过期'\r\n        }\r\n      }\r\n\r\n      // 计算剩余天数和小时数\r\n      const diffTime = expiryDate - now\r\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))\r\n      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n\r\n      // 根据剩余时间确定状态\r\n      let status = 'normal'\r\n      if (diffDays < 7) {\r\n        status = 'danger'  // 少于7天\r\n      } else if (diffDays < 14) {\r\n        status = 'warning' // 少于14天\r\n      }\r\n\r\n      // 格式化显示文本\r\n      let text = ''\r\n      if (diffDays > 0) {\r\n        text += `${diffDays}天`\r\n      }\r\n      if (diffHours > 0 || diffDays === 0) {\r\n        text += `${diffHours}小时`\r\n      }\r\n\r\n      return { status, days: diffDays, text: `剩余${text}` }\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": ";;EAGSA,KAAK,EAAC;AAAqB;;EAgB3BA,KAAK,EAAC;AAA4C;;EAC9CA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAAY;;EAEjBC,KAAK,EAAC,KAAK;EAACD,KAAK,EAAC;;;EAyBnBA,KAAK,EAAC;AAAmC;;EAGxCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EAEpBA,KAAK,EAAC;AAAgC;;EAI9CA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAhEnDE,GAAA;EAyEkBF,KAAK,EAAC;;;EAzExBE,GAAA;EA4E+EF,KAAK,EAAC;;;EAKrEA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAW;oBAnFtC;oBAAA;;EA8FgBA,KAAK,EAAC;AAA6B;;EAGnCA,KAAK,EAAC;AAAmD;oBAjGzE;;EAiHWA,KAAK,EAAC;AAAY;;EACdA,KAAK,EAAC;AAAY;;EAGtBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EA5H1CE,GAAA;AAAA;;EAoIaF,KAAK,EAAC;AAAY;oBApI/B;;EA6IaA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EA/I3BE,GAAA;AAAA;;EA2JaF,KAAK,EAAC;AAAY;;EAMlBA,KAAK,EAAC;AAAY;;EAjK/BE,GAAA;EAqKuCF,KAAK,EAAC;;;EAMlCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAY;oBA3M7B;;EAoNWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EA3N1CE,GAAA;EAiOmEF,KAAK,EAAC;;;EAC1DA,KAAK,EAAC;AAAwB;;EAalCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;oBA1Q7B;;EAmRWA,KAAK,EAAC;AAAY;;EAkBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;oBA/S7B;;EAwTWA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;;;;;;uBAlU3BG,mBAAA,CAwUM,cAvUJC,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNC,UAaM,GAZJD,mBAAA,CAGS;IAHDL,KAAK,EAAC,aAAa;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,kBAAA,IAAAD,QAAA,CAAAC,kBAAA,IAAAF,IAAA,CAAkB;MACpDG,YAAA,CAAuFC,4BAAA;IAAnEC,IAAI,EAAE,+BAA+B;IAAEd,KAAK,EAAC;kCACjEK,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGS;IAHDL,KAAK,EAAC,aAAa;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAK,oBAAA,IAAAL,QAAA,CAAAK,oBAAA,IAAAN,IAAA,CAAoB;MACtDG,YAAA,CAAyDC,4BAAA;IAArCC,IAAI,EAAE,cAAc;IAAEd,KAAK,EAAC;kCAChDK,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAGS;IAHDL,KAAK,EAAC,aAAa;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAM,mBAAA,IAAAN,QAAA,CAAAM,mBAAA,IAAAP,IAAA,CAAmB;MACrDG,YAAA,CAAgEC,4BAAA;IAA5CC,IAAI,EAAE,qBAAqB;IAAEd,KAAK,EAAC;kCACvDK,mBAAA,CAAmB,cAAb,QAAM,qB,KAIhBD,mBAAA,UAAa,EACbC,mBAAA,CAyFM,OAzFNY,UAyFM,GAxFJZ,mBAAA,CAuFQ,SAvFRa,UAuFQ,GAtFNb,mBAAA,CA0BQ,SA1BRc,UA0BQ,GAzBNd,mBAAA,CAwBK,aAvBHA,mBAAA,CAIK,MAJLe,UAIK,GAHHR,YAAA,CAEiBS,yBAAA;IA1B/BC,UAAA,EAwBuCC,KAAA,CAAAC,SAAS;IAxBhD,wB,oCAwBuCD,KAAA,CAAAC,SAAS,GAAAC,MAAA,GAAsBf,QAAA,CAAAgB,eAAe;;IAxBrFC,OAAA,EAAAC,QAAA,CAwBuF,MAEzEpB,MAAA,SAAAA,MAAA,QA1BdqB,gBAAA,CAwBuF,OAEzE,E;IA1BdC,CAAA;0FA4BYzB,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACD,KAAK,EAAC;KAAiF,QAEvG,sB,4BACAK,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACD,KAAK,EAAC;KAAiF,YAEvG,sB,4BACAK,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACD,KAAK,EAAC;KAAiF,UAEvG,sB,4BACAK,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACD,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAK,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACD,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAK,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACD,KAAK,EAAC;KAAiF,MAEvG,qB,KAGJK,mBAAA,CA0DQ,SA1DR0B,UA0DQ,I,kBAzDN5B,mBAAA,CAwDK6B,SAAA,QAzGfC,WAAA,CAiDsCC,IAAA,CAAAC,KAAK,EAjD3C,CAiDsBC,IAAI,EAAEC,KAAK;yBAAvBlC,mBAAA,CAwDK;MAxD+BD,GAAG,EAAEkC,IAAI,CAACE,EAAE;MAC7CtC,KAAK,EAlDlBuC,eAAA;QAAA,cAkDoCF,KAAK;QAAA;MAAA;QAC7BhC,mBAAA,CAMK,MANLmC,UAMK,GALHnC,mBAAA,CAIM,OAJNoC,UAIM,GAHJ7B,YAAA,CAEiBS,yBAAA;MAvDjCC,UAAA,EAqDyCc,IAAI,CAACM,QAAQ;MArDtD,uBAAAjB,MAAA,IAqDyCW,IAAI,CAACM,QAAQ,GAAAjB;;MArDtDE,OAAA,EAAAC,QAAA,CAsDkB,MAAmE,CAAnEvB,mBAAA,CAAmE,QAAnEsC,UAAmE,EAAAC,gBAAA,CAAnBR,IAAI,CAACS,IAAI,iB;MAtD3Ef,CAAA;oFA0DYzB,mBAAA,CAEK,MAFLyC,WAEK,GADHzC,mBAAA,CAAsD,OAAtD0C,WAAsD,EAAAH,gBAAA,CAAhBR,IAAI,CAACY,EAAE,iB,GAE/C3C,mBAAA,CAEK,MAFL4C,WAEK,GADH5C,mBAAA,CAA6E,OAA7E6C,WAA6E,EAAAN,gBAAA,CAAvCR,IAAI,CAACe,kBAAkB,wB,GAE/D9C,mBAAA,CAgBK,MAhBL+C,WAgBK,GAfH/C,mBAAA,CAcM;MAdAL,KAAK,EAjEzBuC,eAAA;;mCAiE2K7B,QAAA,CAAA2C,iBAAiB,CAACjB,IAAI,EAAEkB,MAAM,iBAAiB5C,QAAA,CAAA2C,iBAAiB,CAACjB,IAAI,EAAEkB,MAAM;yCAAkE5C,QAAA,CAAA2C,iBAAiB,CAACjB,IAAI,EAAEkB,MAAM;qCAA8D5C,QAAA,CAAA2C,iBAAiB,CAACjB,IAAI,EAAEkB,MAAM;;QAjEpbzB,gBAAA,CAAAe,gBAAA,CAuEmBlC,QAAA,CAAA2C,iBAAiB,CAACjB,IAAI,EAAEmB,IAAI,IAAG,GAClC,iBAAY7C,QAAA,CAAA2C,iBAAiB,CAACjB,IAAI,EAAEkB,MAAM,kBAAkB5C,QAAA,CAAA2C,iBAAiB,CAACjB,IAAI,EAAEkB,MAAM,iB,cAA1FnD,mBAAA,CAGO,QAHPqD,WAGO,GADL5C,YAAA,CAA6DC,4BAAA;MAAzCC,IAAI,EAAE;IAA+B,G,KAE1CJ,QAAA,CAAA2C,iBAAiB,CAACjB,IAAI,EAAEkB,MAAM,kB,cAA/CnD,mBAAA,CAEO,QAFPsD,WAEO,GADL7C,YAAA,CAA2DC,4BAAA;MAAvCC,IAAI,EAAE;IAA6B,G,KA7EzEV,mBAAA,e,oBAiFYC,mBAAA,CAYK,MAZLqD,WAYK,GAXHrD,mBAAA,CAUM,OAVNsD,WAUM,GATJtD,mBAAA,CAGM,OAHNuD,WAGM,GAFJvD,mBAAA,CACkG;MAD1FwD,IAAI,EAAEtC,KAAA,CAAAuC,kBAAkB,CAAC1B,IAAI,CAACE,EAAE;MAA0ByB,KAAK,EAAE3B,IAAI,CAAC4B,QAAQ;MAAEC,QAAQ,EAAR,EAAQ;MAC9FjE,KAAK,EAAC;4BArF1BkE,WAAA,E,GAuFgB7D,mBAAA,CAIS;MAJAE,OAAK,EAAAkB,MAAA,IAAEf,QAAA,CAAAyD,wBAAwB,CAAC/B,IAAI,CAACE,EAAE;MAC9CtC,KAAK,EAAC;QACNY,YAAA,CACoBC,4BAAA;MADAC,IAAI,UAAUS,KAAA,CAAAuC,kBAAkB,CAAC1B,IAAI,CAACE,EAAE;MAC1DtC,KAAK,EAAC;uDA1F1BoE,WAAA,E,KA8FY/D,mBAAA,CAEK,MAFLgE,WAEK,GADHzD,YAAA,CAAmC0D,sBAAA;MAArBT,IAAI,EAAEzB,IAAI,CAACkB;yCAE3BjD,mBAAA,CAOK,MAPLkE,WAOK,GANHlE,mBAAA,CAKS;MAJPL,KAAK,EAAC,0NAA0N;MAC/NO,OAAK,EAAAkB,MAAA,IAAEf,QAAA,CAAA8D,uBAAuB,CAACpC,IAAI;QACpCxB,YAAA,CAAyDC,4BAAA;MAArCC,IAAI,EAAE,cAAc;MAAEd,KAAK,EAAC;oCArGhE6B,gBAAA,CAqGyE,QAE3D,G,iBAvGd4C,WAAA,E;wCA8GIrE,mBAAA,YAAe,EACfQ,YAAA,CAwEY8D,oBAAA;IAvLhBpD,UAAA,EA+GwBC,KAAA,CAAAoD,mBAAmB,CAACC,IAAI;IA/GhD,uBAAApE,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IA+GwBF,KAAA,CAAAoD,mBAAmB,CAACC,IAAI,GAAAnD,MAAA;IAAEoD,KAAK,EAAC,MAAM;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAEpE,QAAA,CAAAqE,cAAc;IACpGC,OAAO,EAAEzD,KAAA,CAAA0D;;IAhHhBtD,OAAA,EAAAC,QAAA,CAiHM,MAEM,CAFNvB,mBAAA,CAEM,OAFN6E,WAEM,GADJ7E,mBAAA,CAA6D,SAA7D8E,WAA6D,EAAnC,OAAK,GAAAvC,gBAAA,CAAGrB,KAAA,CAAA6D,WAAW,CAACvC,IAAI,iB,GAGpDxC,mBAAA,CAYM,OAZNgF,WAYM,G,4BAXJhF,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CASM,OATNiF,WASM,GARJjF,mBAAA,CAGQ,SAHRkF,WAGQ,G,gBAFNlF,mBAAA,CAAmF;MAA5EwD,IAAI,EAAC,OAAO;MAzH/B,uBAAArD,MAAA,QAAAA,MAAA,MAAAiB,MAAA,IAyHyCF,KAAA,CAAAoD,mBAAmB,CAACa,MAAM,GAAA/D,MAAA;MAAEsC,KAAK,EAAC,MAAM;MAAC/D,KAAK,EAAC;oDAA/CuB,KAAA,CAAAoD,mBAAmB,CAACa,MAAM,E,+BACvDnF,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHRoF,WAGQ,G,gBAFNpF,mBAAA,CAAqF;MAA9EwD,IAAI,EAAC,OAAO;MA7H/B,uBAAArD,MAAA,QAAAA,MAAA,MAAAiB,MAAA,IA6HyCF,KAAA,CAAAoD,mBAAmB,CAACa,MAAM,GAAA/D,MAAA;MAAEsC,KAAK,EAAC,QAAQ;MAAC/D,KAAK,EAAC;oDAAjDuB,KAAA,CAAAoD,mBAAmB,CAACa,MAAM,E,+BACvDnF,mBAAA,CAAiB,cAAX,MAAI,qB,OAKLkB,KAAA,CAAAoD,mBAAmB,CAACa,MAAM,e,cAArCrF,mBAAA,CAqBM,OAxJZuF,WAAA,GAoIQrF,mBAAA,CAOM,OAPNsF,WAOM,G,4BANJtF,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCK,mBAAA,CAIS;MA1InB,uBAAAG,MAAA,QAAAA,MAAA,MAAAiB,MAAA,IAsI2BF,KAAA,CAAAoD,mBAAmB,CAACiB,QAAQ,GAAAnE,MAAA;MAAEzB,KAAK,EAAC;2BACnDG,mBAAA,CAES6B,SAAA,QAzIrBC,WAAA,CAuIqCC,IAAA,CAAA2D,QAAQ,EAAlBC,MAAM;2BAArB3F,mBAAA,CAES;QAF2BD,GAAG,EAAE4F,MAAM,CAACxD,EAAE;QAAGyB,KAAK,EAAE+B,MAAM,CAACxD;0BAC9DwD,MAAM,CAACjD,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAGkD,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAnD,gBAAA,CAAGkD,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAzIZC,WAAA;6EAsI2B1E,KAAA,CAAAoD,mBAAmB,CAACiB,QAAQ,E,KAO/CvF,mBAAA,CAUM,OAVN6F,WAUM,G,4BATJ7F,mBAAA,CAAuC;MAAhCL,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BK,mBAAA,CAMM,OANN8F,WAMM,G,gBALJ9F,mBAAA,CACW;MADJwD,IAAI,EAAC,MAAM;MAhJ9B,uBAAArD,MAAA,QAAAA,MAAA,MAAAiB,MAAA,IAgJwCF,KAAA,CAAAoD,mBAAmB,CAACyB,iBAAiB,GAAA3E,MAAA;MAAEzB,KAAK,EAAC,6BAA6B;MACpGiE,QAAQ,EAAR;mDAD0B1C,KAAA,CAAAoD,mBAAmB,CAACyB,iBAAiB,E,GAEjE/F,mBAAA,CAES;MAFDL,KAAK,EAAC,sDAAsD;MAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAA2F,gBAAA,IAAA3F,QAAA,CAAA2F,gBAAA,IAAA5F,IAAA,CAAgB;QAC3FG,YAAA,CAAiDC,4BAAA;MAA7BC,IAAI,EAAE;IAAmB,G,KAGjDF,YAAA,CAA2E0F,gCAAA;MAAnDtC,QAAQ,EAAEzC,KAAA,CAAAoD,mBAAmB,CAACyB;gEAI1DjG,mBAAA,CAeM,OAzKZoG,WAAA,GA2JQlG,mBAAA,CAIM,OAJNmG,WAIM,G,4BAHJnG,mBAAA,CAAqC;MAA9BL,KAAK,EAAC;IAAY,GAAC,KAAG,sB,gBAC7BK,mBAAA,CAAsF;MAA/EwD,IAAI,EAAC,UAAU;MA7JhC,uBAAArD,MAAA,QAAAA,MAAA,MAAAiB,MAAA,IA6J0CF,KAAA,CAAAoD,mBAAmB,CAAC8B,WAAW,GAAAhF,MAAA;MAAEzB,KAAK,EAAC;mDAAvCuB,KAAA,CAAAoD,mBAAmB,CAAC8B,WAAW,E,GAC/D7F,YAAA,CAAqE0F,gCAAA;MAA7CtC,QAAQ,EAAEzC,KAAA,CAAAoD,mBAAmB,CAAC8B;6CAGxDpG,mBAAA,CAOM,OAPNqG,WAOM,G,4BANJrG,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CACkD;MAD3CwD,IAAI,EAAC,UAAU;MAnKhC,uBAAArD,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IAmK0CF,KAAA,CAAAoD,mBAAmB,CAACgC,eAAe,GAAAlF,MAAA;MAAEzB,KAAK,EAnKpFuC,eAAA,EAmKqF,cAAc;QAAA,kBAC3D7B,QAAA,CAAAkG;MAAgB;4CADdrF,KAAA,CAAAoD,mBAAmB,CAACgC,eAAe,E,GAExDjG,QAAA,CAAAkG,gBAAgB,I,cAA3BzG,mBAAA,CAEM,OAFN0G,WAEM,EAFyD,cAE/D,KAvKVzG,mBAAA,e,MA2KMC,mBAAA,CAWM,OAXNyG,WAWM,G,4BAVJzG,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BY,YAAA,CAEiBS,yBAAA;MA/KzBC,UAAA,EA6KiCC,KAAA,CAAAoD,mBAAmB,CAACoC,kBAAkB;MA7KvE,uBAAAvG,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IA6KiCF,KAAA,CAAAoD,mBAAmB,CAACoC,kBAAkB,GAAAtF,MAAA;;MA7KvEE,OAAA,EAAAC,QAAA,CA6KyE,MAEjEpB,MAAA,SAAAA,MAAA,QA/KRqB,gBAAA,CA6KyE,QAEjE,E;MA/KRC,CAAA;uCAgLQlB,YAAA,CAEiBS,yBAAA;MAlLzBC,UAAA,EAgLiCC,KAAA,CAAAoD,mBAAmB,CAACqC,WAAW;MAhLhE,uBAAAxG,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IAgLiCF,KAAA,CAAAoD,mBAAmB,CAACqC,WAAW,GAAAvF,MAAA;;MAhLhEE,OAAA,EAAAC,QAAA,CAgLkE,MAE1DpB,MAAA,SAAAA,MAAA,QAlLRqB,gBAAA,CAgLkE,YAE1D,E;MAlLRC,CAAA;uCAmLQlB,YAAA,CAEiBS,yBAAA;MArLzBC,UAAA,EAmLiCC,KAAA,CAAAoD,mBAAmB,CAACsC,QAAQ;MAnL7D,uBAAAzG,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IAmLiCF,KAAA,CAAAoD,mBAAmB,CAACsC,QAAQ,GAAAxF,MAAA;;MAnL7DE,OAAA,EAAAC,QAAA,CAmL+D,MAEvDpB,MAAA,SAAAA,MAAA,QArLRqB,gBAAA,CAmL+D,UAEvD,E;MArLRC,CAAA;;IAAAA,CAAA;6DAyLI1B,mBAAA,cAAiB,EACjBQ,YAAA,CAiEY8D,oBAAA;IA3PhBpD,UAAA,EA0LwBC,KAAA,CAAA2F,gBAAgB,CAACtC,IAAI;IA1L7C,uBAAApE,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IA0LwBF,KAAA,CAAA2F,gBAAgB,CAACtC,IAAI,GAAAnD,MAAA;IAAEoD,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAACsC,IAAI,EAAC,IAAI;IACpFrC,SAAO,EAAEpE,QAAA,CAAA0G,oBAAoB;IAAGpC,OAAO,EAAEzD,KAAA,CAAA0D;;IA3LhDtD,OAAA,EAAAC,QAAA,CA4LM,MAaM,CAbNvB,mBAAA,CAaM,OAbNgH,WAaM,G,4BAZJhH,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CAIM,OAJNiH,WAIM,GAHJ1G,YAAA,CAEiBS,yBAAA;MAjM3BC,UAAA,EA+LmCC,KAAA,CAAAgG,cAAc;MA/LjD,wB,sCA+LmChG,KAAA,CAAAgG,cAAc,GAAA9F,MAAA,GAAsBf,QAAA,CAAA8G,oBAAoB;;MA/L3F7F,OAAA,EAAAC,QAAA,CA+L6F,MAEnFpB,MAAA,SAAAA,MAAA,QAjMVqB,gBAAA,CA+L6F,MAEnF,E;MAjMVC,CAAA;gEAmMQzB,mBAAA,CAIM,OAJNoH,WAIM,I,kBAHJtH,mBAAA,CAEiB6B,SAAA,QAtM3BC,WAAA,CAoMyCC,IAAA,CAAAC,KAAK,EAAbC,IAAI;2BAA3BsF,YAAA,CAEiBrG,yBAAA;QAFsBnB,GAAG,EAAEkC,IAAI,CAACE,EAAE;QApM7DhB,UAAA,EAoMwEC,KAAA,CAAA2F,gBAAgB,CAACS,aAAa,CAACvF,IAAI,CAACE,EAAE;QApM9G,uBAAAb,MAAA,IAoMwEF,KAAA,CAAA2F,gBAAgB,CAACS,aAAa,CAACvF,IAAI,CAACE,EAAE,IAAAb;;QApM9GE,OAAA,EAAAC,QAAA,CAqMY,MAAe,CArM3BC,gBAAA,CAAAe,gBAAA,CAqMeR,IAAI,CAACS,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGR,IAAI,CAACY,EAAE,IAAG,IAChC,gB;QAtMVlB,CAAA;;sCAwMQzB,mBAAA,CAAyD,KAAzDuH,WAAyD,EAApC,MAAI,GAAAhF,gBAAA,CAAGlC,QAAA,CAAAmH,kBAAkB,IAAG,MAAI,gB,GAGvDxH,mBAAA,CAOM,OAPNyH,WAOM,G,4BANJzH,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CAIS;MAjNjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IA6MyBF,KAAA,CAAA2F,gBAAgB,CAACtB,QAAQ,GAAAnE,MAAA;MAAEzB,KAAK,EAAC;2BAChDG,mBAAA,CAES6B,SAAA,QAhNnBC,WAAA,CA8MmCC,IAAA,CAAA2D,QAAQ,EAAlBC,MAAM;2BAArB3F,mBAAA,CAES;QAF2BD,GAAG,EAAE4F,MAAM,CAACxD,EAAE;QAAGyB,KAAK,EAAE+B,MAAM,CAACxD;0BAC9DwD,MAAM,CAACjD,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAGkD,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAnD,gBAAA,CAAGkD,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAhNV+B,WAAA;6EA6MyBxG,KAAA,CAAA2F,gBAAgB,CAACtB,QAAQ,E,KAO5CvF,mBAAA,CAyBM,OAzBN2H,WAyBM,G,4BAxBJ3H,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BK,mBAAA,CASM,OATN4H,WASM,GARJ5H,mBAAA,CAGQ,SAHR6H,WAGQ,G,gBAFN7H,mBAAA,CAA4F;MAArFwD,IAAI,EAAC,OAAO;MAxN/B,uBAAArD,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IAwNyCF,KAAA,CAAA2F,gBAAgB,CAACiB,aAAa,GAAA1G,MAAA;MAAEsC,KAAK,EAAC,WAAW;MAAC/D,KAAK,EAAC;oDAAxDuB,KAAA,CAAA2F,gBAAgB,CAACiB,aAAa,E,+BAC3D9H,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHR+H,WAGQ,G,gBAFN/H,mBAAA,CAA4F;MAArFwD,IAAI,EAAC,OAAO;MA5N/B,uBAAArD,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IA4NyCF,KAAA,CAAA2F,gBAAgB,CAACiB,aAAa,GAAA1G,MAAA;MAAEsC,KAAK,EAAC,WAAW;MAAC/D,KAAK,EAAC;oDAAxDuB,KAAA,CAAA2F,gBAAgB,CAACiB,aAAa,E,+BAC3D9H,mBAAA,CAAiB,cAAX,MAAI,qB,KAIHkB,KAAA,CAAA2F,gBAAgB,CAACiB,aAAa,oB,cAAzChI,mBAAA,CAWM,OAXNkI,WAWM,GAVJhI,mBAAA,CASM,OATNiI,WASM,GARJjI,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAoC;MAA7BL,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BK,mBAAA,CAAiF;MAA1EwD,IAAI,EAAC,MAAM;MArOhC,uBAAArD,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IAqO0CF,KAAA,CAAA2F,gBAAgB,CAACqB,aAAa,GAAA9G,MAAA;MAAEzB,KAAK,EAAC;mDAAtCuB,KAAA,CAAA2F,gBAAgB,CAACqB,aAAa,E,KAE5DlI,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAoC;MAA7BL,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BK,mBAAA,CAAiF;MAA1EwD,IAAI,EAAC,MAAM;MAzOhC,uBAAArD,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IAyO0CF,KAAA,CAAA2F,gBAAgB,CAACsB,aAAa,GAAA/G,MAAA;MAAEzB,KAAK,EAAC;mDAAtCuB,KAAA,CAAA2F,gBAAgB,CAACsB,aAAa,E,WAzOxEpI,mBAAA,e,GA+OMC,mBAAA,CAWM,OAXNoI,WAWM,G,4BAVJpI,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BY,YAAA,CAEiBS,yBAAA;MAnPzBC,UAAA,EAiPiCC,KAAA,CAAA2F,gBAAgB,CAACwB,YAAY;MAjP9D,uBAAAlI,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IAiPiCF,KAAA,CAAA2F,gBAAgB,CAACwB,YAAY,GAAAjH,MAAA;;MAjP9DE,OAAA,EAAAC,QAAA,CAiPgE,MAExDpB,MAAA,SAAAA,MAAA,QAnPRqB,gBAAA,CAiPgE,YAExD,E;MAnPRC,CAAA;uCAoPQlB,YAAA,CAEiBS,yBAAA;MAtPzBC,UAAA,EAoPiCC,KAAA,CAAA2F,gBAAgB,CAACyB,WAAW;MApP7D,uBAAAnI,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IAoPiCF,KAAA,CAAA2F,gBAAgB,CAACyB,WAAW,GAAAlH,MAAA;;MApP7DE,OAAA,EAAAC,QAAA,CAoP+D,MAEvDpB,MAAA,SAAAA,MAAA,QAtPRqB,gBAAA,CAoP+D,UAEvD,E;MAtPRC,CAAA;uCAuPQlB,YAAA,CAEiBS,yBAAA;MAzPzBC,UAAA,EAuPiCC,KAAA,CAAA2F,gBAAgB,CAAC0B,gBAAgB;MAvPlE,uBAAApI,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IAuPiCF,KAAA,CAAA2F,gBAAgB,CAAC0B,gBAAgB,GAAAnH,MAAA;;MAvPlEE,OAAA,EAAAC,QAAA,CAuPoE,MAE5DpB,MAAA,SAAAA,MAAA,QAzPRqB,gBAAA,CAuPoE,aAE5D,E;MAzPRC,CAAA;;IAAAA,CAAA;6DA6PI1B,mBAAA,cAAiB,EACjBQ,YAAA,CA8BY8D,oBAAA;IA5RhBpD,UAAA,EA8PwBC,KAAA,CAAAsH,eAAe,CAACjE,IAAI;IA9P5C,uBAAApE,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IA8PwBF,KAAA,CAAAsH,eAAe,CAACjE,IAAI,GAAAnD,MAAA;IAAEoD,KAAK,EAAC,UAAU;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAEpE,QAAA,CAAAoI,gBAAgB;IACtG9D,OAAO,EAAEzD,KAAA,CAAA0D;;IA/PhBtD,OAAA,EAAAC,QAAA,CAgQM,MAQM,CARNvB,mBAAA,CAQM,OARN0I,WAQM,G,4BAPJ1I,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CAKM,OALN2I,WAKM,I,kBAJJ7I,mBAAA,CAGiB6B,SAAA,QAtQ3BC,WAAA,CAmQyCvB,QAAA,CAAAuI,iBAAiB,EAAzB7G,IAAI;2BAA3BsF,YAAA,CAGiBrG,yBAAA;QAHkCnB,GAAG,EAAEkC,IAAI,CAACE,EAAE;QAnQzEhB,UAAA,EAoQqBC,KAAA,CAAAsH,eAAe,CAAClB,aAAa,CAACvF,IAAI,CAACE,EAAE;QApQ1D,uBAAAb,MAAA,IAoQqBF,KAAA,CAAAsH,eAAe,CAAClB,aAAa,CAACvF,IAAI,CAACE,EAAE,IAAAb;;QApQ1DE,OAAA,EAAAC,QAAA,CAqQY,MAAe,CArQ3BC,gBAAA,CAAAe,gBAAA,CAqQeR,IAAI,CAACS,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGR,IAAI,CAACY,EAAE,IAAG,IAChC,gB;QAtQVlB,CAAA;;wCA0QMzB,mBAAA,CAOM,OAPN6I,WAOM,G,4BANJ7I,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCK,mBAAA,CAIS;MAhRjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IA4QyBF,KAAA,CAAAsH,eAAe,CAACjD,QAAQ,GAAAnE,MAAA;MAAEzB,KAAK,EAAC;2BAC/CG,mBAAA,CAES6B,SAAA,QA/QnBC,WAAA,CA6QmCC,IAAA,CAAA2D,QAAQ,EAAlBC,MAAM;2BAArB3F,mBAAA,CAES;QAF2BD,GAAG,EAAE4F,MAAM,CAACxD,EAAE;QAAGyB,KAAK,EAAE+B,MAAM,CAACxD;0BAC9DwD,MAAM,CAACjD,IAAI,wBA9Q1BsG,WAAA;6EA4QyB5H,KAAA,CAAAsH,eAAe,CAACjD,QAAQ,E,KAO3CvF,mBAAA,CAQM,OARN+I,WAQM,G,4BAPJ/I,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BY,YAAA,CAEiBS,yBAAA;MAvRzBC,UAAA,EAqRiCC,KAAA,CAAAsH,eAAe,CAACQ,iBAAiB;MArRlE,uBAAA7I,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IAqRiCF,KAAA,CAAAsH,eAAe,CAACQ,iBAAiB,GAAA5H,MAAA;;MArRlEE,OAAA,EAAAC,QAAA,CAqRoE,MAE5DpB,MAAA,SAAAA,MAAA,QAvRRqB,gBAAA,CAqRoE,eAE5D,E;MAvRRC,CAAA;uCAwRQlB,YAAA,CAEiBS,yBAAA;MA1RzBC,UAAA,EAwRiCC,KAAA,CAAAsH,eAAe,CAACS,iBAAiB;MAxRlE,uBAAA9I,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IAwRiCF,KAAA,CAAAsH,eAAe,CAACS,iBAAiB,GAAA7H,MAAA;;MAxRlEE,OAAA,EAAAC,QAAA,CAwRoE,MAE5DpB,MAAA,SAAAA,MAAA,QA1RRqB,gBAAA,CAwRoE,aAE5D,E;MA1RRC,CAAA;;IAAAA,CAAA;6DA8RI1B,mBAAA,cAAiB,EACjBQ,YAAA,CAyCY8D,oBAAA;IAxUhBpD,UAAA,EA+RwBC,KAAA,CAAAgI,mBAAmB,CAAC3E,IAAI;IA/RhD,uBAAApE,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IA+RwBF,KAAA,CAAAgI,mBAAmB,CAAC3E,IAAI,GAAAnD,MAAA;IAAEoD,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAAC/D,IAAI,EAAC,sBAAsB;IAAC0I,MAAM,EAAN,EAAM;IAChH1E,SAAO,EAAEpE,QAAA,CAAA+I,cAAc;IAAGzE,OAAO,EAAEzD,KAAA,CAAA0D;;IAhS1CtD,OAAA,EAAAC,QAAA,CAiSM,MAEM,C,4BAFNvB,mBAAA,CAEM;MAFDL,KAAK,EAAC;IAA4C,IACrDK,mBAAA,CAA+C,WAA5C,0CAAwC,E,sBAG7CA,mBAAA,CAQM,OARNqJ,WAQM,G,4BAPJrJ,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CAKM,OALNsJ,WAKM,I,kBAJJxJ,mBAAA,CAGiB6B,SAAA,QA3S3BC,WAAA,CAwSyCvB,QAAA,CAAAuI,iBAAiB,EAAzB7G,IAAI;2BAA3BsF,YAAA,CAGiBrG,yBAAA;QAHkCnB,GAAG,EAAEkC,IAAI,CAACE,EAAE;QAxSzEhB,UAAA,EAySqBC,KAAA,CAAAgI,mBAAmB,CAAC5B,aAAa,CAACvF,IAAI,CAACE,EAAE;QAzS9D,uBAAAb,MAAA,IAySqBF,KAAA,CAAAgI,mBAAmB,CAAC5B,aAAa,CAACvF,IAAI,CAACE,EAAE,IAAAb;;QAzS9DE,OAAA,EAAAC,QAAA,CA0SY,MAAe,CA1S3BC,gBAAA,CAAAe,gBAAA,CA0SeR,IAAI,CAACS,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGR,IAAI,CAACY,EAAE,IAAG,IAChC,gB;QA3SVlB,CAAA;;wCA+SMzB,mBAAA,CAOM,OAPNuJ,WAOM,G,4BANJvJ,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCK,mBAAA,CAIS;MArTjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IAiTyBF,KAAA,CAAAgI,mBAAmB,CAAC3D,QAAQ,GAAAnE,MAAA;MAAEzB,KAAK,EAAC;2BACnDG,mBAAA,CAES6B,SAAA,QApTnBC,WAAA,CAkTmCvB,QAAA,CAAAmJ,iBAAiB,EAA3B/D,MAAM;2BAArB3F,mBAAA,CAES;QAFoCD,GAAG,EAAE4F,MAAM,CAACxD,EAAE;QAAGyB,KAAK,EAAE+B,MAAM,CAACxD;0BACvEwD,MAAM,CAACjD,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAGkD,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAnD,gBAAA,CAAGkD,MAAM,CAACE,UAAU,IAAG,KAC9E,uBApTV8D,WAAA;6EAiTyBvI,KAAA,CAAAgI,mBAAmB,CAAC3D,QAAQ,E,KAO/CvF,mBAAA,CASM,OATN0J,WASM,G,4BARJ1J,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CAMS;MAhUjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IA0TyBF,KAAA,CAAAgI,mBAAmB,CAACS,MAAM,GAAAvI,MAAA;MAAEzB,KAAK,EAAC;oCACjDK,mBAAA,CAAiD;MAAzC0D,KAAK,EAAC;IAAmB,GAAC,QAAM,qBACxC1D,mBAAA,CAA2C;MAAnC0D,KAAK,EAAC;IAAe,GAAC,MAAI,qBAClC1D,mBAAA,CAA6C;MAArC0D,KAAK,EAAC;IAAiB,GAAC,MAAI,qBACpC1D,mBAAA,CAAwC;MAAhC0D,KAAK,EAAC;IAAY,GAAC,MAAI,qBAC/B1D,mBAAA,CAAmC;MAA3B0D,KAAK,EAAC;IAAO,GAAC,MAAI,oB,2CALXxC,KAAA,CAAAgI,mBAAmB,CAACS,MAAM,E,KAS7C3J,mBAAA,CAIM,OAJN4J,WAIM,G,4BAHJ5J,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CACuC;MAtU/C,uBAAAG,MAAA,SAAAA,MAAA,OAAAiB,MAAA,IAqU2BF,KAAA,CAAAgI,mBAAmB,CAACW,WAAW,GAAAzI,MAAA;MAAEzB,KAAK,EAAC,cAAc;MAACmK,IAAI,EAAC,GAAG;MAC/EC,WAAW,EAAC;mDADK7I,KAAA,CAAAgI,mBAAmB,CAACW,WAAW,E;IArU1DpI,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}