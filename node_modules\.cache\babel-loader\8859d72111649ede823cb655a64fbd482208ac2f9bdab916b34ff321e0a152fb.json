{"ast": null, "code": "export default {\n  name: 'PasswordStrengthMeter',\n  props: {\n    password: {\n      type: String,\n      required: true\n    }\n  },\n  computed: {\n    score() {\n      if (!this.password) return 0;\n\n      // 简单评分算法，实际应用中可使用更复杂的算法\n      const length = this.password.length;\n      const hasLowerCase = /[a-z]/.test(this.password);\n      const hasUpperCase = /[A-Z]/.test(this.password);\n      const hasNumbers = /\\d/.test(this.password);\n      const hasSpecialChars = /[^A-Za-z0-9]/.test(this.password);\n      let score = 0;\n      if (length >= 8) score += 1;\n      if (length >= 12) score += 1;\n      if (hasLowerCase && hasUpperCase) score += 1;\n      if (hasNumbers) score += 1;\n      if (hasSpecialChars) score += 1;\n\n      // 最高分为4\n      return Math.min(4, Math.floor(score / 1.25));\n    },\n    strengthText() {\n      switch (this.score) {\n        case 0:\n          return '请输入密码';\n        case 1:\n          return '弱';\n        case 2:\n          return '中等';\n        case 3:\n          return '强';\n        case 4:\n          return '非常强';\n        default:\n          return '';\n      }\n    },\n    strengthTextClass() {\n      switch (this.score) {\n        case 0:\n          return 'text-gray-500';\n        case 1:\n          return 'text-red-600';\n        case 2:\n          return 'text-yellow-600';\n        case 3:\n        case 4:\n          return 'text-green-600';\n        default:\n          return '';\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "password", "type", "String", "required", "computed", "score", "length", "hasLowerCase", "test", "hasUpperCase", "hasNumbers", "hasSpecialChars", "Math", "min", "floor", "strengthText", "strengthTextClass"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\PasswordStrengthMeter.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div \r\n      class=\"password-strength\" \r\n      :class=\"{\r\n        'password-strength-low': score === 1,\r\n        'password-strength-medium': score === 2,\r\n        'password-strength-high': score === 3 || score === 4\r\n      }\"\r\n    >\r\n      <div class=\"password-strength-segment segment-1\"></div>\r\n      <div class=\"password-strength-segment segment-2\"></div>\r\n      <div class=\"password-strength-segment segment-3\"></div>\r\n      <div class=\"password-strength-segment segment-4\"></div>\r\n    </div>\r\n    <div class=\"text-right text-xs mt-1\" :class=\"strengthTextClass\">\r\n      {{ strengthText }}\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'PasswordStrengthMeter',\r\n  props: {\r\n    password: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  computed: {\r\n    score() {\r\n      if (!this.password) return 0\r\n      \r\n      // 简单评分算法，实际应用中可使用更复杂的算法\r\n      const length = this.password.length\r\n      const hasLowerCase = /[a-z]/.test(this.password)\r\n      const hasUpperCase = /[A-Z]/.test(this.password)\r\n      const hasNumbers = /\\d/.test(this.password)\r\n      const hasSpecialChars = /[^A-Za-z0-9]/.test(this.password)\r\n      \r\n      let score = 0\r\n      \r\n      if (length >= 8) score += 1\r\n      if (length >= 12) score += 1\r\n      if (hasLowerCase && hasUpperCase) score += 1\r\n      if (hasNumbers) score += 1\r\n      if (hasSpecialChars) score += 1\r\n      \r\n      // 最高分为4\r\n      return Math.min(4, Math.floor(score / 1.25))\r\n    },\r\n    \r\n    strengthText() {\r\n      switch (this.score) {\r\n        case 0: return '请输入密码'\r\n        case 1: return '弱'\r\n        case 2: return '中等'\r\n        case 3: return '强'\r\n        case 4: return '非常强'\r\n        default: return ''\r\n      }\r\n    },\r\n    \r\n    strengthTextClass() {\r\n      switch (this.score) {\r\n        case 0: return 'text-gray-500'\r\n        case 1: return 'text-red-600'\r\n        case 2: return 'text-yellow-600'\r\n        case 3:\r\n        case 4: return 'text-green-600'\r\n        default: return ''\r\n      }\r\n    }\r\n  }\r\n}\r\n</script> "], "mappings": "AAsBA,eAAe;EACbA,IAAI,EAAE,uBAAuB;EAC7BC,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,QAAQ,EAAE;IACRC,KAAKA,CAAA,EAAG;MACN,IAAI,CAAC,IAAI,CAACL,QAAQ,EAAE,OAAO;;MAE3B;MACA,MAAMM,MAAK,GAAI,IAAI,CAACN,QAAQ,CAACM,MAAK;MAClC,MAAMC,YAAW,GAAI,OAAO,CAACC,IAAI,CAAC,IAAI,CAACR,QAAQ;MAC/C,MAAMS,YAAW,GAAI,OAAO,CAACD,IAAI,CAAC,IAAI,CAACR,QAAQ;MAC/C,MAAMU,UAAS,GAAI,IAAI,CAACF,IAAI,CAAC,IAAI,CAACR,QAAQ;MAC1C,MAAMW,eAAc,GAAI,cAAc,CAACH,IAAI,CAAC,IAAI,CAACR,QAAQ;MAEzD,IAAIK,KAAI,GAAI;MAEZ,IAAIC,MAAK,IAAK,CAAC,EAAED,KAAI,IAAK;MAC1B,IAAIC,MAAK,IAAK,EAAE,EAAED,KAAI,IAAK;MAC3B,IAAIE,YAAW,IAAKE,YAAY,EAAEJ,KAAI,IAAK;MAC3C,IAAIK,UAAU,EAAEL,KAAI,IAAK;MACzB,IAAIM,eAAe,EAAEN,KAAI,IAAK;;MAE9B;MACA,OAAOO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,KAAK,CAACT,KAAI,GAAI,IAAI,CAAC;IAC7C,CAAC;IAEDU,YAAYA,CAAA,EAAG;MACb,QAAQ,IAAI,CAACV,KAAK;QAChB,KAAK,CAAC;UAAE,OAAO,OAAM;QACrB,KAAK,CAAC;UAAE,OAAO,GAAE;QACjB,KAAK,CAAC;UAAE,OAAO,IAAG;QAClB,KAAK,CAAC;UAAE,OAAO,GAAE;QACjB,KAAK,CAAC;UAAE,OAAO,KAAI;QACnB;UAAS,OAAO,EAAC;MACnB;IACF,CAAC;IAEDW,iBAAiBA,CAAA,EAAG;MAClB,QAAQ,IAAI,CAACX,KAAK;QAChB,KAAK,CAAC;UAAE,OAAO,eAAc;QAC7B,KAAK,CAAC;UAAE,OAAO,cAAa;QAC5B,KAAK,CAAC;UAAE,OAAO,iBAAgB;QAC/B,KAAK,CAAC;QACN,KAAK,CAAC;UAAE,OAAO,gBAAe;QAC9B;UAAS,OAAO,EAAC;MACnB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}