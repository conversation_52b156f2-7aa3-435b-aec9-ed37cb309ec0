<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">安全概览</h1>
        <p class="text-gray-600 dark:text-gray-400">密码安全状态监控与分析</p>
      </div>
      <div class="flex space-x-3">
        <!-- 测试通知按钮 -->
        <div class="flex space-x-2 mr-4">
          <button
            @click="testSuccessNotification"
            class="inline-flex items-center px-3 py-1 bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-700 rounded text-xs font-medium text-green-700 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/50"
          >
            成功
          </button>
          <button
            @click="testErrorNotification"
            class="inline-flex items-center px-3 py-1 bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-700 rounded text-xs font-medium text-red-700 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-900/50"
          >
            错误
          </button>
          <button
            @click="testWarningNotification"
            class="inline-flex items-center px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-700 rounded text-xs font-medium text-yellow-700 dark:text-yellow-400 hover:bg-yellow-200 dark:hover:bg-yellow-900/50"
          >
            警告
          </button>
          <button
            @click="testInfoNotification"
            class="inline-flex items-center px-3 py-1 bg-blue-100 dark:bg-blue-900/30 border border-blue-300 dark:border-blue-700 rounded text-xs font-medium text-blue-700 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/50"
          >
            信息
          </button>
        </div>

        <button
          @click="refreshData"
          class="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <font-awesome-icon :icon="['fas', 'sync-alt']" class="mr-2" :class="{ 'animate-spin': refreshing }" />
          刷新数据
        </button>
        <button
          @click="exportReport"
          class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <font-awesome-icon :icon="['fas', 'download']" class="mr-2" />
          导出报告
        </button>
      </div>
    </div>

    <!-- 安全仪表板 -->
    <SecurityDashboard :hosts="hosts" />

    <!-- 详细分析 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 密码合规性分析 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <font-awesome-icon :icon="['fas', 'shield-check']" class="mr-2 text-green-500" />
          密码合规性分析
        </h3>
        
        <div class="space-y-4">
          <div v-for="compliance in complianceData" :key="compliance.rule" class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div class="flex items-center space-x-3">
              <div class="w-3 h-3 rounded-full" :class="compliance.status === 'pass' ? 'bg-green-500' : 'bg-red-500'"></div>
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ compliance.rule }}</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-semibold" :class="compliance.status === 'pass' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                {{ compliance.passRate }}%
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">{{ compliance.passed }}/{{ compliance.total }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 安全事件时间线 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <font-awesome-icon :icon="['fas', 'clock']" class="mr-2 text-blue-500" />
          安全事件时间线
        </h3>
        
        <div class="space-y-4">
          <div v-for="event in securityEvents" :key="event.id" class="flex items-start space-x-3">
            <div class="flex-shrink-0 mt-1">
              <div class="w-8 h-8 rounded-full flex items-center justify-center" :class="event.iconBg">
                <font-awesome-icon :icon="['fas', event.icon]" :class="event.iconColor" class="text-sm" />
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ event.title }}</p>
                <span class="text-xs text-gray-500 dark:text-gray-400">{{ event.time }}</span>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-400">{{ event.description }}</p>
              <div v-if="event.severity" class="mt-1">
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium" :class="getSeverityClass(event.severity)">
                  {{ event.severity }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 推荐操作 -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800 p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
        <font-awesome-icon :icon="['fas', 'lightbulb']" class="mr-2 text-yellow-500" />
        安全建议
      </h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div v-for="recommendation in recommendations" :key="recommendation.id" class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div class="flex items-start space-x-3">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 rounded-lg flex items-center justify-center" :class="recommendation.iconBg">
                <font-awesome-icon :icon="['fas', recommendation.icon]" :class="recommendation.iconColor" class="text-sm" />
              </div>
            </div>
            <div class="flex-1">
              <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ recommendation.title }}</h4>
              <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">{{ recommendation.description }}</p>
              <button 
                @click="executeRecommendation(recommendation)"
                class="mt-2 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
              >
                立即执行
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import SecurityDashboard from '@/components/SecurityDashboard.vue'
import { showSuccess, showError, showInfo, showWarning } from '@/utils/notification.js'

export default {
  name: 'SecurityOverview',
  components: {
    SecurityDashboard
  },
  data() {
    return {
      refreshing: false,
      complianceData: [
        { rule: '密码最小长度', status: 'pass', passRate: 95, passed: 38, total: 40 },
        { rule: '包含大写字母', status: 'pass', passRate: 88, passed: 35, total: 40 },
        { rule: '包含特殊字符', status: 'fail', passRate: 72, passed: 29, total: 40 },
        { rule: '定期更新', status: 'fail', passRate: 65, passed: 26, total: 40 },
        { rule: '不重复历史密码', status: 'pass', passRate: 92, passed: 37, total: 40 }
      ],
      securityEvents: [
        {
          id: 1,
          title: '检测到弱密码',
          description: 'server-003 的 root 账号使用了弱密码',
          time: '5分钟前',
          severity: '高',
          icon: 'exclamation-triangle',
          iconColor: 'text-red-600',
          iconBg: 'bg-red-100 dark:bg-red-900/30'
        },
        {
          id: 2,
          title: '密码策略更新',
          description: '高强度策略已应用到生产环境',
          time: '1小时前',
          severity: '信息',
          icon: 'shield-alt',
          iconColor: 'text-blue-600',
          iconBg: 'bg-blue-100 dark:bg-blue-900/30'
        },
        {
          id: 3,
          title: '批量密码更新完成',
          description: '测试环境15台服务器密码更新成功',
          time: '3小时前',
          severity: '成功',
          icon: 'check-circle',
          iconColor: 'text-green-600',
          iconBg: 'bg-green-100 dark:bg-green-900/30'
        },
        {
          id: 4,
          title: '密码即将过期提醒',
          description: '5台服务器的密码将在3天内过期',
          time: '6小时前',
          severity: '警告',
          icon: 'clock',
          iconColor: 'text-yellow-600',
          iconBg: 'bg-yellow-100 dark:bg-yellow-900/30'
        }
      ],
      recommendations: [
        {
          id: 1,
          title: '更新弱密码',
          description: '发现3个弱密码，建议立即更新',
          icon: 'key',
          iconColor: 'text-red-600',
          iconBg: 'bg-red-100 dark:bg-red-900/30',
          action: 'update-weak-passwords'
        },
        {
          id: 2,
          title: '应用安全策略',
          description: '为新主机应用标准安全策略',
          icon: 'shield-alt',
          iconColor: 'text-blue-600',
          iconBg: 'bg-blue-100 dark:bg-blue-900/30',
          action: 'apply-security-policy'
        },
        {
          id: 3,
          title: '设置定时任务',
          description: '配置自动密码更新任务',
          icon: 'clock',
          iconColor: 'text-green-600',
          iconBg: 'bg-green-100 dark:bg-green-900/30',
          action: 'setup-scheduled-task'
        }
      ]
    }
  },
  computed: {
    ...mapState(['hosts'])
  },
  methods: {
    // 测试通知方法
    testSuccessNotification() {
      showSuccess('这是一个成功通知的示例', '操作成功')
    },

    testErrorNotification() {
      showError('这是一个错误通知的示例，不会自动关闭', '操作失败')
    },

    testWarningNotification() {
      showWarning('这是一个警告通知的示例', '注意')
    },

    testInfoNotification() {
      showInfo('这是一个信息通知的示例', '提示')
    },

    async refreshData() {
      this.refreshing = true
      try {
        // 模拟数据刷新
        await new Promise(resolve => setTimeout(resolve, 1000))
        showSuccess('数据已刷新', '刷新成功')
      } catch (error) {
        showError('数据刷新失败')
      } finally {
        this.refreshing = false
      }
    },

    async exportReport() {
      try {
        // 模拟报告导出
        showInfo('正在生成安全报告...', '导出中')
        await new Promise(resolve => setTimeout(resolve, 2000))
        showSuccess('安全报告已导出到下载文件夹', '导出成功')
      } catch (error) {
        showError('报告导出失败')
      }
    },
    
    getSeverityClass(severity) {
      const classes = {
        '高': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
        '中': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
        '低': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
        '警告': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
        '成功': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
        '信息': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
      }
      return classes[severity] || classes['信息']
    },
    
    executeRecommendation(recommendation) {
      // 根据推荐操作类型执行相应的操作
      switch (recommendation.action) {
        case 'update-weak-passwords':
          this.$router.push('/hosts')
          showInfo('已跳转到主机管理页面，请选择需要更新的主机')
          break
        case 'apply-security-policy':
          this.$router.push('/policies')
          showInfo('已跳转到密码策略页面')
          break
        case 'setup-scheduled-task':
          this.$router.push('/tasks')
          showInfo('已跳转到定时任务页面')
          break
        default:
          showInfo('功能开发中...')
      }
    }
  }
}
</script>
