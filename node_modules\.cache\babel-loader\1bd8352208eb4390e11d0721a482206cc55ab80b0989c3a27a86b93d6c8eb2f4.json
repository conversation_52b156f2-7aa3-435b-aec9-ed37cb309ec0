{"ast": null, "code": "import { resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, createVNode as _createVNode, renderSlot as _renderSlot, createElementBlock as _createElementBlock, withModifiers as _withModifiers, Transition as _Transition, withCtx as _withCtx, Teleport as _Teleport } from \"vue\";\nconst _hoisted_1 = {\n  class: \"modal-header\"\n};\nconst _hoisted_2 = {\n  class: \"modal-body\"\n};\nconst _hoisted_3 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_4 = [\"disabled\"];\nconst _hoisted_5 = {\n  key: 0,\n  class: \"inline-block animate-spin mr-2\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  return _openBlock(), _createBlock(_Teleport, {\n    to: \"body\"\n  }, [_createVNode(_Transition, {\n    name: \"modal\"\n  }, {\n    default: _withCtx(() => [$props.modelValue ? (_openBlock(), _createElementBlock(\"div\", {\n      key: 0,\n      class: \"modal-backdrop\",\n      onClick: _cache[3] || (_cache[3] = _withModifiers((...args) => $options.close && $options.close(...args), [\"self\"]))\n    }, [_createElementVNode(\"div\", {\n      class: _normalizeClass(['modal-container', {\n        'modal-lg': $props.size === 'lg'\n      }])\n    }, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"h3\", {\n      class: _normalizeClass([\"text-lg font-semibold\", {\n        'text-red-600': $props.danger\n      }])\n    }, [$props.icon ? (_openBlock(), _createBlock(_component_font_awesome_icon, {\n      key: 0,\n      icon: ['fas', $props.icon],\n      class: \"mr-2\"\n    }, null, 8 /* PROPS */, [\"icon\"])) : _createCommentVNode(\"v-if\", true), _createTextVNode(\" \" + _toDisplayString($props.title), 1 /* TEXT */)], 2 /* CLASS */), _createElementVNode(\"button\", {\n      onClick: _cache[0] || (_cache[0] = (...args) => $options.close && $options.close(...args)),\n      class: \"text-gray-400 hover:text-gray-600\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'times']\n    })])]), _createElementVNode(\"div\", _hoisted_2, [_renderSlot(_ctx.$slots, \"default\", {}, undefined, true)]), _createElementVNode(\"div\", _hoisted_3, [_renderSlot(_ctx.$slots, \"footer\", {}, () => [_createElementVNode(\"button\", {\n      onClick: _cache[1] || (_cache[1] = (...args) => $options.close && $options.close(...args)),\n      class: \"btn-outline\"\n    }, \" 取消 \"), _createElementVNode(\"button\", {\n      onClick: _cache[2] || (_cache[2] = (...args) => $options.confirm && $options.confirm(...args)),\n      class: _normalizeClass(['btn-primary', {\n        'bg-red-600 hover:bg-red-700': $props.danger\n      }]),\n      disabled: $props.loading\n    }, [$props.loading ? (_openBlock(), _createElementBlock(\"span\", _hoisted_5, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt']\n    })])) : _createCommentVNode(\"v-if\", true), _createTextVNode(\" \" + _toDisplayString($props.confirmText), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_4)], true)])], 2 /* CLASS */)])) : _createCommentVNode(\"v-if\", true)]),\n    _: 3 /* FORWARDED */\n  })]);\n}", "map": {"version": 3, "names": ["class", "key", "_createBlock", "_Teleport", "to", "_createVNode", "_Transition", "name", "default", "_withCtx", "$props", "modelValue", "_createElementBlock", "onClick", "_cache", "_withModifiers", "args", "$options", "close", "_createElementVNode", "_normalizeClass", "size", "_hoisted_1", "danger", "icon", "_component_font_awesome_icon", "_createCommentVNode", "_createTextVNode", "_toDisplayString", "title", "_hoisted_2", "_renderSlot", "_ctx", "$slots", "undefined", "_hoisted_3", "confirm", "disabled", "loading", "_hoisted_5", "confirmText", "_hoisted_4", "_"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\BaseModal.vue"], "sourcesContent": ["<template>\r\n  <teleport to=\"body\">\r\n    <transition name=\"modal\">\r\n      <div v-if=\"modelValue\" class=\"modal-backdrop\" @click.self=\"close\">\r\n        <div :class=\"['modal-container', { 'modal-lg': size === 'lg' }]\">\r\n          <div class=\"modal-header\">\r\n            <h3 class=\"text-lg font-semibold\" :class=\"{ 'text-red-600': danger }\">\r\n              <font-awesome-icon v-if=\"icon\" :icon=\"['fas', icon]\" class=\"mr-2\" />\r\n              {{ title }}\r\n            </h3>\r\n            <button @click=\"close\" class=\"text-gray-400 hover:text-gray-600\">\r\n              <font-awesome-icon :icon=\"['fas', 'times']\" />\r\n            </button>\r\n          </div>\r\n          \r\n          <div class=\"modal-body\">\r\n            <slot></slot>\r\n          </div>\r\n          \r\n          <div class=\"modal-footer\">\r\n            <slot name=\"footer\">\r\n              <button @click=\"close\" class=\"btn-outline\">\r\n                取消\r\n              </button>\r\n              <button \r\n                @click=\"confirm\"\r\n                :class=\"[\r\n                  'btn-primary', \r\n                  { 'bg-red-600 hover:bg-red-700': danger }\r\n                ]\"\r\n                :disabled=\"loading\"\r\n              >\r\n                <span v-if=\"loading\" class=\"inline-block animate-spin mr-2\">\r\n                  <font-awesome-icon :icon=\"['fas', 'sync-alt']\" />\r\n                </span>\r\n                {{ confirmText }}\r\n              </button>\r\n            </slot>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </transition>\r\n  </teleport>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'BaseModal',\r\n  props: {\r\n    modelValue: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    title: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    confirmText: {\r\n      type: String,\r\n      default: '确认'\r\n    },\r\n    icon: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    size: {\r\n      type: String,\r\n      default: 'md',\r\n      validator: (value) => ['sm', 'md', 'lg'].includes(value)\r\n    },\r\n    danger: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  emits: ['update:modelValue', 'confirm'],\r\n  methods: {\r\n    close() {\r\n      this.$emit('update:modelValue', false)\r\n    },\r\n    confirm() {\r\n      this.$emit('confirm')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.modal-lg {\r\n  max-width: 600px;\r\n}\r\n\r\n.modal-enter-active,\r\n.modal-leave-active {\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.modal-enter-from,\r\n.modal-leave-to {\r\n  opacity: 0;\r\n}\r\n\r\n.modal-enter-active .modal-container,\r\n.modal-leave-active .modal-container {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.modal-enter-from .modal-container,\r\n.modal-leave-to .modal-container {\r\n  transform: translateY(-20px);\r\n}\r\n</style> "], "mappings": ";;EAKeA,KAAK,EAAC;AAAc;;EAUpBA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAc;mBAnBnC;;EAAAC,GAAA;EAgCqCD,KAAK,EAAC;;;;uBA/BzCE,YAAA,CAyCWC,SAAA;IAzCDC,EAAE,EAAC;EAAM,IACjBC,YAAA,CAuCaC,WAAA;IAvCDC,IAAI,EAAC;EAAO;IAF5BC,OAAA,EAAAC,QAAA,CAIO,MA8CwC,CA/C9BC,MAAA,CAAAC,UAAU,I,cAArBC,mBAAA,CAqCM;MAxCZX,GAAA;MAG6BD,KAAK,EAAC,gBAAgB;MAAEa,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAH1DC,cAAA,KAAAC,IAAA,KAGiEC,QAAA,CAAAC,KAAA,IAAAD,QAAA,CAAAC,KAAA,IAAAF,IAAA,CAAK;QAC9DG,mBAAA,CAmCM;MAnCAnB,KAAK,EAJnBoB,eAAA;QAAA,YAIuDV,MAAA,CAAAW,IAAI;MAAA;QACjDF,mBAAA,CAQM,OARNG,UAQM,GAPJH,mBAAA,CAGK;MAHDnB,KAAK,EANrBoB,eAAA,EAMsB,uBAAuB;QAAA,gBAA2BV,MAAA,CAAAa;MAAM;QACvCb,MAAA,CAAAc,IAAI,I,cAA7BtB,YAAA,CAAoEuB,4BAAA;MAPlFxB,GAAA;MAO8CuB,IAAI,UAAUd,MAAA,CAAAc,IAAI;MAAGxB,KAAK,EAAC;yCAPzE0B,mBAAA,gBAAAC,gBAAA,CAOkF,GACpE,GAAAC,gBAAA,CAAGlB,MAAA,CAAAmB,KAAK,iB,kBAEVV,mBAAA,CAES;MAFAN,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAE,IAAA,KAAEC,QAAA,CAAAC,KAAA,IAAAD,QAAA,CAAAC,KAAA,IAAAF,IAAA,CAAK;MAAEhB,KAAK,EAAC;QAC3BK,YAAA,CAA8CoB,4BAAA;MAA1BD,IAAI,EAAE;IAAgB,G,KAI9CL,mBAAA,CAEM,OAFNW,UAEM,GADJC,WAAA,CAAaC,IAAA,CAAAC,MAAA,iBAAAC,SAAA,Q,GAGff,mBAAA,CAmBM,OAnBNgB,UAmBM,GAlBJJ,WAAA,CAiBOC,IAAA,CAAAC,MAAA,gBAjBP,MAiBO,CAhBLd,mBAAA,CAES;MAFAN,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAE,IAAA,KAAEC,QAAA,CAAAC,KAAA,IAAAD,QAAA,CAAAC,KAAA,IAAAF,IAAA,CAAK;MAAEhB,KAAK,EAAC;OAAc,MAE3C,GACAmB,mBAAA,CAYS;MAXNN,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAE,IAAA,KAAEC,QAAA,CAAAmB,OAAA,IAAAnB,QAAA,CAAAmB,OAAA,IAAApB,IAAA,CAAO;MACdhB,KAAK,EA1BtBoB,eAAA,E;uCA0BiHV,MAAA,CAAAa;MAAM,E;MAItGc,QAAQ,EAAE3B,MAAA,CAAA4B;QAEC5B,MAAA,CAAA4B,OAAO,I,cAAnB1B,mBAAA,CAEO,QAFP2B,UAEO,GADLlC,YAAA,CAAiDoB,4BAAA;MAA7BD,IAAI,EAAE;IAAmB,G,KAjC/DE,mBAAA,gBAAAC,gBAAA,CAkCuB,GACP,GAAAC,gBAAA,CAAGlB,MAAA,CAAA8B,WAAW,iB,yBAnC9BC,UAAA,E,gCAAAf,mBAAA,e;IAAAgB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}