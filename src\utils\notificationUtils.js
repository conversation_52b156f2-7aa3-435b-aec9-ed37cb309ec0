/**
 * 通知管理工具类
 * @deprecated 请使用 src/utils/notification.js 中的新通知系统
 */

// 重新导出新的通知方法以保持向后兼容
import { showSuccess as newShowSuccess, showError as newShowError, showInfo } from './notification.js'

/**
 * 显示普通通知消息
 * @param {string} message 消息内容
 * @param {number} duration 显示时长(ms)
 * @deprecated 请使用 showInfo 方法
 */
export function showMessage(message, duration = 3000) {
    showInfo(message, '消息', { duration })
}

/**
 * 显示成功通知消息
 * @param {string} message 消息内容
 * @param {number} duration 显示时长(ms)
 * @deprecated 请使用新的 showSuccess 方法
 */
export function showSuccess(message, duration = 3000) {
    newShowSuccess(message, '成功', { duration })
}

/**
 * 显示错误通知消息
 * @param {string} message 消息内容
 * @param {number} duration 显示时长(ms)
 * @deprecated 请使用新的 showError 方法
 */
export function showError(message, duration = 3000) {
    newShowError(message, '错误', { duration })
}

/**
 * 显示警告通知消息
 * @param {string} message 消息内容
 * @param {number} duration 显示时长(ms)
 */
export function showWarning(message, duration = 3000) {
    // 在实际应用中，这里可以使用UI库的通知组件
    alert(`⚠️ ${message}`)
}

/**
 * 显示确认对话框
 * @param {string} message 消息内容
 * @param {string} title 标题
 * @returns {Promise<boolean>} 用户选择结果
 */
export function showConfirm(message, title = '确认') {
    // 在实际应用中，这里可以使用UI库的确认对话框组件
    return new Promise((resolve) => {
        const result = confirm(message)
        resolve(result)
    })
}

/**
 * 创建通知消息对象
 * @param {string} type 通知类型 ('success', 'error', 'warning', 'info')
 * @param {string} message 消息内容
 * @param {string} title 标题
 * @param {Date} time 时间
 * @returns {Object} 通知消息对象
 */
export function createNotification(type, message, title, time = new Date()) {
    return {
        id: Date.now().toString(),
        type,
        message,
        title,
        time,
        read: false
    }
}

/**
 * 格式化通知时间
 * @param {Date} time 时间
 * @returns {string} 格式化后的时间字符串
 */
export function formatNotificationTime(time) {
    const now = new Date()
    const diff = now - time

    // 一分钟内
    if (diff < 60000) {
        return '刚刚'
    }

    // 一小时内
    if (diff < 3600000) {
        return `${Math.floor(diff / 60000)}分钟前`
    }

    // 今天内
    if (now.getDate() === time.getDate() &&
        now.getMonth() === time.getMonth() &&
        now.getFullYear() === time.getFullYear()) {
        return `今天 ${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`
    }

    // 昨天
    const yesterday = new Date(now)
    yesterday.setDate(now.getDate() - 1)
    if (yesterday.getDate() === time.getDate() &&
        yesterday.getMonth() === time.getMonth() &&
        yesterday.getFullYear() === time.getFullYear()) {
        return `昨天 ${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`
    }

    // 其他时间
    return `${time.getFullYear()}-${(time.getMonth() + 1).toString().padStart(2, '0')}-${time.getDate().toString().padStart(2, '0')} ${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`
} 