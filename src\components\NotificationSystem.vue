<template>
  <!-- 通知容器 -->
  <div class="notification-container">
    <!-- 通知列表 -->
    <transition-group name="notification" tag="div" class="fixed top-4 right-4 z-50 space-y-2">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        class="notification-item max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"
        :class="getNotificationClass(notification.type)"
      >
        <div class="p-4">
          <div class="flex items-start">
            <!-- 图标 -->
            <div class="flex-shrink-0">
              <div class="w-8 h-8 rounded-full flex items-center justify-center" :class="getIconBgClass(notification.type)">
                <font-awesome-icon 
                  :icon="['fas', getNotificationIcon(notification.type)]" 
                  :class="getIconClass(notification.type)"
                  class="text-sm"
                />
              </div>
            </div>
            
            <!-- 内容 -->
            <div class="ml-3 w-0 flex-1">
              <p class="text-sm font-medium text-gray-900 dark:text-white">
                {{ notification.title }}
              </p>
              <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {{ notification.message }}
              </p>
              
              <!-- 操作按钮 -->
              <div v-if="notification.actions && notification.actions.length > 0" class="mt-3 flex space-x-2">
                <button
                  v-for="action in notification.actions"
                  :key="action.label"
                  @click="handleAction(notification, action)"
                  class="text-xs font-medium px-3 py-1 rounded-md transition-colors"
                  :class="action.primary 
                    ? 'bg-blue-600 text-white hover:bg-blue-700' 
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'"
                >
                  {{ action.label }}
                </button>
              </div>
            </div>
            
            <!-- 关闭按钮 -->
            <div class="ml-4 flex-shrink-0 flex">
              <button
                @click="removeNotification(notification.id)"
                class="bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <font-awesome-icon :icon="['fas', 'times']" class="text-sm" />
              </button>
            </div>
          </div>
          
          <!-- 进度条（用于自动消失的通知） -->
          <div v-if="notification.autoClose && notification.duration" class="mt-3">
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
              <div 
                class="h-1 rounded-full transition-all duration-100 ease-linear"
                :class="getProgressBarClass(notification.type)"
                :style="{ width: getProgressWidth(notification) + '%' }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </transition-group>
  </div>
</template>

<script>
export default {
  name: 'NotificationSystem',
  data() {
    return {
      notifications: [],
      nextId: 1,
      timers: new Map()
    }
  },
  
  mounted() {
    // 监听全局通知事件
    this.$root.$on('show-notification', this.showNotification)
    this.$root.$on('show-success', (message, title = '成功') => {
      this.showNotification({ type: 'success', title, message })
    })
    this.$root.$on('show-error', (message, title = '错误') => {
      this.showNotification({ type: 'error', title, message })
    })
    this.$root.$on('show-warning', (message, title = '警告') => {
      this.showNotification({ type: 'warning', title, message })
    })
    this.$root.$on('show-info', (message, title = '信息') => {
      this.showNotification({ type: 'info', title, message })
    })
  },
  
  beforeUnmount() {
    // 清理事件监听器和定时器
    this.$root.$off('show-notification')
    this.$root.$off('show-success')
    this.$root.$off('show-error')
    this.$root.$off('show-warning')
    this.$root.$off('show-info')
    
    this.timers.forEach(timer => clearInterval(timer.interval))
  },
  
  methods: {
    showNotification(options) {
      const notification = {
        id: this.nextId++,
        type: options.type || 'info',
        title: options.title || '',
        message: options.message || '',
        actions: options.actions || [],
        autoClose: options.autoClose !== false,
        duration: options.duration || 5000,
        createdAt: Date.now(),
        progress: 100
      }
      
      this.notifications.push(notification)
      
      // 设置自动关闭
      if (notification.autoClose) {
        this.startAutoClose(notification)
      }
      
      // 限制通知数量
      if (this.notifications.length > 5) {
        this.removeNotification(this.notifications[0].id)
      }
    },
    
    removeNotification(id) {
      const index = this.notifications.findIndex(n => n.id === id)
      if (index > -1) {
        this.notifications.splice(index, 1)
        
        // 清理定时器
        if (this.timers.has(id)) {
          clearInterval(this.timers.get(id).interval)
          this.timers.delete(id)
        }
      }
    },
    
    startAutoClose(notification) {
      const startTime = Date.now()
      const interval = setInterval(() => {
        const elapsed = Date.now() - startTime
        const progress = Math.max(0, 100 - (elapsed / notification.duration) * 100)
        
        notification.progress = progress
        
        if (progress <= 0) {
          this.removeNotification(notification.id)
        }
      }, 50)
      
      this.timers.set(notification.id, { interval, startTime })
    },
    
    handleAction(notification, action) {
      if (action.handler) {
        action.handler(notification)
      }
      
      if (action.closeOnClick !== false) {
        this.removeNotification(notification.id)
      }
    },
    
    getNotificationClass(type) {
      const classes = {
        success: 'border-l-4 border-green-500',
        error: 'border-l-4 border-red-500',
        warning: 'border-l-4 border-yellow-500',
        info: 'border-l-4 border-blue-500'
      }
      return classes[type] || classes.info
    },
    
    getNotificationIcon(type) {
      const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
      }
      return icons[type] || icons.info
    },
    
    getIconClass(type) {
      const classes = {
        success: 'text-green-600 dark:text-green-400',
        error: 'text-red-600 dark:text-red-400',
        warning: 'text-yellow-600 dark:text-yellow-400',
        info: 'text-blue-600 dark:text-blue-400'
      }
      return classes[type] || classes.info
    },
    
    getIconBgClass(type) {
      const classes = {
        success: 'bg-green-100 dark:bg-green-900/30',
        error: 'bg-red-100 dark:bg-red-900/30',
        warning: 'bg-yellow-100 dark:bg-yellow-900/30',
        info: 'bg-blue-100 dark:bg-blue-900/30'
      }
      return classes[type] || classes.info
    },
    
    getProgressBarClass(type) {
      const classes = {
        success: 'bg-green-500',
        error: 'bg-red-500',
        warning: 'bg-yellow-500',
        info: 'bg-blue-500'
      }
      return classes[type] || classes.info
    },
    
    getProgressWidth(notification) {
      return notification.progress || 0
    }
  }
}
</script>

<style scoped>
.notification-container {
  pointer-events: none;
}

.notification-item {
  pointer-events: auto;
}

/* 通知动画 */
.notification-enter-active {
  transition: all 0.3s ease-out;
}

.notification-leave-active {
  transition: all 0.3s ease-in;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .notification-item {
    max-width: calc(100vw - 2rem);
    margin: 0 1rem;
  }
}
</style>
