{"ast": null, "code": "import { createApp } from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport store from './store';\nimport './assets/styles/index.css';\nimport { library } from '@fortawesome/fontawesome-svg-core';\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';\nimport { faShieldAlt, faServer, faKey, faClock, faCheck, faCog, faCheckSquare, faCalendarAlt, faTimes, faHistory, faPlus, faSyncAlt, faExclamationTriangle, faLock, faCalendarCheck, faEye, faEyeSlash, faExclamationCircle, faTable, faThLarge, faSearch, faEdit, faTrashAlt,\n// 新增的图标\nfaBell, faUser, faChevronDown, faSignOutAlt, faSun, faMoon, faTachometerAlt, faUsers, faMagic, faCopy, faCheckCircle, faInfoCircle, faDownload, faLightbulb, faTasks, faPlay, faPause, faTrash } from '@fortawesome/free-solid-svg-icons';\n\n// 创建全局事件总线\nimport mitt from 'mitt';\nconst eventBus = mitt();\nlibrary.add(faShieldAlt, faServer, faKey, faClock, faCheck, faCog, faCheckSquare, faCalendarAlt, faTimes, faHistory, faPlus, faSyncAlt, faExclamationTriangle, faLock, faCalendarCheck, faEye, faEyeSlash, faExclamationCircle, faTable, faThLarge, faSearch, faEdit, faTrashAlt,\n// 新增的图标\nfaBell, faUser, faChevronDown, faSignOutAlt, faSun, faMoon, faTachometerAlt, faUsers, faMagic, faCopy, faCheckCircle, faInfoCircle, faDownload, faLightbulb, faTasks, faPlay, faPause, faTrash);\nconst app = createApp(App);\napp.use(router);\napp.use(store);\napp.component('font-awesome-icon', FontAwesomeIcon);\n\n// 将事件总线添加到全局属性\napp.config.globalProperties.$eventBus = eventBus;\napp.mount('#app');", "map": {"version": 3, "names": ["createApp", "App", "router", "store", "library", "FontAwesomeIcon", "faShieldAlt", "faServer", "faKey", "faClock", "faCheck", "faCog", "faCheckSquare", "faCalendarAlt", "faTimes", "faHistory", "faPlus", "faSyncAlt", "faExclamationTriangle", "faLock", "faCalendarCheck", "faEye", "faEyeSlash", "faExclamationCircle", "faTable", "faThLarge", "faSearch", "faEdit", "faTrashAlt", "faBell", "faUser", "faChevronDown", "faSignOutAlt", "faSun", "faMoon", "faTachometerAlt", "faUsers", "faMagic", "faCopy", "faCheckCircle", "faInfoCircle", "faDownload", "faLightbulb", "faTasks", "faPlay", "faPause", "faTrash", "mitt", "eventBus", "add", "app", "use", "component", "config", "globalProperties", "$eventBus", "mount"], "sources": ["D:/demo/ooo/pass/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport store from './store'\r\nimport './assets/styles/index.css'\r\nimport { library } from '@fortawesome/fontawesome-svg-core'\r\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'\r\nimport {\r\n    faShieldAlt, faServer, faKey, faClock, faCheck,\r\n    faCog, faCheckSquare, faCalendarAlt, faTimes,\r\n    faHistory, faPlus, faSyncAlt, faExclamationTriangle,\r\n    faLock, faCalendarCheck, faEye, faEyeSlash, faExclamationCircle,\r\n    faTable, faThLarge, faSearch, faEdit, faTrashAlt,\r\n    // 新增的图标\r\n    faBell, faUser, faChevronDown, faSignOutAlt, faSun, faMoon,\r\n    faTachometerAlt, faUsers, faMagic, faCopy, faCheckCircle,\r\n    faInfoCircle, faDownload, faLightbulb,\r\n    faTasks, faPlay, faPause, faTrash\r\n} from '@fortawesome/free-solid-svg-icons'\r\n\r\n// 创建全局事件总线\r\nimport mitt from 'mitt'\r\nconst eventBus = mitt()\r\n\r\nlibrary.add(\r\n    faShieldAlt, faServer, faKey, faClock, faCheck,\r\n    faCog, faCheckSquare, faCalendarAlt, faTimes,\r\n    faHistory, faPlus, faSyncAlt, faExclamationTriangle,\r\n    faLock, faCalendarCheck, faEye, faEyeSlash, faExclamationCircle,\r\n    faTable, faThLarge, faSearch, faEdit, faTrashAlt,\r\n    // 新增的图标\r\n    faBell, faUser, faChevronDown, faSignOutAlt, faSun, faMoon,\r\n    faTachometerAlt, faUsers, faMagic, faCopy, faCheckCircle,\r\n    faInfoCircle, faDownload, faLightbulb,\r\n    faTasks, faPlay, faPause, faTrash\r\n)\r\n\r\nconst app = createApp(App)\r\napp.use(router)\r\napp.use(store)\r\napp.component('font-awesome-icon', FontAwesomeIcon)\r\n\r\n// 将事件总线添加到全局属性\r\napp.config.globalProperties.$eventBus = eventBus\r\n\r\napp.mount('#app')"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAO,2BAA2B;AAClC,SAASC,OAAO,QAAQ,mCAAmC;AAC3D,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SACIC,WAAW,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAC9CC,KAAK,EAAEC,aAAa,EAAEC,aAAa,EAAEC,OAAO,EAC5CC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,qBAAqB,EACnDC,MAAM,EAAEC,eAAe,EAAEC,KAAK,EAAEC,UAAU,EAAEC,mBAAmB,EAC/DC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,UAAU;AAChD;AACAC,MAAM,EAAEC,MAAM,EAAEC,aAAa,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAC1DC,eAAe,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,aAAa,EACxDC,YAAY,EAAEC,UAAU,EAAEC,WAAW,EACrCC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,QAC9B,mCAAmC;;AAE1C;AACA,OAAOC,IAAI,MAAM,MAAM;AACvB,MAAMC,QAAQ,GAAGD,IAAI,CAAC,CAAC;AAEvB3C,OAAO,CAAC6C,GAAG,CACP3C,WAAW,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAC9CC,KAAK,EAAEC,aAAa,EAAEC,aAAa,EAAEC,OAAO,EAC5CC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,qBAAqB,EACnDC,MAAM,EAAEC,eAAe,EAAEC,KAAK,EAAEC,UAAU,EAAEC,mBAAmB,EAC/DC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,UAAU;AAChD;AACAC,MAAM,EAAEC,MAAM,EAAEC,aAAa,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAC1DC,eAAe,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,aAAa,EACxDC,YAAY,EAAEC,UAAU,EAAEC,WAAW,EACrCC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAC9B,CAAC;AAED,MAAMI,GAAG,GAAGlD,SAAS,CAACC,GAAG,CAAC;AAC1BiD,GAAG,CAACC,GAAG,CAACjD,MAAM,CAAC;AACfgD,GAAG,CAACC,GAAG,CAAChD,KAAK,CAAC;AACd+C,GAAG,CAACE,SAAS,CAAC,mBAAmB,EAAE/C,eAAe,CAAC;;AAEnD;AACA6C,GAAG,CAACG,MAAM,CAACC,gBAAgB,CAACC,SAAS,GAAGP,QAAQ;AAEhDE,GAAG,CAACM,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}