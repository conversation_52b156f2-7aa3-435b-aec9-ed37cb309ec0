<template>
    <div class="app-container" :class="{ 'dark': isDarkMode }">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="container mx-auto px-6 py-4">
                <div class="flex justify-between items-center">
                    <!-- 左侧品牌区域 -->
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                                <font-awesome-icon :icon="['fas', 'shield-alt']" class="text-white text-lg" />
                            </div>
                            <div>
                                <h1 class="text-xl font-bold text-gray-900 dark:text-white">SecurePass</h1>
                                <p class="text-xs text-gray-500 dark:text-gray-400">企业密码管理平台</p>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧操作区域 -->
                    <div class="flex items-center space-x-4">
                        <!-- 全局搜索 -->
                        <div class="relative hidden md:block">
                            <input
                                type="text"
                                v-model="globalSearch"
                                placeholder="全局搜索..."
                                class="w-64 pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-700 border-0 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:text-white dark:placeholder-gray-400"
                                @keyup.enter="performGlobalSearch"
                            />
                            <font-awesome-icon :icon="['fas', 'search']" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        </div>

                        <!-- 通知中心 -->
                        <div class="relative">
                            <button
                                @click="showNotifications = !showNotifications"
                                class="relative p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                            >
                                <font-awesome-icon :icon="['fas', 'bell']" class="text-lg" />
                                <span v-if="unreadNotifications > 0" class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                                    {{ unreadNotifications }}
                                </span>
                            </button>

                            <!-- 通知下拉菜单 -->
                            <div v-if="showNotifications" class="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50">
                                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                                    <h3 class="font-semibold text-gray-900 dark:text-white">通知中心</h3>
                                </div>
                                <div class="max-h-64 overflow-y-auto">
                                    <div v-for="notification in notifications" :key="notification.id" class="p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <div class="flex items-start space-x-3">
                                            <div class="flex-shrink-0">
                                                <font-awesome-icon :icon="['fas', notification.icon]" :class="notification.iconColor" />
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ notification.title }}</p>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ notification.message }}</p>
                                                <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">{{ notification.time }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 主题切换 -->
                        <button
                            @click="toggleTheme"
                            class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                        >
                            <font-awesome-icon :icon="['fas', isDarkMode ? 'sun' : 'moon']" class="text-lg" />
                        </button>

                        <!-- 用户菜单 -->
                        <div class="relative">
                            <button
                                @click="showUserMenu = !showUserMenu"
                                class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                            >
                                <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm font-semibold">管</span>
                                </div>
                                <span class="hidden md:block text-sm font-medium text-gray-700 dark:text-gray-300">管理员</span>
                                <font-awesome-icon :icon="['fas', 'chevron-down']" class="text-xs text-gray-400" />
                            </button>

                            <!-- 用户下拉菜单 -->
                            <div v-if="showUserMenu" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50">
                                <div class="py-2">
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                        <font-awesome-icon :icon="['fas', 'user']" class="mr-3" />
                                        个人设置
                                    </a>
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                        <font-awesome-icon :icon="['fas', 'cog']" class="mr-3" />
                                        系统设置
                                    </a>
                                    <hr class="my-2 border-gray-200 dark:border-gray-700">
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20">
                                        <font-awesome-icon :icon="['fas', 'sign-out-alt']" class="mr-3" />
                                        退出登录
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="flex-1 bg-gray-50 dark:bg-gray-900">
            <div class="container mx-auto px-6 py-6">
                <!-- 导航标签 -->
                <nav class="nav-tabs">
                    <router-link v-for="route in routes" :key="route.path" :to="route.path" class="nav-item"
                        active-class="active">
                        <font-awesome-icon :icon="['fas', route.meta.icon]" class="mr-2" />
                        <span>{{ route.meta.title }}</span>
                        <span v-if="route.meta.badge" class="ml-2 px-2 py-0.5 bg-red-500 text-white text-xs rounded-full">
                            {{ route.meta.badge }}
                        </span>
                    </router-link>
                </nav>

                <!-- 路由视图 -->
                <router-view v-slot="{ Component }">
                    <transition name="fade" mode="out-in">
                        <component :is="Component" />
                    </transition>
                </router-view>
            </div>
        </main>

        <!-- 全局加载遮罩 -->
        <div v-if="globalLoading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center space-x-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span class="text-gray-700 dark:text-gray-300">{{ loadingMessage }}</span>
            </div>
        </div>

        <!-- 通知系统 -->
        <NotificationSystem ref="notificationSystem" />
    </div>
</template>

<script>
import { mapState } from 'vuex'
import NotificationSystem from '@/components/NotificationSystem.vue'

export default {
    name: 'App',
    components: {
        NotificationSystem
    },
    data() {
        return {
            isDarkMode: false,
            globalSearch: '',
            showNotifications: false,
            showUserMenu: false,
            globalLoading: false,
            loadingMessage: '处理中...',
            notifications: [
                {
                    id: 1,
                    title: '密码即将过期',
                    message: '5台服务器的密码将在3天内过期',
                    time: '2分钟前',
                    icon: 'exclamation-triangle',
                    iconColor: 'text-yellow-500'
                },
                {
                    id: 2,
                    title: '批量更新完成',
                    message: '生产环境密码更新任务已完成',
                    time: '1小时前',
                    icon: 'check-circle',
                    iconColor: 'text-green-500'
                },
                {
                    id: 3,
                    title: '安全警告',
                    message: '检测到弱密码，建议立即更新',
                    time: '3小时前',
                    icon: 'shield-alt',
                    iconColor: 'text-red-500'
                }
            ]
        }
    },
    computed: {
        ...mapState(['hosts']),
        routes() {
            const baseRoutes = this.$router.options.routes.filter(route => route.meta && route.meta.title)

            // 动态添加徽章信息
            return baseRoutes.map(route => {
                const routeCopy = { ...route }
                if (route.path === '/hosts') {
                    // 计算需要注意的主机数量
                    const warningHosts = this.hosts.filter(host =>
                        host.status === 'warning' || host.status === 'error'
                    ).length
                    if (warningHosts > 0) {
                        routeCopy.meta = { ...route.meta, badge: warningHosts }
                    }
                }
                return routeCopy
            })
        },
        unreadNotifications() {
            return this.notifications.length
        }
    },
    mounted() {
        // 初始化主题
        this.initTheme()

        // 点击外部关闭下拉菜单
        document.addEventListener('click', this.handleClickOutside)
    },
    beforeUnmount() {
        document.removeEventListener('click', this.handleClickOutside)
    },
    methods: {
        initTheme() {
            // 从本地存储读取主题设置
            const savedTheme = localStorage.getItem('theme')
            if (savedTheme) {
                this.isDarkMode = savedTheme === 'dark'
            } else {
                // 检测系统主题偏好
                this.isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
            }
            this.applyTheme()
        },

        toggleTheme() {
            this.isDarkMode = !this.isDarkMode
            this.applyTheme()
            localStorage.setItem('theme', this.isDarkMode ? 'dark' : 'light')
        },

        applyTheme() {
            if (this.isDarkMode) {
                document.documentElement.classList.add('dark')
            } else {
                document.documentElement.classList.remove('dark')
            }
        },

        performGlobalSearch() {
            if (this.globalSearch.trim()) {
                // 实现全局搜索逻辑
                console.log('搜索:', this.globalSearch)
                // 这里可以跳转到搜索结果页面或在当前页面显示搜索结果
            }
        },

        handleClickOutside(event) {
            // 关闭通知下拉菜单
            if (!event.target.closest('.relative') || !event.target.closest('[data-dropdown="notifications"]')) {
                this.showNotifications = false
            }

            // 关闭用户菜单
            if (!event.target.closest('.relative') || !event.target.closest('[data-dropdown="user"]')) {
                this.showUserMenu = false
            }
        },

        showGlobalLoading(message = '处理中...') {
            this.loadingMessage = message
            this.globalLoading = true
        },

        hideGlobalLoading() {
            this.globalLoading = false
        }
    }
}
</script>

<style>
/* 全局样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: "Inter", "PingFang SC", "Microsoft YaHei", -apple-system, BlinkMacSystemFont, sans-serif;
    background-color: #f8fafc;
    color: #1e293b;
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 暗色主题 */
.dark body {
    background-color: #0f172a;
    color: #e2e8f0;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    position: sticky;
    top: 0;
    z-index: 40;
}

.dark .header {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-bottom-color: #334155;
}

/* 导航标签样式 */
.nav-tabs {
    display: flex;
    background: white;
    border-radius: 12px;
    padding: 4px;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.dark .nav-tabs {
    background: #1e293b;
    border: 1px solid #334155;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    border-radius: 8px;
    color: #64748b;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    white-space: nowrap;
    position: relative;
}

.nav-item:hover:not(.active) {
    color: #3b82f6;
    background-color: #f1f5f9;
}

.dark .nav-item:hover:not(.active) {
    color: #60a5fa;
    background-color: #334155;
}

.nav-item.active {
    color: #3b82f6;
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    font-weight: 600;
    box-shadow: 0 2px 4px 0 rgba(59, 130, 246, 0.1);
}

.dark .nav-item.active {
    color: #60a5fa;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
    transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
    transform: translateY(10px);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.dark ::-webkit-scrollbar-track {
    background: #334155;
}

.dark ::-webkit-scrollbar-thumb {
    background: #64748b;
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-tabs {
        margin: 0 -1rem 2rem -1rem;
        border-radius: 0;
        padding: 4px 1rem;
    }

    .nav-item {
        padding: 10px 16px;
        font-size: 14px;
    }
}

/* 动画关键帧 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 工具类 */
.animate-slide-in {
    animation: slideIn 0.3s ease-out;
}

.animate-fade-in-up {
    animation: fadeInUp 0.3s ease-out;
}
</style>