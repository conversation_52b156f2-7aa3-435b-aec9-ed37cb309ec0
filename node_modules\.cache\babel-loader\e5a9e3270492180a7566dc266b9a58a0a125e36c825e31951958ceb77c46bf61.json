{"ast": null, "code": "import { toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"span\", {\n    class: _normalizeClass(['status-badge', `status-${$props.type}`])\n  }, _toDisplayString($options.statusText), 3 /* TEXT, CLASS */);\n}", "map": {"version": 3, "names": ["_createElementBlock", "class", "_normalizeClass", "$props", "type", "$options", "statusText"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\StatusBadge.vue"], "sourcesContent": ["<template>\r\n  <span :class=\"[\r\n    'status-badge',\r\n    `status-${type}`\r\n  ]\">\r\n    {{ statusText }}\r\n  </span>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'StatusBadge',\r\n  props: {\r\n    type: {\r\n      type: String,\r\n      required: true,\r\n      validator: (value) => ['normal', 'warning', 'error', 'running'].includes(value)\r\n    }\r\n  },\r\n  computed: {\r\n    statusText() {\r\n      const statusMap = {\r\n        normal: '正常',\r\n        warning: '密码即将过期',\r\n        error: '密码已过期',\r\n        running: '运行中'\r\n      }\r\n      \r\n      return statusMap[this.type] || '未知状态'\r\n    }\r\n  }\r\n}\r\n</script> "], "mappings": ";;uBACEA,mBAAA,CAKO;IALAC,KAAK,EADdC,eAAA,E,0BACsDC,MAAA,CAAAC,IAAI,G;sBAInDC,QAAA,CAAAC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}