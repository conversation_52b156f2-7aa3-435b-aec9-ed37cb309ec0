{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, vModelText as _vModelText, withDirectives as _withDirectives, vModelSelect as _vModelSelect, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, vModelRadio as _vModelRadio, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"bg-white shadow rounded-lg p-4 mb-6\"\n};\nconst _hoisted_2 = {\n  class: \"flex flex-wrap items-center justify-between\"\n};\nconst _hoisted_3 = {\n  class: \"flex space-x-3 mb-2 sm:mb-0\"\n};\nconst _hoisted_4 = {\n  class: \"flex items-center space-x-4\"\n};\nconst _hoisted_5 = {\n  class: \"flex items-center border rounded-md overflow-hidden\"\n};\nconst _hoisted_6 = {\n  class: \"relative\"\n};\nconst _hoisted_7 = {\n  class: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n};\nconst _hoisted_8 = {\n  key: 0,\n  class: \"bg-white rounded-lg shadow overflow-hidden\"\n};\nconst _hoisted_9 = {\n  class: \"min-w-full divide-y divide-gray-200\"\n};\nconst _hoisted_10 = {\n  class: \"bg-gray-50\"\n};\nconst _hoisted_11 = {\n  scope: \"col\",\n  class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n};\nconst _hoisted_12 = {\n  class: \"bg-white divide-y divide-gray-200\"\n};\nconst _hoisted_13 = [\"rowspan\"];\nconst _hoisted_14 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_15 = {\n  class: \"ml-2 font-medium text-gray-900\"\n};\nconst _hoisted_16 = [\"rowspan\"];\nconst _hoisted_17 = {\n  class: \"text-sm text-gray-900\"\n};\nconst _hoisted_18 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_19 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_20 = {\n  class: \"text-sm font-medium text-gray-900\"\n};\nconst _hoisted_21 = {\n  key: 0,\n  class: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\"\n};\nconst _hoisted_22 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_23 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_24 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_25 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_26 = {\n  key: 1,\n  class: \"ml-1\"\n};\nconst _hoisted_27 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_28 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_29 = {\n  class: \"flex-grow\"\n};\nconst _hoisted_30 = [\"type\", \"value\"];\nconst _hoisted_31 = [\"onClick\"];\nconst _hoisted_32 = [\"rowspan\"];\nconst _hoisted_33 = {\n  class: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\"\n};\nconst _hoisted_34 = [\"onClick\"];\nconst _hoisted_35 = {\n  class: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\"\n};\nconst _hoisted_36 = {\n  class: \"px-4 py-5 sm:p-6\"\n};\nconst _hoisted_37 = {\n  class: \"flex justify-between items-start mb-4\"\n};\nconst _hoisted_38 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_39 = {\n  class: \"text-lg font-medium text-gray-900\"\n};\nconst _hoisted_40 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_41 = {\n  class: \"mb-4\"\n};\nconst _hoisted_42 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_43 = [\"type\", \"value\"];\nconst _hoisted_44 = [\"onClick\"];\nconst _hoisted_45 = {\n  class: \"grid grid-cols-2 gap-4 mb-4\"\n};\nconst _hoisted_46 = {\n  class: \"text-sm text-gray-900\"\n};\nconst _hoisted_47 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_48 = {\n  key: 1,\n  class: \"ml-1\"\n};\nconst _hoisted_49 = {\n  class: \"mt-4\"\n};\nconst _hoisted_50 = [\"onClick\"];\nconst _hoisted_51 = {\n  class: \"form-group\"\n};\nconst _hoisted_52 = {\n  class: \"form-label\"\n};\nconst _hoisted_53 = {\n  class: \"form-group\"\n};\nconst _hoisted_54 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_55 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_56 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_57 = {\n  key: 0\n};\nconst _hoisted_58 = {\n  class: \"form-group\"\n};\nconst _hoisted_59 = [\"value\"];\nconst _hoisted_60 = {\n  class: \"form-group\"\n};\nconst _hoisted_61 = {\n  class: \"flex\"\n};\nconst _hoisted_62 = {\n  key: 1\n};\nconst _hoisted_63 = {\n  class: \"form-group\"\n};\nconst _hoisted_64 = {\n  class: \"form-group\"\n};\nconst _hoisted_65 = {\n  key: 0,\n  class: \"text-red-500 text-xs mt-1\"\n};\nconst _hoisted_66 = {\n  class: \"form-group\"\n};\nconst _hoisted_67 = {\n  class: \"form-group\"\n};\nconst _hoisted_68 = {\n  class: \"mb-2\"\n};\nconst _hoisted_69 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_70 = {\n  class: \"form-text\"\n};\nconst _hoisted_71 = {\n  class: \"form-group\"\n};\nconst _hoisted_72 = [\"value\"];\nconst _hoisted_73 = {\n  class: \"form-group\"\n};\nconst _hoisted_74 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_75 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_76 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_77 = {\n  key: 0,\n  class: \"mt-3\"\n};\nconst _hoisted_78 = {\n  class: \"grid grid-cols-2 gap-4\"\n};\nconst _hoisted_79 = {\n  class: \"form-group\"\n};\nconst _hoisted_80 = {\n  class: \"form-group\"\n};\nconst _hoisted_81 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_82 = {\n  class: \"form-group\"\n};\nconst _hoisted_83 = [\"value\"];\nconst _hoisted_84 = {\n  class: \"form-group\"\n};\nconst _hoisted_85 = {\n  class: \"form-group\"\n};\nconst _hoisted_86 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_87 = {\n  class: \"form-group\"\n};\nconst _hoisted_88 = [\"value\"];\nconst _hoisted_89 = {\n  class: \"form-group\"\n};\nconst _hoisted_90 = {\n  class: \"form-group\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_CustomCheckbox = _resolveComponent(\"CustomCheckbox\");\n  const _component_StatusBadge = _resolveComponent(\"StatusBadge\");\n  const _component_PasswordStrengthMeter = _resolveComponent(\"PasswordStrengthMeter\");\n  const _component_BaseModal = _resolveComponent(\"BaseModal\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createCommentVNode(\" 操作按钮 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.showEmergencyReset && $options.showEmergencyReset(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'exclamation-triangle'],\n    class: \"mr-2\"\n  }), _cache[37] || (_cache[37] = _createElementVNode(\"span\", null, \"紧急重置\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.openBatchUpdateModal && $options.openBatchUpdateModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'key'],\n    class: \"mr-2\"\n  }), _cache[38] || (_cache[38] = _createElementVNode(\"span\", null, \"批量更新密码\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.openBatchApplyModal && $options.openBatchApplyModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"mr-2\"\n  }), _cache[39] || (_cache[39] = _createElementVNode(\"span\", null, \"批量应用策略\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" 视图切换 \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1 focus:outline-none\", {\n      'bg-blue-500 text-white': $data.viewMode === 'table',\n      'bg-gray-100 text-gray-600': $data.viewMode !== 'table'\n    }]),\n    onClick: _cache[3] || (_cache[3] = $event => $data.viewMode = 'table')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'table'],\n    class: \"mr-1\"\n  }), _cache[40] || (_cache[40] = _createTextVNode(\" 表格 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1 focus:outline-none\", {\n      'bg-blue-500 text-white': $data.viewMode === 'card',\n      'bg-gray-100 text-gray-600': $data.viewMode !== 'card'\n    }]),\n    onClick: _cache[4] || (_cache[4] = $event => $data.viewMode = 'card')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'th-large'],\n    class: \"mr-1\"\n  }), _cache[41] || (_cache[41] = _createTextVNode(\" 卡片 \"))], 2 /* CLASS */)]), _createCommentVNode(\" 筛选 \"), _createElementVNode(\"div\", _hoisted_6, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.filterText = $event),\n    placeholder: \"筛选主机...\",\n    class: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.filterText]]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"text-gray-400\"\n  })])]), _createCommentVNode(\" 状态筛选 \"), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.statusFilter = $event),\n    class: \"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n  }, _cache[42] || (_cache[42] = [_createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有状态\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"normal\"\n  }, \"正常\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"warning\"\n  }, \"警告\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"error\"\n  }, \"错误\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.statusFilter]])])])]), _createCommentVNode(\" 主机列表 \"), _createCommentVNode(\" 表格视图 \"), $data.viewMode === 'table' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_createElementVNode(\"table\", _hoisted_9, [_createElementVNode(\"thead\", _hoisted_10, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", _hoisted_11, [_createVNode(_component_CustomCheckbox, {\n    modelValue: $data.selectAll,\n    \"onUpdate:modelValue\": [_cache[7] || (_cache[7] = $event => $data.selectAll = $event), $options.toggleSelectAll]\n  }, {\n    default: _withCtx(() => _cache[43] || (_cache[43] = [_createTextVNode(\" 主机名 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _cache[44] || (_cache[44] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" IP地址 \", -1 /* HOISTED */)), _cache[45] || (_cache[45] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 账号 \", -1 /* HOISTED */)), _cache[46] || (_cache[46] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 最后密码修改时间 \", -1 /* HOISTED */)), _cache[47] || (_cache[47] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码过期时间 \", -1 /* HOISTED */)), _cache[48] || (_cache[48] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码 \", -1 /* HOISTED */)), _cache[49] || (_cache[49] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 状态 \", -1 /* HOISTED */)), _cache[50] || (_cache[50] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 操作 \", -1 /* HOISTED */))])]), _createElementVNode(\"tbody\", _hoisted_12, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredHosts, (host, hostIndex) => {\n    return _openBlock(), _createElementBlock(_Fragment, {\n      key: host.id\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(host.accounts, (account, accountIndex) => {\n      return _openBlock(), _createElementBlock(\"tr\", {\n        key: account.id,\n        class: _normalizeClass({\n          'bg-gray-50': (hostIndex + accountIndex) % 2 === 0,\n          'hover:bg-blue-50': true,\n          'border-t-2 border-indigo-100': accountIndex === 0\n        })\n      }, [accountIndex === 0 ? (_openBlock(), _createElementBlock(\"td\", {\n        key: 0,\n        class: \"px-6 py-4 whitespace-nowrap\",\n        rowspan: accountIndex === 0 ? host.accounts.length : 1\n      }, [_createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_CustomCheckbox, {\n        modelValue: host.selected,\n        \"onUpdate:modelValue\": $event => host.selected = $event\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"span\", _hoisted_15, _toDisplayString(host.name), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])])], 8 /* PROPS */, _hoisted_13)) : _createCommentVNode(\"v-if\", true), accountIndex === 0 ? (_openBlock(), _createElementBlock(\"td\", {\n        key: 1,\n        class: \"px-6 py-4 whitespace-nowrap\",\n        rowspan: accountIndex === 0 ? host.accounts.length : 1\n      }, [_createElementVNode(\"div\", _hoisted_17, _toDisplayString(host.ip), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_16)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"td\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"span\", _hoisted_20, _toDisplayString(account.username), 1 /* TEXT */), account.isDefault ? (_openBlock(), _createElementBlock(\"span\", _hoisted_21, \" 默认 \")) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"td\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, _toDisplayString(account.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_24, [_createElementVNode(\"div\", {\n        class: _normalizeClass({\n          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\n          'bg-red-100 text-red-800': $options.isPasswordExpired(account).status === 'danger' || $options.isPasswordExpired(account).status === 'expired',\n          'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(account).status === 'warning',\n          'bg-gray-100 text-gray-800': $options.isPasswordExpired(account).status === 'normal'\n        })\n      }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(account).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(account).status === 'expired' || $options.isPasswordExpired(account).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_25, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-triangle']\n      })])) : $options.isPasswordExpired(account).status === 'warning' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_26, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-circle']\n      })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]), _createElementVNode(\"td\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"input\", {\n        type: $data.passwordVisibility[account.id] ? 'text' : 'password',\n        value: account.password,\n        readonly: \"\",\n        class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n      }, null, 8 /* PROPS */, _hoisted_30)]), _createElementVNode(\"button\", {\n        onClick: $event => $options.togglePasswordVisibility(account.id),\n        class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $data.passwordVisibility[account.id] ? 'eye-slash' : 'eye'],\n        class: \"text-lg\"\n      }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_31)])]), accountIndex === 0 ? (_openBlock(), _createElementBlock(\"td\", {\n        key: 2,\n        class: \"px-6 py-4 whitespace-nowrap\",\n        rowspan: accountIndex === 0 ? host.accounts.length : 1\n      }, [_createVNode(_component_StatusBadge, {\n        type: host.status\n      }, null, 8 /* PROPS */, [\"type\"])], 8 /* PROPS */, _hoisted_32)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"td\", _hoisted_33, [_createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.openChangePasswordModal(host, account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'key'],\n        class: \"mr-1\"\n      }), _cache[51] || (_cache[51] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_34)])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))], 64 /* STABLE_FRAGMENT */);\n  }), 128 /* KEYED_FRAGMENT */))])])])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 卡片视图 \"), _createElementVNode(\"div\", _hoisted_35, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredHosts, host => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: host.id,\n      class: \"bg-white overflow-hidden shadow rounded-lg\"\n    }, [_createElementVNode(\"div\", _hoisted_36, [_createCommentVNode(\" 主机头部 \"), _createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_createVNode(_component_CustomCheckbox, {\n      modelValue: host.selected,\n      \"onUpdate:modelValue\": $event => host.selected = $event,\n      class: \"mr-2\"\n    }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"]), _createElementVNode(\"div\", null, [_createElementVNode(\"h3\", _hoisted_39, _toDisplayString(host.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_40, _toDisplayString(host.ip), 1 /* TEXT */)])]), _createVNode(_component_StatusBadge, {\n      type: host.status\n    }, null, 8 /* PROPS */, [\"type\"])]), _createCommentVNode(\" 密码展示 \"), _createElementVNode(\"div\", _hoisted_41, [_cache[52] || (_cache[52] = _createElementVNode(\"div\", {\n      class: \"text-sm font-medium text-gray-500 mb-1\"\n    }, \"密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_42, [_createElementVNode(\"input\", {\n      type: $data.passwordVisibility[host.id] ? 'text' : 'password',\n      value: host.password,\n      readonly: \"\",\n      class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n    }, null, 8 /* PROPS */, _hoisted_43), _createElementVNode(\"button\", {\n      onClick: $event => $options.togglePasswordVisibility(host.id),\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility[host.id] ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_44)])]), _createCommentVNode(\" 密码信息区域 \"), _createElementVNode(\"div\", _hoisted_45, [_createElementVNode(\"div\", null, [_cache[53] || (_cache[53] = _createElementVNode(\"div\", {\n      class: \"text-sm font-medium text-gray-500 mb-1\"\n    }, \"最后修改时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_46, _toDisplayString(host.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[54] || (_cache[54] = _createElementVNode(\"div\", {\n      class: \"text-sm font-medium text-gray-500 mb-1\"\n    }, \"密码过期\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n      class: _normalizeClass({\n        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\n        'bg-red-100 text-red-800': $options.isPasswordExpired(host).status === 'danger' || $options.isPasswordExpired(host).status === 'expired',\n        'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(host).status === 'warning',\n        'bg-gray-100 text-gray-800': $options.isPasswordExpired(host).status === 'normal'\n      })\n    }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(host).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(host).status === 'expired' || $options.isPasswordExpired(host).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_47, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'exclamation-triangle']\n    })])) : $options.isPasswordExpired(host).status === 'warning' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_48, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'exclamation-circle']\n    })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)])]), _createCommentVNode(\" 操作按钮 \"), _createElementVNode(\"div\", _hoisted_49, [_createElementVNode(\"button\", {\n      class: \"w-full inline-flex justify-center items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n      onClick: $event => $options.openChangePasswordModal(host)\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'key'],\n      class: \"mr-2\"\n    }), _cache[55] || (_cache[55] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_50)])])]);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 修改密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.changePasswordModal.show,\n    \"onUpdate:modelValue\": _cache[18] || (_cache[18] = $event => $data.changePasswordModal.show = $event),\n    title: \"修改密码\",\n    \"confirm-text\": \"确认更新\",\n    onConfirm: $options.updatePassword,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_51, [_createElementVNode(\"label\", _hoisted_52, \"服务器: \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_53, [_cache[58] || (_cache[58] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_54, [_createElementVNode(\"label\", _hoisted_55, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.changePasswordModal.method = $event),\n      value: \"auto\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.changePasswordModal.method]]), _cache[56] || (_cache[56] = _createElementVNode(\"span\", null, \"自动生成\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_56, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.changePasswordModal.method = $event),\n      value: \"manual\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.changePasswordModal.method]]), _cache[57] || (_cache[57] = _createElementVNode(\"span\", null, \"手动输入\", -1 /* HOISTED */))])])]), $data.changePasswordModal.method === 'auto' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_57, [_createElementVNode(\"div\", _hoisted_58, [_cache[59] || (_cache[59] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.changePasswordModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_59);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.changePasswordModal.policyId]])]), _createElementVNode(\"div\", _hoisted_60, [_cache[60] || (_cache[60] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_61, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $data.changePasswordModal.generatedPassword = $event),\n      class: \"form-control rounded-r-none\",\n      readonly: \"\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.changePasswordModal.generatedPassword]]), _createElementVNode(\"button\", {\n      class: \"bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded-r-md\",\n      onClick: _cache[12] || (_cache[12] = (...args) => $options.generatePassword && $options.generatePassword(...args))\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt']\n    })])]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.generatedPassword\n    }, null, 8 /* PROPS */, [\"password\"])])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_62, [_createElementVNode(\"div\", _hoisted_63, [_cache[61] || (_cache[61] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"新密码\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"password\",\n      \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.changePasswordModal.newPassword = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.changePasswordModal.newPassword]]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.newPassword\n    }, null, 8 /* PROPS */, [\"password\"])]), _createElementVNode(\"div\", _hoisted_64, [_cache[62] || (_cache[62] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"确认密码\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"password\",\n      \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.changePasswordModal.confirmPassword = $event),\n      class: _normalizeClass([\"form-control\", {\n        'border-red-500': $options.passwordMismatch\n      }])\n    }, null, 2 /* CLASS */), [[_vModelText, $data.changePasswordModal.confirmPassword]]), $options.passwordMismatch ? (_openBlock(), _createElementBlock(\"div\", _hoisted_65, \" 两次输入的密码不一致 \")) : _createCommentVNode(\"v-if\", true)])])), _createElementVNode(\"div\", _hoisted_66, [_cache[66] || (_cache[66] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.executeImmediately,\n      \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => $data.changePasswordModal.executeImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[63] || (_cache[63] = [_createTextVNode(\" 立即执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.saveHistory,\n      \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.changePasswordModal.saveHistory = $event)\n    }, {\n      default: _withCtx(() => _cache[64] || (_cache[64] = [_createTextVNode(\" 保存密码历史记录 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.logAudit,\n      \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.changePasswordModal.logAudit = $event)\n    }, {\n      default: _withCtx(() => _cache[65] || (_cache[65] = [_createTextVNode(\" 记录审计日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量更新密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchUpdateModal.show,\n    \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $data.batchUpdateModal.show = $event),\n    title: \"批量更新密码\",\n    \"confirm-text\": \"开始更新\",\n    size: \"lg\",\n    onConfirm: $options.batchUpdatePasswords,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_67, [_cache[68] || (_cache[68] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_68, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatch,\n      \"onUpdate:modelValue\": [_cache[19] || (_cache[19] = $event => $data.selectAllBatch = $event), $options.toggleSelectAllBatch]\n    }, {\n      default: _withCtx(() => _cache[67] || (_cache[67] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_69, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchUpdateModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchUpdateModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_70, \"已选择 \" + _toDisplayString($options.selectedHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_71, [_cache[69] || (_cache[69] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $data.batchUpdateModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_72);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchUpdateModal.policyId]])]), _createElementVNode(\"div\", _hoisted_73, [_cache[74] || (_cache[74] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_74, [_createElementVNode(\"label\", _hoisted_75, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[21] || (_cache[21] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"immediate\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[70] || (_cache[70] = _createElementVNode(\"span\", null, \"立即执行\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_76, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"scheduled\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[71] || (_cache[71] = _createElementVNode(\"span\", null, \"定时执行\", -1 /* HOISTED */))])]), $data.batchUpdateModal.executionTime === 'scheduled' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_77, [_createElementVNode(\"div\", _hoisted_78, [_createElementVNode(\"div\", null, [_cache[72] || (_cache[72] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"日期\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"date\",\n      \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.batchUpdateModal.scheduledDate = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledDate]])]), _createElementVNode(\"div\", null, [_cache[73] || (_cache[73] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"时间\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"time\",\n      \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.batchUpdateModal.scheduledTime = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledTime]])])])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_79, [_cache[78] || (_cache[78] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.batchUpdateModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[75] || (_cache[75] = [_createTextVNode(\" 忽略错误继续执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.detailedLog,\n      \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.batchUpdateModal.detailedLog = $event)\n    }, {\n      default: _withCtx(() => _cache[76] || (_cache[76] = [_createTextVNode(\" 记录详细日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.sendNotification,\n      \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.batchUpdateModal.sendNotification = $event)\n    }, {\n      default: _withCtx(() => _cache[77] || (_cache[77] = [_createTextVNode(\" 执行完成后发送通知 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量应用策略弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchApplyModal.show,\n    \"onUpdate:modelValue\": _cache[32] || (_cache[32] = $event => $data.batchApplyModal.show = $event),\n    title: \"批量应用密码策略\",\n    \"confirm-text\": \"应用策略\",\n    onConfirm: $options.batchApplyPolicy,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_80, [_cache[79] || (_cache[79] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_81, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchApplyModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchApplyModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_82, [_cache[80] || (_cache[80] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[29] || (_cache[29] = $event => $data.batchApplyModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name), 9 /* TEXT, PROPS */, _hoisted_83);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchApplyModal.policyId]])]), _createElementVNode(\"div\", _hoisted_84, [_cache[83] || (_cache[83] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.updateImmediately,\n      \"onUpdate:modelValue\": _cache[30] || (_cache[30] = $event => $data.batchApplyModal.updateImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[81] || (_cache[81] = [_createTextVNode(\" 立即更新密码以符合策略 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.applyOnNextUpdate,\n      \"onUpdate:modelValue\": _cache[31] || (_cache[31] = $event => $data.batchApplyModal.applyOnNextUpdate = $event)\n    }, {\n      default: _withCtx(() => _cache[82] || (_cache[82] = [_createTextVNode(\" 下次密码更新时应用 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 紧急重置密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.emergencyResetModal.show,\n    \"onUpdate:modelValue\": _cache[36] || (_cache[36] = $event => $data.emergencyResetModal.show = $event),\n    title: \"紧急密码重置\",\n    \"confirm-text\": \"立即重置\",\n    icon: \"exclamation-triangle\",\n    danger: \"\",\n    onConfirm: $options.emergencyReset,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_cache[89] || (_cache[89] = _createElementVNode(\"div\", {\n      class: \"bg-red-50 text-red-700 p-3 rounded-md mb-4\"\n    }, [_createElementVNode(\"p\", null, \"紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_85, [_cache[84] || (_cache[84] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_86, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.emergencyResetModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.emergencyResetModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_87, [_cache[85] || (_cache[85] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用紧急策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[33] || (_cache[33] = $event => $data.emergencyResetModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.emergencyPolicies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_88);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.policyId]])]), _createElementVNode(\"div\", _hoisted_89, [_cache[87] || (_cache[87] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"操作原因\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[34] || (_cache[34] = $event => $data.emergencyResetModal.reason = $event),\n      class: \"form-select\"\n    }, _cache[86] || (_cache[86] = [_createElementVNode(\"option\", {\n      value: \"security_incident\"\n    }, \"安全事件响应\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"password_leak\"\n    }, \"密码泄露\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"abnormal_access\"\n    }, \"异常访问\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"compliance\"\n    }, \"合规要求\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"other\"\n    }, \"其他原因\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.reason]])]), _createElementVNode(\"div\", _hoisted_90, [_cache[88] || (_cache[88] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"附加说明\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n      \"onUpdate:modelValue\": _cache[35] || (_cache[35] = $event => $data.emergencyResetModal.description = $event),\n      class: \"form-control\",\n      rows: \"2\",\n      placeholder: \"请输入重置原因详细说明\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.emergencyResetModal.description]])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "scope", "_createElementBlock", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "args", "$options", "showEmergencyReset", "_createVNode", "_component_font_awesome_icon", "icon", "openBatchUpdateModal", "openBatchApplyModal", "_hoisted_4", "_hoisted_5", "_normalizeClass", "$data", "viewMode", "$event", "_createTextVNode", "_hoisted_6", "type", "filterText", "placeholder", "_hoisted_7", "statusFilter", "value", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_component_CustomCheckbox", "modelValue", "selectAll", "toggleSelectAll", "default", "_withCtx", "_", "_hoisted_12", "_Fragment", "_renderList", "filteredHosts", "host", "hostIndex", "id", "accounts", "account", "accountIndex", "rowspan", "length", "_hoisted_14", "selected", "_hoisted_15", "_toDisplayString", "name", "_hoisted_13", "_hoisted_17", "ip", "_hoisted_16", "_hoisted_18", "_hoisted_19", "_hoisted_20", "username", "isDefault", "_hoisted_21", "_hoisted_22", "_hoisted_23", "lastPasswordChange", "_hoisted_24", "isPasswordExpired", "status", "text", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "passwordVisibility", "password", "readonly", "_hoisted_30", "togglePasswordVisibility", "_hoisted_31", "_component_StatusBadge", "_hoisted_32", "_hoisted_33", "openChangePasswordModal", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_hoisted_43", "_hoisted_44", "_hoisted_45", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_hoisted_50", "_component_BaseModal", "changePasswordModal", "show", "title", "onConfirm", "updatePassword", "loading", "processing", "_hoisted_51", "_hoisted_52", "currentHost", "_hoisted_53", "_hoisted_54", "_hoisted_55", "method", "_hoisted_56", "_hoisted_57", "_hoisted_58", "policyId", "_ctx", "policies", "policy", "<PERSON><PERSON><PERSON><PERSON>", "expiryDays", "_hoisted_59", "_hoisted_60", "_hoisted_61", "generatedPassword", "generatePassword", "_component_PasswordStrengthMeter", "_hoisted_62", "_hoisted_63", "newPassword", "_hoisted_64", "confirmPassword", "passwordMismatch", "_hoisted_65", "_hoisted_66", "executeImmediately", "saveHistory", "logAudit", "batchUpdateModal", "size", "batchUpdatePasswords", "_hoisted_67", "_hoisted_68", "selectAllBatch", "toggleSelectAllBatch", "_hoisted_69", "hosts", "_createBlock", "selectedHosts", "_hoisted_70", "selectedHostsCount", "_hoisted_71", "_hoisted_72", "_hoisted_73", "_hoisted_74", "_hoisted_75", "executionTime", "_hoisted_76", "_hoisted_77", "_hoisted_78", "scheduledDate", "scheduledTime", "_hoisted_79", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "batchApplyPolicy", "_hoisted_80", "_hoisted_81", "selectedHostsList", "_hoisted_82", "_hoisted_83", "_hoisted_84", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "danger", "emergencyReset", "_hoisted_85", "_hoisted_86", "_hoisted_87", "emergencyPolicies", "_hoisted_88", "_hoisted_89", "reason", "_hoisted_90", "description", "rows"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"bg-white shadow rounded-lg p-4 mb-6\">\r\n      <div class=\"flex flex-wrap items-center justify-between\">\r\n        <div class=\"flex space-x-3 mb-2 sm:mb-0\">\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\r\n            @click=\"showEmergencyReset\">\r\n            <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2\" />\r\n            <span>紧急重置</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            @click=\"openBatchUpdateModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n            <span>批量更新密码</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\r\n            @click=\"openBatchApplyModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n            <span>批量应用策略</span>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"flex items-center space-x-4\">\r\n          <!-- 视图切换 -->\r\n          <div class=\"flex items-center border rounded-md overflow-hidden\">\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'table', 'bg-gray-100 text-gray-600': viewMode !== 'table' }\"\r\n              @click=\"viewMode = 'table'\">\r\n              <font-awesome-icon :icon=\"['fas', 'table']\" class=\"mr-1\" />\r\n              表格\r\n            </button>\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'card', 'bg-gray-100 text-gray-600': viewMode !== 'card' }\"\r\n              @click=\"viewMode = 'card'\">\r\n              <font-awesome-icon :icon=\"['fas', 'th-large']\" class=\"mr-1\" />\r\n              卡片\r\n            </button>\r\n          </div>\r\n\r\n          <!-- 筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"filterText\" placeholder=\"筛选主机...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 状态筛选 -->\r\n          <select v-model=\"statusFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有状态</option>\r\n            <option value=\"normal\">正常</option>\r\n            <option value=\"warning\">警告</option>\r\n            <option value=\"error\">错误</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <!-- 表格视图 -->\r\n    <div v-if=\"viewMode === 'table'\" class=\"bg-white rounded-lg shadow overflow-hidden\">\r\n      <table class=\"min-w-full divide-y divide-gray-200\">\r\n        <thead class=\"bg-gray-50\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n                主机名\r\n              </CustomCheckbox>\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              IP地址\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              账号\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              最后密码修改时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码过期时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              状态\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              操作\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"bg-white divide-y divide-gray-200\">\r\n          <template v-for=\"(host, hostIndex) in filteredHosts\" :key=\"host.id\">\r\n            <template v-for=\"(account, accountIndex) in host.accounts\" :key=\"account.id\">\r\n              <tr :class=\"{\r\n                'bg-gray-50': (hostIndex + accountIndex) % 2 === 0,\r\n                'hover:bg-blue-50': true,\r\n                'border-t-2 border-indigo-100': accountIndex === 0\r\n              }\">\r\n                <td class=\"px-6 py-4 whitespace-nowrap\" :rowspan=\"accountIndex === 0 ? host.accounts.length : 1\"\r\n                  v-if=\"accountIndex === 0\">\r\n                  <div class=\"flex items-center\">\r\n                    <CustomCheckbox v-model=\"host.selected\">\r\n                      <span class=\"ml-2 font-medium text-gray-900\">{{ host.name }}</span>\r\n                    </CustomCheckbox>\r\n                  </div>\r\n                </td>\r\n                <td class=\"px-6 py-4 whitespace-nowrap\" :rowspan=\"accountIndex === 0 ? host.accounts.length : 1\"\r\n                  v-if=\"accountIndex === 0\">\r\n                  <div class=\"text-sm text-gray-900\">{{ host.ip }}</div>\r\n                </td>\r\n                <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                  <div class=\"flex items-center\">\r\n                    <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                    <span v-if=\"account.isDefault\"\r\n                      class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                      默认\r\n                    </span>\r\n                  </div>\r\n                </td>\r\n                <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                  <div class=\"text-sm text-gray-500\">{{ account.lastPasswordChange || '-' }}</div>\r\n                </td>\r\n                <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                  <div :class=\"{\r\n                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\r\n                    'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                    'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                    'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                  }\">\r\n                    {{ isPasswordExpired(account).text }}\r\n                    <span\r\n                      v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                      class=\"ml-1\">\r\n                      <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                    </span>\r\n                    <span v-else-if=\"isPasswordExpired(account).status === 'warning'\" class=\"ml-1\">\r\n                      <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" />\r\n                    </span>\r\n                  </div>\r\n                </td>\r\n                <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                  <div class=\"flex items-center\">\r\n                    <div class=\"flex-grow\">\r\n                      <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\"\r\n                        readonly\r\n                        class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                    </div>\r\n                    <button @click=\"togglePasswordVisibility(account.id)\"\r\n                      class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                      <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                        class=\"text-lg\" />\r\n                    </button>\r\n                  </div>\r\n                </td>\r\n                <td class=\"px-6 py-4 whitespace-nowrap\" :rowspan=\"accountIndex === 0 ? host.accounts.length : 1\"\r\n                  v-if=\"accountIndex === 0\">\r\n                  <StatusBadge :type=\"host.status\" />\r\n                </td>\r\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"openChangePasswordModal(host, account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                    修改密码\r\n                  </button>\r\n                </td>\r\n              </tr>\r\n            </template>\r\n          </template>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <!-- 卡片视图 -->\r\n    <div v-else class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\">\r\n      <div v-for=\"host in filteredHosts\" :key=\"host.id\" class=\"bg-white overflow-hidden shadow rounded-lg\">\r\n        <div class=\"px-4 py-5 sm:p-6\">\r\n          <!-- 主机头部 -->\r\n          <div class=\"flex justify-between items-start mb-4\">\r\n            <div class=\"flex items-center\">\r\n              <CustomCheckbox v-model=\"host.selected\" class=\"mr-2\" />\r\n              <div>\r\n                <h3 class=\"text-lg font-medium text-gray-900\">{{ host.name }}</h3>\r\n                <p class=\"text-sm text-gray-500\">{{ host.ip }}</p>\r\n              </div>\r\n            </div>\r\n            <StatusBadge :type=\"host.status\" />\r\n          </div>\r\n\r\n          <!-- 密码展示 -->\r\n          <div class=\"mb-4\">\r\n            <div class=\"text-sm font-medium text-gray-500 mb-1\">密码</div>\r\n            <div class=\"flex items-center\">\r\n              <input :type=\"passwordVisibility[host.id] ? 'text' : 'password'\" :value=\"host.password\" readonly\r\n                class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n              <button @click=\"togglePasswordVisibility(host.id)\"\r\n                class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                <font-awesome-icon :icon=\"['fas', passwordVisibility[host.id] ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 密码信息区域 -->\r\n          <div class=\"grid grid-cols-2 gap-4 mb-4\">\r\n            <div>\r\n              <div class=\"text-sm font-medium text-gray-500 mb-1\">最后修改时间</div>\r\n              <div class=\"text-sm text-gray-900\">{{ host.lastPasswordChange || '-' }}</div>\r\n            </div>\r\n            <div>\r\n              <div class=\"text-sm font-medium text-gray-500 mb-1\">密码过期</div>\r\n              <div :class=\"{\r\n                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\r\n                'bg-red-100 text-red-800': isPasswordExpired(host).status === 'danger' || isPasswordExpired(host).status === 'expired',\r\n                'bg-yellow-100 text-yellow-800': isPasswordExpired(host).status === 'warning',\r\n                'bg-gray-100 text-gray-800': isPasswordExpired(host).status === 'normal'\r\n              }\">\r\n                {{ isPasswordExpired(host).text }}\r\n                <span v-if=\"isPasswordExpired(host).status === 'expired' || isPasswordExpired(host).status === 'danger'\"\r\n                  class=\"ml-1\">\r\n                  <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                </span>\r\n                <span v-else-if=\"isPasswordExpired(host).status === 'warning'\" class=\"ml-1\">\r\n                  <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" />\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 操作按钮 -->\r\n          <div class=\"mt-4\">\r\n            <button\r\n              class=\"w-full inline-flex justify-center items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n              @click=\"openChangePasswordModal(host)\">\r\n              <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n              修改密码\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" confirm-text=\"确认更新\" @confirm=\"updatePassword\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">服务器: {{ currentHost.name }}</label>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"changePasswordModal.method\" value=\"auto\" class=\"mr-2\">\r\n            <span>自动生成</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"changePasswordModal.method\" value=\"manual\" class=\"mr-2\">\r\n            <span>手动输入</span>\r\n          </label>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-if=\"changePasswordModal.method === 'auto'\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">选择密码策略</label>\r\n          <select v-model=\"changePasswordModal.policyId\" class=\"form-select\">\r\n            <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n              {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n            </option>\r\n          </select>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <div class=\"flex\">\r\n            <input type=\"text\" v-model=\"changePasswordModal.generatedPassword\" class=\"form-control rounded-r-none\"\r\n              readonly>\r\n            <button class=\"bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded-r-md\" @click=\"generatePassword\">\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" />\r\n            </button>\r\n          </div>\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.generatedPassword\" />\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else>\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <input type=\"password\" v-model=\"changePasswordModal.newPassword\" class=\"form-control\">\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <input type=\"password\" v-model=\"changePasswordModal.confirmPassword\" class=\"form-control\"\r\n            :class=\"{ 'border-red-500': passwordMismatch }\">\r\n          <div v-if=\"passwordMismatch\" class=\"text-red-500 text-xs mt-1\">\r\n            两次输入的密码不一致\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行选项</label>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          立即执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          保存密码历史记录\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          记录审计日志\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      passwordVisibility: {},\r\n      viewMode: 'table',\r\n      filterText: '',\r\n      statusFilter: 'all',\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    },\r\n\r\n    // 过滤后的主机列表\r\n    filteredHosts() {\r\n      return this.hosts.filter(host => {\r\n        // 文本过滤\r\n        const textMatch = this.filterText === '' ||\r\n          host.name.toLowerCase().includes(this.filterText.toLowerCase()) ||\r\n          host.ip.includes(this.filterText);\r\n\r\n        // 状态过滤\r\n        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;\r\n\r\n        return textMatch && statusMatch;\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host) {\r\n      this.currentHost = host\r\n      this.changePasswordModal.show = true\r\n      this.changePasswordModal.generatedPassword = this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword(policy) {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policyObj ? policyObj.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      if (this.changePasswordModal && !policy) {\r\n        this.changePasswordModal.generatedPassword = password\r\n      }\r\n\r\n      return password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto'\r\n          ? this.changePasswordModal.generatedPassword\r\n          : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          password: password,\r\n          policyId: this.changePasswordModal.policyId\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取所选策略\r\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)\r\n\r\n        // 为每台主机生成并更新密码\r\n        await Promise.all(\r\n          selectedHostIds.map(async (hostId) => {\r\n            const newPassword = this.generatePassword(policy)\r\n            return this.$store.dispatch('updateHostPassword', {\r\n              hostId: hostId,\r\n              password: newPassword,\r\n              policyId: policy.id\r\n            })\r\n          })\r\n        )\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取紧急策略\r\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)\r\n\r\n        // 为每台主机生成并更新密码\r\n        await Promise.all(\r\n          selectedHostIds.map(async (hostId) => {\r\n            const newPassword = this.generatePassword(policy)\r\n            return this.$store.dispatch('updateHostPassword', {\r\n              hostId: hostId,\r\n              password: newPassword,\r\n              policyId: policy.id\r\n            })\r\n          })\r\n        )\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    togglePasswordVisibility(hostId) {\r\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]\r\n    },\r\n\r\n    isPasswordExpired(host) {\r\n      if (!host.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }\r\n\r\n      // 解析过期时间\r\n      const expiryDate = new Date(host.passwordExpiryDate)\r\n      const now = new Date()\r\n\r\n      // 如果已过期\r\n      if (expiryDate < now) {\r\n        return {\r\n          status: 'expired',\r\n          days: 0,\r\n          text: '已过期'\r\n        }\r\n      }\r\n\r\n      // 计算剩余天数和小时数\r\n      const diffTime = expiryDate - now\r\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))\r\n      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n\r\n      // 根据剩余时间确定状态\r\n      let status = 'normal'\r\n      if (diffDays < 7) {\r\n        status = 'danger'  // 少于7天\r\n      } else if (diffDays < 14) {\r\n        status = 'warning' // 少于14天\r\n      }\r\n\r\n      // 格式化显示文本\r\n      let text = ''\r\n      if (diffDays > 0) {\r\n        text += `${diffDays}天`\r\n      }\r\n      if (diffHours > 0 || diffDays === 0) {\r\n        text += `${diffHours}小时`\r\n      }\r\n\r\n      return { status, days: diffDays, text: `剩余${text}` }\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": ";;EAGSA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAA6C;;EACjDA,KAAK,EAAC;AAA6B;;EAqBnCA,KAAK,EAAC;AAA6B;;EAEjCA,KAAK,EAAC;AAAqD;;EAgB3DA,KAAK,EAAC;AAAU;;EAGdA,KAAK,EAAC;AAAsE;;EA/C7FC,GAAA;EAkEqCD,KAAK,EAAC;;;EAC9BA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAAY;;EAEjBE,KAAK,EAAC,KAAK;EAACF,KAAK,EAAC;;;EA4BnBA,KAAK,EAAC;AAAmC;oBAlGxD;;EA4GuBA,KAAK,EAAC;AAAmB;;EAEpBA,KAAK,EAAC;AAAgC;oBA9GlE;;EAoHuBA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAmC;;EAxHnEC,GAAA;EA0HsBD,KAAK,EAAC;;;EAKRA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAlIvDC,GAAA;EA4IsBD,KAAK,EAAC;;;EA5I5BC,GAAA;EA+IsFD,KAAK,EAAC;;;EAKxEA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAW;oBAtJ1C;oBAAA;oBAAA;;EAsKoBA,KAAK,EAAC;AAAmD;oBAtK7E;;EAsLgBA,KAAK,EAAC;AAAsD;;EAE/DA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAuC;;EAC3CA,KAAK,EAAC;AAAmB;;EAGtBA,KAAK,EAAC;AAAmC;;EAC1CA,KAAK,EAAC;AAAuB;;EAOjCA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAmB;oBAxM1C;oBAAA;;EAmNeA,KAAK,EAAC;AAA6B;;EAG/BA,KAAK,EAAC;AAAuB;;EAtNhDC,GAAA;EAkOkBD,KAAK,EAAC;;;EAlOxBC,GAAA;EAqO+ED,KAAK,EAAC;;;EAQtEA,KAAK,EAAC;AAAM;oBA7O3B;;EA4PWA,KAAK,EAAC;AAAY;;EACdA,KAAK,EAAC;AAAY;;EAGtBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EAvQ1CC,GAAA;AAAA;;EA+QaD,KAAK,EAAC;AAAY;oBA/Q/B;;EAwRaA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EA1R3BC,GAAA;AAAA;;EAsSaD,KAAK,EAAC;AAAY;;EAMlBA,KAAK,EAAC;AAAY;;EA5S/BC,GAAA;EAgTuCD,KAAK,EAAC;;;EAMlCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAY;oBAtV7B;;EA+VWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EAtW1CC,GAAA;EA4WmED,KAAK,EAAC;;;EAC1DA,KAAK,EAAC;AAAwB;;EAalCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;oBArZ7B;;EA8ZWA,KAAK,EAAC;AAAY;;EAkBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;oBA1b7B;;EAmcWA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;;;;;;uBA7c3BG,mBAAA,CAmdM,cAldJC,mBAAA,UAAa,EACbC,mBAAA,CA2DM,OA3DNC,UA2DM,GA1DJD,mBAAA,CAyDM,OAzDNE,UAyDM,GAxDJF,mBAAA,CAmBM,OAnBNG,UAmBM,GAlBJH,mBAAA,CAKS;IAJPL,KAAK,EAAC,qNAAqN;IAC1NS,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,kBAAA,IAAAD,QAAA,CAAAC,kBAAA,IAAAF,IAAA,CAAkB;MAC1BG,YAAA,CAA0EC,4BAAA;IAAtDC,IAAI,EAAE,+BAA+B;IAAEhB,KAAK,EAAC;kCACjEK,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAKS;IAJPL,KAAK,EAAC,wNAAwN;IAC7NS,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAK,oBAAA,IAAAL,QAAA,CAAAK,oBAAA,IAAAN,IAAA,CAAoB;MAC5BG,YAAA,CAAyDC,4BAAA;IAArCC,IAAI,EAAE,cAAc;IAAEhB,KAAK,EAAC;kCAChDK,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAKS;IAJPL,KAAK,EAAC,8NAA8N;IACnOS,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAM,mBAAA,IAAAN,QAAA,CAAAM,mBAAA,IAAAP,IAAA,CAAmB;MAC3BG,YAAA,CAAgEC,4BAAA;IAA5CC,IAAI,EAAE,qBAAqB;IAAEhB,KAAK,EAAC;kCACvDK,mBAAA,CAAmB,cAAb,QAAM,qB,KAIhBA,mBAAA,CAkCM,OAlCNc,UAkCM,GAjCJf,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNe,UAaM,GAZJf,mBAAA,CAKS;IALDL,KAAK,EA7BzBqB,eAAA,EA6B0B,8BAA8B;MAAA,0BACNC,KAAA,CAAAC,QAAQ;MAAA,6BAA2CD,KAAA,CAAAC,QAAQ;IAAA;IAC9Fd,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAChBT,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEhB,KAAK,EAAC;kCAhChEyB,gBAAA,CAgCyE,MAE7D,G,kBACApB,mBAAA,CAKS;IALDL,KAAK,EAnCzBqB,eAAA,EAmC0B,8BAA8B;MAAA,0BACNC,KAAA,CAAAC,QAAQ;MAAA,6BAA0CD,KAAA,CAAAC,QAAQ;IAAA;IAC7Fd,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAChBT,YAAA,CAA8DC,4BAAA;IAA1CC,IAAI,EAAE,mBAAmB;IAAEhB,KAAK,EAAC;kCAtCnEyB,gBAAA,CAsC4E,MAEhE,G,oBAGFrB,mBAAA,QAAW,EACXC,mBAAA,CAMM,OANNqB,UAMM,G,gBALJrB,mBAAA,CAC2L;IADpLsB,IAAI,EAAC,MAAM;IA7C9B,uBAAAjB,MAAA,QAAAA,MAAA,MAAAc,MAAA,IA6CwCF,KAAA,CAAAM,UAAU,GAAAJ,MAAA;IAAEK,WAAW,EAAC,SAAS;IAC3D7B,KAAK,EAAC;iDADoBsB,KAAA,CAAAM,UAAU,E,GAEtCvB,mBAAA,CAEM,OAFNyB,UAEM,GADJhB,YAAA,CAAqEC,4BAAA;IAAjDC,IAAI,EAAE,iBAAiB;IAAEhB,KAAK,EAAC;UAIvDI,mBAAA,UAAa,E,gBACbC,mBAAA,CAMS;IA3DnB,uBAAAK,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAqD2BF,KAAA,CAAAS,YAAY,GAAAP,MAAA;IAC3BxB,KAAK,EAAC;kCACNK,mBAAA,CAAiC;IAAzB2B,KAAK,EAAC;EAAK,GAAC,MAAI,qBACxB3B,mBAAA,CAAkC;IAA1B2B,KAAK,EAAC;EAAQ,GAAC,IAAE,qBACzB3B,mBAAA,CAAmC;IAA3B2B,KAAK,EAAC;EAAS,GAAC,IAAE,qBAC1B3B,mBAAA,CAAiC;IAAzB2B,KAAK,EAAC;EAAO,GAAC,IAAE,oB,2CALTV,KAAA,CAAAS,YAAY,E,SAWnC3B,mBAAA,UAAa,EACbA,mBAAA,UAAa,EACFkB,KAAA,CAAAC,QAAQ,gB,cAAnBpB,mBAAA,CAiHM,OAjHN8B,UAiHM,GAhHJ5B,mBAAA,CA+GQ,SA/GR6B,UA+GQ,GA9GN7B,mBAAA,CA6BQ,SA7BR8B,WA6BQ,GA5BN9B,mBAAA,CA2BK,aA1BHA,mBAAA,CAIK,MAJL+B,WAIK,GAHHtB,YAAA,CAEiBuB,yBAAA;IAzE/BC,UAAA,EAuEuChB,KAAA,CAAAiB,SAAS;IAvEhD,wB,oCAuEuCjB,KAAA,CAAAiB,SAAS,GAAAf,MAAA,GAAsBZ,QAAA,CAAA4B,eAAe;;IAvErFC,OAAA,EAAAC,QAAA,CAuEuF,MAEzEhC,MAAA,SAAAA,MAAA,QAzEde,gBAAA,CAuEuF,OAEzE,E;IAzEdkB,CAAA;0FA2EYtC,mBAAA,CAEK;IAFDH,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,QAEvG,sB,4BACAK,mBAAA,CAEK;IAFDH,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAK,mBAAA,CAEK;IAFDH,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,YAEvG,sB,4BACAK,mBAAA,CAEK;IAFDH,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,UAEvG,sB,4BACAK,mBAAA,CAEK;IAFDH,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAK,mBAAA,CAEK;IAFDH,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAK,mBAAA,CAEK;IAFDH,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,qB,KAGJK,mBAAA,CA+EQ,SA/ERuC,WA+EQ,I,kBA9ENzC,mBAAA,CA6EW0C,SAAA,QAhLrBC,WAAA,CAmGgDlC,QAAA,CAAAmC,aAAa,EAnG7D,CAmG4BC,IAAI,EAAEC,SAAS;yBAnG3C9C,mBAAA,CAAA0C,SAAA;MAAA5C,GAAA,EAmGqE+C,IAAI,CAACE;2BAC9D/C,mBAAA,CA2EW0C,SAAA,QA/KvBC,WAAA,CAoGwDE,IAAI,CAACG,QAAQ,EApGrE,CAoG8BC,OAAO,EAAEC,YAAY;2BACrClD,mBAAA,CAyEK;QA9KnBF,GAAA,EAoG6EmD,OAAO,CAACF,EAAE;QACpElD,KAAK,EArGxBqB,eAAA;yBAqG4D4B,SAAS,GAAGI,YAAY;;0CAAyGA,YAAY;;UAMjLA,YAAY,U,cADpBlD,mBAAA,CAOK;QAjHrBF,GAAA;QA0GoBD,KAAK,EAAC,6BAA6B;QAAEsD,OAAO,EAAED,YAAY,SAASL,IAAI,CAACG,QAAQ,CAACI,MAAM;UAEzFlD,mBAAA,CAIM,OAJNmD,WAIM,GAHJ1C,YAAA,CAEiBuB,yBAAA;QA/GrCC,UAAA,EA6G6CU,IAAI,CAACS,QAAQ;QA7G1D,uBAAAjC,MAAA,IA6G6CwB,IAAI,CAACS,QAAQ,GAAAjC;;QA7G1DiB,OAAA,EAAAC,QAAA,CA8GsB,MAAmE,CAAnErC,mBAAA,CAAmE,QAAnEqD,WAAmE,EAAAC,gBAAA,CAAnBX,IAAI,CAACY,IAAI,iB;QA9G/EjB,CAAA;oGAAAkB,WAAA,KAAAzD,mBAAA,gBAmHwBiD,YAAY,U,cADpBlD,mBAAA,CAGK;QArHrBF,GAAA;QAkHoBD,KAAK,EAAC,6BAA6B;QAAEsD,OAAO,EAAED,YAAY,SAASL,IAAI,CAACG,QAAQ,CAACI,MAAM;UAEzFlD,mBAAA,CAAsD,OAAtDyD,WAAsD,EAAAH,gBAAA,CAAhBX,IAAI,CAACe,EAAE,iB,iBApH/DC,WAAA,KAAA5D,mBAAA,gBAsHgBC,mBAAA,CAQK,MARL4D,WAQK,GAPH5D,mBAAA,CAMM,OANN6D,WAMM,GALJ7D,mBAAA,CAA6E,QAA7E8D,WAA6E,EAAAR,gBAAA,CAA1BP,OAAO,CAACgB,QAAQ,kBACvDhB,OAAO,CAACiB,SAAS,I,cAA7BlE,mBAAA,CAGO,QAHPmE,WAGO,EAFqG,MAE5G,KA5HpBlE,mBAAA,e,KA+HgBC,mBAAA,CAEK,MAFLkE,WAEK,GADHlE,mBAAA,CAAgF,OAAhFmE,WAAgF,EAAAb,gBAAA,CAA1CP,OAAO,CAACqB,kBAAkB,wB,GAElEpE,mBAAA,CAiBK,MAjBLqE,WAiBK,GAhBHrE,mBAAA,CAeM;QAfAL,KAAK,EAnI7BqB,eAAA;;qCAmIuLT,QAAA,CAAA+D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM,iBAAiBhE,QAAA,CAAA+D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM;2CAAsEhE,QAAA,CAAA+D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM;uCAAkEhE,QAAA,CAAA+D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM;;UAnIpdnD,gBAAA,CAAAkC,gBAAA,CAyIuB/C,QAAA,CAAA+D,iBAAiB,CAACvB,OAAO,EAAEyB,IAAI,IAAG,GACrC,iBACQjE,QAAA,CAAA+D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM,kBAAkBhE,QAAA,CAAA+D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM,iB,cAD5FzE,mBAAA,CAIO,QAJP2E,WAIO,GADLhE,YAAA,CAA6DC,4BAAA;QAAzCC,IAAI,EAAE;MAA+B,G,KAE1CJ,QAAA,CAAA+D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM,kB,cAAlDzE,mBAAA,CAEO,QAFP4E,WAEO,GADLjE,YAAA,CAA2DC,4BAAA;QAAvCC,IAAI,EAAE;MAA6B,G,KAhJ7EZ,mBAAA,e,oBAoJgBC,mBAAA,CAaK,MAbL2E,WAaK,GAZH3E,mBAAA,CAWM,OAXN4E,WAWM,GAVJ5E,mBAAA,CAIM,OAJN6E,WAIM,GAHJ7E,mBAAA,CAEkG;QAF1FsB,IAAI,EAAEL,KAAA,CAAA6D,kBAAkB,CAAC/B,OAAO,CAACF,EAAE;QAA0BlB,KAAK,EAAEoB,OAAO,CAACgC,QAAQ;QAC1FC,QAAQ,EAAR,EAAQ;QACRrF,KAAK,EAAC;8BAzJ9BsF,WAAA,E,GA2JoBjF,mBAAA,CAIS;QAJAI,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAA2E,wBAAwB,CAACnC,OAAO,CAACF,EAAE;QACjDlD,KAAK,EAAC;UACNc,YAAA,CACoBC,4BAAA;QADAC,IAAI,UAAUM,KAAA,CAAA6D,kBAAkB,CAAC/B,OAAO,CAACF,EAAE;QAC7DlD,KAAK,EAAC;yDA9J9BwF,WAAA,E,KAmKwBnC,YAAY,U,cADpBlD,mBAAA,CAGK;QArKrBF,GAAA;QAkKoBD,KAAK,EAAC,6BAA6B;QAAEsD,OAAO,EAAED,YAAY,SAASL,IAAI,CAACG,QAAQ,CAACI,MAAM;UAEzFzC,YAAA,CAAmC2E,sBAAA;QAArB9D,IAAI,EAAEqB,IAAI,CAAC4B;yDApK3Cc,WAAA,KAAAtF,mBAAA,gBAsKgBC,mBAAA,CAOK,MAPLsF,WAOK,GANHtF,mBAAA,CAKS;QAJPL,KAAK,EAAC,0NAA0N;QAC/NS,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAAgF,uBAAuB,CAAC5C,IAAI,EAAEI,OAAO;UAC7CtC,YAAA,CAAyDC,4BAAA;QAArCC,IAAI,EAAE,cAAc;QAAEhB,KAAK,EAAC;sCA1KpEyB,gBAAA,CA0K6E,QAE3D,G,iBA5KlBoE,WAAA,E;;yDAsLI1F,mBAAA,CAiEM0C,SAAA;IAvPV5C,GAAA;EAAA,IAqLIG,mBAAA,UAAa,EACbC,mBAAA,CAiEM,OAjENyF,WAiEM,I,kBAhEJ3F,mBAAA,CA+DM0C,SAAA,QAtPZC,WAAA,CAuL0BlC,QAAA,CAAAmC,aAAa,EAArBC,IAAI;yBAAhB7C,mBAAA,CA+DM;MA/D8BF,GAAG,EAAE+C,IAAI,CAACE,EAAE;MAAElD,KAAK,EAAC;QACtDK,mBAAA,CA6DM,OA7DN0F,WA6DM,GA5DJ3F,mBAAA,UAAa,EACbC,mBAAA,CASM,OATN2F,WASM,GARJ3F,mBAAA,CAMM,OANN4F,WAMM,GALJnF,YAAA,CAAuDuB,yBAAA;MA5LrEC,UAAA,EA4LuCU,IAAI,CAACS,QAAQ;MA5LpD,uBAAAjC,MAAA,IA4LuCwB,IAAI,CAACS,QAAQ,GAAAjC,MAAA;MAAExB,KAAK,EAAC;oEAC9CK,mBAAA,CAGM,cAFJA,mBAAA,CAAkE,MAAlE6F,WAAkE,EAAAvC,gBAAA,CAAjBX,IAAI,CAACY,IAAI,kBAC1DvD,mBAAA,CAAkD,KAAlD8F,WAAkD,EAAAxC,gBAAA,CAAdX,IAAI,CAACe,EAAE,iB,KAG/CjD,YAAA,CAAmC2E,sBAAA;MAArB9D,IAAI,EAAEqB,IAAI,CAAC4B;yCAG3BxE,mBAAA,UAAa,EACbC,mBAAA,CAUM,OAVN+F,WAUM,G,4BATJ/F,mBAAA,CAA4D;MAAvDL,KAAK,EAAC;IAAwC,GAAC,IAAE,sBACtDK,mBAAA,CAOM,OAPNgG,WAOM,GANJhG,mBAAA,CACkG;MAD1FsB,IAAI,EAAEL,KAAA,CAAA6D,kBAAkB,CAACnC,IAAI,CAACE,EAAE;MAA0BlB,KAAK,EAAEgB,IAAI,CAACoC,QAAQ;MAAEC,QAAQ,EAAR,EAAQ;MAC9FrF,KAAK,EAAC;4BA1MtBsG,WAAA,GA2McjG,mBAAA,CAGS;MAHAI,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAA2E,wBAAwB,CAACvC,IAAI,CAACE,EAAE;MAC9ClD,KAAK,EAAC;QACNc,YAAA,CAAwGC,4BAAA;MAApFC,IAAI,UAAUM,KAAA,CAAA6D,kBAAkB,CAACnC,IAAI,CAACE,EAAE;MAA0BlD,KAAK,EAAC;uDA7M5GuG,WAAA,E,KAkNUnG,mBAAA,YAAe,EACfC,mBAAA,CAuBM,OAvBNmG,WAuBM,GAtBJnG,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAgE;MAA3DL,KAAK,EAAC;IAAwC,GAAC,QAAM,sBAC1DK,mBAAA,CAA6E,OAA7EoG,WAA6E,EAAA9C,gBAAA,CAAvCX,IAAI,CAACyB,kBAAkB,wB,GAE/DpE,mBAAA,CAiBM,c,4BAhBJA,mBAAA,CAA8D;MAAzDL,KAAK,EAAC;IAAwC,GAAC,MAAI,sBACxDK,mBAAA,CAcM;MAdAL,KAAK,EA1NzBqB,eAAA;;mCA0N2KT,QAAA,CAAA+D,iBAAiB,CAAC3B,IAAI,EAAE4B,MAAM,iBAAiBhE,QAAA,CAAA+D,iBAAiB,CAAC3B,IAAI,EAAE4B,MAAM;yCAAkEhE,QAAA,CAAA+D,iBAAiB,CAAC3B,IAAI,EAAE4B,MAAM;qCAA8DhE,QAAA,CAAA+D,iBAAiB,CAAC3B,IAAI,EAAE4B,MAAM;;QA1NpbnD,gBAAA,CAAAkC,gBAAA,CAgOmB/C,QAAA,CAAA+D,iBAAiB,CAAC3B,IAAI,EAAE6B,IAAI,IAAG,GAClC,iBAAYjE,QAAA,CAAA+D,iBAAiB,CAAC3B,IAAI,EAAE4B,MAAM,kBAAkBhE,QAAA,CAAA+D,iBAAiB,CAAC3B,IAAI,EAAE4B,MAAM,iB,cAA1FzE,mBAAA,CAGO,QAHPuG,WAGO,GADL5F,YAAA,CAA6DC,4BAAA;MAAzCC,IAAI,EAAE;IAA+B,G,KAE1CJ,QAAA,CAAA+D,iBAAiB,CAAC3B,IAAI,EAAE4B,MAAM,kB,cAA/CzE,mBAAA,CAEO,QAFPwG,WAEO,GADL7F,YAAA,CAA2DC,4BAAA;MAAvCC,IAAI,EAAE;IAA6B,G,KAtOzEZ,mBAAA,e,sBA4OUA,mBAAA,UAAa,EACbC,mBAAA,CAOM,OAPNuG,WAOM,GANJvG,mBAAA,CAKS;MAJPL,KAAK,EAAC,gPAAgP;MACrPS,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAAgF,uBAAuB,CAAC5C,IAAI;QACpClC,YAAA,CAAyDC,4BAAA;MAArCC,IAAI,EAAE,cAAc;MAAEhB,KAAK,EAAC;oCAjP9DyB,gBAAA,CAiPuE,QAE3D,G,iBAnPZoF,WAAA,E;sFAyPIzG,mBAAA,YAAe,EACfU,YAAA,CAwEYgG,oBAAA;IAlUhBxE,UAAA,EA0PwBhB,KAAA,CAAAyF,mBAAmB,CAACC,IAAI;IA1PhD,uBAAAtG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA0PwBF,KAAA,CAAAyF,mBAAmB,CAACC,IAAI,GAAAxF,MAAA;IAAEyF,KAAK,EAAC,MAAM;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAEtG,QAAA,CAAAuG,cAAc;IACpGC,OAAO,EAAE9F,KAAA,CAAA+F;;IA3PhB5E,OAAA,EAAAC,QAAA,CA4PM,MAEM,CAFNrC,mBAAA,CAEM,OAFNiH,WAEM,GADJjH,mBAAA,CAA6D,SAA7DkH,WAA6D,EAAnC,OAAK,GAAA5D,gBAAA,CAAGrC,KAAA,CAAAkG,WAAW,CAAC5D,IAAI,iB,GAGpDvD,mBAAA,CAYM,OAZNoH,WAYM,G,4BAXJpH,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CASM,OATNqH,WASM,GARJrH,mBAAA,CAGQ,SAHRsH,WAGQ,G,gBAFNtH,mBAAA,CAAmF;MAA5EsB,IAAI,EAAC,OAAO;MApQ/B,uBAAAjB,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAoQyCF,KAAA,CAAAyF,mBAAmB,CAACa,MAAM,GAAApG,MAAA;MAAEQ,KAAK,EAAC,MAAM;MAAChC,KAAK,EAAC;oDAA/CsB,KAAA,CAAAyF,mBAAmB,CAACa,MAAM,E,+BACvDvH,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHRwH,WAGQ,G,gBAFNxH,mBAAA,CAAqF;MAA9EsB,IAAI,EAAC,OAAO;MAxQ/B,uBAAAjB,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAwQyCF,KAAA,CAAAyF,mBAAmB,CAACa,MAAM,GAAApG,MAAA;MAAEQ,KAAK,EAAC,QAAQ;MAAChC,KAAK,EAAC;oDAAjDsB,KAAA,CAAAyF,mBAAmB,CAACa,MAAM,E,+BACvDvH,mBAAA,CAAiB,cAAX,MAAI,qB,OAKLiB,KAAA,CAAAyF,mBAAmB,CAACa,MAAM,e,cAArCzH,mBAAA,CAqBM,OAnSZ2H,WAAA,GA+QQzH,mBAAA,CAOM,OAPN0H,WAOM,G,4BANJ1H,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCK,mBAAA,CAIS;MArRnB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAiR2BF,KAAA,CAAAyF,mBAAmB,CAACiB,QAAQ,GAAAxG,MAAA;MAAExB,KAAK,EAAC;2BACnDG,mBAAA,CAES0C,SAAA,QApRrBC,WAAA,CAkRqCmF,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArBhI,mBAAA,CAES;QAF2BF,GAAG,EAAEkI,MAAM,CAACjF,EAAE;QAAGlB,KAAK,EAAEmG,MAAM,CAACjF;0BAC9DiF,MAAM,CAACvE,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAGwE,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAzE,gBAAA,CAAGwE,MAAM,CAACE,UAAU,IAAG,KAC9E,uBApRZC,WAAA;6EAiR2BhH,KAAA,CAAAyF,mBAAmB,CAACiB,QAAQ,E,KAO/C3H,mBAAA,CAUM,OAVNkI,WAUM,G,4BATJlI,mBAAA,CAAuC;MAAhCL,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BK,mBAAA,CAMM,OANNmI,WAMM,G,gBALJnI,mBAAA,CACW;MADJsB,IAAI,EAAC,MAAM;MA3R9B,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA2RwCF,KAAA,CAAAyF,mBAAmB,CAAC0B,iBAAiB,GAAAjH,MAAA;MAAExB,KAAK,EAAC,6BAA6B;MACpGqF,QAAQ,EAAR;mDAD0B/D,KAAA,CAAAyF,mBAAmB,CAAC0B,iBAAiB,E,GAEjEpI,mBAAA,CAES;MAFDL,KAAK,EAAC,sDAAsD;MAAES,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEC,QAAA,CAAA8H,gBAAA,IAAA9H,QAAA,CAAA8H,gBAAA,IAAA/H,IAAA,CAAgB;QAC3FG,YAAA,CAAiDC,4BAAA;MAA7BC,IAAI,EAAE;IAAmB,G,KAGjDF,YAAA,CAA2E6H,gCAAA;MAAnDvD,QAAQ,EAAE9D,KAAA,CAAAyF,mBAAmB,CAAC0B;gEAI1DtI,mBAAA,CAeM,OApTZyI,WAAA,GAsSQvI,mBAAA,CAIM,OAJNwI,WAIM,G,4BAHJxI,mBAAA,CAAqC;MAA9BL,KAAK,EAAC;IAAY,GAAC,KAAG,sB,gBAC7BK,mBAAA,CAAsF;MAA/EsB,IAAI,EAAC,UAAU;MAxShC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAwS0CF,KAAA,CAAAyF,mBAAmB,CAAC+B,WAAW,GAAAtH,MAAA;MAAExB,KAAK,EAAC;mDAAvCsB,KAAA,CAAAyF,mBAAmB,CAAC+B,WAAW,E,GAC/DhI,YAAA,CAAqE6H,gCAAA;MAA7CvD,QAAQ,EAAE9D,KAAA,CAAAyF,mBAAmB,CAAC+B;6CAGxDzI,mBAAA,CAOM,OAPN0I,WAOM,G,4BANJ1I,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CACkD;MAD3CsB,IAAI,EAAC,UAAU;MA9ShC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8S0CF,KAAA,CAAAyF,mBAAmB,CAACiC,eAAe,GAAAxH,MAAA;MAAExB,KAAK,EA9SpFqB,eAAA,EA8SqF,cAAc;QAAA,kBAC3DT,QAAA,CAAAqI;MAAgB;4CADd3H,KAAA,CAAAyF,mBAAmB,CAACiC,eAAe,E,GAExDpI,QAAA,CAAAqI,gBAAgB,I,cAA3B9I,mBAAA,CAEM,OAFN+I,WAEM,EAFyD,cAE/D,KAlTV9I,mBAAA,e,MAsTMC,mBAAA,CAWM,OAXN8I,WAWM,G,4BAVJ9I,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Bc,YAAA,CAEiBuB,yBAAA;MA1TzBC,UAAA,EAwTiChB,KAAA,CAAAyF,mBAAmB,CAACqC,kBAAkB;MAxTvE,uBAAA1I,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAwTiCF,KAAA,CAAAyF,mBAAmB,CAACqC,kBAAkB,GAAA5H,MAAA;;MAxTvEiB,OAAA,EAAAC,QAAA,CAwTyE,MAEjEhC,MAAA,SAAAA,MAAA,QA1TRe,gBAAA,CAwTyE,QAEjE,E;MA1TRkB,CAAA;uCA2TQ7B,YAAA,CAEiBuB,yBAAA;MA7TzBC,UAAA,EA2TiChB,KAAA,CAAAyF,mBAAmB,CAACsC,WAAW;MA3ThE,uBAAA3I,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA2TiCF,KAAA,CAAAyF,mBAAmB,CAACsC,WAAW,GAAA7H,MAAA;;MA3ThEiB,OAAA,EAAAC,QAAA,CA2TkE,MAE1DhC,MAAA,SAAAA,MAAA,QA7TRe,gBAAA,CA2TkE,YAE1D,E;MA7TRkB,CAAA;uCA8TQ7B,YAAA,CAEiBuB,yBAAA;MAhUzBC,UAAA,EA8TiChB,KAAA,CAAAyF,mBAAmB,CAACuC,QAAQ;MA9T7D,uBAAA5I,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8TiCF,KAAA,CAAAyF,mBAAmB,CAACuC,QAAQ,GAAA9H,MAAA;;MA9T7DiB,OAAA,EAAAC,QAAA,CA8T+D,MAEvDhC,MAAA,SAAAA,MAAA,QAhURe,gBAAA,CA8T+D,UAEvD,E;MAhURkB,CAAA;;IAAAA,CAAA;6DAoUIvC,mBAAA,cAAiB,EACjBU,YAAA,CAiEYgG,oBAAA;IAtYhBxE,UAAA,EAqUwBhB,KAAA,CAAAiI,gBAAgB,CAACvC,IAAI;IArU7C,uBAAAtG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAqUwBF,KAAA,CAAAiI,gBAAgB,CAACvC,IAAI,GAAAxF,MAAA;IAAEyF,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAACuC,IAAI,EAAC,IAAI;IACpFtC,SAAO,EAAEtG,QAAA,CAAA6I,oBAAoB;IAAGrC,OAAO,EAAE9F,KAAA,CAAA+F;;IAtUhD5E,OAAA,EAAAC,QAAA,CAuUM,MAaM,CAbNrC,mBAAA,CAaM,OAbNqJ,WAaM,G,4BAZJrJ,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CAIM,OAJNsJ,WAIM,GAHJ7I,YAAA,CAEiBuB,yBAAA;MA5U3BC,UAAA,EA0UmChB,KAAA,CAAAsI,cAAc;MA1UjD,wB,sCA0UmCtI,KAAA,CAAAsI,cAAc,GAAApI,MAAA,GAAsBZ,QAAA,CAAAiJ,oBAAoB;;MA1U3FpH,OAAA,EAAAC,QAAA,CA0U6F,MAEnFhC,MAAA,SAAAA,MAAA,QA5UVe,gBAAA,CA0U6F,MAEnF,E;MA5UVkB,CAAA;gEA8UQtC,mBAAA,CAIM,OAJNyJ,WAIM,I,kBAHJ3J,mBAAA,CAEiB0C,SAAA,QAjV3BC,WAAA,CA+UyCmF,IAAA,CAAA8B,KAAK,EAAb/G,IAAI;2BAA3BgH,YAAA,CAEiB3H,yBAAA;QAFsBpC,GAAG,EAAE+C,IAAI,CAACE,EAAE;QA/U7DZ,UAAA,EA+UwEhB,KAAA,CAAAiI,gBAAgB,CAACU,aAAa,CAACjH,IAAI,CAACE,EAAE;QA/U9G,uBAAA1B,MAAA,IA+UwEF,KAAA,CAAAiI,gBAAgB,CAACU,aAAa,CAACjH,IAAI,CAACE,EAAE,IAAA1B;;QA/U9GiB,OAAA,EAAAC,QAAA,CAgVY,MAAe,CAhV3BjB,gBAAA,CAAAkC,gBAAA,CAgVeX,IAAI,CAACY,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGX,IAAI,CAACe,EAAE,IAAG,IAChC,gB;QAjVVpB,CAAA;;sCAmVQtC,mBAAA,CAAyD,KAAzD6J,WAAyD,EAApC,MAAI,GAAAvG,gBAAA,CAAG/C,QAAA,CAAAuJ,kBAAkB,IAAG,MAAI,gB,GAGvD9J,mBAAA,CAOM,OAPN+J,WAOM,G,4BANJ/J,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CAIS;MA5VjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAwVyBF,KAAA,CAAAiI,gBAAgB,CAACvB,QAAQ,GAAAxG,MAAA;MAAExB,KAAK,EAAC;2BAChDG,mBAAA,CAES0C,SAAA,QA3VnBC,WAAA,CAyVmCmF,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArBhI,mBAAA,CAES;QAF2BF,GAAG,EAAEkI,MAAM,CAACjF,EAAE;QAAGlB,KAAK,EAAEmG,MAAM,CAACjF;0BAC9DiF,MAAM,CAACvE,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAGwE,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAzE,gBAAA,CAAGwE,MAAM,CAACE,UAAU,IAAG,KAC9E,uBA3VVgC,WAAA;6EAwVyB/I,KAAA,CAAAiI,gBAAgB,CAACvB,QAAQ,E,KAO5C3H,mBAAA,CAyBM,OAzBNiK,WAyBM,G,4BAxBJjK,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BK,mBAAA,CASM,OATNkK,WASM,GARJlK,mBAAA,CAGQ,SAHRmK,WAGQ,G,gBAFNnK,mBAAA,CAA4F;MAArFsB,IAAI,EAAC,OAAO;MAnW/B,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAmWyCF,KAAA,CAAAiI,gBAAgB,CAACkB,aAAa,GAAAjJ,MAAA;MAAEQ,KAAK,EAAC,WAAW;MAAChC,KAAK,EAAC;oDAAxDsB,KAAA,CAAAiI,gBAAgB,CAACkB,aAAa,E,+BAC3DpK,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHRqK,WAGQ,G,gBAFNrK,mBAAA,CAA4F;MAArFsB,IAAI,EAAC,OAAO;MAvW/B,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAuWyCF,KAAA,CAAAiI,gBAAgB,CAACkB,aAAa,GAAAjJ,MAAA;MAAEQ,KAAK,EAAC,WAAW;MAAChC,KAAK,EAAC;oDAAxDsB,KAAA,CAAAiI,gBAAgB,CAACkB,aAAa,E,+BAC3DpK,mBAAA,CAAiB,cAAX,MAAI,qB,KAIHiB,KAAA,CAAAiI,gBAAgB,CAACkB,aAAa,oB,cAAzCtK,mBAAA,CAWM,OAXNwK,WAWM,GAVJtK,mBAAA,CASM,OATNuK,WASM,GARJvK,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAoC;MAA7BL,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BK,mBAAA,CAAiF;MAA1EsB,IAAI,EAAC,MAAM;MAhXhC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAgX0CF,KAAA,CAAAiI,gBAAgB,CAACsB,aAAa,GAAArJ,MAAA;MAAExB,KAAK,EAAC;mDAAtCsB,KAAA,CAAAiI,gBAAgB,CAACsB,aAAa,E,KAE5DxK,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAoC;MAA7BL,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BK,mBAAA,CAAiF;MAA1EsB,IAAI,EAAC,MAAM;MApXhC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAoX0CF,KAAA,CAAAiI,gBAAgB,CAACuB,aAAa,GAAAtJ,MAAA;MAAExB,KAAK,EAAC;mDAAtCsB,KAAA,CAAAiI,gBAAgB,CAACuB,aAAa,E,WApXxE1K,mBAAA,e,GA0XMC,mBAAA,CAWM,OAXN0K,WAWM,G,4BAVJ1K,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Bc,YAAA,CAEiBuB,yBAAA;MA9XzBC,UAAA,EA4XiChB,KAAA,CAAAiI,gBAAgB,CAACyB,YAAY;MA5X9D,uBAAAtK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4XiCF,KAAA,CAAAiI,gBAAgB,CAACyB,YAAY,GAAAxJ,MAAA;;MA5X9DiB,OAAA,EAAAC,QAAA,CA4XgE,MAExDhC,MAAA,SAAAA,MAAA,QA9XRe,gBAAA,CA4XgE,YAExD,E;MA9XRkB,CAAA;uCA+XQ7B,YAAA,CAEiBuB,yBAAA;MAjYzBC,UAAA,EA+XiChB,KAAA,CAAAiI,gBAAgB,CAAC0B,WAAW;MA/X7D,uBAAAvK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA+XiCF,KAAA,CAAAiI,gBAAgB,CAAC0B,WAAW,GAAAzJ,MAAA;;MA/X7DiB,OAAA,EAAAC,QAAA,CA+X+D,MAEvDhC,MAAA,SAAAA,MAAA,QAjYRe,gBAAA,CA+X+D,UAEvD,E;MAjYRkB,CAAA;uCAkYQ7B,YAAA,CAEiBuB,yBAAA;MApYzBC,UAAA,EAkYiChB,KAAA,CAAAiI,gBAAgB,CAAC2B,gBAAgB;MAlYlE,uBAAAxK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAkYiCF,KAAA,CAAAiI,gBAAgB,CAAC2B,gBAAgB,GAAA1J,MAAA;;MAlYlEiB,OAAA,EAAAC,QAAA,CAkYoE,MAE5DhC,MAAA,SAAAA,MAAA,QApYRe,gBAAA,CAkYoE,aAE5D,E;MApYRkB,CAAA;;IAAAA,CAAA;6DAwYIvC,mBAAA,cAAiB,EACjBU,YAAA,CA8BYgG,oBAAA;IAvahBxE,UAAA,EAyYwBhB,KAAA,CAAA6J,eAAe,CAACnE,IAAI;IAzY5C,uBAAAtG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAyYwBF,KAAA,CAAA6J,eAAe,CAACnE,IAAI,GAAAxF,MAAA;IAAEyF,KAAK,EAAC,UAAU;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAEtG,QAAA,CAAAwK,gBAAgB;IACtGhE,OAAO,EAAE9F,KAAA,CAAA+F;;IA1YhB5E,OAAA,EAAAC,QAAA,CA2YM,MAQM,CARNrC,mBAAA,CAQM,OARNgL,WAQM,G,4BAPJhL,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CAKM,OALNiL,WAKM,I,kBAJJnL,mBAAA,CAGiB0C,SAAA,QAjZ3BC,WAAA,CA8YyClC,QAAA,CAAA2K,iBAAiB,EAAzBvI,IAAI;2BAA3BgH,YAAA,CAGiB3H,yBAAA;QAHkCpC,GAAG,EAAE+C,IAAI,CAACE,EAAE;QA9YzEZ,UAAA,EA+YqBhB,KAAA,CAAA6J,eAAe,CAAClB,aAAa,CAACjH,IAAI,CAACE,EAAE;QA/Y1D,uBAAA1B,MAAA,IA+YqBF,KAAA,CAAA6J,eAAe,CAAClB,aAAa,CAACjH,IAAI,CAACE,EAAE,IAAA1B;;QA/Y1DiB,OAAA,EAAAC,QAAA,CAgZY,MAAe,CAhZ3BjB,gBAAA,CAAAkC,gBAAA,CAgZeX,IAAI,CAACY,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGX,IAAI,CAACe,EAAE,IAAG,IAChC,gB;QAjZVpB,CAAA;;wCAqZMtC,mBAAA,CAOM,OAPNmL,WAOM,G,4BANJnL,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCK,mBAAA,CAIS;MA3ZjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAuZyBF,KAAA,CAAA6J,eAAe,CAACnD,QAAQ,GAAAxG,MAAA;MAAExB,KAAK,EAAC;2BAC/CG,mBAAA,CAES0C,SAAA,QA1ZnBC,WAAA,CAwZmCmF,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArBhI,mBAAA,CAES;QAF2BF,GAAG,EAAEkI,MAAM,CAACjF,EAAE;QAAGlB,KAAK,EAAEmG,MAAM,CAACjF;0BAC9DiF,MAAM,CAACvE,IAAI,wBAzZ1B6H,WAAA;6EAuZyBnK,KAAA,CAAA6J,eAAe,CAACnD,QAAQ,E,KAO3C3H,mBAAA,CAQM,OARNqL,WAQM,G,4BAPJrL,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Bc,YAAA,CAEiBuB,yBAAA;MAlazBC,UAAA,EAgaiChB,KAAA,CAAA6J,eAAe,CAACQ,iBAAiB;MAhalE,uBAAAjL,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAgaiCF,KAAA,CAAA6J,eAAe,CAACQ,iBAAiB,GAAAnK,MAAA;;MAhalEiB,OAAA,EAAAC,QAAA,CAgaoE,MAE5DhC,MAAA,SAAAA,MAAA,QAlaRe,gBAAA,CAgaoE,eAE5D,E;MAlaRkB,CAAA;uCAmaQ7B,YAAA,CAEiBuB,yBAAA;MArazBC,UAAA,EAmaiChB,KAAA,CAAA6J,eAAe,CAACS,iBAAiB;MAnalE,uBAAAlL,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAmaiCF,KAAA,CAAA6J,eAAe,CAACS,iBAAiB,GAAApK,MAAA;;MAnalEiB,OAAA,EAAAC,QAAA,CAmaoE,MAE5DhC,MAAA,SAAAA,MAAA,QAraRe,gBAAA,CAmaoE,aAE5D,E;MAraRkB,CAAA;;IAAAA,CAAA;6DAyaIvC,mBAAA,cAAiB,EACjBU,YAAA,CAyCYgG,oBAAA;IAndhBxE,UAAA,EA0awBhB,KAAA,CAAAuK,mBAAmB,CAAC7E,IAAI;IA1ahD,uBAAAtG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA0awBF,KAAA,CAAAuK,mBAAmB,CAAC7E,IAAI,GAAAxF,MAAA;IAAEyF,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAACjG,IAAI,EAAC,sBAAsB;IAAC8K,MAAM,EAAN,EAAM;IAChH5E,SAAO,EAAEtG,QAAA,CAAAmL,cAAc;IAAG3E,OAAO,EAAE9F,KAAA,CAAA+F;;IA3a1C5E,OAAA,EAAAC,QAAA,CA4aM,MAEM,C,4BAFNrC,mBAAA,CAEM;MAFDL,KAAK,EAAC;IAA4C,IACrDK,mBAAA,CAA+C,WAA5C,0CAAwC,E,sBAG7CA,mBAAA,CAQM,OARN2L,WAQM,G,4BAPJ3L,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CAKM,OALN4L,WAKM,I,kBAJJ9L,mBAAA,CAGiB0C,SAAA,QAtb3BC,WAAA,CAmbyClC,QAAA,CAAA2K,iBAAiB,EAAzBvI,IAAI;2BAA3BgH,YAAA,CAGiB3H,yBAAA;QAHkCpC,GAAG,EAAE+C,IAAI,CAACE,EAAE;QAnbzEZ,UAAA,EAobqBhB,KAAA,CAAAuK,mBAAmB,CAAC5B,aAAa,CAACjH,IAAI,CAACE,EAAE;QApb9D,uBAAA1B,MAAA,IAobqBF,KAAA,CAAAuK,mBAAmB,CAAC5B,aAAa,CAACjH,IAAI,CAACE,EAAE,IAAA1B;;QApb9DiB,OAAA,EAAAC,QAAA,CAqbY,MAAe,CArb3BjB,gBAAA,CAAAkC,gBAAA,CAqbeX,IAAI,CAACY,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGX,IAAI,CAACe,EAAE,IAAG,IAChC,gB;QAtbVpB,CAAA;;wCA0bMtC,mBAAA,CAOM,OAPN6L,WAOM,G,4BANJ7L,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCK,mBAAA,CAIS;MAhcjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4byBF,KAAA,CAAAuK,mBAAmB,CAAC7D,QAAQ,GAAAxG,MAAA;MAAExB,KAAK,EAAC;2BACnDG,mBAAA,CAES0C,SAAA,QA/bnBC,WAAA,CA6bmClC,QAAA,CAAAuL,iBAAiB,EAA3BhE,MAAM;2BAArBhI,mBAAA,CAES;QAFoCF,GAAG,EAAEkI,MAAM,CAACjF,EAAE;QAAGlB,KAAK,EAAEmG,MAAM,CAACjF;0BACvEiF,MAAM,CAACvE,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAGwE,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAzE,gBAAA,CAAGwE,MAAM,CAACE,UAAU,IAAG,KAC9E,uBA/bV+D,WAAA;6EA4byB9K,KAAA,CAAAuK,mBAAmB,CAAC7D,QAAQ,E,KAO/C3H,mBAAA,CASM,OATNgM,WASM,G,4BARJhM,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CAMS;MA3cjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAqcyBF,KAAA,CAAAuK,mBAAmB,CAACS,MAAM,GAAA9K,MAAA;MAAExB,KAAK,EAAC;oCACjDK,mBAAA,CAAiD;MAAzC2B,KAAK,EAAC;IAAmB,GAAC,QAAM,qBACxC3B,mBAAA,CAA2C;MAAnC2B,KAAK,EAAC;IAAe,GAAC,MAAI,qBAClC3B,mBAAA,CAA6C;MAArC2B,KAAK,EAAC;IAAiB,GAAC,MAAI,qBACpC3B,mBAAA,CAAwC;MAAhC2B,KAAK,EAAC;IAAY,GAAC,MAAI,qBAC/B3B,mBAAA,CAAmC;MAA3B2B,KAAK,EAAC;IAAO,GAAC,MAAI,oB,2CALXV,KAAA,CAAAuK,mBAAmB,CAACS,MAAM,E,KAS7CjM,mBAAA,CAIM,OAJNkM,WAIM,G,4BAHJlM,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CACuC;MAjd/C,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAgd2BF,KAAA,CAAAuK,mBAAmB,CAACW,WAAW,GAAAhL,MAAA;MAAExB,KAAK,EAAC,cAAc;MAACyM,IAAI,EAAC,GAAG;MAC/E5K,WAAW,EAAC;mDADKP,KAAA,CAAAuK,mBAAmB,CAACW,WAAW,E;IAhd1D7J,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}