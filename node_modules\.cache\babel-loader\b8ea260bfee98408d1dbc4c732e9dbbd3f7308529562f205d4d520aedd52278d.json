{"ast": null, "code": "/*!\n * Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)\n * Copyright 2024 Fonticons, Inc.\n */\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && _setPrototypeOf(t, e);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _wrapRegExp() {\n  _wrapRegExp = function (e, r) {\n    return new BabelRegExp(e, void 0, r);\n  };\n  var e = RegExp.prototype,\n    r = new WeakMap();\n  function BabelRegExp(e, t, p) {\n    var o = RegExp(e, t);\n    return r.set(o, p || r.get(e)), _setPrototypeOf(o, BabelRegExp.prototype);\n  }\n  function buildGroups(e, t) {\n    var p = r.get(t);\n    return Object.keys(p).reduce(function (r, t) {\n      var o = p[t];\n      if (\"number\" == typeof o) r[t] = e[o];else {\n        for (var i = 0; void 0 === e[o[i]] && i + 1 < o.length;) i++;\n        r[t] = e[o[i]];\n      }\n      return r;\n    }, Object.create(null));\n  }\n  return _inherits(BabelRegExp, RegExp), BabelRegExp.prototype.exec = function (r) {\n    var t = e.exec.call(this, r);\n    if (t) {\n      t.groups = buildGroups(t, this);\n      var p = t.indices;\n      p && (p.groups = buildGroups(p, this));\n    }\n    return t;\n  }, BabelRegExp.prototype[Symbol.replace] = function (t, p) {\n    if (\"string\" == typeof p) {\n      var o = r.get(this);\n      return e[Symbol.replace].call(this, t, p.replace(/\\$<([^>]+)>/g, function (e, r) {\n        var t = o[r];\n        return \"$\" + (Array.isArray(t) ? t.join(\"$\") : t);\n      }));\n    }\n    if (\"function\" == typeof p) {\n      var i = this;\n      return e[Symbol.replace].call(this, t, function () {\n        var e = arguments;\n        return \"object\" != typeof e[e.length - 1] && (e = [].slice.call(e)).push(buildGroups(e, i)), p.apply(this, e);\n      });\n    }\n    return e[Symbol.replace].call(this, t, p);\n  }, _wrapRegExp.apply(this, arguments);\n}\nconst noop = () => {};\nlet _WINDOW = {};\nlet _DOCUMENT = {};\nlet _MUTATION_OBSERVER = null;\nlet _PERFORMANCE = {\n  mark: noop,\n  measure: noop\n};\ntry {\n  if (typeof window !== 'undefined') _WINDOW = window;\n  if (typeof document !== 'undefined') _DOCUMENT = document;\n  if (typeof MutationObserver !== 'undefined') _MUTATION_OBSERVER = MutationObserver;\n  if (typeof performance !== 'undefined') _PERFORMANCE = performance;\n} catch (e) {}\nconst {\n  userAgent = ''\n} = _WINDOW.navigator || {};\nconst WINDOW = _WINDOW;\nconst DOCUMENT = _DOCUMENT;\nconst MUTATION_OBSERVER = _MUTATION_OBSERVER;\nconst PERFORMANCE = _PERFORMANCE;\nconst IS_BROWSER = !!WINDOW.document;\nconst IS_DOM = !!DOCUMENT.documentElement && !!DOCUMENT.head && typeof DOCUMENT.addEventListener === 'function' && typeof DOCUMENT.createElement === 'function';\nconst IS_IE = ~userAgent.indexOf('MSIE') || ~userAgent.indexOf('Trident/');\nvar p = /fa(s|r|l|t|d|dr|dl|dt|b|k|kd|ss|sr|sl|st|sds|sdr|sdl|sdt)?[\\-\\ ]/,\n  g = /Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp Duotone|Sharp|Kit)?.*/i;\nvar S = {\n    classic: {\n      fa: \"solid\",\n      fas: \"solid\",\n      \"fa-solid\": \"solid\",\n      far: \"regular\",\n      \"fa-regular\": \"regular\",\n      fal: \"light\",\n      \"fa-light\": \"light\",\n      fat: \"thin\",\n      \"fa-thin\": \"thin\",\n      fab: \"brands\",\n      \"fa-brands\": \"brands\"\n    },\n    duotone: {\n      fa: \"solid\",\n      fad: \"solid\",\n      \"fa-solid\": \"solid\",\n      \"fa-duotone\": \"solid\",\n      fadr: \"regular\",\n      \"fa-regular\": \"regular\",\n      fadl: \"light\",\n      \"fa-light\": \"light\",\n      fadt: \"thin\",\n      \"fa-thin\": \"thin\"\n    },\n    sharp: {\n      fa: \"solid\",\n      fass: \"solid\",\n      \"fa-solid\": \"solid\",\n      fasr: \"regular\",\n      \"fa-regular\": \"regular\",\n      fasl: \"light\",\n      \"fa-light\": \"light\",\n      fast: \"thin\",\n      \"fa-thin\": \"thin\"\n    },\n    \"sharp-duotone\": {\n      fa: \"solid\",\n      fasds: \"solid\",\n      \"fa-solid\": \"solid\",\n      fasdr: \"regular\",\n      \"fa-regular\": \"regular\",\n      fasdl: \"light\",\n      \"fa-light\": \"light\",\n      fasdt: \"thin\",\n      \"fa-thin\": \"thin\"\n    }\n  },\n  A = {\n    GROUP: \"duotone-group\",\n    SWAP_OPACITY: \"swap-opacity\",\n    PRIMARY: \"primary\",\n    SECONDARY: \"secondary\"\n  },\n  P = [\"fa-classic\", \"fa-duotone\", \"fa-sharp\", \"fa-sharp-duotone\"];\nvar s = \"classic\",\n  t = \"duotone\",\n  r = \"sharp\",\n  o = \"sharp-duotone\",\n  L = [s, t, r, o];\nvar G = {\n  classic: {\n    900: \"fas\",\n    400: \"far\",\n    normal: \"far\",\n    300: \"fal\",\n    100: \"fat\"\n  },\n  duotone: {\n    900: \"fad\",\n    400: \"fadr\",\n    300: \"fadl\",\n    100: \"fadt\"\n  },\n  sharp: {\n    900: \"fass\",\n    400: \"fasr\",\n    300: \"fasl\",\n    100: \"fast\"\n  },\n  \"sharp-duotone\": {\n    900: \"fasds\",\n    400: \"fasdr\",\n    300: \"fasdl\",\n    100: \"fasdt\"\n  }\n};\nvar lt = {\n  \"Font Awesome 6 Free\": {\n    900: \"fas\",\n    400: \"far\"\n  },\n  \"Font Awesome 6 Pro\": {\n    900: \"fas\",\n    400: \"far\",\n    normal: \"far\",\n    300: \"fal\",\n    100: \"fat\"\n  },\n  \"Font Awesome 6 Brands\": {\n    400: \"fab\",\n    normal: \"fab\"\n  },\n  \"Font Awesome 6 Duotone\": {\n    900: \"fad\",\n    400: \"fadr\",\n    normal: \"fadr\",\n    300: \"fadl\",\n    100: \"fadt\"\n  },\n  \"Font Awesome 6 Sharp\": {\n    900: \"fass\",\n    400: \"fasr\",\n    normal: \"fasr\",\n    300: \"fasl\",\n    100: \"fast\"\n  },\n  \"Font Awesome 6 Sharp Duotone\": {\n    900: \"fasds\",\n    400: \"fasdr\",\n    normal: \"fasdr\",\n    300: \"fasdl\",\n    100: \"fasdt\"\n  }\n};\nvar pt = new Map([[\"classic\", {\n    defaultShortPrefixId: \"fas\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\", \"brands\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"sharp\", {\n    defaultShortPrefixId: \"fass\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"duotone\", {\n    defaultShortPrefixId: \"fad\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"sharp-duotone\", {\n    defaultShortPrefixId: \"fasds\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }]]),\n  xt = {\n    classic: {\n      solid: \"fas\",\n      regular: \"far\",\n      light: \"fal\",\n      thin: \"fat\",\n      brands: \"fab\"\n    },\n    duotone: {\n      solid: \"fad\",\n      regular: \"fadr\",\n      light: \"fadl\",\n      thin: \"fadt\"\n    },\n    sharp: {\n      solid: \"fass\",\n      regular: \"fasr\",\n      light: \"fasl\",\n      thin: \"fast\"\n    },\n    \"sharp-duotone\": {\n      solid: \"fasds\",\n      regular: \"fasdr\",\n      light: \"fasdl\",\n      thin: \"fasdt\"\n    }\n  };\nvar Ft = [\"fak\", \"fa-kit\", \"fakd\", \"fa-kit-duotone\"],\n  St = {\n    kit: {\n      fak: \"kit\",\n      \"fa-kit\": \"kit\"\n    },\n    \"kit-duotone\": {\n      fakd: \"kit-duotone\",\n      \"fa-kit-duotone\": \"kit-duotone\"\n    }\n  },\n  At = [\"kit\"];\nvar Ct = {\n  kit: {\n    \"fa-kit\": \"fak\"\n  },\n  \"kit-duotone\": {\n    \"fa-kit-duotone\": \"fakd\"\n  }\n};\nvar Lt = [\"fak\", \"fakd\"],\n  Wt = {\n    kit: {\n      fak: \"fa-kit\"\n    },\n    \"kit-duotone\": {\n      fakd: \"fa-kit-duotone\"\n    }\n  };\nvar Et = {\n  kit: {\n    kit: \"fak\"\n  },\n  \"kit-duotone\": {\n    \"kit-duotone\": \"fakd\"\n  }\n};\nvar t$1 = {\n    GROUP: \"duotone-group\",\n    SWAP_OPACITY: \"swap-opacity\",\n    PRIMARY: \"primary\",\n    SECONDARY: \"secondary\"\n  },\n  r$1 = [\"fa-classic\", \"fa-duotone\", \"fa-sharp\", \"fa-sharp-duotone\"];\nvar bt$1 = [\"fak\", \"fa-kit\", \"fakd\", \"fa-kit-duotone\"];\nvar Yt = {\n  \"Font Awesome Kit\": {\n    400: \"fak\",\n    normal: \"fak\"\n  },\n  \"Font Awesome Kit Duotone\": {\n    400: \"fakd\",\n    normal: \"fakd\"\n  }\n};\nvar ua = {\n    classic: {\n      \"fa-brands\": \"fab\",\n      \"fa-duotone\": \"fad\",\n      \"fa-light\": \"fal\",\n      \"fa-regular\": \"far\",\n      \"fa-solid\": \"fas\",\n      \"fa-thin\": \"fat\"\n    },\n    duotone: {\n      \"fa-regular\": \"fadr\",\n      \"fa-light\": \"fadl\",\n      \"fa-thin\": \"fadt\"\n    },\n    sharp: {\n      \"fa-solid\": \"fass\",\n      \"fa-regular\": \"fasr\",\n      \"fa-light\": \"fasl\",\n      \"fa-thin\": \"fast\"\n    },\n    \"sharp-duotone\": {\n      \"fa-solid\": \"fasds\",\n      \"fa-regular\": \"fasdr\",\n      \"fa-light\": \"fasdl\",\n      \"fa-thin\": \"fasdt\"\n    }\n  },\n  I$1 = {\n    classic: [\"fas\", \"far\", \"fal\", \"fat\", \"fad\"],\n    duotone: [\"fadr\", \"fadl\", \"fadt\"],\n    sharp: [\"fass\", \"fasr\", \"fasl\", \"fast\"],\n    \"sharp-duotone\": [\"fasds\", \"fasdr\", \"fasdl\", \"fasdt\"]\n  },\n  ga = {\n    classic: {\n      fab: \"fa-brands\",\n      fad: \"fa-duotone\",\n      fal: \"fa-light\",\n      far: \"fa-regular\",\n      fas: \"fa-solid\",\n      fat: \"fa-thin\"\n    },\n    duotone: {\n      fadr: \"fa-regular\",\n      fadl: \"fa-light\",\n      fadt: \"fa-thin\"\n    },\n    sharp: {\n      fass: \"fa-solid\",\n      fasr: \"fa-regular\",\n      fasl: \"fa-light\",\n      fast: \"fa-thin\"\n    },\n    \"sharp-duotone\": {\n      fasds: \"fa-solid\",\n      fasdr: \"fa-regular\",\n      fasdl: \"fa-light\",\n      fasdt: \"fa-thin\"\n    }\n  },\n  x = [\"fa-solid\", \"fa-regular\", \"fa-light\", \"fa-thin\", \"fa-duotone\", \"fa-brands\"],\n  Ia = [\"fa\", \"fas\", \"far\", \"fal\", \"fat\", \"fad\", \"fadr\", \"fadl\", \"fadt\", \"fab\", \"fass\", \"fasr\", \"fasl\", \"fast\", \"fasds\", \"fasdr\", \"fasdl\", \"fasdt\", ...r$1, ...x],\n  m$1 = [\"solid\", \"regular\", \"light\", \"thin\", \"duotone\", \"brands\"],\n  c$1 = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],\n  F$1 = c$1.concat([11, 12, 13, 14, 15, 16, 17, 18, 19, 20]),\n  ma = [...Object.keys(I$1), ...m$1, \"2xs\", \"xs\", \"sm\", \"lg\", \"xl\", \"2xl\", \"beat\", \"border\", \"fade\", \"beat-fade\", \"bounce\", \"flip-both\", \"flip-horizontal\", \"flip-vertical\", \"flip\", \"fw\", \"inverse\", \"layers-counter\", \"layers-text\", \"layers\", \"li\", \"pull-left\", \"pull-right\", \"pulse\", \"rotate-180\", \"rotate-270\", \"rotate-90\", \"rotate-by\", \"shake\", \"spin-pulse\", \"spin-reverse\", \"spin\", \"stack-1x\", \"stack-2x\", \"stack\", \"ul\", t$1.GROUP, t$1.SWAP_OPACITY, t$1.PRIMARY, t$1.SECONDARY].concat(c$1.map(a => \"\".concat(a, \"x\"))).concat(F$1.map(a => \"w-\".concat(a)));\nvar wa = {\n  \"Font Awesome 5 Free\": {\n    900: \"fas\",\n    400: \"far\"\n  },\n  \"Font Awesome 5 Pro\": {\n    900: \"fas\",\n    400: \"far\",\n    normal: \"far\",\n    300: \"fal\"\n  },\n  \"Font Awesome 5 Brands\": {\n    400: \"fab\",\n    normal: \"fab\"\n  },\n  \"Font Awesome 5 Duotone\": {\n    900: \"fad\"\n  }\n};\nconst NAMESPACE_IDENTIFIER = '___FONT_AWESOME___';\nconst UNITS_IN_GRID = 16;\nconst DEFAULT_CSS_PREFIX = 'fa';\nconst DEFAULT_REPLACEMENT_CLASS = 'svg-inline--fa';\nconst DATA_FA_I2SVG = 'data-fa-i2svg';\nconst DATA_FA_PSEUDO_ELEMENT = 'data-fa-pseudo-element';\nconst DATA_FA_PSEUDO_ELEMENT_PENDING = 'data-fa-pseudo-element-pending';\nconst DATA_PREFIX = 'data-prefix';\nconst DATA_ICON = 'data-icon';\nconst HTML_CLASS_I2SVG_BASE_CLASS = 'fontawesome-i2svg';\nconst MUTATION_APPROACH_ASYNC = 'async';\nconst TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS = ['HTML', 'HEAD', 'STYLE', 'SCRIPT'];\nconst PRODUCTION = (() => {\n  try {\n    return process.env.NODE_ENV === 'production';\n  } catch (e$$1) {\n    return false;\n  }\n})();\nfunction familyProxy(obj) {\n  // Defaults to the classic family if family is not available\n  return new Proxy(obj, {\n    get(target, prop) {\n      return prop in target ? target[prop] : target[s];\n    }\n  });\n}\nconst _PREFIX_TO_STYLE = _objectSpread2({}, S);\n\n// We changed FACSSClassesToStyleId in the icons repo to be canonical and as such, \"classic\" family does not have any\n// duotone styles.  But we do still need duotone in _PREFIX_TO_STYLE below, so we are manually adding\n// {'fa-duotone': 'duotone'}\n_PREFIX_TO_STYLE[s] = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\n  'fa-duotone': 'duotone'\n}), S[s]), St['kit']), St['kit-duotone']);\nconst PREFIX_TO_STYLE = familyProxy(_PREFIX_TO_STYLE);\nconst _STYLE_TO_PREFIX = _objectSpread2({}, xt);\n\n// We changed FAStyleIdToShortPrefixId in the icons repo to be canonical and as such, \"classic\" family does not have any\n// duotone styles.  But we do still need duotone in _STYLE_TO_PREFIX below, so we are manually adding {duotone: 'fad'}\n_STYLE_TO_PREFIX[s] = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\n  duotone: 'fad'\n}), _STYLE_TO_PREFIX[s]), Et['kit']), Et['kit-duotone']);\nconst STYLE_TO_PREFIX = familyProxy(_STYLE_TO_PREFIX);\nconst _PREFIX_TO_LONG_STYLE = _objectSpread2({}, ga);\n_PREFIX_TO_LONG_STYLE[s] = _objectSpread2(_objectSpread2({}, _PREFIX_TO_LONG_STYLE[s]), Wt['kit']);\nconst PREFIX_TO_LONG_STYLE = familyProxy(_PREFIX_TO_LONG_STYLE);\nconst _LONG_STYLE_TO_PREFIX = _objectSpread2({}, ua);\n_LONG_STYLE_TO_PREFIX[s] = _objectSpread2(_objectSpread2({}, _LONG_STYLE_TO_PREFIX[s]), Ct['kit']);\nconst LONG_STYLE_TO_PREFIX = familyProxy(_LONG_STYLE_TO_PREFIX);\nconst ICON_SELECTION_SYNTAX_PATTERN = p; // eslint-disable-line no-useless-escape\n\nconst LAYERS_TEXT_CLASSNAME = 'fa-layers-text';\nconst FONT_FAMILY_PATTERN = g;\nconst _FONT_WEIGHT_TO_PREFIX = _objectSpread2({}, G);\nconst FONT_WEIGHT_TO_PREFIX = familyProxy(_FONT_WEIGHT_TO_PREFIX);\nconst ATTRIBUTES_WATCHED_FOR_MUTATION = ['class', 'data-prefix', 'data-icon', 'data-fa-transform', 'data-fa-mask'];\nconst DUOTONE_CLASSES = A;\nconst RESERVED_CLASSES = [...At, ...ma];\nconst initial = WINDOW.FontAwesomeConfig || {};\nfunction getAttrConfig(attr) {\n  var element = DOCUMENT.querySelector('script[' + attr + ']');\n  if (element) {\n    return element.getAttribute(attr);\n  }\n}\nfunction coerce(val) {\n  // Getting an empty string will occur if the attribute is set on the HTML tag but without a value\n  // We'll assume that this is an indication that it should be toggled to true\n  if (val === '') return true;\n  if (val === 'false') return false;\n  if (val === 'true') return true;\n  return val;\n}\nif (DOCUMENT && typeof DOCUMENT.querySelector === 'function') {\n  const attrs = [['data-family-prefix', 'familyPrefix'], ['data-css-prefix', 'cssPrefix'], ['data-family-default', 'familyDefault'], ['data-style-default', 'styleDefault'], ['data-replacement-class', 'replacementClass'], ['data-auto-replace-svg', 'autoReplaceSvg'], ['data-auto-add-css', 'autoAddCss'], ['data-auto-a11y', 'autoA11y'], ['data-search-pseudo-elements', 'searchPseudoElements'], ['data-observe-mutations', 'observeMutations'], ['data-mutate-approach', 'mutateApproach'], ['data-keep-original-source', 'keepOriginalSource'], ['data-measure-performance', 'measurePerformance'], ['data-show-missing-icons', 'showMissingIcons']];\n  attrs.forEach(_ref => {\n    let [attr, key] = _ref;\n    const val = coerce(getAttrConfig(attr));\n    if (val !== undefined && val !== null) {\n      initial[key] = val;\n    }\n  });\n}\nconst _default = {\n  styleDefault: 'solid',\n  familyDefault: s,\n  cssPrefix: DEFAULT_CSS_PREFIX,\n  replacementClass: DEFAULT_REPLACEMENT_CLASS,\n  autoReplaceSvg: true,\n  autoAddCss: true,\n  autoA11y: true,\n  searchPseudoElements: false,\n  observeMutations: true,\n  mutateApproach: 'async',\n  keepOriginalSource: true,\n  measurePerformance: false,\n  showMissingIcons: true\n};\n\n// familyPrefix is deprecated but we must still support it if present\nif (initial.familyPrefix) {\n  initial.cssPrefix = initial.familyPrefix;\n}\nconst _config = _objectSpread2(_objectSpread2({}, _default), initial);\nif (!_config.autoReplaceSvg) _config.observeMutations = false;\nconst config = {};\nObject.keys(_default).forEach(key => {\n  Object.defineProperty(config, key, {\n    enumerable: true,\n    set: function (val) {\n      _config[key] = val;\n      _onChangeCb.forEach(cb => cb(config));\n    },\n    get: function () {\n      return _config[key];\n    }\n  });\n});\n\n// familyPrefix is deprecated as of 6.2.0 and should be removed in 7.0.0\nObject.defineProperty(config, 'familyPrefix', {\n  enumerable: true,\n  set: function (val) {\n    _config.cssPrefix = val;\n    _onChangeCb.forEach(cb => cb(config));\n  },\n  get: function () {\n    return _config.cssPrefix;\n  }\n});\nWINDOW.FontAwesomeConfig = config;\nconst _onChangeCb = [];\nfunction onChange(cb) {\n  _onChangeCb.push(cb);\n  return () => {\n    _onChangeCb.splice(_onChangeCb.indexOf(cb), 1);\n  };\n}\nconst d$2 = UNITS_IN_GRID;\nconst meaninglessTransform = {\n  size: 16,\n  x: 0,\n  y: 0,\n  rotate: 0,\n  flipX: false,\n  flipY: false\n};\nfunction insertCss(css) {\n  if (!css || !IS_DOM) {\n    return;\n  }\n  const style = DOCUMENT.createElement('style');\n  style.setAttribute('type', 'text/css');\n  style.innerHTML = css;\n  const headChildren = DOCUMENT.head.childNodes;\n  let beforeChild = null;\n  for (let i = headChildren.length - 1; i > -1; i--) {\n    const child = headChildren[i];\n    const tagName = (child.tagName || '').toUpperCase();\n    if (['STYLE', 'LINK'].indexOf(tagName) > -1) {\n      beforeChild = child;\n    }\n  }\n  DOCUMENT.head.insertBefore(style, beforeChild);\n  return css;\n}\nconst idPool = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\nfunction nextUniqueId() {\n  let size = 12;\n  let id = '';\n  while (size-- > 0) {\n    id += idPool[Math.random() * 62 | 0];\n  }\n  return id;\n}\nfunction toArray(obj) {\n  const array = [];\n  for (let i = (obj || []).length >>> 0; i--;) {\n    array[i] = obj[i];\n  }\n  return array;\n}\nfunction classArray(node) {\n  if (node.classList) {\n    return toArray(node.classList);\n  } else {\n    return (node.getAttribute('class') || '').split(' ').filter(i => i);\n  }\n}\nfunction htmlEscape(str) {\n  return \"\".concat(str).replace(/&/g, '&amp;').replace(/\"/g, '&quot;').replace(/'/g, '&#39;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\n}\nfunction joinAttributes(attributes) {\n  return Object.keys(attributes || {}).reduce((acc, attributeName) => {\n    return acc + \"\".concat(attributeName, \"=\\\"\").concat(htmlEscape(attributes[attributeName]), \"\\\" \");\n  }, '').trim();\n}\nfunction joinStyles(styles) {\n  return Object.keys(styles || {}).reduce((acc, styleName) => {\n    return acc + \"\".concat(styleName, \": \").concat(styles[styleName].trim(), \";\");\n  }, '');\n}\nfunction transformIsMeaningful(transform) {\n  return transform.size !== meaninglessTransform.size || transform.x !== meaninglessTransform.x || transform.y !== meaninglessTransform.y || transform.rotate !== meaninglessTransform.rotate || transform.flipX || transform.flipY;\n}\nfunction transformForSvg(_ref) {\n  let {\n    transform,\n    containerWidth,\n    iconWidth\n  } = _ref;\n  const outer = {\n    transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\n  };\n  const innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\n  const innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\n  const innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\n  const inner = {\n    transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\n  };\n  const path = {\n    transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\n  };\n  return {\n    outer,\n    inner,\n    path\n  };\n}\nfunction transformForCss(_ref2) {\n  let {\n    transform,\n    width = UNITS_IN_GRID,\n    height = UNITS_IN_GRID,\n    startCentered = false\n  } = _ref2;\n  let val = '';\n  if (startCentered && IS_IE) {\n    val += \"translate(\".concat(transform.x / d$2 - width / 2, \"em, \").concat(transform.y / d$2 - height / 2, \"em) \");\n  } else if (startCentered) {\n    val += \"translate(calc(-50% + \".concat(transform.x / d$2, \"em), calc(-50% + \").concat(transform.y / d$2, \"em)) \");\n  } else {\n    val += \"translate(\".concat(transform.x / d$2, \"em, \").concat(transform.y / d$2, \"em) \");\n  }\n  val += \"scale(\".concat(transform.size / d$2 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / d$2 * (transform.flipY ? -1 : 1), \") \");\n  val += \"rotate(\".concat(transform.rotate, \"deg) \");\n  return val;\n}\nvar baseStyles = \":root, :host {\\n  --fa-font-solid: normal 900 1em/1 \\\"Font Awesome 6 Free\\\";\\n  --fa-font-regular: normal 400 1em/1 \\\"Font Awesome 6 Free\\\";\\n  --fa-font-light: normal 300 1em/1 \\\"Font Awesome 6 Pro\\\";\\n  --fa-font-thin: normal 100 1em/1 \\\"Font Awesome 6 Pro\\\";\\n  --fa-font-duotone: normal 900 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-duotone-regular: normal 400 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-duotone-light: normal 300 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-duotone-thin: normal 100 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-brands: normal 400 1em/1 \\\"Font Awesome 6 Brands\\\";\\n  --fa-font-sharp-solid: normal 900 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-regular: normal 400 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-light: normal 300 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-thin: normal 100 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-duotone-solid: normal 900 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-regular: normal 400 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-light: normal 300 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-thin: normal 100 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n}\\n\\nsvg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {\\n  overflow: visible;\\n  box-sizing: content-box;\\n}\\n\\n.svg-inline--fa {\\n  display: var(--fa-display, inline-block);\\n  height: 1em;\\n  overflow: visible;\\n  vertical-align: -0.125em;\\n}\\n.svg-inline--fa.fa-2xs {\\n  vertical-align: 0.1em;\\n}\\n.svg-inline--fa.fa-xs {\\n  vertical-align: 0em;\\n}\\n.svg-inline--fa.fa-sm {\\n  vertical-align: -0.0714285705em;\\n}\\n.svg-inline--fa.fa-lg {\\n  vertical-align: -0.2em;\\n}\\n.svg-inline--fa.fa-xl {\\n  vertical-align: -0.25em;\\n}\\n.svg-inline--fa.fa-2xl {\\n  vertical-align: -0.3125em;\\n}\\n.svg-inline--fa.fa-pull-left {\\n  margin-right: var(--fa-pull-margin, 0.3em);\\n  width: auto;\\n}\\n.svg-inline--fa.fa-pull-right {\\n  margin-left: var(--fa-pull-margin, 0.3em);\\n  width: auto;\\n}\\n.svg-inline--fa.fa-li {\\n  width: var(--fa-li-width, 2em);\\n  top: 0.25em;\\n}\\n.svg-inline--fa.fa-fw {\\n  width: var(--fa-fw-width, 1.25em);\\n}\\n\\n.fa-layers svg.svg-inline--fa {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n}\\n\\n.fa-layers-counter, .fa-layers-text {\\n  display: inline-block;\\n  position: absolute;\\n  text-align: center;\\n}\\n\\n.fa-layers {\\n  display: inline-block;\\n  height: 1em;\\n  position: relative;\\n  text-align: center;\\n  vertical-align: -0.125em;\\n  width: 1em;\\n}\\n.fa-layers svg.svg-inline--fa {\\n  transform-origin: center center;\\n}\\n\\n.fa-layers-text {\\n  left: 50%;\\n  top: 50%;\\n  transform: translate(-50%, -50%);\\n  transform-origin: center center;\\n}\\n\\n.fa-layers-counter {\\n  background-color: var(--fa-counter-background-color, #ff253a);\\n  border-radius: var(--fa-counter-border-radius, 1em);\\n  box-sizing: border-box;\\n  color: var(--fa-inverse, #fff);\\n  line-height: var(--fa-counter-line-height, 1);\\n  max-width: var(--fa-counter-max-width, 5em);\\n  min-width: var(--fa-counter-min-width, 1.5em);\\n  overflow: hidden;\\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\\n  right: var(--fa-right, 0);\\n  text-overflow: ellipsis;\\n  top: var(--fa-top, 0);\\n  transform: scale(var(--fa-counter-scale, 0.25));\\n  transform-origin: top right;\\n}\\n\\n.fa-layers-bottom-right {\\n  bottom: var(--fa-bottom, 0);\\n  right: var(--fa-right, 0);\\n  top: auto;\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: bottom right;\\n}\\n\\n.fa-layers-bottom-left {\\n  bottom: var(--fa-bottom, 0);\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: auto;\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: bottom left;\\n}\\n\\n.fa-layers-top-right {\\n  top: var(--fa-top, 0);\\n  right: var(--fa-right, 0);\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: top right;\\n}\\n\\n.fa-layers-top-left {\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: var(--fa-top, 0);\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: top left;\\n}\\n\\n.fa-1x {\\n  font-size: 1em;\\n}\\n\\n.fa-2x {\\n  font-size: 2em;\\n}\\n\\n.fa-3x {\\n  font-size: 3em;\\n}\\n\\n.fa-4x {\\n  font-size: 4em;\\n}\\n\\n.fa-5x {\\n  font-size: 5em;\\n}\\n\\n.fa-6x {\\n  font-size: 6em;\\n}\\n\\n.fa-7x {\\n  font-size: 7em;\\n}\\n\\n.fa-8x {\\n  font-size: 8em;\\n}\\n\\n.fa-9x {\\n  font-size: 9em;\\n}\\n\\n.fa-10x {\\n  font-size: 10em;\\n}\\n\\n.fa-2xs {\\n  font-size: 0.625em;\\n  line-height: 0.1em;\\n  vertical-align: 0.225em;\\n}\\n\\n.fa-xs {\\n  font-size: 0.75em;\\n  line-height: 0.0833333337em;\\n  vertical-align: 0.125em;\\n}\\n\\n.fa-sm {\\n  font-size: 0.875em;\\n  line-height: 0.0714285718em;\\n  vertical-align: 0.0535714295em;\\n}\\n\\n.fa-lg {\\n  font-size: 1.25em;\\n  line-height: 0.05em;\\n  vertical-align: -0.075em;\\n}\\n\\n.fa-xl {\\n  font-size: 1.5em;\\n  line-height: 0.0416666682em;\\n  vertical-align: -0.125em;\\n}\\n\\n.fa-2xl {\\n  font-size: 2em;\\n  line-height: 0.03125em;\\n  vertical-align: -0.1875em;\\n}\\n\\n.fa-fw {\\n  text-align: center;\\n  width: 1.25em;\\n}\\n\\n.fa-ul {\\n  list-style-type: none;\\n  margin-left: var(--fa-li-margin, 2.5em);\\n  padding-left: 0;\\n}\\n.fa-ul > li {\\n  position: relative;\\n}\\n\\n.fa-li {\\n  left: calc(-1 * var(--fa-li-width, 2em));\\n  position: absolute;\\n  text-align: center;\\n  width: var(--fa-li-width, 2em);\\n  line-height: inherit;\\n}\\n\\n.fa-border {\\n  border-color: var(--fa-border-color, #eee);\\n  border-radius: var(--fa-border-radius, 0.1em);\\n  border-style: var(--fa-border-style, solid);\\n  border-width: var(--fa-border-width, 0.08em);\\n  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);\\n}\\n\\n.fa-pull-left {\\n  float: left;\\n  margin-right: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-pull-right {\\n  float: right;\\n  margin-left: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-beat {\\n  animation-name: fa-beat;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-bounce {\\n  animation-name: fa-bounce;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\\n}\\n\\n.fa-fade {\\n  animation-name: fa-fade;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-beat-fade {\\n  animation-name: fa-beat-fade;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-flip {\\n  animation-name: fa-flip;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-shake {\\n  animation-name: fa-shake;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin {\\n  animation-name: fa-spin;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 2s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin-reverse {\\n  --fa-animation-direction: reverse;\\n}\\n\\n.fa-pulse,\\n.fa-spin-pulse {\\n  animation-name: fa-spin;\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, steps(8));\\n}\\n\\n@media (prefers-reduced-motion: reduce) {\\n  .fa-beat,\\n.fa-bounce,\\n.fa-fade,\\n.fa-beat-fade,\\n.fa-flip,\\n.fa-pulse,\\n.fa-shake,\\n.fa-spin,\\n.fa-spin-pulse {\\n    animation-delay: -1ms;\\n    animation-duration: 1ms;\\n    animation-iteration-count: 1;\\n    transition-delay: 0s;\\n    transition-duration: 0s;\\n  }\\n}\\n@keyframes fa-beat {\\n  0%, 90% {\\n    transform: scale(1);\\n  }\\n  45% {\\n    transform: scale(var(--fa-beat-scale, 1.25));\\n  }\\n}\\n@keyframes fa-bounce {\\n  0% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n  10% {\\n    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\\n  }\\n  30% {\\n    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\\n  }\\n  50% {\\n    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\\n  }\\n  57% {\\n    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\\n  }\\n  64% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n  100% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n}\\n@keyframes fa-fade {\\n  50% {\\n    opacity: var(--fa-fade-opacity, 0.4);\\n  }\\n}\\n@keyframes fa-beat-fade {\\n  0%, 100% {\\n    opacity: var(--fa-beat-fade-opacity, 0.4);\\n    transform: scale(1);\\n  }\\n  50% {\\n    opacity: 1;\\n    transform: scale(var(--fa-beat-fade-scale, 1.125));\\n  }\\n}\\n@keyframes fa-flip {\\n  50% {\\n    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\\n  }\\n}\\n@keyframes fa-shake {\\n  0% {\\n    transform: rotate(-15deg);\\n  }\\n  4% {\\n    transform: rotate(15deg);\\n  }\\n  8%, 24% {\\n    transform: rotate(-18deg);\\n  }\\n  12%, 28% {\\n    transform: rotate(18deg);\\n  }\\n  16% {\\n    transform: rotate(-22deg);\\n  }\\n  20% {\\n    transform: rotate(22deg);\\n  }\\n  32% {\\n    transform: rotate(-12deg);\\n  }\\n  36% {\\n    transform: rotate(12deg);\\n  }\\n  40%, 100% {\\n    transform: rotate(0deg);\\n  }\\n}\\n@keyframes fa-spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.fa-rotate-90 {\\n  transform: rotate(90deg);\\n}\\n\\n.fa-rotate-180 {\\n  transform: rotate(180deg);\\n}\\n\\n.fa-rotate-270 {\\n  transform: rotate(270deg);\\n}\\n\\n.fa-flip-horizontal {\\n  transform: scale(-1, 1);\\n}\\n\\n.fa-flip-vertical {\\n  transform: scale(1, -1);\\n}\\n\\n.fa-flip-both,\\n.fa-flip-horizontal.fa-flip-vertical {\\n  transform: scale(-1, -1);\\n}\\n\\n.fa-rotate-by {\\n  transform: rotate(var(--fa-rotate-angle, 0));\\n}\\n\\n.fa-stack {\\n  display: inline-block;\\n  vertical-align: middle;\\n  height: 2em;\\n  position: relative;\\n  width: 2.5em;\\n}\\n\\n.fa-stack-1x,\\n.fa-stack-2x {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n  z-index: var(--fa-stack-z-index, auto);\\n}\\n\\n.svg-inline--fa.fa-stack-1x {\\n  height: 1em;\\n  width: 1.25em;\\n}\\n.svg-inline--fa.fa-stack-2x {\\n  height: 2em;\\n  width: 2.5em;\\n}\\n\\n.fa-inverse {\\n  color: var(--fa-inverse, #fff);\\n}\\n\\n.sr-only,\\n.fa-sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n\\n.sr-only-focusable:not(:focus),\\n.fa-sr-only-focusable:not(:focus) {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n\\n.svg-inline--fa .fa-primary {\\n  fill: var(--fa-primary-color, currentColor);\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa .fa-secondary {\\n  fill: var(--fa-secondary-color, currentColor);\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-primary {\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa mask .fa-primary,\\n.svg-inline--fa mask .fa-secondary {\\n  fill: black;\\n}\";\nfunction css() {\n  const dcp = DEFAULT_CSS_PREFIX;\n  const drc = DEFAULT_REPLACEMENT_CLASS;\n  const fp = config.cssPrefix;\n  const rc = config.replacementClass;\n  let s = baseStyles;\n  if (fp !== dcp || rc !== drc) {\n    const dPatt = new RegExp(\"\\\\.\".concat(dcp, \"\\\\-\"), 'g');\n    const customPropPatt = new RegExp(\"\\\\--\".concat(dcp, \"\\\\-\"), 'g');\n    const rPatt = new RegExp(\"\\\\.\".concat(drc), 'g');\n    s = s.replace(dPatt, \".\".concat(fp, \"-\")).replace(customPropPatt, \"--\".concat(fp, \"-\")).replace(rPatt, \".\".concat(rc));\n  }\n  return s;\n}\nlet _cssInserted = false;\nfunction ensureCss() {\n  if (config.autoAddCss && !_cssInserted) {\n    insertCss(css());\n    _cssInserted = true;\n  }\n}\nvar InjectCSS = {\n  mixout() {\n    return {\n      dom: {\n        css,\n        insertCss: ensureCss\n      }\n    };\n  },\n  hooks() {\n    return {\n      beforeDOMElementCreation() {\n        ensureCss();\n      },\n      beforeI2svg() {\n        ensureCss();\n      }\n    };\n  }\n};\nconst w = WINDOW || {};\nif (!w[NAMESPACE_IDENTIFIER]) w[NAMESPACE_IDENTIFIER] = {};\nif (!w[NAMESPACE_IDENTIFIER].styles) w[NAMESPACE_IDENTIFIER].styles = {};\nif (!w[NAMESPACE_IDENTIFIER].hooks) w[NAMESPACE_IDENTIFIER].hooks = {};\nif (!w[NAMESPACE_IDENTIFIER].shims) w[NAMESPACE_IDENTIFIER].shims = [];\nvar namespace = w[NAMESPACE_IDENTIFIER];\nconst functions = [];\nconst listener = function () {\n  DOCUMENT.removeEventListener('DOMContentLoaded', listener);\n  loaded = 1;\n  functions.map(fn => fn());\n};\nlet loaded = false;\nif (IS_DOM) {\n  loaded = (DOCUMENT.documentElement.doScroll ? /^loaded|^c/ : /^loaded|^i|^c/).test(DOCUMENT.readyState);\n  if (!loaded) DOCUMENT.addEventListener('DOMContentLoaded', listener);\n}\nfunction domready(fn) {\n  if (!IS_DOM) return;\n  loaded ? setTimeout(fn, 0) : functions.push(fn);\n}\nfunction toHtml(abstractNodes) {\n  const {\n    tag,\n    attributes = {},\n    children = []\n  } = abstractNodes;\n  if (typeof abstractNodes === 'string') {\n    return htmlEscape(abstractNodes);\n  } else {\n    return \"<\".concat(tag, \" \").concat(joinAttributes(attributes), \">\").concat(children.map(toHtml).join(''), \"</\").concat(tag, \">\");\n  }\n}\nfunction iconFromMapping(mapping, prefix, iconName) {\n  if (mapping && mapping[prefix] && mapping[prefix][iconName]) {\n    return {\n      prefix,\n      iconName,\n      icon: mapping[prefix][iconName]\n    };\n  }\n}\n\n/**\n * Internal helper to bind a function known to have 4 arguments\n * to a given context.\n */\nvar bindInternal4 = function bindInternal4(func, thisContext) {\n  return function (a, b, c, d) {\n    return func.call(thisContext, a, b, c, d);\n  };\n};\n\n/**\n * # Reduce\n *\n * A fast object `.reduce()` implementation.\n *\n * @param  {Object}   subject      The object to reduce over.\n * @param  {Function} fn           The reducer function.\n * @param  {mixed}    initialValue The initial value for the reducer, defaults to subject[0].\n * @param  {Object}   thisContext  The context for the reducer.\n * @return {mixed}                 The final result.\n */\nvar reduce = function fastReduceObject(subject, fn, initialValue, thisContext) {\n  var keys = Object.keys(subject),\n    length = keys.length,\n    iterator = thisContext !== undefined ? bindInternal4(fn, thisContext) : fn,\n    i,\n    key,\n    result;\n  if (initialValue === undefined) {\n    i = 1;\n    result = subject[keys[0]];\n  } else {\n    i = 0;\n    result = initialValue;\n  }\n  for (; i < length; i++) {\n    key = keys[i];\n    result = iterator(result, subject[key], key, subject);\n  }\n  return result;\n};\n\n/**\n * ucs2decode() and codePointAt() are both works of Mathias Bynens and licensed under MIT\n *\n * Copyright Mathias Bynens <https://mathiasbynens.be/>\n\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.\n\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\n * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\n * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\n * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\nfunction ucs2decode(string) {\n  const output = [];\n  let counter = 0;\n  const length = string.length;\n  while (counter < length) {\n    const value = string.charCodeAt(counter++);\n    if (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n      const extra = string.charCodeAt(counter++);\n      if ((extra & 0xFC00) == 0xDC00) {\n        // eslint-disable-line eqeqeq\n        output.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n      } else {\n        output.push(value);\n        counter--;\n      }\n    } else {\n      output.push(value);\n    }\n  }\n  return output;\n}\nfunction toHex(unicode) {\n  const decoded = ucs2decode(unicode);\n  return decoded.length === 1 ? decoded[0].toString(16) : null;\n}\nfunction codePointAt(string, index) {\n  const size = string.length;\n  let first = string.charCodeAt(index);\n  let second;\n  if (first >= 0xD800 && first <= 0xDBFF && size > index + 1) {\n    second = string.charCodeAt(index + 1);\n    if (second >= 0xDC00 && second <= 0xDFFF) {\n      return (first - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n    }\n  }\n  return first;\n}\nfunction normalizeIcons(icons) {\n  return Object.keys(icons).reduce((acc, iconName) => {\n    const icon = icons[iconName];\n    const expanded = !!icon.icon;\n    if (expanded) {\n      acc[icon.iconName] = icon.icon;\n    } else {\n      acc[iconName] = icon;\n    }\n    return acc;\n  }, {});\n}\nfunction defineIcons(prefix, icons) {\n  let params = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  const {\n    skipHooks = false\n  } = params;\n  const normalized = normalizeIcons(icons);\n  if (typeof namespace.hooks.addPack === 'function' && !skipHooks) {\n    namespace.hooks.addPack(prefix, normalizeIcons(icons));\n  } else {\n    namespace.styles[prefix] = _objectSpread2(_objectSpread2({}, namespace.styles[prefix] || {}), normalized);\n  }\n\n  /**\n   * Font Awesome 4 used the prefix of `fa` for all icons. With the introduction\n   * of new styles we needed to differentiate between them. Prefix `fa` is now an alias\n   * for `fas` so we'll ease the upgrade process for our users by automatically defining\n   * this as well.\n   */\n  if (prefix === 'fas') {\n    defineIcons('fa', icons);\n  }\n}\nconst duotonePathRe = [/*#__PURE__*/_wrapRegExp(/path d=\"([^\"]+)\".*path d=\"([^\"]+)\"/, {\n  d1: 1,\n  d2: 2\n}), /*#__PURE__*/_wrapRegExp(/path class=\"([^\"]+)\".*d=\"([^\"]+)\".*path class=\"([^\"]+)\".*d=\"([^\"]+)\"/, {\n  cls1: 1,\n  d1: 2,\n  cls2: 3,\n  d2: 4\n}), /*#__PURE__*/_wrapRegExp(/path class=\"([^\"]+)\".*d=\"([^\"]+)\"/, {\n  cls1: 1,\n  d1: 2\n})];\nconst {\n  styles,\n  shims\n} = namespace;\nconst FAMILY_NAMES = Object.keys(PREFIX_TO_LONG_STYLE);\nconst PREFIXES_FOR_FAMILY = FAMILY_NAMES.reduce((acc, familyId) => {\n  acc[familyId] = Object.keys(PREFIX_TO_LONG_STYLE[familyId]);\n  return acc;\n}, {});\nlet _defaultUsablePrefix = null;\nlet _byUnicode = {};\nlet _byLigature = {};\nlet _byOldName = {};\nlet _byOldUnicode = {};\nlet _byAlias = {};\nfunction isReserved(name) {\n  return ~RESERVED_CLASSES.indexOf(name);\n}\nfunction getIconName(cssPrefix, cls) {\n  const parts = cls.split('-');\n  const prefix = parts[0];\n  const iconName = parts.slice(1).join('-');\n  if (prefix === cssPrefix && iconName !== '' && !isReserved(iconName)) {\n    return iconName;\n  } else {\n    return null;\n  }\n}\nconst build = () => {\n  const lookup = reducer => {\n    return reduce(styles, (o$$1, style, prefix) => {\n      o$$1[prefix] = reduce(style, reducer, {});\n      return o$$1;\n    }, {});\n  };\n  _byUnicode = lookup((acc, icon, iconName) => {\n    if (icon[3]) {\n      acc[icon[3]] = iconName;\n    }\n    if (icon[2]) {\n      const aliases = icon[2].filter(a$$1 => {\n        return typeof a$$1 === 'number';\n      });\n      aliases.forEach(alias => {\n        acc[alias.toString(16)] = iconName;\n      });\n    }\n    return acc;\n  });\n  _byLigature = lookup((acc, icon, iconName) => {\n    acc[iconName] = iconName;\n    if (icon[2]) {\n      const aliases = icon[2].filter(a$$1 => {\n        return typeof a$$1 === 'string';\n      });\n      aliases.forEach(alias => {\n        acc[alias] = iconName;\n      });\n    }\n    return acc;\n  });\n  _byAlias = lookup((acc, icon, iconName) => {\n    const aliases = icon[2];\n    acc[iconName] = iconName;\n    aliases.forEach(alias => {\n      acc[alias] = iconName;\n    });\n    return acc;\n  });\n\n  // If we have a Kit, we can't determine if regular is available since we\n  // could be auto-fetching it. We'll have to assume that it is available.\n  const hasRegular = 'far' in styles || config.autoFetchSvg;\n  const shimLookups = reduce(shims, (acc, shim) => {\n    const maybeNameMaybeUnicode = shim[0];\n    let prefix = shim[1];\n    const iconName = shim[2];\n    if (prefix === 'far' && !hasRegular) {\n      prefix = 'fas';\n    }\n    if (typeof maybeNameMaybeUnicode === 'string') {\n      acc.names[maybeNameMaybeUnicode] = {\n        prefix,\n        iconName\n      };\n    }\n    if (typeof maybeNameMaybeUnicode === 'number') {\n      acc.unicodes[maybeNameMaybeUnicode.toString(16)] = {\n        prefix,\n        iconName\n      };\n    }\n    return acc;\n  }, {\n    names: {},\n    unicodes: {}\n  });\n  _byOldName = shimLookups.names;\n  _byOldUnicode = shimLookups.unicodes;\n  _defaultUsablePrefix = getCanonicalPrefix(config.styleDefault, {\n    family: config.familyDefault\n  });\n};\nonChange(c$$1 => {\n  _defaultUsablePrefix = getCanonicalPrefix(c$$1.styleDefault, {\n    family: config.familyDefault\n  });\n});\nbuild();\nfunction byUnicode(prefix, unicode) {\n  return (_byUnicode[prefix] || {})[unicode];\n}\nfunction byLigature(prefix, ligature) {\n  return (_byLigature[prefix] || {})[ligature];\n}\nfunction byAlias(prefix, alias) {\n  return (_byAlias[prefix] || {})[alias];\n}\nfunction byOldName(name) {\n  return _byOldName[name] || {\n    prefix: null,\n    iconName: null\n  };\n}\nfunction byOldUnicode(unicode) {\n  const oldUnicode = _byOldUnicode[unicode];\n  const newUnicode = byUnicode('fas', unicode);\n  return oldUnicode || (newUnicode ? {\n    prefix: 'fas',\n    iconName: newUnicode\n  } : null) || {\n    prefix: null,\n    iconName: null\n  };\n}\nfunction getDefaultUsablePrefix() {\n  return _defaultUsablePrefix;\n}\nconst emptyCanonicalIcon = () => {\n  return {\n    prefix: null,\n    iconName: null,\n    rest: []\n  };\n};\nfunction getFamilyId(values) {\n  let family = s;\n  const famProps = FAMILY_NAMES.reduce((acc, familyId) => {\n    acc[familyId] = \"\".concat(config.cssPrefix, \"-\").concat(familyId);\n    return acc;\n  }, {});\n  L.forEach(familyId => {\n    if (values.includes(famProps[familyId]) || values.some(v$$1 => PREFIXES_FOR_FAMILY[familyId].includes(v$$1))) {\n      family = familyId;\n    }\n  });\n  return family;\n}\nfunction getCanonicalPrefix(styleOrPrefix) {\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    family = s\n  } = params;\n  const style = PREFIX_TO_STYLE[family][styleOrPrefix];\n\n  // handles the exception of passing in only a family of 'duotone' with no style\n  if (family === t && !styleOrPrefix) {\n    return 'fad';\n  }\n  const prefix = STYLE_TO_PREFIX[family][styleOrPrefix] || STYLE_TO_PREFIX[family][style];\n  const defined = styleOrPrefix in namespace.styles ? styleOrPrefix : null;\n  const result = prefix || defined || null;\n  return result;\n}\nfunction moveNonFaClassesToRest(classNames) {\n  let rest = [];\n  let iconName = null;\n  classNames.forEach(cls => {\n    const result = getIconName(config.cssPrefix, cls);\n    if (result) {\n      iconName = result;\n    } else if (cls) {\n      rest.push(cls);\n    }\n  });\n  return {\n    iconName,\n    rest\n  };\n}\nfunction sortedUniqueValues(arr) {\n  return arr.sort().filter((value, index, arr) => {\n    return arr.indexOf(value) === index;\n  });\n}\nfunction getCanonicalIcon(values) {\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    skipLookups = false\n  } = params;\n  let givenPrefix = null;\n  const faCombinedClasses = Ia.concat(bt$1);\n  const faStyleOrFamilyClasses = sortedUniqueValues(values.filter(cls => faCombinedClasses.includes(cls)));\n  const nonStyleOrFamilyClasses = sortedUniqueValues(values.filter(cls => !Ia.includes(cls)));\n  const faStyles = faStyleOrFamilyClasses.filter(cls => {\n    givenPrefix = cls;\n    return !P.includes(cls);\n  });\n  const [styleFromValues = null] = faStyles;\n  const family = getFamilyId(faStyleOrFamilyClasses);\n  const canonical = _objectSpread2(_objectSpread2({}, moveNonFaClassesToRest(nonStyleOrFamilyClasses)), {}, {\n    prefix: getCanonicalPrefix(styleFromValues, {\n      family\n    })\n  });\n  return _objectSpread2(_objectSpread2(_objectSpread2({}, canonical), getDefaultCanonicalPrefix({\n    values,\n    family,\n    styles,\n    config,\n    canonical,\n    givenPrefix\n  })), applyShimAndAlias(skipLookups, givenPrefix, canonical));\n}\nfunction applyShimAndAlias(skipLookups, givenPrefix, canonical) {\n  let {\n    prefix,\n    iconName\n  } = canonical;\n  if (skipLookups || !prefix || !iconName) {\n    return {\n      prefix,\n      iconName\n    };\n  }\n  const shim = givenPrefix === 'fa' ? byOldName(iconName) : {};\n  const aliasIconName = byAlias(prefix, iconName);\n  iconName = shim.iconName || aliasIconName || iconName;\n  prefix = shim.prefix || prefix;\n  if (prefix === 'far' && !styles['far'] && styles['fas'] && !config.autoFetchSvg) {\n    // Allow a fallback from the regular style to solid if regular is not available\n    // but only if we aren't auto-fetching SVGs\n    prefix = 'fas';\n  }\n  return {\n    prefix,\n    iconName\n  };\n}\nconst newCanonicalFamilies = L.filter(familyId => {\n  return familyId !== s || familyId !== t;\n});\nconst newCanonicalStyles = Object.keys(ga).filter(key => key !== s).map(key => Object.keys(ga[key])).flat();\nfunction getDefaultCanonicalPrefix(prefixOptions) {\n  const {\n    values,\n    family,\n    canonical,\n    givenPrefix = '',\n    styles = {},\n    config: config$$1 = {}\n  } = prefixOptions;\n  const isDuotoneFamily = family === t;\n  const valuesHasDuotone = values.includes('fa-duotone') || values.includes('fad');\n  const defaultFamilyIsDuotone = config$$1.familyDefault === 'duotone';\n  const canonicalPrefixIsDuotone = canonical.prefix === 'fad' || canonical.prefix === 'fa-duotone';\n  if (!isDuotoneFamily && (valuesHasDuotone || defaultFamilyIsDuotone || canonicalPrefixIsDuotone)) {\n    canonical.prefix = 'fad';\n  }\n  if (values.includes('fa-brands') || values.includes('fab')) {\n    canonical.prefix = 'fab';\n  }\n  if (!canonical.prefix && newCanonicalFamilies.includes(family)) {\n    const validPrefix = Object.keys(styles).find(key => newCanonicalStyles.includes(key));\n    if (validPrefix || config$$1.autoFetchSvg) {\n      const defaultPrefix = pt.get(family).defaultShortPrefixId;\n      canonical.prefix = defaultPrefix;\n      canonical.iconName = byAlias(canonical.prefix, canonical.iconName) || canonical.iconName;\n    }\n  }\n  if (canonical.prefix === 'fa' || givenPrefix === 'fa') {\n    // The fa prefix is not canonical. So if it has made it through until this point\n    // we will shift it to the correct prefix.\n    canonical.prefix = getDefaultUsablePrefix() || 'fas';\n  }\n  return canonical;\n}\nclass Library {\n  constructor() {\n    this.definitions = {};\n  }\n  add() {\n    for (var _len = arguments.length, definitions = new Array(_len), _key = 0; _key < _len; _key++) {\n      definitions[_key] = arguments[_key];\n    }\n    const additions = definitions.reduce(this._pullDefinitions, {});\n    Object.keys(additions).forEach(key => {\n      this.definitions[key] = _objectSpread2(_objectSpread2({}, this.definitions[key] || {}), additions[key]);\n      defineIcons(key, additions[key]);\n\n      // TODO can we stop doing this? We can't get the icons by 'fa-solid' any longer so this probably needs to change\n      const longPrefix = PREFIX_TO_LONG_STYLE[s][key];\n      if (longPrefix) defineIcons(longPrefix, additions[key]);\n      build();\n    });\n  }\n  reset() {\n    this.definitions = {};\n  }\n  _pullDefinitions(additions, definition) {\n    const normalized = definition.prefix && definition.iconName && definition.icon ? {\n      0: definition\n    } : definition;\n    Object.keys(normalized).map(key => {\n      const {\n        prefix,\n        iconName,\n        icon\n      } = normalized[key];\n      const aliases = icon[2];\n      if (!additions[prefix]) additions[prefix] = {};\n      if (aliases.length > 0) {\n        aliases.forEach(alias => {\n          if (typeof alias === 'string') {\n            additions[prefix][alias] = icon;\n          }\n        });\n      }\n      additions[prefix][iconName] = icon;\n    });\n    return additions;\n  }\n}\nlet _plugins = [];\nlet _hooks = {};\nconst providers = {};\nconst defaultProviderKeys = Object.keys(providers);\nfunction registerPlugins(nextPlugins, _ref) {\n  let {\n    mixoutsTo: obj\n  } = _ref;\n  _plugins = nextPlugins;\n  _hooks = {};\n  Object.keys(providers).forEach(k => {\n    if (defaultProviderKeys.indexOf(k) === -1) {\n      delete providers[k];\n    }\n  });\n  _plugins.forEach(plugin => {\n    const mixout = plugin.mixout ? plugin.mixout() : {};\n    Object.keys(mixout).forEach(tk => {\n      if (typeof mixout[tk] === 'function') {\n        obj[tk] = mixout[tk];\n      }\n      if (typeof mixout[tk] === 'object') {\n        Object.keys(mixout[tk]).forEach(sk => {\n          if (!obj[tk]) {\n            obj[tk] = {};\n          }\n          obj[tk][sk] = mixout[tk][sk];\n        });\n      }\n    });\n    if (plugin.hooks) {\n      const hooks = plugin.hooks();\n      Object.keys(hooks).forEach(hook => {\n        if (!_hooks[hook]) {\n          _hooks[hook] = [];\n        }\n        _hooks[hook].push(hooks[hook]);\n      });\n    }\n    if (plugin.provides) {\n      plugin.provides(providers);\n    }\n  });\n  return obj;\n}\nfunction chainHooks(hook, accumulator) {\n  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    args[_key - 2] = arguments[_key];\n  }\n  const hookFns = _hooks[hook] || [];\n  hookFns.forEach(hookFn => {\n    accumulator = hookFn.apply(null, [accumulator, ...args]); // eslint-disable-line no-useless-call\n  });\n  return accumulator;\n}\nfunction callHooks(hook) {\n  for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    args[_key2 - 1] = arguments[_key2];\n  }\n  const hookFns = _hooks[hook] || [];\n  hookFns.forEach(hookFn => {\n    hookFn.apply(null, args);\n  });\n  return undefined;\n}\nfunction callProvided() {\n  const hook = arguments[0];\n  const args = Array.prototype.slice.call(arguments, 1);\n  return providers[hook] ? providers[hook].apply(null, args) : undefined;\n}\nfunction findIconDefinition(iconLookup) {\n  if (iconLookup.prefix === 'fa') {\n    iconLookup.prefix = 'fas';\n  }\n  let {\n    iconName\n  } = iconLookup;\n  const prefix = iconLookup.prefix || getDefaultUsablePrefix();\n  if (!iconName) return;\n  iconName = byAlias(prefix, iconName) || iconName;\n  return iconFromMapping(library.definitions, prefix, iconName) || iconFromMapping(namespace.styles, prefix, iconName);\n}\nconst library = new Library();\nconst noAuto = () => {\n  config.autoReplaceSvg = false;\n  config.observeMutations = false;\n  callHooks('noAuto');\n};\nconst dom = {\n  i2svg: function () {\n    let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (IS_DOM) {\n      callHooks('beforeI2svg', params);\n      callProvided('pseudoElements2svg', params);\n      return callProvided('i2svg', params);\n    } else {\n      return Promise.reject(new Error('Operation requires a DOM of some kind.'));\n    }\n  },\n  watch: function () {\n    let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const {\n      autoReplaceSvgRoot\n    } = params;\n    if (config.autoReplaceSvg === false) {\n      config.autoReplaceSvg = true;\n    }\n    config.observeMutations = true;\n    domready(() => {\n      autoReplace({\n        autoReplaceSvgRoot\n      });\n      callHooks('watch', params);\n    });\n  }\n};\nconst parse = {\n  icon: icon => {\n    if (icon === null) {\n      return null;\n    }\n    if (typeof icon === 'object' && icon.prefix && icon.iconName) {\n      return {\n        prefix: icon.prefix,\n        iconName: byAlias(icon.prefix, icon.iconName) || icon.iconName\n      };\n    }\n    if (Array.isArray(icon) && icon.length === 2) {\n      const iconName = icon[1].indexOf('fa-') === 0 ? icon[1].slice(3) : icon[1];\n      const prefix = getCanonicalPrefix(icon[0]);\n      return {\n        prefix,\n        iconName: byAlias(prefix, iconName) || iconName\n      };\n    }\n    if (typeof icon === 'string' && (icon.indexOf(\"\".concat(config.cssPrefix, \"-\")) > -1 || icon.match(ICON_SELECTION_SYNTAX_PATTERN))) {\n      const canonicalIcon = getCanonicalIcon(icon.split(' '), {\n        skipLookups: true\n      });\n      return {\n        prefix: canonicalIcon.prefix || getDefaultUsablePrefix(),\n        iconName: byAlias(canonicalIcon.prefix, canonicalIcon.iconName) || canonicalIcon.iconName\n      };\n    }\n    if (typeof icon === 'string') {\n      const prefix = getDefaultUsablePrefix();\n      return {\n        prefix,\n        iconName: byAlias(prefix, icon) || icon\n      };\n    }\n  }\n};\nconst api = {\n  noAuto,\n  config,\n  dom,\n  parse,\n  library,\n  findIconDefinition,\n  toHtml\n};\nconst autoReplace = function () {\n  let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    autoReplaceSvgRoot = DOCUMENT\n  } = params;\n  if ((Object.keys(namespace.styles).length > 0 || config.autoFetchSvg) && IS_DOM && config.autoReplaceSvg) api.dom.i2svg({\n    node: autoReplaceSvgRoot\n  });\n};\nfunction domVariants(val, abstractCreator) {\n  Object.defineProperty(val, 'abstract', {\n    get: abstractCreator\n  });\n  Object.defineProperty(val, 'html', {\n    get: function () {\n      return val.abstract.map(a => toHtml(a));\n    }\n  });\n  Object.defineProperty(val, 'node', {\n    get: function () {\n      if (!IS_DOM) return;\n      const container = DOCUMENT.createElement('div');\n      container.innerHTML = val.html;\n      return container.children;\n    }\n  });\n  return val;\n}\nfunction asIcon(_ref) {\n  let {\n    children,\n    main,\n    mask,\n    attributes,\n    styles,\n    transform\n  } = _ref;\n  if (transformIsMeaningful(transform) && main.found && !mask.found) {\n    const {\n      width,\n      height\n    } = main;\n    const offset = {\n      x: width / height / 2,\n      y: 0.5\n    };\n    attributes['style'] = joinStyles(_objectSpread2(_objectSpread2({}, styles), {}, {\n      'transform-origin': \"\".concat(offset.x + transform.x / 16, \"em \").concat(offset.y + transform.y / 16, \"em\")\n    }));\n  }\n  return [{\n    tag: 'svg',\n    attributes,\n    children\n  }];\n}\nfunction asSymbol(_ref) {\n  let {\n    prefix,\n    iconName,\n    children,\n    attributes,\n    symbol\n  } = _ref;\n  const id = symbol === true ? \"\".concat(prefix, \"-\").concat(config.cssPrefix, \"-\").concat(iconName) : symbol;\n  return [{\n    tag: 'svg',\n    attributes: {\n      style: 'display: none;'\n    },\n    children: [{\n      tag: 'symbol',\n      attributes: _objectSpread2(_objectSpread2({}, attributes), {}, {\n        id\n      }),\n      children\n    }]\n  }];\n}\nfunction makeInlineSvgAbstract(params) {\n  const {\n    icons: {\n      main,\n      mask\n    },\n    prefix,\n    iconName,\n    transform,\n    symbol,\n    title,\n    maskId,\n    titleId,\n    extra,\n    watchable = false\n  } = params;\n  const {\n    width,\n    height\n  } = mask.found ? mask : main;\n  const isUploadedIcon = Lt.includes(prefix);\n  const attrClass = [config.replacementClass, iconName ? \"\".concat(config.cssPrefix, \"-\").concat(iconName) : ''].filter(c$$1 => extra.classes.indexOf(c$$1) === -1).filter(c$$1 => c$$1 !== '' || !!c$$1).concat(extra.classes).join(' ');\n  let content = {\n    children: [],\n    attributes: _objectSpread2(_objectSpread2({}, extra.attributes), {}, {\n      'data-prefix': prefix,\n      'data-icon': iconName,\n      'class': attrClass,\n      'role': extra.attributes.role || 'img',\n      'xmlns': 'http://www.w3.org/2000/svg',\n      'viewBox': \"0 0 \".concat(width, \" \").concat(height)\n    })\n  };\n  const uploadedIconWidthStyle = isUploadedIcon && !~extra.classes.indexOf('fa-fw') ? {\n    width: \"\".concat(width / height * 16 * 0.0625, \"em\")\n  } : {};\n  if (watchable) {\n    content.attributes[DATA_FA_I2SVG] = '';\n  }\n  if (title) {\n    content.children.push({\n      tag: 'title',\n      attributes: {\n        id: content.attributes['aria-labelledby'] || \"title-\".concat(titleId || nextUniqueId())\n      },\n      children: [title]\n    });\n    delete content.attributes.title;\n  }\n  const args = _objectSpread2(_objectSpread2({}, content), {}, {\n    prefix,\n    iconName,\n    main,\n    mask,\n    maskId,\n    transform,\n    symbol,\n    styles: _objectSpread2(_objectSpread2({}, uploadedIconWidthStyle), extra.styles)\n  });\n  const {\n    children,\n    attributes\n  } = mask.found && main.found ? callProvided('generateAbstractMask', args) || {\n    children: [],\n    attributes: {}\n  } : callProvided('generateAbstractIcon', args) || {\n    children: [],\n    attributes: {}\n  };\n  args.children = children;\n  args.attributes = attributes;\n  if (symbol) {\n    return asSymbol(args);\n  } else {\n    return asIcon(args);\n  }\n}\nfunction makeLayersTextAbstract(params) {\n  const {\n    content,\n    width,\n    height,\n    transform,\n    title,\n    extra,\n    watchable = false\n  } = params;\n  const attributes = _objectSpread2(_objectSpread2(_objectSpread2({}, extra.attributes), title ? {\n    'title': title\n  } : {}), {}, {\n    'class': extra.classes.join(' ')\n  });\n  if (watchable) {\n    attributes[DATA_FA_I2SVG] = '';\n  }\n  const styles = _objectSpread2({}, extra.styles);\n  if (transformIsMeaningful(transform)) {\n    styles['transform'] = transformForCss({\n      transform,\n      startCentered: true,\n      width,\n      height\n    });\n    styles['-webkit-transform'] = styles['transform'];\n  }\n  const styleString = joinStyles(styles);\n  if (styleString.length > 0) {\n    attributes['style'] = styleString;\n  }\n  const val = [];\n  val.push({\n    tag: 'span',\n    attributes,\n    children: [content]\n  });\n  if (title) {\n    val.push({\n      tag: 'span',\n      attributes: {\n        class: 'sr-only'\n      },\n      children: [title]\n    });\n  }\n  return val;\n}\nfunction makeLayersCounterAbstract(params) {\n  const {\n    content,\n    title,\n    extra\n  } = params;\n  const attributes = _objectSpread2(_objectSpread2(_objectSpread2({}, extra.attributes), title ? {\n    'title': title\n  } : {}), {}, {\n    'class': extra.classes.join(' ')\n  });\n  const styleString = joinStyles(extra.styles);\n  if (styleString.length > 0) {\n    attributes['style'] = styleString;\n  }\n  const val = [];\n  val.push({\n    tag: 'span',\n    attributes,\n    children: [content]\n  });\n  if (title) {\n    val.push({\n      tag: 'span',\n      attributes: {\n        class: 'sr-only'\n      },\n      children: [title]\n    });\n  }\n  return val;\n}\nconst {\n  styles: styles$1\n} = namespace;\nfunction asFoundIcon(icon) {\n  const width = icon[0];\n  const height = icon[1];\n  const [vectorData] = icon.slice(4);\n  let element = null;\n  if (Array.isArray(vectorData)) {\n    element = {\n      tag: 'g',\n      attributes: {\n        class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.GROUP)\n      },\n      children: [{\n        tag: 'path',\n        attributes: {\n          class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.SECONDARY),\n          fill: 'currentColor',\n          d: vectorData[0]\n        }\n      }, {\n        tag: 'path',\n        attributes: {\n          class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.PRIMARY),\n          fill: 'currentColor',\n          d: vectorData[1]\n        }\n      }]\n    };\n  } else {\n    element = {\n      tag: 'path',\n      attributes: {\n        fill: 'currentColor',\n        d: vectorData\n      }\n    };\n  }\n  return {\n    found: true,\n    width,\n    height,\n    icon: element\n  };\n}\nconst missingIconResolutionMixin = {\n  found: false,\n  width: 512,\n  height: 512\n};\nfunction maybeNotifyMissing(iconName, prefix) {\n  if (!PRODUCTION && !config.showMissingIcons && iconName) {\n    console.error(\"Icon with name \\\"\".concat(iconName, \"\\\" and prefix \\\"\").concat(prefix, \"\\\" is missing.\"));\n  }\n}\nfunction findIcon(iconName, prefix) {\n  let givenPrefix = prefix;\n  if (prefix === 'fa' && config.styleDefault !== null) {\n    prefix = getDefaultUsablePrefix();\n  }\n  return new Promise((resolve, reject) => {\n    if (givenPrefix === 'fa') {\n      const shim = byOldName(iconName) || {};\n      iconName = shim.iconName || iconName;\n      prefix = shim.prefix || prefix;\n    }\n    if (iconName && prefix && styles$1[prefix] && styles$1[prefix][iconName]) {\n      const icon = styles$1[prefix][iconName];\n      return resolve(asFoundIcon(icon));\n    }\n    maybeNotifyMissing(iconName, prefix);\n    resolve(_objectSpread2(_objectSpread2({}, missingIconResolutionMixin), {}, {\n      icon: config.showMissingIcons && iconName ? callProvided('missingIconAbstract') || {} : {}\n    }));\n  });\n}\nconst noop$1 = () => {};\nconst p$2 = config.measurePerformance && PERFORMANCE && PERFORMANCE.mark && PERFORMANCE.measure ? PERFORMANCE : {\n  mark: noop$1,\n  measure: noop$1\n};\nconst preamble = \"FA \\\"6.7.2\\\"\";\nconst begin = name => {\n  p$2.mark(\"\".concat(preamble, \" \").concat(name, \" begins\"));\n  return () => end(name);\n};\nconst end = name => {\n  p$2.mark(\"\".concat(preamble, \" \").concat(name, \" ends\"));\n  p$2.measure(\"\".concat(preamble, \" \").concat(name), \"\".concat(preamble, \" \").concat(name, \" begins\"), \"\".concat(preamble, \" \").concat(name, \" ends\"));\n};\nvar perf = {\n  begin,\n  end\n};\nconst noop$2 = () => {};\nfunction isWatched(node) {\n  const i2svg = node.getAttribute ? node.getAttribute(DATA_FA_I2SVG) : null;\n  return typeof i2svg === 'string';\n}\nfunction hasPrefixAndIcon(node) {\n  const prefix = node.getAttribute ? node.getAttribute(DATA_PREFIX) : null;\n  const icon = node.getAttribute ? node.getAttribute(DATA_ICON) : null;\n  return prefix && icon;\n}\nfunction hasBeenReplaced(node) {\n  return node && node.classList && node.classList.contains && node.classList.contains(config.replacementClass);\n}\nfunction getMutator() {\n  if (config.autoReplaceSvg === true) {\n    return mutators.replace;\n  }\n  const mutator = mutators[config.autoReplaceSvg];\n  return mutator || mutators.replace;\n}\nfunction createElementNS(tag) {\n  return DOCUMENT.createElementNS('http://www.w3.org/2000/svg', tag);\n}\nfunction createElement(tag) {\n  return DOCUMENT.createElement(tag);\n}\nfunction convertSVG(abstractObj) {\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    ceFn = abstractObj.tag === 'svg' ? createElementNS : createElement\n  } = params;\n  if (typeof abstractObj === 'string') {\n    return DOCUMENT.createTextNode(abstractObj);\n  }\n  const tag = ceFn(abstractObj.tag);\n  Object.keys(abstractObj.attributes || []).forEach(function (key) {\n    tag.setAttribute(key, abstractObj.attributes[key]);\n  });\n  const children = abstractObj.children || [];\n  children.forEach(function (child) {\n    tag.appendChild(convertSVG(child, {\n      ceFn\n    }));\n  });\n  return tag;\n}\nfunction nodeAsComment(node) {\n  let comment = \" \".concat(node.outerHTML, \" \");\n  /* BEGIN.ATTRIBUTION */\n  comment = \"\".concat(comment, \"Font Awesome fontawesome.com \");\n  /* END.ATTRIBUTION */\n  return comment;\n}\nconst mutators = {\n  replace: function (mutation) {\n    const node = mutation[0];\n    if (node.parentNode) {\n      mutation[1].forEach(abstract => {\n        node.parentNode.insertBefore(convertSVG(abstract), node);\n      });\n      if (node.getAttribute(DATA_FA_I2SVG) === null && config.keepOriginalSource) {\n        let comment = DOCUMENT.createComment(nodeAsComment(node));\n        node.parentNode.replaceChild(comment, node);\n      } else {\n        node.remove();\n      }\n    }\n  },\n  nest: function (mutation) {\n    const node = mutation[0];\n    const abstract = mutation[1];\n\n    // If we already have a replaced node we do not want to continue nesting within it.\n    // Short-circuit to the standard replacement\n    if (~classArray(node).indexOf(config.replacementClass)) {\n      return mutators.replace(mutation);\n    }\n    const forSvg = new RegExp(\"\".concat(config.cssPrefix, \"-.*\"));\n    delete abstract[0].attributes.id;\n    if (abstract[0].attributes.class) {\n      const splitClasses = abstract[0].attributes.class.split(' ').reduce((acc, cls) => {\n        if (cls === config.replacementClass || cls.match(forSvg)) {\n          acc.toSvg.push(cls);\n        } else {\n          acc.toNode.push(cls);\n        }\n        return acc;\n      }, {\n        toNode: [],\n        toSvg: []\n      });\n      abstract[0].attributes.class = splitClasses.toSvg.join(' ');\n      if (splitClasses.toNode.length === 0) {\n        node.removeAttribute('class');\n      } else {\n        node.setAttribute('class', splitClasses.toNode.join(' '));\n      }\n    }\n    const newInnerHTML = abstract.map(a => toHtml(a)).join('\\n');\n    node.setAttribute(DATA_FA_I2SVG, '');\n    node.innerHTML = newInnerHTML;\n  }\n};\nfunction performOperationSync(op) {\n  op();\n}\nfunction perform(mutations, callback) {\n  const callbackFunction = typeof callback === 'function' ? callback : noop$2;\n  if (mutations.length === 0) {\n    callbackFunction();\n  } else {\n    let frame = performOperationSync;\n    if (config.mutateApproach === MUTATION_APPROACH_ASYNC) {\n      frame = WINDOW.requestAnimationFrame || performOperationSync;\n    }\n    frame(() => {\n      const mutator = getMutator();\n      const mark = perf.begin('mutate');\n      mutations.map(mutator);\n      mark();\n      callbackFunction();\n    });\n  }\n}\nlet disabled = false;\nfunction disableObservation() {\n  disabled = true;\n}\nfunction enableObservation() {\n  disabled = false;\n}\nlet mo = null;\nfunction observe(options) {\n  if (!MUTATION_OBSERVER) {\n    return;\n  }\n  if (!config.observeMutations) {\n    return;\n  }\n  const {\n    treeCallback = noop$2,\n    nodeCallback = noop$2,\n    pseudoElementsCallback = noop$2,\n    observeMutationsRoot = DOCUMENT\n  } = options;\n  mo = new MUTATION_OBSERVER(objects => {\n    if (disabled) return;\n    const defaultPrefix = getDefaultUsablePrefix();\n    toArray(objects).forEach(mutationRecord => {\n      if (mutationRecord.type === 'childList' && mutationRecord.addedNodes.length > 0 && !isWatched(mutationRecord.addedNodes[0])) {\n        if (config.searchPseudoElements) {\n          pseudoElementsCallback(mutationRecord.target);\n        }\n        treeCallback(mutationRecord.target);\n      }\n      if (mutationRecord.type === 'attributes' && mutationRecord.target.parentNode && config.searchPseudoElements) {\n        pseudoElementsCallback(mutationRecord.target.parentNode);\n      }\n      if (mutationRecord.type === 'attributes' && isWatched(mutationRecord.target) && ~ATTRIBUTES_WATCHED_FOR_MUTATION.indexOf(mutationRecord.attributeName)) {\n        if (mutationRecord.attributeName === 'class' && hasPrefixAndIcon(mutationRecord.target)) {\n          const {\n            prefix,\n            iconName\n          } = getCanonicalIcon(classArray(mutationRecord.target));\n          mutationRecord.target.setAttribute(DATA_PREFIX, prefix || defaultPrefix);\n          if (iconName) mutationRecord.target.setAttribute(DATA_ICON, iconName);\n        } else if (hasBeenReplaced(mutationRecord.target)) {\n          nodeCallback(mutationRecord.target);\n        }\n      }\n    });\n  });\n  if (!IS_DOM) return;\n  mo.observe(observeMutationsRoot, {\n    childList: true,\n    attributes: true,\n    characterData: true,\n    subtree: true\n  });\n}\nfunction disconnect() {\n  if (!mo) return;\n  mo.disconnect();\n}\nfunction styleParser(node) {\n  const style = node.getAttribute('style');\n  let val = [];\n  if (style) {\n    val = style.split(';').reduce((acc, style) => {\n      const styles = style.split(':');\n      const prop = styles[0];\n      const value = styles.slice(1);\n      if (prop && value.length > 0) {\n        acc[prop] = value.join(':').trim();\n      }\n      return acc;\n    }, {});\n  }\n  return val;\n}\nfunction classParser(node) {\n  const existingPrefix = node.getAttribute('data-prefix');\n  const existingIconName = node.getAttribute('data-icon');\n  const innerText = node.innerText !== undefined ? node.innerText.trim() : '';\n  let val = getCanonicalIcon(classArray(node));\n  if (!val.prefix) {\n    val.prefix = getDefaultUsablePrefix();\n  }\n  if (existingPrefix && existingIconName) {\n    val.prefix = existingPrefix;\n    val.iconName = existingIconName;\n  }\n  if (val.iconName && val.prefix) {\n    return val;\n  }\n  if (val.prefix && innerText.length > 0) {\n    val.iconName = byLigature(val.prefix, node.innerText) || byUnicode(val.prefix, toHex(node.innerText));\n  }\n  if (!val.iconName && config.autoFetchSvg && node.firstChild && node.firstChild.nodeType === Node.TEXT_NODE) {\n    val.iconName = node.firstChild.data;\n  }\n  return val;\n}\nfunction attributesParser(node) {\n  const extraAttributes = toArray(node.attributes).reduce((acc, attr) => {\n    if (acc.name !== 'class' && acc.name !== 'style') {\n      acc[attr.name] = attr.value;\n    }\n    return acc;\n  }, {});\n  const title = node.getAttribute('title');\n  const titleId = node.getAttribute('data-fa-title-id');\n  if (config.autoA11y) {\n    if (title) {\n      extraAttributes['aria-labelledby'] = \"\".concat(config.replacementClass, \"-title-\").concat(titleId || nextUniqueId());\n    } else {\n      extraAttributes['aria-hidden'] = 'true';\n      extraAttributes['focusable'] = 'false';\n    }\n  }\n  return extraAttributes;\n}\nfunction blankMeta() {\n  return {\n    iconName: null,\n    title: null,\n    titleId: null,\n    prefix: null,\n    transform: meaninglessTransform,\n    symbol: false,\n    mask: {\n      iconName: null,\n      prefix: null,\n      rest: []\n    },\n    maskId: null,\n    extra: {\n      classes: [],\n      styles: {},\n      attributes: {}\n    }\n  };\n}\nfunction parseMeta(node) {\n  let parser = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    styleParser: true\n  };\n  const {\n    iconName,\n    prefix,\n    rest: extraClasses\n  } = classParser(node);\n  const extraAttributes = attributesParser(node);\n  const pluginMeta = chainHooks('parseNodeAttributes', {}, node);\n  let extraStyles = parser.styleParser ? styleParser(node) : [];\n  return _objectSpread2({\n    iconName,\n    title: node.getAttribute('title'),\n    titleId: node.getAttribute('data-fa-title-id'),\n    prefix,\n    transform: meaninglessTransform,\n    mask: {\n      iconName: null,\n      prefix: null,\n      rest: []\n    },\n    maskId: null,\n    symbol: false,\n    extra: {\n      classes: extraClasses,\n      styles: extraStyles,\n      attributes: extraAttributes\n    }\n  }, pluginMeta);\n}\nconst {\n  styles: styles$2\n} = namespace;\nfunction generateMutation(node) {\n  const nodeMeta = config.autoReplaceSvg === 'nest' ? parseMeta(node, {\n    styleParser: false\n  }) : parseMeta(node);\n  if (~nodeMeta.extra.classes.indexOf(LAYERS_TEXT_CLASSNAME)) {\n    return callProvided('generateLayersText', node, nodeMeta);\n  } else {\n    return callProvided('generateSvgReplacementMutation', node, nodeMeta);\n  }\n}\nfunction getKnownPrefixes() {\n  return [...Ft, ...Ia];\n}\nfunction onTree(root) {\n  let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  if (!IS_DOM) return Promise.resolve();\n  const htmlClassList = DOCUMENT.documentElement.classList;\n  const hclAdd = suffix => htmlClassList.add(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\n  const hclRemove = suffix => htmlClassList.remove(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\n  const prefixes = config.autoFetchSvg ? getKnownPrefixes() : P.concat(Object.keys(styles$2));\n  if (!prefixes.includes('fa')) {\n    prefixes.push('fa');\n  }\n  const prefixesDomQuery = [\".\".concat(LAYERS_TEXT_CLASSNAME, \":not([\").concat(DATA_FA_I2SVG, \"])\")].concat(prefixes.map(p$$1 => \".\".concat(p$$1, \":not([\").concat(DATA_FA_I2SVG, \"])\"))).join(', ');\n  if (prefixesDomQuery.length === 0) {\n    return Promise.resolve();\n  }\n  let candidates = [];\n  try {\n    candidates = toArray(root.querySelectorAll(prefixesDomQuery));\n  } catch (e$$1) {\n    // noop\n  }\n  if (candidates.length > 0) {\n    hclAdd('pending');\n    hclRemove('complete');\n  } else {\n    return Promise.resolve();\n  }\n  const mark = perf.begin('onTree');\n  const mutations = candidates.reduce((acc, node) => {\n    try {\n      const mutation = generateMutation(node);\n      if (mutation) {\n        acc.push(mutation);\n      }\n    } catch (e$$1) {\n      if (!PRODUCTION) {\n        if (e$$1.name === 'MissingIcon') {\n          console.error(e$$1);\n        }\n      }\n    }\n    return acc;\n  }, []);\n  return new Promise((resolve, reject) => {\n    Promise.all(mutations).then(resolvedMutations => {\n      perform(resolvedMutations, () => {\n        hclAdd('active');\n        hclAdd('complete');\n        hclRemove('pending');\n        if (typeof callback === 'function') callback();\n        mark();\n        resolve();\n      });\n    }).catch(e$$1 => {\n      mark();\n      reject(e$$1);\n    });\n  });\n}\nfunction onNode(node) {\n  let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  generateMutation(node).then(mutation => {\n    if (mutation) {\n      perform([mutation], callback);\n    }\n  });\n}\nfunction resolveIcons(next) {\n  return function (maybeIconDefinition) {\n    let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const iconDefinition = (maybeIconDefinition || {}).icon ? maybeIconDefinition : findIconDefinition(maybeIconDefinition || {});\n    let {\n      mask\n    } = params;\n    if (mask) {\n      mask = (mask || {}).icon ? mask : findIconDefinition(mask || {});\n    }\n    return next(iconDefinition, _objectSpread2(_objectSpread2({}, params), {}, {\n      mask\n    }));\n  };\n}\nconst render = function (iconDefinition) {\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    transform = meaninglessTransform,\n    symbol = false,\n    mask = null,\n    maskId = null,\n    title = null,\n    titleId = null,\n    classes = [],\n    attributes = {},\n    styles = {}\n  } = params;\n  if (!iconDefinition) return;\n  const {\n    prefix,\n    iconName,\n    icon\n  } = iconDefinition;\n  return domVariants(_objectSpread2({\n    type: 'icon'\n  }, iconDefinition), () => {\n    callHooks('beforeDOMElementCreation', {\n      iconDefinition,\n      params\n    });\n    if (config.autoA11y) {\n      if (title) {\n        attributes['aria-labelledby'] = \"\".concat(config.replacementClass, \"-title-\").concat(titleId || nextUniqueId());\n      } else {\n        attributes['aria-hidden'] = 'true';\n        attributes['focusable'] = 'false';\n      }\n    }\n    return makeInlineSvgAbstract({\n      icons: {\n        main: asFoundIcon(icon),\n        mask: mask ? asFoundIcon(mask.icon) : {\n          found: false,\n          width: null,\n          height: null,\n          icon: {}\n        }\n      },\n      prefix,\n      iconName,\n      transform: _objectSpread2(_objectSpread2({}, meaninglessTransform), transform),\n      symbol,\n      title,\n      maskId,\n      titleId,\n      extra: {\n        attributes,\n        styles,\n        classes\n      }\n    });\n  });\n};\nvar ReplaceElements = {\n  mixout() {\n    return {\n      icon: resolveIcons(render)\n    };\n  },\n  hooks() {\n    return {\n      mutationObserverCallbacks(accumulator) {\n        accumulator.treeCallback = onTree;\n        accumulator.nodeCallback = onNode;\n        return accumulator;\n      }\n    };\n  },\n  provides(providers$$1) {\n    providers$$1.i2svg = function (params) {\n      const {\n        node = DOCUMENT,\n        callback = () => {}\n      } = params;\n      return onTree(node, callback);\n    };\n    providers$$1.generateSvgReplacementMutation = function (node, nodeMeta) {\n      const {\n        iconName,\n        title,\n        titleId,\n        prefix,\n        transform,\n        symbol,\n        mask,\n        maskId,\n        extra\n      } = nodeMeta;\n      return new Promise((resolve, reject) => {\n        Promise.all([findIcon(iconName, prefix), mask.iconName ? findIcon(mask.iconName, mask.prefix) : Promise.resolve({\n          found: false,\n          width: 512,\n          height: 512,\n          icon: {}\n        })]).then(_ref => {\n          let [main, mask] = _ref;\n          resolve([node, makeInlineSvgAbstract({\n            icons: {\n              main,\n              mask\n            },\n            prefix,\n            iconName,\n            transform,\n            symbol,\n            maskId,\n            title,\n            titleId,\n            extra,\n            watchable: true\n          })]);\n        }).catch(reject);\n      });\n    };\n    providers$$1.generateAbstractIcon = function (_ref2) {\n      let {\n        children,\n        attributes,\n        main,\n        transform,\n        styles\n      } = _ref2;\n      const styleString = joinStyles(styles);\n      if (styleString.length > 0) {\n        attributes['style'] = styleString;\n      }\n      let nextChild;\n      if (transformIsMeaningful(transform)) {\n        nextChild = callProvided('generateAbstractTransformGrouping', {\n          main,\n          transform,\n          containerWidth: main.width,\n          iconWidth: main.width\n        });\n      }\n      children.push(nextChild || main.icon);\n      return {\n        children,\n        attributes\n      };\n    };\n  }\n};\nvar Layers = {\n  mixout() {\n    return {\n      layer(assembler) {\n        let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        const {\n          classes = []\n        } = params;\n        return domVariants({\n          type: 'layer'\n        }, () => {\n          callHooks('beforeDOMElementCreation', {\n            assembler,\n            params\n          });\n          let children = [];\n          assembler(args => {\n            Array.isArray(args) ? args.map(a => {\n              children = children.concat(a.abstract);\n            }) : children = children.concat(args.abstract);\n          });\n          return [{\n            tag: 'span',\n            attributes: {\n              class: [\"\".concat(config.cssPrefix, \"-layers\"), ...classes].join(' ')\n            },\n            children\n          }];\n        });\n      }\n    };\n  }\n};\nvar LayersCounter = {\n  mixout() {\n    return {\n      counter(content) {\n        let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        const {\n          title = null,\n          classes = [],\n          attributes = {},\n          styles = {}\n        } = params;\n        return domVariants({\n          type: 'counter',\n          content\n        }, () => {\n          callHooks('beforeDOMElementCreation', {\n            content,\n            params\n          });\n          return makeLayersCounterAbstract({\n            content: content.toString(),\n            title,\n            extra: {\n              attributes,\n              styles,\n              classes: [\"\".concat(config.cssPrefix, \"-layers-counter\"), ...classes]\n            }\n          });\n        });\n      }\n    };\n  }\n};\nvar LayersText = {\n  mixout() {\n    return {\n      text(content) {\n        let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        const {\n          transform = meaninglessTransform,\n          title = null,\n          classes = [],\n          attributes = {},\n          styles = {}\n        } = params;\n        return domVariants({\n          type: 'text',\n          content\n        }, () => {\n          callHooks('beforeDOMElementCreation', {\n            content,\n            params\n          });\n          return makeLayersTextAbstract({\n            content,\n            transform: _objectSpread2(_objectSpread2({}, meaninglessTransform), transform),\n            title,\n            extra: {\n              attributes,\n              styles,\n              classes: [\"\".concat(config.cssPrefix, \"-layers-text\"), ...classes]\n            }\n          });\n        });\n      }\n    };\n  },\n  provides(providers$$1) {\n    providers$$1.generateLayersText = function (node, nodeMeta) {\n      const {\n        title,\n        transform,\n        extra\n      } = nodeMeta;\n      let width = null;\n      let height = null;\n      if (IS_IE) {\n        const computedFontSize = parseInt(getComputedStyle(node).fontSize, 10);\n        const boundingClientRect = node.getBoundingClientRect();\n        width = boundingClientRect.width / computedFontSize;\n        height = boundingClientRect.height / computedFontSize;\n      }\n      if (config.autoA11y && !title) {\n        extra.attributes['aria-hidden'] = 'true';\n      }\n      return Promise.resolve([node, makeLayersTextAbstract({\n        content: node.innerHTML,\n        width,\n        height,\n        transform,\n        title,\n        extra,\n        watchable: true\n      })]);\n    };\n  }\n};\nconst CLEAN_CONTENT_PATTERN = new RegExp('\\u{22}', 'ug');\nconst SECONDARY_UNICODE_RANGE = [1105920, 1112319];\nconst _FONT_FAMILY_WEIGHT_TO_PREFIX = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\n  FontAwesome: {\n    normal: 'fas',\n    400: 'fas'\n  }\n}), lt), wa), Yt);\nconst FONT_FAMILY_WEIGHT_TO_PREFIX = Object.keys(_FONT_FAMILY_WEIGHT_TO_PREFIX).reduce((acc, key) => {\n  acc[key.toLowerCase()] = _FONT_FAMILY_WEIGHT_TO_PREFIX[key];\n  return acc;\n}, {});\nconst FONT_FAMILY_WEIGHT_FALLBACK = Object.keys(FONT_FAMILY_WEIGHT_TO_PREFIX).reduce((acc, fontFamily) => {\n  const weights = FONT_FAMILY_WEIGHT_TO_PREFIX[fontFamily];\n  acc[fontFamily] = weights[900] || [...Object.entries(weights)][0][1];\n  return acc;\n}, {});\nfunction hexValueFromContent(content) {\n  const cleaned = content.replace(CLEAN_CONTENT_PATTERN, '');\n  const codePoint = codePointAt(cleaned, 0);\n  const isPrependTen = codePoint >= SECONDARY_UNICODE_RANGE[0] && codePoint <= SECONDARY_UNICODE_RANGE[1];\n  const isDoubled = cleaned.length === 2 ? cleaned[0] === cleaned[1] : false;\n  return {\n    value: isDoubled ? toHex(cleaned[0]) : toHex(cleaned),\n    isSecondary: isPrependTen || isDoubled\n  };\n}\nfunction getPrefix(fontFamily, fontWeight) {\n  const fontFamilySanitized = fontFamily.replace(/^['\"]|['\"]$/g, '').toLowerCase();\n  const fontWeightInteger = parseInt(fontWeight);\n  const fontWeightSanitized = isNaN(fontWeightInteger) ? 'normal' : fontWeightInteger;\n  return (FONT_FAMILY_WEIGHT_TO_PREFIX[fontFamilySanitized] || {})[fontWeightSanitized] || FONT_FAMILY_WEIGHT_FALLBACK[fontFamilySanitized];\n}\nfunction replaceForPosition(node, position) {\n  const pendingAttribute = \"\".concat(DATA_FA_PSEUDO_ELEMENT_PENDING).concat(position.replace(':', '-'));\n  return new Promise((resolve, reject) => {\n    if (node.getAttribute(pendingAttribute) !== null) {\n      // This node is already being processed\n      return resolve();\n    }\n    const children = toArray(node.children);\n    const alreadyProcessedPseudoElement = children.filter(c$$1 => c$$1.getAttribute(DATA_FA_PSEUDO_ELEMENT) === position)[0];\n    const styles = WINDOW.getComputedStyle(node, position);\n    const fontFamily = styles.getPropertyValue('font-family');\n    const fontFamilyMatch = fontFamily.match(FONT_FAMILY_PATTERN);\n    const fontWeight = styles.getPropertyValue('font-weight');\n    const content = styles.getPropertyValue('content');\n    if (alreadyProcessedPseudoElement && !fontFamilyMatch) {\n      // If we've already processed it but the current computed style does not result in a font-family,\n      // that probably means that a class name that was previously present to make the icon has been\n      // removed. So we now should delete the icon.\n      node.removeChild(alreadyProcessedPseudoElement);\n      return resolve();\n    } else if (fontFamilyMatch && content !== 'none' && content !== '') {\n      const content = styles.getPropertyValue('content');\n      let prefix = getPrefix(fontFamily, fontWeight);\n      const {\n        value: hexValue,\n        isSecondary\n      } = hexValueFromContent(content);\n      const isV4 = fontFamilyMatch[0].startsWith('FontAwesome');\n      let iconName = byUnicode(prefix, hexValue);\n      let iconIdentifier = iconName;\n      if (isV4) {\n        const iconName4 = byOldUnicode(hexValue);\n        if (iconName4.iconName && iconName4.prefix) {\n          iconName = iconName4.iconName;\n          prefix = iconName4.prefix;\n        }\n      }\n\n      // Only convert the pseudo element in this ::before/::after position into an icon if we haven't\n      // already done so with the same prefix and iconName\n      if (iconName && !isSecondary && (!alreadyProcessedPseudoElement || alreadyProcessedPseudoElement.getAttribute(DATA_PREFIX) !== prefix || alreadyProcessedPseudoElement.getAttribute(DATA_ICON) !== iconIdentifier)) {\n        node.setAttribute(pendingAttribute, iconIdentifier);\n        if (alreadyProcessedPseudoElement) {\n          // Delete the old one, since we're replacing it with a new one\n          node.removeChild(alreadyProcessedPseudoElement);\n        }\n        const meta = blankMeta();\n        const {\n          extra\n        } = meta;\n        extra.attributes[DATA_FA_PSEUDO_ELEMENT] = position;\n        findIcon(iconName, prefix).then(main => {\n          const abstract = makeInlineSvgAbstract(_objectSpread2(_objectSpread2({}, meta), {}, {\n            icons: {\n              main,\n              mask: emptyCanonicalIcon()\n            },\n            prefix,\n            iconName: iconIdentifier,\n            extra,\n            watchable: true\n          }));\n          const element = DOCUMENT.createElementNS('http://www.w3.org/2000/svg', 'svg');\n          if (position === '::before') {\n            node.insertBefore(element, node.firstChild);\n          } else {\n            node.appendChild(element);\n          }\n          element.outerHTML = abstract.map(a$$1 => toHtml(a$$1)).join('\\n');\n          node.removeAttribute(pendingAttribute);\n          resolve();\n        }).catch(reject);\n      } else {\n        resolve();\n      }\n    } else {\n      resolve();\n    }\n  });\n}\nfunction replace(node) {\n  return Promise.all([replaceForPosition(node, '::before'), replaceForPosition(node, '::after')]);\n}\nfunction processable(node) {\n  return node.parentNode !== document.head && !~TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS.indexOf(node.tagName.toUpperCase()) && !node.getAttribute(DATA_FA_PSEUDO_ELEMENT) && (!node.parentNode || node.parentNode.tagName !== 'svg');\n}\nfunction searchPseudoElements(root) {\n  if (!IS_DOM) return;\n  return new Promise((resolve, reject) => {\n    const operations = toArray(root.querySelectorAll('*')).filter(processable).map(replace);\n    const end = perf.begin('searchPseudoElements');\n    disableObservation();\n    Promise.all(operations).then(() => {\n      end();\n      enableObservation();\n      resolve();\n    }).catch(() => {\n      end();\n      enableObservation();\n      reject();\n    });\n  });\n}\nvar PseudoElements = {\n  hooks() {\n    return {\n      mutationObserverCallbacks(accumulator) {\n        accumulator.pseudoElementsCallback = searchPseudoElements;\n        return accumulator;\n      }\n    };\n  },\n  provides(providers) {\n    providers.pseudoElements2svg = function (params) {\n      const {\n        node = DOCUMENT\n      } = params;\n      if (config.searchPseudoElements) {\n        searchPseudoElements(node);\n      }\n    };\n  }\n};\nlet _unwatched = false;\nvar MutationObserver$1 = {\n  mixout() {\n    return {\n      dom: {\n        unwatch() {\n          disableObservation();\n          _unwatched = true;\n        }\n      }\n    };\n  },\n  hooks() {\n    return {\n      bootstrap() {\n        observe(chainHooks('mutationObserverCallbacks', {}));\n      },\n      noAuto() {\n        disconnect();\n      },\n      watch(params) {\n        const {\n          observeMutationsRoot\n        } = params;\n        if (_unwatched) {\n          enableObservation();\n        } else {\n          observe(chainHooks('mutationObserverCallbacks', {\n            observeMutationsRoot\n          }));\n        }\n      }\n    };\n  }\n};\nconst parseTransformString = transformString => {\n  let transform = {\n    size: 16,\n    x: 0,\n    y: 0,\n    flipX: false,\n    flipY: false,\n    rotate: 0\n  };\n  return transformString.toLowerCase().split(' ').reduce((acc, n) => {\n    const parts = n.toLowerCase().split('-');\n    const first = parts[0];\n    let rest = parts.slice(1).join('-');\n    if (first && rest === 'h') {\n      acc.flipX = true;\n      return acc;\n    }\n    if (first && rest === 'v') {\n      acc.flipY = true;\n      return acc;\n    }\n    rest = parseFloat(rest);\n    if (isNaN(rest)) {\n      return acc;\n    }\n    switch (first) {\n      case 'grow':\n        acc.size = acc.size + rest;\n        break;\n      case 'shrink':\n        acc.size = acc.size - rest;\n        break;\n      case 'left':\n        acc.x = acc.x - rest;\n        break;\n      case 'right':\n        acc.x = acc.x + rest;\n        break;\n      case 'up':\n        acc.y = acc.y - rest;\n        break;\n      case 'down':\n        acc.y = acc.y + rest;\n        break;\n      case 'rotate':\n        acc.rotate = acc.rotate + rest;\n        break;\n    }\n    return acc;\n  }, transform);\n};\nvar PowerTransforms = {\n  mixout() {\n    return {\n      parse: {\n        transform: transformString => {\n          return parseTransformString(transformString);\n        }\n      }\n    };\n  },\n  hooks() {\n    return {\n      parseNodeAttributes(accumulator, node) {\n        const transformString = node.getAttribute('data-fa-transform');\n        if (transformString) {\n          accumulator.transform = parseTransformString(transformString);\n        }\n        return accumulator;\n      }\n    };\n  },\n  provides(providers) {\n    providers.generateAbstractTransformGrouping = function (_ref) {\n      let {\n        main,\n        transform,\n        containerWidth,\n        iconWidth\n      } = _ref;\n      const outer = {\n        transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\n      };\n      const innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\n      const innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\n      const innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\n      const inner = {\n        transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\n      };\n      const path = {\n        transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\n      };\n      const operations = {\n        outer,\n        inner,\n        path\n      };\n      return {\n        tag: 'g',\n        attributes: _objectSpread2({}, operations.outer),\n        children: [{\n          tag: 'g',\n          attributes: _objectSpread2({}, operations.inner),\n          children: [{\n            tag: main.icon.tag,\n            children: main.icon.children,\n            attributes: _objectSpread2(_objectSpread2({}, main.icon.attributes), operations.path)\n          }]\n        }]\n      };\n    };\n  }\n};\nconst ALL_SPACE = {\n  x: 0,\n  y: 0,\n  width: '100%',\n  height: '100%'\n};\nfunction fillBlack(abstract) {\n  let force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  if (abstract.attributes && (abstract.attributes.fill || force)) {\n    abstract.attributes.fill = 'black';\n  }\n  return abstract;\n}\nfunction deGroup(abstract) {\n  if (abstract.tag === 'g') {\n    return abstract.children;\n  } else {\n    return [abstract];\n  }\n}\nvar Masks = {\n  hooks() {\n    return {\n      parseNodeAttributes(accumulator, node) {\n        const maskData = node.getAttribute('data-fa-mask');\n        const mask = !maskData ? emptyCanonicalIcon() : getCanonicalIcon(maskData.split(' ').map(i => i.trim()));\n        if (!mask.prefix) {\n          mask.prefix = getDefaultUsablePrefix();\n        }\n        accumulator.mask = mask;\n        accumulator.maskId = node.getAttribute('data-fa-mask-id');\n        return accumulator;\n      }\n    };\n  },\n  provides(providers) {\n    providers.generateAbstractMask = function (_ref) {\n      let {\n        children,\n        attributes,\n        main,\n        mask,\n        maskId: explicitMaskId,\n        transform\n      } = _ref;\n      const {\n        width: mainWidth,\n        icon: mainPath\n      } = main;\n      const {\n        width: maskWidth,\n        icon: maskPath\n      } = mask;\n      const trans = transformForSvg({\n        transform,\n        containerWidth: maskWidth,\n        iconWidth: mainWidth\n      });\n      const maskRect = {\n        tag: 'rect',\n        attributes: _objectSpread2(_objectSpread2({}, ALL_SPACE), {}, {\n          fill: 'white'\n        })\n      };\n      const maskInnerGroupChildrenMixin = mainPath.children ? {\n        children: mainPath.children.map(fillBlack)\n      } : {};\n      const maskInnerGroup = {\n        tag: 'g',\n        attributes: _objectSpread2({}, trans.inner),\n        children: [fillBlack(_objectSpread2({\n          tag: mainPath.tag,\n          attributes: _objectSpread2(_objectSpread2({}, mainPath.attributes), trans.path)\n        }, maskInnerGroupChildrenMixin))]\n      };\n      const maskOuterGroup = {\n        tag: 'g',\n        attributes: _objectSpread2({}, trans.outer),\n        children: [maskInnerGroup]\n      };\n      const maskId = \"mask-\".concat(explicitMaskId || nextUniqueId());\n      const clipId = \"clip-\".concat(explicitMaskId || nextUniqueId());\n      const maskTag = {\n        tag: 'mask',\n        attributes: _objectSpread2(_objectSpread2({}, ALL_SPACE), {}, {\n          id: maskId,\n          maskUnits: 'userSpaceOnUse',\n          maskContentUnits: 'userSpaceOnUse'\n        }),\n        children: [maskRect, maskOuterGroup]\n      };\n      const defs = {\n        tag: 'defs',\n        children: [{\n          tag: 'clipPath',\n          attributes: {\n            id: clipId\n          },\n          children: deGroup(maskPath)\n        }, maskTag]\n      };\n      children.push(defs, {\n        tag: 'rect',\n        attributes: _objectSpread2({\n          fill: 'currentColor',\n          'clip-path': \"url(#\".concat(clipId, \")\"),\n          mask: \"url(#\".concat(maskId, \")\")\n        }, ALL_SPACE)\n      });\n      return {\n        children,\n        attributes\n      };\n    };\n  }\n};\nvar MissingIconIndicator = {\n  provides(providers) {\n    let reduceMotion = false;\n    if (WINDOW.matchMedia) {\n      reduceMotion = WINDOW.matchMedia('(prefers-reduced-motion: reduce)').matches;\n    }\n    providers.missingIconAbstract = function () {\n      const gChildren = [];\n      const FILL = {\n        fill: 'currentColor'\n      };\n      const ANIMATION_BASE = {\n        attributeType: 'XML',\n        repeatCount: 'indefinite',\n        dur: '2s'\n      };\n\n      // Ring\n      gChildren.push({\n        tag: 'path',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          d: 'M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z'\n        })\n      });\n      const OPACITY_ANIMATE = _objectSpread2(_objectSpread2({}, ANIMATION_BASE), {}, {\n        attributeName: 'opacity'\n      });\n      const dot = {\n        tag: 'circle',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          cx: '256',\n          cy: '364',\n          r: '28'\n        }),\n        children: []\n      };\n      if (!reduceMotion) {\n        dot.children.push({\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, ANIMATION_BASE), {}, {\n            attributeName: 'r',\n            values: '28;14;28;28;14;28;'\n          })\n        }, {\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n            values: '1;0;1;1;0;1;'\n          })\n        });\n      }\n      gChildren.push(dot);\n      gChildren.push({\n        tag: 'path',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          opacity: '1',\n          d: 'M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z'\n        }),\n        children: reduceMotion ? [] : [{\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n            values: '1;0;0;0;0;1;'\n          })\n        }]\n      });\n      if (!reduceMotion) {\n        // Exclamation\n        gChildren.push({\n          tag: 'path',\n          attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n            opacity: '0',\n            d: 'M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z'\n          }),\n          children: [{\n            tag: 'animate',\n            attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n              values: '0;0;1;1;0;0;'\n            })\n          }]\n        });\n      }\n      return {\n        tag: 'g',\n        attributes: {\n          'class': 'missing'\n        },\n        children: gChildren\n      };\n    };\n  }\n};\nvar SvgSymbols = {\n  hooks() {\n    return {\n      parseNodeAttributes(accumulator, node) {\n        const symbolData = node.getAttribute('data-fa-symbol');\n        const symbol = symbolData === null ? false : symbolData === '' ? true : symbolData;\n        accumulator['symbol'] = symbol;\n        return accumulator;\n      }\n    };\n  }\n};\nvar plugins = [InjectCSS, ReplaceElements, Layers, LayersCounter, LayersText, PseudoElements, MutationObserver$1, PowerTransforms, Masks, MissingIconIndicator, SvgSymbols];\nregisterPlugins(plugins, {\n  mixoutsTo: api\n});\nconst noAuto$1 = api.noAuto;\nconst config$1 = api.config;\nconst library$1 = api.library;\nconst dom$1 = api.dom;\nconst parse$1 = api.parse;\nconst findIconDefinition$1 = api.findIconDefinition;\nconst toHtml$1 = api.toHtml;\nconst icon = api.icon;\nconst layer = api.layer;\nconst text = api.text;\nconst counter = api.counter;\nexport { noAuto$1 as noAuto, config$1 as config, library$1 as library, dom$1 as dom, parse$1 as parse, findIconDefinition$1 as findIconDefinition, toHtml$1 as toHtml, icon, layer, text, counter, api };", "map": {"version": 3, "names": ["_defineProperty", "e", "r", "t", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "_inherits", "TypeError", "prototype", "create", "constructor", "_setPrototypeOf", "ownKeys", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "push", "apply", "_objectSpread2", "arguments", "length", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "setPrototypeOf", "bind", "__proto__", "_toPrimitive", "Symbol", "toPrimitive", "i", "call", "String", "Number", "_wrapRegExp", "BabelRegExp", "RegExp", "WeakMap", "p", "set", "get", "buildGroups", "reduce", "exec", "groups", "indices", "replace", "Array", "isArray", "join", "slice", "noop", "_WINDOW", "_DOCUMENT", "_MUTATION_OBSERVER", "_PERFORMANCE", "mark", "measure", "window", "document", "MutationObserver", "performance", "userAgent", "navigator", "WINDOW", "DOCUMENT", "MUTATION_OBSERVER", "PERFORMANCE", "IS_BROWSER", "IS_DOM", "documentElement", "head", "addEventListener", "createElement", "IS_IE", "indexOf", "g", "S", "classic", "fa", "fas", "far", "fal", "fat", "fab", "duotone", "fad", "fadr", "fadl", "fadt", "sharp", "fass", "fasr", "fasl", "fast", "fasds", "fasdr", "fasdl", "fasdt", "A", "GROUP", "SWAP_OPACITY", "PRIMARY", "SECONDARY", "P", "s", "L", "G", "normal", "lt", "pt", "Map", "defaultShortPrefixId", "defaultStyleId", "styleIds", "futureStyleIds", "defaultFontWeight", "xt", "solid", "regular", "light", "thin", "brands", "Ft", "St", "kit", "fak", "fakd", "At", "Ct", "Lt", "Wt", "Et", "t$1", "r$1", "bt$1", "Yt", "ua", "I$1", "ga", "x", "Ia", "m$1", "c$1", "F$1", "concat", "ma", "map", "a", "wa", "NAMESPACE_IDENTIFIER", "UNITS_IN_GRID", "DEFAULT_CSS_PREFIX", "DEFAULT_REPLACEMENT_CLASS", "DATA_FA_I2SVG", "DATA_FA_PSEUDO_ELEMENT", "DATA_FA_PSEUDO_ELEMENT_PENDING", "DATA_PREFIX", "DATA_ICON", "HTML_CLASS_I2SVG_BASE_CLASS", "MUTATION_APPROACH_ASYNC", "TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS", "PRODUCTION", "process", "env", "NODE_ENV", "e$$1", "familyProxy", "obj", "Proxy", "target", "prop", "_PREFIX_TO_STYLE", "PREFIX_TO_STYLE", "_STYLE_TO_PREFIX", "STYLE_TO_PREFIX", "_PREFIX_TO_LONG_STYLE", "PREFIX_TO_LONG_STYLE", "_LONG_STYLE_TO_PREFIX", "LONG_STYLE_TO_PREFIX", "ICON_SELECTION_SYNTAX_PATTERN", "LAYERS_TEXT_CLASSNAME", "FONT_FAMILY_PATTERN", "_FONT_WEIGHT_TO_PREFIX", "FONT_WEIGHT_TO_PREFIX", "ATTRIBUTES_WATCHED_FOR_MUTATION", "DUOTONE_CLASSES", "RESERVED_CLASSES", "initial", "FontAwesomeConfig", "getAttrConfig", "attr", "element", "querySelector", "getAttribute", "coerce", "val", "attrs", "_ref", "key", "undefined", "_default", "styleDefault", "<PERSON><PERSON><PERSON><PERSON>", "cssPrefix", "replacementClass", "autoReplaceSvg", "autoAddCss", "autoA11y", "searchPseudoElements", "observeMutations", "mutateApproach", "keepOriginalSource", "measurePerformance", "showMissingIcons", "familyPrefix", "_config", "config", "_onChangeCb", "cb", "onChange", "splice", "d$2", "meaninglessTransform", "size", "y", "rotate", "flipX", "flipY", "insertCss", "css", "style", "setAttribute", "innerHTML", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childNodes", "<PERSON><PERSON><PERSON><PERSON>", "child", "tagName", "toUpperCase", "insertBefore", "idPool", "nextUniqueId", "id", "Math", "random", "toArray", "array", "classArray", "node", "classList", "split", "htmlEscape", "str", "joinAttributes", "attributes", "acc", "attributeName", "trim", "joinStyles", "styles", "styleName", "transformIsMeaningful", "transform", "transformForSvg", "containerWidth", "iconWidth", "outer", "innerTranslate", "innerScale", "innerRotate", "inner", "path", "transformForCss", "_ref2", "width", "height", "startCentered", "baseStyles", "dcp", "drc", "fp", "rc", "dPatt", "customPropPatt", "rPatt", "_cssInserted", "ensureCss", "InjectCSS", "mixout", "dom", "hooks", "beforeDOMElementCreation", "beforeI2svg", "w", "shims", "namespace", "functions", "listener", "removeEventListener", "loaded", "fn", "doScroll", "test", "readyState", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "toHtml", "abstractNodes", "tag", "children", "iconFromMapping", "mapping", "prefix", "iconName", "icon", "bindInternal4", "func", "thisContext", "b", "c", "d", "fastReduceObject", "subject", "initialValue", "iterator", "result", "ucs2decode", "string", "output", "counter", "charCodeAt", "extra", "toHex", "unicode", "decoded", "toString", "codePointAt", "index", "first", "second", "normalizeIcons", "icons", "expanded", "defineIcons", "params", "skip<PERSON><PERSON>s", "normalized", "addPack", "duotonePathRe", "d1", "d2", "cls1", "cls2", "FAMILY_NAMES", "PREFIXES_FOR_FAMILY", "familyId", "_defaultUsablePrefix", "_byUnicode", "_byLigature", "_byOldName", "_byOldUnicode", "_by<PERSON><PERSON><PERSON>", "isReserved", "name", "getIconName", "cls", "parts", "build", "lookup", "reducer", "o$$1", "aliases", "a$$1", "alias", "hasRegular", "autoFetchSvg", "shim<PERSON><PERSON><PERSON>", "shim", "maybeNameMaybeUnicode", "names", "unicodes", "getCanonicalPrefix", "family", "c$$1", "byUnicode", "byLigature", "ligature", "<PERSON><PERSON><PERSON><PERSON>", "byOldName", "byOldUnicode", "oldUnicode", "newUnicode", "getDefaultUsablePrefix", "emptyCanonicalIcon", "rest", "getFamilyId", "values", "famProps", "includes", "some", "v$$1", "styleOrPrefix", "defined", "moveNonFaClassesToRest", "classNames", "sortedUniqueValues", "arr", "sort", "getCanonicalIcon", "skipLookups", "givenPrefix", "faCombinedClasses", "faStyleOrFamilyClasses", "nonStyleOrFamilyClasses", "faStyles", "styleFromValues", "canonical", "getDefaultCanonicalPrefix", "applyShimAndAlias", "aliasIconName", "newCanonicalFamilies", "newCanonicalStyles", "flat", "prefixOptions", "config$$1", "isDuotoneFamily", "valuesHasDuotone", "defaultFamilyIsDuotone", "canonicalPrefixIsDuotone", "validPrefix", "find", "defaultPrefix", "Library", "definitions", "add", "_len", "_key", "additions", "_pullDefinitions", "longPrefix", "reset", "definition", "_plugins", "_hooks", "providers", "defaultProviderKeys", "registerPlugins", "nextPlugins", "mixoutsTo", "k", "plugin", "tk", "sk", "hook", "provides", "chainHooks", "accumulator", "args", "hookFns", "hookFn", "callHooks", "_len2", "_key2", "callProvided", "findIconDefinition", "iconLookup", "library", "noAuto", "i2svg", "Promise", "reject", "Error", "watch", "autoReplaceSvgRoot", "autoReplace", "parse", "match", "canonicalIcon", "api", "dom<PERSON><PERSON><PERSON>", "abstractCreator", "abstract", "container", "html", "asIcon", "main", "mask", "found", "offset", "asSymbol", "symbol", "makeInlineSvgAbstract", "title", "maskId", "titleId", "watchable", "isUploadedIcon", "attrClass", "classes", "content", "role", "uploadedIconWidthStyle", "makeLayersTextAbstract", "styleString", "class", "makeLayersCounterAbstract", "styles$1", "asFoundIcon", "vectorData", "fill", "missingIconResolutionMixin", "maybeNotifyMissing", "console", "error", "findIcon", "resolve", "noop$1", "p$2", "preamble", "begin", "end", "perf", "noop$2", "isWatched", "hasPrefixAndIcon", "hasBeenReplaced", "contains", "getMutator", "mutators", "mutator", "createElementNS", "convertSVG", "abstractObj", "ceFn", "createTextNode", "append<PERSON><PERSON><PERSON>", "nodeAsComment", "comment", "outerHTML", "mutation", "parentNode", "createComment", "<PERSON><PERSON><PERSON><PERSON>", "remove", "nest", "forSvg", "splitClasses", "toSvg", "toNode", "removeAttribute", "newInnerHTML", "performOperationSync", "op", "perform", "mutations", "callback", "callbackFunction", "frame", "requestAnimationFrame", "disabled", "disableObservation", "enableObservation", "mo", "observe", "options", "treeCallback", "nodeCallback", "pseudoElements<PERSON><PERSON><PERSON>", "observeMutationsRoot", "objects", "mutationRecord", "type", "addedNodes", "childList", "characterData", "subtree", "disconnect", "<PERSON><PERSON><PERSON><PERSON>", "class<PERSON><PERSON>er", "existingPrefix", "existingIconName", "innerText", "<PERSON><PERSON><PERSON><PERSON>", "nodeType", "Node", "TEXT_NODE", "data", "<PERSON><PERSON><PERSON><PERSON>", "extraAttributes", "blankMeta", "parseMeta", "parser", "extraClasses", "pluginMeta", "extraStyles", "styles$2", "generateMutation", "nodeMeta", "getKnownPrefixes", "onTree", "root", "htmlClassList", "hclAdd", "suffix", "hclRemove", "prefixes", "prefixesDomQuery", "p$$1", "candidates", "querySelectorAll", "all", "then", "resolvedMutations", "catch", "onNode", "resolveIcons", "next", "maybeIconDefinition", "iconDefinition", "render", "ReplaceElements", "mutationObserverCallbacks", "providers$$1", "generateSvgReplacementMutation", "generateAbstractIcon", "<PERSON><PERSON><PERSON><PERSON>", "Layers", "layer", "assembler", "LayersCounter", "LayersText", "text", "generateLayersText", "computedFontSize", "parseInt", "getComputedStyle", "fontSize", "boundingClientRect", "getBoundingClientRect", "CLEAN_CONTENT_PATTERN", "SECONDARY_UNICODE_RANGE", "_FONT_FAMILY_WEIGHT_TO_PREFIX", "FontAwesome", "FONT_FAMILY_WEIGHT_TO_PREFIX", "toLowerCase", "FONT_FAMILY_WEIGHT_FALLBACK", "fontFamily", "weights", "entries", "hex<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleaned", "codePoint", "isPrependTen", "isDoubled", "isSecondary", "getPrefix", "fontWeight", "fontFamilySanitized", "fontWeightInteger", "fontWeightSanitized", "isNaN", "replaceForPosition", "position", "pendingAttribute", "alreadyProcessedPseudoElement", "getPropertyValue", "fontFamilyMatch", "<PERSON><PERSON><PERSON><PERSON>", "hexValue", "isV4", "startsWith", "iconIdentifier", "iconName4", "meta", "processable", "operations", "P<PERSON>udo<PERSON><PERSON><PERSON>", "pseudoElements2svg", "_unwatched", "MutationObserver$1", "unwatch", "bootstrap", "parseTransformString", "transformString", "n", "parseFloat", "PowerTransforms", "parseNodeAttributes", "generateAbstractTransformGrouping", "ALL_SPACE", "fillBlack", "force", "deGroup", "Masks", "maskData", "generateAbstractMask", "explicitMaskId", "mainWidth", "mainP<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "trans", "maskRect", "maskInnerGroupChildrenMixin", "maskInnerGroup", "maskOuterGroup", "clipId", "maskTag", "maskUnits", "maskContentUnits", "defs", "MissingIconIndicator", "reduceMotion", "matchMedia", "matches", "missingIconAbstract", "g<PERSON><PERSON><PERSON><PERSON>", "FILL", "ANIMATION_BASE", "attributeType", "repeatCount", "dur", "OPACITY_ANIMATE", "dot", "cx", "cy", "opacity", "SvgSymbols", "symbolData", "plugins", "noAuto$1", "config$1", "library$1", "dom$1", "parse$1", "findIconDefinition$1", "toHtml$1"], "sources": ["D:/demo/ooo/pass/node_modules/@fortawesome/fontawesome-svg-core/index.mjs"], "sourcesContent": ["/*!\n * Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)\n * Copyright 2024 Fonticons, Inc.\n */\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && _setPrototypeOf(t, e);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _wrapRegExp() {\n  _wrapRegExp = function (e, r) {\n    return new BabelRegExp(e, void 0, r);\n  };\n  var e = RegExp.prototype,\n    r = new WeakMap();\n  function BabelRegExp(e, t, p) {\n    var o = RegExp(e, t);\n    return r.set(o, p || r.get(e)), _setPrototypeOf(o, BabelRegExp.prototype);\n  }\n  function buildGroups(e, t) {\n    var p = r.get(t);\n    return Object.keys(p).reduce(function (r, t) {\n      var o = p[t];\n      if (\"number\" == typeof o) r[t] = e[o];else {\n        for (var i = 0; void 0 === e[o[i]] && i + 1 < o.length;) i++;\n        r[t] = e[o[i]];\n      }\n      return r;\n    }, Object.create(null));\n  }\n  return _inherits(BabelRegExp, RegExp), BabelRegExp.prototype.exec = function (r) {\n    var t = e.exec.call(this, r);\n    if (t) {\n      t.groups = buildGroups(t, this);\n      var p = t.indices;\n      p && (p.groups = buildGroups(p, this));\n    }\n    return t;\n  }, BabelRegExp.prototype[Symbol.replace] = function (t, p) {\n    if (\"string\" == typeof p) {\n      var o = r.get(this);\n      return e[Symbol.replace].call(this, t, p.replace(/\\$<([^>]+)>/g, function (e, r) {\n        var t = o[r];\n        return \"$\" + (Array.isArray(t) ? t.join(\"$\") : t);\n      }));\n    }\n    if (\"function\" == typeof p) {\n      var i = this;\n      return e[Symbol.replace].call(this, t, function () {\n        var e = arguments;\n        return \"object\" != typeof e[e.length - 1] && (e = [].slice.call(e)).push(buildGroups(e, i)), p.apply(this, e);\n      });\n    }\n    return e[Symbol.replace].call(this, t, p);\n  }, _wrapRegExp.apply(this, arguments);\n}\n\nconst noop = () => {};\nlet _WINDOW = {};\nlet _DOCUMENT = {};\nlet _MUTATION_OBSERVER = null;\nlet _PERFORMANCE = {\n  mark: noop,\n  measure: noop\n};\ntry {\n  if (typeof window !== 'undefined') _WINDOW = window;\n  if (typeof document !== 'undefined') _DOCUMENT = document;\n  if (typeof MutationObserver !== 'undefined') _MUTATION_OBSERVER = MutationObserver;\n  if (typeof performance !== 'undefined') _PERFORMANCE = performance;\n} catch (e) {}\nconst {\n  userAgent = ''\n} = _WINDOW.navigator || {};\nconst WINDOW = _WINDOW;\nconst DOCUMENT = _DOCUMENT;\nconst MUTATION_OBSERVER = _MUTATION_OBSERVER;\nconst PERFORMANCE = _PERFORMANCE;\nconst IS_BROWSER = !!WINDOW.document;\nconst IS_DOM = !!DOCUMENT.documentElement && !!DOCUMENT.head && typeof DOCUMENT.addEventListener === 'function' && typeof DOCUMENT.createElement === 'function';\nconst IS_IE = ~userAgent.indexOf('MSIE') || ~userAgent.indexOf('Trident/');\n\nvar p = /fa(s|r|l|t|d|dr|dl|dt|b|k|kd|ss|sr|sl|st|sds|sdr|sdl|sdt)?[\\-\\ ]/,\n  g = /Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp Duotone|Sharp|Kit)?.*/i;\nvar S = {\n    classic: {\n      fa: \"solid\",\n      fas: \"solid\",\n      \"fa-solid\": \"solid\",\n      far: \"regular\",\n      \"fa-regular\": \"regular\",\n      fal: \"light\",\n      \"fa-light\": \"light\",\n      fat: \"thin\",\n      \"fa-thin\": \"thin\",\n      fab: \"brands\",\n      \"fa-brands\": \"brands\"\n    },\n    duotone: {\n      fa: \"solid\",\n      fad: \"solid\",\n      \"fa-solid\": \"solid\",\n      \"fa-duotone\": \"solid\",\n      fadr: \"regular\",\n      \"fa-regular\": \"regular\",\n      fadl: \"light\",\n      \"fa-light\": \"light\",\n      fadt: \"thin\",\n      \"fa-thin\": \"thin\"\n    },\n    sharp: {\n      fa: \"solid\",\n      fass: \"solid\",\n      \"fa-solid\": \"solid\",\n      fasr: \"regular\",\n      \"fa-regular\": \"regular\",\n      fasl: \"light\",\n      \"fa-light\": \"light\",\n      fast: \"thin\",\n      \"fa-thin\": \"thin\"\n    },\n    \"sharp-duotone\": {\n      fa: \"solid\",\n      fasds: \"solid\",\n      \"fa-solid\": \"solid\",\n      fasdr: \"regular\",\n      \"fa-regular\": \"regular\",\n      fasdl: \"light\",\n      \"fa-light\": \"light\",\n      fasdt: \"thin\",\n      \"fa-thin\": \"thin\"\n    }\n  },\n  A = {\n    GROUP: \"duotone-group\",\n    SWAP_OPACITY: \"swap-opacity\",\n    PRIMARY: \"primary\",\n    SECONDARY: \"secondary\"\n  },\n  P = [\"fa-classic\", \"fa-duotone\", \"fa-sharp\", \"fa-sharp-duotone\"];\nvar s = \"classic\",\n  t = \"duotone\",\n  r = \"sharp\",\n  o = \"sharp-duotone\",\n  L = [s, t, r, o];\nvar G = {\n    classic: {\n      900: \"fas\",\n      400: \"far\",\n      normal: \"far\",\n      300: \"fal\",\n      100: \"fat\"\n    },\n    duotone: {\n      900: \"fad\",\n      400: \"fadr\",\n      300: \"fadl\",\n      100: \"fadt\"\n    },\n    sharp: {\n      900: \"fass\",\n      400: \"fasr\",\n      300: \"fasl\",\n      100: \"fast\"\n    },\n    \"sharp-duotone\": {\n      900: \"fasds\",\n      400: \"fasdr\",\n      300: \"fasdl\",\n      100: \"fasdt\"\n    }\n  };\nvar lt = {\n    \"Font Awesome 6 Free\": {\n      900: \"fas\",\n      400: \"far\"\n    },\n    \"Font Awesome 6 Pro\": {\n      900: \"fas\",\n      400: \"far\",\n      normal: \"far\",\n      300: \"fal\",\n      100: \"fat\"\n    },\n    \"Font Awesome 6 Brands\": {\n      400: \"fab\",\n      normal: \"fab\"\n    },\n    \"Font Awesome 6 Duotone\": {\n      900: \"fad\",\n      400: \"fadr\",\n      normal: \"fadr\",\n      300: \"fadl\",\n      100: \"fadt\"\n    },\n    \"Font Awesome 6 Sharp\": {\n      900: \"fass\",\n      400: \"fasr\",\n      normal: \"fasr\",\n      300: \"fasl\",\n      100: \"fast\"\n    },\n    \"Font Awesome 6 Sharp Duotone\": {\n      900: \"fasds\",\n      400: \"fasdr\",\n      normal: \"fasdr\",\n      300: \"fasdl\",\n      100: \"fasdt\"\n    }\n  };\nvar pt = new Map([[\"classic\", {\n    defaultShortPrefixId: \"fas\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\", \"brands\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"sharp\", {\n    defaultShortPrefixId: \"fass\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"duotone\", {\n    defaultShortPrefixId: \"fad\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }], [\"sharp-duotone\", {\n    defaultShortPrefixId: \"fasds\",\n    defaultStyleId: \"solid\",\n    styleIds: [\"solid\", \"regular\", \"light\", \"thin\"],\n    futureStyleIds: [],\n    defaultFontWeight: 900\n  }]]),\n  xt = {\n    classic: {\n      solid: \"fas\",\n      regular: \"far\",\n      light: \"fal\",\n      thin: \"fat\",\n      brands: \"fab\"\n    },\n    duotone: {\n      solid: \"fad\",\n      regular: \"fadr\",\n      light: \"fadl\",\n      thin: \"fadt\"\n    },\n    sharp: {\n      solid: \"fass\",\n      regular: \"fasr\",\n      light: \"fasl\",\n      thin: \"fast\"\n    },\n    \"sharp-duotone\": {\n      solid: \"fasds\",\n      regular: \"fasdr\",\n      light: \"fasdl\",\n      thin: \"fasdt\"\n    }\n  };\nvar Ft = [\"fak\", \"fa-kit\", \"fakd\", \"fa-kit-duotone\"],\n  St = {\n    kit: {\n      fak: \"kit\",\n      \"fa-kit\": \"kit\"\n    },\n    \"kit-duotone\": {\n      fakd: \"kit-duotone\",\n      \"fa-kit-duotone\": \"kit-duotone\"\n    }\n  },\n  At = [\"kit\"];\nvar Ct = {\n  kit: {\n    \"fa-kit\": \"fak\"\n  },\n  \"kit-duotone\": {\n    \"fa-kit-duotone\": \"fakd\"\n  }\n};\nvar Lt = [\"fak\", \"fakd\"],\n  Wt = {\n    kit: {\n      fak: \"fa-kit\"\n    },\n    \"kit-duotone\": {\n      fakd: \"fa-kit-duotone\"\n    }\n  };\nvar Et = {\n    kit: {\n      kit: \"fak\"\n    },\n    \"kit-duotone\": {\n      \"kit-duotone\": \"fakd\"\n    }\n  };\n\nvar t$1 = {\n    GROUP: \"duotone-group\",\n    SWAP_OPACITY: \"swap-opacity\",\n    PRIMARY: \"primary\",\n    SECONDARY: \"secondary\"\n  },\n  r$1 = [\"fa-classic\", \"fa-duotone\", \"fa-sharp\", \"fa-sharp-duotone\"];\nvar bt$1 = [\"fak\", \"fa-kit\", \"fakd\", \"fa-kit-duotone\"];\nvar Yt = {\n    \"Font Awesome Kit\": {\n      400: \"fak\",\n      normal: \"fak\"\n    },\n    \"Font Awesome Kit Duotone\": {\n      400: \"fakd\",\n      normal: \"fakd\"\n    }\n  };\nvar ua = {\n    classic: {\n      \"fa-brands\": \"fab\",\n      \"fa-duotone\": \"fad\",\n      \"fa-light\": \"fal\",\n      \"fa-regular\": \"far\",\n      \"fa-solid\": \"fas\",\n      \"fa-thin\": \"fat\"\n    },\n    duotone: {\n      \"fa-regular\": \"fadr\",\n      \"fa-light\": \"fadl\",\n      \"fa-thin\": \"fadt\"\n    },\n    sharp: {\n      \"fa-solid\": \"fass\",\n      \"fa-regular\": \"fasr\",\n      \"fa-light\": \"fasl\",\n      \"fa-thin\": \"fast\"\n    },\n    \"sharp-duotone\": {\n      \"fa-solid\": \"fasds\",\n      \"fa-regular\": \"fasdr\",\n      \"fa-light\": \"fasdl\",\n      \"fa-thin\": \"fasdt\"\n    }\n  },\n  I$1 = {\n    classic: [\"fas\", \"far\", \"fal\", \"fat\", \"fad\"],\n    duotone: [\"fadr\", \"fadl\", \"fadt\"],\n    sharp: [\"fass\", \"fasr\", \"fasl\", \"fast\"],\n    \"sharp-duotone\": [\"fasds\", \"fasdr\", \"fasdl\", \"fasdt\"]\n  },\n  ga = {\n    classic: {\n      fab: \"fa-brands\",\n      fad: \"fa-duotone\",\n      fal: \"fa-light\",\n      far: \"fa-regular\",\n      fas: \"fa-solid\",\n      fat: \"fa-thin\"\n    },\n    duotone: {\n      fadr: \"fa-regular\",\n      fadl: \"fa-light\",\n      fadt: \"fa-thin\"\n    },\n    sharp: {\n      fass: \"fa-solid\",\n      fasr: \"fa-regular\",\n      fasl: \"fa-light\",\n      fast: \"fa-thin\"\n    },\n    \"sharp-duotone\": {\n      fasds: \"fa-solid\",\n      fasdr: \"fa-regular\",\n      fasdl: \"fa-light\",\n      fasdt: \"fa-thin\"\n    }\n  },\n  x = [\"fa-solid\", \"fa-regular\", \"fa-light\", \"fa-thin\", \"fa-duotone\", \"fa-brands\"],\n  Ia = [\"fa\", \"fas\", \"far\", \"fal\", \"fat\", \"fad\", \"fadr\", \"fadl\", \"fadt\", \"fab\", \"fass\", \"fasr\", \"fasl\", \"fast\", \"fasds\", \"fasdr\", \"fasdl\", \"fasdt\", ...r$1, ...x],\n  m$1 = [\"solid\", \"regular\", \"light\", \"thin\", \"duotone\", \"brands\"],\n  c$1 = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],\n  F$1 = c$1.concat([11, 12, 13, 14, 15, 16, 17, 18, 19, 20]),\n  ma = [...Object.keys(I$1), ...m$1, \"2xs\", \"xs\", \"sm\", \"lg\", \"xl\", \"2xl\", \"beat\", \"border\", \"fade\", \"beat-fade\", \"bounce\", \"flip-both\", \"flip-horizontal\", \"flip-vertical\", \"flip\", \"fw\", \"inverse\", \"layers-counter\", \"layers-text\", \"layers\", \"li\", \"pull-left\", \"pull-right\", \"pulse\", \"rotate-180\", \"rotate-270\", \"rotate-90\", \"rotate-by\", \"shake\", \"spin-pulse\", \"spin-reverse\", \"spin\", \"stack-1x\", \"stack-2x\", \"stack\", \"ul\", t$1.GROUP, t$1.SWAP_OPACITY, t$1.PRIMARY, t$1.SECONDARY].concat(c$1.map(a => \"\".concat(a, \"x\"))).concat(F$1.map(a => \"w-\".concat(a)));\nvar wa = {\n    \"Font Awesome 5 Free\": {\n      900: \"fas\",\n      400: \"far\"\n    },\n    \"Font Awesome 5 Pro\": {\n      900: \"fas\",\n      400: \"far\",\n      normal: \"far\",\n      300: \"fal\"\n    },\n    \"Font Awesome 5 Brands\": {\n      400: \"fab\",\n      normal: \"fab\"\n    },\n    \"Font Awesome 5 Duotone\": {\n      900: \"fad\"\n    }\n  };\n\nconst NAMESPACE_IDENTIFIER = '___FONT_AWESOME___';\nconst UNITS_IN_GRID = 16;\nconst DEFAULT_CSS_PREFIX = 'fa';\nconst DEFAULT_REPLACEMENT_CLASS = 'svg-inline--fa';\nconst DATA_FA_I2SVG = 'data-fa-i2svg';\nconst DATA_FA_PSEUDO_ELEMENT = 'data-fa-pseudo-element';\nconst DATA_FA_PSEUDO_ELEMENT_PENDING = 'data-fa-pseudo-element-pending';\nconst DATA_PREFIX = 'data-prefix';\nconst DATA_ICON = 'data-icon';\nconst HTML_CLASS_I2SVG_BASE_CLASS = 'fontawesome-i2svg';\nconst MUTATION_APPROACH_ASYNC = 'async';\nconst TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS = ['HTML', 'HEAD', 'STYLE', 'SCRIPT'];\nconst PRODUCTION = (() => {\n  try {\n    return process.env.NODE_ENV === 'production';\n  } catch (e$$1) {\n    return false;\n  }\n})();\nfunction familyProxy(obj) {\n  // Defaults to the classic family if family is not available\n  return new Proxy(obj, {\n    get(target, prop) {\n      return prop in target ? target[prop] : target[s];\n    }\n  });\n}\nconst _PREFIX_TO_STYLE = _objectSpread2({}, S);\n\n// We changed FACSSClassesToStyleId in the icons repo to be canonical and as such, \"classic\" family does not have any\n// duotone styles.  But we do still need duotone in _PREFIX_TO_STYLE below, so we are manually adding\n// {'fa-duotone': 'duotone'}\n_PREFIX_TO_STYLE[s] = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\n  'fa-duotone': 'duotone'\n}), S[s]), St['kit']), St['kit-duotone']);\nconst PREFIX_TO_STYLE = familyProxy(_PREFIX_TO_STYLE);\nconst _STYLE_TO_PREFIX = _objectSpread2({}, xt);\n\n// We changed FAStyleIdToShortPrefixId in the icons repo to be canonical and as such, \"classic\" family does not have any\n// duotone styles.  But we do still need duotone in _STYLE_TO_PREFIX below, so we are manually adding {duotone: 'fad'}\n_STYLE_TO_PREFIX[s] = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\n  duotone: 'fad'\n}), _STYLE_TO_PREFIX[s]), Et['kit']), Et['kit-duotone']);\nconst STYLE_TO_PREFIX = familyProxy(_STYLE_TO_PREFIX);\nconst _PREFIX_TO_LONG_STYLE = _objectSpread2({}, ga);\n_PREFIX_TO_LONG_STYLE[s] = _objectSpread2(_objectSpread2({}, _PREFIX_TO_LONG_STYLE[s]), Wt['kit']);\nconst PREFIX_TO_LONG_STYLE = familyProxy(_PREFIX_TO_LONG_STYLE);\nconst _LONG_STYLE_TO_PREFIX = _objectSpread2({}, ua);\n_LONG_STYLE_TO_PREFIX[s] = _objectSpread2(_objectSpread2({}, _LONG_STYLE_TO_PREFIX[s]), Ct['kit']);\nconst LONG_STYLE_TO_PREFIX = familyProxy(_LONG_STYLE_TO_PREFIX);\nconst ICON_SELECTION_SYNTAX_PATTERN = p; // eslint-disable-line no-useless-escape\n\nconst LAYERS_TEXT_CLASSNAME = 'fa-layers-text';\nconst FONT_FAMILY_PATTERN = g;\nconst _FONT_WEIGHT_TO_PREFIX = _objectSpread2({}, G);\nconst FONT_WEIGHT_TO_PREFIX = familyProxy(_FONT_WEIGHT_TO_PREFIX);\nconst ATTRIBUTES_WATCHED_FOR_MUTATION = ['class', 'data-prefix', 'data-icon', 'data-fa-transform', 'data-fa-mask'];\nconst DUOTONE_CLASSES = A;\nconst RESERVED_CLASSES = [...At, ...ma];\n\nconst initial = WINDOW.FontAwesomeConfig || {};\nfunction getAttrConfig(attr) {\n  var element = DOCUMENT.querySelector('script[' + attr + ']');\n  if (element) {\n    return element.getAttribute(attr);\n  }\n}\nfunction coerce(val) {\n  // Getting an empty string will occur if the attribute is set on the HTML tag but without a value\n  // We'll assume that this is an indication that it should be toggled to true\n  if (val === '') return true;\n  if (val === 'false') return false;\n  if (val === 'true') return true;\n  return val;\n}\nif (DOCUMENT && typeof DOCUMENT.querySelector === 'function') {\n  const attrs = [['data-family-prefix', 'familyPrefix'], ['data-css-prefix', 'cssPrefix'], ['data-family-default', 'familyDefault'], ['data-style-default', 'styleDefault'], ['data-replacement-class', 'replacementClass'], ['data-auto-replace-svg', 'autoReplaceSvg'], ['data-auto-add-css', 'autoAddCss'], ['data-auto-a11y', 'autoA11y'], ['data-search-pseudo-elements', 'searchPseudoElements'], ['data-observe-mutations', 'observeMutations'], ['data-mutate-approach', 'mutateApproach'], ['data-keep-original-source', 'keepOriginalSource'], ['data-measure-performance', 'measurePerformance'], ['data-show-missing-icons', 'showMissingIcons']];\n  attrs.forEach(_ref => {\n    let [attr, key] = _ref;\n    const val = coerce(getAttrConfig(attr));\n    if (val !== undefined && val !== null) {\n      initial[key] = val;\n    }\n  });\n}\nconst _default = {\n  styleDefault: 'solid',\n  familyDefault: s,\n  cssPrefix: DEFAULT_CSS_PREFIX,\n  replacementClass: DEFAULT_REPLACEMENT_CLASS,\n  autoReplaceSvg: true,\n  autoAddCss: true,\n  autoA11y: true,\n  searchPseudoElements: false,\n  observeMutations: true,\n  mutateApproach: 'async',\n  keepOriginalSource: true,\n  measurePerformance: false,\n  showMissingIcons: true\n};\n\n// familyPrefix is deprecated but we must still support it if present\nif (initial.familyPrefix) {\n  initial.cssPrefix = initial.familyPrefix;\n}\nconst _config = _objectSpread2(_objectSpread2({}, _default), initial);\nif (!_config.autoReplaceSvg) _config.observeMutations = false;\nconst config = {};\nObject.keys(_default).forEach(key => {\n  Object.defineProperty(config, key, {\n    enumerable: true,\n    set: function (val) {\n      _config[key] = val;\n      _onChangeCb.forEach(cb => cb(config));\n    },\n    get: function () {\n      return _config[key];\n    }\n  });\n});\n\n// familyPrefix is deprecated as of 6.2.0 and should be removed in 7.0.0\nObject.defineProperty(config, 'familyPrefix', {\n  enumerable: true,\n  set: function (val) {\n    _config.cssPrefix = val;\n    _onChangeCb.forEach(cb => cb(config));\n  },\n  get: function () {\n    return _config.cssPrefix;\n  }\n});\nWINDOW.FontAwesomeConfig = config;\nconst _onChangeCb = [];\nfunction onChange(cb) {\n  _onChangeCb.push(cb);\n  return () => {\n    _onChangeCb.splice(_onChangeCb.indexOf(cb), 1);\n  };\n}\n\nconst d$2 = UNITS_IN_GRID;\nconst meaninglessTransform = {\n  size: 16,\n  x: 0,\n  y: 0,\n  rotate: 0,\n  flipX: false,\n  flipY: false\n};\nfunction insertCss(css) {\n  if (!css || !IS_DOM) {\n    return;\n  }\n  const style = DOCUMENT.createElement('style');\n  style.setAttribute('type', 'text/css');\n  style.innerHTML = css;\n  const headChildren = DOCUMENT.head.childNodes;\n  let beforeChild = null;\n  for (let i = headChildren.length - 1; i > -1; i--) {\n    const child = headChildren[i];\n    const tagName = (child.tagName || '').toUpperCase();\n    if (['STYLE', 'LINK'].indexOf(tagName) > -1) {\n      beforeChild = child;\n    }\n  }\n  DOCUMENT.head.insertBefore(style, beforeChild);\n  return css;\n}\nconst idPool = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\nfunction nextUniqueId() {\n  let size = 12;\n  let id = '';\n  while (size-- > 0) {\n    id += idPool[Math.random() * 62 | 0];\n  }\n  return id;\n}\nfunction toArray(obj) {\n  const array = [];\n  for (let i = (obj || []).length >>> 0; i--;) {\n    array[i] = obj[i];\n  }\n  return array;\n}\nfunction classArray(node) {\n  if (node.classList) {\n    return toArray(node.classList);\n  } else {\n    return (node.getAttribute('class') || '').split(' ').filter(i => i);\n  }\n}\nfunction htmlEscape(str) {\n  return \"\".concat(str).replace(/&/g, '&amp;').replace(/\"/g, '&quot;').replace(/'/g, '&#39;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\n}\nfunction joinAttributes(attributes) {\n  return Object.keys(attributes || {}).reduce((acc, attributeName) => {\n    return acc + \"\".concat(attributeName, \"=\\\"\").concat(htmlEscape(attributes[attributeName]), \"\\\" \");\n  }, '').trim();\n}\nfunction joinStyles(styles) {\n  return Object.keys(styles || {}).reduce((acc, styleName) => {\n    return acc + \"\".concat(styleName, \": \").concat(styles[styleName].trim(), \";\");\n  }, '');\n}\nfunction transformIsMeaningful(transform) {\n  return transform.size !== meaninglessTransform.size || transform.x !== meaninglessTransform.x || transform.y !== meaninglessTransform.y || transform.rotate !== meaninglessTransform.rotate || transform.flipX || transform.flipY;\n}\nfunction transformForSvg(_ref) {\n  let {\n    transform,\n    containerWidth,\n    iconWidth\n  } = _ref;\n  const outer = {\n    transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\n  };\n  const innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\n  const innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\n  const innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\n  const inner = {\n    transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\n  };\n  const path = {\n    transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\n  };\n  return {\n    outer,\n    inner,\n    path\n  };\n}\nfunction transformForCss(_ref2) {\n  let {\n    transform,\n    width = UNITS_IN_GRID,\n    height = UNITS_IN_GRID,\n    startCentered = false\n  } = _ref2;\n  let val = '';\n  if (startCentered && IS_IE) {\n    val += \"translate(\".concat(transform.x / d$2 - width / 2, \"em, \").concat(transform.y / d$2 - height / 2, \"em) \");\n  } else if (startCentered) {\n    val += \"translate(calc(-50% + \".concat(transform.x / d$2, \"em), calc(-50% + \").concat(transform.y / d$2, \"em)) \");\n  } else {\n    val += \"translate(\".concat(transform.x / d$2, \"em, \").concat(transform.y / d$2, \"em) \");\n  }\n  val += \"scale(\".concat(transform.size / d$2 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / d$2 * (transform.flipY ? -1 : 1), \") \");\n  val += \"rotate(\".concat(transform.rotate, \"deg) \");\n  return val;\n}\n\nvar baseStyles = \":root, :host {\\n  --fa-font-solid: normal 900 1em/1 \\\"Font Awesome 6 Free\\\";\\n  --fa-font-regular: normal 400 1em/1 \\\"Font Awesome 6 Free\\\";\\n  --fa-font-light: normal 300 1em/1 \\\"Font Awesome 6 Pro\\\";\\n  --fa-font-thin: normal 100 1em/1 \\\"Font Awesome 6 Pro\\\";\\n  --fa-font-duotone: normal 900 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-duotone-regular: normal 400 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-duotone-light: normal 300 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-duotone-thin: normal 100 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-brands: normal 400 1em/1 \\\"Font Awesome 6 Brands\\\";\\n  --fa-font-sharp-solid: normal 900 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-regular: normal 400 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-light: normal 300 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-thin: normal 100 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-duotone-solid: normal 900 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-regular: normal 400 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-light: normal 300 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n  --fa-font-sharp-duotone-thin: normal 100 1em/1 \\\"Font Awesome 6 Sharp Duotone\\\";\\n}\\n\\nsvg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {\\n  overflow: visible;\\n  box-sizing: content-box;\\n}\\n\\n.svg-inline--fa {\\n  display: var(--fa-display, inline-block);\\n  height: 1em;\\n  overflow: visible;\\n  vertical-align: -0.125em;\\n}\\n.svg-inline--fa.fa-2xs {\\n  vertical-align: 0.1em;\\n}\\n.svg-inline--fa.fa-xs {\\n  vertical-align: 0em;\\n}\\n.svg-inline--fa.fa-sm {\\n  vertical-align: -0.0714285705em;\\n}\\n.svg-inline--fa.fa-lg {\\n  vertical-align: -0.2em;\\n}\\n.svg-inline--fa.fa-xl {\\n  vertical-align: -0.25em;\\n}\\n.svg-inline--fa.fa-2xl {\\n  vertical-align: -0.3125em;\\n}\\n.svg-inline--fa.fa-pull-left {\\n  margin-right: var(--fa-pull-margin, 0.3em);\\n  width: auto;\\n}\\n.svg-inline--fa.fa-pull-right {\\n  margin-left: var(--fa-pull-margin, 0.3em);\\n  width: auto;\\n}\\n.svg-inline--fa.fa-li {\\n  width: var(--fa-li-width, 2em);\\n  top: 0.25em;\\n}\\n.svg-inline--fa.fa-fw {\\n  width: var(--fa-fw-width, 1.25em);\\n}\\n\\n.fa-layers svg.svg-inline--fa {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n}\\n\\n.fa-layers-counter, .fa-layers-text {\\n  display: inline-block;\\n  position: absolute;\\n  text-align: center;\\n}\\n\\n.fa-layers {\\n  display: inline-block;\\n  height: 1em;\\n  position: relative;\\n  text-align: center;\\n  vertical-align: -0.125em;\\n  width: 1em;\\n}\\n.fa-layers svg.svg-inline--fa {\\n  transform-origin: center center;\\n}\\n\\n.fa-layers-text {\\n  left: 50%;\\n  top: 50%;\\n  transform: translate(-50%, -50%);\\n  transform-origin: center center;\\n}\\n\\n.fa-layers-counter {\\n  background-color: var(--fa-counter-background-color, #ff253a);\\n  border-radius: var(--fa-counter-border-radius, 1em);\\n  box-sizing: border-box;\\n  color: var(--fa-inverse, #fff);\\n  line-height: var(--fa-counter-line-height, 1);\\n  max-width: var(--fa-counter-max-width, 5em);\\n  min-width: var(--fa-counter-min-width, 1.5em);\\n  overflow: hidden;\\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\\n  right: var(--fa-right, 0);\\n  text-overflow: ellipsis;\\n  top: var(--fa-top, 0);\\n  transform: scale(var(--fa-counter-scale, 0.25));\\n  transform-origin: top right;\\n}\\n\\n.fa-layers-bottom-right {\\n  bottom: var(--fa-bottom, 0);\\n  right: var(--fa-right, 0);\\n  top: auto;\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: bottom right;\\n}\\n\\n.fa-layers-bottom-left {\\n  bottom: var(--fa-bottom, 0);\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: auto;\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: bottom left;\\n}\\n\\n.fa-layers-top-right {\\n  top: var(--fa-top, 0);\\n  right: var(--fa-right, 0);\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: top right;\\n}\\n\\n.fa-layers-top-left {\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: var(--fa-top, 0);\\n  transform: scale(var(--fa-layers-scale, 0.25));\\n  transform-origin: top left;\\n}\\n\\n.fa-1x {\\n  font-size: 1em;\\n}\\n\\n.fa-2x {\\n  font-size: 2em;\\n}\\n\\n.fa-3x {\\n  font-size: 3em;\\n}\\n\\n.fa-4x {\\n  font-size: 4em;\\n}\\n\\n.fa-5x {\\n  font-size: 5em;\\n}\\n\\n.fa-6x {\\n  font-size: 6em;\\n}\\n\\n.fa-7x {\\n  font-size: 7em;\\n}\\n\\n.fa-8x {\\n  font-size: 8em;\\n}\\n\\n.fa-9x {\\n  font-size: 9em;\\n}\\n\\n.fa-10x {\\n  font-size: 10em;\\n}\\n\\n.fa-2xs {\\n  font-size: 0.625em;\\n  line-height: 0.1em;\\n  vertical-align: 0.225em;\\n}\\n\\n.fa-xs {\\n  font-size: 0.75em;\\n  line-height: 0.0833333337em;\\n  vertical-align: 0.125em;\\n}\\n\\n.fa-sm {\\n  font-size: 0.875em;\\n  line-height: 0.0714285718em;\\n  vertical-align: 0.0535714295em;\\n}\\n\\n.fa-lg {\\n  font-size: 1.25em;\\n  line-height: 0.05em;\\n  vertical-align: -0.075em;\\n}\\n\\n.fa-xl {\\n  font-size: 1.5em;\\n  line-height: 0.0416666682em;\\n  vertical-align: -0.125em;\\n}\\n\\n.fa-2xl {\\n  font-size: 2em;\\n  line-height: 0.03125em;\\n  vertical-align: -0.1875em;\\n}\\n\\n.fa-fw {\\n  text-align: center;\\n  width: 1.25em;\\n}\\n\\n.fa-ul {\\n  list-style-type: none;\\n  margin-left: var(--fa-li-margin, 2.5em);\\n  padding-left: 0;\\n}\\n.fa-ul > li {\\n  position: relative;\\n}\\n\\n.fa-li {\\n  left: calc(-1 * var(--fa-li-width, 2em));\\n  position: absolute;\\n  text-align: center;\\n  width: var(--fa-li-width, 2em);\\n  line-height: inherit;\\n}\\n\\n.fa-border {\\n  border-color: var(--fa-border-color, #eee);\\n  border-radius: var(--fa-border-radius, 0.1em);\\n  border-style: var(--fa-border-style, solid);\\n  border-width: var(--fa-border-width, 0.08em);\\n  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);\\n}\\n\\n.fa-pull-left {\\n  float: left;\\n  margin-right: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-pull-right {\\n  float: right;\\n  margin-left: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-beat {\\n  animation-name: fa-beat;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-bounce {\\n  animation-name: fa-bounce;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\\n}\\n\\n.fa-fade {\\n  animation-name: fa-fade;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-beat-fade {\\n  animation-name: fa-beat-fade;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-flip {\\n  animation-name: fa-flip;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-shake {\\n  animation-name: fa-shake;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin {\\n  animation-name: fa-spin;\\n  animation-delay: var(--fa-animation-delay, 0s);\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 2s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin-reverse {\\n  --fa-animation-direction: reverse;\\n}\\n\\n.fa-pulse,\\n.fa-spin-pulse {\\n  animation-name: fa-spin;\\n  animation-direction: var(--fa-animation-direction, normal);\\n  animation-duration: var(--fa-animation-duration, 1s);\\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  animation-timing-function: var(--fa-animation-timing, steps(8));\\n}\\n\\n@media (prefers-reduced-motion: reduce) {\\n  .fa-beat,\\n.fa-bounce,\\n.fa-fade,\\n.fa-beat-fade,\\n.fa-flip,\\n.fa-pulse,\\n.fa-shake,\\n.fa-spin,\\n.fa-spin-pulse {\\n    animation-delay: -1ms;\\n    animation-duration: 1ms;\\n    animation-iteration-count: 1;\\n    transition-delay: 0s;\\n    transition-duration: 0s;\\n  }\\n}\\n@keyframes fa-beat {\\n  0%, 90% {\\n    transform: scale(1);\\n  }\\n  45% {\\n    transform: scale(var(--fa-beat-scale, 1.25));\\n  }\\n}\\n@keyframes fa-bounce {\\n  0% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n  10% {\\n    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\\n  }\\n  30% {\\n    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\\n  }\\n  50% {\\n    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\\n  }\\n  57% {\\n    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\\n  }\\n  64% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n  100% {\\n    transform: scale(1, 1) translateY(0);\\n  }\\n}\\n@keyframes fa-fade {\\n  50% {\\n    opacity: var(--fa-fade-opacity, 0.4);\\n  }\\n}\\n@keyframes fa-beat-fade {\\n  0%, 100% {\\n    opacity: var(--fa-beat-fade-opacity, 0.4);\\n    transform: scale(1);\\n  }\\n  50% {\\n    opacity: 1;\\n    transform: scale(var(--fa-beat-fade-scale, 1.125));\\n  }\\n}\\n@keyframes fa-flip {\\n  50% {\\n    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\\n  }\\n}\\n@keyframes fa-shake {\\n  0% {\\n    transform: rotate(-15deg);\\n  }\\n  4% {\\n    transform: rotate(15deg);\\n  }\\n  8%, 24% {\\n    transform: rotate(-18deg);\\n  }\\n  12%, 28% {\\n    transform: rotate(18deg);\\n  }\\n  16% {\\n    transform: rotate(-22deg);\\n  }\\n  20% {\\n    transform: rotate(22deg);\\n  }\\n  32% {\\n    transform: rotate(-12deg);\\n  }\\n  36% {\\n    transform: rotate(12deg);\\n  }\\n  40%, 100% {\\n    transform: rotate(0deg);\\n  }\\n}\\n@keyframes fa-spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.fa-rotate-90 {\\n  transform: rotate(90deg);\\n}\\n\\n.fa-rotate-180 {\\n  transform: rotate(180deg);\\n}\\n\\n.fa-rotate-270 {\\n  transform: rotate(270deg);\\n}\\n\\n.fa-flip-horizontal {\\n  transform: scale(-1, 1);\\n}\\n\\n.fa-flip-vertical {\\n  transform: scale(1, -1);\\n}\\n\\n.fa-flip-both,\\n.fa-flip-horizontal.fa-flip-vertical {\\n  transform: scale(-1, -1);\\n}\\n\\n.fa-rotate-by {\\n  transform: rotate(var(--fa-rotate-angle, 0));\\n}\\n\\n.fa-stack {\\n  display: inline-block;\\n  vertical-align: middle;\\n  height: 2em;\\n  position: relative;\\n  width: 2.5em;\\n}\\n\\n.fa-stack-1x,\\n.fa-stack-2x {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n  z-index: var(--fa-stack-z-index, auto);\\n}\\n\\n.svg-inline--fa.fa-stack-1x {\\n  height: 1em;\\n  width: 1.25em;\\n}\\n.svg-inline--fa.fa-stack-2x {\\n  height: 2em;\\n  width: 2.5em;\\n}\\n\\n.fa-inverse {\\n  color: var(--fa-inverse, #fff);\\n}\\n\\n.sr-only,\\n.fa-sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n\\n.sr-only-focusable:not(:focus),\\n.fa-sr-only-focusable:not(:focus) {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n\\n.svg-inline--fa .fa-primary {\\n  fill: var(--fa-primary-color, currentColor);\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa .fa-secondary {\\n  fill: var(--fa-secondary-color, currentColor);\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-primary {\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa mask .fa-primary,\\n.svg-inline--fa mask .fa-secondary {\\n  fill: black;\\n}\";\n\nfunction css() {\n  const dcp = DEFAULT_CSS_PREFIX;\n  const drc = DEFAULT_REPLACEMENT_CLASS;\n  const fp = config.cssPrefix;\n  const rc = config.replacementClass;\n  let s = baseStyles;\n  if (fp !== dcp || rc !== drc) {\n    const dPatt = new RegExp(\"\\\\.\".concat(dcp, \"\\\\-\"), 'g');\n    const customPropPatt = new RegExp(\"\\\\--\".concat(dcp, \"\\\\-\"), 'g');\n    const rPatt = new RegExp(\"\\\\.\".concat(drc), 'g');\n    s = s.replace(dPatt, \".\".concat(fp, \"-\")).replace(customPropPatt, \"--\".concat(fp, \"-\")).replace(rPatt, \".\".concat(rc));\n  }\n  return s;\n}\nlet _cssInserted = false;\nfunction ensureCss() {\n  if (config.autoAddCss && !_cssInserted) {\n    insertCss(css());\n    _cssInserted = true;\n  }\n}\nvar InjectCSS = {\n  mixout() {\n    return {\n      dom: {\n        css,\n        insertCss: ensureCss\n      }\n    };\n  },\n  hooks() {\n    return {\n      beforeDOMElementCreation() {\n        ensureCss();\n      },\n      beforeI2svg() {\n        ensureCss();\n      }\n    };\n  }\n};\n\nconst w = WINDOW || {};\nif (!w[NAMESPACE_IDENTIFIER]) w[NAMESPACE_IDENTIFIER] = {};\nif (!w[NAMESPACE_IDENTIFIER].styles) w[NAMESPACE_IDENTIFIER].styles = {};\nif (!w[NAMESPACE_IDENTIFIER].hooks) w[NAMESPACE_IDENTIFIER].hooks = {};\nif (!w[NAMESPACE_IDENTIFIER].shims) w[NAMESPACE_IDENTIFIER].shims = [];\nvar namespace = w[NAMESPACE_IDENTIFIER];\n\nconst functions = [];\nconst listener = function () {\n  DOCUMENT.removeEventListener('DOMContentLoaded', listener);\n  loaded = 1;\n  functions.map(fn => fn());\n};\nlet loaded = false;\nif (IS_DOM) {\n  loaded = (DOCUMENT.documentElement.doScroll ? /^loaded|^c/ : /^loaded|^i|^c/).test(DOCUMENT.readyState);\n  if (!loaded) DOCUMENT.addEventListener('DOMContentLoaded', listener);\n}\nfunction domready (fn) {\n  if (!IS_DOM) return;\n  loaded ? setTimeout(fn, 0) : functions.push(fn);\n}\n\nfunction toHtml(abstractNodes) {\n  const {\n    tag,\n    attributes = {},\n    children = []\n  } = abstractNodes;\n  if (typeof abstractNodes === 'string') {\n    return htmlEscape(abstractNodes);\n  } else {\n    return \"<\".concat(tag, \" \").concat(joinAttributes(attributes), \">\").concat(children.map(toHtml).join(''), \"</\").concat(tag, \">\");\n  }\n}\n\nfunction iconFromMapping(mapping, prefix, iconName) {\n  if (mapping && mapping[prefix] && mapping[prefix][iconName]) {\n    return {\n      prefix,\n      iconName,\n      icon: mapping[prefix][iconName]\n    };\n  }\n}\n\n/**\n * Internal helper to bind a function known to have 4 arguments\n * to a given context.\n */\nvar bindInternal4 = function bindInternal4(func, thisContext) {\n  return function (a, b, c, d) {\n    return func.call(thisContext, a, b, c, d);\n  };\n};\n\n/**\n * # Reduce\n *\n * A fast object `.reduce()` implementation.\n *\n * @param  {Object}   subject      The object to reduce over.\n * @param  {Function} fn           The reducer function.\n * @param  {mixed}    initialValue The initial value for the reducer, defaults to subject[0].\n * @param  {Object}   thisContext  The context for the reducer.\n * @return {mixed}                 The final result.\n */\nvar reduce = function fastReduceObject(subject, fn, initialValue, thisContext) {\n  var keys = Object.keys(subject),\n    length = keys.length,\n    iterator = thisContext !== undefined ? bindInternal4(fn, thisContext) : fn,\n    i,\n    key,\n    result;\n  if (initialValue === undefined) {\n    i = 1;\n    result = subject[keys[0]];\n  } else {\n    i = 0;\n    result = initialValue;\n  }\n  for (; i < length; i++) {\n    key = keys[i];\n    result = iterator(result, subject[key], key, subject);\n  }\n  return result;\n};\n\n/**\n * ucs2decode() and codePointAt() are both works of Mathias Bynens and licensed under MIT\n *\n * Copyright Mathias Bynens <https://mathiasbynens.be/>\n\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.\n\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\n * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\n * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\n * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\nfunction ucs2decode(string) {\n  const output = [];\n  let counter = 0;\n  const length = string.length;\n  while (counter < length) {\n    const value = string.charCodeAt(counter++);\n    if (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n      const extra = string.charCodeAt(counter++);\n      if ((extra & 0xFC00) == 0xDC00) {\n        // eslint-disable-line eqeqeq\n        output.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n      } else {\n        output.push(value);\n        counter--;\n      }\n    } else {\n      output.push(value);\n    }\n  }\n  return output;\n}\nfunction toHex(unicode) {\n  const decoded = ucs2decode(unicode);\n  return decoded.length === 1 ? decoded[0].toString(16) : null;\n}\nfunction codePointAt(string, index) {\n  const size = string.length;\n  let first = string.charCodeAt(index);\n  let second;\n  if (first >= 0xD800 && first <= 0xDBFF && size > index + 1) {\n    second = string.charCodeAt(index + 1);\n    if (second >= 0xDC00 && second <= 0xDFFF) {\n      return (first - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n    }\n  }\n  return first;\n}\n\nfunction normalizeIcons(icons) {\n  return Object.keys(icons).reduce((acc, iconName) => {\n    const icon = icons[iconName];\n    const expanded = !!icon.icon;\n    if (expanded) {\n      acc[icon.iconName] = icon.icon;\n    } else {\n      acc[iconName] = icon;\n    }\n    return acc;\n  }, {});\n}\nfunction defineIcons(prefix, icons) {\n  let params = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  const {\n    skipHooks = false\n  } = params;\n  const normalized = normalizeIcons(icons);\n  if (typeof namespace.hooks.addPack === 'function' && !skipHooks) {\n    namespace.hooks.addPack(prefix, normalizeIcons(icons));\n  } else {\n    namespace.styles[prefix] = _objectSpread2(_objectSpread2({}, namespace.styles[prefix] || {}), normalized);\n  }\n\n  /**\n   * Font Awesome 4 used the prefix of `fa` for all icons. With the introduction\n   * of new styles we needed to differentiate between them. Prefix `fa` is now an alias\n   * for `fas` so we'll ease the upgrade process for our users by automatically defining\n   * this as well.\n   */\n  if (prefix === 'fas') {\n    defineIcons('fa', icons);\n  }\n}\n\nconst duotonePathRe = [/*#__PURE__*/_wrapRegExp(/path d=\"([^\"]+)\".*path d=\"([^\"]+)\"/, {\n  d1: 1,\n  d2: 2\n}), /*#__PURE__*/_wrapRegExp(/path class=\"([^\"]+)\".*d=\"([^\"]+)\".*path class=\"([^\"]+)\".*d=\"([^\"]+)\"/, {\n  cls1: 1,\n  d1: 2,\n  cls2: 3,\n  d2: 4\n}), /*#__PURE__*/_wrapRegExp(/path class=\"([^\"]+)\".*d=\"([^\"]+)\"/, {\n  cls1: 1,\n  d1: 2\n})];\n\nconst {\n  styles,\n  shims\n} = namespace;\nconst FAMILY_NAMES = Object.keys(PREFIX_TO_LONG_STYLE);\nconst PREFIXES_FOR_FAMILY = FAMILY_NAMES.reduce((acc, familyId) => {\n  acc[familyId] = Object.keys(PREFIX_TO_LONG_STYLE[familyId]);\n  return acc;\n}, {});\nlet _defaultUsablePrefix = null;\nlet _byUnicode = {};\nlet _byLigature = {};\nlet _byOldName = {};\nlet _byOldUnicode = {};\nlet _byAlias = {};\nfunction isReserved(name) {\n  return ~RESERVED_CLASSES.indexOf(name);\n}\nfunction getIconName(cssPrefix, cls) {\n  const parts = cls.split('-');\n  const prefix = parts[0];\n  const iconName = parts.slice(1).join('-');\n  if (prefix === cssPrefix && iconName !== '' && !isReserved(iconName)) {\n    return iconName;\n  } else {\n    return null;\n  }\n}\nconst build = () => {\n  const lookup = reducer => {\n    return reduce(styles, (o$$1, style, prefix) => {\n      o$$1[prefix] = reduce(style, reducer, {});\n      return o$$1;\n    }, {});\n  };\n  _byUnicode = lookup((acc, icon, iconName) => {\n    if (icon[3]) {\n      acc[icon[3]] = iconName;\n    }\n    if (icon[2]) {\n      const aliases = icon[2].filter(a$$1 => {\n        return typeof a$$1 === 'number';\n      });\n      aliases.forEach(alias => {\n        acc[alias.toString(16)] = iconName;\n      });\n    }\n    return acc;\n  });\n  _byLigature = lookup((acc, icon, iconName) => {\n    acc[iconName] = iconName;\n    if (icon[2]) {\n      const aliases = icon[2].filter(a$$1 => {\n        return typeof a$$1 === 'string';\n      });\n      aliases.forEach(alias => {\n        acc[alias] = iconName;\n      });\n    }\n    return acc;\n  });\n  _byAlias = lookup((acc, icon, iconName) => {\n    const aliases = icon[2];\n    acc[iconName] = iconName;\n    aliases.forEach(alias => {\n      acc[alias] = iconName;\n    });\n    return acc;\n  });\n\n  // If we have a Kit, we can't determine if regular is available since we\n  // could be auto-fetching it. We'll have to assume that it is available.\n  const hasRegular = 'far' in styles || config.autoFetchSvg;\n  const shimLookups = reduce(shims, (acc, shim) => {\n    const maybeNameMaybeUnicode = shim[0];\n    let prefix = shim[1];\n    const iconName = shim[2];\n    if (prefix === 'far' && !hasRegular) {\n      prefix = 'fas';\n    }\n    if (typeof maybeNameMaybeUnicode === 'string') {\n      acc.names[maybeNameMaybeUnicode] = {\n        prefix,\n        iconName\n      };\n    }\n    if (typeof maybeNameMaybeUnicode === 'number') {\n      acc.unicodes[maybeNameMaybeUnicode.toString(16)] = {\n        prefix,\n        iconName\n      };\n    }\n    return acc;\n  }, {\n    names: {},\n    unicodes: {}\n  });\n  _byOldName = shimLookups.names;\n  _byOldUnicode = shimLookups.unicodes;\n  _defaultUsablePrefix = getCanonicalPrefix(config.styleDefault, {\n    family: config.familyDefault\n  });\n};\nonChange(c$$1 => {\n  _defaultUsablePrefix = getCanonicalPrefix(c$$1.styleDefault, {\n    family: config.familyDefault\n  });\n});\nbuild();\nfunction byUnicode(prefix, unicode) {\n  return (_byUnicode[prefix] || {})[unicode];\n}\nfunction byLigature(prefix, ligature) {\n  return (_byLigature[prefix] || {})[ligature];\n}\nfunction byAlias(prefix, alias) {\n  return (_byAlias[prefix] || {})[alias];\n}\nfunction byOldName(name) {\n  return _byOldName[name] || {\n    prefix: null,\n    iconName: null\n  };\n}\nfunction byOldUnicode(unicode) {\n  const oldUnicode = _byOldUnicode[unicode];\n  const newUnicode = byUnicode('fas', unicode);\n  return oldUnicode || (newUnicode ? {\n    prefix: 'fas',\n    iconName: newUnicode\n  } : null) || {\n    prefix: null,\n    iconName: null\n  };\n}\nfunction getDefaultUsablePrefix() {\n  return _defaultUsablePrefix;\n}\nconst emptyCanonicalIcon = () => {\n  return {\n    prefix: null,\n    iconName: null,\n    rest: []\n  };\n};\nfunction getFamilyId(values) {\n  let family = s;\n  const famProps = FAMILY_NAMES.reduce((acc, familyId) => {\n    acc[familyId] = \"\".concat(config.cssPrefix, \"-\").concat(familyId);\n    return acc;\n  }, {});\n  L.forEach(familyId => {\n    if (values.includes(famProps[familyId]) || values.some(v$$1 => PREFIXES_FOR_FAMILY[familyId].includes(v$$1))) {\n      family = familyId;\n    }\n  });\n  return family;\n}\nfunction getCanonicalPrefix(styleOrPrefix) {\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    family = s\n  } = params;\n  const style = PREFIX_TO_STYLE[family][styleOrPrefix];\n\n  // handles the exception of passing in only a family of 'duotone' with no style\n  if (family === t && !styleOrPrefix) {\n    return 'fad';\n  }\n  const prefix = STYLE_TO_PREFIX[family][styleOrPrefix] || STYLE_TO_PREFIX[family][style];\n  const defined = styleOrPrefix in namespace.styles ? styleOrPrefix : null;\n  const result = prefix || defined || null;\n  return result;\n}\nfunction moveNonFaClassesToRest(classNames) {\n  let rest = [];\n  let iconName = null;\n  classNames.forEach(cls => {\n    const result = getIconName(config.cssPrefix, cls);\n    if (result) {\n      iconName = result;\n    } else if (cls) {\n      rest.push(cls);\n    }\n  });\n  return {\n    iconName,\n    rest\n  };\n}\nfunction sortedUniqueValues(arr) {\n  return arr.sort().filter((value, index, arr) => {\n    return arr.indexOf(value) === index;\n  });\n}\nfunction getCanonicalIcon(values) {\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    skipLookups = false\n  } = params;\n  let givenPrefix = null;\n  const faCombinedClasses = Ia.concat(bt$1);\n  const faStyleOrFamilyClasses = sortedUniqueValues(values.filter(cls => faCombinedClasses.includes(cls)));\n  const nonStyleOrFamilyClasses = sortedUniqueValues(values.filter(cls => !Ia.includes(cls)));\n  const faStyles = faStyleOrFamilyClasses.filter(cls => {\n    givenPrefix = cls;\n    return !P.includes(cls);\n  });\n  const [styleFromValues = null] = faStyles;\n  const family = getFamilyId(faStyleOrFamilyClasses);\n  const canonical = _objectSpread2(_objectSpread2({}, moveNonFaClassesToRest(nonStyleOrFamilyClasses)), {}, {\n    prefix: getCanonicalPrefix(styleFromValues, {\n      family\n    })\n  });\n  return _objectSpread2(_objectSpread2(_objectSpread2({}, canonical), getDefaultCanonicalPrefix({\n    values,\n    family,\n    styles,\n    config,\n    canonical,\n    givenPrefix\n  })), applyShimAndAlias(skipLookups, givenPrefix, canonical));\n}\nfunction applyShimAndAlias(skipLookups, givenPrefix, canonical) {\n  let {\n    prefix,\n    iconName\n  } = canonical;\n  if (skipLookups || !prefix || !iconName) {\n    return {\n      prefix,\n      iconName\n    };\n  }\n  const shim = givenPrefix === 'fa' ? byOldName(iconName) : {};\n  const aliasIconName = byAlias(prefix, iconName);\n  iconName = shim.iconName || aliasIconName || iconName;\n  prefix = shim.prefix || prefix;\n  if (prefix === 'far' && !styles['far'] && styles['fas'] && !config.autoFetchSvg) {\n    // Allow a fallback from the regular style to solid if regular is not available\n    // but only if we aren't auto-fetching SVGs\n    prefix = 'fas';\n  }\n  return {\n    prefix,\n    iconName\n  };\n}\nconst newCanonicalFamilies = L.filter(familyId => {\n  return familyId !== s || familyId !== t;\n});\nconst newCanonicalStyles = Object.keys(ga).filter(key => key !== s).map(key => Object.keys(ga[key])).flat();\nfunction getDefaultCanonicalPrefix(prefixOptions) {\n  const {\n    values,\n    family,\n    canonical,\n    givenPrefix = '',\n    styles = {},\n    config: config$$1 = {}\n  } = prefixOptions;\n  const isDuotoneFamily = family === t;\n  const valuesHasDuotone = values.includes('fa-duotone') || values.includes('fad');\n  const defaultFamilyIsDuotone = config$$1.familyDefault === 'duotone';\n  const canonicalPrefixIsDuotone = canonical.prefix === 'fad' || canonical.prefix === 'fa-duotone';\n  if (!isDuotoneFamily && (valuesHasDuotone || defaultFamilyIsDuotone || canonicalPrefixIsDuotone)) {\n    canonical.prefix = 'fad';\n  }\n  if (values.includes('fa-brands') || values.includes('fab')) {\n    canonical.prefix = 'fab';\n  }\n  if (!canonical.prefix && newCanonicalFamilies.includes(family)) {\n    const validPrefix = Object.keys(styles).find(key => newCanonicalStyles.includes(key));\n    if (validPrefix || config$$1.autoFetchSvg) {\n      const defaultPrefix = pt.get(family).defaultShortPrefixId;\n      canonical.prefix = defaultPrefix;\n      canonical.iconName = byAlias(canonical.prefix, canonical.iconName) || canonical.iconName;\n    }\n  }\n  if (canonical.prefix === 'fa' || givenPrefix === 'fa') {\n    // The fa prefix is not canonical. So if it has made it through until this point\n    // we will shift it to the correct prefix.\n    canonical.prefix = getDefaultUsablePrefix() || 'fas';\n  }\n  return canonical;\n}\n\nclass Library {\n  constructor() {\n    this.definitions = {};\n  }\n  add() {\n    for (var _len = arguments.length, definitions = new Array(_len), _key = 0; _key < _len; _key++) {\n      definitions[_key] = arguments[_key];\n    }\n    const additions = definitions.reduce(this._pullDefinitions, {});\n    Object.keys(additions).forEach(key => {\n      this.definitions[key] = _objectSpread2(_objectSpread2({}, this.definitions[key] || {}), additions[key]);\n      defineIcons(key, additions[key]);\n\n      // TODO can we stop doing this? We can't get the icons by 'fa-solid' any longer so this probably needs to change\n      const longPrefix = PREFIX_TO_LONG_STYLE[s][key];\n      if (longPrefix) defineIcons(longPrefix, additions[key]);\n      build();\n    });\n  }\n  reset() {\n    this.definitions = {};\n  }\n  _pullDefinitions(additions, definition) {\n    const normalized = definition.prefix && definition.iconName && definition.icon ? {\n      0: definition\n    } : definition;\n    Object.keys(normalized).map(key => {\n      const {\n        prefix,\n        iconName,\n        icon\n      } = normalized[key];\n      const aliases = icon[2];\n      if (!additions[prefix]) additions[prefix] = {};\n      if (aliases.length > 0) {\n        aliases.forEach(alias => {\n          if (typeof alias === 'string') {\n            additions[prefix][alias] = icon;\n          }\n        });\n      }\n      additions[prefix][iconName] = icon;\n    });\n    return additions;\n  }\n}\n\nlet _plugins = [];\nlet _hooks = {};\nconst providers = {};\nconst defaultProviderKeys = Object.keys(providers);\nfunction registerPlugins(nextPlugins, _ref) {\n  let {\n    mixoutsTo: obj\n  } = _ref;\n  _plugins = nextPlugins;\n  _hooks = {};\n  Object.keys(providers).forEach(k => {\n    if (defaultProviderKeys.indexOf(k) === -1) {\n      delete providers[k];\n    }\n  });\n  _plugins.forEach(plugin => {\n    const mixout = plugin.mixout ? plugin.mixout() : {};\n    Object.keys(mixout).forEach(tk => {\n      if (typeof mixout[tk] === 'function') {\n        obj[tk] = mixout[tk];\n      }\n      if (typeof mixout[tk] === 'object') {\n        Object.keys(mixout[tk]).forEach(sk => {\n          if (!obj[tk]) {\n            obj[tk] = {};\n          }\n          obj[tk][sk] = mixout[tk][sk];\n        });\n      }\n    });\n    if (plugin.hooks) {\n      const hooks = plugin.hooks();\n      Object.keys(hooks).forEach(hook => {\n        if (!_hooks[hook]) {\n          _hooks[hook] = [];\n        }\n        _hooks[hook].push(hooks[hook]);\n      });\n    }\n    if (plugin.provides) {\n      plugin.provides(providers);\n    }\n  });\n  return obj;\n}\nfunction chainHooks(hook, accumulator) {\n  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    args[_key - 2] = arguments[_key];\n  }\n  const hookFns = _hooks[hook] || [];\n  hookFns.forEach(hookFn => {\n    accumulator = hookFn.apply(null, [accumulator, ...args]); // eslint-disable-line no-useless-call\n  });\n  return accumulator;\n}\nfunction callHooks(hook) {\n  for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    args[_key2 - 1] = arguments[_key2];\n  }\n  const hookFns = _hooks[hook] || [];\n  hookFns.forEach(hookFn => {\n    hookFn.apply(null, args);\n  });\n  return undefined;\n}\nfunction callProvided() {\n  const hook = arguments[0];\n  const args = Array.prototype.slice.call(arguments, 1);\n  return providers[hook] ? providers[hook].apply(null, args) : undefined;\n}\n\nfunction findIconDefinition(iconLookup) {\n  if (iconLookup.prefix === 'fa') {\n    iconLookup.prefix = 'fas';\n  }\n  let {\n    iconName\n  } = iconLookup;\n  const prefix = iconLookup.prefix || getDefaultUsablePrefix();\n  if (!iconName) return;\n  iconName = byAlias(prefix, iconName) || iconName;\n  return iconFromMapping(library.definitions, prefix, iconName) || iconFromMapping(namespace.styles, prefix, iconName);\n}\nconst library = new Library();\nconst noAuto = () => {\n  config.autoReplaceSvg = false;\n  config.observeMutations = false;\n  callHooks('noAuto');\n};\nconst dom = {\n  i2svg: function () {\n    let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (IS_DOM) {\n      callHooks('beforeI2svg', params);\n      callProvided('pseudoElements2svg', params);\n      return callProvided('i2svg', params);\n    } else {\n      return Promise.reject(new Error('Operation requires a DOM of some kind.'));\n    }\n  },\n  watch: function () {\n    let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const {\n      autoReplaceSvgRoot\n    } = params;\n    if (config.autoReplaceSvg === false) {\n      config.autoReplaceSvg = true;\n    }\n    config.observeMutations = true;\n    domready(() => {\n      autoReplace({\n        autoReplaceSvgRoot\n      });\n      callHooks('watch', params);\n    });\n  }\n};\nconst parse = {\n  icon: icon => {\n    if (icon === null) {\n      return null;\n    }\n    if (typeof icon === 'object' && icon.prefix && icon.iconName) {\n      return {\n        prefix: icon.prefix,\n        iconName: byAlias(icon.prefix, icon.iconName) || icon.iconName\n      };\n    }\n    if (Array.isArray(icon) && icon.length === 2) {\n      const iconName = icon[1].indexOf('fa-') === 0 ? icon[1].slice(3) : icon[1];\n      const prefix = getCanonicalPrefix(icon[0]);\n      return {\n        prefix,\n        iconName: byAlias(prefix, iconName) || iconName\n      };\n    }\n    if (typeof icon === 'string' && (icon.indexOf(\"\".concat(config.cssPrefix, \"-\")) > -1 || icon.match(ICON_SELECTION_SYNTAX_PATTERN))) {\n      const canonicalIcon = getCanonicalIcon(icon.split(' '), {\n        skipLookups: true\n      });\n      return {\n        prefix: canonicalIcon.prefix || getDefaultUsablePrefix(),\n        iconName: byAlias(canonicalIcon.prefix, canonicalIcon.iconName) || canonicalIcon.iconName\n      };\n    }\n    if (typeof icon === 'string') {\n      const prefix = getDefaultUsablePrefix();\n      return {\n        prefix,\n        iconName: byAlias(prefix, icon) || icon\n      };\n    }\n  }\n};\nconst api = {\n  noAuto,\n  config,\n  dom,\n  parse,\n  library,\n  findIconDefinition,\n  toHtml\n};\nconst autoReplace = function () {\n  let params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    autoReplaceSvgRoot = DOCUMENT\n  } = params;\n  if ((Object.keys(namespace.styles).length > 0 || config.autoFetchSvg) && IS_DOM && config.autoReplaceSvg) api.dom.i2svg({\n    node: autoReplaceSvgRoot\n  });\n};\n\nfunction domVariants(val, abstractCreator) {\n  Object.defineProperty(val, 'abstract', {\n    get: abstractCreator\n  });\n  Object.defineProperty(val, 'html', {\n    get: function () {\n      return val.abstract.map(a => toHtml(a));\n    }\n  });\n  Object.defineProperty(val, 'node', {\n    get: function () {\n      if (!IS_DOM) return;\n      const container = DOCUMENT.createElement('div');\n      container.innerHTML = val.html;\n      return container.children;\n    }\n  });\n  return val;\n}\n\nfunction asIcon (_ref) {\n  let {\n    children,\n    main,\n    mask,\n    attributes,\n    styles,\n    transform\n  } = _ref;\n  if (transformIsMeaningful(transform) && main.found && !mask.found) {\n    const {\n      width,\n      height\n    } = main;\n    const offset = {\n      x: width / height / 2,\n      y: 0.5\n    };\n    attributes['style'] = joinStyles(_objectSpread2(_objectSpread2({}, styles), {}, {\n      'transform-origin': \"\".concat(offset.x + transform.x / 16, \"em \").concat(offset.y + transform.y / 16, \"em\")\n    }));\n  }\n  return [{\n    tag: 'svg',\n    attributes,\n    children\n  }];\n}\n\nfunction asSymbol (_ref) {\n  let {\n    prefix,\n    iconName,\n    children,\n    attributes,\n    symbol\n  } = _ref;\n  const id = symbol === true ? \"\".concat(prefix, \"-\").concat(config.cssPrefix, \"-\").concat(iconName) : symbol;\n  return [{\n    tag: 'svg',\n    attributes: {\n      style: 'display: none;'\n    },\n    children: [{\n      tag: 'symbol',\n      attributes: _objectSpread2(_objectSpread2({}, attributes), {}, {\n        id\n      }),\n      children\n    }]\n  }];\n}\n\nfunction makeInlineSvgAbstract(params) {\n  const {\n    icons: {\n      main,\n      mask\n    },\n    prefix,\n    iconName,\n    transform,\n    symbol,\n    title,\n    maskId,\n    titleId,\n    extra,\n    watchable = false\n  } = params;\n  const {\n    width,\n    height\n  } = mask.found ? mask : main;\n  const isUploadedIcon = Lt.includes(prefix);\n  const attrClass = [config.replacementClass, iconName ? \"\".concat(config.cssPrefix, \"-\").concat(iconName) : ''].filter(c$$1 => extra.classes.indexOf(c$$1) === -1).filter(c$$1 => c$$1 !== '' || !!c$$1).concat(extra.classes).join(' ');\n  let content = {\n    children: [],\n    attributes: _objectSpread2(_objectSpread2({}, extra.attributes), {}, {\n      'data-prefix': prefix,\n      'data-icon': iconName,\n      'class': attrClass,\n      'role': extra.attributes.role || 'img',\n      'xmlns': 'http://www.w3.org/2000/svg',\n      'viewBox': \"0 0 \".concat(width, \" \").concat(height)\n    })\n  };\n  const uploadedIconWidthStyle = isUploadedIcon && !~extra.classes.indexOf('fa-fw') ? {\n    width: \"\".concat(width / height * 16 * 0.0625, \"em\")\n  } : {};\n  if (watchable) {\n    content.attributes[DATA_FA_I2SVG] = '';\n  }\n  if (title) {\n    content.children.push({\n      tag: 'title',\n      attributes: {\n        id: content.attributes['aria-labelledby'] || \"title-\".concat(titleId || nextUniqueId())\n      },\n      children: [title]\n    });\n    delete content.attributes.title;\n  }\n  const args = _objectSpread2(_objectSpread2({}, content), {}, {\n    prefix,\n    iconName,\n    main,\n    mask,\n    maskId,\n    transform,\n    symbol,\n    styles: _objectSpread2(_objectSpread2({}, uploadedIconWidthStyle), extra.styles)\n  });\n  const {\n    children,\n    attributes\n  } = mask.found && main.found ? callProvided('generateAbstractMask', args) || {\n    children: [],\n    attributes: {}\n  } : callProvided('generateAbstractIcon', args) || {\n    children: [],\n    attributes: {}\n  };\n  args.children = children;\n  args.attributes = attributes;\n  if (symbol) {\n    return asSymbol(args);\n  } else {\n    return asIcon(args);\n  }\n}\nfunction makeLayersTextAbstract(params) {\n  const {\n    content,\n    width,\n    height,\n    transform,\n    title,\n    extra,\n    watchable = false\n  } = params;\n  const attributes = _objectSpread2(_objectSpread2(_objectSpread2({}, extra.attributes), title ? {\n    'title': title\n  } : {}), {}, {\n    'class': extra.classes.join(' ')\n  });\n  if (watchable) {\n    attributes[DATA_FA_I2SVG] = '';\n  }\n  const styles = _objectSpread2({}, extra.styles);\n  if (transformIsMeaningful(transform)) {\n    styles['transform'] = transformForCss({\n      transform,\n      startCentered: true,\n      width,\n      height\n    });\n    styles['-webkit-transform'] = styles['transform'];\n  }\n  const styleString = joinStyles(styles);\n  if (styleString.length > 0) {\n    attributes['style'] = styleString;\n  }\n  const val = [];\n  val.push({\n    tag: 'span',\n    attributes,\n    children: [content]\n  });\n  if (title) {\n    val.push({\n      tag: 'span',\n      attributes: {\n        class: 'sr-only'\n      },\n      children: [title]\n    });\n  }\n  return val;\n}\nfunction makeLayersCounterAbstract(params) {\n  const {\n    content,\n    title,\n    extra\n  } = params;\n  const attributes = _objectSpread2(_objectSpread2(_objectSpread2({}, extra.attributes), title ? {\n    'title': title\n  } : {}), {}, {\n    'class': extra.classes.join(' ')\n  });\n  const styleString = joinStyles(extra.styles);\n  if (styleString.length > 0) {\n    attributes['style'] = styleString;\n  }\n  const val = [];\n  val.push({\n    tag: 'span',\n    attributes,\n    children: [content]\n  });\n  if (title) {\n    val.push({\n      tag: 'span',\n      attributes: {\n        class: 'sr-only'\n      },\n      children: [title]\n    });\n  }\n  return val;\n}\n\nconst {\n  styles: styles$1\n} = namespace;\nfunction asFoundIcon(icon) {\n  const width = icon[0];\n  const height = icon[1];\n  const [vectorData] = icon.slice(4);\n  let element = null;\n  if (Array.isArray(vectorData)) {\n    element = {\n      tag: 'g',\n      attributes: {\n        class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.GROUP)\n      },\n      children: [{\n        tag: 'path',\n        attributes: {\n          class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.SECONDARY),\n          fill: 'currentColor',\n          d: vectorData[0]\n        }\n      }, {\n        tag: 'path',\n        attributes: {\n          class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.PRIMARY),\n          fill: 'currentColor',\n          d: vectorData[1]\n        }\n      }]\n    };\n  } else {\n    element = {\n      tag: 'path',\n      attributes: {\n        fill: 'currentColor',\n        d: vectorData\n      }\n    };\n  }\n  return {\n    found: true,\n    width,\n    height,\n    icon: element\n  };\n}\nconst missingIconResolutionMixin = {\n  found: false,\n  width: 512,\n  height: 512\n};\nfunction maybeNotifyMissing(iconName, prefix) {\n  if (!PRODUCTION && !config.showMissingIcons && iconName) {\n    console.error(\"Icon with name \\\"\".concat(iconName, \"\\\" and prefix \\\"\").concat(prefix, \"\\\" is missing.\"));\n  }\n}\nfunction findIcon(iconName, prefix) {\n  let givenPrefix = prefix;\n  if (prefix === 'fa' && config.styleDefault !== null) {\n    prefix = getDefaultUsablePrefix();\n  }\n  return new Promise((resolve, reject) => {\n    if (givenPrefix === 'fa') {\n      const shim = byOldName(iconName) || {};\n      iconName = shim.iconName || iconName;\n      prefix = shim.prefix || prefix;\n    }\n    if (iconName && prefix && styles$1[prefix] && styles$1[prefix][iconName]) {\n      const icon = styles$1[prefix][iconName];\n      return resolve(asFoundIcon(icon));\n    }\n    maybeNotifyMissing(iconName, prefix);\n    resolve(_objectSpread2(_objectSpread2({}, missingIconResolutionMixin), {}, {\n      icon: config.showMissingIcons && iconName ? callProvided('missingIconAbstract') || {} : {}\n    }));\n  });\n}\n\nconst noop$1 = () => {};\nconst p$2 = config.measurePerformance && PERFORMANCE && PERFORMANCE.mark && PERFORMANCE.measure ? PERFORMANCE : {\n  mark: noop$1,\n  measure: noop$1\n};\nconst preamble = \"FA \\\"6.7.2\\\"\";\nconst begin = name => {\n  p$2.mark(\"\".concat(preamble, \" \").concat(name, \" begins\"));\n  return () => end(name);\n};\nconst end = name => {\n  p$2.mark(\"\".concat(preamble, \" \").concat(name, \" ends\"));\n  p$2.measure(\"\".concat(preamble, \" \").concat(name), \"\".concat(preamble, \" \").concat(name, \" begins\"), \"\".concat(preamble, \" \").concat(name, \" ends\"));\n};\nvar perf = {\n  begin,\n  end\n};\n\nconst noop$2 = () => {};\nfunction isWatched(node) {\n  const i2svg = node.getAttribute ? node.getAttribute(DATA_FA_I2SVG) : null;\n  return typeof i2svg === 'string';\n}\nfunction hasPrefixAndIcon(node) {\n  const prefix = node.getAttribute ? node.getAttribute(DATA_PREFIX) : null;\n  const icon = node.getAttribute ? node.getAttribute(DATA_ICON) : null;\n  return prefix && icon;\n}\nfunction hasBeenReplaced(node) {\n  return node && node.classList && node.classList.contains && node.classList.contains(config.replacementClass);\n}\nfunction getMutator() {\n  if (config.autoReplaceSvg === true) {\n    return mutators.replace;\n  }\n  const mutator = mutators[config.autoReplaceSvg];\n  return mutator || mutators.replace;\n}\nfunction createElementNS(tag) {\n  return DOCUMENT.createElementNS('http://www.w3.org/2000/svg', tag);\n}\nfunction createElement(tag) {\n  return DOCUMENT.createElement(tag);\n}\nfunction convertSVG(abstractObj) {\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    ceFn = abstractObj.tag === 'svg' ? createElementNS : createElement\n  } = params;\n  if (typeof abstractObj === 'string') {\n    return DOCUMENT.createTextNode(abstractObj);\n  }\n  const tag = ceFn(abstractObj.tag);\n  Object.keys(abstractObj.attributes || []).forEach(function (key) {\n    tag.setAttribute(key, abstractObj.attributes[key]);\n  });\n  const children = abstractObj.children || [];\n  children.forEach(function (child) {\n    tag.appendChild(convertSVG(child, {\n      ceFn\n    }));\n  });\n  return tag;\n}\nfunction nodeAsComment(node) {\n  let comment = \" \".concat(node.outerHTML, \" \");\n  /* BEGIN.ATTRIBUTION */\n  comment = \"\".concat(comment, \"Font Awesome fontawesome.com \");\n  /* END.ATTRIBUTION */\n  return comment;\n}\nconst mutators = {\n  replace: function (mutation) {\n    const node = mutation[0];\n    if (node.parentNode) {\n      mutation[1].forEach(abstract => {\n        node.parentNode.insertBefore(convertSVG(abstract), node);\n      });\n      if (node.getAttribute(DATA_FA_I2SVG) === null && config.keepOriginalSource) {\n        let comment = DOCUMENT.createComment(nodeAsComment(node));\n        node.parentNode.replaceChild(comment, node);\n      } else {\n        node.remove();\n      }\n    }\n  },\n  nest: function (mutation) {\n    const node = mutation[0];\n    const abstract = mutation[1];\n\n    // If we already have a replaced node we do not want to continue nesting within it.\n    // Short-circuit to the standard replacement\n    if (~classArray(node).indexOf(config.replacementClass)) {\n      return mutators.replace(mutation);\n    }\n    const forSvg = new RegExp(\"\".concat(config.cssPrefix, \"-.*\"));\n    delete abstract[0].attributes.id;\n    if (abstract[0].attributes.class) {\n      const splitClasses = abstract[0].attributes.class.split(' ').reduce((acc, cls) => {\n        if (cls === config.replacementClass || cls.match(forSvg)) {\n          acc.toSvg.push(cls);\n        } else {\n          acc.toNode.push(cls);\n        }\n        return acc;\n      }, {\n        toNode: [],\n        toSvg: []\n      });\n      abstract[0].attributes.class = splitClasses.toSvg.join(' ');\n      if (splitClasses.toNode.length === 0) {\n        node.removeAttribute('class');\n      } else {\n        node.setAttribute('class', splitClasses.toNode.join(' '));\n      }\n    }\n    const newInnerHTML = abstract.map(a => toHtml(a)).join('\\n');\n    node.setAttribute(DATA_FA_I2SVG, '');\n    node.innerHTML = newInnerHTML;\n  }\n};\nfunction performOperationSync(op) {\n  op();\n}\nfunction perform(mutations, callback) {\n  const callbackFunction = typeof callback === 'function' ? callback : noop$2;\n  if (mutations.length === 0) {\n    callbackFunction();\n  } else {\n    let frame = performOperationSync;\n    if (config.mutateApproach === MUTATION_APPROACH_ASYNC) {\n      frame = WINDOW.requestAnimationFrame || performOperationSync;\n    }\n    frame(() => {\n      const mutator = getMutator();\n      const mark = perf.begin('mutate');\n      mutations.map(mutator);\n      mark();\n      callbackFunction();\n    });\n  }\n}\nlet disabled = false;\nfunction disableObservation() {\n  disabled = true;\n}\nfunction enableObservation() {\n  disabled = false;\n}\nlet mo = null;\nfunction observe(options) {\n  if (!MUTATION_OBSERVER) {\n    return;\n  }\n  if (!config.observeMutations) {\n    return;\n  }\n  const {\n    treeCallback = noop$2,\n    nodeCallback = noop$2,\n    pseudoElementsCallback = noop$2,\n    observeMutationsRoot = DOCUMENT\n  } = options;\n  mo = new MUTATION_OBSERVER(objects => {\n    if (disabled) return;\n    const defaultPrefix = getDefaultUsablePrefix();\n    toArray(objects).forEach(mutationRecord => {\n      if (mutationRecord.type === 'childList' && mutationRecord.addedNodes.length > 0 && !isWatched(mutationRecord.addedNodes[0])) {\n        if (config.searchPseudoElements) {\n          pseudoElementsCallback(mutationRecord.target);\n        }\n        treeCallback(mutationRecord.target);\n      }\n      if (mutationRecord.type === 'attributes' && mutationRecord.target.parentNode && config.searchPseudoElements) {\n        pseudoElementsCallback(mutationRecord.target.parentNode);\n      }\n      if (mutationRecord.type === 'attributes' && isWatched(mutationRecord.target) && ~ATTRIBUTES_WATCHED_FOR_MUTATION.indexOf(mutationRecord.attributeName)) {\n        if (mutationRecord.attributeName === 'class' && hasPrefixAndIcon(mutationRecord.target)) {\n          const {\n            prefix,\n            iconName\n          } = getCanonicalIcon(classArray(mutationRecord.target));\n          mutationRecord.target.setAttribute(DATA_PREFIX, prefix || defaultPrefix);\n          if (iconName) mutationRecord.target.setAttribute(DATA_ICON, iconName);\n        } else if (hasBeenReplaced(mutationRecord.target)) {\n          nodeCallback(mutationRecord.target);\n        }\n      }\n    });\n  });\n  if (!IS_DOM) return;\n  mo.observe(observeMutationsRoot, {\n    childList: true,\n    attributes: true,\n    characterData: true,\n    subtree: true\n  });\n}\nfunction disconnect() {\n  if (!mo) return;\n  mo.disconnect();\n}\n\nfunction styleParser (node) {\n  const style = node.getAttribute('style');\n  let val = [];\n  if (style) {\n    val = style.split(';').reduce((acc, style) => {\n      const styles = style.split(':');\n      const prop = styles[0];\n      const value = styles.slice(1);\n      if (prop && value.length > 0) {\n        acc[prop] = value.join(':').trim();\n      }\n      return acc;\n    }, {});\n  }\n  return val;\n}\n\nfunction classParser (node) {\n  const existingPrefix = node.getAttribute('data-prefix');\n  const existingIconName = node.getAttribute('data-icon');\n  const innerText = node.innerText !== undefined ? node.innerText.trim() : '';\n  let val = getCanonicalIcon(classArray(node));\n  if (!val.prefix) {\n    val.prefix = getDefaultUsablePrefix();\n  }\n  if (existingPrefix && existingIconName) {\n    val.prefix = existingPrefix;\n    val.iconName = existingIconName;\n  }\n  if (val.iconName && val.prefix) {\n    return val;\n  }\n  if (val.prefix && innerText.length > 0) {\n    val.iconName = byLigature(val.prefix, node.innerText) || byUnicode(val.prefix, toHex(node.innerText));\n  }\n  if (!val.iconName && config.autoFetchSvg && node.firstChild && node.firstChild.nodeType === Node.TEXT_NODE) {\n    val.iconName = node.firstChild.data;\n  }\n  return val;\n}\n\nfunction attributesParser (node) {\n  const extraAttributes = toArray(node.attributes).reduce((acc, attr) => {\n    if (acc.name !== 'class' && acc.name !== 'style') {\n      acc[attr.name] = attr.value;\n    }\n    return acc;\n  }, {});\n  const title = node.getAttribute('title');\n  const titleId = node.getAttribute('data-fa-title-id');\n  if (config.autoA11y) {\n    if (title) {\n      extraAttributes['aria-labelledby'] = \"\".concat(config.replacementClass, \"-title-\").concat(titleId || nextUniqueId());\n    } else {\n      extraAttributes['aria-hidden'] = 'true';\n      extraAttributes['focusable'] = 'false';\n    }\n  }\n  return extraAttributes;\n}\n\nfunction blankMeta() {\n  return {\n    iconName: null,\n    title: null,\n    titleId: null,\n    prefix: null,\n    transform: meaninglessTransform,\n    symbol: false,\n    mask: {\n      iconName: null,\n      prefix: null,\n      rest: []\n    },\n    maskId: null,\n    extra: {\n      classes: [],\n      styles: {},\n      attributes: {}\n    }\n  };\n}\nfunction parseMeta(node) {\n  let parser = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    styleParser: true\n  };\n  const {\n    iconName,\n    prefix,\n    rest: extraClasses\n  } = classParser(node);\n  const extraAttributes = attributesParser(node);\n  const pluginMeta = chainHooks('parseNodeAttributes', {}, node);\n  let extraStyles = parser.styleParser ? styleParser(node) : [];\n  return _objectSpread2({\n    iconName,\n    title: node.getAttribute('title'),\n    titleId: node.getAttribute('data-fa-title-id'),\n    prefix,\n    transform: meaninglessTransform,\n    mask: {\n      iconName: null,\n      prefix: null,\n      rest: []\n    },\n    maskId: null,\n    symbol: false,\n    extra: {\n      classes: extraClasses,\n      styles: extraStyles,\n      attributes: extraAttributes\n    }\n  }, pluginMeta);\n}\n\nconst {\n  styles: styles$2\n} = namespace;\nfunction generateMutation(node) {\n  const nodeMeta = config.autoReplaceSvg === 'nest' ? parseMeta(node, {\n    styleParser: false\n  }) : parseMeta(node);\n  if (~nodeMeta.extra.classes.indexOf(LAYERS_TEXT_CLASSNAME)) {\n    return callProvided('generateLayersText', node, nodeMeta);\n  } else {\n    return callProvided('generateSvgReplacementMutation', node, nodeMeta);\n  }\n}\nfunction getKnownPrefixes() {\n  return [...Ft, ...Ia];\n}\nfunction onTree(root) {\n  let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  if (!IS_DOM) return Promise.resolve();\n  const htmlClassList = DOCUMENT.documentElement.classList;\n  const hclAdd = suffix => htmlClassList.add(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\n  const hclRemove = suffix => htmlClassList.remove(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\n  const prefixes = config.autoFetchSvg ? getKnownPrefixes() : P.concat(Object.keys(styles$2));\n  if (!prefixes.includes('fa')) {\n    prefixes.push('fa');\n  }\n  const prefixesDomQuery = [\".\".concat(LAYERS_TEXT_CLASSNAME, \":not([\").concat(DATA_FA_I2SVG, \"])\")].concat(prefixes.map(p$$1 => \".\".concat(p$$1, \":not([\").concat(DATA_FA_I2SVG, \"])\"))).join(', ');\n  if (prefixesDomQuery.length === 0) {\n    return Promise.resolve();\n  }\n  let candidates = [];\n  try {\n    candidates = toArray(root.querySelectorAll(prefixesDomQuery));\n  } catch (e$$1) {\n    // noop\n  }\n  if (candidates.length > 0) {\n    hclAdd('pending');\n    hclRemove('complete');\n  } else {\n    return Promise.resolve();\n  }\n  const mark = perf.begin('onTree');\n  const mutations = candidates.reduce((acc, node) => {\n    try {\n      const mutation = generateMutation(node);\n      if (mutation) {\n        acc.push(mutation);\n      }\n    } catch (e$$1) {\n      if (!PRODUCTION) {\n        if (e$$1.name === 'MissingIcon') {\n          console.error(e$$1);\n        }\n      }\n    }\n    return acc;\n  }, []);\n  return new Promise((resolve, reject) => {\n    Promise.all(mutations).then(resolvedMutations => {\n      perform(resolvedMutations, () => {\n        hclAdd('active');\n        hclAdd('complete');\n        hclRemove('pending');\n        if (typeof callback === 'function') callback();\n        mark();\n        resolve();\n      });\n    }).catch(e$$1 => {\n      mark();\n      reject(e$$1);\n    });\n  });\n}\nfunction onNode(node) {\n  let callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  generateMutation(node).then(mutation => {\n    if (mutation) {\n      perform([mutation], callback);\n    }\n  });\n}\nfunction resolveIcons(next) {\n  return function (maybeIconDefinition) {\n    let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const iconDefinition = (maybeIconDefinition || {}).icon ? maybeIconDefinition : findIconDefinition(maybeIconDefinition || {});\n    let {\n      mask\n    } = params;\n    if (mask) {\n      mask = (mask || {}).icon ? mask : findIconDefinition(mask || {});\n    }\n    return next(iconDefinition, _objectSpread2(_objectSpread2({}, params), {}, {\n      mask\n    }));\n  };\n}\nconst render = function (iconDefinition) {\n  let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    transform = meaninglessTransform,\n    symbol = false,\n    mask = null,\n    maskId = null,\n    title = null,\n    titleId = null,\n    classes = [],\n    attributes = {},\n    styles = {}\n  } = params;\n  if (!iconDefinition) return;\n  const {\n    prefix,\n    iconName,\n    icon\n  } = iconDefinition;\n  return domVariants(_objectSpread2({\n    type: 'icon'\n  }, iconDefinition), () => {\n    callHooks('beforeDOMElementCreation', {\n      iconDefinition,\n      params\n    });\n    if (config.autoA11y) {\n      if (title) {\n        attributes['aria-labelledby'] = \"\".concat(config.replacementClass, \"-title-\").concat(titleId || nextUniqueId());\n      } else {\n        attributes['aria-hidden'] = 'true';\n        attributes['focusable'] = 'false';\n      }\n    }\n    return makeInlineSvgAbstract({\n      icons: {\n        main: asFoundIcon(icon),\n        mask: mask ? asFoundIcon(mask.icon) : {\n          found: false,\n          width: null,\n          height: null,\n          icon: {}\n        }\n      },\n      prefix,\n      iconName,\n      transform: _objectSpread2(_objectSpread2({}, meaninglessTransform), transform),\n      symbol,\n      title,\n      maskId,\n      titleId,\n      extra: {\n        attributes,\n        styles,\n        classes\n      }\n    });\n  });\n};\nvar ReplaceElements = {\n  mixout() {\n    return {\n      icon: resolveIcons(render)\n    };\n  },\n  hooks() {\n    return {\n      mutationObserverCallbacks(accumulator) {\n        accumulator.treeCallback = onTree;\n        accumulator.nodeCallback = onNode;\n        return accumulator;\n      }\n    };\n  },\n  provides(providers$$1) {\n    providers$$1.i2svg = function (params) {\n      const {\n        node = DOCUMENT,\n        callback = () => {}\n      } = params;\n      return onTree(node, callback);\n    };\n    providers$$1.generateSvgReplacementMutation = function (node, nodeMeta) {\n      const {\n        iconName,\n        title,\n        titleId,\n        prefix,\n        transform,\n        symbol,\n        mask,\n        maskId,\n        extra\n      } = nodeMeta;\n      return new Promise((resolve, reject) => {\n        Promise.all([findIcon(iconName, prefix), mask.iconName ? findIcon(mask.iconName, mask.prefix) : Promise.resolve({\n          found: false,\n          width: 512,\n          height: 512,\n          icon: {}\n        })]).then(_ref => {\n          let [main, mask] = _ref;\n          resolve([node, makeInlineSvgAbstract({\n            icons: {\n              main,\n              mask\n            },\n            prefix,\n            iconName,\n            transform,\n            symbol,\n            maskId,\n            title,\n            titleId,\n            extra,\n            watchable: true\n          })]);\n        }).catch(reject);\n      });\n    };\n    providers$$1.generateAbstractIcon = function (_ref2) {\n      let {\n        children,\n        attributes,\n        main,\n        transform,\n        styles\n      } = _ref2;\n      const styleString = joinStyles(styles);\n      if (styleString.length > 0) {\n        attributes['style'] = styleString;\n      }\n      let nextChild;\n      if (transformIsMeaningful(transform)) {\n        nextChild = callProvided('generateAbstractTransformGrouping', {\n          main,\n          transform,\n          containerWidth: main.width,\n          iconWidth: main.width\n        });\n      }\n      children.push(nextChild || main.icon);\n      return {\n        children,\n        attributes\n      };\n    };\n  }\n};\n\nvar Layers = {\n  mixout() {\n    return {\n      layer(assembler) {\n        let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        const {\n          classes = []\n        } = params;\n        return domVariants({\n          type: 'layer'\n        }, () => {\n          callHooks('beforeDOMElementCreation', {\n            assembler,\n            params\n          });\n          let children = [];\n          assembler(args => {\n            Array.isArray(args) ? args.map(a => {\n              children = children.concat(a.abstract);\n            }) : children = children.concat(args.abstract);\n          });\n          return [{\n            tag: 'span',\n            attributes: {\n              class: [\"\".concat(config.cssPrefix, \"-layers\"), ...classes].join(' ')\n            },\n            children\n          }];\n        });\n      }\n    };\n  }\n};\n\nvar LayersCounter = {\n  mixout() {\n    return {\n      counter(content) {\n        let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        const {\n          title = null,\n          classes = [],\n          attributes = {},\n          styles = {}\n        } = params;\n        return domVariants({\n          type: 'counter',\n          content\n        }, () => {\n          callHooks('beforeDOMElementCreation', {\n            content,\n            params\n          });\n          return makeLayersCounterAbstract({\n            content: content.toString(),\n            title,\n            extra: {\n              attributes,\n              styles,\n              classes: [\"\".concat(config.cssPrefix, \"-layers-counter\"), ...classes]\n            }\n          });\n        });\n      }\n    };\n  }\n};\n\nvar LayersText = {\n  mixout() {\n    return {\n      text(content) {\n        let params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        const {\n          transform = meaninglessTransform,\n          title = null,\n          classes = [],\n          attributes = {},\n          styles = {}\n        } = params;\n        return domVariants({\n          type: 'text',\n          content\n        }, () => {\n          callHooks('beforeDOMElementCreation', {\n            content,\n            params\n          });\n          return makeLayersTextAbstract({\n            content,\n            transform: _objectSpread2(_objectSpread2({}, meaninglessTransform), transform),\n            title,\n            extra: {\n              attributes,\n              styles,\n              classes: [\"\".concat(config.cssPrefix, \"-layers-text\"), ...classes]\n            }\n          });\n        });\n      }\n    };\n  },\n  provides(providers$$1) {\n    providers$$1.generateLayersText = function (node, nodeMeta) {\n      const {\n        title,\n        transform,\n        extra\n      } = nodeMeta;\n      let width = null;\n      let height = null;\n      if (IS_IE) {\n        const computedFontSize = parseInt(getComputedStyle(node).fontSize, 10);\n        const boundingClientRect = node.getBoundingClientRect();\n        width = boundingClientRect.width / computedFontSize;\n        height = boundingClientRect.height / computedFontSize;\n      }\n      if (config.autoA11y && !title) {\n        extra.attributes['aria-hidden'] = 'true';\n      }\n      return Promise.resolve([node, makeLayersTextAbstract({\n        content: node.innerHTML,\n        width,\n        height,\n        transform,\n        title,\n        extra,\n        watchable: true\n      })]);\n    };\n  }\n};\n\nconst CLEAN_CONTENT_PATTERN = new RegExp('\\u{22}', 'ug');\nconst SECONDARY_UNICODE_RANGE = [1105920, 1112319];\nconst _FONT_FAMILY_WEIGHT_TO_PREFIX = _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, {\n  FontAwesome: {\n    normal: 'fas',\n    400: 'fas'\n  }\n}), lt), wa), Yt);\nconst FONT_FAMILY_WEIGHT_TO_PREFIX = Object.keys(_FONT_FAMILY_WEIGHT_TO_PREFIX).reduce((acc, key) => {\n  acc[key.toLowerCase()] = _FONT_FAMILY_WEIGHT_TO_PREFIX[key];\n  return acc;\n}, {});\nconst FONT_FAMILY_WEIGHT_FALLBACK = Object.keys(FONT_FAMILY_WEIGHT_TO_PREFIX).reduce((acc, fontFamily) => {\n  const weights = FONT_FAMILY_WEIGHT_TO_PREFIX[fontFamily];\n  acc[fontFamily] = weights[900] || [...Object.entries(weights)][0][1];\n  return acc;\n}, {});\nfunction hexValueFromContent(content) {\n  const cleaned = content.replace(CLEAN_CONTENT_PATTERN, '');\n  const codePoint = codePointAt(cleaned, 0);\n  const isPrependTen = codePoint >= SECONDARY_UNICODE_RANGE[0] && codePoint <= SECONDARY_UNICODE_RANGE[1];\n  const isDoubled = cleaned.length === 2 ? cleaned[0] === cleaned[1] : false;\n  return {\n    value: isDoubled ? toHex(cleaned[0]) : toHex(cleaned),\n    isSecondary: isPrependTen || isDoubled\n  };\n}\nfunction getPrefix(fontFamily, fontWeight) {\n  const fontFamilySanitized = fontFamily.replace(/^['\"]|['\"]$/g, '').toLowerCase();\n  const fontWeightInteger = parseInt(fontWeight);\n  const fontWeightSanitized = isNaN(fontWeightInteger) ? 'normal' : fontWeightInteger;\n  return (FONT_FAMILY_WEIGHT_TO_PREFIX[fontFamilySanitized] || {})[fontWeightSanitized] || FONT_FAMILY_WEIGHT_FALLBACK[fontFamilySanitized];\n}\nfunction replaceForPosition(node, position) {\n  const pendingAttribute = \"\".concat(DATA_FA_PSEUDO_ELEMENT_PENDING).concat(position.replace(':', '-'));\n  return new Promise((resolve, reject) => {\n    if (node.getAttribute(pendingAttribute) !== null) {\n      // This node is already being processed\n      return resolve();\n    }\n    const children = toArray(node.children);\n    const alreadyProcessedPseudoElement = children.filter(c$$1 => c$$1.getAttribute(DATA_FA_PSEUDO_ELEMENT) === position)[0];\n    const styles = WINDOW.getComputedStyle(node, position);\n    const fontFamily = styles.getPropertyValue('font-family');\n    const fontFamilyMatch = fontFamily.match(FONT_FAMILY_PATTERN);\n    const fontWeight = styles.getPropertyValue('font-weight');\n    const content = styles.getPropertyValue('content');\n    if (alreadyProcessedPseudoElement && !fontFamilyMatch) {\n      // If we've already processed it but the current computed style does not result in a font-family,\n      // that probably means that a class name that was previously present to make the icon has been\n      // removed. So we now should delete the icon.\n      node.removeChild(alreadyProcessedPseudoElement);\n      return resolve();\n    } else if (fontFamilyMatch && content !== 'none' && content !== '') {\n      const content = styles.getPropertyValue('content');\n      let prefix = getPrefix(fontFamily, fontWeight);\n      const {\n        value: hexValue,\n        isSecondary\n      } = hexValueFromContent(content);\n      const isV4 = fontFamilyMatch[0].startsWith('FontAwesome');\n      let iconName = byUnicode(prefix, hexValue);\n      let iconIdentifier = iconName;\n      if (isV4) {\n        const iconName4 = byOldUnicode(hexValue);\n        if (iconName4.iconName && iconName4.prefix) {\n          iconName = iconName4.iconName;\n          prefix = iconName4.prefix;\n        }\n      }\n\n      // Only convert the pseudo element in this ::before/::after position into an icon if we haven't\n      // already done so with the same prefix and iconName\n      if (iconName && !isSecondary && (!alreadyProcessedPseudoElement || alreadyProcessedPseudoElement.getAttribute(DATA_PREFIX) !== prefix || alreadyProcessedPseudoElement.getAttribute(DATA_ICON) !== iconIdentifier)) {\n        node.setAttribute(pendingAttribute, iconIdentifier);\n        if (alreadyProcessedPseudoElement) {\n          // Delete the old one, since we're replacing it with a new one\n          node.removeChild(alreadyProcessedPseudoElement);\n        }\n        const meta = blankMeta();\n        const {\n          extra\n        } = meta;\n        extra.attributes[DATA_FA_PSEUDO_ELEMENT] = position;\n        findIcon(iconName, prefix).then(main => {\n          const abstract = makeInlineSvgAbstract(_objectSpread2(_objectSpread2({}, meta), {}, {\n            icons: {\n              main,\n              mask: emptyCanonicalIcon()\n            },\n            prefix,\n            iconName: iconIdentifier,\n            extra,\n            watchable: true\n          }));\n          const element = DOCUMENT.createElementNS('http://www.w3.org/2000/svg', 'svg');\n          if (position === '::before') {\n            node.insertBefore(element, node.firstChild);\n          } else {\n            node.appendChild(element);\n          }\n          element.outerHTML = abstract.map(a$$1 => toHtml(a$$1)).join('\\n');\n          node.removeAttribute(pendingAttribute);\n          resolve();\n        }).catch(reject);\n      } else {\n        resolve();\n      }\n    } else {\n      resolve();\n    }\n  });\n}\nfunction replace(node) {\n  return Promise.all([replaceForPosition(node, '::before'), replaceForPosition(node, '::after')]);\n}\nfunction processable(node) {\n  return node.parentNode !== document.head && !~TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS.indexOf(node.tagName.toUpperCase()) && !node.getAttribute(DATA_FA_PSEUDO_ELEMENT) && (!node.parentNode || node.parentNode.tagName !== 'svg');\n}\nfunction searchPseudoElements(root) {\n  if (!IS_DOM) return;\n  return new Promise((resolve, reject) => {\n    const operations = toArray(root.querySelectorAll('*')).filter(processable).map(replace);\n    const end = perf.begin('searchPseudoElements');\n    disableObservation();\n    Promise.all(operations).then(() => {\n      end();\n      enableObservation();\n      resolve();\n    }).catch(() => {\n      end();\n      enableObservation();\n      reject();\n    });\n  });\n}\nvar PseudoElements = {\n  hooks() {\n    return {\n      mutationObserverCallbacks(accumulator) {\n        accumulator.pseudoElementsCallback = searchPseudoElements;\n        return accumulator;\n      }\n    };\n  },\n  provides(providers) {\n    providers.pseudoElements2svg = function (params) {\n      const {\n        node = DOCUMENT\n      } = params;\n      if (config.searchPseudoElements) {\n        searchPseudoElements(node);\n      }\n    };\n  }\n};\n\nlet _unwatched = false;\nvar MutationObserver$1 = {\n  mixout() {\n    return {\n      dom: {\n        unwatch() {\n          disableObservation();\n          _unwatched = true;\n        }\n      }\n    };\n  },\n  hooks() {\n    return {\n      bootstrap() {\n        observe(chainHooks('mutationObserverCallbacks', {}));\n      },\n      noAuto() {\n        disconnect();\n      },\n      watch(params) {\n        const {\n          observeMutationsRoot\n        } = params;\n        if (_unwatched) {\n          enableObservation();\n        } else {\n          observe(chainHooks('mutationObserverCallbacks', {\n            observeMutationsRoot\n          }));\n        }\n      }\n    };\n  }\n};\n\nconst parseTransformString = transformString => {\n  let transform = {\n    size: 16,\n    x: 0,\n    y: 0,\n    flipX: false,\n    flipY: false,\n    rotate: 0\n  };\n  return transformString.toLowerCase().split(' ').reduce((acc, n) => {\n    const parts = n.toLowerCase().split('-');\n    const first = parts[0];\n    let rest = parts.slice(1).join('-');\n    if (first && rest === 'h') {\n      acc.flipX = true;\n      return acc;\n    }\n    if (first && rest === 'v') {\n      acc.flipY = true;\n      return acc;\n    }\n    rest = parseFloat(rest);\n    if (isNaN(rest)) {\n      return acc;\n    }\n    switch (first) {\n      case 'grow':\n        acc.size = acc.size + rest;\n        break;\n      case 'shrink':\n        acc.size = acc.size - rest;\n        break;\n      case 'left':\n        acc.x = acc.x - rest;\n        break;\n      case 'right':\n        acc.x = acc.x + rest;\n        break;\n      case 'up':\n        acc.y = acc.y - rest;\n        break;\n      case 'down':\n        acc.y = acc.y + rest;\n        break;\n      case 'rotate':\n        acc.rotate = acc.rotate + rest;\n        break;\n    }\n    return acc;\n  }, transform);\n};\nvar PowerTransforms = {\n  mixout() {\n    return {\n      parse: {\n        transform: transformString => {\n          return parseTransformString(transformString);\n        }\n      }\n    };\n  },\n  hooks() {\n    return {\n      parseNodeAttributes(accumulator, node) {\n        const transformString = node.getAttribute('data-fa-transform');\n        if (transformString) {\n          accumulator.transform = parseTransformString(transformString);\n        }\n        return accumulator;\n      }\n    };\n  },\n  provides(providers) {\n    providers.generateAbstractTransformGrouping = function (_ref) {\n      let {\n        main,\n        transform,\n        containerWidth,\n        iconWidth\n      } = _ref;\n      const outer = {\n        transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\n      };\n      const innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\n      const innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\n      const innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\n      const inner = {\n        transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\n      };\n      const path = {\n        transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\n      };\n      const operations = {\n        outer,\n        inner,\n        path\n      };\n      return {\n        tag: 'g',\n        attributes: _objectSpread2({}, operations.outer),\n        children: [{\n          tag: 'g',\n          attributes: _objectSpread2({}, operations.inner),\n          children: [{\n            tag: main.icon.tag,\n            children: main.icon.children,\n            attributes: _objectSpread2(_objectSpread2({}, main.icon.attributes), operations.path)\n          }]\n        }]\n      };\n    };\n  }\n};\n\nconst ALL_SPACE = {\n  x: 0,\n  y: 0,\n  width: '100%',\n  height: '100%'\n};\nfunction fillBlack(abstract) {\n  let force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  if (abstract.attributes && (abstract.attributes.fill || force)) {\n    abstract.attributes.fill = 'black';\n  }\n  return abstract;\n}\nfunction deGroup(abstract) {\n  if (abstract.tag === 'g') {\n    return abstract.children;\n  } else {\n    return [abstract];\n  }\n}\nvar Masks = {\n  hooks() {\n    return {\n      parseNodeAttributes(accumulator, node) {\n        const maskData = node.getAttribute('data-fa-mask');\n        const mask = !maskData ? emptyCanonicalIcon() : getCanonicalIcon(maskData.split(' ').map(i => i.trim()));\n        if (!mask.prefix) {\n          mask.prefix = getDefaultUsablePrefix();\n        }\n        accumulator.mask = mask;\n        accumulator.maskId = node.getAttribute('data-fa-mask-id');\n        return accumulator;\n      }\n    };\n  },\n  provides(providers) {\n    providers.generateAbstractMask = function (_ref) {\n      let {\n        children,\n        attributes,\n        main,\n        mask,\n        maskId: explicitMaskId,\n        transform\n      } = _ref;\n      const {\n        width: mainWidth,\n        icon: mainPath\n      } = main;\n      const {\n        width: maskWidth,\n        icon: maskPath\n      } = mask;\n      const trans = transformForSvg({\n        transform,\n        containerWidth: maskWidth,\n        iconWidth: mainWidth\n      });\n      const maskRect = {\n        tag: 'rect',\n        attributes: _objectSpread2(_objectSpread2({}, ALL_SPACE), {}, {\n          fill: 'white'\n        })\n      };\n      const maskInnerGroupChildrenMixin = mainPath.children ? {\n        children: mainPath.children.map(fillBlack)\n      } : {};\n      const maskInnerGroup = {\n        tag: 'g',\n        attributes: _objectSpread2({}, trans.inner),\n        children: [fillBlack(_objectSpread2({\n          tag: mainPath.tag,\n          attributes: _objectSpread2(_objectSpread2({}, mainPath.attributes), trans.path)\n        }, maskInnerGroupChildrenMixin))]\n      };\n      const maskOuterGroup = {\n        tag: 'g',\n        attributes: _objectSpread2({}, trans.outer),\n        children: [maskInnerGroup]\n      };\n      const maskId = \"mask-\".concat(explicitMaskId || nextUniqueId());\n      const clipId = \"clip-\".concat(explicitMaskId || nextUniqueId());\n      const maskTag = {\n        tag: 'mask',\n        attributes: _objectSpread2(_objectSpread2({}, ALL_SPACE), {}, {\n          id: maskId,\n          maskUnits: 'userSpaceOnUse',\n          maskContentUnits: 'userSpaceOnUse'\n        }),\n        children: [maskRect, maskOuterGroup]\n      };\n      const defs = {\n        tag: 'defs',\n        children: [{\n          tag: 'clipPath',\n          attributes: {\n            id: clipId\n          },\n          children: deGroup(maskPath)\n        }, maskTag]\n      };\n      children.push(defs, {\n        tag: 'rect',\n        attributes: _objectSpread2({\n          fill: 'currentColor',\n          'clip-path': \"url(#\".concat(clipId, \")\"),\n          mask: \"url(#\".concat(maskId, \")\")\n        }, ALL_SPACE)\n      });\n      return {\n        children,\n        attributes\n      };\n    };\n  }\n};\n\nvar MissingIconIndicator = {\n  provides(providers) {\n    let reduceMotion = false;\n    if (WINDOW.matchMedia) {\n      reduceMotion = WINDOW.matchMedia('(prefers-reduced-motion: reduce)').matches;\n    }\n    providers.missingIconAbstract = function () {\n      const gChildren = [];\n      const FILL = {\n        fill: 'currentColor'\n      };\n      const ANIMATION_BASE = {\n        attributeType: 'XML',\n        repeatCount: 'indefinite',\n        dur: '2s'\n      };\n\n      // Ring\n      gChildren.push({\n        tag: 'path',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          d: 'M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z'\n        })\n      });\n      const OPACITY_ANIMATE = _objectSpread2(_objectSpread2({}, ANIMATION_BASE), {}, {\n        attributeName: 'opacity'\n      });\n      const dot = {\n        tag: 'circle',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          cx: '256',\n          cy: '364',\n          r: '28'\n        }),\n        children: []\n      };\n      if (!reduceMotion) {\n        dot.children.push({\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, ANIMATION_BASE), {}, {\n            attributeName: 'r',\n            values: '28;14;28;28;14;28;'\n          })\n        }, {\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n            values: '1;0;1;1;0;1;'\n          })\n        });\n      }\n      gChildren.push(dot);\n      gChildren.push({\n        tag: 'path',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          opacity: '1',\n          d: 'M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z'\n        }),\n        children: reduceMotion ? [] : [{\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n            values: '1;0;0;0;0;1;'\n          })\n        }]\n      });\n      if (!reduceMotion) {\n        // Exclamation\n        gChildren.push({\n          tag: 'path',\n          attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n            opacity: '0',\n            d: 'M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z'\n          }),\n          children: [{\n            tag: 'animate',\n            attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n              values: '0;0;1;1;0;0;'\n            })\n          }]\n        });\n      }\n      return {\n        tag: 'g',\n        attributes: {\n          'class': 'missing'\n        },\n        children: gChildren\n      };\n    };\n  }\n};\n\nvar SvgSymbols = {\n  hooks() {\n    return {\n      parseNodeAttributes(accumulator, node) {\n        const symbolData = node.getAttribute('data-fa-symbol');\n        const symbol = symbolData === null ? false : symbolData === '' ? true : symbolData;\n        accumulator['symbol'] = symbol;\n        return accumulator;\n      }\n    };\n  }\n};\n\nvar plugins = [InjectCSS, ReplaceElements, Layers, LayersCounter, LayersText, PseudoElements, MutationObserver$1, PowerTransforms, Masks, MissingIconIndicator, SvgSymbols];\n\nregisterPlugins(plugins, {\n  mixoutsTo: api\n});\nconst noAuto$1 = api.noAuto;\nconst config$1 = api.config;\nconst library$1 = api.library;\nconst dom$1 = api.dom;\nconst parse$1 = api.parse;\nconst findIconDefinition$1 = api.findIconDefinition;\nconst toHtml$1 = api.toHtml;\nconst icon = api.icon;\nconst layer = api.layer;\nconst text = api.text;\nconst counter = api.counter;\n\nexport { noAuto$1 as noAuto, config$1 as config, library$1 as library, dom$1 as dom, parse$1 as parse, findIconDefinition$1 as findIconDefinition, toHtml$1 as toHtml, icon, layer, text, counter, api };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAChC,OAAO,CAACD,CAAC,GAAGE,cAAc,CAACF,CAAC,CAAC,KAAKD,CAAC,GAAGI,MAAM,CAACC,cAAc,CAACL,CAAC,EAAEC,CAAC,EAAE;IAChEK,KAAK,EAAEJ,CAAC;IACRK,UAAU,EAAE,CAAC,CAAC;IACdC,YAAY,EAAE,CAAC,CAAC;IAChBC,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC,GAAGT,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAClB;AACA,SAASU,SAASA,CAACR,CAAC,EAAEF,CAAC,EAAE;EACvB,IAAI,UAAU,IAAI,OAAOA,CAAC,IAAI,IAAI,KAAKA,CAAC,EAAE,MAAM,IAAIW,SAAS,CAAC,oDAAoD,CAAC;EACnHT,CAAC,CAACU,SAAS,GAAGR,MAAM,CAACS,MAAM,CAACb,CAAC,IAAIA,CAAC,CAACY,SAAS,EAAE;IAC5CE,WAAW,EAAE;MACXR,KAAK,EAAEJ,CAAC;MACRO,QAAQ,EAAE,CAAC,CAAC;MACZD,YAAY,EAAE,CAAC;IACjB;EACF,CAAC,CAAC,EAAEJ,MAAM,CAACC,cAAc,CAACH,CAAC,EAAE,WAAW,EAAE;IACxCO,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC,EAAET,CAAC,IAAIe,eAAe,CAACb,CAAC,EAAEF,CAAC,CAAC;AAChC;AACA,SAASgB,OAAOA,CAAChB,CAAC,EAAEC,CAAC,EAAE;EACrB,IAAIC,CAAC,GAAGE,MAAM,CAACa,IAAI,CAACjB,CAAC,CAAC;EACtB,IAAII,MAAM,CAACc,qBAAqB,EAAE;IAChC,IAAIC,CAAC,GAAGf,MAAM,CAACc,qBAAqB,CAAClB,CAAC,CAAC;IACvCC,CAAC,KAAKkB,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUnB,CAAC,EAAE;MAC9B,OAAOG,MAAM,CAACiB,wBAAwB,CAACrB,CAAC,EAAEC,CAAC,CAAC,CAACM,UAAU;IACzD,CAAC,CAAC,CAAC,EAAEL,CAAC,CAACoB,IAAI,CAACC,KAAK,CAACrB,CAAC,EAAEiB,CAAC,CAAC;EACzB;EACA,OAAOjB,CAAC;AACV;AACA,SAASsB,cAAcA,CAACxB,CAAC,EAAE;EACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,SAAS,CAACC,MAAM,EAAEzB,CAAC,EAAE,EAAE;IACzC,IAAIC,CAAC,GAAG,IAAI,IAAIuB,SAAS,CAACxB,CAAC,CAAC,GAAGwB,SAAS,CAACxB,CAAC,CAAC,GAAG,CAAC,CAAC;IAChDA,CAAC,GAAG,CAAC,GAAGe,OAAO,CAACZ,MAAM,CAACF,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACyB,OAAO,CAAC,UAAU1B,CAAC,EAAE;MAClDF,eAAe,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,GAAGG,MAAM,CAACwB,yBAAyB,GAAGxB,MAAM,CAACyB,gBAAgB,CAAC7B,CAAC,EAAEI,MAAM,CAACwB,yBAAyB,CAAC1B,CAAC,CAAC,CAAC,GAAGc,OAAO,CAACZ,MAAM,CAACF,CAAC,CAAC,CAAC,CAACyB,OAAO,CAAC,UAAU1B,CAAC,EAAE;MAChJG,MAAM,CAACC,cAAc,CAACL,CAAC,EAAEC,CAAC,EAAEG,MAAM,CAACiB,wBAAwB,CAACnB,CAAC,EAAED,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;EACJ;EACA,OAAOD,CAAC;AACV;AACA,SAASe,eAAeA,CAACb,CAAC,EAAEF,CAAC,EAAE;EAC7B,OAAOe,eAAe,GAAGX,MAAM,CAAC0B,cAAc,GAAG1B,MAAM,CAAC0B,cAAc,CAACC,IAAI,CAAC,CAAC,GAAG,UAAU7B,CAAC,EAAEF,CAAC,EAAE;IAC9F,OAAOE,CAAC,CAAC8B,SAAS,GAAGhC,CAAC,EAAEE,CAAC;EAC3B,CAAC,EAAEa,eAAe,CAACb,CAAC,EAAEF,CAAC,CAAC;AAC1B;AACA,SAASiC,YAAYA,CAAC/B,CAAC,EAAED,CAAC,EAAE;EAC1B,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EACxC,IAAIF,CAAC,GAAGE,CAAC,CAACgC,MAAM,CAACC,WAAW,CAAC;EAC7B,IAAI,KAAK,CAAC,KAAKnC,CAAC,EAAE;IAChB,IAAIoC,CAAC,GAAGpC,CAAC,CAACqC,IAAI,CAACnC,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IACjC,IAAI,QAAQ,IAAI,OAAOmC,CAAC,EAAE,OAAOA,CAAC;IAClC,MAAM,IAAIzB,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAAC,QAAQ,KAAKV,CAAC,GAAGqC,MAAM,GAAGC,MAAM,EAAErC,CAAC,CAAC;AAC9C;AACA,SAASC,cAAcA,CAACD,CAAC,EAAE;EACzB,IAAIkC,CAAC,GAAGH,YAAY,CAAC/B,CAAC,EAAE,QAAQ,CAAC;EACjC,OAAO,QAAQ,IAAI,OAAOkC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAC1C;AACA,SAASI,WAAWA,CAAA,EAAG;EACrBA,WAAW,GAAG,SAAAA,CAAUxC,CAAC,EAAEC,CAAC,EAAE;IAC5B,OAAO,IAAIwC,WAAW,CAACzC,CAAC,EAAE,KAAK,CAAC,EAAEC,CAAC,CAAC;EACtC,CAAC;EACD,IAAID,CAAC,GAAG0C,MAAM,CAAC9B,SAAS;IACtBX,CAAC,GAAG,IAAI0C,OAAO,CAAC,CAAC;EACnB,SAASF,WAAWA,CAACzC,CAAC,EAAEE,CAAC,EAAE0C,CAAC,EAAE;IAC5B,IAAIzB,CAAC,GAAGuB,MAAM,CAAC1C,CAAC,EAAEE,CAAC,CAAC;IACpB,OAAOD,CAAC,CAAC4C,GAAG,CAAC1B,CAAC,EAAEyB,CAAC,IAAI3C,CAAC,CAAC6C,GAAG,CAAC9C,CAAC,CAAC,CAAC,EAAEe,eAAe,CAACI,CAAC,EAAEsB,WAAW,CAAC7B,SAAS,CAAC;EAC3E;EACA,SAASmC,WAAWA,CAAC/C,CAAC,EAAEE,CAAC,EAAE;IACzB,IAAI0C,CAAC,GAAG3C,CAAC,CAAC6C,GAAG,CAAC5C,CAAC,CAAC;IAChB,OAAOE,MAAM,CAACa,IAAI,CAAC2B,CAAC,CAAC,CAACI,MAAM,CAAC,UAAU/C,CAAC,EAAEC,CAAC,EAAE;MAC3C,IAAIiB,CAAC,GAAGyB,CAAC,CAAC1C,CAAC,CAAC;MACZ,IAAI,QAAQ,IAAI,OAAOiB,CAAC,EAAElB,CAAC,CAACC,CAAC,CAAC,GAAGF,CAAC,CAACmB,CAAC,CAAC,CAAC,KAAK;QACzC,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,KAAKpC,CAAC,CAACmB,CAAC,CAACiB,CAAC,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,GAAGjB,CAAC,CAACO,MAAM,GAAGU,CAAC,EAAE;QAC5DnC,CAAC,CAACC,CAAC,CAAC,GAAGF,CAAC,CAACmB,CAAC,CAACiB,CAAC,CAAC,CAAC;MAChB;MACA,OAAOnC,CAAC;IACV,CAAC,EAAEG,MAAM,CAACS,MAAM,CAAC,IAAI,CAAC,CAAC;EACzB;EACA,OAAOH,SAAS,CAAC+B,WAAW,EAAEC,MAAM,CAAC,EAAED,WAAW,CAAC7B,SAAS,CAACqC,IAAI,GAAG,UAAUhD,CAAC,EAAE;IAC/E,IAAIC,CAAC,GAAGF,CAAC,CAACiD,IAAI,CAACZ,IAAI,CAAC,IAAI,EAAEpC,CAAC,CAAC;IAC5B,IAAIC,CAAC,EAAE;MACLA,CAAC,CAACgD,MAAM,GAAGH,WAAW,CAAC7C,CAAC,EAAE,IAAI,CAAC;MAC/B,IAAI0C,CAAC,GAAG1C,CAAC,CAACiD,OAAO;MACjBP,CAAC,KAAKA,CAAC,CAACM,MAAM,GAAGH,WAAW,CAACH,CAAC,EAAE,IAAI,CAAC,CAAC;IACxC;IACA,OAAO1C,CAAC;EACV,CAAC,EAAEuC,WAAW,CAAC7B,SAAS,CAACsB,MAAM,CAACkB,OAAO,CAAC,GAAG,UAAUlD,CAAC,EAAE0C,CAAC,EAAE;IACzD,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE;MACxB,IAAIzB,CAAC,GAAGlB,CAAC,CAAC6C,GAAG,CAAC,IAAI,CAAC;MACnB,OAAO9C,CAAC,CAACkC,MAAM,CAACkB,OAAO,CAAC,CAACf,IAAI,CAAC,IAAI,EAAEnC,CAAC,EAAE0C,CAAC,CAACQ,OAAO,CAAC,cAAc,EAAE,UAAUpD,CAAC,EAAEC,CAAC,EAAE;QAC/E,IAAIC,CAAC,GAAGiB,CAAC,CAAClB,CAAC,CAAC;QACZ,OAAO,GAAG,IAAIoD,KAAK,CAACC,OAAO,CAACpD,CAAC,CAAC,GAAGA,CAAC,CAACqD,IAAI,CAAC,GAAG,CAAC,GAAGrD,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC;IACL;IACA,IAAI,UAAU,IAAI,OAAO0C,CAAC,EAAE;MAC1B,IAAIR,CAAC,GAAG,IAAI;MACZ,OAAOpC,CAAC,CAACkC,MAAM,CAACkB,OAAO,CAAC,CAACf,IAAI,CAAC,IAAI,EAAEnC,CAAC,EAAE,YAAY;QACjD,IAAIF,CAAC,GAAGyB,SAAS;QACjB,OAAO,QAAQ,IAAI,OAAOzB,CAAC,CAACA,CAAC,CAAC0B,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC1B,CAAC,GAAG,EAAE,CAACwD,KAAK,CAACnB,IAAI,CAACrC,CAAC,CAAC,EAAEsB,IAAI,CAACyB,WAAW,CAAC/C,CAAC,EAAEoC,CAAC,CAAC,CAAC,EAAEQ,CAAC,CAACrB,KAAK,CAAC,IAAI,EAAEvB,CAAC,CAAC;MAC/G,CAAC,CAAC;IACJ;IACA,OAAOA,CAAC,CAACkC,MAAM,CAACkB,OAAO,CAAC,CAACf,IAAI,CAAC,IAAI,EAAEnC,CAAC,EAAE0C,CAAC,CAAC;EAC3C,CAAC,EAAEJ,WAAW,CAACjB,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;AACvC;AAEA,MAAMgC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACrB,IAAIC,OAAO,GAAG,CAAC,CAAC;AAChB,IAAIC,SAAS,GAAG,CAAC,CAAC;AAClB,IAAIC,kBAAkB,GAAG,IAAI;AAC7B,IAAIC,YAAY,GAAG;EACjBC,IAAI,EAAEL,IAAI;EACVM,OAAO,EAAEN;AACX,CAAC;AACD,IAAI;EACF,IAAI,OAAOO,MAAM,KAAK,WAAW,EAAEN,OAAO,GAAGM,MAAM;EACnD,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAEN,SAAS,GAAGM,QAAQ;EACzD,IAAI,OAAOC,gBAAgB,KAAK,WAAW,EAAEN,kBAAkB,GAAGM,gBAAgB;EAClF,IAAI,OAAOC,WAAW,KAAK,WAAW,EAAEN,YAAY,GAAGM,WAAW;AACpE,CAAC,CAAC,OAAOnE,CAAC,EAAE,CAAC;AACb,MAAM;EACJoE,SAAS,GAAG;AACd,CAAC,GAAGV,OAAO,CAACW,SAAS,IAAI,CAAC,CAAC;AAC3B,MAAMC,MAAM,GAAGZ,OAAO;AACtB,MAAMa,QAAQ,GAAGZ,SAAS;AAC1B,MAAMa,iBAAiB,GAAGZ,kBAAkB;AAC5C,MAAMa,WAAW,GAAGZ,YAAY;AAChC,MAAMa,UAAU,GAAG,CAAC,CAACJ,MAAM,CAACL,QAAQ;AACpC,MAAMU,MAAM,GAAG,CAAC,CAACJ,QAAQ,CAACK,eAAe,IAAI,CAAC,CAACL,QAAQ,CAACM,IAAI,IAAI,OAAON,QAAQ,CAACO,gBAAgB,KAAK,UAAU,IAAI,OAAOP,QAAQ,CAACQ,aAAa,KAAK,UAAU;AAC/J,MAAMC,KAAK,GAAG,CAACZ,SAAS,CAACa,OAAO,CAAC,MAAM,CAAC,IAAI,CAACb,SAAS,CAACa,OAAO,CAAC,UAAU,CAAC;AAE1E,IAAIrC,CAAC,GAAG,kEAAkE;EACxEsC,CAAC,GAAG,uGAAuG;AAC7G,IAAIC,CAAC,GAAG;IACJC,OAAO,EAAE;MACPC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,OAAO;MACZ,UAAU,EAAE,OAAO;MACnBC,GAAG,EAAE,SAAS;MACd,YAAY,EAAE,SAAS;MACvBC,GAAG,EAAE,OAAO;MACZ,UAAU,EAAE,OAAO;MACnBC,GAAG,EAAE,MAAM;MACX,SAAS,EAAE,MAAM;MACjBC,GAAG,EAAE,QAAQ;MACb,WAAW,EAAE;IACf,CAAC;IACDC,OAAO,EAAE;MACPN,EAAE,EAAE,OAAO;MACXO,GAAG,EAAE,OAAO;MACZ,UAAU,EAAE,OAAO;MACnB,YAAY,EAAE,OAAO;MACrBC,IAAI,EAAE,SAAS;MACf,YAAY,EAAE,SAAS;MACvBC,IAAI,EAAE,OAAO;MACb,UAAU,EAAE,OAAO;MACnBC,IAAI,EAAE,MAAM;MACZ,SAAS,EAAE;IACb,CAAC;IACDC,KAAK,EAAE;MACLX,EAAE,EAAE,OAAO;MACXY,IAAI,EAAE,OAAO;MACb,UAAU,EAAE,OAAO;MACnBC,IAAI,EAAE,SAAS;MACf,YAAY,EAAE,SAAS;MACvBC,IAAI,EAAE,OAAO;MACb,UAAU,EAAE,OAAO;MACnBC,IAAI,EAAE,MAAM;MACZ,SAAS,EAAE;IACb,CAAC;IACD,eAAe,EAAE;MACff,EAAE,EAAE,OAAO;MACXgB,KAAK,EAAE,OAAO;MACd,UAAU,EAAE,OAAO;MACnBC,KAAK,EAAE,SAAS;MAChB,YAAY,EAAE,SAAS;MACvBC,KAAK,EAAE,OAAO;MACd,UAAU,EAAE,OAAO;MACnBC,KAAK,EAAE,MAAM;MACb,SAAS,EAAE;IACb;EACF,CAAC;EACDC,CAAC,GAAG;IACFC,KAAK,EAAE,eAAe;IACtBC,YAAY,EAAE,cAAc;IAC5BC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE;EACb,CAAC;EACDC,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,kBAAkB,CAAC;AAClE,IAAIC,CAAC,GAAG,SAAS;EACf7G,CAAC,GAAG,SAAS;EACbD,CAAC,GAAG,OAAO;EACXkB,CAAC,GAAG,eAAe;EACnB6F,CAAC,GAAG,CAACD,CAAC,EAAE7G,CAAC,EAAED,CAAC,EAAEkB,CAAC,CAAC;AAClB,IAAI8F,CAAC,GAAG;EACJ7B,OAAO,EAAE;IACP,GAAG,EAAE,KAAK;IACV,GAAG,EAAE,KAAK;IACV8B,MAAM,EAAE,KAAK;IACb,GAAG,EAAE,KAAK;IACV,GAAG,EAAE;EACP,CAAC;EACDvB,OAAO,EAAE;IACP,GAAG,EAAE,KAAK;IACV,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACDK,KAAK,EAAE;IACL,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,eAAe,EAAE;IACf,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE;EACP;AACF,CAAC;AACH,IAAImB,EAAE,GAAG;EACL,qBAAqB,EAAE;IACrB,GAAG,EAAE,KAAK;IACV,GAAG,EAAE;EACP,CAAC;EACD,oBAAoB,EAAE;IACpB,GAAG,EAAE,KAAK;IACV,GAAG,EAAE,KAAK;IACVD,MAAM,EAAE,KAAK;IACb,GAAG,EAAE,KAAK;IACV,GAAG,EAAE;EACP,CAAC;EACD,uBAAuB,EAAE;IACvB,GAAG,EAAE,KAAK;IACVA,MAAM,EAAE;EACV,CAAC;EACD,wBAAwB,EAAE;IACxB,GAAG,EAAE,KAAK;IACV,GAAG,EAAE,MAAM;IACXA,MAAM,EAAE,MAAM;IACd,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,sBAAsB,EAAE;IACtB,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACXA,MAAM,EAAE,MAAM;IACd,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,8BAA8B,EAAE;IAC9B,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,OAAO;IACZA,MAAM,EAAE,OAAO;IACf,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE;EACP;AACF,CAAC;AACH,IAAIE,EAAE,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE;IAC1BC,oBAAoB,EAAE,KAAK;IAC3BC,cAAc,EAAE,OAAO;IACvBC,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC;IACzDC,cAAc,EAAE,EAAE;IAClBC,iBAAiB,EAAE;EACrB,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE;IACZJ,oBAAoB,EAAE,MAAM;IAC5BC,cAAc,EAAE,OAAO;IACvBC,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;IAC/CC,cAAc,EAAE,EAAE;IAClBC,iBAAiB,EAAE;EACrB,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE;IACdJ,oBAAoB,EAAE,KAAK;IAC3BC,cAAc,EAAE,OAAO;IACvBC,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;IAC/CC,cAAc,EAAE,EAAE;IAClBC,iBAAiB,EAAE;EACrB,CAAC,CAAC,EAAE,CAAC,eAAe,EAAE;IACpBJ,oBAAoB,EAAE,OAAO;IAC7BC,cAAc,EAAE,OAAO;IACvBC,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;IAC/CC,cAAc,EAAE,EAAE;IAClBC,iBAAiB,EAAE;EACrB,CAAC,CAAC,CAAC,CAAC;EACJC,EAAE,GAAG;IACHvC,OAAO,EAAE;MACPwC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE,KAAK;MACZC,IAAI,EAAE,KAAK;MACXC,MAAM,EAAE;IACV,CAAC;IACDrC,OAAO,EAAE;MACPiC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,MAAM;MACfC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE;IACR,CAAC;IACD/B,KAAK,EAAE;MACL4B,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,MAAM;MACfC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE;IACR,CAAC;IACD,eAAe,EAAE;MACfH,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,OAAO;MAChBC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE;IACR;EACF,CAAC;AACH,IAAIE,EAAE,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,gBAAgB,CAAC;EAClDC,EAAE,GAAG;IACHC,GAAG,EAAE;MACHC,GAAG,EAAE,KAAK;MACV,QAAQ,EAAE;IACZ,CAAC;IACD,aAAa,EAAE;MACbC,IAAI,EAAE,aAAa;MACnB,gBAAgB,EAAE;IACpB;EACF,CAAC;EACDC,EAAE,GAAG,CAAC,KAAK,CAAC;AACd,IAAIC,EAAE,GAAG;EACPJ,GAAG,EAAE;IACH,QAAQ,EAAE;EACZ,CAAC;EACD,aAAa,EAAE;IACb,gBAAgB,EAAE;EACpB;AACF,CAAC;AACD,IAAIK,EAAE,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;EACtBC,EAAE,GAAG;IACHN,GAAG,EAAE;MACHC,GAAG,EAAE;IACP,CAAC;IACD,aAAa,EAAE;MACbC,IAAI,EAAE;IACR;EACF,CAAC;AACH,IAAIK,EAAE,GAAG;EACLP,GAAG,EAAE;IACHA,GAAG,EAAE;EACP,CAAC;EACD,aAAa,EAAE;IACb,aAAa,EAAE;EACjB;AACF,CAAC;AAEH,IAAIQ,GAAG,GAAG;IACNjC,KAAK,EAAE,eAAe;IACtBC,YAAY,EAAE,cAAc;IAC5BC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE;EACb,CAAC;EACD+B,GAAG,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,kBAAkB,CAAC;AACpE,IAAIC,IAAI,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,gBAAgB,CAAC;AACtD,IAAIC,EAAE,GAAG;EACL,kBAAkB,EAAE;IAClB,GAAG,EAAE,KAAK;IACV5B,MAAM,EAAE;EACV,CAAC;EACD,0BAA0B,EAAE;IAC1B,GAAG,EAAE,MAAM;IACXA,MAAM,EAAE;EACV;AACF,CAAC;AACH,IAAI6B,EAAE,GAAG;IACL3D,OAAO,EAAE;MACP,WAAW,EAAE,KAAK;MAClB,YAAY,EAAE,KAAK;MACnB,UAAU,EAAE,KAAK;MACjB,YAAY,EAAE,KAAK;MACnB,UAAU,EAAE,KAAK;MACjB,SAAS,EAAE;IACb,CAAC;IACDO,OAAO,EAAE;MACP,YAAY,EAAE,MAAM;MACpB,UAAU,EAAE,MAAM;MAClB,SAAS,EAAE;IACb,CAAC;IACDK,KAAK,EAAE;MACL,UAAU,EAAE,MAAM;MAClB,YAAY,EAAE,MAAM;MACpB,UAAU,EAAE,MAAM;MAClB,SAAS,EAAE;IACb,CAAC;IACD,eAAe,EAAE;MACf,UAAU,EAAE,OAAO;MACnB,YAAY,EAAE,OAAO;MACrB,UAAU,EAAE,OAAO;MACnB,SAAS,EAAE;IACb;EACF,CAAC;EACDgD,GAAG,GAAG;IACJ5D,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC5CO,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACjCK,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACvC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;EACtD,CAAC;EACDiD,EAAE,GAAG;IACH7D,OAAO,EAAE;MACPM,GAAG,EAAE,WAAW;MAChBE,GAAG,EAAE,YAAY;MACjBJ,GAAG,EAAE,UAAU;MACfD,GAAG,EAAE,YAAY;MACjBD,GAAG,EAAE,UAAU;MACfG,GAAG,EAAE;IACP,CAAC;IACDE,OAAO,EAAE;MACPE,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE;IACR,CAAC;IACDC,KAAK,EAAE;MACLC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE;IACR,CAAC;IACD,eAAe,EAAE;MACfC,KAAK,EAAE,UAAU;MACjBC,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE,UAAU;MACjBC,KAAK,EAAE;IACT;EACF,CAAC;EACD0C,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,WAAW,CAAC;EAChFC,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAGP,GAAG,EAAE,GAAGM,CAAC,CAAC;EAC/JE,GAAG,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC;EAChEC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EACrCC,GAAG,GAAGD,GAAG,CAACE,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EAC1DC,EAAE,GAAG,CAAC,GAAGpJ,MAAM,CAACa,IAAI,CAAC+H,GAAG,CAAC,EAAE,GAAGI,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,iBAAiB,EAAE,eAAe,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,gBAAgB,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAET,GAAG,CAACjC,KAAK,EAAEiC,GAAG,CAAChC,YAAY,EAAEgC,GAAG,CAAC/B,OAAO,EAAE+B,GAAG,CAAC9B,SAAS,CAAC,CAAC0C,MAAM,CAACF,GAAG,CAACI,GAAG,CAACC,CAAC,IAAI,EAAE,CAACH,MAAM,CAACG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAACH,MAAM,CAACD,GAAG,CAACG,GAAG,CAACC,CAAC,IAAI,IAAI,CAACH,MAAM,CAACG,CAAC,CAAC,CAAC,CAAC;AAC5iB,IAAIC,EAAE,GAAG;EACL,qBAAqB,EAAE;IACrB,GAAG,EAAE,KAAK;IACV,GAAG,EAAE;EACP,CAAC;EACD,oBAAoB,EAAE;IACpB,GAAG,EAAE,KAAK;IACV,GAAG,EAAE,KAAK;IACVzC,MAAM,EAAE,KAAK;IACb,GAAG,EAAE;EACP,CAAC;EACD,uBAAuB,EAAE;IACvB,GAAG,EAAE,KAAK;IACVA,MAAM,EAAE;EACV,CAAC;EACD,wBAAwB,EAAE;IACxB,GAAG,EAAE;EACP;AACF,CAAC;AAEH,MAAM0C,oBAAoB,GAAG,oBAAoB;AACjD,MAAMC,aAAa,GAAG,EAAE;AACxB,MAAMC,kBAAkB,GAAG,IAAI;AAC/B,MAAMC,yBAAyB,GAAG,gBAAgB;AAClD,MAAMC,aAAa,GAAG,eAAe;AACrC,MAAMC,sBAAsB,GAAG,wBAAwB;AACvD,MAAMC,8BAA8B,GAAG,gCAAgC;AACvE,MAAMC,WAAW,GAAG,aAAa;AACjC,MAAMC,SAAS,GAAG,WAAW;AAC7B,MAAMC,2BAA2B,GAAG,mBAAmB;AACvD,MAAMC,uBAAuB,GAAG,OAAO;AACvC,MAAMC,mCAAmC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;AAC/E,MAAMC,UAAU,GAAG,CAAC,MAAM;EACxB,IAAI;IACF,OAAOC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;EAC9C,CAAC,CAAC,OAAOC,IAAI,EAAE;IACb,OAAO,KAAK;EACd;AACF,CAAC,EAAE,CAAC;AACJ,SAASC,WAAWA,CAACC,GAAG,EAAE;EACxB;EACA,OAAO,IAAIC,KAAK,CAACD,GAAG,EAAE;IACpBhI,GAAGA,CAACkI,MAAM,EAAEC,IAAI,EAAE;MAChB,OAAOA,IAAI,IAAID,MAAM,GAAGA,MAAM,CAACC,IAAI,CAAC,GAAGD,MAAM,CAACjE,CAAC,CAAC;IAClD;EACF,CAAC,CAAC;AACJ;AACA,MAAMmE,gBAAgB,GAAG1J,cAAc,CAAC,CAAC,CAAC,EAAE2D,CAAC,CAAC;;AAE9C;AACA;AACA;AACA+F,gBAAgB,CAACnE,CAAC,CAAC,GAAGvF,cAAc,CAACA,cAAc,CAACA,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE;EACpF,YAAY,EAAE;AAChB,CAAC,CAAC,EAAE2D,CAAC,CAAC4B,CAAC,CAAC,CAAC,EAAEmB,EAAE,CAAC,KAAK,CAAC,CAAC,EAAEA,EAAE,CAAC,aAAa,CAAC,CAAC;AACzC,MAAMiD,eAAe,GAAGN,WAAW,CAACK,gBAAgB,CAAC;AACrD,MAAME,gBAAgB,GAAG5J,cAAc,CAAC,CAAC,CAAC,EAAEmG,EAAE,CAAC;;AAE/C;AACA;AACAyD,gBAAgB,CAACrE,CAAC,CAAC,GAAGvF,cAAc,CAACA,cAAc,CAACA,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE;EACpFmE,OAAO,EAAE;AACX,CAAC,CAAC,EAAEyF,gBAAgB,CAACrE,CAAC,CAAC,CAAC,EAAE2B,EAAE,CAAC,KAAK,CAAC,CAAC,EAAEA,EAAE,CAAC,aAAa,CAAC,CAAC;AACxD,MAAM2C,eAAe,GAAGR,WAAW,CAACO,gBAAgB,CAAC;AACrD,MAAME,qBAAqB,GAAG9J,cAAc,CAAC,CAAC,CAAC,EAAEyH,EAAE,CAAC;AACpDqC,qBAAqB,CAACvE,CAAC,CAAC,GAAGvF,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE8J,qBAAqB,CAACvE,CAAC,CAAC,CAAC,EAAE0B,EAAE,CAAC,KAAK,CAAC,CAAC;AAClG,MAAM8C,oBAAoB,GAAGV,WAAW,CAACS,qBAAqB,CAAC;AAC/D,MAAME,qBAAqB,GAAGhK,cAAc,CAAC,CAAC,CAAC,EAAEuH,EAAE,CAAC;AACpDyC,qBAAqB,CAACzE,CAAC,CAAC,GAAGvF,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEgK,qBAAqB,CAACzE,CAAC,CAAC,CAAC,EAAEwB,EAAE,CAAC,KAAK,CAAC,CAAC;AAClG,MAAMkD,oBAAoB,GAAGZ,WAAW,CAACW,qBAAqB,CAAC;AAC/D,MAAME,6BAA6B,GAAG9I,CAAC,CAAC,CAAC;;AAEzC,MAAM+I,qBAAqB,GAAG,gBAAgB;AAC9C,MAAMC,mBAAmB,GAAG1G,CAAC;AAC7B,MAAM2G,sBAAsB,GAAGrK,cAAc,CAAC,CAAC,CAAC,EAAEyF,CAAC,CAAC;AACpD,MAAM6E,qBAAqB,GAAGjB,WAAW,CAACgB,sBAAsB,CAAC;AACjE,MAAME,+BAA+B,GAAG,CAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,mBAAmB,EAAE,cAAc,CAAC;AAClH,MAAMC,eAAe,GAAGvF,CAAC;AACzB,MAAMwF,gBAAgB,GAAG,CAAC,GAAG3D,EAAE,EAAE,GAAGkB,EAAE,CAAC;AAEvC,MAAM0C,OAAO,GAAG5H,MAAM,CAAC6H,iBAAiB,IAAI,CAAC,CAAC;AAC9C,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAIC,OAAO,GAAG/H,QAAQ,CAACgI,aAAa,CAAC,SAAS,GAAGF,IAAI,GAAG,GAAG,CAAC;EAC5D,IAAIC,OAAO,EAAE;IACX,OAAOA,OAAO,CAACE,YAAY,CAACH,IAAI,CAAC;EACnC;AACF;AACA,SAASI,MAAMA,CAACC,GAAG,EAAE;EACnB;EACA;EACA,IAAIA,GAAG,KAAK,EAAE,EAAE,OAAO,IAAI;EAC3B,IAAIA,GAAG,KAAK,OAAO,EAAE,OAAO,KAAK;EACjC,IAAIA,GAAG,KAAK,MAAM,EAAE,OAAO,IAAI;EAC/B,OAAOA,GAAG;AACZ;AACA,IAAInI,QAAQ,IAAI,OAAOA,QAAQ,CAACgI,aAAa,KAAK,UAAU,EAAE;EAC5D,MAAMI,KAAK,GAAG,CAAC,CAAC,oBAAoB,EAAE,cAAc,CAAC,EAAE,CAAC,iBAAiB,EAAE,WAAW,CAAC,EAAE,CAAC,qBAAqB,EAAE,eAAe,CAAC,EAAE,CAAC,oBAAoB,EAAE,cAAc,CAAC,EAAE,CAAC,wBAAwB,EAAE,kBAAkB,CAAC,EAAE,CAAC,uBAAuB,EAAE,gBAAgB,CAAC,EAAE,CAAC,mBAAmB,EAAE,YAAY,CAAC,EAAE,CAAC,gBAAgB,EAAE,UAAU,CAAC,EAAE,CAAC,6BAA6B,EAAE,sBAAsB,CAAC,EAAE,CAAC,wBAAwB,EAAE,kBAAkB,CAAC,EAAE,CAAC,sBAAsB,EAAE,gBAAgB,CAAC,EAAE,CAAC,2BAA2B,EAAE,oBAAoB,CAAC,EAAE,CAAC,0BAA0B,EAAE,oBAAoB,CAAC,EAAE,CAAC,yBAAyB,EAAE,kBAAkB,CAAC,CAAC;EAC3nBA,KAAK,CAAChL,OAAO,CAACiL,IAAI,IAAI;IACpB,IAAI,CAACP,IAAI,EAAEQ,GAAG,CAAC,GAAGD,IAAI;IACtB,MAAMF,GAAG,GAAGD,MAAM,CAACL,aAAa,CAACC,IAAI,CAAC,CAAC;IACvC,IAAIK,GAAG,KAAKI,SAAS,IAAIJ,GAAG,KAAK,IAAI,EAAE;MACrCR,OAAO,CAACW,GAAG,CAAC,GAAGH,GAAG;IACpB;EACF,CAAC,CAAC;AACJ;AACA,MAAMK,QAAQ,GAAG;EACfC,YAAY,EAAE,OAAO;EACrBC,aAAa,EAAElG,CAAC;EAChBmG,SAAS,EAAEpD,kBAAkB;EAC7BqD,gBAAgB,EAAEpD,yBAAyB;EAC3CqD,cAAc,EAAE,IAAI;EACpBC,UAAU,EAAE,IAAI;EAChBC,QAAQ,EAAE,IAAI;EACdC,oBAAoB,EAAE,KAAK;EAC3BC,gBAAgB,EAAE,IAAI;EACtBC,cAAc,EAAE,OAAO;EACvBC,kBAAkB,EAAE,IAAI;EACxBC,kBAAkB,EAAE,KAAK;EACzBC,gBAAgB,EAAE;AACpB,CAAC;;AAED;AACA,IAAI1B,OAAO,CAAC2B,YAAY,EAAE;EACxB3B,OAAO,CAACgB,SAAS,GAAGhB,OAAO,CAAC2B,YAAY;AAC1C;AACA,MAAMC,OAAO,GAAGtM,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEuL,QAAQ,CAAC,EAAEb,OAAO,CAAC;AACrE,IAAI,CAAC4B,OAAO,CAACV,cAAc,EAAEU,OAAO,CAACN,gBAAgB,GAAG,KAAK;AAC7D,MAAMO,MAAM,GAAG,CAAC,CAAC;AACjB3N,MAAM,CAACa,IAAI,CAAC8L,QAAQ,CAAC,CAACpL,OAAO,CAACkL,GAAG,IAAI;EACnCzM,MAAM,CAACC,cAAc,CAAC0N,MAAM,EAAElB,GAAG,EAAE;IACjCtM,UAAU,EAAE,IAAI;IAChBsC,GAAG,EAAE,SAAAA,CAAU6J,GAAG,EAAE;MAClBoB,OAAO,CAACjB,GAAG,CAAC,GAAGH,GAAG;MAClBsB,WAAW,CAACrM,OAAO,CAACsM,EAAE,IAAIA,EAAE,CAACF,MAAM,CAAC,CAAC;IACvC,CAAC;IACDjL,GAAG,EAAE,SAAAA,CAAA,EAAY;MACf,OAAOgL,OAAO,CAACjB,GAAG,CAAC;IACrB;EACF,CAAC,CAAC;AACJ,CAAC,CAAC;;AAEF;AACAzM,MAAM,CAACC,cAAc,CAAC0N,MAAM,EAAE,cAAc,EAAE;EAC5CxN,UAAU,EAAE,IAAI;EAChBsC,GAAG,EAAE,SAAAA,CAAU6J,GAAG,EAAE;IAClBoB,OAAO,CAACZ,SAAS,GAAGR,GAAG;IACvBsB,WAAW,CAACrM,OAAO,CAACsM,EAAE,IAAIA,EAAE,CAACF,MAAM,CAAC,CAAC;EACvC,CAAC;EACDjL,GAAG,EAAE,SAAAA,CAAA,EAAY;IACf,OAAOgL,OAAO,CAACZ,SAAS;EAC1B;AACF,CAAC,CAAC;AACF5I,MAAM,CAAC6H,iBAAiB,GAAG4B,MAAM;AACjC,MAAMC,WAAW,GAAG,EAAE;AACtB,SAASE,QAAQA,CAACD,EAAE,EAAE;EACpBD,WAAW,CAAC1M,IAAI,CAAC2M,EAAE,CAAC;EACpB,OAAO,MAAM;IACXD,WAAW,CAACG,MAAM,CAACH,WAAW,CAAC/I,OAAO,CAACgJ,EAAE,CAAC,EAAE,CAAC,CAAC;EAChD,CAAC;AACH;AAEA,MAAMG,GAAG,GAAGvE,aAAa;AACzB,MAAMwE,oBAAoB,GAAG;EAC3BC,IAAI,EAAE,EAAE;EACRpF,CAAC,EAAE,CAAC;EACJqF,CAAC,EAAE,CAAC;EACJC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,KAAK;EACZC,KAAK,EAAE;AACT,CAAC;AACD,SAASC,SAASA,CAACC,GAAG,EAAE;EACtB,IAAI,CAACA,GAAG,IAAI,CAACjK,MAAM,EAAE;IACnB;EACF;EACA,MAAMkK,KAAK,GAAGtK,QAAQ,CAACQ,aAAa,CAAC,OAAO,CAAC;EAC7C8J,KAAK,CAACC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;EACtCD,KAAK,CAACE,SAAS,GAAGH,GAAG;EACrB,MAAMI,YAAY,GAAGzK,QAAQ,CAACM,IAAI,CAACoK,UAAU;EAC7C,IAAIC,WAAW,GAAG,IAAI;EACtB,KAAK,IAAI9M,CAAC,GAAG4M,YAAY,CAACtN,MAAM,GAAG,CAAC,EAAEU,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;IACjD,MAAM+M,KAAK,GAAGH,YAAY,CAAC5M,CAAC,CAAC;IAC7B,MAAMgN,OAAO,GAAG,CAACD,KAAK,CAACC,OAAO,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC;IACnD,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAACpK,OAAO,CAACmK,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MAC3CF,WAAW,GAAGC,KAAK;IACrB;EACF;EACA5K,QAAQ,CAACM,IAAI,CAACyK,YAAY,CAACT,KAAK,EAAEK,WAAW,CAAC;EAC9C,OAAON,GAAG;AACZ;AACA,MAAMW,MAAM,GAAG,gEAAgE;AAC/E,SAASC,YAAYA,CAAA,EAAG;EACtB,IAAIlB,IAAI,GAAG,EAAE;EACb,IAAImB,EAAE,GAAG,EAAE;EACX,OAAOnB,IAAI,EAAE,GAAG,CAAC,EAAE;IACjBmB,EAAE,IAAIF,MAAM,CAACG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EACtC;EACA,OAAOF,EAAE;AACX;AACA,SAASG,OAAOA,CAAC9E,GAAG,EAAE;EACpB,MAAM+E,KAAK,GAAG,EAAE;EAChB,KAAK,IAAIzN,CAAC,GAAG,CAAC0I,GAAG,IAAI,EAAE,EAAEpJ,MAAM,KAAK,CAAC,EAAEU,CAAC,EAAE,GAAG;IAC3CyN,KAAK,CAACzN,CAAC,CAAC,GAAG0I,GAAG,CAAC1I,CAAC,CAAC;EACnB;EACA,OAAOyN,KAAK;AACd;AACA,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,IAAIA,IAAI,CAACC,SAAS,EAAE;IAClB,OAAOJ,OAAO,CAACG,IAAI,CAACC,SAAS,CAAC;EAChC,CAAC,MAAM;IACL,OAAO,CAACD,IAAI,CAACvD,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAEyD,KAAK,CAAC,GAAG,CAAC,CAAC7O,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAAC;EACrE;AACF;AACA,SAAS8N,UAAUA,CAACC,GAAG,EAAE;EACvB,OAAO,EAAE,CAAC5G,MAAM,CAAC4G,GAAG,CAAC,CAAC/M,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;AACzI;AACA,SAASgN,cAAcA,CAACC,UAAU,EAAE;EAClC,OAAOjQ,MAAM,CAACa,IAAI,CAACoP,UAAU,IAAI,CAAC,CAAC,CAAC,CAACrN,MAAM,CAAC,CAACsN,GAAG,EAAEC,aAAa,KAAK;IAClE,OAAOD,GAAG,GAAG,EAAE,CAAC/G,MAAM,CAACgH,aAAa,EAAE,KAAK,CAAC,CAAChH,MAAM,CAAC2G,UAAU,CAACG,UAAU,CAACE,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC;EACnG,CAAC,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;AACf;AACA,SAASC,UAAUA,CAACC,MAAM,EAAE;EAC1B,OAAOtQ,MAAM,CAACa,IAAI,CAACyP,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC1N,MAAM,CAAC,CAACsN,GAAG,EAAEK,SAAS,KAAK;IAC1D,OAAOL,GAAG,GAAG,EAAE,CAAC/G,MAAM,CAACoH,SAAS,EAAE,IAAI,CAAC,CAACpH,MAAM,CAACmH,MAAM,CAACC,SAAS,CAAC,CAACH,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC;EAC/E,CAAC,EAAE,EAAE,CAAC;AACR;AACA,SAASI,qBAAqBA,CAACC,SAAS,EAAE;EACxC,OAAOA,SAAS,CAACvC,IAAI,KAAKD,oBAAoB,CAACC,IAAI,IAAIuC,SAAS,CAAC3H,CAAC,KAAKmF,oBAAoB,CAACnF,CAAC,IAAI2H,SAAS,CAACtC,CAAC,KAAKF,oBAAoB,CAACE,CAAC,IAAIsC,SAAS,CAACrC,MAAM,KAAKH,oBAAoB,CAACG,MAAM,IAAIqC,SAAS,CAACpC,KAAK,IAAIoC,SAAS,CAACnC,KAAK;AACnO;AACA,SAASoC,eAAeA,CAAClE,IAAI,EAAE;EAC7B,IAAI;IACFiE,SAAS;IACTE,cAAc;IACdC;EACF,CAAC,GAAGpE,IAAI;EACR,MAAMqE,KAAK,GAAG;IACZJ,SAAS,EAAE,YAAY,CAACtH,MAAM,CAACwH,cAAc,GAAG,CAAC,EAAE,OAAO;EAC5D,CAAC;EACD,MAAMG,cAAc,GAAG,YAAY,CAAC3H,MAAM,CAACsH,SAAS,CAAC3H,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,CAACK,MAAM,CAACsH,SAAS,CAACtC,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC;EACjG,MAAM4C,UAAU,GAAG,QAAQ,CAAC5H,MAAM,CAACsH,SAAS,CAACvC,IAAI,GAAG,EAAE,IAAIuC,SAAS,CAACpC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAClF,MAAM,CAACsH,SAAS,CAACvC,IAAI,GAAG,EAAE,IAAIuC,SAAS,CAACnC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;EACzJ,MAAM0C,WAAW,GAAG,SAAS,CAAC7H,MAAM,CAACsH,SAAS,CAACrC,MAAM,EAAE,OAAO,CAAC;EAC/D,MAAM6C,KAAK,GAAG;IACZR,SAAS,EAAE,EAAE,CAACtH,MAAM,CAAC2H,cAAc,EAAE,GAAG,CAAC,CAAC3H,MAAM,CAAC4H,UAAU,EAAE,GAAG,CAAC,CAAC5H,MAAM,CAAC6H,WAAW;EACtF,CAAC;EACD,MAAME,IAAI,GAAG;IACXT,SAAS,EAAE,YAAY,CAACtH,MAAM,CAACyH,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ;EAC7D,CAAC;EACD,OAAO;IACLC,KAAK;IACLI,KAAK;IACLC;EACF,CAAC;AACH;AACA,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,IAAI;IACFX,SAAS;IACTY,KAAK,GAAG5H,aAAa;IACrB6H,MAAM,GAAG7H,aAAa;IACtB8H,aAAa,GAAG;EAClB,CAAC,GAAGH,KAAK;EACT,IAAI9E,GAAG,GAAG,EAAE;EACZ,IAAIiF,aAAa,IAAI3M,KAAK,EAAE;IAC1B0H,GAAG,IAAI,YAAY,CAACnD,MAAM,CAACsH,SAAS,CAAC3H,CAAC,GAAGkF,GAAG,GAAGqD,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,CAAClI,MAAM,CAACsH,SAAS,CAACtC,CAAC,GAAGH,GAAG,GAAGsD,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC;EAClH,CAAC,MAAM,IAAIC,aAAa,EAAE;IACxBjF,GAAG,IAAI,wBAAwB,CAACnD,MAAM,CAACsH,SAAS,CAAC3H,CAAC,GAAGkF,GAAG,EAAE,mBAAmB,CAAC,CAAC7E,MAAM,CAACsH,SAAS,CAACtC,CAAC,GAAGH,GAAG,EAAE,OAAO,CAAC;EACnH,CAAC,MAAM;IACL1B,GAAG,IAAI,YAAY,CAACnD,MAAM,CAACsH,SAAS,CAAC3H,CAAC,GAAGkF,GAAG,EAAE,MAAM,CAAC,CAAC7E,MAAM,CAACsH,SAAS,CAACtC,CAAC,GAAGH,GAAG,EAAE,MAAM,CAAC;EACzF;EACA1B,GAAG,IAAI,QAAQ,CAACnD,MAAM,CAACsH,SAAS,CAACvC,IAAI,GAAGF,GAAG,IAAIyC,SAAS,CAACpC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAClF,MAAM,CAACsH,SAAS,CAACvC,IAAI,GAAGF,GAAG,IAAIyC,SAAS,CAACnC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;EAC/IhC,GAAG,IAAI,SAAS,CAACnD,MAAM,CAACsH,SAAS,CAACrC,MAAM,EAAE,OAAO,CAAC;EAClD,OAAO9B,GAAG;AACZ;AAEA,IAAIkF,UAAU,GAAG,ynZAAynZ;AAE1oZ,SAAShD,GAAGA,CAAA,EAAG;EACb,MAAMiD,GAAG,GAAG/H,kBAAkB;EAC9B,MAAMgI,GAAG,GAAG/H,yBAAyB;EACrC,MAAMgI,EAAE,GAAGhE,MAAM,CAACb,SAAS;EAC3B,MAAM8E,EAAE,GAAGjE,MAAM,CAACZ,gBAAgB;EAClC,IAAIpG,CAAC,GAAG6K,UAAU;EAClB,IAAIG,EAAE,KAAKF,GAAG,IAAIG,EAAE,KAAKF,GAAG,EAAE;IAC5B,MAAMG,KAAK,GAAG,IAAIvP,MAAM,CAAC,KAAK,CAAC6G,MAAM,CAACsI,GAAG,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC;IACvD,MAAMK,cAAc,GAAG,IAAIxP,MAAM,CAAC,MAAM,CAAC6G,MAAM,CAACsI,GAAG,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC;IACjE,MAAMM,KAAK,GAAG,IAAIzP,MAAM,CAAC,KAAK,CAAC6G,MAAM,CAACuI,GAAG,CAAC,EAAE,GAAG,CAAC;IAChD/K,CAAC,GAAGA,CAAC,CAAC3D,OAAO,CAAC6O,KAAK,EAAE,GAAG,CAAC1I,MAAM,CAACwI,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC3O,OAAO,CAAC8O,cAAc,EAAE,IAAI,CAAC3I,MAAM,CAACwI,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC3O,OAAO,CAAC+O,KAAK,EAAE,GAAG,CAAC5I,MAAM,CAACyI,EAAE,CAAC,CAAC;EACxH;EACA,OAAOjL,CAAC;AACV;AACA,IAAIqL,YAAY,GAAG,KAAK;AACxB,SAASC,SAASA,CAAA,EAAG;EACnB,IAAItE,MAAM,CAACV,UAAU,IAAI,CAAC+E,YAAY,EAAE;IACtCzD,SAAS,CAACC,GAAG,CAAC,CAAC,CAAC;IAChBwD,YAAY,GAAG,IAAI;EACrB;AACF;AACA,IAAIE,SAAS,GAAG;EACdC,MAAMA,CAAA,EAAG;IACP,OAAO;MACLC,GAAG,EAAE;QACH5D,GAAG;QACHD,SAAS,EAAE0D;MACb;IACF,CAAC;EACH,CAAC;EACDI,KAAKA,CAAA,EAAG;IACN,OAAO;MACLC,wBAAwBA,CAAA,EAAG;QACzBL,SAAS,CAAC,CAAC;MACb,CAAC;MACDM,WAAWA,CAAA,EAAG;QACZN,SAAS,CAAC,CAAC;MACb;IACF,CAAC;EACH;AACF,CAAC;AAED,MAAMO,CAAC,GAAGtO,MAAM,IAAI,CAAC,CAAC;AACtB,IAAI,CAACsO,CAAC,CAAChJ,oBAAoB,CAAC,EAAEgJ,CAAC,CAAChJ,oBAAoB,CAAC,GAAG,CAAC,CAAC;AAC1D,IAAI,CAACgJ,CAAC,CAAChJ,oBAAoB,CAAC,CAAC8G,MAAM,EAAEkC,CAAC,CAAChJ,oBAAoB,CAAC,CAAC8G,MAAM,GAAG,CAAC,CAAC;AACxE,IAAI,CAACkC,CAAC,CAAChJ,oBAAoB,CAAC,CAAC6I,KAAK,EAAEG,CAAC,CAAChJ,oBAAoB,CAAC,CAAC6I,KAAK,GAAG,CAAC,CAAC;AACtE,IAAI,CAACG,CAAC,CAAChJ,oBAAoB,CAAC,CAACiJ,KAAK,EAAED,CAAC,CAAChJ,oBAAoB,CAAC,CAACiJ,KAAK,GAAG,EAAE;AACtE,IAAIC,SAAS,GAAGF,CAAC,CAAChJ,oBAAoB,CAAC;AAEvC,MAAMmJ,SAAS,GAAG,EAAE;AACpB,MAAMC,QAAQ,GAAG,SAAAA,CAAA,EAAY;EAC3BzO,QAAQ,CAAC0O,mBAAmB,CAAC,kBAAkB,EAAED,QAAQ,CAAC;EAC1DE,MAAM,GAAG,CAAC;EACVH,SAAS,CAACtJ,GAAG,CAAC0J,EAAE,IAAIA,EAAE,CAAC,CAAC,CAAC;AAC3B,CAAC;AACD,IAAID,MAAM,GAAG,KAAK;AAClB,IAAIvO,MAAM,EAAE;EACVuO,MAAM,GAAG,CAAC3O,QAAQ,CAACK,eAAe,CAACwO,QAAQ,GAAG,YAAY,GAAG,eAAe,EAAEC,IAAI,CAAC9O,QAAQ,CAAC+O,UAAU,CAAC;EACvG,IAAI,CAACJ,MAAM,EAAE3O,QAAQ,CAACO,gBAAgB,CAAC,kBAAkB,EAAEkO,QAAQ,CAAC;AACtE;AACA,SAASO,QAAQA,CAAEJ,EAAE,EAAE;EACrB,IAAI,CAACxO,MAAM,EAAE;EACbuO,MAAM,GAAGM,UAAU,CAACL,EAAE,EAAE,CAAC,CAAC,GAAGJ,SAAS,CAACzR,IAAI,CAAC6R,EAAE,CAAC;AACjD;AAEA,SAASM,MAAMA,CAACC,aAAa,EAAE;EAC7B,MAAM;IACJC,GAAG;IACHtD,UAAU,GAAG,CAAC,CAAC;IACfuD,QAAQ,GAAG;EACb,CAAC,GAAGF,aAAa;EACjB,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;IACrC,OAAOxD,UAAU,CAACwD,aAAa,CAAC;EAClC,CAAC,MAAM;IACL,OAAO,GAAG,CAACnK,MAAM,CAACoK,GAAG,EAAE,GAAG,CAAC,CAACpK,MAAM,CAAC6G,cAAc,CAACC,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC9G,MAAM,CAACqK,QAAQ,CAACnK,GAAG,CAACgK,MAAM,CAAC,CAAClQ,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAACgG,MAAM,CAACoK,GAAG,EAAE,GAAG,CAAC;EAClI;AACF;AAEA,SAASE,eAAeA,CAACC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EAClD,IAAIF,OAAO,IAAIA,OAAO,CAACC,MAAM,CAAC,IAAID,OAAO,CAACC,MAAM,CAAC,CAACC,QAAQ,CAAC,EAAE;IAC3D,OAAO;MACLD,MAAM;MACNC,QAAQ;MACRC,IAAI,EAAEH,OAAO,CAACC,MAAM,CAAC,CAACC,QAAQ;IAChC,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,IAAI,EAAEC,WAAW,EAAE;EAC5D,OAAO,UAAU1K,CAAC,EAAE2K,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IAC3B,OAAOJ,IAAI,CAAC9R,IAAI,CAAC+R,WAAW,EAAE1K,CAAC,EAAE2K,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAC3C,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIvR,MAAM,GAAG,SAASwR,gBAAgBA,CAACC,OAAO,EAAEtB,EAAE,EAAEuB,YAAY,EAAEN,WAAW,EAAE;EAC7E,IAAInT,IAAI,GAAGb,MAAM,CAACa,IAAI,CAACwT,OAAO,CAAC;IAC7B/S,MAAM,GAAGT,IAAI,CAACS,MAAM;IACpBiT,QAAQ,GAAGP,WAAW,KAAKtH,SAAS,GAAGoH,aAAa,CAACf,EAAE,EAAEiB,WAAW,CAAC,GAAGjB,EAAE;IAC1E/Q,CAAC;IACDyK,GAAG;IACH+H,MAAM;EACR,IAAIF,YAAY,KAAK5H,SAAS,EAAE;IAC9B1K,CAAC,GAAG,CAAC;IACLwS,MAAM,GAAGH,OAAO,CAACxT,IAAI,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC,MAAM;IACLmB,CAAC,GAAG,CAAC;IACLwS,MAAM,GAAGF,YAAY;EACvB;EACA,OAAOtS,CAAC,GAAGV,MAAM,EAAEU,CAAC,EAAE,EAAE;IACtByK,GAAG,GAAG5L,IAAI,CAACmB,CAAC,CAAC;IACbwS,MAAM,GAAGD,QAAQ,CAACC,MAAM,EAAEH,OAAO,CAAC5H,GAAG,CAAC,EAAEA,GAAG,EAAE4H,OAAO,CAAC;EACvD;EACA,OAAOG,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,UAAUA,CAACC,MAAM,EAAE;EAC1B,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAIC,OAAO,GAAG,CAAC;EACf,MAAMtT,MAAM,GAAGoT,MAAM,CAACpT,MAAM;EAC5B,OAAOsT,OAAO,GAAGtT,MAAM,EAAE;IACvB,MAAMpB,KAAK,GAAGwU,MAAM,CAACG,UAAU,CAACD,OAAO,EAAE,CAAC;IAC1C,IAAI1U,KAAK,IAAI,MAAM,IAAIA,KAAK,IAAI,MAAM,IAAI0U,OAAO,GAAGtT,MAAM,EAAE;MAC1D,MAAMwT,KAAK,GAAGJ,MAAM,CAACG,UAAU,CAACD,OAAO,EAAE,CAAC;MAC1C,IAAI,CAACE,KAAK,GAAG,MAAM,KAAK,MAAM,EAAE;QAC9B;QACAH,MAAM,CAACzT,IAAI,CAAC,CAAC,CAAChB,KAAK,GAAG,KAAK,KAAK,EAAE,KAAK4U,KAAK,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC;MAClE,CAAC,MAAM;QACLH,MAAM,CAACzT,IAAI,CAAChB,KAAK,CAAC;QAClB0U,OAAO,EAAE;MACX;IACF,CAAC,MAAM;MACLD,MAAM,CAACzT,IAAI,CAAChB,KAAK,CAAC;IACpB;EACF;EACA,OAAOyU,MAAM;AACf;AACA,SAASI,KAAKA,CAACC,OAAO,EAAE;EACtB,MAAMC,OAAO,GAAGR,UAAU,CAACO,OAAO,CAAC;EACnC,OAAOC,OAAO,CAAC3T,MAAM,KAAK,CAAC,GAAG2T,OAAO,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI;AAC9D;AACA,SAASC,WAAWA,CAACT,MAAM,EAAEU,KAAK,EAAE;EAClC,MAAMlH,IAAI,GAAGwG,MAAM,CAACpT,MAAM;EAC1B,IAAI+T,KAAK,GAAGX,MAAM,CAACG,UAAU,CAACO,KAAK,CAAC;EACpC,IAAIE,MAAM;EACV,IAAID,KAAK,IAAI,MAAM,IAAIA,KAAK,IAAI,MAAM,IAAInH,IAAI,GAAGkH,KAAK,GAAG,CAAC,EAAE;IAC1DE,MAAM,GAAGZ,MAAM,CAACG,UAAU,CAACO,KAAK,GAAG,CAAC,CAAC;IACrC,IAAIE,MAAM,IAAI,MAAM,IAAIA,MAAM,IAAI,MAAM,EAAE;MACxC,OAAO,CAACD,KAAK,GAAG,MAAM,IAAI,KAAK,GAAGC,MAAM,GAAG,MAAM,GAAG,OAAO;IAC7D;EACF;EACA,OAAOD,KAAK;AACd;AAEA,SAASE,cAAcA,CAACC,KAAK,EAAE;EAC7B,OAAOxV,MAAM,CAACa,IAAI,CAAC2U,KAAK,CAAC,CAAC5S,MAAM,CAAC,CAACsN,GAAG,EAAE0D,QAAQ,KAAK;IAClD,MAAMC,IAAI,GAAG2B,KAAK,CAAC5B,QAAQ,CAAC;IAC5B,MAAM6B,QAAQ,GAAG,CAAC,CAAC5B,IAAI,CAACA,IAAI;IAC5B,IAAI4B,QAAQ,EAAE;MACZvF,GAAG,CAAC2D,IAAI,CAACD,QAAQ,CAAC,GAAGC,IAAI,CAACA,IAAI;IAChC,CAAC,MAAM;MACL3D,GAAG,CAAC0D,QAAQ,CAAC,GAAGC,IAAI;IACtB;IACA,OAAO3D,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AACA,SAASwF,WAAWA,CAAC/B,MAAM,EAAE6B,KAAK,EAAE;EAClC,IAAIG,MAAM,GAAGtU,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqL,SAAS,GAAGrL,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,MAAM;IACJuU,SAAS,GAAG;EACd,CAAC,GAAGD,MAAM;EACV,MAAME,UAAU,GAAGN,cAAc,CAACC,KAAK,CAAC;EACxC,IAAI,OAAO9C,SAAS,CAACL,KAAK,CAACyD,OAAO,KAAK,UAAU,IAAI,CAACF,SAAS,EAAE;IAC/DlD,SAAS,CAACL,KAAK,CAACyD,OAAO,CAACnC,MAAM,EAAE4B,cAAc,CAACC,KAAK,CAAC,CAAC;EACxD,CAAC,MAAM;IACL9C,SAAS,CAACpC,MAAM,CAACqD,MAAM,CAAC,GAAGvS,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEsR,SAAS,CAACpC,MAAM,CAACqD,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAEkC,UAAU,CAAC;EAC3G;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIlC,MAAM,KAAK,KAAK,EAAE;IACpB+B,WAAW,CAAC,IAAI,EAAEF,KAAK,CAAC;EAC1B;AACF;AAEA,MAAMO,aAAa,GAAG,CAAC,aAAa3T,WAAW,CAAC,oCAAoC,EAAE;EACpF4T,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE;AACN,CAAC,CAAC,EAAE,aAAa7T,WAAW,CAAC,sEAAsE,EAAE;EACnG8T,IAAI,EAAE,CAAC;EACPF,EAAE,EAAE,CAAC;EACLG,IAAI,EAAE,CAAC;EACPF,EAAE,EAAE;AACN,CAAC,CAAC,EAAE,aAAa7T,WAAW,CAAC,mCAAmC,EAAE;EAChE8T,IAAI,EAAE,CAAC;EACPF,EAAE,EAAE;AACN,CAAC,CAAC,CAAC;AAEH,MAAM;EACJ1F,MAAM;EACNmC;AACF,CAAC,GAAGC,SAAS;AACb,MAAM0D,YAAY,GAAGpW,MAAM,CAACa,IAAI,CAACsK,oBAAoB,CAAC;AACtD,MAAMkL,mBAAmB,GAAGD,YAAY,CAACxT,MAAM,CAAC,CAACsN,GAAG,EAAEoG,QAAQ,KAAK;EACjEpG,GAAG,CAACoG,QAAQ,CAAC,GAAGtW,MAAM,CAACa,IAAI,CAACsK,oBAAoB,CAACmL,QAAQ,CAAC,CAAC;EAC3D,OAAOpG,GAAG;AACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,IAAIqG,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,UAAU,GAAG,CAAC,CAAC;AACnB,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,UAAU,GAAG,CAAC,CAAC;AACnB,IAAIC,aAAa,GAAG,CAAC,CAAC;AACtB,IAAIC,QAAQ,GAAG,CAAC,CAAC;AACjB,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAO,CAACjL,gBAAgB,CAAChH,OAAO,CAACiS,IAAI,CAAC;AACxC;AACA,SAASC,WAAWA,CAACjK,SAAS,EAAEkK,GAAG,EAAE;EACnC,MAAMC,KAAK,GAAGD,GAAG,CAACnH,KAAK,CAAC,GAAG,CAAC;EAC5B,MAAM8D,MAAM,GAAGsD,KAAK,CAAC,CAAC,CAAC;EACvB,MAAMrD,QAAQ,GAAGqD,KAAK,CAAC7T,KAAK,CAAC,CAAC,CAAC,CAACD,IAAI,CAAC,GAAG,CAAC;EACzC,IAAIwQ,MAAM,KAAK7G,SAAS,IAAI8G,QAAQ,KAAK,EAAE,IAAI,CAACiD,UAAU,CAACjD,QAAQ,CAAC,EAAE;IACpE,OAAOA,QAAQ;EACjB,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF;AACA,MAAMsD,KAAK,GAAGA,CAAA,KAAM;EAClB,MAAMC,MAAM,GAAGC,OAAO,IAAI;IACxB,OAAOxU,MAAM,CAAC0N,MAAM,EAAE,CAAC+G,IAAI,EAAE5I,KAAK,EAAEkF,MAAM,KAAK;MAC7C0D,IAAI,CAAC1D,MAAM,CAAC,GAAG/Q,MAAM,CAAC6L,KAAK,EAAE2I,OAAO,EAAE,CAAC,CAAC,CAAC;MACzC,OAAOC,IAAI;IACb,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC;EACDb,UAAU,GAAGW,MAAM,CAAC,CAACjH,GAAG,EAAE2D,IAAI,EAAED,QAAQ,KAAK;IAC3C,IAAIC,IAAI,CAAC,CAAC,CAAC,EAAE;MACX3D,GAAG,CAAC2D,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGD,QAAQ;IACzB;IACA,IAAIC,IAAI,CAAC,CAAC,CAAC,EAAE;MACX,MAAMyD,OAAO,GAAGzD,IAAI,CAAC,CAAC,CAAC,CAAC7S,MAAM,CAACuW,IAAI,IAAI;QACrC,OAAO,OAAOA,IAAI,KAAK,QAAQ;MACjC,CAAC,CAAC;MACFD,OAAO,CAAC/V,OAAO,CAACiW,KAAK,IAAI;QACvBtH,GAAG,CAACsH,KAAK,CAACtC,QAAQ,CAAC,EAAE,CAAC,CAAC,GAAGtB,QAAQ;MACpC,CAAC,CAAC;IACJ;IACA,OAAO1D,GAAG;EACZ,CAAC,CAAC;EACFuG,WAAW,GAAGU,MAAM,CAAC,CAACjH,GAAG,EAAE2D,IAAI,EAAED,QAAQ,KAAK;IAC5C1D,GAAG,CAAC0D,QAAQ,CAAC,GAAGA,QAAQ;IACxB,IAAIC,IAAI,CAAC,CAAC,CAAC,EAAE;MACX,MAAMyD,OAAO,GAAGzD,IAAI,CAAC,CAAC,CAAC,CAAC7S,MAAM,CAACuW,IAAI,IAAI;QACrC,OAAO,OAAOA,IAAI,KAAK,QAAQ;MACjC,CAAC,CAAC;MACFD,OAAO,CAAC/V,OAAO,CAACiW,KAAK,IAAI;QACvBtH,GAAG,CAACsH,KAAK,CAAC,GAAG5D,QAAQ;MACvB,CAAC,CAAC;IACJ;IACA,OAAO1D,GAAG;EACZ,CAAC,CAAC;EACF0G,QAAQ,GAAGO,MAAM,CAAC,CAACjH,GAAG,EAAE2D,IAAI,EAAED,QAAQ,KAAK;IACzC,MAAM0D,OAAO,GAAGzD,IAAI,CAAC,CAAC,CAAC;IACvB3D,GAAG,CAAC0D,QAAQ,CAAC,GAAGA,QAAQ;IACxB0D,OAAO,CAAC/V,OAAO,CAACiW,KAAK,IAAI;MACvBtH,GAAG,CAACsH,KAAK,CAAC,GAAG5D,QAAQ;IACvB,CAAC,CAAC;IACF,OAAO1D,GAAG;EACZ,CAAC,CAAC;;EAEF;EACA;EACA,MAAMuH,UAAU,GAAG,KAAK,IAAInH,MAAM,IAAI3C,MAAM,CAAC+J,YAAY;EACzD,MAAMC,WAAW,GAAG/U,MAAM,CAAC6P,KAAK,EAAE,CAACvC,GAAG,EAAE0H,IAAI,KAAK;IAC/C,MAAMC,qBAAqB,GAAGD,IAAI,CAAC,CAAC,CAAC;IACrC,IAAIjE,MAAM,GAAGiE,IAAI,CAAC,CAAC,CAAC;IACpB,MAAMhE,QAAQ,GAAGgE,IAAI,CAAC,CAAC,CAAC;IACxB,IAAIjE,MAAM,KAAK,KAAK,IAAI,CAAC8D,UAAU,EAAE;MACnC9D,MAAM,GAAG,KAAK;IAChB;IACA,IAAI,OAAOkE,qBAAqB,KAAK,QAAQ,EAAE;MAC7C3H,GAAG,CAAC4H,KAAK,CAACD,qBAAqB,CAAC,GAAG;QACjClE,MAAM;QACNC;MACF,CAAC;IACH;IACA,IAAI,OAAOiE,qBAAqB,KAAK,QAAQ,EAAE;MAC7C3H,GAAG,CAAC6H,QAAQ,CAACF,qBAAqB,CAAC3C,QAAQ,CAAC,EAAE,CAAC,CAAC,GAAG;QACjDvB,MAAM;QACNC;MACF,CAAC;IACH;IACA,OAAO1D,GAAG;EACZ,CAAC,EAAE;IACD4H,KAAK,EAAE,CAAC,CAAC;IACTC,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC;EACFrB,UAAU,GAAGiB,WAAW,CAACG,KAAK;EAC9BnB,aAAa,GAAGgB,WAAW,CAACI,QAAQ;EACpCxB,oBAAoB,GAAGyB,kBAAkB,CAACrK,MAAM,CAACf,YAAY,EAAE;IAC7DqL,MAAM,EAAEtK,MAAM,CAACd;EACjB,CAAC,CAAC;AACJ,CAAC;AACDiB,QAAQ,CAACoK,IAAI,IAAI;EACf3B,oBAAoB,GAAGyB,kBAAkB,CAACE,IAAI,CAACtL,YAAY,EAAE;IAC3DqL,MAAM,EAAEtK,MAAM,CAACd;EACjB,CAAC,CAAC;AACJ,CAAC,CAAC;AACFqK,KAAK,CAAC,CAAC;AACP,SAASiB,SAASA,CAACxE,MAAM,EAAEqB,OAAO,EAAE;EAClC,OAAO,CAACwB,UAAU,CAAC7C,MAAM,CAAC,IAAI,CAAC,CAAC,EAAEqB,OAAO,CAAC;AAC5C;AACA,SAASoD,UAAUA,CAACzE,MAAM,EAAE0E,QAAQ,EAAE;EACpC,OAAO,CAAC5B,WAAW,CAAC9C,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE0E,QAAQ,CAAC;AAC9C;AACA,SAASC,OAAOA,CAAC3E,MAAM,EAAE6D,KAAK,EAAE;EAC9B,OAAO,CAACZ,QAAQ,CAACjD,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE6D,KAAK,CAAC;AACxC;AACA,SAASe,SAASA,CAACzB,IAAI,EAAE;EACvB,OAAOJ,UAAU,CAACI,IAAI,CAAC,IAAI;IACzBnD,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE;EACZ,CAAC;AACH;AACA,SAAS4E,YAAYA,CAACxD,OAAO,EAAE;EAC7B,MAAMyD,UAAU,GAAG9B,aAAa,CAAC3B,OAAO,CAAC;EACzC,MAAM0D,UAAU,GAAGP,SAAS,CAAC,KAAK,EAAEnD,OAAO,CAAC;EAC5C,OAAOyD,UAAU,KAAKC,UAAU,GAAG;IACjC/E,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE8E;EACZ,CAAC,GAAG,IAAI,CAAC,IAAI;IACX/E,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE;EACZ,CAAC;AACH;AACA,SAAS+E,sBAAsBA,CAAA,EAAG;EAChC,OAAOpC,oBAAoB;AAC7B;AACA,MAAMqC,kBAAkB,GAAGA,CAAA,KAAM;EAC/B,OAAO;IACLjF,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,IAAI;IACdiF,IAAI,EAAE;EACR,CAAC;AACH,CAAC;AACD,SAASC,WAAWA,CAACC,MAAM,EAAE;EAC3B,IAAId,MAAM,GAAGtR,CAAC;EACd,MAAMqS,QAAQ,GAAG5C,YAAY,CAACxT,MAAM,CAAC,CAACsN,GAAG,EAAEoG,QAAQ,KAAK;IACtDpG,GAAG,CAACoG,QAAQ,CAAC,GAAG,EAAE,CAACnN,MAAM,CAACwE,MAAM,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC3D,MAAM,CAACmN,QAAQ,CAAC;IACjE,OAAOpG,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACNtJ,CAAC,CAACrF,OAAO,CAAC+U,QAAQ,IAAI;IACpB,IAAIyC,MAAM,CAACE,QAAQ,CAACD,QAAQ,CAAC1C,QAAQ,CAAC,CAAC,IAAIyC,MAAM,CAACG,IAAI,CAACC,IAAI,IAAI9C,mBAAmB,CAACC,QAAQ,CAAC,CAAC2C,QAAQ,CAACE,IAAI,CAAC,CAAC,EAAE;MAC5GlB,MAAM,GAAG3B,QAAQ;IACnB;EACF,CAAC,CAAC;EACF,OAAO2B,MAAM;AACf;AACA,SAASD,kBAAkBA,CAACoB,aAAa,EAAE;EACzC,IAAIzD,MAAM,GAAGtU,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqL,SAAS,GAAGrL,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,MAAM;IACJ4W,MAAM,GAAGtR;EACX,CAAC,GAAGgP,MAAM;EACV,MAAMlH,KAAK,GAAG1D,eAAe,CAACkN,MAAM,CAAC,CAACmB,aAAa,CAAC;;EAEpD;EACA,IAAInB,MAAM,KAAKnY,CAAC,IAAI,CAACsZ,aAAa,EAAE;IAClC,OAAO,KAAK;EACd;EACA,MAAMzF,MAAM,GAAG1I,eAAe,CAACgN,MAAM,CAAC,CAACmB,aAAa,CAAC,IAAInO,eAAe,CAACgN,MAAM,CAAC,CAACxJ,KAAK,CAAC;EACvF,MAAM4K,OAAO,GAAGD,aAAa,IAAI1G,SAAS,CAACpC,MAAM,GAAG8I,aAAa,GAAG,IAAI;EACxE,MAAM5E,MAAM,GAAGb,MAAM,IAAI0F,OAAO,IAAI,IAAI;EACxC,OAAO7E,MAAM;AACf;AACA,SAAS8E,sBAAsBA,CAACC,UAAU,EAAE;EAC1C,IAAIV,IAAI,GAAG,EAAE;EACb,IAAIjF,QAAQ,GAAG,IAAI;EACnB2F,UAAU,CAAChY,OAAO,CAACyV,GAAG,IAAI;IACxB,MAAMxC,MAAM,GAAGuC,WAAW,CAACpJ,MAAM,CAACb,SAAS,EAAEkK,GAAG,CAAC;IACjD,IAAIxC,MAAM,EAAE;MACVZ,QAAQ,GAAGY,MAAM;IACnB,CAAC,MAAM,IAAIwC,GAAG,EAAE;MACd6B,IAAI,CAAC3X,IAAI,CAAC8V,GAAG,CAAC;IAChB;EACF,CAAC,CAAC;EACF,OAAO;IACLpD,QAAQ;IACRiF;EACF,CAAC;AACH;AACA,SAASW,kBAAkBA,CAACC,GAAG,EAAE;EAC/B,OAAOA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC1Y,MAAM,CAAC,CAACd,KAAK,EAAEkV,KAAK,EAAEqE,GAAG,KAAK;IAC9C,OAAOA,GAAG,CAAC5U,OAAO,CAAC3E,KAAK,CAAC,KAAKkV,KAAK;EACrC,CAAC,CAAC;AACJ;AACA,SAASuE,gBAAgBA,CAACZ,MAAM,EAAE;EAChC,IAAIpD,MAAM,GAAGtU,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqL,SAAS,GAAGrL,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,MAAM;IACJuY,WAAW,GAAG;EAChB,CAAC,GAAGjE,MAAM;EACV,IAAIkE,WAAW,GAAG,IAAI;EACtB,MAAMC,iBAAiB,GAAG/Q,EAAE,CAACI,MAAM,CAACV,IAAI,CAAC;EACzC,MAAMsR,sBAAsB,GAAGP,kBAAkB,CAACT,MAAM,CAAC/X,MAAM,CAACgW,GAAG,IAAI8C,iBAAiB,CAACb,QAAQ,CAACjC,GAAG,CAAC,CAAC,CAAC;EACxG,MAAMgD,uBAAuB,GAAGR,kBAAkB,CAACT,MAAM,CAAC/X,MAAM,CAACgW,GAAG,IAAI,CAACjO,EAAE,CAACkQ,QAAQ,CAACjC,GAAG,CAAC,CAAC,CAAC;EAC3F,MAAMiD,QAAQ,GAAGF,sBAAsB,CAAC/Y,MAAM,CAACgW,GAAG,IAAI;IACpD6C,WAAW,GAAG7C,GAAG;IACjB,OAAO,CAACtQ,CAAC,CAACuS,QAAQ,CAACjC,GAAG,CAAC;EACzB,CAAC,CAAC;EACF,MAAM,CAACkD,eAAe,GAAG,IAAI,CAAC,GAAGD,QAAQ;EACzC,MAAMhC,MAAM,GAAGa,WAAW,CAACiB,sBAAsB,CAAC;EAClD,MAAMI,SAAS,GAAG/Y,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkY,sBAAsB,CAACU,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACxGrG,MAAM,EAAEqE,kBAAkB,CAACkC,eAAe,EAAE;MAC1CjC;IACF,CAAC;EACH,CAAC,CAAC;EACF,OAAO7W,cAAc,CAACA,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE+Y,SAAS,CAAC,EAAEC,yBAAyB,CAAC;IAC5FrB,MAAM;IACNd,MAAM;IACN3H,MAAM;IACN3C,MAAM;IACNwM,SAAS;IACTN;EACF,CAAC,CAAC,CAAC,EAAEQ,iBAAiB,CAACT,WAAW,EAAEC,WAAW,EAAEM,SAAS,CAAC,CAAC;AAC9D;AACA,SAASE,iBAAiBA,CAACT,WAAW,EAAEC,WAAW,EAAEM,SAAS,EAAE;EAC9D,IAAI;IACFxG,MAAM;IACNC;EACF,CAAC,GAAGuG,SAAS;EACb,IAAIP,WAAW,IAAI,CAACjG,MAAM,IAAI,CAACC,QAAQ,EAAE;IACvC,OAAO;MACLD,MAAM;MACNC;IACF,CAAC;EACH;EACA,MAAMgE,IAAI,GAAGiC,WAAW,KAAK,IAAI,GAAGtB,SAAS,CAAC3E,QAAQ,CAAC,GAAG,CAAC,CAAC;EAC5D,MAAM0G,aAAa,GAAGhC,OAAO,CAAC3E,MAAM,EAAEC,QAAQ,CAAC;EAC/CA,QAAQ,GAAGgE,IAAI,CAAChE,QAAQ,IAAI0G,aAAa,IAAI1G,QAAQ;EACrDD,MAAM,GAAGiE,IAAI,CAACjE,MAAM,IAAIA,MAAM;EAC9B,IAAIA,MAAM,KAAK,KAAK,IAAI,CAACrD,MAAM,CAAC,KAAK,CAAC,IAAIA,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC3C,MAAM,CAAC+J,YAAY,EAAE;IAC/E;IACA;IACA/D,MAAM,GAAG,KAAK;EAChB;EACA,OAAO;IACLA,MAAM;IACNC;EACF,CAAC;AACH;AACA,MAAM2G,oBAAoB,GAAG3T,CAAC,CAAC5F,MAAM,CAACsV,QAAQ,IAAI;EAChD,OAAOA,QAAQ,KAAK3P,CAAC,IAAI2P,QAAQ,KAAKxW,CAAC;AACzC,CAAC,CAAC;AACF,MAAM0a,kBAAkB,GAAGxa,MAAM,CAACa,IAAI,CAACgI,EAAE,CAAC,CAAC7H,MAAM,CAACyL,GAAG,IAAIA,GAAG,KAAK9F,CAAC,CAAC,CAAC0C,GAAG,CAACoD,GAAG,IAAIzM,MAAM,CAACa,IAAI,CAACgI,EAAE,CAAC4D,GAAG,CAAC,CAAC,CAAC,CAACgO,IAAI,CAAC,CAAC;AAC3G,SAASL,yBAAyBA,CAACM,aAAa,EAAE;EAChD,MAAM;IACJ3B,MAAM;IACNd,MAAM;IACNkC,SAAS;IACTN,WAAW,GAAG,EAAE;IAChBvJ,MAAM,GAAG,CAAC,CAAC;IACX3C,MAAM,EAAEgN,SAAS,GAAG,CAAC;EACvB,CAAC,GAAGD,aAAa;EACjB,MAAME,eAAe,GAAG3C,MAAM,KAAKnY,CAAC;EACpC,MAAM+a,gBAAgB,GAAG9B,MAAM,CAACE,QAAQ,CAAC,YAAY,CAAC,IAAIF,MAAM,CAACE,QAAQ,CAAC,KAAK,CAAC;EAChF,MAAM6B,sBAAsB,GAAGH,SAAS,CAAC9N,aAAa,KAAK,SAAS;EACpE,MAAMkO,wBAAwB,GAAGZ,SAAS,CAACxG,MAAM,KAAK,KAAK,IAAIwG,SAAS,CAACxG,MAAM,KAAK,YAAY;EAChG,IAAI,CAACiH,eAAe,KAAKC,gBAAgB,IAAIC,sBAAsB,IAAIC,wBAAwB,CAAC,EAAE;IAChGZ,SAAS,CAACxG,MAAM,GAAG,KAAK;EAC1B;EACA,IAAIoF,MAAM,CAACE,QAAQ,CAAC,WAAW,CAAC,IAAIF,MAAM,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAE;IAC1DkB,SAAS,CAACxG,MAAM,GAAG,KAAK;EAC1B;EACA,IAAI,CAACwG,SAAS,CAACxG,MAAM,IAAI4G,oBAAoB,CAACtB,QAAQ,CAAChB,MAAM,CAAC,EAAE;IAC9D,MAAM+C,WAAW,GAAGhb,MAAM,CAACa,IAAI,CAACyP,MAAM,CAAC,CAAC2K,IAAI,CAACxO,GAAG,IAAI+N,kBAAkB,CAACvB,QAAQ,CAACxM,GAAG,CAAC,CAAC;IACrF,IAAIuO,WAAW,IAAIL,SAAS,CAACjD,YAAY,EAAE;MACzC,MAAMwD,aAAa,GAAGlU,EAAE,CAACtE,GAAG,CAACuV,MAAM,CAAC,CAAC/Q,oBAAoB;MACzDiT,SAAS,CAACxG,MAAM,GAAGuH,aAAa;MAChCf,SAAS,CAACvG,QAAQ,GAAG0E,OAAO,CAAC6B,SAAS,CAACxG,MAAM,EAAEwG,SAAS,CAACvG,QAAQ,CAAC,IAAIuG,SAAS,CAACvG,QAAQ;IAC1F;EACF;EACA,IAAIuG,SAAS,CAACxG,MAAM,KAAK,IAAI,IAAIkG,WAAW,KAAK,IAAI,EAAE;IACrD;IACA;IACAM,SAAS,CAACxG,MAAM,GAAGgF,sBAAsB,CAAC,CAAC,IAAI,KAAK;EACtD;EACA,OAAOwB,SAAS;AAClB;AAEA,MAAMgB,OAAO,CAAC;EACZza,WAAWA,CAAA,EAAG;IACZ,IAAI,CAAC0a,WAAW,GAAG,CAAC,CAAC;EACvB;EACAC,GAAGA,CAAA,EAAG;IACJ,KAAK,IAAIC,IAAI,GAAGja,SAAS,CAACC,MAAM,EAAE8Z,WAAW,GAAG,IAAInY,KAAK,CAACqY,IAAI,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGD,IAAI,EAAEC,IAAI,EAAE,EAAE;MAC9FH,WAAW,CAACG,IAAI,CAAC,GAAGla,SAAS,CAACka,IAAI,CAAC;IACrC;IACA,MAAMC,SAAS,GAAGJ,WAAW,CAACxY,MAAM,CAAC,IAAI,CAAC6Y,gBAAgB,EAAE,CAAC,CAAC,CAAC;IAC/Dzb,MAAM,CAACa,IAAI,CAAC2a,SAAS,CAAC,CAACja,OAAO,CAACkL,GAAG,IAAI;MACpC,IAAI,CAAC2O,WAAW,CAAC3O,GAAG,CAAC,GAAGrL,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,CAACga,WAAW,CAAC3O,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE+O,SAAS,CAAC/O,GAAG,CAAC,CAAC;MACvGiJ,WAAW,CAACjJ,GAAG,EAAE+O,SAAS,CAAC/O,GAAG,CAAC,CAAC;;MAEhC;MACA,MAAMiP,UAAU,GAAGvQ,oBAAoB,CAACxE,CAAC,CAAC,CAAC8F,GAAG,CAAC;MAC/C,IAAIiP,UAAU,EAAEhG,WAAW,CAACgG,UAAU,EAAEF,SAAS,CAAC/O,GAAG,CAAC,CAAC;MACvDyK,KAAK,CAAC,CAAC;IACT,CAAC,CAAC;EACJ;EACAyE,KAAKA,CAAA,EAAG;IACN,IAAI,CAACP,WAAW,GAAG,CAAC,CAAC;EACvB;EACAK,gBAAgBA,CAACD,SAAS,EAAEI,UAAU,EAAE;IACtC,MAAM/F,UAAU,GAAG+F,UAAU,CAACjI,MAAM,IAAIiI,UAAU,CAAChI,QAAQ,IAAIgI,UAAU,CAAC/H,IAAI,GAAG;MAC/E,CAAC,EAAE+H;IACL,CAAC,GAAGA,UAAU;IACd5b,MAAM,CAACa,IAAI,CAACgV,UAAU,CAAC,CAACxM,GAAG,CAACoD,GAAG,IAAI;MACjC,MAAM;QACJkH,MAAM;QACNC,QAAQ;QACRC;MACF,CAAC,GAAGgC,UAAU,CAACpJ,GAAG,CAAC;MACnB,MAAM6K,OAAO,GAAGzD,IAAI,CAAC,CAAC,CAAC;MACvB,IAAI,CAAC2H,SAAS,CAAC7H,MAAM,CAAC,EAAE6H,SAAS,CAAC7H,MAAM,CAAC,GAAG,CAAC,CAAC;MAC9C,IAAI2D,OAAO,CAAChW,MAAM,GAAG,CAAC,EAAE;QACtBgW,OAAO,CAAC/V,OAAO,CAACiW,KAAK,IAAI;UACvB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;YAC7BgE,SAAS,CAAC7H,MAAM,CAAC,CAAC6D,KAAK,CAAC,GAAG3D,IAAI;UACjC;QACF,CAAC,CAAC;MACJ;MACA2H,SAAS,CAAC7H,MAAM,CAAC,CAACC,QAAQ,CAAC,GAAGC,IAAI;IACpC,CAAC,CAAC;IACF,OAAO2H,SAAS;EAClB;AACF;AAEA,IAAIK,QAAQ,GAAG,EAAE;AACjB,IAAIC,MAAM,GAAG,CAAC,CAAC;AACf,MAAMC,SAAS,GAAG,CAAC,CAAC;AACpB,MAAMC,mBAAmB,GAAGhc,MAAM,CAACa,IAAI,CAACkb,SAAS,CAAC;AAClD,SAASE,eAAeA,CAACC,WAAW,EAAE1P,IAAI,EAAE;EAC1C,IAAI;IACF2P,SAAS,EAAEzR;EACb,CAAC,GAAG8B,IAAI;EACRqP,QAAQ,GAAGK,WAAW;EACtBJ,MAAM,GAAG,CAAC,CAAC;EACX9b,MAAM,CAACa,IAAI,CAACkb,SAAS,CAAC,CAACxa,OAAO,CAAC6a,CAAC,IAAI;IAClC,IAAIJ,mBAAmB,CAACnX,OAAO,CAACuX,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MACzC,OAAOL,SAAS,CAACK,CAAC,CAAC;IACrB;EACF,CAAC,CAAC;EACFP,QAAQ,CAACta,OAAO,CAAC8a,MAAM,IAAI;IACzB,MAAMlK,MAAM,GAAGkK,MAAM,CAAClK,MAAM,GAAGkK,MAAM,CAAClK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACnDnS,MAAM,CAACa,IAAI,CAACsR,MAAM,CAAC,CAAC5Q,OAAO,CAAC+a,EAAE,IAAI;MAChC,IAAI,OAAOnK,MAAM,CAACmK,EAAE,CAAC,KAAK,UAAU,EAAE;QACpC5R,GAAG,CAAC4R,EAAE,CAAC,GAAGnK,MAAM,CAACmK,EAAE,CAAC;MACtB;MACA,IAAI,OAAOnK,MAAM,CAACmK,EAAE,CAAC,KAAK,QAAQ,EAAE;QAClCtc,MAAM,CAACa,IAAI,CAACsR,MAAM,CAACmK,EAAE,CAAC,CAAC,CAAC/a,OAAO,CAACgb,EAAE,IAAI;UACpC,IAAI,CAAC7R,GAAG,CAAC4R,EAAE,CAAC,EAAE;YACZ5R,GAAG,CAAC4R,EAAE,CAAC,GAAG,CAAC,CAAC;UACd;UACA5R,GAAG,CAAC4R,EAAE,CAAC,CAACC,EAAE,CAAC,GAAGpK,MAAM,CAACmK,EAAE,CAAC,CAACC,EAAE,CAAC;QAC9B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,IAAIF,MAAM,CAAChK,KAAK,EAAE;MAChB,MAAMA,KAAK,GAAGgK,MAAM,CAAChK,KAAK,CAAC,CAAC;MAC5BrS,MAAM,CAACa,IAAI,CAACwR,KAAK,CAAC,CAAC9Q,OAAO,CAACib,IAAI,IAAI;QACjC,IAAI,CAACV,MAAM,CAACU,IAAI,CAAC,EAAE;UACjBV,MAAM,CAACU,IAAI,CAAC,GAAG,EAAE;QACnB;QACAV,MAAM,CAACU,IAAI,CAAC,CAACtb,IAAI,CAACmR,KAAK,CAACmK,IAAI,CAAC,CAAC;MAChC,CAAC,CAAC;IACJ;IACA,IAAIH,MAAM,CAACI,QAAQ,EAAE;MACnBJ,MAAM,CAACI,QAAQ,CAACV,SAAS,CAAC;IAC5B;EACF,CAAC,CAAC;EACF,OAAOrR,GAAG;AACZ;AACA,SAASgS,UAAUA,CAACF,IAAI,EAAEG,WAAW,EAAE;EACrC,KAAK,IAAIrB,IAAI,GAAGja,SAAS,CAACC,MAAM,EAAEsb,IAAI,GAAG,IAAI3Z,KAAK,CAACqY,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGD,IAAI,EAAEC,IAAI,EAAE,EAAE;IAC1GqB,IAAI,CAACrB,IAAI,GAAG,CAAC,CAAC,GAAGla,SAAS,CAACka,IAAI,CAAC;EAClC;EACA,MAAMsB,OAAO,GAAGf,MAAM,CAACU,IAAI,CAAC,IAAI,EAAE;EAClCK,OAAO,CAACtb,OAAO,CAACub,MAAM,IAAI;IACxBH,WAAW,GAAGG,MAAM,CAAC3b,KAAK,CAAC,IAAI,EAAE,CAACwb,WAAW,EAAE,GAAGC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC5D,CAAC,CAAC;EACF,OAAOD,WAAW;AACpB;AACA,SAASI,SAASA,CAACP,IAAI,EAAE;EACvB,KAAK,IAAIQ,KAAK,GAAG3b,SAAS,CAACC,MAAM,EAAEsb,IAAI,GAAG,IAAI3Z,KAAK,CAAC+Z,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;IACjHL,IAAI,CAACK,KAAK,GAAG,CAAC,CAAC,GAAG5b,SAAS,CAAC4b,KAAK,CAAC;EACpC;EACA,MAAMJ,OAAO,GAAGf,MAAM,CAACU,IAAI,CAAC,IAAI,EAAE;EAClCK,OAAO,CAACtb,OAAO,CAACub,MAAM,IAAI;IACxBA,MAAM,CAAC3b,KAAK,CAAC,IAAI,EAAEyb,IAAI,CAAC;EAC1B,CAAC,CAAC;EACF,OAAOlQ,SAAS;AAClB;AACA,SAASwQ,YAAYA,CAAA,EAAG;EACtB,MAAMV,IAAI,GAAGnb,SAAS,CAAC,CAAC,CAAC;EACzB,MAAMub,IAAI,GAAG3Z,KAAK,CAACzC,SAAS,CAAC4C,KAAK,CAACnB,IAAI,CAACZ,SAAS,EAAE,CAAC,CAAC;EACrD,OAAO0a,SAAS,CAACS,IAAI,CAAC,GAAGT,SAAS,CAACS,IAAI,CAAC,CAACrb,KAAK,CAAC,IAAI,EAAEyb,IAAI,CAAC,GAAGlQ,SAAS;AACxE;AAEA,SAASyQ,kBAAkBA,CAACC,UAAU,EAAE;EACtC,IAAIA,UAAU,CAACzJ,MAAM,KAAK,IAAI,EAAE;IAC9ByJ,UAAU,CAACzJ,MAAM,GAAG,KAAK;EAC3B;EACA,IAAI;IACFC;EACF,CAAC,GAAGwJ,UAAU;EACd,MAAMzJ,MAAM,GAAGyJ,UAAU,CAACzJ,MAAM,IAAIgF,sBAAsB,CAAC,CAAC;EAC5D,IAAI,CAAC/E,QAAQ,EAAE;EACfA,QAAQ,GAAG0E,OAAO,CAAC3E,MAAM,EAAEC,QAAQ,CAAC,IAAIA,QAAQ;EAChD,OAAOH,eAAe,CAAC4J,OAAO,CAACjC,WAAW,EAAEzH,MAAM,EAAEC,QAAQ,CAAC,IAAIH,eAAe,CAACf,SAAS,CAACpC,MAAM,EAAEqD,MAAM,EAAEC,QAAQ,CAAC;AACtH;AACA,MAAMyJ,OAAO,GAAG,IAAIlC,OAAO,CAAC,CAAC;AAC7B,MAAMmC,MAAM,GAAGA,CAAA,KAAM;EACnB3P,MAAM,CAACX,cAAc,GAAG,KAAK;EAC7BW,MAAM,CAACP,gBAAgB,GAAG,KAAK;EAC/B2P,SAAS,CAAC,QAAQ,CAAC;AACrB,CAAC;AACD,MAAM3K,GAAG,GAAG;EACVmL,KAAK,EAAE,SAAAA,CAAA,EAAY;IACjB,IAAI5H,MAAM,GAAGtU,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqL,SAAS,GAAGrL,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACnF,IAAIkD,MAAM,EAAE;MACVwY,SAAS,CAAC,aAAa,EAAEpH,MAAM,CAAC;MAChCuH,YAAY,CAAC,oBAAoB,EAAEvH,MAAM,CAAC;MAC1C,OAAOuH,YAAY,CAAC,OAAO,EAAEvH,MAAM,CAAC;IACtC,CAAC,MAAM;MACL,OAAO6H,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,wCAAwC,CAAC,CAAC;IAC5E;EACF,CAAC;EACDC,KAAK,EAAE,SAAAA,CAAA,EAAY;IACjB,IAAIhI,MAAM,GAAGtU,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqL,SAAS,GAAGrL,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACnF,MAAM;MACJuc;IACF,CAAC,GAAGjI,MAAM;IACV,IAAIhI,MAAM,CAACX,cAAc,KAAK,KAAK,EAAE;MACnCW,MAAM,CAACX,cAAc,GAAG,IAAI;IAC9B;IACAW,MAAM,CAACP,gBAAgB,GAAG,IAAI;IAC9B+F,QAAQ,CAAC,MAAM;MACb0K,WAAW,CAAC;QACVD;MACF,CAAC,CAAC;MACFb,SAAS,CAAC,OAAO,EAAEpH,MAAM,CAAC;IAC5B,CAAC,CAAC;EACJ;AACF,CAAC;AACD,MAAMmI,KAAK,GAAG;EACZjK,IAAI,EAAEA,IAAI,IAAI;IACZ,IAAIA,IAAI,KAAK,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACF,MAAM,IAAIE,IAAI,CAACD,QAAQ,EAAE;MAC5D,OAAO;QACLD,MAAM,EAAEE,IAAI,CAACF,MAAM;QACnBC,QAAQ,EAAE0E,OAAO,CAACzE,IAAI,CAACF,MAAM,EAAEE,IAAI,CAACD,QAAQ,CAAC,IAAIC,IAAI,CAACD;MACxD,CAAC;IACH;IACA,IAAI3Q,KAAK,CAACC,OAAO,CAAC2Q,IAAI,CAAC,IAAIA,IAAI,CAACvS,MAAM,KAAK,CAAC,EAAE;MAC5C,MAAMsS,QAAQ,GAAGC,IAAI,CAAC,CAAC,CAAC,CAAChP,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAGgP,IAAI,CAAC,CAAC,CAAC,CAACzQ,KAAK,CAAC,CAAC,CAAC,GAAGyQ,IAAI,CAAC,CAAC,CAAC;MAC1E,MAAMF,MAAM,GAAGqE,kBAAkB,CAACnE,IAAI,CAAC,CAAC,CAAC,CAAC;MAC1C,OAAO;QACLF,MAAM;QACNC,QAAQ,EAAE0E,OAAO,CAAC3E,MAAM,EAAEC,QAAQ,CAAC,IAAIA;MACzC,CAAC;IACH;IACA,IAAI,OAAOC,IAAI,KAAK,QAAQ,KAAKA,IAAI,CAAChP,OAAO,CAAC,EAAE,CAACsE,MAAM,CAACwE,MAAM,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI+G,IAAI,CAACkK,KAAK,CAACzS,6BAA6B,CAAC,CAAC,EAAE;MAClI,MAAM0S,aAAa,GAAGrE,gBAAgB,CAAC9F,IAAI,CAAChE,KAAK,CAAC,GAAG,CAAC,EAAE;QACtD+J,WAAW,EAAE;MACf,CAAC,CAAC;MACF,OAAO;QACLjG,MAAM,EAAEqK,aAAa,CAACrK,MAAM,IAAIgF,sBAAsB,CAAC,CAAC;QACxD/E,QAAQ,EAAE0E,OAAO,CAAC0F,aAAa,CAACrK,MAAM,EAAEqK,aAAa,CAACpK,QAAQ,CAAC,IAAIoK,aAAa,CAACpK;MACnF,CAAC;IACH;IACA,IAAI,OAAOC,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAMF,MAAM,GAAGgF,sBAAsB,CAAC,CAAC;MACvC,OAAO;QACLhF,MAAM;QACNC,QAAQ,EAAE0E,OAAO,CAAC3E,MAAM,EAAEE,IAAI,CAAC,IAAIA;MACrC,CAAC;IACH;EACF;AACF,CAAC;AACD,MAAMoK,GAAG,GAAG;EACVX,MAAM;EACN3P,MAAM;EACNyE,GAAG;EACH0L,KAAK;EACLT,OAAO;EACPF,kBAAkB;EAClB9J;AACF,CAAC;AACD,MAAMwK,WAAW,GAAG,SAAAA,CAAA,EAAY;EAC9B,IAAIlI,MAAM,GAAGtU,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqL,SAAS,GAAGrL,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,MAAM;IACJuc,kBAAkB,GAAGzZ;EACvB,CAAC,GAAGwR,MAAM;EACV,IAAI,CAAC3V,MAAM,CAACa,IAAI,CAAC6R,SAAS,CAACpC,MAAM,CAAC,CAAChP,MAAM,GAAG,CAAC,IAAIqM,MAAM,CAAC+J,YAAY,KAAKnT,MAAM,IAAIoJ,MAAM,CAACX,cAAc,EAAEiR,GAAG,CAAC7L,GAAG,CAACmL,KAAK,CAAC;IACtH5N,IAAI,EAAEiO;EACR,CAAC,CAAC;AACJ,CAAC;AAED,SAASM,WAAWA,CAAC5R,GAAG,EAAE6R,eAAe,EAAE;EACzCne,MAAM,CAACC,cAAc,CAACqM,GAAG,EAAE,UAAU,EAAE;IACrC5J,GAAG,EAAEyb;EACP,CAAC,CAAC;EACFne,MAAM,CAACC,cAAc,CAACqM,GAAG,EAAE,MAAM,EAAE;IACjC5J,GAAG,EAAE,SAAAA,CAAA,EAAY;MACf,OAAO4J,GAAG,CAAC8R,QAAQ,CAAC/U,GAAG,CAACC,CAAC,IAAI+J,MAAM,CAAC/J,CAAC,CAAC,CAAC;IACzC;EACF,CAAC,CAAC;EACFtJ,MAAM,CAACC,cAAc,CAACqM,GAAG,EAAE,MAAM,EAAE;IACjC5J,GAAG,EAAE,SAAAA,CAAA,EAAY;MACf,IAAI,CAAC6B,MAAM,EAAE;MACb,MAAM8Z,SAAS,GAAGla,QAAQ,CAACQ,aAAa,CAAC,KAAK,CAAC;MAC/C0Z,SAAS,CAAC1P,SAAS,GAAGrC,GAAG,CAACgS,IAAI;MAC9B,OAAOD,SAAS,CAAC7K,QAAQ;IAC3B;EACF,CAAC,CAAC;EACF,OAAOlH,GAAG;AACZ;AAEA,SAASiS,MAAMA,CAAE/R,IAAI,EAAE;EACrB,IAAI;IACFgH,QAAQ;IACRgL,IAAI;IACJC,IAAI;IACJxO,UAAU;IACVK,MAAM;IACNG;EACF,CAAC,GAAGjE,IAAI;EACR,IAAIgE,qBAAqB,CAACC,SAAS,CAAC,IAAI+N,IAAI,CAACE,KAAK,IAAI,CAACD,IAAI,CAACC,KAAK,EAAE;IACjE,MAAM;MACJrN,KAAK;MACLC;IACF,CAAC,GAAGkN,IAAI;IACR,MAAMG,MAAM,GAAG;MACb7V,CAAC,EAAEuI,KAAK,GAAGC,MAAM,GAAG,CAAC;MACrBnD,CAAC,EAAE;IACL,CAAC;IACD8B,UAAU,CAAC,OAAO,CAAC,GAAGI,UAAU,CAACjP,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkP,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;MAC9E,kBAAkB,EAAE,EAAE,CAACnH,MAAM,CAACwV,MAAM,CAAC7V,CAAC,GAAG2H,SAAS,CAAC3H,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,CAACK,MAAM,CAACwV,MAAM,CAACxQ,CAAC,GAAGsC,SAAS,CAACtC,CAAC,GAAG,EAAE,EAAE,IAAI;IAC5G,CAAC,CAAC,CAAC;EACL;EACA,OAAO,CAAC;IACNoF,GAAG,EAAE,KAAK;IACVtD,UAAU;IACVuD;EACF,CAAC,CAAC;AACJ;AAEA,SAASoL,QAAQA,CAAEpS,IAAI,EAAE;EACvB,IAAI;IACFmH,MAAM;IACNC,QAAQ;IACRJ,QAAQ;IACRvD,UAAU;IACV4O;EACF,CAAC,GAAGrS,IAAI;EACR,MAAM6C,EAAE,GAAGwP,MAAM,KAAK,IAAI,GAAG,EAAE,CAAC1V,MAAM,CAACwK,MAAM,EAAE,GAAG,CAAC,CAACxK,MAAM,CAACwE,MAAM,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC3D,MAAM,CAACyK,QAAQ,CAAC,GAAGiL,MAAM;EAC3G,OAAO,CAAC;IACNtL,GAAG,EAAE,KAAK;IACVtD,UAAU,EAAE;MACVxB,KAAK,EAAE;IACT,CAAC;IACD+E,QAAQ,EAAE,CAAC;MACTD,GAAG,EAAE,QAAQ;MACbtD,UAAU,EAAE7O,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6O,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;QAC7DZ;MACF,CAAC,CAAC;MACFmE;IACF,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,SAASsL,qBAAqBA,CAACnJ,MAAM,EAAE;EACrC,MAAM;IACJH,KAAK,EAAE;MACLgJ,IAAI;MACJC;IACF,CAAC;IACD9K,MAAM;IACNC,QAAQ;IACRnD,SAAS;IACToO,MAAM;IACNE,KAAK;IACLC,MAAM;IACNC,OAAO;IACPnK,KAAK;IACLoK,SAAS,GAAG;EACd,CAAC,GAAGvJ,MAAM;EACV,MAAM;IACJtE,KAAK;IACLC;EACF,CAAC,GAAGmN,IAAI,CAACC,KAAK,GAAGD,IAAI,GAAGD,IAAI;EAC5B,MAAMW,cAAc,GAAG/W,EAAE,CAAC6Q,QAAQ,CAACtF,MAAM,CAAC;EAC1C,MAAMyL,SAAS,GAAG,CAACzR,MAAM,CAACZ,gBAAgB,EAAE6G,QAAQ,GAAG,EAAE,CAACzK,MAAM,CAACwE,MAAM,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC3D,MAAM,CAACyK,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC5S,MAAM,CAACkX,IAAI,IAAIpD,KAAK,CAACuK,OAAO,CAACxa,OAAO,CAACqT,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAClX,MAAM,CAACkX,IAAI,IAAIA,IAAI,KAAK,EAAE,IAAI,CAAC,CAACA,IAAI,CAAC,CAAC/O,MAAM,CAAC2L,KAAK,CAACuK,OAAO,CAAC,CAAClc,IAAI,CAAC,GAAG,CAAC;EACvO,IAAImc,OAAO,GAAG;IACZ9L,QAAQ,EAAE,EAAE;IACZvD,UAAU,EAAE7O,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE0T,KAAK,CAAC7E,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;MACnE,aAAa,EAAE0D,MAAM;MACrB,WAAW,EAAEC,QAAQ;MACrB,OAAO,EAAEwL,SAAS;MAClB,MAAM,EAAEtK,KAAK,CAAC7E,UAAU,CAACsP,IAAI,IAAI,KAAK;MACtC,OAAO,EAAE,4BAA4B;MACrC,SAAS,EAAE,MAAM,CAACpW,MAAM,CAACkI,KAAK,EAAE,GAAG,CAAC,CAAClI,MAAM,CAACmI,MAAM;IACpD,CAAC;EACH,CAAC;EACD,MAAMkO,sBAAsB,GAAGL,cAAc,IAAI,CAAC,CAACrK,KAAK,CAACuK,OAAO,CAACxa,OAAO,CAAC,OAAO,CAAC,GAAG;IAClFwM,KAAK,EAAE,EAAE,CAAClI,MAAM,CAACkI,KAAK,GAAGC,MAAM,GAAG,EAAE,GAAG,MAAM,EAAE,IAAI;EACrD,CAAC,GAAG,CAAC,CAAC;EACN,IAAI4N,SAAS,EAAE;IACbI,OAAO,CAACrP,UAAU,CAACrG,aAAa,CAAC,GAAG,EAAE;EACxC;EACA,IAAImV,KAAK,EAAE;IACTO,OAAO,CAAC9L,QAAQ,CAACtS,IAAI,CAAC;MACpBqS,GAAG,EAAE,OAAO;MACZtD,UAAU,EAAE;QACVZ,EAAE,EAAEiQ,OAAO,CAACrP,UAAU,CAAC,iBAAiB,CAAC,IAAI,QAAQ,CAAC9G,MAAM,CAAC8V,OAAO,IAAI7P,YAAY,CAAC,CAAC;MACxF,CAAC;MACDoE,QAAQ,EAAE,CAACuL,KAAK;IAClB,CAAC,CAAC;IACF,OAAOO,OAAO,CAACrP,UAAU,CAAC8O,KAAK;EACjC;EACA,MAAMnC,IAAI,GAAGxb,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEke,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;IAC3D3L,MAAM;IACNC,QAAQ;IACR4K,IAAI;IACJC,IAAI;IACJO,MAAM;IACNvO,SAAS;IACToO,MAAM;IACNvO,MAAM,EAAElP,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEoe,sBAAsB,CAAC,EAAE1K,KAAK,CAACxE,MAAM;EACjF,CAAC,CAAC;EACF,MAAM;IACJkD,QAAQ;IACRvD;EACF,CAAC,GAAGwO,IAAI,CAACC,KAAK,IAAIF,IAAI,CAACE,KAAK,GAAGxB,YAAY,CAAC,sBAAsB,EAAEN,IAAI,CAAC,IAAI;IAC3EpJ,QAAQ,EAAE,EAAE;IACZvD,UAAU,EAAE,CAAC;EACf,CAAC,GAAGiN,YAAY,CAAC,sBAAsB,EAAEN,IAAI,CAAC,IAAI;IAChDpJ,QAAQ,EAAE,EAAE;IACZvD,UAAU,EAAE,CAAC;EACf,CAAC;EACD2M,IAAI,CAACpJ,QAAQ,GAAGA,QAAQ;EACxBoJ,IAAI,CAAC3M,UAAU,GAAGA,UAAU;EAC5B,IAAI4O,MAAM,EAAE;IACV,OAAOD,QAAQ,CAAChC,IAAI,CAAC;EACvB,CAAC,MAAM;IACL,OAAO2B,MAAM,CAAC3B,IAAI,CAAC;EACrB;AACF;AACA,SAAS6C,sBAAsBA,CAAC9J,MAAM,EAAE;EACtC,MAAM;IACJ2J,OAAO;IACPjO,KAAK;IACLC,MAAM;IACNb,SAAS;IACTsO,KAAK;IACLjK,KAAK;IACLoK,SAAS,GAAG;EACd,CAAC,GAAGvJ,MAAM;EACV,MAAM1F,UAAU,GAAG7O,cAAc,CAACA,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE0T,KAAK,CAAC7E,UAAU,CAAC,EAAE8O,KAAK,GAAG;IAC7F,OAAO,EAAEA;EACX,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACX,OAAO,EAAEjK,KAAK,CAACuK,OAAO,CAAClc,IAAI,CAAC,GAAG;EACjC,CAAC,CAAC;EACF,IAAI+b,SAAS,EAAE;IACbjP,UAAU,CAACrG,aAAa,CAAC,GAAG,EAAE;EAChC;EACA,MAAM0G,MAAM,GAAGlP,cAAc,CAAC,CAAC,CAAC,EAAE0T,KAAK,CAACxE,MAAM,CAAC;EAC/C,IAAIE,qBAAqB,CAACC,SAAS,CAAC,EAAE;IACpCH,MAAM,CAAC,WAAW,CAAC,GAAGa,eAAe,CAAC;MACpCV,SAAS;MACTc,aAAa,EAAE,IAAI;MACnBF,KAAK;MACLC;IACF,CAAC,CAAC;IACFhB,MAAM,CAAC,mBAAmB,CAAC,GAAGA,MAAM,CAAC,WAAW,CAAC;EACnD;EACA,MAAMoP,WAAW,GAAGrP,UAAU,CAACC,MAAM,CAAC;EACtC,IAAIoP,WAAW,CAACpe,MAAM,GAAG,CAAC,EAAE;IAC1B2O,UAAU,CAAC,OAAO,CAAC,GAAGyP,WAAW;EACnC;EACA,MAAMpT,GAAG,GAAG,EAAE;EACdA,GAAG,CAACpL,IAAI,CAAC;IACPqS,GAAG,EAAE,MAAM;IACXtD,UAAU;IACVuD,QAAQ,EAAE,CAAC8L,OAAO;EACpB,CAAC,CAAC;EACF,IAAIP,KAAK,EAAE;IACTzS,GAAG,CAACpL,IAAI,CAAC;MACPqS,GAAG,EAAE,MAAM;MACXtD,UAAU,EAAE;QACV0P,KAAK,EAAE;MACT,CAAC;MACDnM,QAAQ,EAAE,CAACuL,KAAK;IAClB,CAAC,CAAC;EACJ;EACA,OAAOzS,GAAG;AACZ;AACA,SAASsT,yBAAyBA,CAACjK,MAAM,EAAE;EACzC,MAAM;IACJ2J,OAAO;IACPP,KAAK;IACLjK;EACF,CAAC,GAAGa,MAAM;EACV,MAAM1F,UAAU,GAAG7O,cAAc,CAACA,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE0T,KAAK,CAAC7E,UAAU,CAAC,EAAE8O,KAAK,GAAG;IAC7F,OAAO,EAAEA;EACX,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACX,OAAO,EAAEjK,KAAK,CAACuK,OAAO,CAAClc,IAAI,CAAC,GAAG;EACjC,CAAC,CAAC;EACF,MAAMuc,WAAW,GAAGrP,UAAU,CAACyE,KAAK,CAACxE,MAAM,CAAC;EAC5C,IAAIoP,WAAW,CAACpe,MAAM,GAAG,CAAC,EAAE;IAC1B2O,UAAU,CAAC,OAAO,CAAC,GAAGyP,WAAW;EACnC;EACA,MAAMpT,GAAG,GAAG,EAAE;EACdA,GAAG,CAACpL,IAAI,CAAC;IACPqS,GAAG,EAAE,MAAM;IACXtD,UAAU;IACVuD,QAAQ,EAAE,CAAC8L,OAAO;EACpB,CAAC,CAAC;EACF,IAAIP,KAAK,EAAE;IACTzS,GAAG,CAACpL,IAAI,CAAC;MACPqS,GAAG,EAAE,MAAM;MACXtD,UAAU,EAAE;QACV0P,KAAK,EAAE;MACT,CAAC;MACDnM,QAAQ,EAAE,CAACuL,KAAK;IAClB,CAAC,CAAC;EACJ;EACA,OAAOzS,GAAG;AACZ;AAEA,MAAM;EACJgE,MAAM,EAAEuP;AACV,CAAC,GAAGnN,SAAS;AACb,SAASoN,WAAWA,CAACjM,IAAI,EAAE;EACzB,MAAMxC,KAAK,GAAGwC,IAAI,CAAC,CAAC,CAAC;EACrB,MAAMvC,MAAM,GAAGuC,IAAI,CAAC,CAAC,CAAC;EACtB,MAAM,CAACkM,UAAU,CAAC,GAAGlM,IAAI,CAACzQ,KAAK,CAAC,CAAC,CAAC;EAClC,IAAI8I,OAAO,GAAG,IAAI;EAClB,IAAIjJ,KAAK,CAACC,OAAO,CAAC6c,UAAU,CAAC,EAAE;IAC7B7T,OAAO,GAAG;MACRqH,GAAG,EAAE,GAAG;MACRtD,UAAU,EAAE;QACV0P,KAAK,EAAE,EAAE,CAACxW,MAAM,CAACwE,MAAM,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC3D,MAAM,CAACyC,eAAe,CAACtF,KAAK;MACtE,CAAC;MACDkN,QAAQ,EAAE,CAAC;QACTD,GAAG,EAAE,MAAM;QACXtD,UAAU,EAAE;UACV0P,KAAK,EAAE,EAAE,CAACxW,MAAM,CAACwE,MAAM,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC3D,MAAM,CAACyC,eAAe,CAACnF,SAAS,CAAC;UACzEuZ,IAAI,EAAE,cAAc;UACpB7L,CAAC,EAAE4L,UAAU,CAAC,CAAC;QACjB;MACF,CAAC,EAAE;QACDxM,GAAG,EAAE,MAAM;QACXtD,UAAU,EAAE;UACV0P,KAAK,EAAE,EAAE,CAACxW,MAAM,CAACwE,MAAM,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC3D,MAAM,CAACyC,eAAe,CAACpF,OAAO,CAAC;UACvEwZ,IAAI,EAAE,cAAc;UACpB7L,CAAC,EAAE4L,UAAU,CAAC,CAAC;QACjB;MACF,CAAC;IACH,CAAC;EACH,CAAC,MAAM;IACL7T,OAAO,GAAG;MACRqH,GAAG,EAAE,MAAM;MACXtD,UAAU,EAAE;QACV+P,IAAI,EAAE,cAAc;QACpB7L,CAAC,EAAE4L;MACL;IACF,CAAC;EACH;EACA,OAAO;IACLrB,KAAK,EAAE,IAAI;IACXrN,KAAK;IACLC,MAAM;IACNuC,IAAI,EAAE3H;EACR,CAAC;AACH;AACA,MAAM+T,0BAA0B,GAAG;EACjCvB,KAAK,EAAE,KAAK;EACZrN,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE;AACV,CAAC;AACD,SAAS4O,kBAAkBA,CAACtM,QAAQ,EAAED,MAAM,EAAE;EAC5C,IAAI,CAACvJ,UAAU,IAAI,CAACuD,MAAM,CAACH,gBAAgB,IAAIoG,QAAQ,EAAE;IACvDuM,OAAO,CAACC,KAAK,CAAC,mBAAmB,CAACjX,MAAM,CAACyK,QAAQ,EAAE,kBAAkB,CAAC,CAACzK,MAAM,CAACwK,MAAM,EAAE,gBAAgB,CAAC,CAAC;EAC1G;AACF;AACA,SAAS0M,QAAQA,CAACzM,QAAQ,EAAED,MAAM,EAAE;EAClC,IAAIkG,WAAW,GAAGlG,MAAM;EACxB,IAAIA,MAAM,KAAK,IAAI,IAAIhG,MAAM,CAACf,YAAY,KAAK,IAAI,EAAE;IACnD+G,MAAM,GAAGgF,sBAAsB,CAAC,CAAC;EACnC;EACA,OAAO,IAAI6E,OAAO,CAAC,CAAC8C,OAAO,EAAE7C,MAAM,KAAK;IACtC,IAAI5D,WAAW,KAAK,IAAI,EAAE;MACxB,MAAMjC,IAAI,GAAGW,SAAS,CAAC3E,QAAQ,CAAC,IAAI,CAAC,CAAC;MACtCA,QAAQ,GAAGgE,IAAI,CAAChE,QAAQ,IAAIA,QAAQ;MACpCD,MAAM,GAAGiE,IAAI,CAACjE,MAAM,IAAIA,MAAM;IAChC;IACA,IAAIC,QAAQ,IAAID,MAAM,IAAIkM,QAAQ,CAAClM,MAAM,CAAC,IAAIkM,QAAQ,CAAClM,MAAM,CAAC,CAACC,QAAQ,CAAC,EAAE;MACxE,MAAMC,IAAI,GAAGgM,QAAQ,CAAClM,MAAM,CAAC,CAACC,QAAQ,CAAC;MACvC,OAAO0M,OAAO,CAACR,WAAW,CAACjM,IAAI,CAAC,CAAC;IACnC;IACAqM,kBAAkB,CAACtM,QAAQ,EAAED,MAAM,CAAC;IACpC2M,OAAO,CAAClf,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6e,0BAA0B,CAAC,EAAE,CAAC,CAAC,EAAE;MACzEpM,IAAI,EAAElG,MAAM,CAACH,gBAAgB,IAAIoG,QAAQ,GAAGsJ,YAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;IAC3F,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ;AAEA,MAAMqD,MAAM,GAAGA,CAAA,KAAM,CAAC,CAAC;AACvB,MAAMC,GAAG,GAAG7S,MAAM,CAACJ,kBAAkB,IAAIlJ,WAAW,IAAIA,WAAW,CAACX,IAAI,IAAIW,WAAW,CAACV,OAAO,GAAGU,WAAW,GAAG;EAC9GX,IAAI,EAAE6c,MAAM;EACZ5c,OAAO,EAAE4c;AACX,CAAC;AACD,MAAME,QAAQ,GAAG,cAAc;AAC/B,MAAMC,KAAK,GAAG5J,IAAI,IAAI;EACpB0J,GAAG,CAAC9c,IAAI,CAAC,EAAE,CAACyF,MAAM,CAACsX,QAAQ,EAAE,GAAG,CAAC,CAACtX,MAAM,CAAC2N,IAAI,EAAE,SAAS,CAAC,CAAC;EAC1D,OAAO,MAAM6J,GAAG,CAAC7J,IAAI,CAAC;AACxB,CAAC;AACD,MAAM6J,GAAG,GAAG7J,IAAI,IAAI;EAClB0J,GAAG,CAAC9c,IAAI,CAAC,EAAE,CAACyF,MAAM,CAACsX,QAAQ,EAAE,GAAG,CAAC,CAACtX,MAAM,CAAC2N,IAAI,EAAE,OAAO,CAAC,CAAC;EACxD0J,GAAG,CAAC7c,OAAO,CAAC,EAAE,CAACwF,MAAM,CAACsX,QAAQ,EAAE,GAAG,CAAC,CAACtX,MAAM,CAAC2N,IAAI,CAAC,EAAE,EAAE,CAAC3N,MAAM,CAACsX,QAAQ,EAAE,GAAG,CAAC,CAACtX,MAAM,CAAC2N,IAAI,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC3N,MAAM,CAACsX,QAAQ,EAAE,GAAG,CAAC,CAACtX,MAAM,CAAC2N,IAAI,EAAE,OAAO,CAAC,CAAC;AACtJ,CAAC;AACD,IAAI8J,IAAI,GAAG;EACTF,KAAK;EACLC;AACF,CAAC;AAED,MAAME,MAAM,GAAGA,CAAA,KAAM,CAAC,CAAC;AACvB,SAASC,SAASA,CAACnR,IAAI,EAAE;EACvB,MAAM4N,KAAK,GAAG5N,IAAI,CAACvD,YAAY,GAAGuD,IAAI,CAACvD,YAAY,CAACxC,aAAa,CAAC,GAAG,IAAI;EACzE,OAAO,OAAO2T,KAAK,KAAK,QAAQ;AAClC;AACA,SAASwD,gBAAgBA,CAACpR,IAAI,EAAE;EAC9B,MAAMgE,MAAM,GAAGhE,IAAI,CAACvD,YAAY,GAAGuD,IAAI,CAACvD,YAAY,CAACrC,WAAW,CAAC,GAAG,IAAI;EACxE,MAAM8J,IAAI,GAAGlE,IAAI,CAACvD,YAAY,GAAGuD,IAAI,CAACvD,YAAY,CAACpC,SAAS,CAAC,GAAG,IAAI;EACpE,OAAO2J,MAAM,IAAIE,IAAI;AACvB;AACA,SAASmN,eAAeA,CAACrR,IAAI,EAAE;EAC7B,OAAOA,IAAI,IAAIA,IAAI,CAACC,SAAS,IAAID,IAAI,CAACC,SAAS,CAACqR,QAAQ,IAAItR,IAAI,CAACC,SAAS,CAACqR,QAAQ,CAACtT,MAAM,CAACZ,gBAAgB,CAAC;AAC9G;AACA,SAASmU,UAAUA,CAAA,EAAG;EACpB,IAAIvT,MAAM,CAACX,cAAc,KAAK,IAAI,EAAE;IAClC,OAAOmU,QAAQ,CAACne,OAAO;EACzB;EACA,MAAMoe,OAAO,GAAGD,QAAQ,CAACxT,MAAM,CAACX,cAAc,CAAC;EAC/C,OAAOoU,OAAO,IAAID,QAAQ,CAACne,OAAO;AACpC;AACA,SAASqe,eAAeA,CAAC9N,GAAG,EAAE;EAC5B,OAAOpP,QAAQ,CAACkd,eAAe,CAAC,4BAA4B,EAAE9N,GAAG,CAAC;AACpE;AACA,SAAS5O,aAAaA,CAAC4O,GAAG,EAAE;EAC1B,OAAOpP,QAAQ,CAACQ,aAAa,CAAC4O,GAAG,CAAC;AACpC;AACA,SAAS+N,UAAUA,CAACC,WAAW,EAAE;EAC/B,IAAI5L,MAAM,GAAGtU,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqL,SAAS,GAAGrL,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,MAAM;IACJmgB,IAAI,GAAGD,WAAW,CAAChO,GAAG,KAAK,KAAK,GAAG8N,eAAe,GAAG1c;EACvD,CAAC,GAAGgR,MAAM;EACV,IAAI,OAAO4L,WAAW,KAAK,QAAQ,EAAE;IACnC,OAAOpd,QAAQ,CAACsd,cAAc,CAACF,WAAW,CAAC;EAC7C;EACA,MAAMhO,GAAG,GAAGiO,IAAI,CAACD,WAAW,CAAChO,GAAG,CAAC;EACjCvT,MAAM,CAACa,IAAI,CAAC0gB,WAAW,CAACtR,UAAU,IAAI,EAAE,CAAC,CAAC1O,OAAO,CAAC,UAAUkL,GAAG,EAAE;IAC/D8G,GAAG,CAAC7E,YAAY,CAACjC,GAAG,EAAE8U,WAAW,CAACtR,UAAU,CAACxD,GAAG,CAAC,CAAC;EACpD,CAAC,CAAC;EACF,MAAM+G,QAAQ,GAAG+N,WAAW,CAAC/N,QAAQ,IAAI,EAAE;EAC3CA,QAAQ,CAACjS,OAAO,CAAC,UAAUwN,KAAK,EAAE;IAChCwE,GAAG,CAACmO,WAAW,CAACJ,UAAU,CAACvS,KAAK,EAAE;MAChCyS;IACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,OAAOjO,GAAG;AACZ;AACA,SAASoO,aAAaA,CAAChS,IAAI,EAAE;EAC3B,IAAIiS,OAAO,GAAG,GAAG,CAACzY,MAAM,CAACwG,IAAI,CAACkS,SAAS,EAAE,GAAG,CAAC;EAC7C;EACAD,OAAO,GAAG,EAAE,CAACzY,MAAM,CAACyY,OAAO,EAAE,+BAA+B,CAAC;EAC7D;EACA,OAAOA,OAAO;AAChB;AACA,MAAMT,QAAQ,GAAG;EACfne,OAAO,EAAE,SAAAA,CAAU8e,QAAQ,EAAE;IAC3B,MAAMnS,IAAI,GAAGmS,QAAQ,CAAC,CAAC,CAAC;IACxB,IAAInS,IAAI,CAACoS,UAAU,EAAE;MACnBD,QAAQ,CAAC,CAAC,CAAC,CAACvgB,OAAO,CAAC6c,QAAQ,IAAI;QAC9BzO,IAAI,CAACoS,UAAU,CAAC7S,YAAY,CAACoS,UAAU,CAAClD,QAAQ,CAAC,EAAEzO,IAAI,CAAC;MAC1D,CAAC,CAAC;MACF,IAAIA,IAAI,CAACvD,YAAY,CAACxC,aAAa,CAAC,KAAK,IAAI,IAAI+D,MAAM,CAACL,kBAAkB,EAAE;QAC1E,IAAIsU,OAAO,GAAGzd,QAAQ,CAAC6d,aAAa,CAACL,aAAa,CAAChS,IAAI,CAAC,CAAC;QACzDA,IAAI,CAACoS,UAAU,CAACE,YAAY,CAACL,OAAO,EAAEjS,IAAI,CAAC;MAC7C,CAAC,MAAM;QACLA,IAAI,CAACuS,MAAM,CAAC,CAAC;MACf;IACF;EACF,CAAC;EACDC,IAAI,EAAE,SAAAA,CAAUL,QAAQ,EAAE;IACxB,MAAMnS,IAAI,GAAGmS,QAAQ,CAAC,CAAC,CAAC;IACxB,MAAM1D,QAAQ,GAAG0D,QAAQ,CAAC,CAAC,CAAC;;IAE5B;IACA;IACA,IAAI,CAACpS,UAAU,CAACC,IAAI,CAAC,CAAC9K,OAAO,CAAC8I,MAAM,CAACZ,gBAAgB,CAAC,EAAE;MACtD,OAAOoU,QAAQ,CAACne,OAAO,CAAC8e,QAAQ,CAAC;IACnC;IACA,MAAMM,MAAM,GAAG,IAAI9f,MAAM,CAAC,EAAE,CAAC6G,MAAM,CAACwE,MAAM,CAACb,SAAS,EAAE,KAAK,CAAC,CAAC;IAC7D,OAAOsR,QAAQ,CAAC,CAAC,CAAC,CAACnO,UAAU,CAACZ,EAAE;IAChC,IAAI+O,QAAQ,CAAC,CAAC,CAAC,CAACnO,UAAU,CAAC0P,KAAK,EAAE;MAChC,MAAM0C,YAAY,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAACnO,UAAU,CAAC0P,KAAK,CAAC9P,KAAK,CAAC,GAAG,CAAC,CAACjN,MAAM,CAAC,CAACsN,GAAG,EAAE8G,GAAG,KAAK;QAChF,IAAIA,GAAG,KAAKrJ,MAAM,CAACZ,gBAAgB,IAAIiK,GAAG,CAAC+G,KAAK,CAACqE,MAAM,CAAC,EAAE;UACxDlS,GAAG,CAACoS,KAAK,CAACphB,IAAI,CAAC8V,GAAG,CAAC;QACrB,CAAC,MAAM;UACL9G,GAAG,CAACqS,MAAM,CAACrhB,IAAI,CAAC8V,GAAG,CAAC;QACtB;QACA,OAAO9G,GAAG;MACZ,CAAC,EAAE;QACDqS,MAAM,EAAE,EAAE;QACVD,KAAK,EAAE;MACT,CAAC,CAAC;MACFlE,QAAQ,CAAC,CAAC,CAAC,CAACnO,UAAU,CAAC0P,KAAK,GAAG0C,YAAY,CAACC,KAAK,CAACnf,IAAI,CAAC,GAAG,CAAC;MAC3D,IAAIkf,YAAY,CAACE,MAAM,CAACjhB,MAAM,KAAK,CAAC,EAAE;QACpCqO,IAAI,CAAC6S,eAAe,CAAC,OAAO,CAAC;MAC/B,CAAC,MAAM;QACL7S,IAAI,CAACjB,YAAY,CAAC,OAAO,EAAE2T,YAAY,CAACE,MAAM,CAACpf,IAAI,CAAC,GAAG,CAAC,CAAC;MAC3D;IACF;IACA,MAAMsf,YAAY,GAAGrE,QAAQ,CAAC/U,GAAG,CAACC,CAAC,IAAI+J,MAAM,CAAC/J,CAAC,CAAC,CAAC,CAACnG,IAAI,CAAC,IAAI,CAAC;IAC5DwM,IAAI,CAACjB,YAAY,CAAC9E,aAAa,EAAE,EAAE,CAAC;IACpC+F,IAAI,CAAChB,SAAS,GAAG8T,YAAY;EAC/B;AACF,CAAC;AACD,SAASC,oBAAoBA,CAACC,EAAE,EAAE;EAChCA,EAAE,CAAC,CAAC;AACN;AACA,SAASC,OAAOA,CAACC,SAAS,EAAEC,QAAQ,EAAE;EACpC,MAAMC,gBAAgB,GAAG,OAAOD,QAAQ,KAAK,UAAU,GAAGA,QAAQ,GAAGjC,MAAM;EAC3E,IAAIgC,SAAS,CAACvhB,MAAM,KAAK,CAAC,EAAE;IAC1ByhB,gBAAgB,CAAC,CAAC;EACpB,CAAC,MAAM;IACL,IAAIC,KAAK,GAAGN,oBAAoB;IAChC,IAAI/U,MAAM,CAACN,cAAc,KAAKnD,uBAAuB,EAAE;MACrD8Y,KAAK,GAAG9e,MAAM,CAAC+e,qBAAqB,IAAIP,oBAAoB;IAC9D;IACAM,KAAK,CAAC,MAAM;MACV,MAAM5B,OAAO,GAAGF,UAAU,CAAC,CAAC;MAC5B,MAAMxd,IAAI,GAAGkd,IAAI,CAACF,KAAK,CAAC,QAAQ,CAAC;MACjCmC,SAAS,CAACxZ,GAAG,CAAC+X,OAAO,CAAC;MACtB1d,IAAI,CAAC,CAAC;MACNqf,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC;EACJ;AACF;AACA,IAAIG,QAAQ,GAAG,KAAK;AACpB,SAASC,kBAAkBA,CAAA,EAAG;EAC5BD,QAAQ,GAAG,IAAI;AACjB;AACA,SAASE,iBAAiBA,CAAA,EAAG;EAC3BF,QAAQ,GAAG,KAAK;AAClB;AACA,IAAIG,EAAE,GAAG,IAAI;AACb,SAASC,OAAOA,CAACC,OAAO,EAAE;EACxB,IAAI,CAACnf,iBAAiB,EAAE;IACtB;EACF;EACA,IAAI,CAACuJ,MAAM,CAACP,gBAAgB,EAAE;IAC5B;EACF;EACA,MAAM;IACJoW,YAAY,GAAG3C,MAAM;IACrB4C,YAAY,GAAG5C,MAAM;IACrB6C,sBAAsB,GAAG7C,MAAM;IAC/B8C,oBAAoB,GAAGxf;EACzB,CAAC,GAAGof,OAAO;EACXF,EAAE,GAAG,IAAIjf,iBAAiB,CAACwf,OAAO,IAAI;IACpC,IAAIV,QAAQ,EAAE;IACd,MAAMhI,aAAa,GAAGvC,sBAAsB,CAAC,CAAC;IAC9CnJ,OAAO,CAACoU,OAAO,CAAC,CAACriB,OAAO,CAACsiB,cAAc,IAAI;MACzC,IAAIA,cAAc,CAACC,IAAI,KAAK,WAAW,IAAID,cAAc,CAACE,UAAU,CAACziB,MAAM,GAAG,CAAC,IAAI,CAACwf,SAAS,CAAC+C,cAAc,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3H,IAAIpW,MAAM,CAACR,oBAAoB,EAAE;UAC/BuW,sBAAsB,CAACG,cAAc,CAACjZ,MAAM,CAAC;QAC/C;QACA4Y,YAAY,CAACK,cAAc,CAACjZ,MAAM,CAAC;MACrC;MACA,IAAIiZ,cAAc,CAACC,IAAI,KAAK,YAAY,IAAID,cAAc,CAACjZ,MAAM,CAACmX,UAAU,IAAIpU,MAAM,CAACR,oBAAoB,EAAE;QAC3GuW,sBAAsB,CAACG,cAAc,CAACjZ,MAAM,CAACmX,UAAU,CAAC;MAC1D;MACA,IAAI8B,cAAc,CAACC,IAAI,KAAK,YAAY,IAAIhD,SAAS,CAAC+C,cAAc,CAACjZ,MAAM,CAAC,IAAI,CAACe,+BAA+B,CAAC9G,OAAO,CAACgf,cAAc,CAAC1T,aAAa,CAAC,EAAE;QACtJ,IAAI0T,cAAc,CAAC1T,aAAa,KAAK,OAAO,IAAI4Q,gBAAgB,CAAC8C,cAAc,CAACjZ,MAAM,CAAC,EAAE;UACvF,MAAM;YACJ+I,MAAM;YACNC;UACF,CAAC,GAAG+F,gBAAgB,CAACjK,UAAU,CAACmU,cAAc,CAACjZ,MAAM,CAAC,CAAC;UACvDiZ,cAAc,CAACjZ,MAAM,CAAC8D,YAAY,CAAC3E,WAAW,EAAE4J,MAAM,IAAIuH,aAAa,CAAC;UACxE,IAAItH,QAAQ,EAAEiQ,cAAc,CAACjZ,MAAM,CAAC8D,YAAY,CAAC1E,SAAS,EAAE4J,QAAQ,CAAC;QACvE,CAAC,MAAM,IAAIoN,eAAe,CAAC6C,cAAc,CAACjZ,MAAM,CAAC,EAAE;UACjD6Y,YAAY,CAACI,cAAc,CAACjZ,MAAM,CAAC;QACrC;MACF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAI,CAACrG,MAAM,EAAE;EACb8e,EAAE,CAACC,OAAO,CAACK,oBAAoB,EAAE;IAC/BK,SAAS,EAAE,IAAI;IACf/T,UAAU,EAAE,IAAI;IAChBgU,aAAa,EAAE,IAAI;IACnBC,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;AACA,SAASC,UAAUA,CAAA,EAAG;EACpB,IAAI,CAACd,EAAE,EAAE;EACTA,EAAE,CAACc,UAAU,CAAC,CAAC;AACjB;AAEA,SAASC,WAAWA,CAAEzU,IAAI,EAAE;EAC1B,MAAMlB,KAAK,GAAGkB,IAAI,CAACvD,YAAY,CAAC,OAAO,CAAC;EACxC,IAAIE,GAAG,GAAG,EAAE;EACZ,IAAImC,KAAK,EAAE;IACTnC,GAAG,GAAGmC,KAAK,CAACoB,KAAK,CAAC,GAAG,CAAC,CAACjN,MAAM,CAAC,CAACsN,GAAG,EAAEzB,KAAK,KAAK;MAC5C,MAAM6B,MAAM,GAAG7B,KAAK,CAACoB,KAAK,CAAC,GAAG,CAAC;MAC/B,MAAMhF,IAAI,GAAGyF,MAAM,CAAC,CAAC,CAAC;MACtB,MAAMpQ,KAAK,GAAGoQ,MAAM,CAAClN,KAAK,CAAC,CAAC,CAAC;MAC7B,IAAIyH,IAAI,IAAI3K,KAAK,CAACoB,MAAM,GAAG,CAAC,EAAE;QAC5B4O,GAAG,CAACrF,IAAI,CAAC,GAAG3K,KAAK,CAACiD,IAAI,CAAC,GAAG,CAAC,CAACiN,IAAI,CAAC,CAAC;MACpC;MACA,OAAOF,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACR;EACA,OAAO5D,GAAG;AACZ;AAEA,SAAS+X,WAAWA,CAAE1U,IAAI,EAAE;EAC1B,MAAM2U,cAAc,GAAG3U,IAAI,CAACvD,YAAY,CAAC,aAAa,CAAC;EACvD,MAAMmY,gBAAgB,GAAG5U,IAAI,CAACvD,YAAY,CAAC,WAAW,CAAC;EACvD,MAAMoY,SAAS,GAAG7U,IAAI,CAAC6U,SAAS,KAAK9X,SAAS,GAAGiD,IAAI,CAAC6U,SAAS,CAACpU,IAAI,CAAC,CAAC,GAAG,EAAE;EAC3E,IAAI9D,GAAG,GAAGqN,gBAAgB,CAACjK,UAAU,CAACC,IAAI,CAAC,CAAC;EAC5C,IAAI,CAACrD,GAAG,CAACqH,MAAM,EAAE;IACfrH,GAAG,CAACqH,MAAM,GAAGgF,sBAAsB,CAAC,CAAC;EACvC;EACA,IAAI2L,cAAc,IAAIC,gBAAgB,EAAE;IACtCjY,GAAG,CAACqH,MAAM,GAAG2Q,cAAc;IAC3BhY,GAAG,CAACsH,QAAQ,GAAG2Q,gBAAgB;EACjC;EACA,IAAIjY,GAAG,CAACsH,QAAQ,IAAItH,GAAG,CAACqH,MAAM,EAAE;IAC9B,OAAOrH,GAAG;EACZ;EACA,IAAIA,GAAG,CAACqH,MAAM,IAAI6Q,SAAS,CAACljB,MAAM,GAAG,CAAC,EAAE;IACtCgL,GAAG,CAACsH,QAAQ,GAAGwE,UAAU,CAAC9L,GAAG,CAACqH,MAAM,EAAEhE,IAAI,CAAC6U,SAAS,CAAC,IAAIrM,SAAS,CAAC7L,GAAG,CAACqH,MAAM,EAAEoB,KAAK,CAACpF,IAAI,CAAC6U,SAAS,CAAC,CAAC;EACvG;EACA,IAAI,CAAClY,GAAG,CAACsH,QAAQ,IAAIjG,MAAM,CAAC+J,YAAY,IAAI/H,IAAI,CAAC8U,UAAU,IAAI9U,IAAI,CAAC8U,UAAU,CAACC,QAAQ,KAAKC,IAAI,CAACC,SAAS,EAAE;IAC1GtY,GAAG,CAACsH,QAAQ,GAAGjE,IAAI,CAAC8U,UAAU,CAACI,IAAI;EACrC;EACA,OAAOvY,GAAG;AACZ;AAEA,SAASwY,gBAAgBA,CAAEnV,IAAI,EAAE;EAC/B,MAAMoV,eAAe,GAAGvV,OAAO,CAACG,IAAI,CAACM,UAAU,CAAC,CAACrN,MAAM,CAAC,CAACsN,GAAG,EAAEjE,IAAI,KAAK;IACrE,IAAIiE,GAAG,CAAC4G,IAAI,KAAK,OAAO,IAAI5G,GAAG,CAAC4G,IAAI,KAAK,OAAO,EAAE;MAChD5G,GAAG,CAACjE,IAAI,CAAC6K,IAAI,CAAC,GAAG7K,IAAI,CAAC/L,KAAK;IAC7B;IACA,OAAOgQ,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,MAAM6O,KAAK,GAAGpP,IAAI,CAACvD,YAAY,CAAC,OAAO,CAAC;EACxC,MAAM6S,OAAO,GAAGtP,IAAI,CAACvD,YAAY,CAAC,kBAAkB,CAAC;EACrD,IAAIuB,MAAM,CAACT,QAAQ,EAAE;IACnB,IAAI6R,KAAK,EAAE;MACTgG,eAAe,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC5b,MAAM,CAACwE,MAAM,CAACZ,gBAAgB,EAAE,SAAS,CAAC,CAAC5D,MAAM,CAAC8V,OAAO,IAAI7P,YAAY,CAAC,CAAC,CAAC;IACtH,CAAC,MAAM;MACL2V,eAAe,CAAC,aAAa,CAAC,GAAG,MAAM;MACvCA,eAAe,CAAC,WAAW,CAAC,GAAG,OAAO;IACxC;EACF;EACA,OAAOA,eAAe;AACxB;AAEA,SAASC,SAASA,CAAA,EAAG;EACnB,OAAO;IACLpR,QAAQ,EAAE,IAAI;IACdmL,KAAK,EAAE,IAAI;IACXE,OAAO,EAAE,IAAI;IACbtL,MAAM,EAAE,IAAI;IACZlD,SAAS,EAAExC,oBAAoB;IAC/B4Q,MAAM,EAAE,KAAK;IACbJ,IAAI,EAAE;MACJ7K,QAAQ,EAAE,IAAI;MACdD,MAAM,EAAE,IAAI;MACZkF,IAAI,EAAE;IACR,CAAC;IACDmG,MAAM,EAAE,IAAI;IACZlK,KAAK,EAAE;MACLuK,OAAO,EAAE,EAAE;MACX/O,MAAM,EAAE,CAAC,CAAC;MACVL,UAAU,EAAE,CAAC;IACf;EACF,CAAC;AACH;AACA,SAASgV,SAASA,CAACtV,IAAI,EAAE;EACvB,IAAIuV,MAAM,GAAG7jB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqL,SAAS,GAAGrL,SAAS,CAAC,CAAC,CAAC,GAAG;IAC/E+iB,WAAW,EAAE;EACf,CAAC;EACD,MAAM;IACJxQ,QAAQ;IACRD,MAAM;IACNkF,IAAI,EAAEsM;EACR,CAAC,GAAGd,WAAW,CAAC1U,IAAI,CAAC;EACrB,MAAMoV,eAAe,GAAGD,gBAAgB,CAACnV,IAAI,CAAC;EAC9C,MAAMyV,UAAU,GAAG1I,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC,EAAE/M,IAAI,CAAC;EAC9D,IAAI0V,WAAW,GAAGH,MAAM,CAACd,WAAW,GAAGA,WAAW,CAACzU,IAAI,CAAC,GAAG,EAAE;EAC7D,OAAOvO,cAAc,CAAC;IACpBwS,QAAQ;IACRmL,KAAK,EAAEpP,IAAI,CAACvD,YAAY,CAAC,OAAO,CAAC;IACjC6S,OAAO,EAAEtP,IAAI,CAACvD,YAAY,CAAC,kBAAkB,CAAC;IAC9CuH,MAAM;IACNlD,SAAS,EAAExC,oBAAoB;IAC/BwQ,IAAI,EAAE;MACJ7K,QAAQ,EAAE,IAAI;MACdD,MAAM,EAAE,IAAI;MACZkF,IAAI,EAAE;IACR,CAAC;IACDmG,MAAM,EAAE,IAAI;IACZH,MAAM,EAAE,KAAK;IACb/J,KAAK,EAAE;MACLuK,OAAO,EAAE8F,YAAY;MACrB7U,MAAM,EAAE+U,WAAW;MACnBpV,UAAU,EAAE8U;IACd;EACF,CAAC,EAAEK,UAAU,CAAC;AAChB;AAEA,MAAM;EACJ9U,MAAM,EAAEgV;AACV,CAAC,GAAG5S,SAAS;AACb,SAAS6S,gBAAgBA,CAAC5V,IAAI,EAAE;EAC9B,MAAM6V,QAAQ,GAAG7X,MAAM,CAACX,cAAc,KAAK,MAAM,GAAGiY,SAAS,CAACtV,IAAI,EAAE;IAClEyU,WAAW,EAAE;EACf,CAAC,CAAC,GAAGa,SAAS,CAACtV,IAAI,CAAC;EACpB,IAAI,CAAC6V,QAAQ,CAAC1Q,KAAK,CAACuK,OAAO,CAACxa,OAAO,CAAC0G,qBAAqB,CAAC,EAAE;IAC1D,OAAO2R,YAAY,CAAC,oBAAoB,EAAEvN,IAAI,EAAE6V,QAAQ,CAAC;EAC3D,CAAC,MAAM;IACL,OAAOtI,YAAY,CAAC,gCAAgC,EAAEvN,IAAI,EAAE6V,QAAQ,CAAC;EACvE;AACF;AACA,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,OAAO,CAAC,GAAG5d,EAAE,EAAE,GAAGkB,EAAE,CAAC;AACvB;AACA,SAAS2c,MAAMA,CAACC,IAAI,EAAE;EACpB,IAAI7C,QAAQ,GAAGzhB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqL,SAAS,GAAGrL,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACvF,IAAI,CAACkD,MAAM,EAAE,OAAOiZ,OAAO,CAAC8C,OAAO,CAAC,CAAC;EACrC,MAAMsF,aAAa,GAAGzhB,QAAQ,CAACK,eAAe,CAACoL,SAAS;EACxD,MAAMiW,MAAM,GAAGC,MAAM,IAAIF,aAAa,CAACvK,GAAG,CAAC,EAAE,CAAClS,MAAM,CAACc,2BAA2B,EAAE,GAAG,CAAC,CAACd,MAAM,CAAC2c,MAAM,CAAC,CAAC;EACtG,MAAMC,SAAS,GAAGD,MAAM,IAAIF,aAAa,CAAC1D,MAAM,CAAC,EAAE,CAAC/Y,MAAM,CAACc,2BAA2B,EAAE,GAAG,CAAC,CAACd,MAAM,CAAC2c,MAAM,CAAC,CAAC;EAC5G,MAAME,QAAQ,GAAGrY,MAAM,CAAC+J,YAAY,GAAG+N,gBAAgB,CAAC,CAAC,GAAG/e,CAAC,CAACyC,MAAM,CAACnJ,MAAM,CAACa,IAAI,CAACykB,QAAQ,CAAC,CAAC;EAC3F,IAAI,CAACU,QAAQ,CAAC/M,QAAQ,CAAC,IAAI,CAAC,EAAE;IAC5B+M,QAAQ,CAAC9kB,IAAI,CAAC,IAAI,CAAC;EACrB;EACA,MAAM+kB,gBAAgB,GAAG,CAAC,GAAG,CAAC9c,MAAM,CAACoC,qBAAqB,EAAE,QAAQ,CAAC,CAACpC,MAAM,CAACS,aAAa,EAAE,IAAI,CAAC,CAAC,CAACT,MAAM,CAAC6c,QAAQ,CAAC3c,GAAG,CAAC6c,IAAI,IAAI,GAAG,CAAC/c,MAAM,CAAC+c,IAAI,EAAE,QAAQ,CAAC,CAAC/c,MAAM,CAACS,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC,CAACzG,IAAI,CAAC,IAAI,CAAC;EAClM,IAAI8iB,gBAAgB,CAAC3kB,MAAM,KAAK,CAAC,EAAE;IACjC,OAAOkc,OAAO,CAAC8C,OAAO,CAAC,CAAC;EAC1B;EACA,IAAI6F,UAAU,GAAG,EAAE;EACnB,IAAI;IACFA,UAAU,GAAG3W,OAAO,CAACmW,IAAI,CAACS,gBAAgB,CAACH,gBAAgB,CAAC,CAAC;EAC/D,CAAC,CAAC,OAAOzb,IAAI,EAAE;IACb;EAAA;EAEF,IAAI2b,UAAU,CAAC7kB,MAAM,GAAG,CAAC,EAAE;IACzBukB,MAAM,CAAC,SAAS,CAAC;IACjBE,SAAS,CAAC,UAAU,CAAC;EACvB,CAAC,MAAM;IACL,OAAOvI,OAAO,CAAC8C,OAAO,CAAC,CAAC;EAC1B;EACA,MAAM5c,IAAI,GAAGkd,IAAI,CAACF,KAAK,CAAC,QAAQ,CAAC;EACjC,MAAMmC,SAAS,GAAGsD,UAAU,CAACvjB,MAAM,CAAC,CAACsN,GAAG,EAAEP,IAAI,KAAK;IACjD,IAAI;MACF,MAAMmS,QAAQ,GAAGyD,gBAAgB,CAAC5V,IAAI,CAAC;MACvC,IAAImS,QAAQ,EAAE;QACZ5R,GAAG,CAAChP,IAAI,CAAC4gB,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOtX,IAAI,EAAE;MACb,IAAI,CAACJ,UAAU,EAAE;QACf,IAAII,IAAI,CAACsM,IAAI,KAAK,aAAa,EAAE;UAC/BqJ,OAAO,CAACC,KAAK,CAAC5V,IAAI,CAAC;QACrB;MACF;IACF;IACA,OAAO0F,GAAG;EACZ,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,IAAIsN,OAAO,CAAC,CAAC8C,OAAO,EAAE7C,MAAM,KAAK;IACtCD,OAAO,CAAC6I,GAAG,CAACxD,SAAS,CAAC,CAACyD,IAAI,CAACC,iBAAiB,IAAI;MAC/C3D,OAAO,CAAC2D,iBAAiB,EAAE,MAAM;QAC/BV,MAAM,CAAC,QAAQ,CAAC;QAChBA,MAAM,CAAC,UAAU,CAAC;QAClBE,SAAS,CAAC,SAAS,CAAC;QACpB,IAAI,OAAOjD,QAAQ,KAAK,UAAU,EAAEA,QAAQ,CAAC,CAAC;QAC9Cpf,IAAI,CAAC,CAAC;QACN4c,OAAO,CAAC,CAAC;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,CAACkG,KAAK,CAAChc,IAAI,IAAI;MACf9G,IAAI,CAAC,CAAC;MACN+Z,MAAM,CAACjT,IAAI,CAAC;IACd,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,SAASic,MAAMA,CAAC9W,IAAI,EAAE;EACpB,IAAImT,QAAQ,GAAGzhB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqL,SAAS,GAAGrL,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACvFkkB,gBAAgB,CAAC5V,IAAI,CAAC,CAAC2W,IAAI,CAACxE,QAAQ,IAAI;IACtC,IAAIA,QAAQ,EAAE;MACZc,OAAO,CAAC,CAACd,QAAQ,CAAC,EAAEgB,QAAQ,CAAC;IAC/B;EACF,CAAC,CAAC;AACJ;AACA,SAAS4D,YAAYA,CAACC,IAAI,EAAE;EAC1B,OAAO,UAAUC,mBAAmB,EAAE;IACpC,IAAIjR,MAAM,GAAGtU,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqL,SAAS,GAAGrL,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACnF,MAAMwlB,cAAc,GAAG,CAACD,mBAAmB,IAAI,CAAC,CAAC,EAAE/S,IAAI,GAAG+S,mBAAmB,GAAGzJ,kBAAkB,CAACyJ,mBAAmB,IAAI,CAAC,CAAC,CAAC;IAC7H,IAAI;MACFnI;IACF,CAAC,GAAG9I,MAAM;IACV,IAAI8I,IAAI,EAAE;MACRA,IAAI,GAAG,CAACA,IAAI,IAAI,CAAC,CAAC,EAAE5K,IAAI,GAAG4K,IAAI,GAAGtB,kBAAkB,CAACsB,IAAI,IAAI,CAAC,CAAC,CAAC;IAClE;IACA,OAAOkI,IAAI,CAACE,cAAc,EAAEzlB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEuU,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;MACzE8I;IACF,CAAC,CAAC,CAAC;EACL,CAAC;AACH;AACA,MAAMqI,MAAM,GAAG,SAAAA,CAAUD,cAAc,EAAE;EACvC,IAAIlR,MAAM,GAAGtU,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqL,SAAS,GAAGrL,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,MAAM;IACJoP,SAAS,GAAGxC,oBAAoB;IAChC4Q,MAAM,GAAG,KAAK;IACdJ,IAAI,GAAG,IAAI;IACXO,MAAM,GAAG,IAAI;IACbD,KAAK,GAAG,IAAI;IACZE,OAAO,GAAG,IAAI;IACdI,OAAO,GAAG,EAAE;IACZpP,UAAU,GAAG,CAAC,CAAC;IACfK,MAAM,GAAG,CAAC;EACZ,CAAC,GAAGqF,MAAM;EACV,IAAI,CAACkR,cAAc,EAAE;EACrB,MAAM;IACJlT,MAAM;IACNC,QAAQ;IACRC;EACF,CAAC,GAAGgT,cAAc;EAClB,OAAO3I,WAAW,CAAC9c,cAAc,CAAC;IAChC0iB,IAAI,EAAE;EACR,CAAC,EAAE+C,cAAc,CAAC,EAAE,MAAM;IACxB9J,SAAS,CAAC,0BAA0B,EAAE;MACpC8J,cAAc;MACdlR;IACF,CAAC,CAAC;IACF,IAAIhI,MAAM,CAACT,QAAQ,EAAE;MACnB,IAAI6R,KAAK,EAAE;QACT9O,UAAU,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC9G,MAAM,CAACwE,MAAM,CAACZ,gBAAgB,EAAE,SAAS,CAAC,CAAC5D,MAAM,CAAC8V,OAAO,IAAI7P,YAAY,CAAC,CAAC,CAAC;MACjH,CAAC,MAAM;QACLa,UAAU,CAAC,aAAa,CAAC,GAAG,MAAM;QAClCA,UAAU,CAAC,WAAW,CAAC,GAAG,OAAO;MACnC;IACF;IACA,OAAO6O,qBAAqB,CAAC;MAC3BtJ,KAAK,EAAE;QACLgJ,IAAI,EAAEsB,WAAW,CAACjM,IAAI,CAAC;QACvB4K,IAAI,EAAEA,IAAI,GAAGqB,WAAW,CAACrB,IAAI,CAAC5K,IAAI,CAAC,GAAG;UACpC6K,KAAK,EAAE,KAAK;UACZrN,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,IAAI;UACZuC,IAAI,EAAE,CAAC;QACT;MACF,CAAC;MACDF,MAAM;MACNC,QAAQ;MACRnD,SAAS,EAAErP,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6M,oBAAoB,CAAC,EAAEwC,SAAS,CAAC;MAC9EoO,MAAM;MACNE,KAAK;MACLC,MAAM;MACNC,OAAO;MACPnK,KAAK,EAAE;QACL7E,UAAU;QACVK,MAAM;QACN+O;MACF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,IAAI0H,eAAe,GAAG;EACpB5U,MAAMA,CAAA,EAAG;IACP,OAAO;MACL0B,IAAI,EAAE6S,YAAY,CAACI,MAAM;IAC3B,CAAC;EACH,CAAC;EACDzU,KAAKA,CAAA,EAAG;IACN,OAAO;MACL2U,yBAAyBA,CAACrK,WAAW,EAAE;QACrCA,WAAW,CAAC6G,YAAY,GAAGkC,MAAM;QACjC/I,WAAW,CAAC8G,YAAY,GAAGgD,MAAM;QACjC,OAAO9J,WAAW;MACpB;IACF,CAAC;EACH,CAAC;EACDF,QAAQA,CAACwK,YAAY,EAAE;IACrBA,YAAY,CAAC1J,KAAK,GAAG,UAAU5H,MAAM,EAAE;MACrC,MAAM;QACJhG,IAAI,GAAGxL,QAAQ;QACf2e,QAAQ,GAAGA,CAAA,KAAM,CAAC;MACpB,CAAC,GAAGnN,MAAM;MACV,OAAO+P,MAAM,CAAC/V,IAAI,EAAEmT,QAAQ,CAAC;IAC/B,CAAC;IACDmE,YAAY,CAACC,8BAA8B,GAAG,UAAUvX,IAAI,EAAE6V,QAAQ,EAAE;MACtE,MAAM;QACJ5R,QAAQ;QACRmL,KAAK;QACLE,OAAO;QACPtL,MAAM;QACNlD,SAAS;QACToO,MAAM;QACNJ,IAAI;QACJO,MAAM;QACNlK;MACF,CAAC,GAAG0Q,QAAQ;MACZ,OAAO,IAAIhI,OAAO,CAAC,CAAC8C,OAAO,EAAE7C,MAAM,KAAK;QACtCD,OAAO,CAAC6I,GAAG,CAAC,CAAChG,QAAQ,CAACzM,QAAQ,EAAED,MAAM,CAAC,EAAE8K,IAAI,CAAC7K,QAAQ,GAAGyM,QAAQ,CAAC5B,IAAI,CAAC7K,QAAQ,EAAE6K,IAAI,CAAC9K,MAAM,CAAC,GAAG6J,OAAO,CAAC8C,OAAO,CAAC;UAC9G5B,KAAK,EAAE,KAAK;UACZrN,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE,GAAG;UACXuC,IAAI,EAAE,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAACyS,IAAI,CAAC9Z,IAAI,IAAI;UAChB,IAAI,CAACgS,IAAI,EAAEC,IAAI,CAAC,GAAGjS,IAAI;UACvB8T,OAAO,CAAC,CAAC3Q,IAAI,EAAEmP,qBAAqB,CAAC;YACnCtJ,KAAK,EAAE;cACLgJ,IAAI;cACJC;YACF,CAAC;YACD9K,MAAM;YACNC,QAAQ;YACRnD,SAAS;YACToO,MAAM;YACNG,MAAM;YACND,KAAK;YACLE,OAAO;YACPnK,KAAK;YACLoK,SAAS,EAAE;UACb,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAACsH,KAAK,CAAC/I,MAAM,CAAC;MAClB,CAAC,CAAC;IACJ,CAAC;IACDwJ,YAAY,CAACE,oBAAoB,GAAG,UAAU/V,KAAK,EAAE;MACnD,IAAI;QACFoC,QAAQ;QACRvD,UAAU;QACVuO,IAAI;QACJ/N,SAAS;QACTH;MACF,CAAC,GAAGc,KAAK;MACT,MAAMsO,WAAW,GAAGrP,UAAU,CAACC,MAAM,CAAC;MACtC,IAAIoP,WAAW,CAACpe,MAAM,GAAG,CAAC,EAAE;QAC1B2O,UAAU,CAAC,OAAO,CAAC,GAAGyP,WAAW;MACnC;MACA,IAAI0H,SAAS;MACb,IAAI5W,qBAAqB,CAACC,SAAS,CAAC,EAAE;QACpC2W,SAAS,GAAGlK,YAAY,CAAC,mCAAmC,EAAE;UAC5DsB,IAAI;UACJ/N,SAAS;UACTE,cAAc,EAAE6N,IAAI,CAACnN,KAAK;UAC1BT,SAAS,EAAE4N,IAAI,CAACnN;QAClB,CAAC,CAAC;MACJ;MACAmC,QAAQ,CAACtS,IAAI,CAACkmB,SAAS,IAAI5I,IAAI,CAAC3K,IAAI,CAAC;MACrC,OAAO;QACLL,QAAQ;QACRvD;MACF,CAAC;IACH,CAAC;EACH;AACF,CAAC;AAED,IAAIoX,MAAM,GAAG;EACXlV,MAAMA,CAAA,EAAG;IACP,OAAO;MACLmV,KAAKA,CAACC,SAAS,EAAE;QACf,IAAI5R,MAAM,GAAGtU,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqL,SAAS,GAAGrL,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACnF,MAAM;UACJge,OAAO,GAAG;QACZ,CAAC,GAAG1J,MAAM;QACV,OAAOuI,WAAW,CAAC;UACjB4F,IAAI,EAAE;QACR,CAAC,EAAE,MAAM;UACP/G,SAAS,CAAC,0BAA0B,EAAE;YACpCwK,SAAS;YACT5R;UACF,CAAC,CAAC;UACF,IAAInC,QAAQ,GAAG,EAAE;UACjB+T,SAAS,CAAC3K,IAAI,IAAI;YAChB3Z,KAAK,CAACC,OAAO,CAAC0Z,IAAI,CAAC,GAAGA,IAAI,CAACvT,GAAG,CAACC,CAAC,IAAI;cAClCkK,QAAQ,GAAGA,QAAQ,CAACrK,MAAM,CAACG,CAAC,CAAC8U,QAAQ,CAAC;YACxC,CAAC,CAAC,GAAG5K,QAAQ,GAAGA,QAAQ,CAACrK,MAAM,CAACyT,IAAI,CAACwB,QAAQ,CAAC;UAChD,CAAC,CAAC;UACF,OAAO,CAAC;YACN7K,GAAG,EAAE,MAAM;YACXtD,UAAU,EAAE;cACV0P,KAAK,EAAE,CAAC,EAAE,CAACxW,MAAM,CAACwE,MAAM,CAACb,SAAS,EAAE,SAAS,CAAC,EAAE,GAAGuS,OAAO,CAAC,CAAClc,IAAI,CAAC,GAAG;YACtE,CAAC;YACDqQ;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC;EACH;AACF,CAAC;AAED,IAAIgU,aAAa,GAAG;EAClBrV,MAAMA,CAAA,EAAG;IACP,OAAO;MACLyC,OAAOA,CAAC0K,OAAO,EAAE;QACf,IAAI3J,MAAM,GAAGtU,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqL,SAAS,GAAGrL,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACnF,MAAM;UACJ0d,KAAK,GAAG,IAAI;UACZM,OAAO,GAAG,EAAE;UACZpP,UAAU,GAAG,CAAC,CAAC;UACfK,MAAM,GAAG,CAAC;QACZ,CAAC,GAAGqF,MAAM;QACV,OAAOuI,WAAW,CAAC;UACjB4F,IAAI,EAAE,SAAS;UACfxE;QACF,CAAC,EAAE,MAAM;UACPvC,SAAS,CAAC,0BAA0B,EAAE;YACpCuC,OAAO;YACP3J;UACF,CAAC,CAAC;UACF,OAAOiK,yBAAyB,CAAC;YAC/BN,OAAO,EAAEA,OAAO,CAACpK,QAAQ,CAAC,CAAC;YAC3B6J,KAAK;YACLjK,KAAK,EAAE;cACL7E,UAAU;cACVK,MAAM;cACN+O,OAAO,EAAE,CAAC,EAAE,CAAClW,MAAM,CAACwE,MAAM,CAACb,SAAS,EAAE,iBAAiB,CAAC,EAAE,GAAGuS,OAAO;YACtE;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC;EACH;AACF,CAAC;AAED,IAAIoI,UAAU,GAAG;EACftV,MAAMA,CAAA,EAAG;IACP,OAAO;MACLuV,IAAIA,CAACpI,OAAO,EAAE;QACZ,IAAI3J,MAAM,GAAGtU,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqL,SAAS,GAAGrL,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACnF,MAAM;UACJoP,SAAS,GAAGxC,oBAAoB;UAChC8Q,KAAK,GAAG,IAAI;UACZM,OAAO,GAAG,EAAE;UACZpP,UAAU,GAAG,CAAC,CAAC;UACfK,MAAM,GAAG,CAAC;QACZ,CAAC,GAAGqF,MAAM;QACV,OAAOuI,WAAW,CAAC;UACjB4F,IAAI,EAAE,MAAM;UACZxE;QACF,CAAC,EAAE,MAAM;UACPvC,SAAS,CAAC,0BAA0B,EAAE;YACpCuC,OAAO;YACP3J;UACF,CAAC,CAAC;UACF,OAAO8J,sBAAsB,CAAC;YAC5BH,OAAO;YACP7O,SAAS,EAAErP,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6M,oBAAoB,CAAC,EAAEwC,SAAS,CAAC;YAC9EsO,KAAK;YACLjK,KAAK,EAAE;cACL7E,UAAU;cACVK,MAAM;cACN+O,OAAO,EAAE,CAAC,EAAE,CAAClW,MAAM,CAACwE,MAAM,CAACb,SAAS,EAAE,cAAc,CAAC,EAAE,GAAGuS,OAAO;YACnE;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC;EACH,CAAC;EACD5C,QAAQA,CAACwK,YAAY,EAAE;IACrBA,YAAY,CAACU,kBAAkB,GAAG,UAAUhY,IAAI,EAAE6V,QAAQ,EAAE;MAC1D,MAAM;QACJzG,KAAK;QACLtO,SAAS;QACTqE;MACF,CAAC,GAAG0Q,QAAQ;MACZ,IAAInU,KAAK,GAAG,IAAI;MAChB,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAI1M,KAAK,EAAE;QACT,MAAMgjB,gBAAgB,GAAGC,QAAQ,CAACC,gBAAgB,CAACnY,IAAI,CAAC,CAACoY,QAAQ,EAAE,EAAE,CAAC;QACtE,MAAMC,kBAAkB,GAAGrY,IAAI,CAACsY,qBAAqB,CAAC,CAAC;QACvD5W,KAAK,GAAG2W,kBAAkB,CAAC3W,KAAK,GAAGuW,gBAAgB;QACnDtW,MAAM,GAAG0W,kBAAkB,CAAC1W,MAAM,GAAGsW,gBAAgB;MACvD;MACA,IAAIja,MAAM,CAACT,QAAQ,IAAI,CAAC6R,KAAK,EAAE;QAC7BjK,KAAK,CAAC7E,UAAU,CAAC,aAAa,CAAC,GAAG,MAAM;MAC1C;MACA,OAAOuN,OAAO,CAAC8C,OAAO,CAAC,CAAC3Q,IAAI,EAAE8P,sBAAsB,CAAC;QACnDH,OAAO,EAAE3P,IAAI,CAAChB,SAAS;QACvB0C,KAAK;QACLC,MAAM;QACNb,SAAS;QACTsO,KAAK;QACLjK,KAAK;QACLoK,SAAS,EAAE;MACb,CAAC,CAAC,CAAC,CAAC;IACN,CAAC;EACH;AACF,CAAC;AAED,MAAMgJ,qBAAqB,GAAG,IAAI5lB,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC;AACxD,MAAM6lB,uBAAuB,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;AAClD,MAAMC,6BAA6B,GAAGhnB,cAAc,CAACA,cAAc,CAACA,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE;EACpGinB,WAAW,EAAE;IACXvhB,MAAM,EAAE,KAAK;IACb,GAAG,EAAE;EACP;AACF,CAAC,CAAC,EAAEC,EAAE,CAAC,EAAEwC,EAAE,CAAC,EAAEb,EAAE,CAAC;AACjB,MAAM4f,4BAA4B,GAAGtoB,MAAM,CAACa,IAAI,CAACunB,6BAA6B,CAAC,CAACxlB,MAAM,CAAC,CAACsN,GAAG,EAAEzD,GAAG,KAAK;EACnGyD,GAAG,CAACzD,GAAG,CAAC8b,WAAW,CAAC,CAAC,CAAC,GAAGH,6BAA6B,CAAC3b,GAAG,CAAC;EAC3D,OAAOyD,GAAG;AACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,MAAMsY,2BAA2B,GAAGxoB,MAAM,CAACa,IAAI,CAACynB,4BAA4B,CAAC,CAAC1lB,MAAM,CAAC,CAACsN,GAAG,EAAEuY,UAAU,KAAK;EACxG,MAAMC,OAAO,GAAGJ,4BAA4B,CAACG,UAAU,CAAC;EACxDvY,GAAG,CAACuY,UAAU,CAAC,GAAGC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG1oB,MAAM,CAAC2oB,OAAO,CAACD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpE,OAAOxY,GAAG;AACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,SAAS0Y,mBAAmBA,CAACtJ,OAAO,EAAE;EACpC,MAAMuJ,OAAO,GAAGvJ,OAAO,CAACtc,OAAO,CAACklB,qBAAqB,EAAE,EAAE,CAAC;EAC1D,MAAMY,SAAS,GAAG3T,WAAW,CAAC0T,OAAO,EAAE,CAAC,CAAC;EACzC,MAAME,YAAY,GAAGD,SAAS,IAAIX,uBAAuB,CAAC,CAAC,CAAC,IAAIW,SAAS,IAAIX,uBAAuB,CAAC,CAAC,CAAC;EACvG,MAAMa,SAAS,GAAGH,OAAO,CAACvnB,MAAM,KAAK,CAAC,GAAGunB,OAAO,CAAC,CAAC,CAAC,KAAKA,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;EAC1E,OAAO;IACL3oB,KAAK,EAAE8oB,SAAS,GAAGjU,KAAK,CAAC8T,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG9T,KAAK,CAAC8T,OAAO,CAAC;IACrDI,WAAW,EAAEF,YAAY,IAAIC;EAC/B,CAAC;AACH;AACA,SAASE,SAASA,CAACT,UAAU,EAAEU,UAAU,EAAE;EACzC,MAAMC,mBAAmB,GAAGX,UAAU,CAACzlB,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAACulB,WAAW,CAAC,CAAC;EAChF,MAAMc,iBAAiB,GAAGxB,QAAQ,CAACsB,UAAU,CAAC;EAC9C,MAAMG,mBAAmB,GAAGC,KAAK,CAACF,iBAAiB,CAAC,GAAG,QAAQ,GAAGA,iBAAiB;EACnF,OAAO,CAACf,4BAA4B,CAACc,mBAAmB,CAAC,IAAI,CAAC,CAAC,EAAEE,mBAAmB,CAAC,IAAId,2BAA2B,CAACY,mBAAmB,CAAC;AAC3I;AACA,SAASI,kBAAkBA,CAAC7Z,IAAI,EAAE8Z,QAAQ,EAAE;EAC1C,MAAMC,gBAAgB,GAAG,EAAE,CAACvgB,MAAM,CAACW,8BAA8B,CAAC,CAACX,MAAM,CAACsgB,QAAQ,CAACzmB,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EACrG,OAAO,IAAIwa,OAAO,CAAC,CAAC8C,OAAO,EAAE7C,MAAM,KAAK;IACtC,IAAI9N,IAAI,CAACvD,YAAY,CAACsd,gBAAgB,CAAC,KAAK,IAAI,EAAE;MAChD;MACA,OAAOpJ,OAAO,CAAC,CAAC;IAClB;IACA,MAAM9M,QAAQ,GAAGhE,OAAO,CAACG,IAAI,CAAC6D,QAAQ,CAAC;IACvC,MAAMmW,6BAA6B,GAAGnW,QAAQ,CAACxS,MAAM,CAACkX,IAAI,IAAIA,IAAI,CAAC9L,YAAY,CAACvC,sBAAsB,CAAC,KAAK4f,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxH,MAAMnZ,MAAM,GAAGpM,MAAM,CAAC4jB,gBAAgB,CAACnY,IAAI,EAAE8Z,QAAQ,CAAC;IACtD,MAAMhB,UAAU,GAAGnY,MAAM,CAACsZ,gBAAgB,CAAC,aAAa,CAAC;IACzD,MAAMC,eAAe,GAAGpB,UAAU,CAAC1K,KAAK,CAACvS,mBAAmB,CAAC;IAC7D,MAAM2d,UAAU,GAAG7Y,MAAM,CAACsZ,gBAAgB,CAAC,aAAa,CAAC;IACzD,MAAMtK,OAAO,GAAGhP,MAAM,CAACsZ,gBAAgB,CAAC,SAAS,CAAC;IAClD,IAAID,6BAA6B,IAAI,CAACE,eAAe,EAAE;MACrD;MACA;MACA;MACAla,IAAI,CAACma,WAAW,CAACH,6BAA6B,CAAC;MAC/C,OAAOrJ,OAAO,CAAC,CAAC;IAClB,CAAC,MAAM,IAAIuJ,eAAe,IAAIvK,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,EAAE,EAAE;MAClE,MAAMA,OAAO,GAAGhP,MAAM,CAACsZ,gBAAgB,CAAC,SAAS,CAAC;MAClD,IAAIjW,MAAM,GAAGuV,SAAS,CAACT,UAAU,EAAEU,UAAU,CAAC;MAC9C,MAAM;QACJjpB,KAAK,EAAE6pB,QAAQ;QACfd;MACF,CAAC,GAAGL,mBAAmB,CAACtJ,OAAO,CAAC;MAChC,MAAM0K,IAAI,GAAGH,eAAe,CAAC,CAAC,CAAC,CAACI,UAAU,CAAC,aAAa,CAAC;MACzD,IAAIrW,QAAQ,GAAGuE,SAAS,CAACxE,MAAM,EAAEoW,QAAQ,CAAC;MAC1C,IAAIG,cAAc,GAAGtW,QAAQ;MAC7B,IAAIoW,IAAI,EAAE;QACR,MAAMG,SAAS,GAAG3R,YAAY,CAACuR,QAAQ,CAAC;QACxC,IAAII,SAAS,CAACvW,QAAQ,IAAIuW,SAAS,CAACxW,MAAM,EAAE;UAC1CC,QAAQ,GAAGuW,SAAS,CAACvW,QAAQ;UAC7BD,MAAM,GAAGwW,SAAS,CAACxW,MAAM;QAC3B;MACF;;MAEA;MACA;MACA,IAAIC,QAAQ,IAAI,CAACqV,WAAW,KAAK,CAACU,6BAA6B,IAAIA,6BAA6B,CAACvd,YAAY,CAACrC,WAAW,CAAC,KAAK4J,MAAM,IAAIgW,6BAA6B,CAACvd,YAAY,CAACpC,SAAS,CAAC,KAAKkgB,cAAc,CAAC,EAAE;QAClNva,IAAI,CAACjB,YAAY,CAACgb,gBAAgB,EAAEQ,cAAc,CAAC;QACnD,IAAIP,6BAA6B,EAAE;UACjC;UACAha,IAAI,CAACma,WAAW,CAACH,6BAA6B,CAAC;QACjD;QACA,MAAMS,IAAI,GAAGpF,SAAS,CAAC,CAAC;QACxB,MAAM;UACJlQ;QACF,CAAC,GAAGsV,IAAI;QACRtV,KAAK,CAAC7E,UAAU,CAACpG,sBAAsB,CAAC,GAAG4f,QAAQ;QACnDpJ,QAAQ,CAACzM,QAAQ,EAAED,MAAM,CAAC,CAAC2S,IAAI,CAAC9H,IAAI,IAAI;UACtC,MAAMJ,QAAQ,GAAGU,qBAAqB,CAAC1d,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEgpB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;YAClF5U,KAAK,EAAE;cACLgJ,IAAI;cACJC,IAAI,EAAE7F,kBAAkB,CAAC;YAC3B,CAAC;YACDjF,MAAM;YACNC,QAAQ,EAAEsW,cAAc;YACxBpV,KAAK;YACLoK,SAAS,EAAE;UACb,CAAC,CAAC,CAAC;UACH,MAAMhT,OAAO,GAAG/H,QAAQ,CAACkd,eAAe,CAAC,4BAA4B,EAAE,KAAK,CAAC;UAC7E,IAAIoI,QAAQ,KAAK,UAAU,EAAE;YAC3B9Z,IAAI,CAACT,YAAY,CAAChD,OAAO,EAAEyD,IAAI,CAAC8U,UAAU,CAAC;UAC7C,CAAC,MAAM;YACL9U,IAAI,CAAC+R,WAAW,CAACxV,OAAO,CAAC;UAC3B;UACAA,OAAO,CAAC2V,SAAS,GAAGzD,QAAQ,CAAC/U,GAAG,CAACkO,IAAI,IAAIlE,MAAM,CAACkE,IAAI,CAAC,CAAC,CAACpU,IAAI,CAAC,IAAI,CAAC;UACjEwM,IAAI,CAAC6S,eAAe,CAACkH,gBAAgB,CAAC;UACtCpJ,OAAO,CAAC,CAAC;QACX,CAAC,CAAC,CAACkG,KAAK,CAAC/I,MAAM,CAAC;MAClB,CAAC,MAAM;QACL6C,OAAO,CAAC,CAAC;MACX;IACF,CAAC,MAAM;MACLA,OAAO,CAAC,CAAC;IACX;EACF,CAAC,CAAC;AACJ;AACA,SAAStd,OAAOA,CAAC2M,IAAI,EAAE;EACrB,OAAO6N,OAAO,CAAC6I,GAAG,CAAC,CAACmD,kBAAkB,CAAC7Z,IAAI,EAAE,UAAU,CAAC,EAAE6Z,kBAAkB,CAAC7Z,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;AACjG;AACA,SAAS0a,WAAWA,CAAC1a,IAAI,EAAE;EACzB,OAAOA,IAAI,CAACoS,UAAU,KAAKle,QAAQ,CAACY,IAAI,IAAI,CAAC,CAAC0F,mCAAmC,CAACtF,OAAO,CAAC8K,IAAI,CAACX,OAAO,CAACC,WAAW,CAAC,CAAC,CAAC,IAAI,CAACU,IAAI,CAACvD,YAAY,CAACvC,sBAAsB,CAAC,KAAK,CAAC8F,IAAI,CAACoS,UAAU,IAAIpS,IAAI,CAACoS,UAAU,CAAC/S,OAAO,KAAK,KAAK,CAAC;AAChO;AACA,SAAS7B,oBAAoBA,CAACwY,IAAI,EAAE;EAClC,IAAI,CAACphB,MAAM,EAAE;EACb,OAAO,IAAIiZ,OAAO,CAAC,CAAC8C,OAAO,EAAE7C,MAAM,KAAK;IACtC,MAAM6M,UAAU,GAAG9a,OAAO,CAACmW,IAAI,CAACS,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAACplB,MAAM,CAACqpB,WAAW,CAAC,CAAChhB,GAAG,CAACrG,OAAO,CAAC;IACvF,MAAM2d,GAAG,GAAGC,IAAI,CAACF,KAAK,CAAC,sBAAsB,CAAC;IAC9CyC,kBAAkB,CAAC,CAAC;IACpB3F,OAAO,CAAC6I,GAAG,CAACiE,UAAU,CAAC,CAAChE,IAAI,CAAC,MAAM;MACjC3F,GAAG,CAAC,CAAC;MACLyC,iBAAiB,CAAC,CAAC;MACnB9C,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,CAACkG,KAAK,CAAC,MAAM;MACb7F,GAAG,CAAC,CAAC;MACLyC,iBAAiB,CAAC,CAAC;MACnB3F,MAAM,CAAC,CAAC;IACV,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,IAAI8M,cAAc,GAAG;EACnBlY,KAAKA,CAAA,EAAG;IACN,OAAO;MACL2U,yBAAyBA,CAACrK,WAAW,EAAE;QACrCA,WAAW,CAAC+G,sBAAsB,GAAGvW,oBAAoB;QACzD,OAAOwP,WAAW;MACpB;IACF,CAAC;EACH,CAAC;EACDF,QAAQA,CAACV,SAAS,EAAE;IAClBA,SAAS,CAACyO,kBAAkB,GAAG,UAAU7U,MAAM,EAAE;MAC/C,MAAM;QACJhG,IAAI,GAAGxL;MACT,CAAC,GAAGwR,MAAM;MACV,IAAIhI,MAAM,CAACR,oBAAoB,EAAE;QAC/BA,oBAAoB,CAACwC,IAAI,CAAC;MAC5B;IACF,CAAC;EACH;AACF,CAAC;AAED,IAAI8a,UAAU,GAAG,KAAK;AACtB,IAAIC,kBAAkB,GAAG;EACvBvY,MAAMA,CAAA,EAAG;IACP,OAAO;MACLC,GAAG,EAAE;QACHuY,OAAOA,CAAA,EAAG;UACRxH,kBAAkB,CAAC,CAAC;UACpBsH,UAAU,GAAG,IAAI;QACnB;MACF;IACF,CAAC;EACH,CAAC;EACDpY,KAAKA,CAAA,EAAG;IACN,OAAO;MACLuY,SAASA,CAAA,EAAG;QACVtH,OAAO,CAAC5G,UAAU,CAAC,2BAA2B,EAAE,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC;MACDY,MAAMA,CAAA,EAAG;QACP6G,UAAU,CAAC,CAAC;MACd,CAAC;MACDxG,KAAKA,CAAChI,MAAM,EAAE;QACZ,MAAM;UACJgO;QACF,CAAC,GAAGhO,MAAM;QACV,IAAI8U,UAAU,EAAE;UACdrH,iBAAiB,CAAC,CAAC;QACrB,CAAC,MAAM;UACLE,OAAO,CAAC5G,UAAU,CAAC,2BAA2B,EAAE;YAC9CiH;UACF,CAAC,CAAC,CAAC;QACL;MACF;IACF,CAAC;EACH;AACF,CAAC;AAED,MAAMkH,oBAAoB,GAAGC,eAAe,IAAI;EAC9C,IAAIra,SAAS,GAAG;IACdvC,IAAI,EAAE,EAAE;IACRpF,CAAC,EAAE,CAAC;IACJqF,CAAC,EAAE,CAAC;IACJE,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,KAAK;IACZF,MAAM,EAAE;EACV,CAAC;EACD,OAAO0c,eAAe,CAACvC,WAAW,CAAC,CAAC,CAAC1Y,KAAK,CAAC,GAAG,CAAC,CAACjN,MAAM,CAAC,CAACsN,GAAG,EAAE6a,CAAC,KAAK;IACjE,MAAM9T,KAAK,GAAG8T,CAAC,CAACxC,WAAW,CAAC,CAAC,CAAC1Y,KAAK,CAAC,GAAG,CAAC;IACxC,MAAMwF,KAAK,GAAG4B,KAAK,CAAC,CAAC,CAAC;IACtB,IAAI4B,IAAI,GAAG5B,KAAK,CAAC7T,KAAK,CAAC,CAAC,CAAC,CAACD,IAAI,CAAC,GAAG,CAAC;IACnC,IAAIkS,KAAK,IAAIwD,IAAI,KAAK,GAAG,EAAE;MACzB3I,GAAG,CAAC7B,KAAK,GAAG,IAAI;MAChB,OAAO6B,GAAG;IACZ;IACA,IAAImF,KAAK,IAAIwD,IAAI,KAAK,GAAG,EAAE;MACzB3I,GAAG,CAAC5B,KAAK,GAAG,IAAI;MAChB,OAAO4B,GAAG;IACZ;IACA2I,IAAI,GAAGmS,UAAU,CAACnS,IAAI,CAAC;IACvB,IAAI0Q,KAAK,CAAC1Q,IAAI,CAAC,EAAE;MACf,OAAO3I,GAAG;IACZ;IACA,QAAQmF,KAAK;MACX,KAAK,MAAM;QACTnF,GAAG,CAAChC,IAAI,GAAGgC,GAAG,CAAChC,IAAI,GAAG2K,IAAI;QAC1B;MACF,KAAK,QAAQ;QACX3I,GAAG,CAAChC,IAAI,GAAGgC,GAAG,CAAChC,IAAI,GAAG2K,IAAI;QAC1B;MACF,KAAK,MAAM;QACT3I,GAAG,CAACpH,CAAC,GAAGoH,GAAG,CAACpH,CAAC,GAAG+P,IAAI;QACpB;MACF,KAAK,OAAO;QACV3I,GAAG,CAACpH,CAAC,GAAGoH,GAAG,CAACpH,CAAC,GAAG+P,IAAI;QACpB;MACF,KAAK,IAAI;QACP3I,GAAG,CAAC/B,CAAC,GAAG+B,GAAG,CAAC/B,CAAC,GAAG0K,IAAI;QACpB;MACF,KAAK,MAAM;QACT3I,GAAG,CAAC/B,CAAC,GAAG+B,GAAG,CAAC/B,CAAC,GAAG0K,IAAI;QACpB;MACF,KAAK,QAAQ;QACX3I,GAAG,CAAC9B,MAAM,GAAG8B,GAAG,CAAC9B,MAAM,GAAGyK,IAAI;QAC9B;IACJ;IACA,OAAO3I,GAAG;EACZ,CAAC,EAAEO,SAAS,CAAC;AACf,CAAC;AACD,IAAIwa,eAAe,GAAG;EACpB9Y,MAAMA,CAAA,EAAG;IACP,OAAO;MACL2L,KAAK,EAAE;QACLrN,SAAS,EAAEqa,eAAe,IAAI;UAC5B,OAAOD,oBAAoB,CAACC,eAAe,CAAC;QAC9C;MACF;IACF,CAAC;EACH,CAAC;EACDzY,KAAKA,CAAA,EAAG;IACN,OAAO;MACL6Y,mBAAmBA,CAACvO,WAAW,EAAEhN,IAAI,EAAE;QACrC,MAAMmb,eAAe,GAAGnb,IAAI,CAACvD,YAAY,CAAC,mBAAmB,CAAC;QAC9D,IAAI0e,eAAe,EAAE;UACnBnO,WAAW,CAAClM,SAAS,GAAGoa,oBAAoB,CAACC,eAAe,CAAC;QAC/D;QACA,OAAOnO,WAAW;MACpB;IACF,CAAC;EACH,CAAC;EACDF,QAAQA,CAACV,SAAS,EAAE;IAClBA,SAAS,CAACoP,iCAAiC,GAAG,UAAU3e,IAAI,EAAE;MAC5D,IAAI;QACFgS,IAAI;QACJ/N,SAAS;QACTE,cAAc;QACdC;MACF,CAAC,GAAGpE,IAAI;MACR,MAAMqE,KAAK,GAAG;QACZJ,SAAS,EAAE,YAAY,CAACtH,MAAM,CAACwH,cAAc,GAAG,CAAC,EAAE,OAAO;MAC5D,CAAC;MACD,MAAMG,cAAc,GAAG,YAAY,CAAC3H,MAAM,CAACsH,SAAS,CAAC3H,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,CAACK,MAAM,CAACsH,SAAS,CAACtC,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC;MACjG,MAAM4C,UAAU,GAAG,QAAQ,CAAC5H,MAAM,CAACsH,SAAS,CAACvC,IAAI,GAAG,EAAE,IAAIuC,SAAS,CAACpC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAClF,MAAM,CAACsH,SAAS,CAACvC,IAAI,GAAG,EAAE,IAAIuC,SAAS,CAACnC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;MACzJ,MAAM0C,WAAW,GAAG,SAAS,CAAC7H,MAAM,CAACsH,SAAS,CAACrC,MAAM,EAAE,OAAO,CAAC;MAC/D,MAAM6C,KAAK,GAAG;QACZR,SAAS,EAAE,EAAE,CAACtH,MAAM,CAAC2H,cAAc,EAAE,GAAG,CAAC,CAAC3H,MAAM,CAAC4H,UAAU,EAAE,GAAG,CAAC,CAAC5H,MAAM,CAAC6H,WAAW;MACtF,CAAC;MACD,MAAME,IAAI,GAAG;QACXT,SAAS,EAAE,YAAY,CAACtH,MAAM,CAACyH,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ;MAC7D,CAAC;MACD,MAAM0Z,UAAU,GAAG;QACjBzZ,KAAK;QACLI,KAAK;QACLC;MACF,CAAC;MACD,OAAO;QACLqC,GAAG,EAAE,GAAG;QACRtD,UAAU,EAAE7O,cAAc,CAAC,CAAC,CAAC,EAAEkpB,UAAU,CAACzZ,KAAK,CAAC;QAChD2C,QAAQ,EAAE,CAAC;UACTD,GAAG,EAAE,GAAG;UACRtD,UAAU,EAAE7O,cAAc,CAAC,CAAC,CAAC,EAAEkpB,UAAU,CAACrZ,KAAK,CAAC;UAChDuC,QAAQ,EAAE,CAAC;YACTD,GAAG,EAAEiL,IAAI,CAAC3K,IAAI,CAACN,GAAG;YAClBC,QAAQ,EAAEgL,IAAI,CAAC3K,IAAI,CAACL,QAAQ;YAC5BvD,UAAU,EAAE7O,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEod,IAAI,CAAC3K,IAAI,CAAC5D,UAAU,CAAC,EAAEqa,UAAU,CAACpZ,IAAI;UACtF,CAAC;QACH,CAAC;MACH,CAAC;IACH,CAAC;EACH;AACF,CAAC;AAED,MAAMka,SAAS,GAAG;EAChBtiB,CAAC,EAAE,CAAC;EACJqF,CAAC,EAAE,CAAC;EACJkD,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE;AACV,CAAC;AACD,SAAS+Z,SAASA,CAACjN,QAAQ,EAAE;EAC3B,IAAIkN,KAAK,GAAGjqB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKqL,SAAS,GAAGrL,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACpF,IAAI+c,QAAQ,CAACnO,UAAU,KAAKmO,QAAQ,CAACnO,UAAU,CAAC+P,IAAI,IAAIsL,KAAK,CAAC,EAAE;IAC9DlN,QAAQ,CAACnO,UAAU,CAAC+P,IAAI,GAAG,OAAO;EACpC;EACA,OAAO5B,QAAQ;AACjB;AACA,SAASmN,OAAOA,CAACnN,QAAQ,EAAE;EACzB,IAAIA,QAAQ,CAAC7K,GAAG,KAAK,GAAG,EAAE;IACxB,OAAO6K,QAAQ,CAAC5K,QAAQ;EAC1B,CAAC,MAAM;IACL,OAAO,CAAC4K,QAAQ,CAAC;EACnB;AACF;AACA,IAAIoN,KAAK,GAAG;EACVnZ,KAAKA,CAAA,EAAG;IACN,OAAO;MACL6Y,mBAAmBA,CAACvO,WAAW,EAAEhN,IAAI,EAAE;QACrC,MAAM8b,QAAQ,GAAG9b,IAAI,CAACvD,YAAY,CAAC,cAAc,CAAC;QAClD,MAAMqS,IAAI,GAAG,CAACgN,QAAQ,GAAG7S,kBAAkB,CAAC,CAAC,GAAGe,gBAAgB,CAAC8R,QAAQ,CAAC5b,KAAK,CAAC,GAAG,CAAC,CAACxG,GAAG,CAACrH,CAAC,IAAIA,CAAC,CAACoO,IAAI,CAAC,CAAC,CAAC,CAAC;QACxG,IAAI,CAACqO,IAAI,CAAC9K,MAAM,EAAE;UAChB8K,IAAI,CAAC9K,MAAM,GAAGgF,sBAAsB,CAAC,CAAC;QACxC;QACAgE,WAAW,CAAC8B,IAAI,GAAGA,IAAI;QACvB9B,WAAW,CAACqC,MAAM,GAAGrP,IAAI,CAACvD,YAAY,CAAC,iBAAiB,CAAC;QACzD,OAAOuQ,WAAW;MACpB;IACF,CAAC;EACH,CAAC;EACDF,QAAQA,CAACV,SAAS,EAAE;IAClBA,SAAS,CAAC2P,oBAAoB,GAAG,UAAUlf,IAAI,EAAE;MAC/C,IAAI;QACFgH,QAAQ;QACRvD,UAAU;QACVuO,IAAI;QACJC,IAAI;QACJO,MAAM,EAAE2M,cAAc;QACtBlb;MACF,CAAC,GAAGjE,IAAI;MACR,MAAM;QACJ6E,KAAK,EAAEua,SAAS;QAChB/X,IAAI,EAAEgY;MACR,CAAC,GAAGrN,IAAI;MACR,MAAM;QACJnN,KAAK,EAAEya,SAAS;QAChBjY,IAAI,EAAEkY;MACR,CAAC,GAAGtN,IAAI;MACR,MAAMuN,KAAK,GAAGtb,eAAe,CAAC;QAC5BD,SAAS;QACTE,cAAc,EAAEmb,SAAS;QACzBlb,SAAS,EAAEgb;MACb,CAAC,CAAC;MACF,MAAMK,QAAQ,GAAG;QACf1Y,GAAG,EAAE,MAAM;QACXtD,UAAU,EAAE7O,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEgqB,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UAC5DpL,IAAI,EAAE;QACR,CAAC;MACH,CAAC;MACD,MAAMkM,2BAA2B,GAAGL,QAAQ,CAACrY,QAAQ,GAAG;QACtDA,QAAQ,EAAEqY,QAAQ,CAACrY,QAAQ,CAACnK,GAAG,CAACgiB,SAAS;MAC3C,CAAC,GAAG,CAAC,CAAC;MACN,MAAMc,cAAc,GAAG;QACrB5Y,GAAG,EAAE,GAAG;QACRtD,UAAU,EAAE7O,cAAc,CAAC,CAAC,CAAC,EAAE4qB,KAAK,CAAC/a,KAAK,CAAC;QAC3CuC,QAAQ,EAAE,CAAC6X,SAAS,CAACjqB,cAAc,CAAC;UAClCmS,GAAG,EAAEsY,QAAQ,CAACtY,GAAG;UACjBtD,UAAU,EAAE7O,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEyqB,QAAQ,CAAC5b,UAAU,CAAC,EAAE+b,KAAK,CAAC9a,IAAI;QAChF,CAAC,EAAEgb,2BAA2B,CAAC,CAAC;MAClC,CAAC;MACD,MAAME,cAAc,GAAG;QACrB7Y,GAAG,EAAE,GAAG;QACRtD,UAAU,EAAE7O,cAAc,CAAC,CAAC,CAAC,EAAE4qB,KAAK,CAACnb,KAAK,CAAC;QAC3C2C,QAAQ,EAAE,CAAC2Y,cAAc;MAC3B,CAAC;MACD,MAAMnN,MAAM,GAAG,OAAO,CAAC7V,MAAM,CAACwiB,cAAc,IAAIvc,YAAY,CAAC,CAAC,CAAC;MAC/D,MAAMid,MAAM,GAAG,OAAO,CAACljB,MAAM,CAACwiB,cAAc,IAAIvc,YAAY,CAAC,CAAC,CAAC;MAC/D,MAAMkd,OAAO,GAAG;QACd/Y,GAAG,EAAE,MAAM;QACXtD,UAAU,EAAE7O,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEgqB,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UAC5D/b,EAAE,EAAE2P,MAAM;UACVuN,SAAS,EAAE,gBAAgB;UAC3BC,gBAAgB,EAAE;QACpB,CAAC,CAAC;QACFhZ,QAAQ,EAAE,CAACyY,QAAQ,EAAEG,cAAc;MACrC,CAAC;MACD,MAAMK,IAAI,GAAG;QACXlZ,GAAG,EAAE,MAAM;QACXC,QAAQ,EAAE,CAAC;UACTD,GAAG,EAAE,UAAU;UACftD,UAAU,EAAE;YACVZ,EAAE,EAAEgd;UACN,CAAC;UACD7Y,QAAQ,EAAE+X,OAAO,CAACQ,QAAQ;QAC5B,CAAC,EAAEO,OAAO;MACZ,CAAC;MACD9Y,QAAQ,CAACtS,IAAI,CAACurB,IAAI,EAAE;QAClBlZ,GAAG,EAAE,MAAM;QACXtD,UAAU,EAAE7O,cAAc,CAAC;UACzB4e,IAAI,EAAE,cAAc;UACpB,WAAW,EAAE,OAAO,CAAC7W,MAAM,CAACkjB,MAAM,EAAE,GAAG,CAAC;UACxC5N,IAAI,EAAE,OAAO,CAACtV,MAAM,CAAC6V,MAAM,EAAE,GAAG;QAClC,CAAC,EAAEoM,SAAS;MACd,CAAC,CAAC;MACF,OAAO;QACL5X,QAAQ;QACRvD;MACF,CAAC;IACH,CAAC;EACH;AACF,CAAC;AAED,IAAIyc,oBAAoB,GAAG;EACzBjQ,QAAQA,CAACV,SAAS,EAAE;IAClB,IAAI4Q,YAAY,GAAG,KAAK;IACxB,IAAIzoB,MAAM,CAAC0oB,UAAU,EAAE;MACrBD,YAAY,GAAGzoB,MAAM,CAAC0oB,UAAU,CAAC,kCAAkC,CAAC,CAACC,OAAO;IAC9E;IACA9Q,SAAS,CAAC+Q,mBAAmB,GAAG,YAAY;MAC1C,MAAMC,SAAS,GAAG,EAAE;MACpB,MAAMC,IAAI,GAAG;QACXhN,IAAI,EAAE;MACR,CAAC;MACD,MAAMiN,cAAc,GAAG;QACrBC,aAAa,EAAE,KAAK;QACpBC,WAAW,EAAE,YAAY;QACzBC,GAAG,EAAE;MACP,CAAC;;MAED;MACAL,SAAS,CAAC7rB,IAAI,CAAC;QACbqS,GAAG,EAAE,MAAM;QACXtD,UAAU,EAAE7O,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE4rB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UACvD7Y,CAAC,EAAE;QACL,CAAC;MACH,CAAC,CAAC;MACF,MAAMkZ,eAAe,GAAGjsB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6rB,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;QAC7E9c,aAAa,EAAE;MACjB,CAAC,CAAC;MACF,MAAMmd,GAAG,GAAG;QACV/Z,GAAG,EAAE,QAAQ;QACbtD,UAAU,EAAE7O,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE4rB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UACvDO,EAAE,EAAE,KAAK;UACTC,EAAE,EAAE,KAAK;UACT3tB,CAAC,EAAE;QACL,CAAC,CAAC;QACF2T,QAAQ,EAAE;MACZ,CAAC;MACD,IAAI,CAACmZ,YAAY,EAAE;QACjBW,GAAG,CAAC9Z,QAAQ,CAACtS,IAAI,CAAC;UAChBqS,GAAG,EAAE,SAAS;UACdtD,UAAU,EAAE7O,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6rB,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;YACjE9c,aAAa,EAAE,GAAG;YAClB4I,MAAM,EAAE;UACV,CAAC;QACH,CAAC,EAAE;UACDxF,GAAG,EAAE,SAAS;UACdtD,UAAU,EAAE7O,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEisB,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;YAClEtU,MAAM,EAAE;UACV,CAAC;QACH,CAAC,CAAC;MACJ;MACAgU,SAAS,CAAC7rB,IAAI,CAACosB,GAAG,CAAC;MACnBP,SAAS,CAAC7rB,IAAI,CAAC;QACbqS,GAAG,EAAE,MAAM;QACXtD,UAAU,EAAE7O,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE4rB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UACvDS,OAAO,EAAE,GAAG;UACZtZ,CAAC,EAAE;QACL,CAAC,CAAC;QACFX,QAAQ,EAAEmZ,YAAY,GAAG,EAAE,GAAG,CAAC;UAC7BpZ,GAAG,EAAE,SAAS;UACdtD,UAAU,EAAE7O,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEisB,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;YAClEtU,MAAM,EAAE;UACV,CAAC;QACH,CAAC;MACH,CAAC,CAAC;MACF,IAAI,CAAC4T,YAAY,EAAE;QACjB;QACAI,SAAS,CAAC7rB,IAAI,CAAC;UACbqS,GAAG,EAAE,MAAM;UACXtD,UAAU,EAAE7O,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE4rB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;YACvDS,OAAO,EAAE,GAAG;YACZtZ,CAAC,EAAE;UACL,CAAC,CAAC;UACFX,QAAQ,EAAE,CAAC;YACTD,GAAG,EAAE,SAAS;YACdtD,UAAU,EAAE7O,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEisB,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;cAClEtU,MAAM,EAAE;YACV,CAAC;UACH,CAAC;QACH,CAAC,CAAC;MACJ;MACA,OAAO;QACLxF,GAAG,EAAE,GAAG;QACRtD,UAAU,EAAE;UACV,OAAO,EAAE;QACX,CAAC;QACDuD,QAAQ,EAAEuZ;MACZ,CAAC;IACH,CAAC;EACH;AACF,CAAC;AAED,IAAIW,UAAU,GAAG;EACfrb,KAAKA,CAAA,EAAG;IACN,OAAO;MACL6Y,mBAAmBA,CAACvO,WAAW,EAAEhN,IAAI,EAAE;QACrC,MAAMge,UAAU,GAAGhe,IAAI,CAACvD,YAAY,CAAC,gBAAgB,CAAC;QACtD,MAAMyS,MAAM,GAAG8O,UAAU,KAAK,IAAI,GAAG,KAAK,GAAGA,UAAU,KAAK,EAAE,GAAG,IAAI,GAAGA,UAAU;QAClFhR,WAAW,CAAC,QAAQ,CAAC,GAAGkC,MAAM;QAC9B,OAAOlC,WAAW;MACpB;IACF,CAAC;EACH;AACF,CAAC;AAED,IAAIiR,OAAO,GAAG,CAAC1b,SAAS,EAAE6U,eAAe,EAAEM,MAAM,EAAEG,aAAa,EAAEC,UAAU,EAAE8C,cAAc,EAAEG,kBAAkB,EAAEO,eAAe,EAAEO,KAAK,EAAEkB,oBAAoB,EAAEgB,UAAU,CAAC;AAE3KzR,eAAe,CAAC2R,OAAO,EAAE;EACvBzR,SAAS,EAAE8B;AACb,CAAC,CAAC;AACF,MAAM4P,QAAQ,GAAG5P,GAAG,CAACX,MAAM;AAC3B,MAAMwQ,QAAQ,GAAG7P,GAAG,CAACtQ,MAAM;AAC3B,MAAMogB,SAAS,GAAG9P,GAAG,CAACZ,OAAO;AAC7B,MAAM2Q,KAAK,GAAG/P,GAAG,CAAC7L,GAAG;AACrB,MAAM6b,OAAO,GAAGhQ,GAAG,CAACH,KAAK;AACzB,MAAMoQ,oBAAoB,GAAGjQ,GAAG,CAACd,kBAAkB;AACnD,MAAMgR,QAAQ,GAAGlQ,GAAG,CAAC5K,MAAM;AAC3B,MAAMQ,IAAI,GAAGoK,GAAG,CAACpK,IAAI;AACrB,MAAMyT,KAAK,GAAGrJ,GAAG,CAACqJ,KAAK;AACvB,MAAMI,IAAI,GAAGzJ,GAAG,CAACyJ,IAAI;AACrB,MAAM9S,OAAO,GAAGqJ,GAAG,CAACrJ,OAAO;AAE3B,SAASiZ,QAAQ,IAAIvQ,MAAM,EAAEwQ,QAAQ,IAAIngB,MAAM,EAAEogB,SAAS,IAAI1Q,OAAO,EAAE2Q,KAAK,IAAI5b,GAAG,EAAE6b,OAAO,IAAInQ,KAAK,EAAEoQ,oBAAoB,IAAI/Q,kBAAkB,EAAEgR,QAAQ,IAAI9a,MAAM,EAAEQ,IAAI,EAAEyT,KAAK,EAAEI,IAAI,EAAE9S,OAAO,EAAEqJ,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}