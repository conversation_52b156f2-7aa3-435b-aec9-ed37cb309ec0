{"ast": null, "code": "import { defineStore } from 'pinia';\nexport const useHostsStore = defineStore('hosts', {\n  state: () => ({\n    hosts: [],\n    loading: false,\n    error: null\n  }),\n  getters: {\n    getHostById: state => id => state.hosts.find(host => host.id === id),\n    hostsByStatus: state => status => state.hosts.filter(host => host.status === status),\n    totalCount: state => state.hosts.length\n  },\n  actions: {\n    async fetchHosts() {\n      this.loading = true;\n      try {\n        const response = await api.getHosts();\n        this.hosts = response.data;\n      } catch (err) {\n        this.error = err.message;\n      } finally {\n        this.loading = false;\n      }\n    },\n    async updateHostPassword(hostId, password) {\n      // 实现密码更新逻辑\n    }\n  }\n});\nexport default {\n  name: 'App',\n  computed: {\n    routes() {\n      return this.$router.options.routes.filter(route => route.meta && route.meta.title);\n    }\n  }\n};", "map": {"version": 3, "names": ["defineStore", "useHostsStore", "state", "hosts", "loading", "error", "getters", "getHostById", "id", "find", "host", "hostsByStatus", "status", "filter", "totalCount", "length", "actions", "fetchHosts", "response", "api", "getHosts", "data", "err", "message", "updateHostPassword", "hostId", "password", "name", "computed", "routes", "$router", "options", "route", "meta", "title"], "sources": ["D:\\demo\\ooo\\pass\\src\\App.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <header class=\"header\">\r\n            <div class=\"container mx-auto px-4 py-3 flex justify-between items-center\">\r\n                <div class=\"flex items-center\">\r\n                    <div class=\"text-blue-600 mr-2\">\r\n                        <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"text-2xl\" />\r\n                    </div>\r\n                    <h1 class=\"text-xl font-semibold\">密码管理系统</h1>\r\n                </div>\r\n                <div>\r\n                    <button class=\"p-2 text-gray-500 hover:text-gray-700\">\r\n                        <font-awesome-icon :icon=\"['fas', 'cog']\" />\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </header>\r\n\r\n        <main class=\"container mx-auto px-4 py-6\">\r\n            <!-- 导航标签 -->\r\n            <nav class=\"nav-tabs\">\r\n                <router-link v-for=\"route in routes\" :key=\"route.path\" :to=\"route.path\" class=\"nav-item\"\r\n                    active-class=\"active\">\r\n                    <font-awesome-icon :icon=\"['fas', route.meta.icon]\" class=\"mr-2\" />\r\n                    <span>{{ route.meta.title }}</span>\r\n                </router-link>\r\n            </nav>\r\n\r\n            <!-- 路由视图 -->\r\n            <router-view v-slot=\"{ Component }\">\r\n                <transition name=\"fade\" mode=\"out-in\">\r\n                    <component :is=\"Component\" />\r\n                </transition>\r\n            </router-view>\r\n        </main>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { defineStore } from 'pinia'\r\n\r\nexport const useHostsStore = defineStore('hosts', {\r\n    state: () => ({\r\n        hosts: [],\r\n        loading: false,\r\n        error: null\r\n    }),\r\n    getters: {\r\n        getHostById: (state) => (id) => state.hosts.find(host => host.id === id),\r\n        hostsByStatus: (state) => (status) => state.hosts.filter(host => host.status === status),\r\n        totalCount: (state) => state.hosts.length\r\n    },\r\n    actions: {\r\n        async fetchHosts() {\r\n            this.loading = true\r\n            try {\r\n                const response = await api.getHosts()\r\n                this.hosts = response.data\r\n            } catch (err) {\r\n                this.error = err.message\r\n            } finally {\r\n                this.loading = false\r\n            }\r\n        },\r\n        async updateHostPassword(hostId, password) {\r\n            // 实现密码更新逻辑\r\n        }\r\n    }\r\n})\r\n\r\nexport default {\r\n    name: 'App',\r\n    computed: {\r\n        routes() {\r\n            return this.$router.options.routes.filter(route => route.meta && route.meta.title)\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style>\r\nbody {\r\n    font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\r\n    background-color: #f8f9fa;\r\n    color: #333;\r\n}\r\n\r\n.header {\r\n    background-color: white;\r\n    border-bottom: 1px solid #e5e7eb;\r\n}\r\n\r\n.nav-tabs {\r\n    display: flex;\r\n    border-bottom: 1px solid #e5e7eb;\r\n    margin-bottom: 1.5rem;\r\n}\r\n\r\n.nav-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px 16px;\r\n    border-bottom: 2px solid transparent;\r\n    color: #666;\r\n    text-decoration: none;\r\n}\r\n\r\n.nav-item.active {\r\n    color: #2563eb;\r\n    border-bottom-color: #2563eb;\r\n    font-weight: 500;\r\n}\r\n\r\n.nav-item:hover:not(.active) {\r\n    color: #1e40af;\r\n    background-color: #f3f4f6;\r\n}\r\n\r\n/* 过渡效果 */\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n    transition: opacity 0.2s ease;\r\n}\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n    opacity: 0;\r\n}\r\n</style>"], "mappings": "AAuCA,SAASA,WAAU,QAAS,OAAM;AAElC,OAAO,MAAMC,aAAY,GAAID,WAAW,CAAC,OAAO,EAAE;EAC9CE,KAAK,EAAEA,CAAA,MAAO;IACVC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACX,CAAC,CAAC;EACFC,OAAO,EAAE;IACLC,WAAW,EAAGL,KAAK,IAAMM,EAAE,IAAKN,KAAK,CAACC,KAAK,CAACM,IAAI,CAACC,IAAG,IAAKA,IAAI,CAACF,EAAC,KAAMA,EAAE,CAAC;IACxEG,aAAa,EAAGT,KAAK,IAAMU,MAAM,IAAKV,KAAK,CAACC,KAAK,CAACU,MAAM,CAACH,IAAG,IAAKA,IAAI,CAACE,MAAK,KAAMA,MAAM,CAAC;IACxFE,UAAU,EAAGZ,KAAK,IAAKA,KAAK,CAACC,KAAK,CAACY;EACvC,CAAC;EACDC,OAAO,EAAE;IACL,MAAMC,UAAUA,CAAA,EAAG;MACf,IAAI,CAACb,OAAM,GAAI,IAAG;MAClB,IAAI;QACA,MAAMc,QAAO,GAAI,MAAMC,GAAG,CAACC,QAAQ,CAAC;QACpC,IAAI,CAACjB,KAAI,GAAIe,QAAQ,CAACG,IAAG;MAC7B,EAAE,OAAOC,GAAG,EAAE;QACV,IAAI,CAACjB,KAAI,GAAIiB,GAAG,CAACC,OAAM;MAC3B,UAAU;QACN,IAAI,CAACnB,OAAM,GAAI,KAAI;MACvB;IACJ,CAAC;IACD,MAAMoB,kBAAkBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;MACvC;IAAA;EAER;AACJ,CAAC;AAED,eAAe;EACXC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE;IACNC,MAAMA,CAAA,EAAG;MACL,OAAO,IAAI,CAACC,OAAO,CAACC,OAAO,CAACF,MAAM,CAAChB,MAAM,CAACmB,KAAI,IAAKA,KAAK,CAACC,IAAG,IAAKD,KAAK,CAACC,IAAI,CAACC,KAAK;IACrF;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}