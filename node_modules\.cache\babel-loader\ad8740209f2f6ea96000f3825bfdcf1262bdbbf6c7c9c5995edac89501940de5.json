{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, vModelRadio as _vModelRadio, withDirectives as _withDirectives, vModelSelect as _vModelSelect, vModelText as _vModelText, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"flex space-x-3 mb-6\"\n};\nconst _hoisted_2 = {\n  class: \"data-table\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"text-red-600 ml-1\"\n};\nconst _hoisted_4 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_5 = [\"type\", \"value\"];\nconst _hoisted_6 = [\"onClick\"];\nconst _hoisted_7 = [\"onClick\"];\nconst _hoisted_8 = {\n  class: \"form-group\"\n};\nconst _hoisted_9 = {\n  class: \"form-label\"\n};\nconst _hoisted_10 = {\n  class: \"form-group\"\n};\nconst _hoisted_11 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_12 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_13 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_14 = {\n  key: 0\n};\nconst _hoisted_15 = {\n  class: \"form-group\"\n};\nconst _hoisted_16 = [\"value\"];\nconst _hoisted_17 = {\n  class: \"form-group\"\n};\nconst _hoisted_18 = {\n  class: \"flex\"\n};\nconst _hoisted_19 = {\n  key: 1\n};\nconst _hoisted_20 = {\n  class: \"form-group\"\n};\nconst _hoisted_21 = {\n  class: \"form-group\"\n};\nconst _hoisted_22 = {\n  key: 0,\n  class: \"text-red-500 text-xs mt-1\"\n};\nconst _hoisted_23 = {\n  class: \"form-group\"\n};\nconst _hoisted_24 = {\n  class: \"form-group\"\n};\nconst _hoisted_25 = {\n  class: \"mb-2\"\n};\nconst _hoisted_26 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_27 = {\n  class: \"form-text\"\n};\nconst _hoisted_28 = {\n  class: \"form-group\"\n};\nconst _hoisted_29 = [\"value\"];\nconst _hoisted_30 = {\n  class: \"form-group\"\n};\nconst _hoisted_31 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_32 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_33 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_34 = {\n  key: 0,\n  class: \"mt-3\"\n};\nconst _hoisted_35 = {\n  class: \"grid grid-cols-2 gap-4\"\n};\nconst _hoisted_36 = {\n  class: \"form-group\"\n};\nconst _hoisted_37 = {\n  class: \"form-group\"\n};\nconst _hoisted_38 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_39 = {\n  class: \"form-group\"\n};\nconst _hoisted_40 = [\"value\"];\nconst _hoisted_41 = {\n  class: \"form-group\"\n};\nconst _hoisted_42 = {\n  class: \"form-group\"\n};\nconst _hoisted_43 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_44 = {\n  class: \"form-group\"\n};\nconst _hoisted_45 = [\"value\"];\nconst _hoisted_46 = {\n  class: \"form-group\"\n};\nconst _hoisted_47 = {\n  class: \"form-group\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_CustomCheckbox = _resolveComponent(\"CustomCheckbox\");\n  const _component_StatusBadge = _resolveComponent(\"StatusBadge\");\n  const _component_PasswordStrengthMeter = _resolveComponent(\"PasswordStrengthMeter\");\n  const _component_BaseModal = _resolveComponent(\"BaseModal\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createCommentVNode(\" 操作按钮 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"button\", {\n    class: \"btn-outline\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.showEmergencyReset && $options.showEmergencyReset(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'exclamation-triangle'],\n    class: \"mr-2 text-red-600\"\n  }), _cache[33] || (_cache[33] = _createElementVNode(\"span\", null, \"紧急重置\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"btn-outline\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.openBatchUpdateModal && $options.openBatchUpdateModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'key'],\n    class: \"mr-2\"\n  }), _cache[34] || (_cache[34] = _createElementVNode(\"span\", null, \"批量更新密码\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"btn-outline\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.openBatchApplyModal && $options.openBatchApplyModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"mr-2\"\n  }), _cache[35] || (_cache[35] = _createElementVNode(\"span\", null, \"批量应用策略\", -1 /* HOISTED */))])]), _createCommentVNode(\" 主机列表 \"), _createElementVNode(\"table\", _hoisted_2, [_createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, [_createVNode(_component_CustomCheckbox, {\n    modelValue: $data.selectAll,\n    \"onUpdate:modelValue\": [_cache[3] || (_cache[3] = $event => $data.selectAll = $event), $options.toggleSelectAll]\n  }, {\n    default: _withCtx(() => _cache[36] || (_cache[36] = [_createTextVNode(\" 主机名 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _cache[37] || (_cache[37] = _createElementVNode(\"th\", null, \"IP地址\", -1 /* HOISTED */)), _cache[38] || (_cache[38] = _createElementVNode(\"th\", null, \"最后密码修改时间\", -1 /* HOISTED */)), _cache[39] || (_cache[39] = _createElementVNode(\"th\", null, \"密码过期时间\", -1 /* HOISTED */)), _cache[40] || (_cache[40] = _createElementVNode(\"th\", null, \"密码\", -1 /* HOISTED */)), _cache[41] || (_cache[41] = _createElementVNode(\"th\", null, \"状态\", -1 /* HOISTED */)), _cache[42] || (_cache[42] = _createElementVNode(\"th\", null, \"操作\", -1 /* HOISTED */))])]), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: host.id\n    }, [_createElementVNode(\"td\", null, [_createVNode(_component_CustomCheckbox, {\n      modelValue: host.selected,\n      \"onUpdate:modelValue\": $event => host.selected = $event\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name), 1 /* TEXT */)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"td\", null, _toDisplayString(host.ip), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(host.lastPasswordChange || '-'), 1 /* TEXT */), _createElementVNode(\"td\", {\n      class: _normalizeClass({\n        'text-red-600 font-bold': $options.isPasswordExpired(host).status === 'expired'\n      })\n    }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(host).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(host).status === 'expired' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_3, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'exclamation-triangle']\n    })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */), _createElementVNode(\"td\", null, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"input\", {\n      type: $data.passwordVisibility[host.id] ? 'text' : 'password',\n      value: host.password,\n      readonly: \"\",\n      class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n    }, null, 8 /* PROPS */, _hoisted_5), _createElementVNode(\"button\", {\n      onClick: $event => $options.togglePasswordVisibility(host.id),\n      class: \"ml-2 text-gray-600 hover:text-blue-700\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility[host.id] ? 'eye-slash' : 'eye']\n    }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_6)])]), _createElementVNode(\"td\", null, [_createVNode(_component_StatusBadge, {\n      type: host.status\n    }, null, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"td\", null, [_createElementVNode(\"button\", {\n      class: \"text-blue-600 hover:text-blue-800\",\n      onClick: $event => $options.openChangePasswordModal(host)\n    }, \" 修改密码 \", 8 /* PROPS */, _hoisted_7)])]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 修改密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.changePasswordModal.show,\n    \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.changePasswordModal.show = $event),\n    title: \"修改密码\",\n    \"confirm-text\": \"确认更新\",\n    onConfirm: $options.updatePassword,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"label\", _hoisted_9, \"服务器: \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_10, [_cache[45] || (_cache[45] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"label\", _hoisted_12, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.changePasswordModal.method = $event),\n      value: \"auto\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.changePasswordModal.method]]), _cache[43] || (_cache[43] = _createElementVNode(\"span\", null, \"自动生成\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_13, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.changePasswordModal.method = $event),\n      value: \"manual\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.changePasswordModal.method]]), _cache[44] || (_cache[44] = _createElementVNode(\"span\", null, \"手动输入\", -1 /* HOISTED */))])])]), $data.changePasswordModal.method === 'auto' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_cache[46] || (_cache[46] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.changePasswordModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_16);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.changePasswordModal.policyId]])]), _createElementVNode(\"div\", _hoisted_17, [_cache[47] || (_cache[47] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_18, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.changePasswordModal.generatedPassword = $event),\n      class: \"form-control rounded-r-none\",\n      readonly: \"\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.changePasswordModal.generatedPassword]]), _createElementVNode(\"button\", {\n      class: \"bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded-r-md\",\n      onClick: _cache[8] || (_cache[8] = (...args) => $options.generatePassword && $options.generatePassword(...args))\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt']\n    })])]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.generatedPassword\n    }, null, 8 /* PROPS */, [\"password\"])])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_cache[48] || (_cache[48] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"新密码\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"password\",\n      \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.changePasswordModal.newPassword = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.changePasswordModal.newPassword]]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.newPassword\n    }, null, 8 /* PROPS */, [\"password\"])]), _createElementVNode(\"div\", _hoisted_21, [_cache[49] || (_cache[49] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"确认密码\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"password\",\n      \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.changePasswordModal.confirmPassword = $event),\n      class: _normalizeClass([\"form-control\", {\n        'border-red-500': $options.passwordMismatch\n      }])\n    }, null, 2 /* CLASS */), [[_vModelText, $data.changePasswordModal.confirmPassword]]), $options.passwordMismatch ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, \" 两次输入的密码不一致 \")) : _createCommentVNode(\"v-if\", true)])])), _createElementVNode(\"div\", _hoisted_23, [_cache[53] || (_cache[53] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.executeImmediately,\n      \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $data.changePasswordModal.executeImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[50] || (_cache[50] = [_createTextVNode(\" 立即执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.saveHistory,\n      \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $data.changePasswordModal.saveHistory = $event)\n    }, {\n      default: _withCtx(() => _cache[51] || (_cache[51] = [_createTextVNode(\" 保存密码历史记录 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.logAudit,\n      \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.changePasswordModal.logAudit = $event)\n    }, {\n      default: _withCtx(() => _cache[52] || (_cache[52] = [_createTextVNode(\" 记录审计日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量更新密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchUpdateModal.show,\n    \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.batchUpdateModal.show = $event),\n    title: \"批量更新密码\",\n    \"confirm-text\": \"开始更新\",\n    size: \"lg\",\n    onConfirm: $options.batchUpdatePasswords,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_24, [_cache[55] || (_cache[55] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_25, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatch,\n      \"onUpdate:modelValue\": [_cache[15] || (_cache[15] = $event => $data.selectAllBatch = $event), $options.toggleSelectAllBatch]\n    }, {\n      default: _withCtx(() => _cache[54] || (_cache[54] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_26, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchUpdateModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchUpdateModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_27, \"已选择 \" + _toDisplayString($options.selectedHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_28, [_cache[56] || (_cache[56] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.batchUpdateModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_29);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchUpdateModal.policyId]])]), _createElementVNode(\"div\", _hoisted_30, [_cache[61] || (_cache[61] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"label\", _hoisted_32, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"immediate\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[57] || (_cache[57] = _createElementVNode(\"span\", null, \"立即执行\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_33, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[18] || (_cache[18] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"scheduled\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[58] || (_cache[58] = _createElementVNode(\"span\", null, \"定时执行\", -1 /* HOISTED */))])]), $data.batchUpdateModal.executionTime === 'scheduled' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_34, [_createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"div\", null, [_cache[59] || (_cache[59] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"日期\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"date\",\n      \"onUpdate:modelValue\": _cache[19] || (_cache[19] = $event => $data.batchUpdateModal.scheduledDate = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledDate]])]), _createElementVNode(\"div\", null, [_cache[60] || (_cache[60] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"时间\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"time\",\n      \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $data.batchUpdateModal.scheduledTime = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledTime]])])])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_36, [_cache[65] || (_cache[65] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[21] || (_cache[21] = $event => $data.batchUpdateModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[62] || (_cache[62] = [_createTextVNode(\" 忽略错误继续执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.detailedLog,\n      \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $data.batchUpdateModal.detailedLog = $event)\n    }, {\n      default: _withCtx(() => _cache[63] || (_cache[63] = [_createTextVNode(\" 记录详细日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.sendNotification,\n      \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.batchUpdateModal.sendNotification = $event)\n    }, {\n      default: _withCtx(() => _cache[64] || (_cache[64] = [_createTextVNode(\" 执行完成后发送通知 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量应用策略弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchApplyModal.show,\n    \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $data.batchApplyModal.show = $event),\n    title: \"批量应用密码策略\",\n    \"confirm-text\": \"应用策略\",\n    onConfirm: $options.batchApplyPolicy,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_37, [_cache[66] || (_cache[66] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_38, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchApplyModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchApplyModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_39, [_cache[67] || (_cache[67] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.batchApplyModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name), 9 /* TEXT, PROPS */, _hoisted_40);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchApplyModal.policyId]])]), _createElementVNode(\"div\", _hoisted_41, [_cache[70] || (_cache[70] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.updateImmediately,\n      \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.batchApplyModal.updateImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[68] || (_cache[68] = [_createTextVNode(\" 立即更新密码以符合策略 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.applyOnNextUpdate,\n      \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.batchApplyModal.applyOnNextUpdate = $event)\n    }, {\n      default: _withCtx(() => _cache[69] || (_cache[69] = [_createTextVNode(\" 下次密码更新时应用 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 紧急重置密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.emergencyResetModal.show,\n    \"onUpdate:modelValue\": _cache[32] || (_cache[32] = $event => $data.emergencyResetModal.show = $event),\n    title: \"紧急密码重置\",\n    \"confirm-text\": \"立即重置\",\n    icon: \"exclamation-triangle\",\n    danger: \"\",\n    onConfirm: $options.emergencyReset,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_cache[76] || (_cache[76] = _createElementVNode(\"div\", {\n      class: \"bg-red-50 text-red-700 p-3 rounded-md mb-4\"\n    }, [_createElementVNode(\"p\", null, \"紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_42, [_cache[71] || (_cache[71] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_43, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.emergencyResetModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.emergencyResetModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_44, [_cache[72] || (_cache[72] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用紧急策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[29] || (_cache[29] = $event => $data.emergencyResetModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.emergencyPolicies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_45);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.policyId]])]), _createElementVNode(\"div\", _hoisted_46, [_cache[74] || (_cache[74] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"操作原因\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[30] || (_cache[30] = $event => $data.emergencyResetModal.reason = $event),\n      class: \"form-select\"\n    }, _cache[73] || (_cache[73] = [_createElementVNode(\"option\", {\n      value: \"security_incident\"\n    }, \"安全事件响应\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"password_leak\"\n    }, \"密码泄露\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"abnormal_access\"\n    }, \"异常访问\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"compliance\"\n    }, \"合规要求\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"other\"\n    }, \"其他原因\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.reason]])]), _createElementVNode(\"div\", _hoisted_47, [_cache[75] || (_cache[75] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"附加说明\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n      \"onUpdate:modelValue\": _cache[31] || (_cache[31] = $event => $data.emergencyResetModal.description = $event),\n      class: \"form-control\",\n      rows: \"2\",\n      placeholder: \"请输入重置原因详细说明\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.emergencyResetModal.description]])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "onClick", "_cache", "args", "$options", "showEmergencyReset", "_createVNode", "_component_font_awesome_icon", "icon", "openBatchUpdateModal", "openBatchApplyModal", "_hoisted_2", "_component_CustomCheckbox", "modelValue", "$data", "selectAll", "$event", "toggleSelectAll", "default", "_withCtx", "_createTextVNode", "_", "_Fragment", "_renderList", "_ctx", "hosts", "host", "id", "selected", "_toDisplayString", "name", "ip", "lastPasswordChange", "_normalizeClass", "isPasswordExpired", "status", "text", "_hoisted_3", "_hoisted_4", "type", "passwordVisibility", "value", "password", "readonly", "_hoisted_5", "togglePasswordVisibility", "_hoisted_6", "_component_StatusBadge", "openChangePasswordModal", "_hoisted_7", "_component_BaseModal", "changePasswordModal", "show", "title", "onConfirm", "updatePassword", "loading", "processing", "_hoisted_8", "_hoisted_9", "currentHost", "_hoisted_10", "_hoisted_11", "_hoisted_12", "method", "_hoisted_13", "_hoisted_14", "_hoisted_15", "policyId", "policies", "policy", "<PERSON><PERSON><PERSON><PERSON>", "expiryDays", "_hoisted_16", "_hoisted_17", "_hoisted_18", "generatedPassword", "generatePassword", "_component_PasswordStrengthMeter", "_hoisted_19", "_hoisted_20", "newPassword", "_hoisted_21", "confirmPassword", "passwordMismatch", "_hoisted_22", "_hoisted_23", "executeImmediately", "saveHistory", "logAudit", "batchUpdateModal", "size", "batchUpdatePasswords", "_hoisted_24", "_hoisted_25", "selectAllBatch", "toggleSelectAllBatch", "_hoisted_26", "_createBlock", "selectedHosts", "_hoisted_27", "selectedHostsCount", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "executionTime", "_hoisted_33", "_hoisted_34", "_hoisted_35", "scheduledDate", "scheduledTime", "_hoisted_36", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "batchApplyPolicy", "_hoisted_37", "_hoisted_38", "selectedHostsList", "_hoisted_39", "_hoisted_40", "_hoisted_41", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "danger", "emergencyReset", "_hoisted_42", "_hoisted_43", "_hoisted_44", "emergencyPolicies", "_hoisted_45", "_hoisted_46", "reason", "_hoisted_47", "description", "rows", "placeholder"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"flex space-x-3 mb-6\">\r\n      <button class=\"btn-outline\" @click=\"showEmergencyReset\">\r\n        <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2 text-red-600\" />\r\n        <span>紧急重置</span>\r\n      </button>\r\n      <button class=\"btn-outline\" @click=\"openBatchUpdateModal\">\r\n        <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n        <span>批量更新密码</span>\r\n      </button>\r\n      <button class=\"btn-outline\" @click=\"openBatchApplyModal\">\r\n        <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n        <span>批量应用策略</span>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <table class=\"data-table\">\r\n      <thead>\r\n        <tr>\r\n          <th>\r\n            <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n              主机名\r\n            </CustomCheckbox>\r\n          </th>\r\n          <th>IP地址</th>\r\n          <th>最后密码修改时间</th>\r\n          <th>密码过期时间</th>\r\n          <th>密码</th>\r\n          <th>状态</th>\r\n          <th>操作</th>\r\n        </tr>\r\n      </thead>\r\n      <tbody>\r\n        <tr v-for=\"host in hosts\" :key=\"host.id\">\r\n          <td>\r\n            <CustomCheckbox v-model=\"host.selected\">\r\n              {{ host.name }}\r\n            </CustomCheckbox>\r\n          </td>\r\n          <td>{{ host.ip }}</td>\r\n          <td>{{ host.lastPasswordChange || '-' }}</td>\r\n          <td :class=\"{ 'text-red-600 font-bold': isPasswordExpired(host).status === 'expired' }\">\r\n            {{ isPasswordExpired(host).text }}\r\n            <span v-if=\"isPasswordExpired(host).status === 'expired'\" class=\"text-red-600 ml-1\">\r\n              <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n            </span>\r\n          </td>\r\n          <td>\r\n            <div class=\"flex items-center\">\r\n              <input :type=\"passwordVisibility[host.id] ? 'text' : 'password'\" :value=\"host.password\" readonly\r\n                class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n              <button @click=\"togglePasswordVisibility(host.id)\" class=\"ml-2 text-gray-600 hover:text-blue-700\">\r\n                <font-awesome-icon :icon=\"['fas', passwordVisibility[host.id] ? 'eye-slash' : 'eye']\" />\r\n              </button>\r\n            </div>\r\n          </td>\r\n          <td>\r\n            <StatusBadge :type=\"host.status\" />\r\n          </td>\r\n          <td>\r\n            <button class=\"text-blue-600 hover:text-blue-800\" @click=\"openChangePasswordModal(host)\">\r\n              修改密码\r\n            </button>\r\n          </td>\r\n        </tr>\r\n      </tbody>\r\n    </table>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" confirm-text=\"确认更新\" @confirm=\"updatePassword\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">服务器: {{ currentHost.name }}</label>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"changePasswordModal.method\" value=\"auto\" class=\"mr-2\">\r\n            <span>自动生成</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"changePasswordModal.method\" value=\"manual\" class=\"mr-2\">\r\n            <span>手动输入</span>\r\n          </label>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-if=\"changePasswordModal.method === 'auto'\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">选择密码策略</label>\r\n          <select v-model=\"changePasswordModal.policyId\" class=\"form-select\">\r\n            <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n              {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n            </option>\r\n          </select>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <div class=\"flex\">\r\n            <input type=\"text\" v-model=\"changePasswordModal.generatedPassword\" class=\"form-control rounded-r-none\"\r\n              readonly>\r\n            <button class=\"bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded-r-md\" @click=\"generatePassword\">\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" />\r\n            </button>\r\n          </div>\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.generatedPassword\" />\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else>\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <input type=\"password\" v-model=\"changePasswordModal.newPassword\" class=\"form-control\">\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <input type=\"password\" v-model=\"changePasswordModal.confirmPassword\" class=\"form-control\"\r\n            :class=\"{ 'border-red-500': passwordMismatch }\">\r\n          <div v-if=\"passwordMismatch\" class=\"text-red-500 text-xs mt-1\">\r\n            两次输入的密码不一致\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行选项</label>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          立即执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          保存密码历史记录\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          记录审计日志\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      passwordVisibility: {},\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host) {\r\n      this.currentHost = host\r\n      this.changePasswordModal.show = true\r\n      this.changePasswordModal.generatedPassword = this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword(policy) {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policyObj ? policyObj.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      if (this.changePasswordModal && !policy) {\r\n        this.changePasswordModal.generatedPassword = password\r\n      }\r\n\r\n      return password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto'\r\n          ? this.changePasswordModal.generatedPassword\r\n          : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          password: password,\r\n          policyId: this.changePasswordModal.policyId\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取所选策略\r\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)\r\n\r\n        // 为每台主机生成并更新密码\r\n        await Promise.all(\r\n          selectedHostIds.map(async (hostId) => {\r\n            const newPassword = this.generatePassword(policy)\r\n            return this.$store.dispatch('updateHostPassword', {\r\n              hostId: hostId,\r\n              password: newPassword,\r\n              policyId: policy.id\r\n            })\r\n          })\r\n        )\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取紧急策略\r\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)\r\n\r\n        // 为每台主机生成并更新密码\r\n        await Promise.all(\r\n          selectedHostIds.map(async (hostId) => {\r\n            const newPassword = this.generatePassword(policy)\r\n            return this.$store.dispatch('updateHostPassword', {\r\n              hostId: hostId,\r\n              password: newPassword,\r\n              policyId: policy.id\r\n            })\r\n          })\r\n        )\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    togglePasswordVisibility(hostId) {\r\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]\r\n    },\r\n\r\n    isPasswordExpired(host) {\r\n      if (!host.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }\r\n      \r\n      // 解析过期时间\r\n      const expiryDate = new Date(host.passwordExpiryDate)\r\n      const now = new Date()\r\n      \r\n      // 如果已过期\r\n      if (expiryDate < now) {\r\n        return { \r\n          status: 'expired', \r\n          days: 0, \r\n          text: '已过期' \r\n        }\r\n      }\r\n      \r\n      // 计算剩余天数和小时数\r\n      const diffTime = expiryDate - now\r\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))\r\n      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n      \r\n      // 根据剩余时间确定状态\r\n      let status = 'normal'\r\n      if (diffDays < 7) {\r\n        status = 'danger'  // 少于7天\r\n      } else if (diffDays < 14) {\r\n        status = 'warning' // 少于14天\r\n      }\r\n      \r\n      // 格式化显示文本\r\n      let text = ''\r\n      if (diffDays > 0) {\r\n        text += `${diffDays}天`\r\n      }\r\n      if (diffHours > 0 || diffDays === 0) {\r\n        text += `${diffHours}小时`\r\n      }\r\n      \r\n      return { status, days: diffDays, text: `剩余${text}` }\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": ";;EAGSA,KAAK,EAAC;AAAqB;;EAgBzBA,KAAK,EAAC;AAAY;;EAnB7BC,GAAA;EA8CsED,KAAK,EAAC;;;EAK3DA,KAAK,EAAC;AAAmB;mBAnD1C;mBAAA;mBAAA;;EA0EWA,KAAK,EAAC;AAAY;;EACdA,KAAK,EAAC;AAAY;;EAGtBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EArF1CC,GAAA;AAAA;;EA6FaD,KAAK,EAAC;AAAY;oBA7F/B;;EAsGaA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EAxG3BC,GAAA;AAAA;;EAoHaD,KAAK,EAAC;AAAY;;EAMlBA,KAAK,EAAC;AAAY;;EA1H/BC,GAAA;EA8HuCD,KAAK,EAAC;;;EAMlCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAY;oBApK7B;;EA6KWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EApL1CC,GAAA;EA0LmED,KAAK,EAAC;;;EAC1DA,KAAK,EAAC;AAAwB;;EAalCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;oBAnO7B;;EA4OWA,KAAK,EAAC;AAAY;;EAkBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;oBAxQ7B;;EAiRWA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;;;;;;uBA3R3BE,mBAAA,CAiSM,cAhSJC,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNC,UAaM,GAZJD,mBAAA,CAGS;IAHDJ,KAAK,EAAC,aAAa;IAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,kBAAA,IAAAD,QAAA,CAAAC,kBAAA,IAAAF,IAAA,CAAkB;MACpDG,YAAA,CAAuFC,4BAAA;IAAnEC,IAAI,EAAE,+BAA+B;IAAEb,KAAK,EAAC;kCACjEI,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGS;IAHDJ,KAAK,EAAC,aAAa;IAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAK,oBAAA,IAAAL,QAAA,CAAAK,oBAAA,IAAAN,IAAA,CAAoB;MACtDG,YAAA,CAAyDC,4BAAA;IAArCC,IAAI,EAAE,cAAc;IAAEb,KAAK,EAAC;kCAChDI,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAGS;IAHDJ,KAAK,EAAC,aAAa;IAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAM,mBAAA,IAAAN,QAAA,CAAAM,mBAAA,IAAAP,IAAA,CAAmB;MACrDG,YAAA,CAAgEC,4BAAA;IAA5CC,IAAI,EAAE,qBAAqB;IAAEb,KAAK,EAAC;kCACvDI,mBAAA,CAAmB,cAAb,QAAM,qB,KAIhBD,mBAAA,UAAa,EACbC,mBAAA,CAkDQ,SAlDRY,UAkDQ,GAjDNZ,mBAAA,CAcQ,gBAbNA,mBAAA,CAYK,aAXHA,mBAAA,CAIK,aAHHO,YAAA,CAEiBM,yBAAA;IAzB7BC,UAAA,EAuBqCC,KAAA,CAAAC,SAAS;IAvB9C,wB,oCAuBqCD,KAAA,CAAAC,SAAS,GAAAC,MAAA,GAAsBZ,QAAA,CAAAa,eAAe;;IAvBnFC,OAAA,EAAAC,QAAA,CAuBqF,MAEzEjB,MAAA,SAAAA,MAAA,QAzBZkB,gBAAA,CAuBqF,OAEzE,E;IAzBZC,CAAA;0FA2BUtB,mBAAA,CAAa,YAAT,MAAI,sB,4BACRA,mBAAA,CAAiB,YAAb,UAAQ,sB,4BACZA,mBAAA,CAAe,YAAX,QAAM,sB,4BACVA,mBAAA,CAAW,YAAP,IAAE,sB,4BACNA,mBAAA,CAAW,YAAP,IAAE,sB,4BACNA,mBAAA,CAAW,YAAP,IAAE,qB,KAGVA,mBAAA,CAiCQ,iB,kBAhCNF,mBAAA,CA+BKyB,SAAA,QAnEbC,WAAA,CAoC2BC,IAAA,CAAAC,KAAK,EAAbC,IAAI;yBAAf7B,mBAAA,CA+BK;MA/BsBD,GAAG,EAAE8B,IAAI,CAACC;QACnC5B,mBAAA,CAIK,aAHHO,YAAA,CAEiBM,yBAAA;MAxC7BC,UAAA,EAsCqCa,IAAI,CAACE,QAAQ;MAtClD,uBAAAZ,MAAA,IAsCqCU,IAAI,CAACE,QAAQ,GAAAZ;;MAtClDE,OAAA,EAAAC,QAAA,CAuCc,MAAe,CAvC7BC,gBAAA,CAAAS,gBAAA,CAuCiBH,IAAI,CAACI,IAAI,iB;MAvC1BT,CAAA;kFA0CUtB,mBAAA,CAAsB,YAAA8B,gBAAA,CAAfH,IAAI,CAACK,EAAE,kBACdhC,mBAAA,CAA6C,YAAA8B,gBAAA,CAAtCH,IAAI,CAACM,kBAAkB,yBAC9BjC,mBAAA,CAKK;MALAJ,KAAK,EA5CpBsC,eAAA;QAAA,0BA4CkD7B,QAAA,CAAA8B,iBAAiB,CAACR,IAAI,EAAES,MAAM;MAAA;QA5ChFf,gBAAA,CAAAS,gBAAA,CA6CezB,QAAA,CAAA8B,iBAAiB,CAACR,IAAI,EAAEU,IAAI,IAAG,GAClC,iBAAYhC,QAAA,CAAA8B,iBAAiB,CAACR,IAAI,EAAES,MAAM,kB,cAA1CtC,mBAAA,CAEO,QAFPwC,UAEO,GADL/B,YAAA,CAA6DC,4BAAA;MAAzCC,IAAI,EAAE;IAA+B,G,KA/CvEV,mBAAA,e,kBAkDUC,mBAAA,CAQK,aAPHA,mBAAA,CAMM,OANNuC,UAMM,GALJvC,mBAAA,CACkG;MAD1FwC,IAAI,EAAEzB,KAAA,CAAA0B,kBAAkB,CAACd,IAAI,CAACC,EAAE;MAA0Bc,KAAK,EAAEf,IAAI,CAACgB,QAAQ;MAAEC,QAAQ,EAAR,EAAQ;MAC9FhD,KAAK,EAAC;4BArDtBiD,UAAA,GAsDc7C,mBAAA,CAES;MAFAE,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAAyC,wBAAwB,CAACnB,IAAI,CAACC,EAAE;MAAGhC,KAAK,EAAC;QACvDW,YAAA,CAAwFC,4BAAA;MAApEC,IAAI,UAAUM,KAAA,CAAA0B,kBAAkB,CAACd,IAAI,CAACC,EAAE;uDAvD5EmB,UAAA,E,KA2DU/C,mBAAA,CAEK,aADHO,YAAA,CAAmCyC,sBAAA;MAArBR,IAAI,EAAEb,IAAI,CAACS;yCAE3BpC,mBAAA,CAIK,aAHHA,mBAAA,CAES;MAFDJ,KAAK,EAAC,mCAAmC;MAAEM,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAA4C,uBAAuB,CAACtB,IAAI;OAAG,QAEzF,iBAjEZuB,UAAA,E;sCAuEInD,mBAAA,YAAe,EACfQ,YAAA,CAwEY4C,oBAAA;IAhJhBrC,UAAA,EAwEwBC,KAAA,CAAAqC,mBAAmB,CAACC,IAAI;IAxEhD,uBAAAlD,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAwEwBF,KAAA,CAAAqC,mBAAmB,CAACC,IAAI,GAAApC,MAAA;IAAEqC,KAAK,EAAC,MAAM;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAElD,QAAA,CAAAmD,cAAc;IACpGC,OAAO,EAAE1C,KAAA,CAAA2C;;IAzEhBvC,OAAA,EAAAC,QAAA,CA0EM,MAEM,CAFNpB,mBAAA,CAEM,OAFN2D,UAEM,GADJ3D,mBAAA,CAA6D,SAA7D4D,UAA6D,EAAnC,OAAK,GAAA9B,gBAAA,CAAGf,KAAA,CAAA8C,WAAW,CAAC9B,IAAI,iB,GAGpD/B,mBAAA,CAYM,OAZN8D,WAYM,G,4BAXJ9D,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCI,mBAAA,CASM,OATN+D,WASM,GARJ/D,mBAAA,CAGQ,SAHRgE,WAGQ,G,gBAFNhE,mBAAA,CAAmF;MAA5EwC,IAAI,EAAC,OAAO;MAlF/B,uBAAArC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAkFyCF,KAAA,CAAAqC,mBAAmB,CAACa,MAAM,GAAAhD,MAAA;MAAEyB,KAAK,EAAC,MAAM;MAAC9C,KAAK,EAAC;oDAA/CmB,KAAA,CAAAqC,mBAAmB,CAACa,MAAM,E,+BACvDjE,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHRkE,WAGQ,G,gBAFNlE,mBAAA,CAAqF;MAA9EwC,IAAI,EAAC,OAAO;MAtF/B,uBAAArC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAsFyCF,KAAA,CAAAqC,mBAAmB,CAACa,MAAM,GAAAhD,MAAA;MAAEyB,KAAK,EAAC,QAAQ;MAAC9C,KAAK,EAAC;oDAAjDmB,KAAA,CAAAqC,mBAAmB,CAACa,MAAM,E,+BACvDjE,mBAAA,CAAiB,cAAX,MAAI,qB,OAKLe,KAAA,CAAAqC,mBAAmB,CAACa,MAAM,e,cAArCnE,mBAAA,CAqBM,OAjHZqE,WAAA,GA6FQnE,mBAAA,CAOM,OAPNoE,WAOM,G,4BANJpE,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCI,mBAAA,CAIS;MAnGnB,uBAAAG,MAAA,QAAAA,MAAA,MAAAc,MAAA,IA+F2BF,KAAA,CAAAqC,mBAAmB,CAACiB,QAAQ,GAAApD,MAAA;MAAErB,KAAK,EAAC;2BACnDE,mBAAA,CAESyB,SAAA,QAlGrBC,WAAA,CAgGqCC,IAAA,CAAA6C,QAAQ,EAAlBC,MAAM;2BAArBzE,mBAAA,CAES;QAF2BD,GAAG,EAAE0E,MAAM,CAAC3C,EAAE;QAAGc,KAAK,EAAE6B,MAAM,CAAC3C;0BAC9D2C,MAAM,CAACxC,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAGyC,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA1C,gBAAA,CAAGyC,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAlGZC,WAAA;6EA+F2B3D,KAAA,CAAAqC,mBAAmB,CAACiB,QAAQ,E,KAO/CrE,mBAAA,CAUM,OAVN2E,WAUM,G,4BATJ3E,mBAAA,CAAuC;MAAhCJ,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BI,mBAAA,CAMM,OANN4E,WAMM,G,gBALJ5E,mBAAA,CACW;MADJwC,IAAI,EAAC,MAAM;MAzG9B,uBAAArC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAyGwCF,KAAA,CAAAqC,mBAAmB,CAACyB,iBAAiB,GAAA5D,MAAA;MAAErB,KAAK,EAAC,6BAA6B;MACpGgD,QAAQ,EAAR;mDAD0B7B,KAAA,CAAAqC,mBAAmB,CAACyB,iBAAiB,E,GAEjE7E,mBAAA,CAES;MAFDJ,KAAK,EAAC,sDAAsD;MAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAyE,gBAAA,IAAAzE,QAAA,CAAAyE,gBAAA,IAAA1E,IAAA,CAAgB;QAC3FG,YAAA,CAAiDC,4BAAA;MAA7BC,IAAI,EAAE;IAAmB,G,KAGjDF,YAAA,CAA2EwE,gCAAA;MAAnDpC,QAAQ,EAAE5B,KAAA,CAAAqC,mBAAmB,CAACyB;gEAI1D/E,mBAAA,CAeM,OAlIZkF,WAAA,GAoHQhF,mBAAA,CAIM,OAJNiF,WAIM,G,4BAHJjF,mBAAA,CAAqC;MAA9BJ,KAAK,EAAC;IAAY,GAAC,KAAG,sB,gBAC7BI,mBAAA,CAAsF;MAA/EwC,IAAI,EAAC,UAAU;MAtHhC,uBAAArC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAsH0CF,KAAA,CAAAqC,mBAAmB,CAAC8B,WAAW,GAAAjE,MAAA;MAAErB,KAAK,EAAC;mDAAvCmB,KAAA,CAAAqC,mBAAmB,CAAC8B,WAAW,E,GAC/D3E,YAAA,CAAqEwE,gCAAA;MAA7CpC,QAAQ,EAAE5B,KAAA,CAAAqC,mBAAmB,CAAC8B;6CAGxDlF,mBAAA,CAOM,OAPNmF,WAOM,G,4BANJnF,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BI,mBAAA,CACkD;MAD3CwC,IAAI,EAAC,UAAU;MA5HhC,uBAAArC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4H0CF,KAAA,CAAAqC,mBAAmB,CAACgC,eAAe,GAAAnE,MAAA;MAAErB,KAAK,EA5HpFsC,eAAA,EA4HqF,cAAc;QAAA,kBAC3D7B,QAAA,CAAAgF;MAAgB;4CADdtE,KAAA,CAAAqC,mBAAmB,CAACgC,eAAe,E,GAExD/E,QAAA,CAAAgF,gBAAgB,I,cAA3BvF,mBAAA,CAEM,OAFNwF,WAEM,EAFyD,cAE/D,KAhIVvF,mBAAA,e,MAoIMC,mBAAA,CAWM,OAXNuF,WAWM,G,4BAVJvF,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BW,YAAA,CAEiBM,yBAAA;MAxIzBC,UAAA,EAsIiCC,KAAA,CAAAqC,mBAAmB,CAACoC,kBAAkB;MAtIvE,uBAAArF,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAsIiCF,KAAA,CAAAqC,mBAAmB,CAACoC,kBAAkB,GAAAvE,MAAA;;MAtIvEE,OAAA,EAAAC,QAAA,CAsIyE,MAEjEjB,MAAA,SAAAA,MAAA,QAxIRkB,gBAAA,CAsIyE,QAEjE,E;MAxIRC,CAAA;uCAyIQf,YAAA,CAEiBM,yBAAA;MA3IzBC,UAAA,EAyIiCC,KAAA,CAAAqC,mBAAmB,CAACqC,WAAW;MAzIhE,uBAAAtF,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAyIiCF,KAAA,CAAAqC,mBAAmB,CAACqC,WAAW,GAAAxE,MAAA;;MAzIhEE,OAAA,EAAAC,QAAA,CAyIkE,MAE1DjB,MAAA,SAAAA,MAAA,QA3IRkB,gBAAA,CAyIkE,YAE1D,E;MA3IRC,CAAA;uCA4IQf,YAAA,CAEiBM,yBAAA;MA9IzBC,UAAA,EA4IiCC,KAAA,CAAAqC,mBAAmB,CAACsC,QAAQ;MA5I7D,uBAAAvF,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4IiCF,KAAA,CAAAqC,mBAAmB,CAACsC,QAAQ,GAAAzE,MAAA;;MA5I7DE,OAAA,EAAAC,QAAA,CA4I+D,MAEvDjB,MAAA,SAAAA,MAAA,QA9IRkB,gBAAA,CA4I+D,UAEvD,E;MA9IRC,CAAA;;IAAAA,CAAA;6DAkJIvB,mBAAA,cAAiB,EACjBQ,YAAA,CAiEY4C,oBAAA;IApNhBrC,UAAA,EAmJwBC,KAAA,CAAA4E,gBAAgB,CAACtC,IAAI;IAnJ7C,uBAAAlD,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAmJwBF,KAAA,CAAA4E,gBAAgB,CAACtC,IAAI,GAAApC,MAAA;IAAEqC,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAACsC,IAAI,EAAC,IAAI;IACpFrC,SAAO,EAAElD,QAAA,CAAAwF,oBAAoB;IAAGpC,OAAO,EAAE1C,KAAA,CAAA2C;;IApJhDvC,OAAA,EAAAC,QAAA,CAqJM,MAaM,CAbNpB,mBAAA,CAaM,OAbN8F,WAaM,G,4BAZJ9F,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCI,mBAAA,CAIM,OAJN+F,WAIM,GAHJxF,YAAA,CAEiBM,yBAAA;MA1J3BC,UAAA,EAwJmCC,KAAA,CAAAiF,cAAc;MAxJjD,wB,sCAwJmCjF,KAAA,CAAAiF,cAAc,GAAA/E,MAAA,GAAsBZ,QAAA,CAAA4F,oBAAoB;;MAxJ3F9E,OAAA,EAAAC,QAAA,CAwJ6F,MAEnFjB,MAAA,SAAAA,MAAA,QA1JVkB,gBAAA,CAwJ6F,MAEnF,E;MA1JVC,CAAA;gEA4JQtB,mBAAA,CAIM,OAJNkG,WAIM,I,kBAHJpG,mBAAA,CAEiByB,SAAA,QA/J3BC,WAAA,CA6JyCC,IAAA,CAAAC,KAAK,EAAbC,IAAI;2BAA3BwE,YAAA,CAEiBtF,yBAAA;QAFsBhB,GAAG,EAAE8B,IAAI,CAACC,EAAE;QA7J7Dd,UAAA,EA6JwEC,KAAA,CAAA4E,gBAAgB,CAACS,aAAa,CAACzE,IAAI,CAACC,EAAE;QA7J9G,uBAAAX,MAAA,IA6JwEF,KAAA,CAAA4E,gBAAgB,CAACS,aAAa,CAACzE,IAAI,CAACC,EAAE,IAAAX;;QA7J9GE,OAAA,EAAAC,QAAA,CA8JY,MAAe,CA9J3BC,gBAAA,CAAAS,gBAAA,CA8JeH,IAAI,CAACI,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGH,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QA/JVV,CAAA;;sCAiKQtB,mBAAA,CAAyD,KAAzDqG,WAAyD,EAApC,MAAI,GAAAvE,gBAAA,CAAGzB,QAAA,CAAAiG,kBAAkB,IAAG,MAAI,gB,GAGvDtG,mBAAA,CAOM,OAPNuG,WAOM,G,4BANJvG,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BI,mBAAA,CAIS;MA1KjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAsKyBF,KAAA,CAAA4E,gBAAgB,CAACtB,QAAQ,GAAApD,MAAA;MAAErB,KAAK,EAAC;2BAChDE,mBAAA,CAESyB,SAAA,QAzKnBC,WAAA,CAuKmCC,IAAA,CAAA6C,QAAQ,EAAlBC,MAAM;2BAArBzE,mBAAA,CAES;QAF2BD,GAAG,EAAE0E,MAAM,CAAC3C,EAAE;QAAGc,KAAK,EAAE6B,MAAM,CAAC3C;0BAC9D2C,MAAM,CAACxC,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAGyC,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA1C,gBAAA,CAAGyC,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAzKV+B,WAAA;6EAsKyBzF,KAAA,CAAA4E,gBAAgB,CAACtB,QAAQ,E,KAO5CrE,mBAAA,CAyBM,OAzBNyG,WAyBM,G,4BAxBJzG,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BI,mBAAA,CASM,OATN0G,WASM,GARJ1G,mBAAA,CAGQ,SAHR2G,WAGQ,G,gBAFN3G,mBAAA,CAA4F;MAArFwC,IAAI,EAAC,OAAO;MAjL/B,uBAAArC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAiLyCF,KAAA,CAAA4E,gBAAgB,CAACiB,aAAa,GAAA3F,MAAA;MAAEyB,KAAK,EAAC,WAAW;MAAC9C,KAAK,EAAC;oDAAxDmB,KAAA,CAAA4E,gBAAgB,CAACiB,aAAa,E,+BAC3D5G,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHR6G,WAGQ,G,gBAFN7G,mBAAA,CAA4F;MAArFwC,IAAI,EAAC,OAAO;MArL/B,uBAAArC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAqLyCF,KAAA,CAAA4E,gBAAgB,CAACiB,aAAa,GAAA3F,MAAA;MAAEyB,KAAK,EAAC,WAAW;MAAC9C,KAAK,EAAC;oDAAxDmB,KAAA,CAAA4E,gBAAgB,CAACiB,aAAa,E,+BAC3D5G,mBAAA,CAAiB,cAAX,MAAI,qB,KAIHe,KAAA,CAAA4E,gBAAgB,CAACiB,aAAa,oB,cAAzC9G,mBAAA,CAWM,OAXNgH,WAWM,GAVJ9G,mBAAA,CASM,OATN+G,WASM,GARJ/G,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAoC;MAA7BJ,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BI,mBAAA,CAAiF;MAA1EwC,IAAI,EAAC,MAAM;MA9LhC,uBAAArC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8L0CF,KAAA,CAAA4E,gBAAgB,CAACqB,aAAa,GAAA/F,MAAA;MAAErB,KAAK,EAAC;mDAAtCmB,KAAA,CAAA4E,gBAAgB,CAACqB,aAAa,E,KAE5DhH,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAoC;MAA7BJ,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BI,mBAAA,CAAiF;MAA1EwC,IAAI,EAAC,MAAM;MAlMhC,uBAAArC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAkM0CF,KAAA,CAAA4E,gBAAgB,CAACsB,aAAa,GAAAhG,MAAA;MAAErB,KAAK,EAAC;mDAAtCmB,KAAA,CAAA4E,gBAAgB,CAACsB,aAAa,E,WAlMxElH,mBAAA,e,GAwMMC,mBAAA,CAWM,OAXNkH,WAWM,G,4BAVJlH,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BW,YAAA,CAEiBM,yBAAA;MA5MzBC,UAAA,EA0MiCC,KAAA,CAAA4E,gBAAgB,CAACwB,YAAY;MA1M9D,uBAAAhH,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA0MiCF,KAAA,CAAA4E,gBAAgB,CAACwB,YAAY,GAAAlG,MAAA;;MA1M9DE,OAAA,EAAAC,QAAA,CA0MgE,MAExDjB,MAAA,SAAAA,MAAA,QA5MRkB,gBAAA,CA0MgE,YAExD,E;MA5MRC,CAAA;uCA6MQf,YAAA,CAEiBM,yBAAA;MA/MzBC,UAAA,EA6MiCC,KAAA,CAAA4E,gBAAgB,CAACyB,WAAW;MA7M7D,uBAAAjH,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA6MiCF,KAAA,CAAA4E,gBAAgB,CAACyB,WAAW,GAAAnG,MAAA;;MA7M7DE,OAAA,EAAAC,QAAA,CA6M+D,MAEvDjB,MAAA,SAAAA,MAAA,QA/MRkB,gBAAA,CA6M+D,UAEvD,E;MA/MRC,CAAA;uCAgNQf,YAAA,CAEiBM,yBAAA;MAlNzBC,UAAA,EAgNiCC,KAAA,CAAA4E,gBAAgB,CAAC0B,gBAAgB;MAhNlE,uBAAAlH,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAgNiCF,KAAA,CAAA4E,gBAAgB,CAAC0B,gBAAgB,GAAApG,MAAA;;MAhNlEE,OAAA,EAAAC,QAAA,CAgNoE,MAE5DjB,MAAA,SAAAA,MAAA,QAlNRkB,gBAAA,CAgNoE,aAE5D,E;MAlNRC,CAAA;;IAAAA,CAAA;6DAsNIvB,mBAAA,cAAiB,EACjBQ,YAAA,CA8BY4C,oBAAA;IArPhBrC,UAAA,EAuNwBC,KAAA,CAAAuG,eAAe,CAACjE,IAAI;IAvN5C,uBAAAlD,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAuNwBF,KAAA,CAAAuG,eAAe,CAACjE,IAAI,GAAApC,MAAA;IAAEqC,KAAK,EAAC,UAAU;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAElD,QAAA,CAAAkH,gBAAgB;IACtG9D,OAAO,EAAE1C,KAAA,CAAA2C;;IAxNhBvC,OAAA,EAAAC,QAAA,CAyNM,MAQM,CARNpB,mBAAA,CAQM,OARNwH,WAQM,G,4BAPJxH,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCI,mBAAA,CAKM,OALNyH,WAKM,I,kBAJJ3H,mBAAA,CAGiByB,SAAA,QA/N3BC,WAAA,CA4NyCnB,QAAA,CAAAqH,iBAAiB,EAAzB/F,IAAI;2BAA3BwE,YAAA,CAGiBtF,yBAAA;QAHkChB,GAAG,EAAE8B,IAAI,CAACC,EAAE;QA5NzEd,UAAA,EA6NqBC,KAAA,CAAAuG,eAAe,CAAClB,aAAa,CAACzE,IAAI,CAACC,EAAE;QA7N1D,uBAAAX,MAAA,IA6NqBF,KAAA,CAAAuG,eAAe,CAAClB,aAAa,CAACzE,IAAI,CAACC,EAAE,IAAAX;;QA7N1DE,OAAA,EAAAC,QAAA,CA8NY,MAAe,CA9N3BC,gBAAA,CAAAS,gBAAA,CA8NeH,IAAI,CAACI,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGH,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QA/NVV,CAAA;;wCAmOMtB,mBAAA,CAOM,OAPN2H,WAOM,G,4BANJ3H,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCI,mBAAA,CAIS;MAzOjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAqOyBF,KAAA,CAAAuG,eAAe,CAACjD,QAAQ,GAAApD,MAAA;MAAErB,KAAK,EAAC;2BAC/CE,mBAAA,CAESyB,SAAA,QAxOnBC,WAAA,CAsOmCC,IAAA,CAAA6C,QAAQ,EAAlBC,MAAM;2BAArBzE,mBAAA,CAES;QAF2BD,GAAG,EAAE0E,MAAM,CAAC3C,EAAE;QAAGc,KAAK,EAAE6B,MAAM,CAAC3C;0BAC9D2C,MAAM,CAACxC,IAAI,wBAvO1B6F,WAAA;6EAqOyB7G,KAAA,CAAAuG,eAAe,CAACjD,QAAQ,E,KAO3CrE,mBAAA,CAQM,OARN6H,WAQM,G,4BAPJ7H,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BW,YAAA,CAEiBM,yBAAA;MAhPzBC,UAAA,EA8OiCC,KAAA,CAAAuG,eAAe,CAACQ,iBAAiB;MA9OlE,uBAAA3H,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8OiCF,KAAA,CAAAuG,eAAe,CAACQ,iBAAiB,GAAA7G,MAAA;;MA9OlEE,OAAA,EAAAC,QAAA,CA8OoE,MAE5DjB,MAAA,SAAAA,MAAA,QAhPRkB,gBAAA,CA8OoE,eAE5D,E;MAhPRC,CAAA;uCAiPQf,YAAA,CAEiBM,yBAAA;MAnPzBC,UAAA,EAiPiCC,KAAA,CAAAuG,eAAe,CAACS,iBAAiB;MAjPlE,uBAAA5H,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAiPiCF,KAAA,CAAAuG,eAAe,CAACS,iBAAiB,GAAA9G,MAAA;;MAjPlEE,OAAA,EAAAC,QAAA,CAiPoE,MAE5DjB,MAAA,SAAAA,MAAA,QAnPRkB,gBAAA,CAiPoE,aAE5D,E;MAnPRC,CAAA;;IAAAA,CAAA;6DAuPIvB,mBAAA,cAAiB,EACjBQ,YAAA,CAyCY4C,oBAAA;IAjShBrC,UAAA,EAwPwBC,KAAA,CAAAiH,mBAAmB,CAAC3E,IAAI;IAxPhD,uBAAAlD,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAwPwBF,KAAA,CAAAiH,mBAAmB,CAAC3E,IAAI,GAAApC,MAAA;IAAEqC,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAAC7C,IAAI,EAAC,sBAAsB;IAACwH,MAAM,EAAN,EAAM;IAChH1E,SAAO,EAAElD,QAAA,CAAA6H,cAAc;IAAGzE,OAAO,EAAE1C,KAAA,CAAA2C;;IAzP1CvC,OAAA,EAAAC,QAAA,CA0PM,MAEM,C,4BAFNpB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAA4C,IACrDI,mBAAA,CAA+C,WAA5C,0CAAwC,E,sBAG7CA,mBAAA,CAQM,OARNmI,WAQM,G,4BAPJnI,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCI,mBAAA,CAKM,OALNoI,WAKM,I,kBAJJtI,mBAAA,CAGiByB,SAAA,QApQ3BC,WAAA,CAiQyCnB,QAAA,CAAAqH,iBAAiB,EAAzB/F,IAAI;2BAA3BwE,YAAA,CAGiBtF,yBAAA;QAHkChB,GAAG,EAAE8B,IAAI,CAACC,EAAE;QAjQzEd,UAAA,EAkQqBC,KAAA,CAAAiH,mBAAmB,CAAC5B,aAAa,CAACzE,IAAI,CAACC,EAAE;QAlQ9D,uBAAAX,MAAA,IAkQqBF,KAAA,CAAAiH,mBAAmB,CAAC5B,aAAa,CAACzE,IAAI,CAACC,EAAE,IAAAX;;QAlQ9DE,OAAA,EAAAC,QAAA,CAmQY,MAAe,CAnQ3BC,gBAAA,CAAAS,gBAAA,CAmQeH,IAAI,CAACI,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGH,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QApQVV,CAAA;;wCAwQMtB,mBAAA,CAOM,OAPNqI,WAOM,G,4BANJrI,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCI,mBAAA,CAIS;MA9QjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA0QyBF,KAAA,CAAAiH,mBAAmB,CAAC3D,QAAQ,GAAApD,MAAA;MAAErB,KAAK,EAAC;2BACnDE,mBAAA,CAESyB,SAAA,QA7QnBC,WAAA,CA2QmCnB,QAAA,CAAAiI,iBAAiB,EAA3B/D,MAAM;2BAArBzE,mBAAA,CAES;QAFoCD,GAAG,EAAE0E,MAAM,CAAC3C,EAAE;QAAGc,KAAK,EAAE6B,MAAM,CAAC3C;0BACvE2C,MAAM,CAACxC,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAGyC,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA1C,gBAAA,CAAGyC,MAAM,CAACE,UAAU,IAAG,KAC9E,uBA7QV8D,WAAA;6EA0QyBxH,KAAA,CAAAiH,mBAAmB,CAAC3D,QAAQ,E,KAO/CrE,mBAAA,CASM,OATNwI,WASM,G,4BARJxI,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BI,mBAAA,CAMS;MAzRjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAmRyBF,KAAA,CAAAiH,mBAAmB,CAACS,MAAM,GAAAxH,MAAA;MAAErB,KAAK,EAAC;oCACjDI,mBAAA,CAAiD;MAAzC0C,KAAK,EAAC;IAAmB,GAAC,QAAM,qBACxC1C,mBAAA,CAA2C;MAAnC0C,KAAK,EAAC;IAAe,GAAC,MAAI,qBAClC1C,mBAAA,CAA6C;MAArC0C,KAAK,EAAC;IAAiB,GAAC,MAAI,qBACpC1C,mBAAA,CAAwC;MAAhC0C,KAAK,EAAC;IAAY,GAAC,MAAI,qBAC/B1C,mBAAA,CAAmC;MAA3B0C,KAAK,EAAC;IAAO,GAAC,MAAI,oB,2CALX3B,KAAA,CAAAiH,mBAAmB,CAACS,MAAM,E,KAS7CzI,mBAAA,CAIM,OAJN0I,WAIM,G,4BAHJ1I,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BI,mBAAA,CACuC;MA/R/C,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8R2BF,KAAA,CAAAiH,mBAAmB,CAACW,WAAW,GAAA1H,MAAA;MAAErB,KAAK,EAAC,cAAc;MAACgJ,IAAI,EAAC,GAAG;MAC/EC,WAAW,EAAC;mDADK9H,KAAA,CAAAiH,mBAAmB,CAACW,WAAW,E;IA9R1DrH,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}