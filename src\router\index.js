import { createRouter, createWebHistory } from 'vue-router'
import SecurityOverview from '../views/SecurityOverview.vue'
import HostManagement from '../views/HostManagement.vue'
import PasswordPolicies from '../views/PasswordPolicies.vue'
import ScheduledTasks from '../views/ScheduledTasks.vue'
import NotificationTest from '../views/NotificationTest.vue'

const routes = [
    {
        path: '/',
        redirect: '/overview'
    },
    {
        path: '/overview',
        name: 'SecurityOverview',
        component: SecurityOverview,
        meta: { title: '安全概览', icon: 'tachometer-alt' }
    },
    {
        path: '/hosts',
        name: 'HostManagement',
        component: HostManagement,
        meta: { title: '主机管理', icon: 'server' }
    },
    {
        path: '/policies',
        name: 'PasswordPolicies',
        component: PasswordPolicies,
        meta: { title: '密码策略', icon: 'shield-alt' }
    },
    {
        path: '/tasks',
        name: 'ScheduledTasks',
        component: ScheduledTasks,
        meta: { title: '定时任务', icon: 'clock' }
    },
    {
        path: '/test',
        name: 'NotificationTest',
        component: NotificationTest,
        meta: { title: '通知测试', icon: 'bell' }
    }
]

const router = createRouter({
    history: createWebHistory(),
    routes
})

router.beforeEach((to, from, next) => {
    // 设置文档标题
    document.title = `${to.meta.title || '主页'} - 密码管理系统`
    next()
})

export default router 