{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, vModelText as _vModelText, withDirectives as _withDirectives, resolveComponent as _resolveComponent, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, vModelCheckbox as _vModelCheckbox, withCtx as _withCtx, createTextVNode as _createTextVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"bg-white shadow rounded-lg p-4 mb-6\"\n};\nconst _hoisted_2 = {\n  class: \"flex flex-wrap items-center justify-between\"\n};\nconst _hoisted_3 = {\n  class: \"flex items-center space-x-4\"\n};\nconst _hoisted_4 = {\n  class: \"relative\"\n};\nconst _hoisted_5 = {\n  class: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n};\nconst _hoisted_6 = {\n  class: \"grid grid-cols-1 md:grid-cols-2 gap-6\"\n};\nconst _hoisted_7 = {\n  class: \"flex justify-between items-start mb-2\"\n};\nconst _hoisted_8 = {\n  class: \"text-lg font-medium\"\n};\nconst _hoisted_9 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_10 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_11 = {\n  class: \"grid grid-cols-2 gap-4 mb-4\"\n};\nconst _hoisted_12 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_13 = {\n  class: \"text-sm\"\n};\nconst _hoisted_14 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_15 = {\n  class: \"text-sm\"\n};\nconst _hoisted_16 = {\n  class: \"flex justify-end space-x-3\"\n};\nconst _hoisted_17 = [\"onClick\"];\nconst _hoisted_18 = [\"onClick\"];\nconst _hoisted_19 = {\n  class: \"form-group\"\n};\nconst _hoisted_20 = {\n  class: \"form-group\"\n};\nconst _hoisted_21 = {\n  class: \"form-group\"\n};\nconst _hoisted_22 = {\n  class: \"space-y-3\"\n};\nconst _hoisted_23 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_24 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_25 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_26 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_27 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_28 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_29 = {\n  class: \"form-group\"\n};\nconst _hoisted_30 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_31 = {\n  class: \"form-group\"\n};\nconst _hoisted_32 = {\n  class: \"flex items-center\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_BaseModal = _resolveComponent(\"BaseModal\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createCommentVNode(\" 顶部操作区域 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n    class: \"flex items-center\"\n  }, [_createElementVNode(\"h2\", {\n    class: \"text-lg font-semibold\"\n  }, \"密码策略管理\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" 搜索框 \"), _createElementVNode(\"div\", _hoisted_4, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.searchText = $event),\n    placeholder: \"搜索策略...\",\n    class: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.searchText]]), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"text-gray-400\"\n  })])]), _createCommentVNode(\" 新建策略按钮 \"), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.showNewPolicyModal && $options.showNewPolicyModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'plus'],\n    class: \"mr-2\"\n  }), _cache[14] || (_cache[14] = _createElementVNode(\"span\", null, \"新建策略\", -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_6, [_createCommentVNode(\" 策略卡片 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: policy.id,\n      class: \"card\"\n    }, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", null, [_createElementVNode(\"h3\", _hoisted_8, _toDisplayString(policy.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_9, _toDisplayString(policy.description), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_10, _toDisplayString(policy.hostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'lock'],\n      class: \"text-gray-400 mr-2\"\n    }), _createElementVNode(\"span\", _hoisted_13, \"最小长度: \" + _toDisplayString(policy.minLength), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'calendar-alt'],\n      class: \"text-gray-400 mr-2\"\n    }), _createElementVNode(\"span\", _hoisted_15, \"过期时间: \" + _toDisplayString(policy.expiryDays) + \" 天\", 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"button\", {\n      class: \"text-gray-600 hover:text-gray-800 text-sm\",\n      onClick: $event => $options.editPolicy(policy)\n    }, \" 编辑 \", 8 /* PROPS */, _hoisted_17), _createElementVNode(\"button\", {\n      class: \"text-red-600 hover:text-red-800 text-sm\",\n      onClick: $event => $options.confirmDeletePolicy(policy)\n    }, \" 删除 \", 8 /* PROPS */, _hoisted_18)])]);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 新建/编辑策略弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.policyModal.show,\n    \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $data.policyModal.show = $event),\n    title: $data.policyModal.isEdit ? '编辑密码策略' : '新建密码策略',\n    \"confirm-text\": $data.policyModal.isEdit ? '保存更改' : '创建策略',\n    onConfirm: $options.savePolicyChanges,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_19, [_cache[16] || (_cache[16] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"策略名称\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.policyModal.form.name = $event),\n      class: \"form-control\",\n      placeholder: \"输入策略名称\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.policyModal.form.name]])]), _createElementVNode(\"div\", _hoisted_20, [_cache[17] || (_cache[17] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"策略描述\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.policyModal.form.description = $event),\n      class: \"form-control\",\n      placeholder: \"适用场景描述\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.policyModal.form.description]])]), _createElementVNode(\"div\", _hoisted_21, [_cache[24] || (_cache[24] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码规则\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_cache[18] || (_cache[18] = _createElementVNode(\"span\", {\n      class: \"w-32 text-sm\"\n    }, \"最小长度:\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"number\",\n      min: \"8\",\n      \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.policyModal.form.minLength = $event),\n      class: \"w-20 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.policyModal.form.minLength, void 0, {\n      number: true\n    }]])]), _createElementVNode(\"div\", _hoisted_24, [_cache[19] || (_cache[19] = _createElementVNode(\"span\", {\n      class: \"w-32 text-sm\"\n    }, \"必须包含大写字母:\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.policyModal.form.requireUppercase = $event)\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.policyModal.form.requireUppercase]])]), _createElementVNode(\"div\", _hoisted_25, [_cache[20] || (_cache[20] = _createElementVNode(\"span\", {\n      class: \"w-32 text-sm\"\n    }, \"必须包含小写字母:\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.policyModal.form.requireLowercase = $event)\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.policyModal.form.requireLowercase]])]), _createElementVNode(\"div\", _hoisted_26, [_cache[21] || (_cache[21] = _createElementVNode(\"span\", {\n      class: \"w-32 text-sm\"\n    }, \"必须包含数字:\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.policyModal.form.requireNumbers = $event)\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.policyModal.form.requireNumbers]])]), _createElementVNode(\"div\", _hoisted_27, [_cache[22] || (_cache[22] = _createElementVNode(\"span\", {\n      class: \"w-32 text-sm\"\n    }, \"必须包含特殊字符:\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.policyModal.form.requireSpecial = $event)\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.policyModal.form.requireSpecial]])]), _createElementVNode(\"div\", _hoisted_28, [_cache[23] || (_cache[23] = _createElementVNode(\"span\", {\n      class: \"w-32 text-sm\"\n    }, \"不能包含用户名:\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.policyModal.form.forbidUsername = $event)\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.policyModal.form.forbidUsername]])])])]), _createElementVNode(\"div\", _hoisted_29, [_cache[26] || (_cache[26] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码有效期\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_30, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"number\",\n      min: \"1\",\n      \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.policyModal.form.expiryDays = $event),\n      class: \"w-20 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.policyModal.form.expiryDays, void 0, {\n      number: true\n    }]]), _cache[25] || (_cache[25] = _createElementVNode(\"span\", {\n      class: \"ml-2\"\n    }, \"天\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_31, [_cache[29] || (_cache[29] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码历史\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_32, [_cache[27] || (_cache[27] = _createElementVNode(\"span\", {\n      class: \"text-sm\"\n    }, \"不能重复使用最近\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"number\",\n      min: \"1\",\n      \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $data.policyModal.form.historyCount = $event),\n      class: \"mx-2 w-16 px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.policyModal.form.historyCount, void 0, {\n      number: true\n    }]]), _cache[28] || (_cache[28] = _createElementVNode(\"span\", {\n      class: \"text-sm\"\n    }, \"次使用过的密码\", -1 /* HOISTED */))])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"title\", \"confirm-text\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 删除确认弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.deleteModal.show,\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.deleteModal.show = $event),\n    title: \"确认删除策略\",\n    \"confirm-text\": \"删除\",\n    danger: \"\",\n    onConfirm: $options.deletePolicy,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"p\", null, [_cache[30] || (_cache[30] = _createTextVNode(\"您确定要删除策略 \")), _createElementVNode(\"strong\", null, _toDisplayString($data.deleteModal.policyName), 1 /* TEXT */), _cache[31] || (_cache[31] = _createTextVNode(\" 吗？\"))]), _cache[32] || (_cache[32] = _createElementVNode(\"p\", {\n      class: \"mt-2 text-red-600\"\n    }, \"此操作无法撤销，删除后将影响所有使用此策略的主机。\", -1 /* HOISTED */))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "type", "_cache", "$event", "$data", "searchText", "placeholder", "_hoisted_5", "_createVNode", "_component_font_awesome_icon", "icon", "onClick", "args", "$options", "showNewPolicyModal", "_hoisted_6", "_Fragment", "_renderList", "_ctx", "policies", "policy", "key", "id", "_hoisted_7", "_hoisted_8", "_toDisplayString", "name", "_hoisted_9", "description", "_hoisted_10", "hostsCount", "_hoisted_11", "_hoisted_12", "_hoisted_13", "<PERSON><PERSON><PERSON><PERSON>", "_hoisted_14", "_hoisted_15", "expiryDays", "_hoisted_16", "editPolicy", "_hoisted_17", "confirmDeletePolicy", "_hoisted_18", "_component_BaseModal", "modelValue", "policyModal", "show", "title", "isEdit", "onConfirm", "savePolicyChanges", "loading", "processing", "default", "_withCtx", "_hoisted_19", "form", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "min", "number", "_hoisted_24", "requireUppercase", "_hoisted_25", "requireLowercase", "_hoisted_26", "requireNumbers", "_hoisted_27", "requireSpecial", "_hoisted_28", "forbidUsername", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "historyCount", "_", "deleteModal", "danger", "deletePolicy", "_createTextVNode", "policyName"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\PasswordPolicies.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 顶部操作区域 -->\r\n    <div class=\"bg-white shadow rounded-lg p-4 mb-6\">\r\n      <div class=\"flex flex-wrap items-center justify-between\">\r\n        <div class=\"flex items-center\">\r\n          <h2 class=\"text-lg font-semibold\">密码策略管理</h2>\r\n        </div>\r\n        \r\n        <div class=\"flex items-center space-x-4\">\r\n          <!-- 搜索框 -->\r\n          <div class=\"relative\">\r\n            <input \r\n              type=\"text\" \r\n              v-model=\"searchText\" \r\n              placeholder=\"搜索策略...\" \r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\r\n            />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 新建策略按钮 -->\r\n          <button \r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            @click=\"showNewPolicyModal\"\r\n          >\r\n            <font-awesome-icon :icon=\"['fas', 'plus']\" class=\"mr-2\" />\r\n            <span>新建策略</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n      <!-- 策略卡片 -->\r\n      <div v-for=\"policy in policies\" :key=\"policy.id\" class=\"card\">\r\n        <div class=\"flex justify-between items-start mb-2\">\r\n          <div>\r\n            <h3 class=\"text-lg font-medium\">{{ policy.name }}</h3>\r\n            <p class=\"text-sm text-gray-500\">{{ policy.description }}</p>\r\n          </div>\r\n          <div class=\"text-sm text-gray-500\">{{ policy.hostsCount }} 台主机</div>\r\n        </div>\r\n        <div class=\"grid grid-cols-2 gap-4 mb-4\">\r\n          <div class=\"flex items-center\">\r\n            <font-awesome-icon :icon=\"['fas', 'lock']\" class=\"text-gray-400 mr-2\" />\r\n            <span class=\"text-sm\">最小长度: {{ policy.minLength }}</span>\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <font-awesome-icon :icon=\"['fas', 'calendar-alt']\" class=\"text-gray-400 mr-2\" />\r\n            <span class=\"text-sm\">过期时间: {{ policy.expiryDays }} 天</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"flex justify-end space-x-3\">\r\n          <button \r\n            class=\"text-gray-600 hover:text-gray-800 text-sm\"\r\n            @click=\"editPolicy(policy)\"\r\n          >\r\n            编辑\r\n          </button>\r\n          <button \r\n            class=\"text-red-600 hover:text-red-800 text-sm\"\r\n            @click=\"confirmDeletePolicy(policy)\"\r\n          >\r\n            删除\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 新建/编辑策略弹窗 -->\r\n    <BaseModal\r\n      v-model=\"policyModal.show\"\r\n      :title=\"policyModal.isEdit ? '编辑密码策略' : '新建密码策略'\"\r\n      :confirm-text=\"policyModal.isEdit ? '保存更改' : '创建策略'\"\r\n      @confirm=\"savePolicyChanges\"\r\n      :loading=\"processing\"\r\n    >\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">策略名称</label>\r\n        <input \r\n          type=\"text\" \r\n          v-model=\"policyModal.form.name\" \r\n          class=\"form-control\" \r\n          placeholder=\"输入策略名称\"\r\n        >\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">策略描述</label>\r\n        <input \r\n          type=\"text\" \r\n          v-model=\"policyModal.form.description\" \r\n          class=\"form-control\" \r\n          placeholder=\"适用场景描述\"\r\n        >\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码规则</label>\r\n        <div class=\"space-y-3\">\r\n          <div class=\"flex items-center\">\r\n            <span class=\"w-32 text-sm\">最小长度:</span>\r\n            <input \r\n              type=\"number\" \r\n              min=\"8\" \r\n              v-model.number=\"policyModal.form.minLength\" \r\n              class=\"w-20 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\"\r\n            >\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <span class=\"w-32 text-sm\">必须包含大写字母:</span>\r\n            <input type=\"checkbox\" v-model=\"policyModal.form.requireUppercase\">\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <span class=\"w-32 text-sm\">必须包含小写字母:</span>\r\n            <input type=\"checkbox\" v-model=\"policyModal.form.requireLowercase\">\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <span class=\"w-32 text-sm\">必须包含数字:</span>\r\n            <input type=\"checkbox\" v-model=\"policyModal.form.requireNumbers\">\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <span class=\"w-32 text-sm\">必须包含特殊字符:</span>\r\n            <input type=\"checkbox\" v-model=\"policyModal.form.requireSpecial\">\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <span class=\"w-32 text-sm\">不能包含用户名:</span>\r\n            <input type=\"checkbox\" v-model=\"policyModal.form.forbidUsername\">\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码有效期</label>\r\n        <div class=\"flex items-center\">\r\n          <input \r\n            type=\"number\" \r\n            min=\"1\" \r\n            v-model.number=\"policyModal.form.expiryDays\"\r\n            class=\"w-20 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\"\r\n          >\r\n          <span class=\"ml-2\">天</span>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码历史</label>\r\n        <div class=\"flex items-center\">\r\n          <span class=\"text-sm\">不能重复使用最近</span>\r\n          <input \r\n            type=\"number\" \r\n            min=\"1\" \r\n            v-model.number=\"policyModal.form.historyCount\"\r\n            class=\"mx-2 w-16 px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\"\r\n          >\r\n          <span class=\"text-sm\">次使用过的密码</span>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 删除确认弹窗 -->\r\n    <BaseModal\r\n      v-model=\"deleteModal.show\"\r\n      title=\"确认删除策略\"\r\n      confirm-text=\"删除\"\r\n      danger\r\n      @confirm=\"deletePolicy\"\r\n      :loading=\"processing\"\r\n    >\r\n      <p>您确定要删除策略 <strong>{{ deleteModal.policyName }}</strong> 吗？</p>\r\n      <p class=\"mt-2 text-red-600\">此操作无法撤销，删除后将影响所有使用此策略的主机。</p>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\n\r\nexport default {\r\n  name: 'PasswordPolicies',\r\n  components: {\r\n    BaseModal\r\n  },\r\n  data() {\r\n    return {\r\n      processing: false,\r\n      \r\n      // 新建/编辑策略弹窗\r\n      policyModal: {\r\n        show: false,\r\n        isEdit: false,\r\n        policyId: null,\r\n        form: {\r\n          name: '',\r\n          description: '',\r\n          minLength: 12,\r\n          expiryDays: 30,\r\n          requireUppercase: true,\r\n          requireLowercase: true,\r\n          requireNumbers: true,\r\n          requireSpecial: true,\r\n          forbidUsername: true,\r\n          historyCount: 5\r\n        }\r\n      },\r\n      \r\n      // 删除确认弹窗\r\n      deleteModal: {\r\n        show: false,\r\n        policyId: null,\r\n        policyName: ''\r\n      },\r\n      \r\n      searchText: ''\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      policies: state => state.policies\r\n    })\r\n  },\r\n  methods: {\r\n    showNewPolicyModal() {\r\n      this.policyModal.isEdit = false\r\n      this.policyModal.policyId = null\r\n      this.resetPolicyForm()\r\n      this.policyModal.show = true\r\n    },\r\n    \r\n    editPolicy(policy) {\r\n      this.policyModal.isEdit = true\r\n      this.policyModal.policyId = policy.id\r\n      this.policyModal.form = { ...policy }\r\n      this.policyModal.show = true\r\n    },\r\n    \r\n    confirmDeletePolicy(policy) {\r\n      this.deleteModal.policyId = policy.id\r\n      this.deleteModal.policyName = policy.name\r\n      this.deleteModal.show = true\r\n    },\r\n    \r\n    resetPolicyForm() {\r\n      this.policyModal.form = {\r\n        name: '',\r\n        description: '',\r\n        minLength: 12,\r\n        expiryDays: 30,\r\n        requireUppercase: true,\r\n        requireLowercase: true,\r\n        requireNumbers: true,\r\n        requireSpecial: true,\r\n        forbidUsername: true,\r\n        historyCount: 5\r\n      }\r\n    },\r\n    \r\n    validatePolicyForm() {\r\n      if (!this.policyModal.form.name) {\r\n        alert('请输入策略名称')\r\n        return false\r\n      }\r\n      \r\n      if (this.policyModal.form.minLength < 8) {\r\n        alert('密码最小长度不能小于8')\r\n        return false\r\n      }\r\n      \r\n      return true\r\n    },\r\n    \r\n    async savePolicyChanges() {\r\n      if (!this.validatePolicyForm()) {\r\n        return\r\n      }\r\n      \r\n      this.processing = true\r\n      \r\n      try {\r\n        if (this.policyModal.isEdit) {\r\n          this.$store.commit('updatePolicy', {\r\n            id: this.policyModal.policyId,\r\n            ...this.policyModal.form\r\n          })\r\n        } else {\r\n          this.$store.commit('addPolicy', { ...this.policyModal.form })\r\n        }\r\n        \r\n        this.policyModal.show = false\r\n        \r\n        // 提示用户操作成功\r\n        alert(`策略${this.policyModal.isEdit ? '更新' : '创建'}成功！`)\r\n      } catch (error) {\r\n        console.error('保存策略失败', error)\r\n        alert('保存策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n    \r\n    async deletePolicy() {\r\n      this.processing = true\r\n      \r\n      try {\r\n        this.$store.commit('deletePolicy', this.deleteModal.policyId)\r\n        this.deleteModal.show = false\r\n        \r\n        // 提示用户操作成功\r\n        alert('策略删除成功！')\r\n      } catch (error) {\r\n        console.error('删除策略失败', error)\r\n        alert('删除策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script> "], "mappings": ";;EAGSA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAA6C;;EAKjDA,KAAK,EAAC;AAA6B;;EAEjCA,KAAK,EAAC;AAAU;;EAOdA,KAAK,EAAC;AAAsE;;EAiBpFA,KAAK,EAAC;AAAuC;;EAGzCA,KAAK,EAAC;AAAuC;;EAE1CA,KAAK,EAAC;AAAqB;;EAC5BA,KAAK,EAAC;AAAuB;;EAE7BA,KAAK,EAAC;AAAuB;;EAE/BA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAAmB;;EAEtBA,KAAK,EAAC;AAAS;;EAElBA,KAAK,EAAC;AAAmB;;EAEtBA,KAAK,EAAC;AAAS;;EAGpBA,KAAK,EAAC;AAA4B;oBAvD/C;oBAAA;;EAgFWA,KAAK,EAAC;AAAY;;EAUlBA,KAAK,EAAC;AAAY;;EAUlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmB;;EASzBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EAO7BA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;;EAW3BA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;;;;uBArJpCC,mBAAA,CA8KM,cA7KJC,mBAAA,YAAe,EACfC,mBAAA,CA8BM,OA9BNC,UA8BM,GA7BJD,mBAAA,CA4BM,OA5BNE,UA4BM,G,4BA3BJF,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAmB,IAC5BG,mBAAA,CAA6C;IAAzCH,KAAK,EAAC;EAAuB,GAAC,QAAM,E,sBAG1CG,mBAAA,CAsBM,OAtBNG,UAsBM,GArBJJ,mBAAA,SAAY,EACZC,mBAAA,CAUM,OAVNI,UAUM,G,gBATJJ,mBAAA,CAKE;IAJAK,IAAI,EAAC,MAAM;IAbzB,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAcuBC,KAAA,CAAAC,UAAU,GAAAF,MAAA;IACnBG,WAAW,EAAC,SAAS;IACrBb,KAAK,EAAC;iDAFGW,KAAA,CAAAC,UAAU,E,GAIrBT,mBAAA,CAEM,OAFNW,UAEM,GADJC,YAAA,CAAqEC,4BAAA;IAAjDC,IAAI,EAAE,iBAAiB;IAAEjB,KAAK,EAAC;UAIvDE,mBAAA,YAAe,EACfC,mBAAA,CAMS;IALPH,KAAK,EAAC,wNAAwN;IAC7NkB,OAAK,EAAAT,MAAA,QAAAA,MAAA,UAAAU,IAAA,KAAEC,QAAA,CAAAC,kBAAA,IAAAD,QAAA,CAAAC,kBAAA,IAAAF,IAAA,CAAkB;MAE1BJ,YAAA,CAA0DC,4BAAA;IAAtCC,IAAI,EAAE,eAAe;IAAEjB,KAAK,EAAC;kCACjDG,mBAAA,CAAiB,cAAX,MAAI,qB,SAMlBA,mBAAA,CAmCM,OAnCNmB,UAmCM,GAlCJpB,mBAAA,UAAa,G,kBACbD,mBAAA,CAgCMsB,SAAA,QArEZC,WAAA,CAqC4BC,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;yBAAlB1B,mBAAA,CAgCM;MAhC2B2B,GAAG,EAAED,MAAM,CAACE,EAAE;MAAE7B,KAAK,EAAC;QACrDG,mBAAA,CAMM,OANN2B,UAMM,GALJ3B,mBAAA,CAGM,cAFJA,mBAAA,CAAsD,MAAtD4B,UAAsD,EAAAC,gBAAA,CAAnBL,MAAM,CAACM,IAAI,kBAC9C9B,mBAAA,CAA6D,KAA7D+B,UAA6D,EAAAF,gBAAA,CAAzBL,MAAM,CAACQ,WAAW,iB,GAExDhC,mBAAA,CAAoE,OAApEiC,WAAoE,EAAAJ,gBAAA,CAA9BL,MAAM,CAACU,UAAU,IAAG,MAAI,gB,GAEhElC,mBAAA,CASM,OATNmC,WASM,GARJnC,mBAAA,CAGM,OAHNoC,WAGM,GAFJxB,YAAA,CAAwEC,4BAAA;MAApDC,IAAI,EAAE,eAAe;MAAEjB,KAAK,EAAC;QACjDG,mBAAA,CAAyD,QAAzDqC,WAAyD,EAAnC,QAAM,GAAAR,gBAAA,CAAGL,MAAM,CAACc,SAAS,iB,GAEjDtC,mBAAA,CAGM,OAHNuC,WAGM,GAFJ3B,YAAA,CAAgFC,4BAAA;MAA5DC,IAAI,EAAE,uBAAuB;MAAEjB,KAAK,EAAC;QACzDG,mBAAA,CAA4D,QAA5DwC,WAA4D,EAAtC,QAAM,GAAAX,gBAAA,CAAGL,MAAM,CAACiB,UAAU,IAAG,IAAE,gB,KAGzDzC,mBAAA,CAaM,OAbN0C,WAaM,GAZJ1C,mBAAA,CAKS;MAJPH,KAAK,EAAC,2CAA2C;MAChDkB,OAAK,EAAAR,MAAA,IAAEU,QAAA,CAAA0B,UAAU,CAACnB,MAAM;OAC1B,MAED,iBA7DVoB,WAAA,GA8DU5C,mBAAA,CAKS;MAJPH,KAAK,EAAC,yCAAyC;MAC9CkB,OAAK,EAAAR,MAAA,IAAEU,QAAA,CAAA4B,mBAAmB,CAACrB,MAAM;OACnC,MAED,iBAnEVsB,WAAA,E;oCAwEI/C,mBAAA,eAAkB,EAClBa,YAAA,CAwFYmC,oBAAA;IAjKhBC,UAAA,EA0EexC,KAAA,CAAAyC,WAAW,CAACC,IAAI;IA1E/B,uBAAA5C,MAAA,SAAAA,MAAA,OAAAC,MAAA,IA0EeC,KAAA,CAAAyC,WAAW,CAACC,IAAI,GAAA3C,MAAA;IACxB4C,KAAK,EAAE3C,KAAA,CAAAyC,WAAW,CAACG,MAAM;IACzB,cAAY,EAAE5C,KAAA,CAAAyC,WAAW,CAACG,MAAM;IAChCC,SAAO,EAAEpC,QAAA,CAAAqC,iBAAiB;IAC1BC,OAAO,EAAE/C,KAAA,CAAAgD;;IA9EhBC,OAAA,EAAAC,QAAA,CAgFM,MAQM,CARN1D,mBAAA,CAQM,OARN2D,WAQM,G,4BAPJ3D,mBAAA,CAAsC;MAA/BH,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BG,mBAAA,CAKC;MAJCK,IAAI,EAAC,MAAM;MAnFrB,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAoFmBC,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAAC9B,IAAI,GAAAvB,MAAA;MAC9BV,KAAK,EAAC,cAAc;MACpBa,WAAW,EAAC;mDAFHF,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAAC9B,IAAI,E,KAMlC9B,mBAAA,CAQM,OARN6D,WAQM,G,4BAPJ7D,mBAAA,CAAsC;MAA/BH,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BG,mBAAA,CAKC;MAJCK,IAAI,EAAC,MAAM;MA7FrB,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA8FmBC,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAAC5B,WAAW,GAAAzB,MAAA;MACrCV,KAAK,EAAC,cAAc;MACpBa,WAAW,EAAC;mDAFHF,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAAC5B,WAAW,E,KAMzChC,mBAAA,CAiCM,OAjCN8D,WAiCM,G,4BAhCJ9D,mBAAA,CAAsC;MAA/BH,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BG,mBAAA,CA8BM,OA9BN+D,WA8BM,GA7BJ/D,mBAAA,CAQM,OARNgE,WAQM,G,4BAPJhE,mBAAA,CAAuC;MAAjCH,KAAK,EAAC;IAAc,GAAC,OAAK,sB,gBAChCG,mBAAA,CAKC;MAJCK,IAAI,EAAC,QAAQ;MACb4D,GAAG,EAAC,GAAG;MA3GrB,uBAAA3D,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA4G8BC,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAACtB,SAAS,GAAA/B,MAAA;MAC1CV,KAAK,EAAC;mDADUW,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAACtB,SAAS,E;MAAlC4B,MAAM,EAAd;IAA2C,E,MAI/ClE,mBAAA,CAGM,OAHNmE,WAGM,G,4BAFJnE,mBAAA,CAA2C;MAArCH,KAAK,EAAC;IAAc,GAAC,WAAS,sB,gBACpCG,mBAAA,CAAmE;MAA5DK,IAAI,EAAC,UAAU;MAlHlC,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAkH4CC,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAACQ,gBAAgB,GAAA7D,MAAA;uDAAjCC,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAACQ,gBAAgB,E,KAEnEpE,mBAAA,CAGM,OAHNqE,WAGM,G,4BAFJrE,mBAAA,CAA2C;MAArCH,KAAK,EAAC;IAAc,GAAC,WAAS,sB,gBACpCG,mBAAA,CAAmE;MAA5DK,IAAI,EAAC,UAAU;MAtHlC,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAsH4CC,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAACU,gBAAgB,GAAA/D,MAAA;uDAAjCC,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAACU,gBAAgB,E,KAEnEtE,mBAAA,CAGM,OAHNuE,WAGM,G,4BAFJvE,mBAAA,CAAyC;MAAnCH,KAAK,EAAC;IAAc,GAAC,SAAO,sB,gBAClCG,mBAAA,CAAiE;MAA1DK,IAAI,EAAC,UAAU;MA1HlC,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA0H4CC,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAACY,cAAc,GAAAjE,MAAA;uDAA/BC,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAACY,cAAc,E,KAEjExE,mBAAA,CAGM,OAHNyE,WAGM,G,4BAFJzE,mBAAA,CAA2C;MAArCH,KAAK,EAAC;IAAc,GAAC,WAAS,sB,gBACpCG,mBAAA,CAAiE;MAA1DK,IAAI,EAAC,UAAU;MA9HlC,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA8H4CC,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAACc,cAAc,GAAAnE,MAAA;uDAA/BC,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAACc,cAAc,E,KAEjE1E,mBAAA,CAGM,OAHN2E,WAGM,G,4BAFJ3E,mBAAA,CAA0C;MAApCH,KAAK,EAAC;IAAc,GAAC,UAAQ,sB,gBACnCG,mBAAA,CAAiE;MAA1DK,IAAI,EAAC,UAAU;MAlIlC,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAkI4CC,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAACgB,cAAc,GAAArE,MAAA;uDAA/BC,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAACgB,cAAc,E,SAKrE5E,mBAAA,CAWM,OAXN6E,WAWM,G,4BAVJ7E,mBAAA,CAAuC;MAAhCH,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BG,mBAAA,CAQM,OARN8E,WAQM,G,gBAPJ9E,mBAAA,CAKC;MAJCK,IAAI,EAAC,QAAQ;MACb4D,GAAG,EAAC,GAAG;MA5InB,uBAAA3D,MAAA,SAAAA,MAAA,OAAAC,MAAA,IA6I4BC,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAACnB,UAAU,GAAAlC,MAAA;MAC3CV,KAAK,EAAC;mDADUW,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAACnB,UAAU,E;MAAnCyB,MAAM,EAAd;IAA4C,E,gCAG9ClE,mBAAA,CAA2B;MAArBH,KAAK,EAAC;IAAM,GAAC,GAAC,qB,KAIxBG,mBAAA,CAYM,OAZN+E,WAYM,G,4BAXJ/E,mBAAA,CAAsC;MAA/BH,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BG,mBAAA,CASM,OATNgF,WASM,G,4BARJhF,mBAAA,CAAqC;MAA/BH,KAAK,EAAC;IAAS,GAAC,UAAQ,sB,gBAC9BG,mBAAA,CAKC;MAJCK,IAAI,EAAC,QAAQ;MACb4D,GAAG,EAAC,GAAG;MA1JnB,uBAAA3D,MAAA,SAAAA,MAAA,OAAAC,MAAA,IA2J4BC,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAACqB,YAAY,GAAA1E,MAAA;MAC7CV,KAAK,EAAC;mDADUW,KAAA,CAAAyC,WAAW,CAACW,IAAI,CAACqB,YAAY,E;MAArCf,MAAM,EAAd;IAA8C,E,gCAGhDlE,mBAAA,CAAoC;MAA9BH,KAAK,EAAC;IAAS,GAAC,SAAO,qB;IA9JvCqF,CAAA;sFAmKInF,mBAAA,YAAe,EACfa,YAAA,CAUYmC,oBAAA;IA9KhBC,UAAA,EAqKexC,KAAA,CAAA2E,WAAW,CAACjC,IAAI;IArK/B,uBAAA5C,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAqKeC,KAAA,CAAA2E,WAAW,CAACjC,IAAI,GAAA3C,MAAA;IACzB4C,KAAK,EAAC,QAAQ;IACd,cAAY,EAAC,IAAI;IACjBiC,MAAM,EAAN,EAAM;IACL/B,SAAO,EAAEpC,QAAA,CAAAoE,YAAY;IACrB9B,OAAO,EAAE/C,KAAA,CAAAgD;;IA1KhBC,OAAA,EAAAC,QAAA,CA4KM,MAAgE,CAAhE1D,mBAAA,CAAgE,Y,4BA5KtEsF,gBAAA,CA4KS,WAAS,IAAAtF,mBAAA,CAA6C,gBAAA6B,gBAAA,CAAlCrB,KAAA,CAAA2E,WAAW,CAACI,UAAU,kB,4BA5KnDD,gBAAA,CA4K+D,KAAG,G,+BAC5DtF,mBAAA,CAA0D;MAAvDH,KAAK,EAAC;IAAmB,GAAC,2BAAyB,qB;IA7K5DqF,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}