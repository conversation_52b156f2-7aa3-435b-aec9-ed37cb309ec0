{"ast": null, "code": "export default {\n  name: 'BaseModal',\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    title: {\n      type: String,\n      default: ''\n    },\n    confirmText: {\n      type: String,\n      default: ''\n    },\n    size: {\n      type: String,\n      default: 'md'\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    danger: {\n      type: Boolean,\n      default: false\n    },\n    icon: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      isAnimationComplete: false\n    };\n  },\n  computed: {\n    modalSize() {\n      const sizes = {\n        sm: 'max-w-sm',\n        md: 'max-w-md',\n        lg: 'max-w-lg',\n        xl: 'max-w-xl',\n        '2xl': 'max-w-2xl',\n        '3xl': 'max-w-3xl',\n        '4xl': 'max-w-4xl',\n        '5xl': 'max-w-5xl',\n        full: 'max-w-full'\n      };\n      return sizes[this.size] || sizes.md;\n    }\n  },\n  watch: {\n    modelValue(val) {\n      if (val) {\n        this.$nextTick(() => {\n          this.isAnimationComplete = true;\n          document.body.classList.add('overflow-hidden');\n        });\n      } else {\n        this.isAnimationComplete = false;\n        document.body.classList.remove('overflow-hidden');\n      }\n    }\n  },\n  methods: {\n    closeModal() {\n      this.isAnimationComplete = false;\n      setTimeout(() => {\n        this.$emit('update:modelValue', false);\n      }, 200);\n    },\n    confirmModal() {\n      this.$emit('confirm');\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "modelValue", "type", "Boolean", "default", "title", "String", "confirmText", "size", "loading", "danger", "icon", "data", "isAnimationComplete", "computed", "modalSize", "sizes", "sm", "md", "lg", "xl", "full", "watch", "val", "$nextTick", "document", "body", "classList", "add", "remove", "methods", "closeModal", "setTimeout", "$emit", "confirmModal"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\BaseModal.vue"], "sourcesContent": ["<template>\r\n  <Teleport to=\"body\">\r\n    <!-- 模态框背景遮罩 -->\r\n    <div \r\n      v-if=\"modelValue\" \r\n      class=\"fixed inset-0 z-50 bg-gray-900 bg-opacity-60 backdrop-blur-sm transition-opacity duration-300\"\r\n      :class=\"{ 'opacity-0': !isAnimationComplete, 'opacity-100': isAnimationComplete }\"\r\n      @click=\"closeModal\"\r\n    ></div>\r\n\r\n    <!-- 模态框容器 -->\r\n    <div \r\n      v-if=\"modelValue\" \r\n      class=\"fixed inset-0 z-50 flex items-center justify-center p-4 overflow-hidden\" \r\n      :class=\"{ 'opacity-0': !isAnimationComplete, 'opacity-100': isAnimationComplete }\"\r\n    >\r\n      <div \r\n        class=\"bg-white rounded-lg shadow-2xl shadow-gray-600/20 w-full relative overflow-hidden transform transition-all duration-300 ease-out\"\r\n        :class=\"[modalSize, { 'scale-95': !isAnimationComplete, 'scale-100': isAnimationComplete }]\"\r\n        @click.stop\r\n      >\r\n        <!-- 模态框标题栏 -->\r\n        <div class=\"px-6 py-4 flex items-center justify-between border-b border-gray-200\">\r\n          <div class=\"flex items-center space-x-3\">\r\n            <font-awesome-icon \r\n              v-if=\"icon\" \r\n              :icon=\"['fas', icon]\" \r\n              class=\"text-lg\"\r\n              :class=\"[danger ? 'text-red-500' : 'text-blue-500']\"\r\n            />\r\n            <h3 class=\"text-lg font-medium text-gray-900\">{{ title }}</h3>\r\n          </div>\r\n          <button \r\n            @click=\"closeModal\" \r\n            class=\"text-gray-400 hover:text-gray-500 focus:outline-none transition-colors\"\r\n          >\r\n            <font-awesome-icon :icon=\"['fas', 'times']\" />\r\n          </button>\r\n        </div>\r\n\r\n        <!-- 模态框内容区 -->\r\n        <div class=\"px-6 py-4 max-h-[70vh] overflow-y-auto\">\r\n          <slot></slot>\r\n        </div>\r\n\r\n        <!-- 模态框底部按钮区 -->\r\n        <div class=\"px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-end space-x-3\">\r\n          <button \r\n            @click=\"closeModal\" \r\n            class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors\"\r\n          >\r\n            取消\r\n          </button>\r\n          <button \r\n            @click=\"confirmModal\" \r\n            class=\"px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors\"\r\n            :class=\"[\r\n              danger \r\n                ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500' \r\n                : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'\r\n            ]\"\r\n            :disabled=\"loading\"\r\n          >\r\n            <span v-if=\"!loading\">{{ confirmText || '确认' }}</span>\r\n            <div v-else class=\"flex items-center justify-center\">\r\n              <svg class=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\r\n                <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n              </svg>\r\n              处理中...\r\n            </div>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </Teleport>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'BaseModal',\r\n  props: {\r\n    modelValue: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    confirmText: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    size: {\r\n      type: String,\r\n      default: 'md'\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    danger: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    icon: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isAnimationComplete: false\r\n    }\r\n  },\r\n  computed: {\r\n    modalSize() {\r\n      const sizes = {\r\n        sm: 'max-w-sm',\r\n        md: 'max-w-md',\r\n        lg: 'max-w-lg',\r\n        xl: 'max-w-xl',\r\n        '2xl': 'max-w-2xl',\r\n        '3xl': 'max-w-3xl',\r\n        '4xl': 'max-w-4xl',\r\n        '5xl': 'max-w-5xl',\r\n        full: 'max-w-full'\r\n      }\r\n      return sizes[this.size] || sizes.md\r\n    }\r\n  },\r\n  watch: {\r\n    modelValue(val) {\r\n      if (val) {\r\n        this.$nextTick(() => {\r\n          this.isAnimationComplete = true\r\n          document.body.classList.add('overflow-hidden')\r\n        })\r\n      } else {\r\n        this.isAnimationComplete = false\r\n        document.body.classList.remove('overflow-hidden')\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    closeModal() {\r\n      this.isAnimationComplete = false\r\n      setTimeout(() => {\r\n        this.$emit('update:modelValue', false)\r\n      }, 200)\r\n    },\r\n    confirmModal() {\r\n      this.$emit('confirm')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 添加一些过渡动画效果 */\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n  opacity: 0;\r\n}\r\n\r\n.scale-enter-active,\r\n.scale-leave-active {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.scale-enter-from,\r\n.scale-leave-to {\r\n  transform: scale(0.95);\r\n}\r\n</style> "], "mappings": "AA+EA,eAAe;EACbA,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE;IACLC,UAAU,EAAE;MACVC,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACLH,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX,CAAC;IACDG,WAAW,EAAE;MACXL,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX,CAAC;IACDI,IAAI,EAAE;MACJN,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX,CAAC;IACDK,OAAO,EAAE;MACPP,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDM,MAAM,EAAE;MACNR,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDO,IAAI,EAAE;MACJT,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX;EACF,CAAC;EACDQ,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,mBAAmB,EAAE;IACvB;EACF,CAAC;EACDC,QAAQ,EAAE;IACRC,SAASA,CAAA,EAAG;MACV,MAAMC,KAAI,GAAI;QACZC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACdC,EAAE,EAAE,UAAU;QACd,KAAK,EAAE,WAAW;QAClB,KAAK,EAAE,WAAW;QAClB,KAAK,EAAE,WAAW;QAClB,KAAK,EAAE,WAAW;QAClBC,IAAI,EAAE;MACR;MACA,OAAOL,KAAK,CAAC,IAAI,CAACR,IAAI,KAAKQ,KAAK,CAACE,EAAC;IACpC;EACF,CAAC;EACDI,KAAK,EAAE;IACLrB,UAAUA,CAACsB,GAAG,EAAE;MACd,IAAIA,GAAG,EAAE;QACP,IAAI,CAACC,SAAS,CAAC,MAAM;UACnB,IAAI,CAACX,mBAAkB,GAAI,IAAG;UAC9BY,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB;QAC/C,CAAC;MACH,OAAO;QACL,IAAI,CAACf,mBAAkB,GAAI,KAAI;QAC/BY,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB;MAClD;IACF;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,UAAUA,CAAA,EAAG;MACX,IAAI,CAAClB,mBAAkB,GAAI,KAAI;MAC/BmB,UAAU,CAAC,MAAM;QACf,IAAI,CAACC,KAAK,CAAC,mBAAmB,EAAE,KAAK;MACvC,CAAC,EAAE,GAAG;IACR,CAAC;IACDC,YAAYA,CAAA,EAAG;MACb,IAAI,CAACD,KAAK,CAAC,SAAS;IACtB;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}