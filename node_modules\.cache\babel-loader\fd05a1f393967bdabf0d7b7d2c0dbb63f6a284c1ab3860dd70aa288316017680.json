{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, vModelRadio as _vModelRadio, withDirectives as _withDirectives, vModelSelect as _vModelSelect, vModelText as _vModelText, normalizeClass as _normalizeClass, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"flex space-x-3 mb-6\"\n};\nconst _hoisted_2 = {\n  class: \"data-table\"\n};\nconst _hoisted_3 = [\"onClick\"];\nconst _hoisted_4 = {\n  class: \"form-group\"\n};\nconst _hoisted_5 = {\n  class: \"form-label\"\n};\nconst _hoisted_6 = {\n  class: \"form-group\"\n};\nconst _hoisted_7 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_8 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_9 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_10 = {\n  key: 0\n};\nconst _hoisted_11 = {\n  class: \"form-group\"\n};\nconst _hoisted_12 = [\"value\"];\nconst _hoisted_13 = {\n  class: \"form-group\"\n};\nconst _hoisted_14 = {\n  class: \"flex\"\n};\nconst _hoisted_15 = {\n  key: 1\n};\nconst _hoisted_16 = {\n  class: \"form-group\"\n};\nconst _hoisted_17 = {\n  class: \"form-group\"\n};\nconst _hoisted_18 = {\n  key: 0,\n  class: \"text-red-500 text-xs mt-1\"\n};\nconst _hoisted_19 = {\n  class: \"form-group\"\n};\nconst _hoisted_20 = {\n  class: \"form-group\"\n};\nconst _hoisted_21 = {\n  class: \"mb-2\"\n};\nconst _hoisted_22 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_23 = {\n  class: \"form-text\"\n};\nconst _hoisted_24 = {\n  class: \"form-group\"\n};\nconst _hoisted_25 = [\"value\"];\nconst _hoisted_26 = {\n  class: \"form-group\"\n};\nconst _hoisted_27 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_28 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_29 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_30 = {\n  key: 0,\n  class: \"mt-3\"\n};\nconst _hoisted_31 = {\n  class: \"grid grid-cols-2 gap-4\"\n};\nconst _hoisted_32 = {\n  class: \"form-group\"\n};\nconst _hoisted_33 = {\n  class: \"form-group\"\n};\nconst _hoisted_34 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_35 = {\n  class: \"form-group\"\n};\nconst _hoisted_36 = [\"value\"];\nconst _hoisted_37 = {\n  class: \"form-group\"\n};\nconst _hoisted_38 = {\n  class: \"form-group\"\n};\nconst _hoisted_39 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_40 = {\n  class: \"form-group\"\n};\nconst _hoisted_41 = [\"value\"];\nconst _hoisted_42 = {\n  class: \"form-group\"\n};\nconst _hoisted_43 = {\n  class: \"form-group\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_CustomCheckbox = _resolveComponent(\"CustomCheckbox\");\n  const _component_StatusBadge = _resolveComponent(\"StatusBadge\");\n  const _component_PasswordStrengthMeter = _resolveComponent(\"PasswordStrengthMeter\");\n  const _component_BaseModal = _resolveComponent(\"BaseModal\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createCommentVNode(\" 操作按钮 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"button\", {\n    class: \"btn-outline\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.showEmergencyReset && $options.showEmergencyReset(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'exclamation-triangle'],\n    class: \"mr-2 text-red-600\"\n  }), _cache[33] || (_cache[33] = _createElementVNode(\"span\", null, \"紧急重置\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"btn-outline\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.openBatchUpdateModal && $options.openBatchUpdateModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'key'],\n    class: \"mr-2\"\n  }), _cache[34] || (_cache[34] = _createElementVNode(\"span\", null, \"批量更新密码\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"btn-outline\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.openBatchApplyModal && $options.openBatchApplyModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"mr-2\"\n  }), _cache[35] || (_cache[35] = _createElementVNode(\"span\", null, \"批量应用策略\", -1 /* HOISTED */))])]), _createCommentVNode(\" 主机列表 \"), _createElementVNode(\"table\", _hoisted_2, [_createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, [_createVNode(_component_CustomCheckbox, {\n    modelValue: $data.selectAll,\n    \"onUpdate:modelValue\": [_cache[3] || (_cache[3] = $event => $data.selectAll = $event), $options.toggleSelectAll]\n  }, {\n    default: _withCtx(() => _cache[36] || (_cache[36] = [_createTextVNode(\" 主机名 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _cache[37] || (_cache[37] = _createElementVNode(\"th\", null, \"IP地址\", -1 /* HOISTED */)), _cache[38] || (_cache[38] = _createElementVNode(\"th\", null, \"状态\", -1 /* HOISTED */)), _cache[39] || (_cache[39] = _createElementVNode(\"th\", null, \"操作\", -1 /* HOISTED */))])]), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: host.id\n    }, [_createElementVNode(\"td\", null, [_createVNode(_component_CustomCheckbox, {\n      modelValue: host.selected,\n      \"onUpdate:modelValue\": $event => host.selected = $event\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name), 1 /* TEXT */)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"td\", null, _toDisplayString(host.ip), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createVNode(_component_StatusBadge, {\n      type: host.status\n    }, null, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"td\", null, [_createElementVNode(\"button\", {\n      class: \"text-blue-600 hover:text-blue-800\",\n      onClick: $event => $options.openChangePasswordModal(host)\n    }, \" 修改密码 \", 8 /* PROPS */, _hoisted_3)])]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 修改密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.changePasswordModal.show,\n    \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.changePasswordModal.show = $event),\n    title: \"修改密码\",\n    \"confirm-text\": \"确认更新\",\n    onConfirm: $options.updatePassword,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"label\", _hoisted_5, \"服务器: \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_6, [_cache[42] || (_cache[42] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"label\", _hoisted_8, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.changePasswordModal.method = $event),\n      value: \"auto\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.changePasswordModal.method]]), _cache[40] || (_cache[40] = _createElementVNode(\"span\", null, \"自动生成\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_9, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.changePasswordModal.method = $event),\n      value: \"manual\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.changePasswordModal.method]]), _cache[41] || (_cache[41] = _createElementVNode(\"span\", null, \"手动输入\", -1 /* HOISTED */))])])]), $data.changePasswordModal.method === 'auto' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_cache[43] || (_cache[43] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.changePasswordModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_12);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.changePasswordModal.policyId]])]), _createElementVNode(\"div\", _hoisted_13, [_cache[44] || (_cache[44] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_14, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.changePasswordModal.generatedPassword = $event),\n      class: \"form-control rounded-r-none\",\n      readonly: \"\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.changePasswordModal.generatedPassword]]), _createElementVNode(\"button\", {\n      class: \"bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded-r-md\",\n      onClick: _cache[8] || (_cache[8] = (...args) => $options.generatePassword && $options.generatePassword(...args))\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt']\n    })])]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.generatedPassword\n    }, null, 8 /* PROPS */, [\"password\"])])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_cache[45] || (_cache[45] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"新密码\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"password\",\n      \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.changePasswordModal.newPassword = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.changePasswordModal.newPassword]]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.newPassword\n    }, null, 8 /* PROPS */, [\"password\"])]), _createElementVNode(\"div\", _hoisted_17, [_cache[46] || (_cache[46] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"确认密码\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"password\",\n      \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.changePasswordModal.confirmPassword = $event),\n      class: _normalizeClass([\"form-control\", {\n        'border-red-500': $options.passwordMismatch\n      }])\n    }, null, 2 /* CLASS */), [[_vModelText, $data.changePasswordModal.confirmPassword]]), $options.passwordMismatch ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, \" 两次输入的密码不一致 \")) : _createCommentVNode(\"v-if\", true)])])), _createElementVNode(\"div\", _hoisted_19, [_cache[50] || (_cache[50] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.executeImmediately,\n      \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $data.changePasswordModal.executeImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[47] || (_cache[47] = [_createTextVNode(\" 立即执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.saveHistory,\n      \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $data.changePasswordModal.saveHistory = $event)\n    }, {\n      default: _withCtx(() => _cache[48] || (_cache[48] = [_createTextVNode(\" 保存密码历史记录 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.logAudit,\n      \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.changePasswordModal.logAudit = $event)\n    }, {\n      default: _withCtx(() => _cache[49] || (_cache[49] = [_createTextVNode(\" 记录审计日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量更新密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchUpdateModal.show,\n    \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.batchUpdateModal.show = $event),\n    title: \"批量更新密码\",\n    \"confirm-text\": \"开始更新\",\n    size: \"lg\",\n    onConfirm: $options.batchUpdatePasswords,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_20, [_cache[52] || (_cache[52] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatch,\n      \"onUpdate:modelValue\": [_cache[15] || (_cache[15] = $event => $data.selectAllBatch = $event), $options.toggleSelectAllBatch]\n    }, {\n      default: _withCtx(() => _cache[51] || (_cache[51] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_22, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchUpdateModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchUpdateModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_23, \"已选择 \" + _toDisplayString($options.selectedHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_24, [_cache[53] || (_cache[53] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.batchUpdateModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_25);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchUpdateModal.policyId]])]), _createElementVNode(\"div\", _hoisted_26, [_cache[58] || (_cache[58] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"label\", _hoisted_28, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"immediate\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[54] || (_cache[54] = _createElementVNode(\"span\", null, \"立即执行\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_29, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[18] || (_cache[18] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"scheduled\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[55] || (_cache[55] = _createElementVNode(\"span\", null, \"定时执行\", -1 /* HOISTED */))])]), $data.batchUpdateModal.executionTime === 'scheduled' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_30, [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", null, [_cache[56] || (_cache[56] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"日期\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"date\",\n      \"onUpdate:modelValue\": _cache[19] || (_cache[19] = $event => $data.batchUpdateModal.scheduledDate = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledDate]])]), _createElementVNode(\"div\", null, [_cache[57] || (_cache[57] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"时间\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"time\",\n      \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $data.batchUpdateModal.scheduledTime = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledTime]])])])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_32, [_cache[62] || (_cache[62] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[21] || (_cache[21] = $event => $data.batchUpdateModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[59] || (_cache[59] = [_createTextVNode(\" 忽略错误继续执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.detailedLog,\n      \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $data.batchUpdateModal.detailedLog = $event)\n    }, {\n      default: _withCtx(() => _cache[60] || (_cache[60] = [_createTextVNode(\" 记录详细日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.sendNotification,\n      \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.batchUpdateModal.sendNotification = $event)\n    }, {\n      default: _withCtx(() => _cache[61] || (_cache[61] = [_createTextVNode(\" 执行完成后发送通知 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量应用策略弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchApplyModal.show,\n    \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $data.batchApplyModal.show = $event),\n    title: \"批量应用密码策略\",\n    \"confirm-text\": \"应用策略\",\n    onConfirm: $options.batchApplyPolicy,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_33, [_cache[63] || (_cache[63] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_34, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchApplyModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchApplyModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_35, [_cache[64] || (_cache[64] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.batchApplyModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name), 9 /* TEXT, PROPS */, _hoisted_36);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchApplyModal.policyId]])]), _createElementVNode(\"div\", _hoisted_37, [_cache[67] || (_cache[67] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.updateImmediately,\n      \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.batchApplyModal.updateImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[65] || (_cache[65] = [_createTextVNode(\" 立即更新密码以符合策略 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.applyOnNextUpdate,\n      \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.batchApplyModal.applyOnNextUpdate = $event)\n    }, {\n      default: _withCtx(() => _cache[66] || (_cache[66] = [_createTextVNode(\" 下次密码更新时应用 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 紧急重置密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.emergencyResetModal.show,\n    \"onUpdate:modelValue\": _cache[32] || (_cache[32] = $event => $data.emergencyResetModal.show = $event),\n    title: \"紧急密码重置\",\n    \"confirm-text\": \"立即重置\",\n    icon: \"exclamation-triangle\",\n    danger: \"\",\n    onConfirm: $options.emergencyReset,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_cache[73] || (_cache[73] = _createElementVNode(\"div\", {\n      class: \"bg-red-50 text-red-700 p-3 rounded-md mb-4\"\n    }, [_createElementVNode(\"p\", null, \"紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_38, [_cache[68] || (_cache[68] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_39, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.emergencyResetModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.emergencyResetModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_40, [_cache[69] || (_cache[69] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用紧急策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[29] || (_cache[29] = $event => $data.emergencyResetModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.emergencyPolicies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_41);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.policyId]])]), _createElementVNode(\"div\", _hoisted_42, [_cache[71] || (_cache[71] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"操作原因\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[30] || (_cache[30] = $event => $data.emergencyResetModal.reason = $event),\n      class: \"form-select\"\n    }, _cache[70] || (_cache[70] = [_createElementVNode(\"option\", {\n      value: \"security_incident\"\n    }, \"安全事件响应\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"password_leak\"\n    }, \"密码泄露\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"abnormal_access\"\n    }, \"异常访问\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"compliance\"\n    }, \"合规要求\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"other\"\n    }, \"其他原因\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.reason]])]), _createElementVNode(\"div\", _hoisted_43, [_cache[72] || (_cache[72] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"附加说明\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n      \"onUpdate:modelValue\": _cache[31] || (_cache[31] = $event => $data.emergencyResetModal.description = $event),\n      class: \"form-control\",\n      rows: \"2\",\n      placeholder: \"请输入重置原因详细说明\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.emergencyResetModal.description]])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "onClick", "_cache", "args", "$options", "showEmergencyReset", "_createVNode", "_component_font_awesome_icon", "icon", "openBatchUpdateModal", "openBatchApplyModal", "_hoisted_2", "_component_CustomCheckbox", "modelValue", "$data", "selectAll", "$event", "toggleSelectAll", "default", "_withCtx", "_createTextVNode", "_", "_Fragment", "_renderList", "_ctx", "hosts", "host", "id", "selected", "_toDisplayString", "name", "ip", "_component_StatusBadge", "type", "status", "openChangePasswordModal", "_hoisted_3", "_component_BaseModal", "changePasswordModal", "show", "title", "onConfirm", "updatePassword", "loading", "processing", "_hoisted_4", "_hoisted_5", "currentHost", "_hoisted_6", "_hoisted_7", "_hoisted_8", "method", "value", "_hoisted_9", "_hoisted_10", "_hoisted_11", "policyId", "policies", "policy", "<PERSON><PERSON><PERSON><PERSON>", "expiryDays", "_hoisted_12", "_hoisted_13", "_hoisted_14", "generatedPassword", "readonly", "generatePassword", "_component_PasswordStrengthMeter", "password", "_hoisted_15", "_hoisted_16", "newPassword", "_hoisted_17", "confirmPassword", "_normalizeClass", "passwordMismatch", "_hoisted_18", "_hoisted_19", "executeImmediately", "saveHistory", "logAudit", "batchUpdateModal", "size", "batchUpdatePasswords", "_hoisted_20", "_hoisted_21", "selectAllBatch", "toggleSelectAllBatch", "_hoisted_22", "_createBlock", "selectedHosts", "_hoisted_23", "selectedHostsCount", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "executionTime", "_hoisted_29", "_hoisted_30", "_hoisted_31", "scheduledDate", "scheduledTime", "_hoisted_32", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "batchApplyPolicy", "_hoisted_33", "_hoisted_34", "selectedHostsList", "_hoisted_35", "_hoisted_36", "_hoisted_37", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "danger", "emergencyReset", "_hoisted_38", "_hoisted_39", "_hoisted_40", "emergencyPolicies", "_hoisted_41", "_hoisted_42", "reason", "_hoisted_43", "description", "rows", "placeholder"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"flex space-x-3 mb-6\">\r\n      <button class=\"btn-outline\" @click=\"showEmergencyReset\">\r\n        <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2 text-red-600\" />\r\n        <span>紧急重置</span>\r\n      </button>\r\n      <button class=\"btn-outline\" @click=\"openBatchUpdateModal\">\r\n        <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n        <span>批量更新密码</span>\r\n      </button>\r\n      <button class=\"btn-outline\" @click=\"openBatchApplyModal\">\r\n        <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n        <span>批量应用策略</span>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <table class=\"data-table\">\r\n      <thead>\r\n        <tr>\r\n          <th>\r\n            <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n              主机名\r\n            </CustomCheckbox>\r\n          </th>\r\n          <th>IP地址</th>\r\n          <th>状态</th>\r\n          <th>操作</th>\r\n        </tr>\r\n      </thead>\r\n      <tbody>\r\n        <tr v-for=\"host in hosts\" :key=\"host.id\">\r\n          <td>\r\n            <CustomCheckbox v-model=\"host.selected\">\r\n              {{ host.name }}\r\n            </CustomCheckbox>\r\n          </td>\r\n          <td>{{ host.ip }}</td>\r\n          <td>\r\n            <StatusBadge :type=\"host.status\" />\r\n          </td>\r\n          <td>\r\n            <button class=\"text-blue-600 hover:text-blue-800\" @click=\"openChangePasswordModal(host)\">\r\n              修改密码\r\n            </button>\r\n          </td>\r\n        </tr>\r\n      </tbody>\r\n    </table>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" confirm-text=\"确认更新\" @confirm=\"updatePassword\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">服务器: {{ currentHost.name }}</label>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"changePasswordModal.method\" value=\"auto\" class=\"mr-2\">\r\n            <span>自动生成</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"changePasswordModal.method\" value=\"manual\" class=\"mr-2\">\r\n            <span>手动输入</span>\r\n          </label>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-if=\"changePasswordModal.method === 'auto'\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">选择密码策略</label>\r\n          <select v-model=\"changePasswordModal.policyId\" class=\"form-select\">\r\n            <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n              {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n            </option>\r\n          </select>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <div class=\"flex\">\r\n            <input type=\"text\" v-model=\"changePasswordModal.generatedPassword\" class=\"form-control rounded-r-none\"\r\n              readonly>\r\n            <button class=\"bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded-r-md\" @click=\"generatePassword\">\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" />\r\n            </button>\r\n          </div>\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.generatedPassword\" />\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else>\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <input type=\"password\" v-model=\"changePasswordModal.newPassword\" class=\"form-control\">\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <input type=\"password\" v-model=\"changePasswordModal.confirmPassword\" class=\"form-control\"\r\n            :class=\"{ 'border-red-500': passwordMismatch }\">\r\n          <div v-if=\"passwordMismatch\" class=\"text-red-500 text-xs mt-1\">\r\n            两次输入的密码不一致\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行选项</label>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          立即执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          保存密码历史记录\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          记录审计日志\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      processing: false,\r\n      currentHost: {},\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host) {\r\n      this.currentHost = host\r\n      this.changePasswordModal.show = true\r\n      this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword() {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policy = this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policy ? policy.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      this.changePasswordModal.generatedPassword = password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 未使用的变量，注释掉\r\n        // const password = this.changePasswordModal.method === 'auto'\r\n        //   ? this.changePasswordModal.generatedPassword\r\n        //   : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          newStatus: 'normal'\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('batchUpdateHostPasswords', {\r\n          hostIds: selectedHostIds,\r\n          status: 'normal'\r\n        })\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('batchUpdateHostPasswords', {\r\n          hostIds: selectedHostIds,\r\n          status: 'normal'\r\n        })\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": ";;EAGSA,KAAK,EAAC;AAAqB;;EAgBzBA,KAAK,EAAC;AAAY;mBAnB7B;;EAuDWA,KAAK,EAAC;AAAY;;EACdA,KAAK,EAAC;AAAY;;EAGtBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EAlE1CC,GAAA;AAAA;;EA0EaD,KAAK,EAAC;AAAY;oBA1E/B;;EAmFaA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EArF3BC,GAAA;AAAA;;EAiGaD,KAAK,EAAC;AAAY;;EAMlBA,KAAK,EAAC;AAAY;;EAvG/BC,GAAA;EA2GuCD,KAAK,EAAC;;;EAMlCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAY;oBAjJ7B;;EA0JWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EAjK1CC,GAAA;EAuKmED,KAAK,EAAC;;;EAC1DA,KAAK,EAAC;AAAwB;;EAalCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;oBAhN7B;;EAyNWA,KAAK,EAAC;AAAY;;EAkBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;oBArP7B;;EA8PWA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;;;;;;uBAxQ3BE,mBAAA,CA8QM,cA7QJC,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNC,UAaM,GAZJD,mBAAA,CAGS;IAHDJ,KAAK,EAAC,aAAa;IAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,kBAAA,IAAAD,QAAA,CAAAC,kBAAA,IAAAF,IAAA,CAAkB;MACpDG,YAAA,CAAuFC,4BAAA;IAAnEC,IAAI,EAAE,+BAA+B;IAAEb,KAAK,EAAC;kCACjEI,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGS;IAHDJ,KAAK,EAAC,aAAa;IAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAK,oBAAA,IAAAL,QAAA,CAAAK,oBAAA,IAAAN,IAAA,CAAoB;MACtDG,YAAA,CAAyDC,4BAAA;IAArCC,IAAI,EAAE,cAAc;IAAEb,KAAK,EAAC;kCAChDI,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAGS;IAHDJ,KAAK,EAAC,aAAa;IAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAM,mBAAA,IAAAN,QAAA,CAAAM,mBAAA,IAAAP,IAAA,CAAmB;MACrDG,YAAA,CAAgEC,4BAAA;IAA5CC,IAAI,EAAE,qBAAqB;IAAEb,KAAK,EAAC;kCACvDI,mBAAA,CAAmB,cAAb,QAAM,qB,KAIhBD,mBAAA,UAAa,EACbC,mBAAA,CA+BQ,SA/BRY,UA+BQ,GA9BNZ,mBAAA,CAWQ,gBAVNA,mBAAA,CASK,aARHA,mBAAA,CAIK,aAHHO,YAAA,CAEiBM,yBAAA;IAzB7BC,UAAA,EAuBqCC,KAAA,CAAAC,SAAS;IAvB9C,wB,oCAuBqCD,KAAA,CAAAC,SAAS,GAAAC,MAAA,GAAsBZ,QAAA,CAAAa,eAAe;;IAvBnFC,OAAA,EAAAC,QAAA,CAuBqF,MAEzEjB,MAAA,SAAAA,MAAA,QAzBZkB,gBAAA,CAuBqF,OAEzE,E;IAzBZC,CAAA;0FA2BUtB,mBAAA,CAAa,YAAT,MAAI,sB,4BACRA,mBAAA,CAAW,YAAP,IAAE,sB,4BACNA,mBAAA,CAAW,YAAP,IAAE,qB,KAGVA,mBAAA,CAiBQ,iB,kBAhBNF,mBAAA,CAeKyB,SAAA,QAhDbC,WAAA,CAiC2BC,IAAA,CAAAC,KAAK,EAAbC,IAAI;yBAAf7B,mBAAA,CAeK;MAfsBD,GAAG,EAAE8B,IAAI,CAACC;QACnC5B,mBAAA,CAIK,aAHHO,YAAA,CAEiBM,yBAAA;MArC7BC,UAAA,EAmCqCa,IAAI,CAACE,QAAQ;MAnClD,uBAAAZ,MAAA,IAmCqCU,IAAI,CAACE,QAAQ,GAAAZ;;MAnClDE,OAAA,EAAAC,QAAA,CAoCc,MAAe,CApC7BC,gBAAA,CAAAS,gBAAA,CAoCiBH,IAAI,CAACI,IAAI,iB;MApC1BT,CAAA;kFAuCUtB,mBAAA,CAAsB,YAAA8B,gBAAA,CAAfH,IAAI,CAACK,EAAE,kBACdhC,mBAAA,CAEK,aADHO,YAAA,CAAmC0B,sBAAA;MAArBC,IAAI,EAAEP,IAAI,CAACQ;yCAE3BnC,mBAAA,CAIK,aAHHA,mBAAA,CAES;MAFDJ,KAAK,EAAC,mCAAmC;MAAEM,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAA+B,uBAAuB,CAACT,IAAI;OAAG,QAEzF,iBA9CZU,UAAA,E;sCAoDItC,mBAAA,YAAe,EACfQ,YAAA,CAwEY+B,oBAAA;IA7HhBxB,UAAA,EAqDwBC,KAAA,CAAAwB,mBAAmB,CAACC,IAAI;IArDhD,uBAAArC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAqDwBF,KAAA,CAAAwB,mBAAmB,CAACC,IAAI,GAAAvB,MAAA;IAAEwB,KAAK,EAAC,MAAM;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAErC,QAAA,CAAAsC,cAAc;IACpGC,OAAO,EAAE7B,KAAA,CAAA8B;;IAtDhB1B,OAAA,EAAAC,QAAA,CAuDM,MAEM,CAFNpB,mBAAA,CAEM,OAFN8C,UAEM,GADJ9C,mBAAA,CAA6D,SAA7D+C,UAA6D,EAAnC,OAAK,GAAAjB,gBAAA,CAAGf,KAAA,CAAAiC,WAAW,CAACjB,IAAI,iB,GAGpD/B,mBAAA,CAYM,OAZNiD,UAYM,G,4BAXJjD,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCI,mBAAA,CASM,OATNkD,UASM,GARJlD,mBAAA,CAGQ,SAHRmD,UAGQ,G,gBAFNnD,mBAAA,CAAmF;MAA5EkC,IAAI,EAAC,OAAO;MA/D/B,uBAAA/B,MAAA,QAAAA,MAAA,MAAAc,MAAA,IA+DyCF,KAAA,CAAAwB,mBAAmB,CAACa,MAAM,GAAAnC,MAAA;MAAEoC,KAAK,EAAC,MAAM;MAACzD,KAAK,EAAC;oDAA/CmB,KAAA,CAAAwB,mBAAmB,CAACa,MAAM,E,+BACvDpD,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHRsD,UAGQ,G,gBAFNtD,mBAAA,CAAqF;MAA9EkC,IAAI,EAAC,OAAO;MAnE/B,uBAAA/B,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAmEyCF,KAAA,CAAAwB,mBAAmB,CAACa,MAAM,GAAAnC,MAAA;MAAEoC,KAAK,EAAC,QAAQ;MAACzD,KAAK,EAAC;oDAAjDmB,KAAA,CAAAwB,mBAAmB,CAACa,MAAM,E,+BACvDpD,mBAAA,CAAiB,cAAX,MAAI,qB,OAKLe,KAAA,CAAAwB,mBAAmB,CAACa,MAAM,e,cAArCtD,mBAAA,CAqBM,OA9FZyD,WAAA,GA0EQvD,mBAAA,CAOM,OAPNwD,WAOM,G,4BANJxD,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCI,mBAAA,CAIS;MAhFnB,uBAAAG,MAAA,QAAAA,MAAA,MAAAc,MAAA,IA4E2BF,KAAA,CAAAwB,mBAAmB,CAACkB,QAAQ,GAAAxC,MAAA;MAAErB,KAAK,EAAC;2BACnDE,mBAAA,CAESyB,SAAA,QA/ErBC,WAAA,CA6EqCC,IAAA,CAAAiC,QAAQ,EAAlBC,MAAM;2BAArB7D,mBAAA,CAES;QAF2BD,GAAG,EAAE8D,MAAM,CAAC/B,EAAE;QAAGyB,KAAK,EAAEM,MAAM,CAAC/B;0BAC9D+B,MAAM,CAAC5B,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAG6B,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA9B,gBAAA,CAAG6B,MAAM,CAACE,UAAU,IAAG,KAC9E,uBA/EZC,WAAA;6EA4E2B/C,KAAA,CAAAwB,mBAAmB,CAACkB,QAAQ,E,KAO/CzD,mBAAA,CAUM,OAVN+D,WAUM,G,4BATJ/D,mBAAA,CAAuC;MAAhCJ,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BI,mBAAA,CAMM,OANNgE,WAMM,G,gBALJhE,mBAAA,CACW;MADJkC,IAAI,EAAC,MAAM;MAtF9B,uBAAA/B,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAsFwCF,KAAA,CAAAwB,mBAAmB,CAAC0B,iBAAiB,GAAAhD,MAAA;MAAErB,KAAK,EAAC,6BAA6B;MACpGsE,QAAQ,EAAR;mDAD0BnD,KAAA,CAAAwB,mBAAmB,CAAC0B,iBAAiB,E,GAEjEjE,mBAAA,CAES;MAFDJ,KAAK,EAAC,sDAAsD;MAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAA8D,gBAAA,IAAA9D,QAAA,CAAA8D,gBAAA,IAAA/D,IAAA,CAAgB;QAC3FG,YAAA,CAAiDC,4BAAA;MAA7BC,IAAI,EAAE;IAAmB,G,KAGjDF,YAAA,CAA2E6D,gCAAA;MAAnDC,QAAQ,EAAEtD,KAAA,CAAAwB,mBAAmB,CAAC0B;gEAI1DnE,mBAAA,CAeM,OA/GZwE,WAAA,GAiGQtE,mBAAA,CAIM,OAJNuE,WAIM,G,4BAHJvE,mBAAA,CAAqC;MAA9BJ,KAAK,EAAC;IAAY,GAAC,KAAG,sB,gBAC7BI,mBAAA,CAAsF;MAA/EkC,IAAI,EAAC,UAAU;MAnGhC,uBAAA/B,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAmG0CF,KAAA,CAAAwB,mBAAmB,CAACiC,WAAW,GAAAvD,MAAA;MAAErB,KAAK,EAAC;mDAAvCmB,KAAA,CAAAwB,mBAAmB,CAACiC,WAAW,E,GAC/DjE,YAAA,CAAqE6D,gCAAA;MAA7CC,QAAQ,EAAEtD,KAAA,CAAAwB,mBAAmB,CAACiC;6CAGxDxE,mBAAA,CAOM,OAPNyE,WAOM,G,4BANJzE,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BI,mBAAA,CACkD;MAD3CkC,IAAI,EAAC,UAAU;MAzGhC,uBAAA/B,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAyG0CF,KAAA,CAAAwB,mBAAmB,CAACmC,eAAe,GAAAzD,MAAA;MAAErB,KAAK,EAzGpF+E,eAAA,EAyGqF,cAAc;QAAA,kBAC3DtE,QAAA,CAAAuE;MAAgB;4CADd7D,KAAA,CAAAwB,mBAAmB,CAACmC,eAAe,E,GAExDrE,QAAA,CAAAuE,gBAAgB,I,cAA3B9E,mBAAA,CAEM,OAFN+E,WAEM,EAFyD,cAE/D,KA7GV9E,mBAAA,e,MAiHMC,mBAAA,CAWM,OAXN8E,WAWM,G,4BAVJ9E,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BW,YAAA,CAEiBM,yBAAA;MArHzBC,UAAA,EAmHiCC,KAAA,CAAAwB,mBAAmB,CAACwC,kBAAkB;MAnHvE,uBAAA5E,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAmHiCF,KAAA,CAAAwB,mBAAmB,CAACwC,kBAAkB,GAAA9D,MAAA;;MAnHvEE,OAAA,EAAAC,QAAA,CAmHyE,MAEjEjB,MAAA,SAAAA,MAAA,QArHRkB,gBAAA,CAmHyE,QAEjE,E;MArHRC,CAAA;uCAsHQf,YAAA,CAEiBM,yBAAA;MAxHzBC,UAAA,EAsHiCC,KAAA,CAAAwB,mBAAmB,CAACyC,WAAW;MAtHhE,uBAAA7E,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAsHiCF,KAAA,CAAAwB,mBAAmB,CAACyC,WAAW,GAAA/D,MAAA;;MAtHhEE,OAAA,EAAAC,QAAA,CAsHkE,MAE1DjB,MAAA,SAAAA,MAAA,QAxHRkB,gBAAA,CAsHkE,YAE1D,E;MAxHRC,CAAA;uCAyHQf,YAAA,CAEiBM,yBAAA;MA3HzBC,UAAA,EAyHiCC,KAAA,CAAAwB,mBAAmB,CAAC0C,QAAQ;MAzH7D,uBAAA9E,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAyHiCF,KAAA,CAAAwB,mBAAmB,CAAC0C,QAAQ,GAAAhE,MAAA;;MAzH7DE,OAAA,EAAAC,QAAA,CAyH+D,MAEvDjB,MAAA,SAAAA,MAAA,QA3HRkB,gBAAA,CAyH+D,UAEvD,E;MA3HRC,CAAA;;IAAAA,CAAA;6DA+HIvB,mBAAA,cAAiB,EACjBQ,YAAA,CAiEY+B,oBAAA;IAjMhBxB,UAAA,EAgIwBC,KAAA,CAAAmE,gBAAgB,CAAC1C,IAAI;IAhI7C,uBAAArC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAgIwBF,KAAA,CAAAmE,gBAAgB,CAAC1C,IAAI,GAAAvB,MAAA;IAAEwB,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAAC0C,IAAI,EAAC,IAAI;IACpFzC,SAAO,EAAErC,QAAA,CAAA+E,oBAAoB;IAAGxC,OAAO,EAAE7B,KAAA,CAAA8B;;IAjIhD1B,OAAA,EAAAC,QAAA,CAkIM,MAaM,CAbNpB,mBAAA,CAaM,OAbNqF,WAaM,G,4BAZJrF,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCI,mBAAA,CAIM,OAJNsF,WAIM,GAHJ/E,YAAA,CAEiBM,yBAAA;MAvI3BC,UAAA,EAqImCC,KAAA,CAAAwE,cAAc;MArIjD,wB,sCAqImCxE,KAAA,CAAAwE,cAAc,GAAAtE,MAAA,GAAsBZ,QAAA,CAAAmF,oBAAoB;;MArI3FrE,OAAA,EAAAC,QAAA,CAqI6F,MAEnFjB,MAAA,SAAAA,MAAA,QAvIVkB,gBAAA,CAqI6F,MAEnF,E;MAvIVC,CAAA;gEAyIQtB,mBAAA,CAIM,OAJNyF,WAIM,I,kBAHJ3F,mBAAA,CAEiByB,SAAA,QA5I3BC,WAAA,CA0IyCC,IAAA,CAAAC,KAAK,EAAbC,IAAI;2BAA3B+D,YAAA,CAEiB7E,yBAAA;QAFsBhB,GAAG,EAAE8B,IAAI,CAACC,EAAE;QA1I7Dd,UAAA,EA0IwEC,KAAA,CAAAmE,gBAAgB,CAACS,aAAa,CAAChE,IAAI,CAACC,EAAE;QA1I9G,uBAAAX,MAAA,IA0IwEF,KAAA,CAAAmE,gBAAgB,CAACS,aAAa,CAAChE,IAAI,CAACC,EAAE,IAAAX;;QA1I9GE,OAAA,EAAAC,QAAA,CA2IY,MAAe,CA3I3BC,gBAAA,CAAAS,gBAAA,CA2IeH,IAAI,CAACI,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGH,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QA5IVV,CAAA;;sCA8IQtB,mBAAA,CAAyD,KAAzD4F,WAAyD,EAApC,MAAI,GAAA9D,gBAAA,CAAGzB,QAAA,CAAAwF,kBAAkB,IAAG,MAAI,gB,GAGvD7F,mBAAA,CAOM,OAPN8F,WAOM,G,4BANJ9F,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BI,mBAAA,CAIS;MAvJjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAmJyBF,KAAA,CAAAmE,gBAAgB,CAACzB,QAAQ,GAAAxC,MAAA;MAAErB,KAAK,EAAC;2BAChDE,mBAAA,CAESyB,SAAA,QAtJnBC,WAAA,CAoJmCC,IAAA,CAAAiC,QAAQ,EAAlBC,MAAM;2BAArB7D,mBAAA,CAES;QAF2BD,GAAG,EAAE8D,MAAM,CAAC/B,EAAE;QAAGyB,KAAK,EAAEM,MAAM,CAAC/B;0BAC9D+B,MAAM,CAAC5B,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAG6B,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA9B,gBAAA,CAAG6B,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAtJVkC,WAAA;6EAmJyBhF,KAAA,CAAAmE,gBAAgB,CAACzB,QAAQ,E,KAO5CzD,mBAAA,CAyBM,OAzBNgG,WAyBM,G,4BAxBJhG,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BI,mBAAA,CASM,OATNiG,WASM,GARJjG,mBAAA,CAGQ,SAHRkG,WAGQ,G,gBAFNlG,mBAAA,CAA4F;MAArFkC,IAAI,EAAC,OAAO;MA9J/B,uBAAA/B,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8JyCF,KAAA,CAAAmE,gBAAgB,CAACiB,aAAa,GAAAlF,MAAA;MAAEoC,KAAK,EAAC,WAAW;MAACzD,KAAK,EAAC;oDAAxDmB,KAAA,CAAAmE,gBAAgB,CAACiB,aAAa,E,+BAC3DnG,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHRoG,WAGQ,G,gBAFNpG,mBAAA,CAA4F;MAArFkC,IAAI,EAAC,OAAO;MAlK/B,uBAAA/B,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAkKyCF,KAAA,CAAAmE,gBAAgB,CAACiB,aAAa,GAAAlF,MAAA;MAAEoC,KAAK,EAAC,WAAW;MAACzD,KAAK,EAAC;oDAAxDmB,KAAA,CAAAmE,gBAAgB,CAACiB,aAAa,E,+BAC3DnG,mBAAA,CAAiB,cAAX,MAAI,qB,KAIHe,KAAA,CAAAmE,gBAAgB,CAACiB,aAAa,oB,cAAzCrG,mBAAA,CAWM,OAXNuG,WAWM,GAVJrG,mBAAA,CASM,OATNsG,WASM,GARJtG,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAoC;MAA7BJ,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BI,mBAAA,CAAiF;MAA1EkC,IAAI,EAAC,MAAM;MA3KhC,uBAAA/B,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA2K0CF,KAAA,CAAAmE,gBAAgB,CAACqB,aAAa,GAAAtF,MAAA;MAAErB,KAAK,EAAC;mDAAtCmB,KAAA,CAAAmE,gBAAgB,CAACqB,aAAa,E,KAE5DvG,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAoC;MAA7BJ,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BI,mBAAA,CAAiF;MAA1EkC,IAAI,EAAC,MAAM;MA/KhC,uBAAA/B,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA+K0CF,KAAA,CAAAmE,gBAAgB,CAACsB,aAAa,GAAAvF,MAAA;MAAErB,KAAK,EAAC;mDAAtCmB,KAAA,CAAAmE,gBAAgB,CAACsB,aAAa,E,WA/KxEzG,mBAAA,e,GAqLMC,mBAAA,CAWM,OAXNyG,WAWM,G,4BAVJzG,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BW,YAAA,CAEiBM,yBAAA;MAzLzBC,UAAA,EAuLiCC,KAAA,CAAAmE,gBAAgB,CAACwB,YAAY;MAvL9D,uBAAAvG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAuLiCF,KAAA,CAAAmE,gBAAgB,CAACwB,YAAY,GAAAzF,MAAA;;MAvL9DE,OAAA,EAAAC,QAAA,CAuLgE,MAExDjB,MAAA,SAAAA,MAAA,QAzLRkB,gBAAA,CAuLgE,YAExD,E;MAzLRC,CAAA;uCA0LQf,YAAA,CAEiBM,yBAAA;MA5LzBC,UAAA,EA0LiCC,KAAA,CAAAmE,gBAAgB,CAACyB,WAAW;MA1L7D,uBAAAxG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA0LiCF,KAAA,CAAAmE,gBAAgB,CAACyB,WAAW,GAAA1F,MAAA;;MA1L7DE,OAAA,EAAAC,QAAA,CA0L+D,MAEvDjB,MAAA,SAAAA,MAAA,QA5LRkB,gBAAA,CA0L+D,UAEvD,E;MA5LRC,CAAA;uCA6LQf,YAAA,CAEiBM,yBAAA;MA/LzBC,UAAA,EA6LiCC,KAAA,CAAAmE,gBAAgB,CAAC0B,gBAAgB;MA7LlE,uBAAAzG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA6LiCF,KAAA,CAAAmE,gBAAgB,CAAC0B,gBAAgB,GAAA3F,MAAA;;MA7LlEE,OAAA,EAAAC,QAAA,CA6LoE,MAE5DjB,MAAA,SAAAA,MAAA,QA/LRkB,gBAAA,CA6LoE,aAE5D,E;MA/LRC,CAAA;;IAAAA,CAAA;6DAmMIvB,mBAAA,cAAiB,EACjBQ,YAAA,CA8BY+B,oBAAA;IAlOhBxB,UAAA,EAoMwBC,KAAA,CAAA8F,eAAe,CAACrE,IAAI;IApM5C,uBAAArC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAoMwBF,KAAA,CAAA8F,eAAe,CAACrE,IAAI,GAAAvB,MAAA;IAAEwB,KAAK,EAAC,UAAU;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAErC,QAAA,CAAAyG,gBAAgB;IACtGlE,OAAO,EAAE7B,KAAA,CAAA8B;;IArMhB1B,OAAA,EAAAC,QAAA,CAsMM,MAQM,CARNpB,mBAAA,CAQM,OARN+G,WAQM,G,4BAPJ/G,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCI,mBAAA,CAKM,OALNgH,WAKM,I,kBAJJlH,mBAAA,CAGiByB,SAAA,QA5M3BC,WAAA,CAyMyCnB,QAAA,CAAA4G,iBAAiB,EAAzBtF,IAAI;2BAA3B+D,YAAA,CAGiB7E,yBAAA;QAHkChB,GAAG,EAAE8B,IAAI,CAACC,EAAE;QAzMzEd,UAAA,EA0MqBC,KAAA,CAAA8F,eAAe,CAAClB,aAAa,CAAChE,IAAI,CAACC,EAAE;QA1M1D,uBAAAX,MAAA,IA0MqBF,KAAA,CAAA8F,eAAe,CAAClB,aAAa,CAAChE,IAAI,CAACC,EAAE,IAAAX;;QA1M1DE,OAAA,EAAAC,QAAA,CA2MY,MAAe,CA3M3BC,gBAAA,CAAAS,gBAAA,CA2MeH,IAAI,CAACI,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGH,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QA5MVV,CAAA;;wCAgNMtB,mBAAA,CAOM,OAPNkH,WAOM,G,4BANJlH,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCI,mBAAA,CAIS;MAtNjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAkNyBF,KAAA,CAAA8F,eAAe,CAACpD,QAAQ,GAAAxC,MAAA;MAAErB,KAAK,EAAC;2BAC/CE,mBAAA,CAESyB,SAAA,QArNnBC,WAAA,CAmNmCC,IAAA,CAAAiC,QAAQ,EAAlBC,MAAM;2BAArB7D,mBAAA,CAES;QAF2BD,GAAG,EAAE8D,MAAM,CAAC/B,EAAE;QAAGyB,KAAK,EAAEM,MAAM,CAAC/B;0BAC9D+B,MAAM,CAAC5B,IAAI,wBApN1BoF,WAAA;6EAkNyBpG,KAAA,CAAA8F,eAAe,CAACpD,QAAQ,E,KAO3CzD,mBAAA,CAQM,OARNoH,WAQM,G,4BAPJpH,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BW,YAAA,CAEiBM,yBAAA;MA7NzBC,UAAA,EA2NiCC,KAAA,CAAA8F,eAAe,CAACQ,iBAAiB;MA3NlE,uBAAAlH,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA2NiCF,KAAA,CAAA8F,eAAe,CAACQ,iBAAiB,GAAApG,MAAA;;MA3NlEE,OAAA,EAAAC,QAAA,CA2NoE,MAE5DjB,MAAA,SAAAA,MAAA,QA7NRkB,gBAAA,CA2NoE,eAE5D,E;MA7NRC,CAAA;uCA8NQf,YAAA,CAEiBM,yBAAA;MAhOzBC,UAAA,EA8NiCC,KAAA,CAAA8F,eAAe,CAACS,iBAAiB;MA9NlE,uBAAAnH,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8NiCF,KAAA,CAAA8F,eAAe,CAACS,iBAAiB,GAAArG,MAAA;;MA9NlEE,OAAA,EAAAC,QAAA,CA8NoE,MAE5DjB,MAAA,SAAAA,MAAA,QAhORkB,gBAAA,CA8NoE,aAE5D,E;MAhORC,CAAA;;IAAAA,CAAA;6DAoOIvB,mBAAA,cAAiB,EACjBQ,YAAA,CAyCY+B,oBAAA;IA9QhBxB,UAAA,EAqOwBC,KAAA,CAAAwG,mBAAmB,CAAC/E,IAAI;IArOhD,uBAAArC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAqOwBF,KAAA,CAAAwG,mBAAmB,CAAC/E,IAAI,GAAAvB,MAAA;IAAEwB,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAAChC,IAAI,EAAC,sBAAsB;IAAC+G,MAAM,EAAN,EAAM;IAChH9E,SAAO,EAAErC,QAAA,CAAAoH,cAAc;IAAG7E,OAAO,EAAE7B,KAAA,CAAA8B;;IAtO1C1B,OAAA,EAAAC,QAAA,CAuOM,MAEM,C,4BAFNpB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAA4C,IACrDI,mBAAA,CAA+C,WAA5C,0CAAwC,E,sBAG7CA,mBAAA,CAQM,OARN0H,WAQM,G,4BAPJ1H,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCI,mBAAA,CAKM,OALN2H,WAKM,I,kBAJJ7H,mBAAA,CAGiByB,SAAA,QAjP3BC,WAAA,CA8OyCnB,QAAA,CAAA4G,iBAAiB,EAAzBtF,IAAI;2BAA3B+D,YAAA,CAGiB7E,yBAAA;QAHkChB,GAAG,EAAE8B,IAAI,CAACC,EAAE;QA9OzEd,UAAA,EA+OqBC,KAAA,CAAAwG,mBAAmB,CAAC5B,aAAa,CAAChE,IAAI,CAACC,EAAE;QA/O9D,uBAAAX,MAAA,IA+OqBF,KAAA,CAAAwG,mBAAmB,CAAC5B,aAAa,CAAChE,IAAI,CAACC,EAAE,IAAAX;;QA/O9DE,OAAA,EAAAC,QAAA,CAgPY,MAAe,CAhP3BC,gBAAA,CAAAS,gBAAA,CAgPeH,IAAI,CAACI,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGH,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QAjPVV,CAAA;;wCAqPMtB,mBAAA,CAOM,OAPN4H,WAOM,G,4BANJ5H,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCI,mBAAA,CAIS;MA3PjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAuPyBF,KAAA,CAAAwG,mBAAmB,CAAC9D,QAAQ,GAAAxC,MAAA;MAAErB,KAAK,EAAC;2BACnDE,mBAAA,CAESyB,SAAA,QA1PnBC,WAAA,CAwPmCnB,QAAA,CAAAwH,iBAAiB,EAA3BlE,MAAM;2BAArB7D,mBAAA,CAES;QAFoCD,GAAG,EAAE8D,MAAM,CAAC/B,EAAE;QAAGyB,KAAK,EAAEM,MAAM,CAAC/B;0BACvE+B,MAAM,CAAC5B,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAG6B,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA9B,gBAAA,CAAG6B,MAAM,CAACE,UAAU,IAAG,KAC9E,uBA1PViE,WAAA;6EAuPyB/G,KAAA,CAAAwG,mBAAmB,CAAC9D,QAAQ,E,KAO/CzD,mBAAA,CASM,OATN+H,WASM,G,4BARJ/H,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BI,mBAAA,CAMS;MAtQjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAgQyBF,KAAA,CAAAwG,mBAAmB,CAACS,MAAM,GAAA/G,MAAA;MAAErB,KAAK,EAAC;oCACjDI,mBAAA,CAAiD;MAAzCqD,KAAK,EAAC;IAAmB,GAAC,QAAM,qBACxCrD,mBAAA,CAA2C;MAAnCqD,KAAK,EAAC;IAAe,GAAC,MAAI,qBAClCrD,mBAAA,CAA6C;MAArCqD,KAAK,EAAC;IAAiB,GAAC,MAAI,qBACpCrD,mBAAA,CAAwC;MAAhCqD,KAAK,EAAC;IAAY,GAAC,MAAI,qBAC/BrD,mBAAA,CAAmC;MAA3BqD,KAAK,EAAC;IAAO,GAAC,MAAI,oB,2CALXtC,KAAA,CAAAwG,mBAAmB,CAACS,MAAM,E,KAS7ChI,mBAAA,CAIM,OAJNiI,WAIM,G,4BAHJjI,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BI,mBAAA,CACuC;MA5Q/C,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA2Q2BF,KAAA,CAAAwG,mBAAmB,CAACW,WAAW,GAAAjH,MAAA;MAAErB,KAAK,EAAC,cAAc;MAACuI,IAAI,EAAC,GAAG;MAC/EC,WAAW,EAAC;mDADKrH,KAAA,CAAAwG,mBAAmB,CAACW,WAAW,E;IA3Q1D5G,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}