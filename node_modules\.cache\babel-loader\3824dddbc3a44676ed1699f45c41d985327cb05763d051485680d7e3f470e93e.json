{"ast": null, "code": "import { HOOK_PLUGIN_SETTINGS_SET } from './const.js';\nimport { now } from './time.js';\nexport class ApiProxy {\n  constructor(plugin, hook) {\n    this.target = null;\n    this.targetQueue = [];\n    this.onQueue = [];\n    this.plugin = plugin;\n    this.hook = hook;\n    const defaultSettings = {};\n    if (plugin.settings) {\n      for (const id in plugin.settings) {\n        const item = plugin.settings[id];\n        defaultSettings[id] = item.defaultValue;\n      }\n    }\n    const localSettingsSaveId = `__vue-devtools-plugin-settings__${plugin.id}`;\n    let currentSettings = Object.assign({}, defaultSettings);\n    try {\n      const raw = localStorage.getItem(localSettingsSaveId);\n      const data = JSON.parse(raw);\n      Object.assign(currentSettings, data);\n    } catch (e) {\n      // noop\n    }\n    this.fallbacks = {\n      getSettings() {\n        return currentSettings;\n      },\n      setSettings(value) {\n        try {\n          localStorage.setItem(localSettingsSaveId, JSON.stringify(value));\n        } catch (e) {\n          // noop\n        }\n        currentSettings = value;\n      },\n      now() {\n        return now();\n      }\n    };\n    if (hook) {\n      hook.on(HOOK_PLUGIN_SETTINGS_SET, (pluginId, value) => {\n        if (pluginId === this.plugin.id) {\n          this.fallbacks.setSettings(value);\n        }\n      });\n    }\n    this.proxiedOn = new Proxy({}, {\n      get: (_target, prop) => {\n        if (this.target) {\n          return this.target.on[prop];\n        } else {\n          return (...args) => {\n            this.onQueue.push({\n              method: prop,\n              args\n            });\n          };\n        }\n      }\n    });\n    this.proxiedTarget = new Proxy({}, {\n      get: (_target, prop) => {\n        if (this.target) {\n          return this.target[prop];\n        } else if (prop === 'on') {\n          return this.proxiedOn;\n        } else if (Object.keys(this.fallbacks).includes(prop)) {\n          return (...args) => {\n            this.targetQueue.push({\n              method: prop,\n              args,\n              resolve: () => {}\n            });\n            return this.fallbacks[prop](...args);\n          };\n        } else {\n          return (...args) => {\n            return new Promise(resolve => {\n              this.targetQueue.push({\n                method: prop,\n                args,\n                resolve\n              });\n            });\n          };\n        }\n      }\n    });\n  }\n  async setRealTarget(target) {\n    this.target = target;\n    for (const item of this.onQueue) {\n      this.target.on[item.method](...item.args);\n    }\n    for (const item of this.targetQueue) {\n      item.resolve(await this.target[item.method](...item.args));\n    }\n  }\n}", "map": {"version": 3, "names": ["HOOK_PLUGIN_SETTINGS_SET", "now", "ApiProxy", "constructor", "plugin", "hook", "target", "targetQueue", "onQueue", "defaultSettings", "settings", "id", "item", "defaultValue", "localSettingsSaveId", "currentSettings", "Object", "assign", "raw", "localStorage", "getItem", "data", "JSON", "parse", "e", "fallbacks", "getSettings", "setSettings", "value", "setItem", "stringify", "on", "pluginId", "proxiedOn", "Proxy", "get", "_target", "prop", "args", "push", "method", "proxied<PERSON><PERSON><PERSON>", "keys", "includes", "resolve", "Promise", "setRealTarget"], "sources": ["D:/demo/ooo/pass/node_modules/@vue/devtools-api/lib/esm/proxy.js"], "sourcesContent": ["import { HOOK_PLUGIN_SETTINGS_SET } from './const.js';\nimport { now } from './time.js';\nexport class ApiProxy {\n    constructor(plugin, hook) {\n        this.target = null;\n        this.targetQueue = [];\n        this.onQueue = [];\n        this.plugin = plugin;\n        this.hook = hook;\n        const defaultSettings = {};\n        if (plugin.settings) {\n            for (const id in plugin.settings) {\n                const item = plugin.settings[id];\n                defaultSettings[id] = item.defaultValue;\n            }\n        }\n        const localSettingsSaveId = `__vue-devtools-plugin-settings__${plugin.id}`;\n        let currentSettings = Object.assign({}, defaultSettings);\n        try {\n            const raw = localStorage.getItem(localSettingsSaveId);\n            const data = JSON.parse(raw);\n            Object.assign(currentSettings, data);\n        }\n        catch (e) {\n            // noop\n        }\n        this.fallbacks = {\n            getSettings() {\n                return currentSettings;\n            },\n            setSettings(value) {\n                try {\n                    localStorage.setItem(localSettingsSaveId, JSON.stringify(value));\n                }\n                catch (e) {\n                    // noop\n                }\n                currentSettings = value;\n            },\n            now() {\n                return now();\n            },\n        };\n        if (hook) {\n            hook.on(HOOK_PLUGIN_SETTINGS_SET, (pluginId, value) => {\n                if (pluginId === this.plugin.id) {\n                    this.fallbacks.setSettings(value);\n                }\n            });\n        }\n        this.proxiedOn = new Proxy({}, {\n            get: (_target, prop) => {\n                if (this.target) {\n                    return this.target.on[prop];\n                }\n                else {\n                    return (...args) => {\n                        this.onQueue.push({\n                            method: prop,\n                            args,\n                        });\n                    };\n                }\n            },\n        });\n        this.proxiedTarget = new Proxy({}, {\n            get: (_target, prop) => {\n                if (this.target) {\n                    return this.target[prop];\n                }\n                else if (prop === 'on') {\n                    return this.proxiedOn;\n                }\n                else if (Object.keys(this.fallbacks).includes(prop)) {\n                    return (...args) => {\n                        this.targetQueue.push({\n                            method: prop,\n                            args,\n                            resolve: () => { },\n                        });\n                        return this.fallbacks[prop](...args);\n                    };\n                }\n                else {\n                    return (...args) => {\n                        return new Promise((resolve) => {\n                            this.targetQueue.push({\n                                method: prop,\n                                args,\n                                resolve,\n                            });\n                        });\n                    };\n                }\n            },\n        });\n    }\n    async setRealTarget(target) {\n        this.target = target;\n        for (const item of this.onQueue) {\n            this.target.on[item.method](...item.args);\n        }\n        for (const item of this.targetQueue) {\n            item.resolve(await this.target[item.method](...item.args));\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,wBAAwB,QAAQ,YAAY;AACrD,SAASC,GAAG,QAAQ,WAAW;AAC/B,OAAO,MAAMC,QAAQ,CAAC;EAClBC,WAAWA,CAACC,MAAM,EAAEC,IAAI,EAAE;IACtB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACJ,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,MAAMI,eAAe,GAAG,CAAC,CAAC;IAC1B,IAAIL,MAAM,CAACM,QAAQ,EAAE;MACjB,KAAK,MAAMC,EAAE,IAAIP,MAAM,CAACM,QAAQ,EAAE;QAC9B,MAAME,IAAI,GAAGR,MAAM,CAACM,QAAQ,CAACC,EAAE,CAAC;QAChCF,eAAe,CAACE,EAAE,CAAC,GAAGC,IAAI,CAACC,YAAY;MAC3C;IACJ;IACA,MAAMC,mBAAmB,GAAG,mCAAmCV,MAAM,CAACO,EAAE,EAAE;IAC1E,IAAII,eAAe,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,eAAe,CAAC;IACxD,IAAI;MACA,MAAMS,GAAG,GAAGC,YAAY,CAACC,OAAO,CAACN,mBAAmB,CAAC;MACrD,MAAMO,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,GAAG,CAAC;MAC5BF,MAAM,CAACC,MAAM,CAACF,eAAe,EAAEM,IAAI,CAAC;IACxC,CAAC,CACD,OAAOG,CAAC,EAAE;MACN;IAAA;IAEJ,IAAI,CAACC,SAAS,GAAG;MACbC,WAAWA,CAAA,EAAG;QACV,OAAOX,eAAe;MAC1B,CAAC;MACDY,WAAWA,CAACC,KAAK,EAAE;QACf,IAAI;UACAT,YAAY,CAACU,OAAO,CAACf,mBAAmB,EAAEQ,IAAI,CAACQ,SAAS,CAACF,KAAK,CAAC,CAAC;QACpE,CAAC,CACD,OAAOJ,CAAC,EAAE;UACN;QAAA;QAEJT,eAAe,GAAGa,KAAK;MAC3B,CAAC;MACD3B,GAAGA,CAAA,EAAG;QACF,OAAOA,GAAG,CAAC,CAAC;MAChB;IACJ,CAAC;IACD,IAAII,IAAI,EAAE;MACNA,IAAI,CAAC0B,EAAE,CAAC/B,wBAAwB,EAAE,CAACgC,QAAQ,EAAEJ,KAAK,KAAK;QACnD,IAAII,QAAQ,KAAK,IAAI,CAAC5B,MAAM,CAACO,EAAE,EAAE;UAC7B,IAAI,CAACc,SAAS,CAACE,WAAW,CAACC,KAAK,CAAC;QACrC;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACK,SAAS,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,EAAE;MAC3BC,GAAG,EAAEA,CAACC,OAAO,EAAEC,IAAI,KAAK;QACpB,IAAI,IAAI,CAAC/B,MAAM,EAAE;UACb,OAAO,IAAI,CAACA,MAAM,CAACyB,EAAE,CAACM,IAAI,CAAC;QAC/B,CAAC,MACI;UACD,OAAO,CAAC,GAAGC,IAAI,KAAK;YAChB,IAAI,CAAC9B,OAAO,CAAC+B,IAAI,CAAC;cACdC,MAAM,EAAEH,IAAI;cACZC;YACJ,CAAC,CAAC;UACN,CAAC;QACL;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACG,aAAa,GAAG,IAAIP,KAAK,CAAC,CAAC,CAAC,EAAE;MAC/BC,GAAG,EAAEA,CAACC,OAAO,EAAEC,IAAI,KAAK;QACpB,IAAI,IAAI,CAAC/B,MAAM,EAAE;UACb,OAAO,IAAI,CAACA,MAAM,CAAC+B,IAAI,CAAC;QAC5B,CAAC,MACI,IAAIA,IAAI,KAAK,IAAI,EAAE;UACpB,OAAO,IAAI,CAACJ,SAAS;QACzB,CAAC,MACI,IAAIjB,MAAM,CAAC0B,IAAI,CAAC,IAAI,CAACjB,SAAS,CAAC,CAACkB,QAAQ,CAACN,IAAI,CAAC,EAAE;UACjD,OAAO,CAAC,GAAGC,IAAI,KAAK;YAChB,IAAI,CAAC/B,WAAW,CAACgC,IAAI,CAAC;cAClBC,MAAM,EAAEH,IAAI;cACZC,IAAI;cACJM,OAAO,EAAEA,CAAA,KAAM,CAAE;YACrB,CAAC,CAAC;YACF,OAAO,IAAI,CAACnB,SAAS,CAACY,IAAI,CAAC,CAAC,GAAGC,IAAI,CAAC;UACxC,CAAC;QACL,CAAC,MACI;UACD,OAAO,CAAC,GAAGA,IAAI,KAAK;YAChB,OAAO,IAAIO,OAAO,CAAED,OAAO,IAAK;cAC5B,IAAI,CAACrC,WAAW,CAACgC,IAAI,CAAC;gBAClBC,MAAM,EAAEH,IAAI;gBACZC,IAAI;gBACJM;cACJ,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC;QACL;MACJ;IACJ,CAAC,CAAC;EACN;EACA,MAAME,aAAaA,CAACxC,MAAM,EAAE;IACxB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,KAAK,MAAMM,IAAI,IAAI,IAAI,CAACJ,OAAO,EAAE;MAC7B,IAAI,CAACF,MAAM,CAACyB,EAAE,CAACnB,IAAI,CAAC4B,MAAM,CAAC,CAAC,GAAG5B,IAAI,CAAC0B,IAAI,CAAC;IAC7C;IACA,KAAK,MAAM1B,IAAI,IAAI,IAAI,CAACL,WAAW,EAAE;MACjCK,IAAI,CAACgC,OAAO,CAAC,MAAM,IAAI,CAACtC,MAAM,CAACM,IAAI,CAAC4B,MAAM,CAAC,CAAC,GAAG5B,IAAI,CAAC0B,IAAI,CAAC,CAAC;IAC9D;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}