{"ast": null, "code": "export default {\n  name: 'CustomCheckbox',\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['update:modelValue']\n};", "map": {"version": 3, "names": ["name", "props", "modelValue", "type", "Boolean", "default", "emits"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\CustomCheckbox.vue"], "sourcesContent": ["<template>\r\n  <label class=\"checkbox-wrapper\">\r\n    <input\r\n      type=\"checkbox\"\r\n      :checked=\"modelValue\"\r\n      @change=\"$emit('update:modelValue', $event.target.checked)\"\r\n    />\r\n    <span class=\"checkbox-mark\"></span>\r\n    <span class=\"checkbox-label\">\r\n      <slot></slot>\r\n    </span>\r\n  </label>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CustomCheckbox',\r\n  props: {\r\n    modelValue: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  emits: ['update:modelValue']\r\n}\r\n</script> "], "mappings": "AAeA,eAAe;EACbA,IAAI,EAAE,gBAAgB;EACtBC,KAAK,EAAE;IACLC,UAAU,EAAE;MACVC,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,KAAK,EAAE,CAAC,mBAAmB;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}