{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, vModelText as _vModelText, withDirectives as _withDirectives, vModelSelect as _vModelSelect, toDisplayString as _toDisplayString, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, vModelDynamic as _vModelDynamic, vModelCheckbox as _vModelCheckbox, createBlock as _createBlock, vModelRadio as _vModelRadio } from \"vue\";\nconst _hoisted_1 = {\n  class: \"bg-white shadow rounded-lg p-4 mb-6\"\n};\nconst _hoisted_2 = {\n  class: \"flex flex-wrap items-center justify-between\"\n};\nconst _hoisted_3 = {\n  class: \"flex space-x-3 mb-2 sm:mb-0\"\n};\nconst _hoisted_4 = {\n  class: \"flex items-center space-x-4\"\n};\nconst _hoisted_5 = {\n  class: \"flex items-center border rounded-md overflow-hidden\"\n};\nconst _hoisted_6 = {\n  class: \"relative\"\n};\nconst _hoisted_7 = {\n  class: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n};\nconst _hoisted_8 = {\n  class: \"relative\"\n};\nconst _hoisted_9 = {\n  class: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n};\nconst _hoisted_10 = {\n  key: 0,\n  class: \"bg-white rounded-lg shadow overflow-hidden\"\n};\nconst _hoisted_11 = {\n  class: \"px-4 py-3 bg-gray-50 border-b flex justify-between items-center\"\n};\nconst _hoisted_12 = {\n  class: \"text-sm text-gray-700\"\n};\nconst _hoisted_13 = {\n  class: \"font-medium\"\n};\nconst _hoisted_14 = {\n  class: \"font-medium\"\n};\nconst _hoisted_15 = {\n  class: \"flex space-x-2\"\n};\nconst _hoisted_16 = {\n  class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n};\nconst _hoisted_17 = {\n  class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n};\nconst _hoisted_18 = {\n  class: \"min-w-full divide-y divide-gray-200\"\n};\nconst _hoisted_19 = {\n  class: \"bg-gray-50\"\n};\nconst _hoisted_20 = {\n  scope: \"col\",\n  class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n};\nconst _hoisted_21 = {\n  class: \"bg-white divide-y divide-gray-200\"\n};\nconst _hoisted_22 = {\n  class: \"bg-gray-100\"\n};\nconst _hoisted_23 = {\n  colspan: \"8\",\n  class: \"px-6 py-2\"\n};\nconst _hoisted_24 = {\n  class: \"flex items-center justify-between\"\n};\nconst _hoisted_25 = {\n  class: \"font-medium text-gray-700\"\n};\nconst _hoisted_26 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_27 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_28 = {\n  class: \"ml-2 font-medium text-gray-900\"\n};\nconst _hoisted_29 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_30 = {\n  class: \"text-sm text-gray-900\"\n};\nconst _hoisted_31 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_32 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_33 = {\n  class: \"text-sm font-medium text-gray-900\"\n};\nconst _hoisted_34 = {\n  key: 0,\n  class: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\"\n};\nconst _hoisted_35 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_36 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_37 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_38 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_39 = {\n  key: 1,\n  class: \"ml-1\"\n};\nconst _hoisted_40 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_41 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_42 = {\n  class: \"flex-grow\"\n};\nconst _hoisted_43 = [\"type\", \"value\"];\nconst _hoisted_44 = [\"onClick\"];\nconst _hoisted_45 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_46 = {\n  class: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\"\n};\nconst _hoisted_47 = {\n  class: \"flex space-x-2\"\n};\nconst _hoisted_48 = [\"onClick\"];\nconst _hoisted_49 = [\"onClick\"];\nconst _hoisted_50 = {\n  key: 0\n};\nconst _hoisted_51 = {\n  colspan: \"8\",\n  class: \"px-6 py-10 text-center\"\n};\nconst _hoisted_52 = {\n  class: \"text-gray-500\"\n};\nconst _hoisted_53 = {\n  class: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\"\n};\nconst _hoisted_54 = {\n  class: \"px-4 py-5 sm:p-6\"\n};\nconst _hoisted_55 = {\n  class: \"flex justify-between items-start mb-4\"\n};\nconst _hoisted_56 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_57 = {\n  class: \"text-lg font-medium text-gray-900\"\n};\nconst _hoisted_58 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_59 = {\n  class: \"space-y-4\"\n};\nconst _hoisted_60 = {\n  class: \"flex justify-between items-center mb-2\"\n};\nconst _hoisted_61 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_62 = {\n  class: \"text-sm font-medium text-gray-900\"\n};\nconst _hoisted_63 = {\n  key: 0,\n  class: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\"\n};\nconst _hoisted_64 = [\"onClick\"];\nconst _hoisted_65 = {\n  class: \"mb-2\"\n};\nconst _hoisted_66 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_67 = [\"type\", \"value\"];\nconst _hoisted_68 = [\"onClick\"];\nconst _hoisted_69 = {\n  class: \"grid grid-cols-2 gap-2 text-xs\"\n};\nconst _hoisted_70 = {\n  class: \"text-gray-900\"\n};\nconst _hoisted_71 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_72 = {\n  class: \"mt-4 flex justify-center\"\n};\nconst _hoisted_73 = [\"onClick\"];\nconst _hoisted_74 = {\n  class: \"mb-4\"\n};\nconst _hoisted_75 = {\n  class: \"px-3 py-2 bg-gray-50 rounded-md\"\n};\nconst _hoisted_76 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_77 = {\n  class: \"flex space-x-3\"\n};\nconst _hoisted_78 = {\n  key: 0,\n  class: \"form-group mb-4\"\n};\nconst _hoisted_79 = [\"value\"];\nconst _hoisted_80 = {\n  class: \"mt-3\"\n};\nconst _hoisted_81 = {\n  class: \"flex justify-between mb-1\"\n};\nconst _hoisted_82 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_83 = [\"type\"];\nconst _hoisted_84 = {\n  key: 1,\n  class: \"space-y-4\"\n};\nconst _hoisted_85 = {\n  class: \"form-group\"\n};\nconst _hoisted_86 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_87 = [\"type\"];\nconst _hoisted_88 = {\n  class: \"form-group\"\n};\nconst _hoisted_89 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_90 = [\"type\"];\nconst _hoisted_91 = {\n  key: 0,\n  class: \"text-sm text-red-500 mt-1\"\n};\nconst _hoisted_92 = {\n  class: \"space-y-2 mt-4\"\n};\nconst _hoisted_93 = {\n  class: \"mb-4\"\n};\nconst _hoisted_94 = {\n  class: \"px-3 py-2 bg-gray-50 rounded-md\"\n};\nconst _hoisted_95 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_96 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_97 = {\n  class: \"relative inline-block w-10 mr-2 align-middle select-none\"\n};\nconst _hoisted_98 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_99 = [\"value\"];\nconst _hoisted_100 = {\n  class: \"form-group\"\n};\nconst _hoisted_101 = {\n  class: \"flex justify-between mb-1\"\n};\nconst _hoisted_102 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_103 = [\"type\"];\nconst _hoisted_104 = {\n  class: \"form-group\"\n};\nconst _hoisted_105 = {\n  class: \"mb-2\"\n};\nconst _hoisted_106 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_107 = {\n  class: \"form-text\"\n};\nconst _hoisted_108 = {\n  class: \"form-group\"\n};\nconst _hoisted_109 = [\"value\"];\nconst _hoisted_110 = {\n  class: \"form-group\"\n};\nconst _hoisted_111 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_112 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_113 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_114 = {\n  key: 0,\n  class: \"mt-3\"\n};\nconst _hoisted_115 = {\n  class: \"grid grid-cols-2 gap-4\"\n};\nconst _hoisted_116 = {\n  class: \"form-group\"\n};\nconst _hoisted_117 = {\n  class: \"form-group\"\n};\nconst _hoisted_118 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_119 = {\n  class: \"form-group\"\n};\nconst _hoisted_120 = [\"value\"];\nconst _hoisted_121 = {\n  class: \"form-group\"\n};\nconst _hoisted_122 = {\n  class: \"form-group\"\n};\nconst _hoisted_123 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_124 = {\n  class: \"form-group\"\n};\nconst _hoisted_125 = [\"value\"];\nconst _hoisted_126 = {\n  class: \"form-group\"\n};\nconst _hoisted_127 = {\n  class: \"form-group\"\n};\nconst _hoisted_128 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_129 = {\n  class: \"mb-2\"\n};\nconst _hoisted_130 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_131 = {\n  class: \"form-text\"\n};\nconst _hoisted_132 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_133 = {\n  class: \"p-4 border border-gray-200 rounded-md\"\n};\nconst _hoisted_134 = {\n  class: \"mb-3\"\n};\nconst _hoisted_135 = {\n  class: \"mb-3\"\n};\nconst _hoisted_136 = {\n  class: \"flex items-center mb-3\"\n};\nconst _hoisted_137 = {\n  class: \"inline-flex items-center\"\n};\nconst _hoisted_138 = {\n  class: \"mb-3\"\n};\nconst _hoisted_139 = {\n  class: \"flex space-x-3\"\n};\nconst _hoisted_140 = {\n  key: 0,\n  class: \"mb-3\"\n};\nconst _hoisted_141 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_142 = [\"type\"];\nconst _hoisted_143 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_144 = [\"value\"];\nconst _hoisted_145 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_146 = {\n  class: \"space-y-2\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_CustomCheckbox = _resolveComponent(\"CustomCheckbox\");\n  const _component_StatusBadge = _resolveComponent(\"StatusBadge\");\n  const _component_PasswordStrengthMeter = _resolveComponent(\"PasswordStrengthMeter\");\n  const _component_BaseModal = _resolveComponent(\"BaseModal\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createCommentVNode(\" 操作按钮 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.showEmergencyReset && $options.showEmergencyReset(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'exclamation-triangle'],\n    class: \"mr-2\"\n  }), _cache[66] || (_cache[66] = _createElementVNode(\"span\", null, \"紧急重置\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.openBatchUpdateModal && $options.openBatchUpdateModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'key'],\n    class: \"mr-2\"\n  }), _cache[67] || (_cache[67] = _createElementVNode(\"span\", null, \"批量更新密码\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.openBatchApplyModal && $options.openBatchApplyModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"mr-2\"\n  }), _cache[68] || (_cache[68] = _createElementVNode(\"span\", null, \"批量应用策略\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n    onClick: _cache[3] || (_cache[3] = (...args) => _ctx.openBatchAddAccountModal && _ctx.openBatchAddAccountModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'users'],\n    class: \"mr-2\"\n  }), _cache[69] || (_cache[69] = _createElementVNode(\"span\", null, \"批量添加账号\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" 视图切换 \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1 focus:outline-none\", {\n      'bg-blue-500 text-white': $data.viewMode === 'table',\n      'bg-gray-100 text-gray-600': $data.viewMode !== 'table'\n    }]),\n    onClick: _cache[4] || (_cache[4] = $event => $data.viewMode = 'table')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'table'],\n    class: \"mr-1\"\n  }), _cache[70] || (_cache[70] = _createTextVNode(\" 表格 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1 focus:outline-none\", {\n      'bg-blue-500 text-white': $data.viewMode === 'card',\n      'bg-gray-100 text-gray-600': $data.viewMode !== 'card'\n    }]),\n    onClick: _cache[5] || (_cache[5] = $event => $data.viewMode = 'card')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'th-large'],\n    class: \"mr-1\"\n  }), _cache[71] || (_cache[71] = _createTextVNode(\" 卡片 \"))], 2 /* CLASS */)]), _createCommentVNode(\" 筛选 \"), _createElementVNode(\"div\", _hoisted_6, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.filterText = $event),\n    placeholder: \"筛选主机...\",\n    class: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.filterText]]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"text-gray-400\"\n  })])]), _createCommentVNode(\" 账号筛选 \"), _createElementVNode(\"div\", _hoisted_8, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.accountFilterText = $event),\n    placeholder: \"筛选账号...\",\n    class: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.accountFilterText]]), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'user'],\n    class: \"text-gray-400\"\n  })])]), _createCommentVNode(\" 状态筛选 \"), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.statusFilter = $event),\n    class: \"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n  }, _cache[72] || (_cache[72] = [_createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有状态\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"normal\"\n  }, \"正常\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"warning\"\n  }, \"警告\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"error\"\n  }, \"错误\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.statusFilter]]), _createCommentVNode(\" 显示密码过期选项 \"), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.expiryFilter = $event),\n    class: \"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n  }, _cache[73] || (_cache[73] = [_createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有密码\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"expired\"\n  }, \"已过期\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"expiring-soon\"\n  }, \"即将过期\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"valid\"\n  }, \"有效期内\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.expiryFilter]])])])]), _createCommentVNode(\" 主机列表 \"), _createCommentVNode(\" 表格视图 \"), $data.viewMode === 'table' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createCommentVNode(\" 账号计数和导出按钮 \"), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_cache[74] || (_cache[74] = _createTextVNode(\" 显示 \")), _createElementVNode(\"span\", _hoisted_13, _toDisplayString($options.filteredAccounts.length), 1 /* TEXT */), _cache[75] || (_cache[75] = _createTextVNode(\" 个账号 (共 \")), _createElementVNode(\"span\", _hoisted_14, _toDisplayString($options.getAllAccounts.length), 1 /* TEXT */), _cache[76] || (_cache[76] = _createTextVNode(\" 个) \"))]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"button\", _hoisted_16, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'file-export'],\n    class: \"mr-1\"\n  }), _cache[77] || (_cache[77] = _createTextVNode(\" 导出 \"))]), _createElementVNode(\"button\", _hoisted_17, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'print'],\n    class: \"mr-1\"\n  }), _cache[78] || (_cache[78] = _createTextVNode(\" 打印 \"))])])]), _createElementVNode(\"table\", _hoisted_18, [_createElementVNode(\"thead\", _hoisted_19, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", _hoisted_20, [_createVNode(_component_CustomCheckbox, {\n    modelValue: $data.selectAll,\n    \"onUpdate:modelValue\": [_cache[10] || (_cache[10] = $event => $data.selectAll = $event), $options.toggleSelectAll]\n  }, {\n    default: _withCtx(() => _cache[79] || (_cache[79] = [_createTextVNode(\" 主机名 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _cache[80] || (_cache[80] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" IP地址 \", -1 /* HOISTED */)), _cache[81] || (_cache[81] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 账号 \", -1 /* HOISTED */)), _cache[82] || (_cache[82] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 最后密码修改时间 \", -1 /* HOISTED */)), _cache[83] || (_cache[83] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码过期时间 \", -1 /* HOISTED */)), _cache[84] || (_cache[84] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码 \", -1 /* HOISTED */)), _cache[85] || (_cache[85] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 状态 \", -1 /* HOISTED */)), _cache[86] || (_cache[86] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 操作 \", -1 /* HOISTED */))])]), _createElementVNode(\"tbody\", _hoisted_21, [_createCommentVNode(\" 按主机分组显示 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.groupedAccounts, hostGroup => {\n    return _openBlock(), _createElementBlock(_Fragment, {\n      key: hostGroup.hostId\n    }, [_createCommentVNode(\" 主机分组标题行 \"), _createElementVNode(\"tr\", _hoisted_22, [_createElementVNode(\"td\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, _toDisplayString(hostGroup.hostName) + \" (\" + _toDisplayString(hostGroup.hostIp) + \")\", 1 /* TEXT */)])])]), _createCommentVNode(\" 账号行 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(hostGroup.accounts, (account, accountIndex) => {\n      return _openBlock(), _createElementBlock(\"tr\", {\n        key: account.id,\n        class: _normalizeClass({\n          'bg-gray-50': accountIndex % 2 === 0,\n          'hover:bg-blue-50': true\n        })\n      }, [_createElementVNode(\"td\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_createVNode(_component_CustomCheckbox, {\n        modelValue: account.host.selected,\n        \"onUpdate:modelValue\": $event => account.host.selected = $event,\n        class: \"ml-4\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"span\", _hoisted_28, _toDisplayString(account.host.name), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])])]), _createElementVNode(\"td\", _hoisted_29, [_createElementVNode(\"div\", _hoisted_30, _toDisplayString(account.host.ip), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"span\", _hoisted_33, _toDisplayString(account.username), 1 /* TEXT */), account.isDefault ? (_openBlock(), _createElementBlock(\"span\", _hoisted_34, \" 默认 \")) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"td\", _hoisted_35, [_createElementVNode(\"div\", _hoisted_36, _toDisplayString(account.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_37, [_createElementVNode(\"div\", {\n        class: _normalizeClass({\n          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\n          'bg-red-100 text-red-800': $options.isPasswordExpired(account).status === 'danger' || $options.isPasswordExpired(account).status === 'expired',\n          'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(account).status === 'warning',\n          'bg-gray-100 text-gray-800': $options.isPasswordExpired(account).status === 'normal'\n        })\n      }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(account).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(account).status === 'expired' || $options.isPasswordExpired(account).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_38, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-triangle']\n      })])) : $options.isPasswordExpired(account).status === 'warning' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_39, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-circle']\n      })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]), _createElementVNode(\"td\", _hoisted_40, [_createElementVNode(\"div\", _hoisted_41, [_createElementVNode(\"div\", _hoisted_42, [_createElementVNode(\"input\", {\n        type: $data.passwordVisibility[account.id] ? 'text' : 'password',\n        value: account.password,\n        readonly: \"\",\n        class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n      }, null, 8 /* PROPS */, _hoisted_43)]), _createElementVNode(\"button\", {\n        onClick: $event => $options.togglePasswordVisibility(account.id),\n        class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $data.passwordVisibility[account.id] ? 'eye-slash' : 'eye'],\n        class: \"text-lg\"\n      }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_44)])]), _createElementVNode(\"td\", _hoisted_45, [_createVNode(_component_StatusBadge, {\n        type: account.host.status\n      }, null, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"td\", _hoisted_46, [_createElementVNode(\"div\", _hoisted_47, [_createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.openChangePasswordModal(account.host, account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'key'],\n        class: \"mr-1\"\n      }), _cache[87] || (_cache[87] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_48), _createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.copyPassword(account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'copy'],\n        class: \"mr-1\"\n      }), _cache[88] || (_cache[88] = _createTextVNode(\" 复制 \"))], 8 /* PROPS */, _hoisted_49)])])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))], 64 /* STABLE_FRAGMENT */);\n  }), 128 /* KEYED_FRAGMENT */)), _createCommentVNode(\" 无数据显示 \"), $options.filteredAccounts.length === 0 ? (_openBlock(), _createElementBlock(\"tr\", _hoisted_50, [_createElementVNode(\"td\", _hoisted_51, [_createElementVNode(\"div\", _hoisted_52, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"text-4xl mb-3\"\n  }), _cache[89] || (_cache[89] = _createElementVNode(\"p\", null, \"没有找到匹配的账号数据\", -1 /* HOISTED */))])])])) : _createCommentVNode(\"v-if\", true)])])])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 卡片视图 \"), _createElementVNode(\"div\", _hoisted_53, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredHosts, host => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: host.id,\n      class: \"bg-white overflow-hidden shadow rounded-lg\"\n    }, [_createElementVNode(\"div\", _hoisted_54, [_createCommentVNode(\" 主机头部 \"), _createElementVNode(\"div\", _hoisted_55, [_createElementVNode(\"div\", _hoisted_56, [_createVNode(_component_CustomCheckbox, {\n      modelValue: host.selected,\n      \"onUpdate:modelValue\": $event => host.selected = $event,\n      class: \"mr-2\"\n    }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"]), _createElementVNode(\"div\", null, [_createElementVNode(\"h3\", _hoisted_57, _toDisplayString(host.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_58, _toDisplayString(host.ip), 1 /* TEXT */)])]), _createVNode(_component_StatusBadge, {\n      type: host.status\n    }, null, 8 /* PROPS */, [\"type\"])]), _createCommentVNode(\" 账号列表 \"), _createElementVNode(\"div\", _hoisted_59, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(host.accounts, account => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: account.id,\n        class: _normalizeClass([\"border border-gray-200 rounded-lg p-3\", {\n          'border-green-300 bg-green-50': account.isDefault\n        }])\n      }, [_createElementVNode(\"div\", _hoisted_60, [_createElementVNode(\"div\", _hoisted_61, [_createElementVNode(\"span\", _hoisted_62, _toDisplayString(account.username), 1 /* TEXT */), account.isDefault ? (_openBlock(), _createElementBlock(\"span\", _hoisted_63, \" 默认 \")) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.openChangePasswordModal(host, account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'key'],\n        class: \"mr-1\"\n      }), _cache[90] || (_cache[90] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_64)]), _createCommentVNode(\" 密码展示 \"), _createElementVNode(\"div\", _hoisted_65, [_cache[91] || (_cache[91] = _createElementVNode(\"div\", {\n        class: \"text-xs font-medium text-gray-500 mb-1\"\n      }, \"密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_66, [_createElementVNode(\"input\", {\n        type: $data.passwordVisibility[account.id] ? 'text' : 'password',\n        value: account.password,\n        readonly: \"\",\n        class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n      }, null, 8 /* PROPS */, _hoisted_67), _createElementVNode(\"button\", {\n        onClick: $event => $options.togglePasswordVisibility(account.id),\n        class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $data.passwordVisibility[account.id] ? 'eye-slash' : 'eye'],\n        class: \"text-lg\"\n      }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_68)])]), _createCommentVNode(\" 密码信息区域 \"), _createElementVNode(\"div\", _hoisted_69, [_createElementVNode(\"div\", null, [_cache[92] || (_cache[92] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"最后修改时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_70, _toDisplayString(account.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[93] || (_cache[93] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"密码过期\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n        class: _normalizeClass({\n          'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\n          'bg-red-100 text-red-800': $options.isPasswordExpired(account).status === 'danger' || $options.isPasswordExpired(account).status === 'expired',\n          'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(account).status === 'warning',\n          'bg-gray-100 text-gray-800': $options.isPasswordExpired(account).status === 'normal'\n        })\n      }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(account).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(account).status === 'expired' || $options.isPasswordExpired(account).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_71, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-triangle']\n      })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)])])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 添加账号按钮 \"), _createElementVNode(\"div\", _hoisted_72, [_createElementVNode(\"button\", {\n      class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n      onClick: $event => $options.openAddAccountModal(host)\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'plus'],\n      class: \"mr-1\"\n    }), _cache[94] || (_cache[94] = _createTextVNode(\" 添加账号 \"))], 8 /* PROPS */, _hoisted_73)])])]);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 修改密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.changePasswordModal.show,\n    \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.changePasswordModal.show = $event),\n    title: \"修改密码\",\n    onConfirm: $options.updatePassword,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_74, [_cache[98] || (_cache[98] = _createElementVNode(\"div\", {\n      class: \"font-medium mb-2\"\n    }, \"主机信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_75, [_createElementVNode(\"div\", null, [_cache[95] || (_cache[95] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"主机名:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[96] || (_cache[96] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"IP地址:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.ip), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[97] || (_cache[97] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"账号:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentAccount.username), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_76, [_cache[101] || (_cache[101] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_77, [_createElementVNode(\"button\", {\n      onClick: _cache[11] || (_cache[11] = $event => $data.changePasswordModal.method = 'auto'),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", $data.changePasswordModal.method === 'auto' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-2\"\n    }), _cache[99] || (_cache[99] = _createTextVNode(\" 自动生成 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n      onClick: _cache[12] || (_cache[12] = $event => $data.changePasswordModal.method = 'manual'),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", $data.changePasswordModal.method === 'manual' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'edit'],\n      class: \"mr-2\"\n    }), _cache[100] || (_cache[100] = _createTextVNode(\" 手动输入 \"))], 2 /* CLASS */)])]), $data.changePasswordModal.method === 'auto' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_78, [_cache[104] || (_cache[104] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.changePasswordModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[14] || (_cache[14] = $event => $options.generatePassword())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_79);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.changePasswordModal.policyId]]), _createElementVNode(\"div\", _hoisted_80, [_createElementVNode(\"div\", _hoisted_81, [_cache[103] || (_cache[103] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      onClick: _cache[15] || (_cache[15] = $event => $options.generatePassword()),\n      type: \"button\",\n      class: \"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[102] || (_cache[102] = _createTextVNode(\" 重新生成 \"))])]), _createElementVNode(\"div\", _hoisted_82, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.generated ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.changePasswordModal.generatedPassword = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_83), [[_vModelDynamic, $data.changePasswordModal.generatedPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[17] || (_cache[17] = $event => $data.passwordVisibility.generated = !$data.passwordVisibility.generated),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.generated ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_84, [_createElementVNode(\"div\", _hoisted_85, [_cache[105] || (_cache[105] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"新密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_86, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.new ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[18] || (_cache[18] = $event => $data.changePasswordModal.newPassword = $event),\n      class: \"form-control flex-1\",\n      placeholder: \"输入新密码\"\n    }, null, 8 /* PROPS */, _hoisted_87), [[_vModelDynamic, $data.changePasswordModal.newPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[19] || (_cache[19] = $event => $data.passwordVisibility.new = !$data.passwordVisibility.new),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.new ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])]), _createElementVNode(\"div\", _hoisted_88, [_cache[106] || (_cache[106] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"确认密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_89, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.confirm ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $data.changePasswordModal.confirmPassword = $event),\n      class: _normalizeClass([\"form-control flex-1\", {\n        'border-red-500': $options.passwordMismatch\n      }]),\n      placeholder: \"再次输入新密码\"\n    }, null, 10 /* CLASS, PROPS */, _hoisted_90), [[_vModelDynamic, $data.changePasswordModal.confirmPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[21] || (_cache[21] = $event => $data.passwordVisibility.confirm = !$data.passwordVisibility.confirm),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.confirm ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])]), $options.passwordMismatch ? (_openBlock(), _createElementBlock(\"div\", _hoisted_91, \"两次输入的密码不一致\")) : _createCommentVNode(\"v-if\", true)]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.newPassword\n    }, null, 8 /* PROPS */, [\"password\"])])), _createElementVNode(\"div\", _hoisted_92, [_cache[110] || (_cache[110] = _createElementVNode(\"div\", {\n      class: \"form-label font-medium\"\n    }, \"执行选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.executeImmediately,\n      \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $data.changePasswordModal.executeImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[107] || (_cache[107] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"立即执行\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.saveHistory,\n      \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.changePasswordModal.saveHistory = $event)\n    }, {\n      default: _withCtx(() => _cache[108] || (_cache[108] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"保存历史记录\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.logAudit,\n      \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.changePasswordModal.logAudit = $event)\n    }, {\n      default: _withCtx(() => _cache[109] || (_cache[109] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"记录审计日志\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 添加账号弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.addAccountModal.show,\n    \"onUpdate:modelValue\": _cache[33] || (_cache[33] = $event => $data.addAccountModal.show = $event),\n    title: \"添加账号\",\n    onConfirm: $options.addAccount,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_93, [_cache[113] || (_cache[113] = _createElementVNode(\"div\", {\n      class: \"font-medium mb-2\"\n    }, \"主机信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_94, [_createElementVNode(\"div\", null, [_cache[111] || (_cache[111] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"主机名:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[112] || (_cache[112] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"IP地址:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.ip), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_95, [_cache[114] || (_cache[114] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"账号名称\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.addAccountModal.username = $event),\n      class: \"form-control\",\n      placeholder: \"输入账号名称\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addAccountModal.username]])]), _createElementVNode(\"div\", _hoisted_96, [_cache[116] || (_cache[116] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"设为默认账号\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_97, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.addAccountModal.isDefault = $event),\n      class: \"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.addAccountModal.isDefault]]), _cache[115] || (_cache[115] = _createElementVNode(\"label\", {\n      class: \"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"\n    }, null, -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_98, [_cache[117] || (_cache[117] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $data.addAccountModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[29] || (_cache[29] = $event => $options.generatePasswordForNewAccount())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_99);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.addAccountModal.policyId]])]), _createElementVNode(\"div\", _hoisted_100, [_createElementVNode(\"div\", _hoisted_101, [_cache[119] || (_cache[119] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      onClick: _cache[30] || (_cache[30] = $event => $options.generatePasswordForNewAccount()),\n      type: \"button\",\n      class: \"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[118] || (_cache[118] = _createTextVNode(\" 重新生成 \"))])]), _createElementVNode(\"div\", _hoisted_102, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.newAccount ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[31] || (_cache[31] = $event => $data.addAccountModal.password = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_103), [[_vModelDynamic, $data.addAccountModal.password]]), _createElementVNode(\"button\", {\n      onClick: _cache[32] || (_cache[32] = $event => $data.passwordVisibility.newAccount = !$data.passwordVisibility.newAccount),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.newAccount ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量更新密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchUpdateModal.show,\n    \"onUpdate:modelValue\": _cache[43] || (_cache[43] = $event => $data.batchUpdateModal.show = $event),\n    title: \"批量更新密码\",\n    \"confirm-text\": \"开始更新\",\n    size: \"lg\",\n    onConfirm: $options.batchUpdatePasswords,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_104, [_cache[121] || (_cache[121] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_105, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatch,\n      \"onUpdate:modelValue\": [_cache[34] || (_cache[34] = $event => $data.selectAllBatch = $event), $options.toggleSelectAllBatch]\n    }, {\n      default: _withCtx(() => _cache[120] || (_cache[120] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_106, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchUpdateModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchUpdateModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_107, \"已选择 \" + _toDisplayString($options.selectedHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_108, [_cache[122] || (_cache[122] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[35] || (_cache[35] = $event => $data.batchUpdateModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_109);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchUpdateModal.policyId]])]), _createElementVNode(\"div\", _hoisted_110, [_cache[127] || (_cache[127] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_111, [_createElementVNode(\"label\", _hoisted_112, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[36] || (_cache[36] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"immediate\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[123] || (_cache[123] = _createElementVNode(\"span\", null, \"立即执行\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_113, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[37] || (_cache[37] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"scheduled\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[124] || (_cache[124] = _createElementVNode(\"span\", null, \"定时执行\", -1 /* HOISTED */))])]), $data.batchUpdateModal.executionTime === 'scheduled' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_114, [_createElementVNode(\"div\", _hoisted_115, [_createElementVNode(\"div\", null, [_cache[125] || (_cache[125] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"日期\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"date\",\n      \"onUpdate:modelValue\": _cache[38] || (_cache[38] = $event => $data.batchUpdateModal.scheduledDate = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledDate]])]), _createElementVNode(\"div\", null, [_cache[126] || (_cache[126] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"时间\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"time\",\n      \"onUpdate:modelValue\": _cache[39] || (_cache[39] = $event => $data.batchUpdateModal.scheduledTime = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledTime]])])])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_116, [_cache[131] || (_cache[131] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[40] || (_cache[40] = $event => $data.batchUpdateModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[128] || (_cache[128] = [_createTextVNode(\" 忽略错误继续执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.detailedLog,\n      \"onUpdate:modelValue\": _cache[41] || (_cache[41] = $event => $data.batchUpdateModal.detailedLog = $event)\n    }, {\n      default: _withCtx(() => _cache[129] || (_cache[129] = [_createTextVNode(\" 记录详细日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.sendNotification,\n      \"onUpdate:modelValue\": _cache[42] || (_cache[42] = $event => $data.batchUpdateModal.sendNotification = $event)\n    }, {\n      default: _withCtx(() => _cache[130] || (_cache[130] = [_createTextVNode(\" 执行完成后发送通知 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量应用策略弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchApplyModal.show,\n    \"onUpdate:modelValue\": _cache[47] || (_cache[47] = $event => $data.batchApplyModal.show = $event),\n    title: \"批量应用密码策略\",\n    \"confirm-text\": \"应用策略\",\n    onConfirm: $options.batchApplyPolicy,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_117, [_cache[132] || (_cache[132] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_118, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchApplyModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchApplyModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_119, [_cache[133] || (_cache[133] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[44] || (_cache[44] = $event => $data.batchApplyModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name), 9 /* TEXT, PROPS */, _hoisted_120);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchApplyModal.policyId]])]), _createElementVNode(\"div\", _hoisted_121, [_cache[136] || (_cache[136] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.updateImmediately,\n      \"onUpdate:modelValue\": _cache[45] || (_cache[45] = $event => $data.batchApplyModal.updateImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[134] || (_cache[134] = [_createTextVNode(\" 立即更新密码以符合策略 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.applyOnNextUpdate,\n      \"onUpdate:modelValue\": _cache[46] || (_cache[46] = $event => $data.batchApplyModal.applyOnNextUpdate = $event)\n    }, {\n      default: _withCtx(() => _cache[135] || (_cache[135] = [_createTextVNode(\" 下次密码更新时应用 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 紧急重置密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.emergencyResetModal.show,\n    \"onUpdate:modelValue\": _cache[51] || (_cache[51] = $event => $data.emergencyResetModal.show = $event),\n    title: \"紧急密码重置\",\n    \"confirm-text\": \"立即重置\",\n    icon: \"exclamation-triangle\",\n    danger: \"\",\n    onConfirm: $options.emergencyReset,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_cache[142] || (_cache[142] = _createElementVNode(\"div\", {\n      class: \"bg-red-50 text-red-700 p-3 rounded-md mb-4\"\n    }, [_createElementVNode(\"p\", null, \"紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_122, [_cache[137] || (_cache[137] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_123, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.emergencyResetModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.emergencyResetModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_124, [_cache[138] || (_cache[138] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用紧急策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[48] || (_cache[48] = $event => $data.emergencyResetModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.emergencyPolicies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_125);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.policyId]])]), _createElementVNode(\"div\", _hoisted_126, [_cache[140] || (_cache[140] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"操作原因\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[49] || (_cache[49] = $event => $data.emergencyResetModal.reason = $event),\n      class: \"form-select\"\n    }, _cache[139] || (_cache[139] = [_createElementVNode(\"option\", {\n      value: \"security_incident\"\n    }, \"安全事件响应\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"password_leak\"\n    }, \"密码泄露\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"abnormal_access\"\n    }, \"异常访问\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"compliance\"\n    }, \"合规要求\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"other\"\n    }, \"其他原因\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.reason]])]), _createElementVNode(\"div\", _hoisted_127, [_cache[141] || (_cache[141] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"附加说明\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n      \"onUpdate:modelValue\": _cache[50] || (_cache[50] = $event => $data.emergencyResetModal.description = $event),\n      class: \"form-control\",\n      rows: \"2\",\n      placeholder: \"请输入重置原因详细说明\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.emergencyResetModal.description]])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量添加账号弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchAddAccountModal.show,\n    \"onUpdate:modelValue\": _cache[65] || (_cache[65] = $event => $data.batchAddAccountModal.show = $event),\n    title: \"批量添加账号\",\n    size: \"lg\",\n    onConfirm: $options.batchAddAccounts,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_128, [_cache[144] || (_cache[144] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_129, [_createVNode(_component_CustomCheckbox, {\n      modelValue: _ctx.selectAllBatchAdd,\n      \"onUpdate:modelValue\": [_cache[52] || (_cache[52] = $event => _ctx.selectAllBatchAdd = $event), _ctx.toggleSelectAllBatchAdd]\n    }, {\n      default: _withCtx(() => _cache[143] || (_cache[143] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_130, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchAddAccountModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchAddAccountModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_131, \"已选择 \" + _toDisplayString($options.selectedBatchAddHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_132, [_cache[155] || (_cache[155] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"账号信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_133, [_createElementVNode(\"div\", _hoisted_134, [_cache[145] || (_cache[145] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, [_createTextVNode(\"账号名称 \"), _createElementVNode(\"span\", {\n      class: \"text-red-500\"\n    }, \"*\")], -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[53] || (_cache[53] = $event => $data.batchAddAccountModal.username = $event),\n      class: \"form-control\",\n      placeholder: \"输入统一账号名称\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchAddAccountModal.username]]), _cache[146] || (_cache[146] = _createElementVNode(\"div\", {\n      class: \"text-xs text-gray-500 mt-1\"\n    }, \"将在所有选中主机上创建同名账号\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_135, [_cache[148] || (_cache[148] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"账号角色\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[54] || (_cache[54] = $event => $data.batchAddAccountModal.role = $event),\n      class: \"form-select\"\n    }, _cache[147] || (_cache[147] = [_createElementVNode(\"option\", {\n      value: \"admin\"\n    }, \"管理员\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"user\"\n    }, \"普通用户\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"service\"\n    }, \"服务账号\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"readonly\"\n    }, \"只读账号\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchAddAccountModal.role]])]), _createElementVNode(\"div\", _hoisted_136, [_createElementVNode(\"label\", _hoisted_137, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[55] || (_cache[55] = $event => $data.batchAddAccountModal.setAsDefault = $event),\n      class: \"form-checkbox\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.batchAddAccountModal.setAsDefault]]), _cache[149] || (_cache[149] = _createElementVNode(\"span\", {\n      class: \"ml-2\"\n    }, \"设为默认账号\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_138, [_cache[152] || (_cache[152] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_139, [_createElementVNode(\"button\", {\n      onClick: _cache[56] || (_cache[56] = $event => $data.batchAddAccountModal.useSamePassword = true),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", $data.batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'key'],\n      class: \"mr-2\"\n    }), _cache[150] || (_cache[150] = _createTextVNode(\" 同一密码 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n      onClick: _cache[57] || (_cache[57] = $event => $data.batchAddAccountModal.useSamePassword = false),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", !$data.batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'random'],\n      class: \"mr-2\"\n    }), _cache[151] || (_cache[151] = _createTextVNode(\" 随机密码 \"))], 2 /* CLASS */)])]), $data.batchAddAccountModal.useSamePassword ? (_openBlock(), _createElementBlock(\"div\", _hoisted_140, [_cache[154] || (_cache[154] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"统一密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_141, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.batchPassword ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[58] || (_cache[58] = $event => $data.batchAddAccountModal.password = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_142), [[_vModelDynamic, $data.batchAddAccountModal.password]]), _createElementVNode(\"button\", {\n      onClick: _cache[59] || (_cache[59] = $event => $data.passwordVisibility.batchPassword = !$data.passwordVisibility.batchPassword),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.batchPassword ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])]), _createElementVNode(\"button\", {\n      onClick: _cache[60] || (_cache[60] = $event => $options.generatePasswordForBatchAccount()),\n      type: \"button\",\n      class: \"ml-2 px-3 py-1.5 border border-gray-300 text-xs rounded-md\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[153] || (_cache[153] = _createTextVNode(\" 重新生成 \"))])])])) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_143, [_cache[156] || (_cache[156] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[61] || (_cache[61] = $event => $data.batchAddAccountModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[62] || (_cache[62] = $event => $options.generatePasswordForBatchAccount())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_144);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.batchAddAccountModal.policyId]])]), _createElementVNode(\"div\", _hoisted_145, [_cache[159] || (_cache[159] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_146, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchAddAccountModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[63] || (_cache[63] = $event => $data.batchAddAccountModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[157] || (_cache[157] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"忽略错误继续执行\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchAddAccountModal.generateReport,\n      \"onUpdate:modelValue\": _cache[64] || (_cache[64] = $event => $data.batchAddAccountModal.generateReport = $event)\n    }, {\n      default: _withCtx(() => _cache[158] || (_cache[158] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"生成账号创建报告\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "scope", "colspan", "_createElementBlock", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "args", "$options", "showEmergencyReset", "_createVNode", "_component_font_awesome_icon", "icon", "openBatchUpdateModal", "openBatchApplyModal", "_ctx", "openBatchAddAccountModal", "_hoisted_4", "_hoisted_5", "_normalizeClass", "$data", "viewMode", "$event", "_createTextVNode", "_hoisted_6", "type", "filterText", "placeholder", "_hoisted_7", "_hoisted_8", "accountFilterText", "_hoisted_9", "statusFilter", "value", "expiryFilter", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_toDisplayString", "filteredAccounts", "length", "_hoisted_14", "getAllAccounts", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_component_CustomCheckbox", "modelValue", "selectAll", "toggleSelectAll", "default", "_withCtx", "_", "_hoisted_21", "_Fragment", "_renderList", "groupedAccounts", "hostGroup", "hostId", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "hostName", "hostIp", "accounts", "account", "accountIndex", "id", "_hoisted_26", "_hoisted_27", "host", "selected", "_hoisted_28", "name", "_hoisted_29", "_hoisted_30", "ip", "_hoisted_31", "_hoisted_32", "_hoisted_33", "username", "isDefault", "_hoisted_34", "_hoisted_35", "_hoisted_36", "lastPasswordChange", "_hoisted_37", "isPasswordExpired", "status", "text", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "passwordVisibility", "password", "readonly", "_hoisted_43", "togglePasswordVisibility", "_hoisted_44", "_hoisted_45", "_component_StatusBadge", "_hoisted_46", "_hoisted_47", "openChangePasswordModal", "_hoisted_48", "copyPassword", "_hoisted_49", "_hoisted_50", "_hoisted_51", "_hoisted_52", "_hoisted_53", "filteredHosts", "_hoisted_54", "_hoisted_55", "_hoisted_56", "_hoisted_57", "_hoisted_58", "_hoisted_59", "_hoisted_60", "_hoisted_61", "_hoisted_62", "_hoisted_63", "_hoisted_64", "_hoisted_65", "_hoisted_66", "_hoisted_67", "_hoisted_68", "_hoisted_69", "_hoisted_70", "_hoisted_71", "_hoisted_72", "openAddAccountModal", "_hoisted_73", "_component_BaseModal", "changePasswordModal", "show", "title", "onConfirm", "updatePassword", "loading", "processing", "_hoisted_74", "_hoisted_75", "currentHost", "currentAccount", "_hoisted_76", "_hoisted_77", "method", "_hoisted_78", "policyId", "onChange", "generatePassword", "policies", "policy", "<PERSON><PERSON><PERSON><PERSON>", "expiryDays", "_hoisted_79", "_hoisted_80", "_hoisted_81", "_hoisted_82", "generated", "generatedPassword", "_hoisted_83", "_hoisted_84", "_hoisted_85", "_hoisted_86", "new", "newPassword", "_hoisted_87", "_hoisted_88", "_hoisted_89", "confirm", "confirmPassword", "passwordMismatch", "_hoisted_90", "_hoisted_91", "_component_PasswordStrengthMeter", "_hoisted_92", "executeImmediately", "saveHistory", "logAudit", "addAccountModal", "addAccount", "_hoisted_93", "_hoisted_94", "_hoisted_95", "_hoisted_96", "_hoisted_97", "_hoisted_98", "generatePasswordForNewAccount", "_hoisted_99", "_hoisted_100", "_hoisted_101", "_hoisted_102", "newAccount", "_hoisted_103", "batchUpdateModal", "size", "batchUpdatePasswords", "_hoisted_104", "_hoisted_105", "selectAllBatch", "toggleSelectAllBatch", "_hoisted_106", "hosts", "_createBlock", "selectedHosts", "_hoisted_107", "selectedHostsCount", "_hoisted_108", "_hoisted_109", "_hoisted_110", "_hoisted_111", "_hoisted_112", "executionTime", "_hoisted_113", "_hoisted_114", "_hoisted_115", "scheduledDate", "scheduledTime", "_hoisted_116", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "batchApplyPolicy", "_hoisted_117", "_hoisted_118", "selectedHostsList", "_hoisted_119", "_hoisted_120", "_hoisted_121", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "danger", "emergencyReset", "_hoisted_122", "_hoisted_123", "_hoisted_124", "emergencyPolicies", "_hoisted_125", "_hoisted_126", "reason", "_hoisted_127", "description", "rows", "batchAddAccountModal", "batchAddAccounts", "_hoisted_128", "_hoisted_129", "selectAllBatchAdd", "toggleSelectAllBatchAdd", "_hoisted_130", "_hoisted_131", "selectedBatchAddHostsCount", "_hoisted_132", "_hoisted_133", "_hoisted_134", "_hoisted_135", "role", "_hoisted_136", "_hoisted_137", "setAsDefault", "_hoisted_138", "_hoisted_139", "useSamePassword", "_hoisted_140", "_hoisted_141", "batchPassword", "_hoisted_142", "generatePasswordForBatchAccount", "_hoisted_143", "_hoisted_144", "_hoisted_145", "_hoisted_146", "generateReport"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"bg-white shadow rounded-lg p-4 mb-6\">\r\n      <div class=\"flex flex-wrap items-center justify-between\">\r\n        <div class=\"flex space-x-3 mb-2 sm:mb-0\">\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\r\n            @click=\"showEmergencyReset\">\r\n            <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2\" />\r\n            <span>紧急重置</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            @click=\"openBatchUpdateModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n            <span>批量更新密码</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\r\n            @click=\"openBatchApplyModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n            <span>批量应用策略</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\r\n            @click=\"openBatchAddAccountModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'users']\" class=\"mr-2\" />\r\n            <span>批量添加账号</span>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"flex items-center space-x-4\">\r\n          <!-- 视图切换 -->\r\n          <div class=\"flex items-center border rounded-md overflow-hidden\">\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'table', 'bg-gray-100 text-gray-600': viewMode !== 'table' }\"\r\n              @click=\"viewMode = 'table'\">\r\n              <font-awesome-icon :icon=\"['fas', 'table']\" class=\"mr-1\" />\r\n              表格\r\n            </button>\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'card', 'bg-gray-100 text-gray-600': viewMode !== 'card' }\"\r\n              @click=\"viewMode = 'card'\">\r\n              <font-awesome-icon :icon=\"['fas', 'th-large']\" class=\"mr-1\" />\r\n              卡片\r\n            </button>\r\n          </div>\r\n\r\n          <!-- 筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"filterText\" placeholder=\"筛选主机...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 账号筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"accountFilterText\" placeholder=\"筛选账号...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'user']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 状态筛选 -->\r\n          <select v-model=\"statusFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有状态</option>\r\n            <option value=\"normal\">正常</option>\r\n            <option value=\"warning\">警告</option>\r\n            <option value=\"error\">错误</option>\r\n          </select>\r\n\r\n          <!-- 显示密码过期选项 -->\r\n          <select v-model=\"expiryFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有密码</option>\r\n            <option value=\"expired\">已过期</option>\r\n            <option value=\"expiring-soon\">即将过期</option>\r\n            <option value=\"valid\">有效期内</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <!-- 表格视图 -->\r\n    <div v-if=\"viewMode === 'table'\" class=\"bg-white rounded-lg shadow overflow-hidden\">\r\n      <!-- 账号计数和导出按钮 -->\r\n      <div class=\"px-4 py-3 bg-gray-50 border-b flex justify-between items-center\">\r\n        <div class=\"text-sm text-gray-700\">\r\n          显示 <span class=\"font-medium\">{{ filteredAccounts.length }}</span> 个账号\r\n          (共 <span class=\"font-medium\">{{ getAllAccounts.length }}</span> 个)\r\n        </div>\r\n        <div class=\"flex space-x-2\">\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\r\n            <font-awesome-icon :icon=\"['fas', 'file-export']\" class=\"mr-1\" />\r\n            导出\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\r\n            <font-awesome-icon :icon=\"['fas', 'print']\" class=\"mr-1\" />\r\n            打印\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <table class=\"min-w-full divide-y divide-gray-200\">\r\n        <thead class=\"bg-gray-50\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n                主机名\r\n              </CustomCheckbox>\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              IP地址\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              账号\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              最后密码修改时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码过期时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              状态\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              操作\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"bg-white divide-y divide-gray-200\">\r\n          <!-- 按主机分组显示 -->\r\n          <template v-for=\"hostGroup in groupedAccounts\" :key=\"hostGroup.hostId\">\r\n            <!-- 主机分组标题行 -->\r\n            <tr class=\"bg-gray-100\">\r\n              <td colspan=\"8\" class=\"px-6 py-2\">\r\n                <div class=\"flex items-center justify-between\">\r\n                  <div class=\"font-medium text-gray-700\">{{ hostGroup.hostName }} ({{ hostGroup.hostIp }})</div>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n            <!-- 账号行 -->\r\n            <tr v-for=\"(account, accountIndex) in hostGroup.accounts\" :key=\"account.id\"\r\n              :class=\"{ 'bg-gray-50': accountIndex % 2 === 0, 'hover:bg-blue-50': true }\">\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <CustomCheckbox v-model=\"account.host.selected\" class=\"ml-4\">\r\n                    <span class=\"ml-2 font-medium text-gray-900\">{{ account.host.name }}</span>\r\n                  </CustomCheckbox>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-900\">{{ account.host.ip }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-500\">{{ account.lastPasswordChange || '-' }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div :class=\"{\r\n                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\r\n                  'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                  'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                  'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                }\">\r\n                  {{ isPasswordExpired(account).text }}\r\n                  <span\r\n                    v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                    class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                  </span>\r\n                  <span v-else-if=\"isPasswordExpired(account).status === 'warning'\" class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" />\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <div class=\"flex-grow\">\r\n                    <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\"\r\n                      readonly\r\n                      class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  </div>\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <StatusBadge :type=\"account.host.status\" />\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                <div class=\"flex space-x-2\">\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"openChangePasswordModal(account.host, account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                    修改密码\r\n                  </button>\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"copyPassword(account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'copy']\" class=\"mr-1\" />\r\n                    复制\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          </template>\r\n          <!-- 无数据显示 -->\r\n          <tr v-if=\"filteredAccounts.length === 0\">\r\n            <td colspan=\"8\" class=\"px-6 py-10 text-center\">\r\n              <div class=\"text-gray-500\">\r\n                <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-4xl mb-3\" />\r\n                <p>没有找到匹配的账号数据</p>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <!-- 卡片视图 -->\r\n    <div v-else class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\">\r\n      <div v-for=\"host in filteredHosts\" :key=\"host.id\" class=\"bg-white overflow-hidden shadow rounded-lg\">\r\n        <div class=\"px-4 py-5 sm:p-6\">\r\n          <!-- 主机头部 -->\r\n          <div class=\"flex justify-between items-start mb-4\">\r\n            <div class=\"flex items-center\">\r\n              <CustomCheckbox v-model=\"host.selected\" class=\"mr-2\" />\r\n              <div>\r\n                <h3 class=\"text-lg font-medium text-gray-900\">{{ host.name }}</h3>\r\n                <p class=\"text-sm text-gray-500\">{{ host.ip }}</p>\r\n              </div>\r\n            </div>\r\n            <StatusBadge :type=\"host.status\" />\r\n          </div>\r\n\r\n          <!-- 账号列表 -->\r\n          <div class=\"space-y-4\">\r\n            <div v-for=\"account in host.accounts\" :key=\"account.id\" class=\"border border-gray-200 rounded-lg p-3\"\r\n              :class=\"{ 'border-green-300 bg-green-50': account.isDefault }\">\r\n              <div class=\"flex justify-between items-center mb-2\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n                <button\r\n                  class=\"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                  @click=\"openChangePasswordModal(host, account)\">\r\n                  <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                  修改密码\r\n                </button>\r\n              </div>\r\n\r\n              <!-- 密码展示 -->\r\n              <div class=\"mb-2\">\r\n                <div class=\"text-xs font-medium text-gray-500 mb-1\">密码</div>\r\n                <div class=\"flex items-center\">\r\n                  <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\" readonly\r\n                    class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 密码信息区域 -->\r\n              <div class=\"grid grid-cols-2 gap-2 text-xs\">\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">最后修改时间</div>\r\n                  <div class=\"text-gray-900\">{{ account.lastPasswordChange || '-' }}</div>\r\n                </div>\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">密码过期</div>\r\n                  <div :class=\"{\r\n                    'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\r\n                    'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                    'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                    'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                  }\">\r\n                    {{ isPasswordExpired(account).text }}\r\n                    <span\r\n                      v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                      class=\"ml-1\">\r\n                      <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 添加账号按钮 -->\r\n          <div class=\"mt-4 flex justify-center\">\r\n            <button\r\n              class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n              @click=\"openAddAccountModal(host)\">\r\n              <font-awesome-icon :icon=\"['fas', 'plus']\" class=\"mr-1\" />\r\n              添加账号\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" @confirm=\"updatePassword\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n          <div><span class=\"font-medium\">账号:</span> {{ currentAccount.username }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-3\">\r\n          <button @click=\"changePasswordModal.method = 'auto'\"\r\n            class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n            :class=\"changePasswordModal.method === 'auto' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-2\" />\r\n            自动生成\r\n          </button>\r\n          <button @click=\"changePasswordModal.method = 'manual'\"\r\n            class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n            :class=\"changePasswordModal.method === 'manual' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n            <font-awesome-icon :icon=\"['fas', 'edit']\" class=\"mr-2\" />\r\n            手动输入\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-if=\"changePasswordModal.method === 'auto'\" class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"changePasswordModal.policyId\" class=\"form-select\" @change=\"generatePassword()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n\r\n        <div class=\"mt-3\">\r\n          <div class=\"flex justify-between mb-1\">\r\n            <label class=\"form-label\">生成的密码</label>\r\n            <button @click=\"generatePassword()\" type=\"button\"\r\n              class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n              重新生成\r\n            </button>\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.generated ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.generatedPassword\" readonly class=\"form-control flex-1 bg-gray-50\" />\r\n            <button @click=\"passwordVisibility.generated = !passwordVisibility.generated\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.generated ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else class=\"space-y-4\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.new ? 'text' : 'password'\" v-model=\"changePasswordModal.newPassword\"\r\n              class=\"form-control flex-1\" placeholder=\"输入新密码\" />\r\n            <button @click=\"passwordVisibility.new = !passwordVisibility.new\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.new ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.confirm ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.confirmPassword\" class=\"form-control flex-1\"\r\n              :class=\"{ 'border-red-500': passwordMismatch }\" placeholder=\"再次输入新密码\" />\r\n            <button @click=\"passwordVisibility.confirm = !passwordVisibility.confirm\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.confirm ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n          <div v-if=\"passwordMismatch\" class=\"text-sm text-red-500 mt-1\">两次输入的密码不一致</div>\r\n        </div>\r\n\r\n        <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n      </div>\r\n\r\n      <div class=\"space-y-2 mt-4\">\r\n        <div class=\"form-label font-medium\">执行选项</div>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          <span class=\"ml-2\">立即执行</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          <span class=\"ml-2\">保存历史记录</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          <span class=\"ml-2\">记录审计日志</span>\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 添加账号弹窗 -->\r\n    <BaseModal v-model=\"addAccountModal.show\" title=\"添加账号\" @confirm=\"addAccount\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">账号名称</label>\r\n        <input type=\"text\" v-model=\"addAccountModal.username\" class=\"form-control\" placeholder=\"输入账号名称\" />\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">设为默认账号</label>\r\n        <div class=\"relative inline-block w-10 mr-2 align-middle select-none\">\r\n          <input type=\"checkbox\" v-model=\"addAccountModal.isDefault\"\r\n            class=\"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\" />\r\n          <label class=\"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"></label>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"addAccountModal.policyId\" class=\"form-select\" @change=\"generatePasswordForNewAccount()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <div class=\"flex justify-between mb-1\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <button @click=\"generatePasswordForNewAccount()\" type=\"button\"\r\n            class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n            重新生成\r\n          </button>\r\n        </div>\r\n        <div class=\"flex items-center\">\r\n          <input :type=\"passwordVisibility.newAccount ? 'text' : 'password'\" v-model=\"addAccountModal.password\" readonly\r\n            class=\"form-control flex-1 bg-gray-50\" />\r\n          <button @click=\"passwordVisibility.newAccount = !passwordVisibility.newAccount\" type=\"button\"\r\n            class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', passwordVisibility.newAccount ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量添加账号弹窗 -->\r\n    <BaseModal v-model=\"batchAddAccountModal.show\" title=\"批量添加账号\" size=\"lg\" @confirm=\"batchAddAccounts\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatchAdd\" @update:modelValue=\"toggleSelectAllBatchAdd\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchAddAccountModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedBatchAddHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">账号信息</label>\r\n        <div class=\"p-4 border border-gray-200 rounded-md\">\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">账号名称 <span class=\"text-red-500\">*</span></label>\r\n            <input type=\"text\" v-model=\"batchAddAccountModal.username\" class=\"form-control\" placeholder=\"输入统一账号名称\" />\r\n            <div class=\"text-xs text-gray-500 mt-1\">将在所有选中主机上创建同名账号</div>\r\n          </div>\r\n\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">账号角色</label>\r\n            <select v-model=\"batchAddAccountModal.role\" class=\"form-select\">\r\n              <option value=\"admin\">管理员</option>\r\n              <option value=\"user\">普通用户</option>\r\n              <option value=\"service\">服务账号</option>\r\n              <option value=\"readonly\">只读账号</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div class=\"flex items-center mb-3\">\r\n            <label class=\"inline-flex items-center\">\r\n              <input type=\"checkbox\" v-model=\"batchAddAccountModal.setAsDefault\" class=\"form-checkbox\">\r\n              <span class=\"ml-2\">设为默认账号</span>\r\n            </label>\r\n          </div>\r\n\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">密码生成方式</label>\r\n            <div class=\"flex space-x-3\">\r\n              <button @click=\"batchAddAccountModal.useSamePassword = true\"\r\n                class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n                :class=\"batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n                <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n                同一密码\r\n              </button>\r\n              <button @click=\"batchAddAccountModal.useSamePassword = false\"\r\n                class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n                :class=\"!batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n                <font-awesome-icon :icon=\"['fas', 'random']\" class=\"mr-2\" />\r\n                随机密码\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <div v-if=\"batchAddAccountModal.useSamePassword\" class=\"mb-3\">\r\n            <label class=\"form-label\">统一密码</label>\r\n            <div class=\"flex items-center\">\r\n              <input :type=\"passwordVisibility.batchPassword ? 'text' : 'password'\"\r\n                v-model=\"batchAddAccountModal.password\" readonly class=\"form-control flex-1 bg-gray-50\" />\r\n              <button @click=\"passwordVisibility.batchPassword = !passwordVisibility.batchPassword\" type=\"button\"\r\n                class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                <font-awesome-icon :icon=\"['fas', passwordVisibility.batchPassword ? 'eye-slash' : 'eye']\"\r\n                  class=\"text-lg\" />\r\n              </button>\r\n              <button @click=\"generatePasswordForBatchAccount()\" type=\"button\"\r\n                class=\"ml-2 px-3 py-1.5 border border-gray-300 text-xs rounded-md\">\r\n                <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n                重新生成\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchAddAccountModal.policyId\" class=\"form-select\" @change=\"generatePasswordForBatchAccount()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <div class=\"space-y-2\">\r\n          <CustomCheckbox v-model=\"batchAddAccountModal.ignoreErrors\">\r\n            <span class=\"ml-2\">忽略错误继续执行</span>\r\n          </CustomCheckbox>\r\n          <CustomCheckbox v-model=\"batchAddAccountModal.generateReport\">\r\n            <span class=\"ml-2\">生成账号创建报告</span>\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      currentAccount: {},\r\n      passwordVisibility: {\r\n        generated: false,\r\n        new: false,\r\n        confirm: false,\r\n        newAccount: false,\r\n        batchPassword: false\r\n      },\r\n      viewMode: 'table',\r\n      filterText: '',\r\n      accountFilterText: '',\r\n      statusFilter: 'all',\r\n      expiryFilter: 'all',\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      },\r\n\r\n      // 添加账号弹窗\r\n      addAccountModal: {\r\n        show: false,\r\n        username: '',\r\n        password: '',\r\n        isDefault: false,\r\n        policyId: 1\r\n      },\r\n\r\n      // 批量添加账号弹窗\r\n      batchAddAccountModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        username: '',\r\n        password: '',\r\n        role: 'admin',\r\n        setAsDefault: false,\r\n        useSamePassword: true,\r\n        policyId: 1\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    },\r\n\r\n    // 过滤后的主机列表\r\n    filteredHosts() {\r\n      return this.hosts.filter(host => {\r\n        // 文本过滤\r\n        const textMatch = this.filterText === '' ||\r\n          host.name.toLowerCase().includes(this.filterText.toLowerCase()) ||\r\n          host.ip.includes(this.filterText);\r\n\r\n        // 状态过滤\r\n        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;\r\n\r\n        return textMatch && statusMatch;\r\n      });\r\n    },\r\n\r\n    // 获取所有账号（扁平化处理）\r\n    getAllAccounts() {\r\n      // 为每个账号添加主机引用\r\n      const accounts = [];\r\n      this.filteredHosts.forEach(host => {\r\n        host.accounts.forEach(account => {\r\n          accounts.push({\r\n            ...account,\r\n            host: host\r\n          });\r\n        });\r\n      });\r\n      return accounts;\r\n    },\r\n\r\n    // 筛选后的账号\r\n    filteredAccounts() {\r\n      return this.getAllAccounts.filter(account => {\r\n        // 账号名称筛选\r\n        const accountMatch = this.accountFilterText === '' ||\r\n          account.username.toLowerCase().includes(this.accountFilterText.toLowerCase());\r\n\r\n        // 密码过期筛选\r\n        let expiryMatch = true;\r\n        if (this.expiryFilter !== 'all') {\r\n          const expiryStatus = this.isPasswordExpired(account).status;\r\n          if (this.expiryFilter === 'expired') {\r\n            expiryMatch = expiryStatus === 'expired';\r\n          } else if (this.expiryFilter === 'expiring-soon') {\r\n            expiryMatch = expiryStatus === 'danger' || expiryStatus === 'warning';\r\n          } else if (this.expiryFilter === 'valid') {\r\n            expiryMatch = expiryStatus === 'normal';\r\n          }\r\n        }\r\n\r\n        return accountMatch && expiryMatch;\r\n      });\r\n    },\r\n\r\n    // 分组后的账号列表\r\n    groupedAccounts() {\r\n      // 按主机ID分组\r\n      const groups = {};\r\n      this.filteredAccounts.forEach(account => {\r\n        const hostId = account.host.id;\r\n        if (!groups[hostId]) {\r\n          groups[hostId] = {\r\n            hostId: hostId,\r\n            hostName: account.host.name,\r\n            hostIp: account.host.ip,\r\n            host: account.host,\r\n            accounts: []\r\n          };\r\n        }\r\n        groups[hostId].accounts.push(account);\r\n      });\r\n\r\n      // 转换为数组\r\n      return Object.values(groups);\r\n    },\r\n\r\n    selectedBatchAddHostsCount() {\r\n      return Object.values(this.batchAddAccountModal.selectedHosts).filter(Boolean).length\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchAddAccountModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host, account) {\r\n      this.currentHost = host\r\n      this.currentAccount = account\r\n      this.changePasswordModal.show = true\r\n      this.changePasswordModal.generatedPassword = this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword(policy) {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policyObj ? policyObj.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      if (this.changePasswordModal && !policy) {\r\n        this.changePasswordModal.generatedPassword = password\r\n      }\r\n\r\n      return password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto'\r\n          ? this.changePasswordModal.generatedPassword\r\n          : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          accountId: this.currentAccount.id,\r\n          password: password,\r\n          policyId: this.changePasswordModal.policyId\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的 ${this.currentAccount.username} 账号密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取所选策略\r\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取紧急策略\r\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    togglePasswordVisibility(hostId) {\r\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]\r\n    },\r\n\r\n    isPasswordExpired(account) {\r\n      if (!account.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }\r\n\r\n      // 解析过期时间\r\n      const expiryDate = new Date(account.passwordExpiryDate)\r\n      const now = new Date()\r\n\r\n      // 如果已过期\r\n      if (expiryDate < now) {\r\n        return {\r\n          status: 'expired',\r\n          days: 0,\r\n          text: '已过期'\r\n        }\r\n      }\r\n\r\n      // 计算剩余天数和小时数\r\n      const diffTime = expiryDate - now\r\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))\r\n      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n\r\n      // 根据剩余时间确定状态\r\n      let status = 'normal'\r\n      if (diffDays < 7) {\r\n        status = 'danger'  // 少于7天\r\n      } else if (diffDays < 14) {\r\n        status = 'warning' // 少于14天\r\n      }\r\n\r\n      // 格式化显示文本\r\n      let text = ''\r\n      if (diffDays > 0) {\r\n        text += `${diffDays}天`\r\n      }\r\n      if (diffHours > 0 || diffDays === 0) {\r\n        text += `${diffHours}小时`\r\n      }\r\n\r\n      return { status, days: diffDays, text: `剩余${text}` }\r\n    },\r\n\r\n    openAddAccountModal(host) {\r\n      this.currentHost = host\r\n      this.addAccountModal.show = true\r\n    },\r\n\r\n    async addAccount() {\r\n      if (!this.addAccountModal.username || !this.addAccountModal.password) {\r\n        alert('请填写完整的账号信息！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('addHostAccount', {\r\n          hostId: this.currentHost.id,\r\n          username: this.addAccountModal.username,\r\n          password: this.addAccountModal.password,\r\n          policyId: this.addAccountModal.policyId,\r\n          isDefault: this.addAccountModal.isDefault\r\n        })\r\n\r\n        this.addAccountModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为主机 ${this.currentHost.name} 添加账号！`)\r\n      } catch (error) {\r\n        console.error('添加账号失败', error)\r\n        alert('添加账号失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    generatePasswordForNewAccount() {\r\n      this.addAccountModal.password = this.generatePassword()\r\n    },\r\n\r\n    // 复制密码到剪贴板\r\n    copyPassword(account) {\r\n      // 创建一个临时输入框\r\n      const tempInput = document.createElement('input');\r\n      tempInput.value = account.password;\r\n      document.body.appendChild(tempInput);\r\n      tempInput.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(tempInput);\r\n\r\n      // 显示提示\r\n      alert(`已复制 ${account.host.name} 的 ${account.username} 账号密码到剪贴板！`);\r\n    },\r\n\r\n    generatePasswordForBatchAccount() {\r\n      this.batchAddAccountModal.password = this.generatePassword()\r\n    },\r\n\r\n    async batchAddAccounts() {\r\n      if (!this.batchAddAccountModal.username) {\r\n        alert('请填写账号名称！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('batchAddAccounts', {\r\n          hostIds: Object.keys(this.batchAddAccountModal.selectedHosts).map(id => parseInt(id)),\r\n          username: this.batchAddAccountModal.username,\r\n          password: this.batchAddAccountModal.password,\r\n          role: this.batchAddAccountModal.role,\r\n          isDefault: this.batchAddAccountModal.setAsDefault,\r\n          policyId: this.batchAddAccountModal.policyId\r\n        })\r\n\r\n        this.batchAddAccountModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为选中的 ${Object.keys(this.batchAddAccountModal.selectedHosts).length} 台主机添加账号！`)\r\n      } catch (error) {\r\n        console.error('批量添加账号失败', error)\r\n        alert('批量添加账号失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": ";;EAGSA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAA6C;;EACjDA,KAAK,EAAC;AAA6B;;EA2BnCA,KAAK,EAAC;AAA6B;;EAEjCA,KAAK,EAAC;AAAqD;;EAgB3DA,KAAK,EAAC;AAAU;;EAGdA,KAAK,EAAC;AAAsE;;EAM9EA,KAAK,EAAC;AAAU;;EAGdA,KAAK,EAAC;AAAsE;;EA9D7FC,GAAA;EA0FqCD,KAAK,EAAC;;;EAEhCA,KAAK,EAAC;AAAiE;;EACrEA,KAAK,EAAC;AAAuB;;EACvBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EAEzBA,KAAK,EAAC;AAAgB;;EAEvBA,KAAK,EAAC;AAAsN;;EAK5NA,KAAK,EAAC;AAAsN;;EAO3NA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAAY;;EAEjBE,KAAK,EAAC,KAAK;EAACF,KAAK,EAAC;;;EA4BnBA,KAAK,EAAC;AAAmC;;EAIxCA,KAAK,EAAC;AAAa;;EACjBG,OAAO,EAAC,GAAG;EAACH,KAAK,EAAC;;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAA2B;;EAOtCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EAEpBA,KAAK,EAAC;AAAgC;;EAI9CA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAmC;;EAxKjEC,GAAA;EA0KoBD,KAAK,EAAC;;;EAKRA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAlLrDC,GAAA;EA4LoBD,KAAK,EAAC;;;EA5L1BC,GAAA;EA+LoFD,KAAK,EAAC;;;EAKxEA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAW;oBAtMxC;oBAAA;;EAkNkBA,KAAK,EAAC;AAA6B;;EAGnCA,KAAK,EAAC;AAAmD;;EACtDA,KAAK,EAAC;AAAgB;oBAtN3C;oBAAA;;EAAAC,GAAA;AAAA;;EAyOgBE,OAAO,EAAC,GAAG;EAACH,KAAK,EAAC;;;EACfA,KAAK,EAAC;AAAe;;EAWxBA,KAAK,EAAC;AAAsD;;EAE/DA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAuC;;EAC3CA,KAAK,EAAC;AAAmB;;EAGtBA,KAAK,EAAC;AAAmC;;EAC1CA,KAAK,EAAC;AAAuB;;EAOjCA,KAAK,EAAC;AAAW;;EAGbA,KAAK,EAAC;AAAwC;;EAC5CA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAmC;;EA1QjEC,GAAA;EA4QoBD,KAAK,EAAC;;oBA5Q1B;;EAyRmBA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAmB;oBA3R9C;oBAAA;;EAuSmBA,KAAK,EAAC;AAAgC;;EAGlCA,KAAK,EAAC;AAAe;;EA1S5CC,GAAA;EAuTsBD,KAAK,EAAC;;;EAUbA,KAAK,EAAC;AAA0B;oBAjU/C;;EA+UWA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAiC;;EAOzCA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAgB;;EA1VnCC,GAAA;EA0WwDD,KAAK,EAAC;;oBA1W9D;;EAkXaA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAA2B;;EAQjCA,KAAK,EAAC;AAAmB;oBA3XxC;;EAAAC,GAAA;EAsYkBD,KAAK,EAAC;;;EACXA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;oBAzYxC;;EAmZaA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;oBArZxC;;EAAAC,GAAA;EA8ZuCD,KAAK,EAAC;;;EAMlCA,KAAK,EAAC;AAAgB;;EAgBtBA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAiC;;EAMzCA,KAAK,EAAC;AAAiB;;EAKvBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAA0D;;EAOlEA,KAAK,EAAC;AAAiB;oBA1clC;;EAmdWA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAA2B;;EAQjCA,KAAK,EAAC;AAAmB;qBA5dtC;;EA0eWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAY;qBAzf7B;;EAkgBWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EAzgB1CC,GAAA;EA+gBmED,KAAK,EAAC;;;EAC1DA,KAAK,EAAC;AAAwB;;EAalCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;qBAxjB7B;;EAikBWA,KAAK,EAAC;AAAY;;EAkBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;qBA7lB7B;;EAsmBWA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;EAUlBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAuC;;EAC3CA,KAAK,EAAC;AAAM;;EAMZA,KAAK,EAAC;AAAM;;EAUZA,KAAK,EAAC;AAAwB;;EAC1BA,KAAK,EAAC;AAA0B;;EAMpCA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAgB;;EAtqBvCC,GAAA;EAsrB2DD,KAAK,EAAC;;;EAEhDA,KAAK,EAAC;AAAmB;qBAxrB1C;;EA0sBWA,KAAK,EAAC;AAAiB;qBA1sBlC;;EAmtBWA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAW;;;;;;;uBAptB5BI,mBAAA,CA8tBM,cA7tBJC,mBAAA,UAAa,EACbC,mBAAA,CAmFM,OAnFNC,UAmFM,GAlFJD,mBAAA,CAiFM,OAjFNE,UAiFM,GAhFJF,mBAAA,CAyBM,OAzBNG,UAyBM,GAxBJH,mBAAA,CAKS;IAJPN,KAAK,EAAC,qNAAqN;IAC1NU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,kBAAA,IAAAD,QAAA,CAAAC,kBAAA,IAAAF,IAAA,CAAkB;MAC1BG,YAAA,CAA0EC,4BAAA;IAAtDC,IAAI,EAAE,+BAA+B;IAAEjB,KAAK,EAAC;kCACjEM,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAKS;IAJPN,KAAK,EAAC,wNAAwN;IAC7NU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAK,oBAAA,IAAAL,QAAA,CAAAK,oBAAA,IAAAN,IAAA,CAAoB;MAC5BG,YAAA,CAAyDC,4BAAA;IAArCC,IAAI,EAAE,cAAc;IAAEjB,KAAK,EAAC;kCAChDM,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAKS;IAJPN,KAAK,EAAC,8NAA8N;IACnOU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAM,mBAAA,IAAAN,QAAA,CAAAM,mBAAA,IAAAP,IAAA,CAAmB;MAC3BG,YAAA,CAAgEC,4BAAA;IAA5CC,IAAI,EAAE,qBAAqB;IAAEjB,KAAK,EAAC;kCACvDM,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAKS;IAJPN,KAAK,EAAC,2NAA2N;IAChOU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEQ,IAAA,CAAAC,wBAAA,IAAAD,IAAA,CAAAC,wBAAA,IAAAT,IAAA,CAAwB;MAChCG,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEjB,KAAK,EAAC;kCAClDM,mBAAA,CAAmB,cAAb,QAAM,qB,KAIhBA,mBAAA,CAoDM,OApDNgB,UAoDM,GAnDJjB,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNiB,UAaM,GAZJjB,mBAAA,CAKS;IALDN,KAAK,EAnCzBwB,eAAA,EAmC0B,8BAA8B;MAAA,0BACNC,KAAA,CAAAC,QAAQ;MAAA,6BAA2CD,KAAA,CAAAC,QAAQ;IAAA;IAC9FhB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAgB,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAChBX,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEjB,KAAK,EAAC;kCAtChE4B,gBAAA,CAsCyE,MAE7D,G,kBACAtB,mBAAA,CAKS;IALDN,KAAK,EAzCzBwB,eAAA,EAyC0B,8BAA8B;MAAA,0BACNC,KAAA,CAAAC,QAAQ;MAAA,6BAA0CD,KAAA,CAAAC,QAAQ;IAAA;IAC7FhB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAgB,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAChBX,YAAA,CAA8DC,4BAAA;IAA1CC,IAAI,EAAE,mBAAmB;IAAEjB,KAAK,EAAC;kCA5CnE4B,gBAAA,CA4C4E,MAEhE,G,oBAGFvB,mBAAA,QAAW,EACXC,mBAAA,CAMM,OANNuB,UAMM,G,gBALJvB,mBAAA,CAC2L;IADpLwB,IAAI,EAAC,MAAM;IAnD9B,uBAAAnB,MAAA,QAAAA,MAAA,MAAAgB,MAAA,IAmDwCF,KAAA,CAAAM,UAAU,GAAAJ,MAAA;IAAEK,WAAW,EAAC,SAAS;IAC3DhC,KAAK,EAAC;iDADoByB,KAAA,CAAAM,UAAU,E,GAEtCzB,mBAAA,CAEM,OAFN2B,UAEM,GADJlB,YAAA,CAAqEC,4BAAA;IAAjDC,IAAI,EAAE,iBAAiB;IAAEjB,KAAK,EAAC;UAIvDK,mBAAA,UAAa,EACbC,mBAAA,CAMM,OANN4B,UAMM,G,gBALJ5B,mBAAA,CAC2L;IADpLwB,IAAI,EAAC,MAAM;IA5D9B,uBAAAnB,MAAA,QAAAA,MAAA,MAAAgB,MAAA,IA4DwCF,KAAA,CAAAU,iBAAiB,GAAAR,MAAA;IAAEK,WAAW,EAAC,SAAS;IAClEhC,KAAK,EAAC;iDADoByB,KAAA,CAAAU,iBAAiB,E,GAE7C7B,mBAAA,CAEM,OAFN8B,UAEM,GADJrB,YAAA,CAAmEC,4BAAA;IAA/CC,IAAI,EAAE,eAAe;IAAEjB,KAAK,EAAC;UAIrDK,mBAAA,UAAa,E,gBACbC,mBAAA,CAMS;IA1EnB,uBAAAK,MAAA,QAAAA,MAAA,MAAAgB,MAAA,IAoE2BF,KAAA,CAAAY,YAAY,GAAAV,MAAA;IAC3B3B,KAAK,EAAC;kCACNM,mBAAA,CAAiC;IAAzBgC,KAAK,EAAC;EAAK,GAAC,MAAI,qBACxBhC,mBAAA,CAAkC;IAA1BgC,KAAK,EAAC;EAAQ,GAAC,IAAE,qBACzBhC,mBAAA,CAAmC;IAA3BgC,KAAK,EAAC;EAAS,GAAC,IAAE,qBAC1BhC,mBAAA,CAAiC;IAAzBgC,KAAK,EAAC;EAAO,GAAC,IAAE,oB,2CALTb,KAAA,CAAAY,YAAY,E,GAQ7BhC,mBAAA,cAAiB,E,gBACjBC,mBAAA,CAMS;IAnFnB,uBAAAK,MAAA,QAAAA,MAAA,MAAAgB,MAAA,IA6E2BF,KAAA,CAAAc,YAAY,GAAAZ,MAAA;IAC3B3B,KAAK,EAAC;kCACNM,mBAAA,CAAiC;IAAzBgC,KAAK,EAAC;EAAK,GAAC,MAAI,qBACxBhC,mBAAA,CAAoC;IAA5BgC,KAAK,EAAC;EAAS,GAAC,KAAG,qBAC3BhC,mBAAA,CAA2C;IAAnCgC,KAAK,EAAC;EAAe,GAAC,MAAI,qBAClChC,mBAAA,CAAmC;IAA3BgC,KAAK,EAAC;EAAO,GAAC,MAAI,oB,2CALXb,KAAA,CAAAc,YAAY,E,SAWnClC,mBAAA,UAAa,EACbA,mBAAA,UAAa,EACFoB,KAAA,CAAAC,QAAQ,gB,cAAnBtB,mBAAA,CAwJM,OAxJNoC,WAwJM,GAvJJnC,mBAAA,eAAkB,EAClBC,mBAAA,CAiBM,OAjBNmC,WAiBM,GAhBJnC,mBAAA,CAGM,OAHNoC,WAGM,G,4BAhGdd,gBAAA,CA6F2C,MAC9B,IAAAtB,mBAAA,CAA8D,QAA9DqC,WAA8D,EAAAC,gBAAA,CAAjC/B,QAAA,CAAAgC,gBAAgB,CAACC,MAAM,kB,4BA9FjElB,gBAAA,CA8F2E,UAC9D,IAAAtB,mBAAA,CAA4D,QAA5DyC,WAA4D,EAAAH,gBAAA,CAA/B/B,QAAA,CAAAmC,cAAc,CAACF,MAAM,kB,4BA/F/DlB,gBAAA,CA+FyE,MACjE,G,GACAtB,mBAAA,CAWM,OAXN2C,WAWM,GAVJ3C,mBAAA,CAIS,UAJT4C,WAIS,GAFPnC,YAAA,CAAiEC,4BAAA;IAA7CC,IAAI,EAAE,sBAAsB;IAAEjB,KAAK,EAAC;kCApGpE4B,gBAAA,CAoG6E,MAEnE,G,GACAtB,mBAAA,CAIS,UAJT6C,WAIS,GAFPpC,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEjB,KAAK,EAAC;kCAzG9D4B,gBAAA,CAyGuE,MAE7D,G,OAIJtB,mBAAA,CAkIQ,SAlIR8C,WAkIQ,GAjIN9C,mBAAA,CA6BQ,SA7BR+C,WA6BQ,GA5BN/C,mBAAA,CA2BK,aA1BHA,mBAAA,CAIK,MAJLgD,WAIK,GAHHvC,YAAA,CAEiBwC,yBAAA;IArH/BC,UAAA,EAmHuC/B,KAAA,CAAAgC,SAAS;IAnHhD,wB,sCAmHuChC,KAAA,CAAAgC,SAAS,GAAA9B,MAAA,GAAsBd,QAAA,CAAA6C,eAAe;;IAnHrFC,OAAA,EAAAC,QAAA,CAmHuF,MAEzEjD,MAAA,SAAAA,MAAA,QArHdiB,gBAAA,CAmHuF,OAEzE,E;IArHdiC,CAAA;0FAuHYvD,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,QAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,YAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,UAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,qB,KAGJM,mBAAA,CAkGQ,SAlGRwD,WAkGQ,GAjGNzD,mBAAA,aAAgB,G,kBAChBD,mBAAA,CAsFW2D,SAAA,QAtOrBC,WAAA,CAgJwCnD,QAAA,CAAAoD,eAAe,EAA5BC,SAAS;yBAhJpC9D,mBAAA,CAAA2D,SAAA;MAAA9D,GAAA,EAgJ+DiE,SAAS,CAACC;QAC7D9D,mBAAA,aAAgB,EAChBC,mBAAA,CAMK,MANL8D,WAMK,GALH9D,mBAAA,CAIK,MAJL+D,WAIK,GAHH/D,mBAAA,CAEM,OAFNgE,WAEM,GADJhE,mBAAA,CAA8F,OAA9FiE,WAA8F,EAAA3B,gBAAA,CAApDsB,SAAS,CAACM,QAAQ,IAAG,IAAE,GAAA5B,gBAAA,CAAGsB,SAAS,CAACO,MAAM,IAAG,GAAC,gB,OAI9FpE,mBAAA,SAAY,G,kBACZD,mBAAA,CA2EK2D,SAAA,QArOjBC,WAAA,CA0JkDE,SAAS,CAACQ,QAAQ,EA1JpE,CA0JwBC,OAAO,EAAEC,YAAY;2BAAjCxE,mBAAA,CA2EK;QA3EsDH,GAAG,EAAE0E,OAAO,CAACE,EAAE;QACvE7E,KAAK,EA3JpBwB,eAAA;UAAA,cA2JsCoD,YAAY;UAAA;QAAA;UACpCtE,mBAAA,CAMK,MANLwE,WAMK,GALHxE,mBAAA,CAIM,OAJNyE,WAIM,GAHJhE,YAAA,CAEiBwC,yBAAA;QAhKnCC,UAAA,EA8J2CmB,OAAO,CAACK,IAAI,CAACC,QAAQ;QA9JhE,uBAAAtD,MAAA,IA8J2CgD,OAAO,CAACK,IAAI,CAACC,QAAQ,GAAAtD,MAAA;QAAE3B,KAAK,EAAC;;QA9JxE2D,OAAA,EAAAC,QAAA,CA+JoB,MAA2E,CAA3EtD,mBAAA,CAA2E,QAA3E4E,WAA2E,EAAAtC,gBAAA,CAA3B+B,OAAO,CAACK,IAAI,CAACG,IAAI,iB;QA/JrFtB,CAAA;sFAmKcvD,mBAAA,CAEK,MAFL8E,WAEK,GADH9E,mBAAA,CAA8D,OAA9D+E,WAA8D,EAAAzC,gBAAA,CAAxB+B,OAAO,CAACK,IAAI,CAACM,EAAE,iB,GAEvDhF,mBAAA,CAQK,MARLiF,WAQK,GAPHjF,mBAAA,CAMM,OANNkF,WAMM,GALJlF,mBAAA,CAA6E,QAA7EmF,WAA6E,EAAA7C,gBAAA,CAA1B+B,OAAO,CAACe,QAAQ,kBACvDf,OAAO,CAACgB,SAAS,I,cAA7BvF,mBAAA,CAGO,QAHPwF,WAGO,EAFqG,MAE5G,KA5KlBvF,mBAAA,e,KA+KcC,mBAAA,CAEK,MAFLuF,WAEK,GADHvF,mBAAA,CAAgF,OAAhFwF,WAAgF,EAAAlD,gBAAA,CAA1C+B,OAAO,CAACoB,kBAAkB,wB,GAElEzF,mBAAA,CAiBK,MAjBL0F,WAiBK,GAhBH1F,mBAAA,CAeM;QAfAN,KAAK,EAnL3BwB,eAAA;;qCAmLiLX,QAAA,CAAAoF,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM,iBAAiBrF,QAAA,CAAAoF,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM;2CAAoErF,QAAA,CAAAoF,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM;uCAAgErF,QAAA,CAAAoF,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM;;UAnL1ctE,gBAAA,CAAAgB,gBAAA,CAyLqB/B,QAAA,CAAAoF,iBAAiB,CAACtB,OAAO,EAAEwB,IAAI,IAAG,GACrC,iBACQtF,QAAA,CAAAoF,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM,kBAAkBrF,QAAA,CAAAoF,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM,iB,cAD5F9F,mBAAA,CAIO,QAJPgG,WAIO,GADLrF,YAAA,CAA6DC,4BAAA;QAAzCC,IAAI,EAAE;MAA+B,G,KAE1CJ,QAAA,CAAAoF,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM,kB,cAAlD9F,mBAAA,CAEO,QAFPiG,WAEO,GADLtF,YAAA,CAA2DC,4BAAA;QAAvCC,IAAI,EAAE;MAA6B,G,KAhM3EZ,mBAAA,e,oBAoMcC,mBAAA,CAaK,MAbLgG,WAaK,GAZHhG,mBAAA,CAWM,OAXNiG,WAWM,GAVJjG,mBAAA,CAIM,OAJNkG,WAIM,GAHJlG,mBAAA,CAEkG;QAF1FwB,IAAI,EAAEL,KAAA,CAAAgF,kBAAkB,CAAC9B,OAAO,CAACE,EAAE;QAA0BvC,KAAK,EAAEqC,OAAO,CAAC+B,QAAQ;QAC1FC,QAAQ,EAAR,EAAQ;QACR3G,KAAK,EAAC;8BAzM5B4G,WAAA,E,GA2MkBtG,mBAAA,CAIS;QAJAI,OAAK,EAAAiB,MAAA,IAAEd,QAAA,CAAAgG,wBAAwB,CAAClC,OAAO,CAACE,EAAE;QACjD7E,KAAK,EAAC;UACNe,YAAA,CACoBC,4BAAA;QADAC,IAAI,UAAUQ,KAAA,CAAAgF,kBAAkB,CAAC9B,OAAO,CAACE,EAAE;QAC7D7E,KAAK,EAAC;yDA9M5B8G,WAAA,E,KAkNcxG,mBAAA,CAEK,MAFLyG,WAEK,GADHhG,YAAA,CAA2CiG,sBAAA;QAA7BlF,IAAI,EAAE6C,OAAO,CAACK,IAAI,CAACkB;2CAEnC5F,mBAAA,CAeK,MAfL2G,WAeK,GAdH3G,mBAAA,CAaM,OAbN4G,WAaM,GAZJ5G,mBAAA,CAKS;QAJPN,KAAK,EAAC,0NAA0N;QAC/NU,OAAK,EAAAiB,MAAA,IAAEd,QAAA,CAAAsG,uBAAuB,CAACxC,OAAO,CAACK,IAAI,EAAEL,OAAO;UACrD5D,YAAA,CAAyDC,4BAAA;QAArCC,IAAI,EAAE,cAAc;QAAEjB,KAAK,EAAC;sCA1NpE4B,gBAAA,CA0N6E,QAE3D,G,iBA5NlBwF,WAAA,GA6NkB9G,mBAAA,CAKS;QAJPN,KAAK,EAAC,sNAAsN;QAC3NU,OAAK,EAAAiB,MAAA,IAAEd,QAAA,CAAAwG,YAAY,CAAC1C,OAAO;UAC5B5D,YAAA,CAA0DC,4BAAA;QAAtCC,IAAI,EAAE,eAAe;QAAEjB,KAAK,EAAC;sCAhOrE4B,gBAAA,CAgO8E,MAE5D,G,iBAlOlB0F,WAAA,E;;kCAuOUjH,mBAAA,WAAc,EACJQ,QAAA,CAAAgC,gBAAgB,CAACC,MAAM,U,cAAjC1C,mBAAA,CAOK,MA/OfmH,WAAA,GAyOYjH,mBAAA,CAKK,MALLkH,WAKK,GAJHlH,mBAAA,CAGM,OAHNmH,WAGM,GAFJ1G,YAAA,CAAqEC,4BAAA;IAAjDC,IAAI,EAAE,iBAAiB;IAAEjB,KAAK,EAAC;kCACnDM,mBAAA,CAAkB,WAAf,aAAW,qB,SA5O9BD,mBAAA,e,wBAqPID,mBAAA,CAsFM2D,SAAA;IA3UV9D,GAAA;EAAA,IAoPII,mBAAA,UAAa,EACbC,mBAAA,CAsFM,OAtFNoH,WAsFM,I,kBArFJtH,mBAAA,CAoFM2D,SAAA,QA1UZC,WAAA,CAsP0BnD,QAAA,CAAA8G,aAAa,EAArB3C,IAAI;yBAAhB5E,mBAAA,CAoFM;MApF8BH,GAAG,EAAE+E,IAAI,CAACH,EAAE;MAAE7E,KAAK,EAAC;QACtDM,mBAAA,CAkFM,OAlFNsH,WAkFM,GAjFJvH,mBAAA,UAAa,EACbC,mBAAA,CASM,OATNuH,WASM,GARJvH,mBAAA,CAMM,OANNwH,WAMM,GALJ/G,YAAA,CAAuDwC,yBAAA;MA3PrEC,UAAA,EA2PuCwB,IAAI,CAACC,QAAQ;MA3PpD,uBAAAtD,MAAA,IA2PuCqD,IAAI,CAACC,QAAQ,GAAAtD,MAAA;MAAE3B,KAAK,EAAC;oEAC9CM,mBAAA,CAGM,cAFJA,mBAAA,CAAkE,MAAlEyH,WAAkE,EAAAnF,gBAAA,CAAjBoC,IAAI,CAACG,IAAI,kBAC1D7E,mBAAA,CAAkD,KAAlD0H,WAAkD,EAAApF,gBAAA,CAAdoC,IAAI,CAACM,EAAE,iB,KAG/CvE,YAAA,CAAmCiG,sBAAA;MAArBlF,IAAI,EAAEkD,IAAI,CAACkB;yCAG3B7F,mBAAA,UAAa,EACbC,mBAAA,CAyDM,OAzDN2H,WAyDM,I,kBAxDJ7H,mBAAA,CAuDM2D,SAAA,QA7TlBC,WAAA,CAsQmCgB,IAAI,CAACN,QAAQ,EAAxBC,OAAO;2BAAnBvE,mBAAA,CAuDM;QAvDiCH,GAAG,EAAE0E,OAAO,CAACE,EAAE;QAAE7E,KAAK,EAtQzEwB,eAAA,EAsQ0E,uCAAuC;UAAA,gCACzDmD,OAAO,CAACgB;QAAS;UAC3DrF,mBAAA,CAcM,OAdN4H,WAcM,GAbJ5H,mBAAA,CAMM,OANN6H,WAMM,GALJ7H,mBAAA,CAA6E,QAA7E8H,WAA6E,EAAAxF,gBAAA,CAA1B+B,OAAO,CAACe,QAAQ,kBACvDf,OAAO,CAACgB,SAAS,I,cAA7BvF,mBAAA,CAGO,QAHPiI,WAGO,EAFqG,MAE5G,KA9QlBhI,mBAAA,e,GAgRgBC,mBAAA,CAKS;QAJPN,KAAK,EAAC,wNAAwN;QAC7NU,OAAK,EAAAiB,MAAA,IAAEd,QAAA,CAAAsG,uBAAuB,CAACnC,IAAI,EAAEL,OAAO;UAC7C5D,YAAA,CAAyDC,4BAAA;QAArCC,IAAI,EAAE,cAAc;QAAEjB,KAAK,EAAC;sCAnRlE4B,gBAAA,CAmR2E,QAE3D,G,iBArRhB0G,WAAA,E,GAwRcjI,mBAAA,UAAa,EACbC,mBAAA,CAWM,OAXNiI,WAWM,G,4BAVJjI,mBAAA,CAA4D;QAAvDN,KAAK,EAAC;MAAwC,GAAC,IAAE,sBACtDM,mBAAA,CAQM,OARNkI,WAQM,GAPJlI,mBAAA,CACkG;QAD1FwB,IAAI,EAAEL,KAAA,CAAAgF,kBAAkB,CAAC9B,OAAO,CAACE,EAAE;QAA0BvC,KAAK,EAAEqC,OAAO,CAAC+B,QAAQ;QAAEC,QAAQ,EAAR,EAAQ;QACpG3G,KAAK,EAAC;8BA7R1ByI,WAAA,GA8RkBnI,mBAAA,CAIS;QAJAI,OAAK,EAAAiB,MAAA,IAAEd,QAAA,CAAAgG,wBAAwB,CAAClC,OAAO,CAACE,EAAE;QACjD7E,KAAK,EAAC;UACNe,YAAA,CACoBC,4BAAA;QADAC,IAAI,UAAUQ,KAAA,CAAAgF,kBAAkB,CAAC9B,OAAO,CAACE,EAAE;QAC7D7E,KAAK,EAAC;yDAjS5B0I,WAAA,E,KAsScrI,mBAAA,YAAe,EACfC,mBAAA,CAqBM,OArBNqI,WAqBM,GApBJrI,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAwD;QAAnDN,KAAK,EAAC;MAAgC,GAAC,QAAM,sBAClDM,mBAAA,CAAwE,OAAxEsI,WAAwE,EAAAhG,gBAAA,CAA1C+B,OAAO,CAACoB,kBAAkB,wB,GAE1DzF,mBAAA,CAeM,c,4BAdJA,mBAAA,CAAsD;QAAjDN,KAAK,EAAC;MAAgC,GAAC,MAAI,sBAChDM,mBAAA,CAYM;QAZAN,KAAK,EA9S7BwB,eAAA;;qCA8SqLX,QAAA,CAAAoF,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM,iBAAiBrF,QAAA,CAAAoF,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM;2CAAsErF,QAAA,CAAAoF,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM;uCAAkErF,QAAA,CAAAoF,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM;;UA9SldtE,gBAAA,CAAAgB,gBAAA,CAoTuB/B,QAAA,CAAAoF,iBAAiB,CAACtB,OAAO,EAAEwB,IAAI,IAAG,GACrC,iBACQtF,QAAA,CAAAoF,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM,kBAAkBrF,QAAA,CAAAoF,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM,iB,cAD5F9F,mBAAA,CAIO,QAJPyI,WAIO,GADL9H,YAAA,CAA6DC,4BAAA;QAAzCC,IAAI,EAAE;MAA+B,G,KAxT/EZ,mBAAA,e;sCAgUUA,mBAAA,YAAe,EACfC,mBAAA,CAOM,OAPNwI,WAOM,GANJxI,mBAAA,CAKS;MAJPN,KAAK,EAAC,sNAAsN;MAC3NU,OAAK,EAAAiB,MAAA,IAAEd,QAAA,CAAAkI,mBAAmB,CAAC/D,IAAI;QAChCjE,YAAA,CAA0DC,4BAAA;MAAtCC,IAAI,EAAE,eAAe;MAAEjB,KAAK,EAAC;oCArU/D4B,gBAAA,CAqUwE,QAE5D,G,iBAvUZoH,WAAA,E;sFA6UI3I,mBAAA,YAAe,EACfU,YAAA,CAkGYkI,oBAAA;IAhbhBzF,UAAA,EA8UwB/B,KAAA,CAAAyH,mBAAmB,CAACC,IAAI;IA9UhD,uBAAAxI,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA8UwBF,KAAA,CAAAyH,mBAAmB,CAACC,IAAI,GAAAxH,MAAA;IAAEyH,KAAK,EAAC,MAAM;IAAEC,SAAO,EAAExI,QAAA,CAAAyI,cAAc;IAAGC,OAAO,EAAE9H,KAAA,CAAA+H;;IA9UnG7F,OAAA,EAAAC,QAAA,CA+UM,MAOM,CAPNtD,mBAAA,CAOM,OAPNmJ,WAOM,G,4BANJnJ,mBAAA,CAAwC;MAAnCN,KAAK,EAAC;IAAkB,GAAC,MAAI,sBAClCM,mBAAA,CAIM,OAJNoJ,WAIM,GAHJpJ,mBAAA,CAAuE,c,4BAAlEA,mBAAA,CAAqC;MAA/BN,KAAK,EAAC;IAAa,GAAC,MAAI,sBAlV7C4B,gBAAA,CAkVoD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAkI,WAAW,CAACxE,IAAI,iB,GAC9D7E,mBAAA,CAAsE,c,4BAAjEA,mBAAA,CAAsC;MAAhCN,KAAK,EAAC;IAAa,GAAC,OAAK,sBAnV9C4B,gBAAA,CAmVqD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAkI,WAAW,CAACrE,EAAE,iB,GAC7DhF,mBAAA,CAA6E,c,4BAAxEA,mBAAA,CAAoC;MAA9BN,KAAK,EAAC;IAAa,GAAC,KAAG,sBApV5C4B,gBAAA,CAoVmD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAmI,cAAc,CAAClE,QAAQ,iB,OAIxEpF,mBAAA,CAgBM,OAhBNuJ,WAgBM,G,8BAfJvJ,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAaM,OAbNwJ,WAaM,GAZJxJ,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEF,KAAA,CAAAyH,mBAAmB,CAACa,MAAM;MACxC/J,KAAK,EA5VjBwB,eAAA,EA4VkB,iFAAiF,EAC/EC,KAAA,CAAAyH,mBAAmB,CAACa,MAAM;QAClChJ,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;oCA9VjE4B,gBAAA,CA8V0E,QAEhE,G,kBACAtB,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEF,KAAA,CAAAyH,mBAAmB,CAACa,MAAM;MACxC/J,KAAK,EAlWjBwB,eAAA,EAkWkB,iFAAiF,EAC/EC,KAAA,CAAAyH,mBAAmB,CAACa,MAAM;QAClChJ,YAAA,CAA0DC,4BAAA;MAAtCC,IAAI,EAAE,eAAe;MAAEjB,KAAK,EAAC;sCApW7D4B,gBAAA,CAoWsE,QAE5D,G,sBAIOH,KAAA,CAAAyH,mBAAmB,CAACa,MAAM,e,cAArC3J,mBAAA,CA0BM,OA1BN4J,WA0BM,G,8BAzBJ1J,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MAhXjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA4WyBF,KAAA,CAAAyH,mBAAmB,CAACe,QAAQ,GAAAtI,MAAA;MAAE3B,KAAK,EAAC,aAAa;MAAEkK,QAAM,EAAAvJ,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEd,QAAA,CAAAsJ,gBAAgB;2BAC1F/J,mBAAA,CAES2D,SAAA,QA/WnBC,WAAA,CA6WmC5C,IAAA,CAAAgJ,QAAQ,EAAlBC,MAAM;2BAArBjK,mBAAA,CAES;QAF2BH,GAAG,EAAEoK,MAAM,CAACxF,EAAE;QAAGvC,KAAK,EAAE+H,MAAM,CAACxF;0BAC9DwF,MAAM,CAAClF,IAAI,IAAG,UAAQ,GAAAvC,gBAAA,CAAGyH,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA1H,gBAAA,CAAGyH,MAAM,CAACE,UAAU,IAAG,KAC9E,uBA/WVC,WAAA;6FA4WyB/I,KAAA,CAAAyH,mBAAmB,CAACe,QAAQ,E,GAM7C3J,mBAAA,CAiBM,OAjBNmK,WAiBM,GAhBJnK,mBAAA,CAOM,OAPNoK,WAOM,G,8BANJpK,mBAAA,CAAuC;MAAhCN,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BM,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEd,QAAA,CAAAsJ,gBAAgB;MAAIrI,IAAI,EAAC,QAAQ;MAC/C9B,KAAK,EAAC;QACNe,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;sCAvXnE4B,gBAAA,CAuX4E,QAEhE,G,KAEFtB,mBAAA,CAOM,OAPNqK,WAOM,G,gBANJrK,mBAAA,CACoG;MAD5FwB,IAAI,EAAEL,KAAA,CAAAgF,kBAAkB,CAACmE,SAAS;MA5XtD,uBAAAjK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA6XuBF,KAAA,CAAAyH,mBAAmB,CAAC2B,iBAAiB,GAAAlJ,MAAA;MAAEgF,QAAQ,EAAR,EAAQ;MAAC3G,KAAK,EAAC;4BA7X7E8K,WAAA,I,iBA6XuBrJ,KAAA,CAAAyH,mBAAmB,CAAC2B,iBAAiB,E,GAChDvK,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEF,KAAA,CAAAgF,kBAAkB,CAACmE,SAAS,IAAInJ,KAAA,CAAAgF,kBAAkB,CAACmE,SAAS;MAAE9I,IAAI,EAAC,QAAQ;MACzF9B,KAAK,EAAC;QACNe,YAAA,CAAyGC,4BAAA;MAArFC,IAAI,UAAUQ,KAAA,CAAAgF,kBAAkB,CAACmE,SAAS;MAAyB5K,KAAK,EAAC;gEAMrGI,mBAAA,CA4BM,OA5BN2K,WA4BM,GA3BJzK,mBAAA,CAUM,OAVN0K,WAUM,G,8BATJ1K,mBAAA,CAAqC;MAA9BN,KAAK,EAAC;IAAY,GAAC,KAAG,sBAC7BM,mBAAA,CAOM,OAPN2K,WAOM,G,gBANJ3K,mBAAA,CACoD;MAD5CwB,IAAI,EAAEL,KAAA,CAAAgF,kBAAkB,CAACyE,GAAG;MA1YhD,uBAAAvK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA0YiFF,KAAA,CAAAyH,mBAAmB,CAACiC,WAAW,GAAAxJ,MAAA;MAClG3B,KAAK,EAAC,qBAAqB;MAACgC,WAAW,EAAC;4BA3YtDoJ,WAAA,I,iBA0YiF3J,KAAA,CAAAyH,mBAAmB,CAACiC,WAAW,E,GAEpG7K,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEF,KAAA,CAAAgF,kBAAkB,CAACyE,GAAG,IAAIzJ,KAAA,CAAAgF,kBAAkB,CAACyE,GAAG;MAAEpJ,IAAI,EAAC,QAAQ;MAC7E9B,KAAK,EAAC;QACNe,YAAA,CAAmGC,4BAAA;MAA/EC,IAAI,UAAUQ,KAAA,CAAAgF,kBAAkB,CAACyE,GAAG;MAAyBlL,KAAK,EAAC;6CAK7FM,mBAAA,CAYM,OAZN+K,WAYM,G,8BAXJ/K,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CAQM,OARNgL,WAQM,G,gBAPJhL,mBAAA,CAE0E;MAFlEwB,IAAI,EAAEL,KAAA,CAAAgF,kBAAkB,CAAC8E,OAAO;MAtZpD,uBAAA5K,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAuZuBF,KAAA,CAAAyH,mBAAmB,CAACsC,eAAe,GAAA7J,MAAA;MAAE3B,KAAK,EAvZjEwB,eAAA,EAuZkE,qBAAqB;QAAA,kBAC7CX,QAAA,CAAA4K;MAAgB;MAAIzJ,WAAW,EAAC;oCAxZ1E0J,WAAA,I,iBAuZuBjK,KAAA,CAAAyH,mBAAmB,CAACsC,eAAe,E,GAE9ClL,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEF,KAAA,CAAAgF,kBAAkB,CAAC8E,OAAO,IAAI9J,KAAA,CAAAgF,kBAAkB,CAAC8E,OAAO;MAAEzJ,IAAI,EAAC,QAAQ;MACrF9B,KAAK,EAAC;QACNe,YAAA,CAAuGC,4BAAA;MAAnFC,IAAI,UAAUQ,KAAA,CAAAgF,kBAAkB,CAAC8E,OAAO;MAAyBvL,KAAK,EAAC;2CAGpFa,QAAA,CAAA4K,gBAAgB,I,cAA3BrL,mBAAA,CAA+E,OAA/EuL,WAA+E,EAAhB,YAAU,KA9ZnFtL,mBAAA,e,GAiaQU,YAAA,CAAqE6K,gCAAA;MAA7ClF,QAAQ,EAAEjF,KAAA,CAAAyH,mBAAmB,CAACiC;8CAGxD7K,mBAAA,CAWM,OAXNuL,WAWM,G,8BAVJvL,mBAAA,CAA8C;MAAzCN,KAAK,EAAC;IAAwB,GAAC,MAAI,sBACxCe,YAAA,CAEiBwC,yBAAA;MAxazBC,UAAA,EAsaiC/B,KAAA,CAAAyH,mBAAmB,CAAC4C,kBAAkB;MAtavE,uBAAAnL,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAsaiCF,KAAA,CAAAyH,mBAAmB,CAAC4C,kBAAkB,GAAAnK,MAAA;;MAtavEgC,OAAA,EAAAC,QAAA,CAuaU,MAA8BjD,MAAA,UAAAA,MAAA,SAA9BL,mBAAA,CAA8B;QAAxBN,KAAK,EAAC;MAAM,GAAC,MAAI,oB;MAvajC6D,CAAA;uCAyaQ9C,YAAA,CAEiBwC,yBAAA;MA3azBC,UAAA,EAyaiC/B,KAAA,CAAAyH,mBAAmB,CAAC6C,WAAW;MAzahE,uBAAApL,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAyaiCF,KAAA,CAAAyH,mBAAmB,CAAC6C,WAAW,GAAApK,MAAA;;MAzahEgC,OAAA,EAAAC,QAAA,CA0aU,MAAgCjD,MAAA,UAAAA,MAAA,SAAhCL,mBAAA,CAAgC;QAA1BN,KAAK,EAAC;MAAM,GAAC,QAAM,oB;MA1anC6D,CAAA;uCA4aQ9C,YAAA,CAEiBwC,yBAAA;MA9azBC,UAAA,EA4aiC/B,KAAA,CAAAyH,mBAAmB,CAAC8C,QAAQ;MA5a7D,uBAAArL,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA4aiCF,KAAA,CAAAyH,mBAAmB,CAAC8C,QAAQ,GAAArK,MAAA;;MA5a7DgC,OAAA,EAAAC,QAAA,CA6aU,MAAgCjD,MAAA,UAAAA,MAAA,SAAhCL,mBAAA,CAAgC;QAA1BN,KAAK,EAAC;MAAM,GAAC,QAAM,oB;MA7anC6D,CAAA;;IAAAA,CAAA;6DAkbIxD,mBAAA,YAAe,EACfU,YAAA,CAkDYkI,oBAAA;IArehBzF,UAAA,EAmbwB/B,KAAA,CAAAwK,eAAe,CAAC9C,IAAI;IAnb5C,uBAAAxI,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAmbwBF,KAAA,CAAAwK,eAAe,CAAC9C,IAAI,GAAAxH,MAAA;IAAEyH,KAAK,EAAC,MAAM;IAAEC,SAAO,EAAExI,QAAA,CAAAqL,UAAU;IAAG3C,OAAO,EAAE9H,KAAA,CAAA+H;;IAnb3F7F,OAAA,EAAAC,QAAA,CAobM,MAMM,CANNtD,mBAAA,CAMM,OANN6L,WAMM,G,8BALJ7L,mBAAA,CAAwC;MAAnCN,KAAK,EAAC;IAAkB,GAAC,MAAI,sBAClCM,mBAAA,CAGM,OAHN8L,WAGM,GAFJ9L,mBAAA,CAAuE,c,8BAAlEA,mBAAA,CAAqC;MAA/BN,KAAK,EAAC;IAAa,GAAC,MAAI,sBAvb7C4B,gBAAA,CAuboD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAkI,WAAW,CAACxE,IAAI,iB,GAC9D7E,mBAAA,CAAsE,c,8BAAjEA,mBAAA,CAAsC;MAAhCN,KAAK,EAAC;IAAa,GAAC,OAAK,sBAxb9C4B,gBAAA,CAwbqD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAkI,WAAW,CAACrE,EAAE,iB,OAIjEhF,mBAAA,CAGM,OAHN+L,WAGM,G,8BAFJ/L,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAAkG;MAA3FwB,IAAI,EAAC,MAAM;MA9b1B,uBAAAnB,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA8boCF,KAAA,CAAAwK,eAAe,CAACvG,QAAQ,GAAA/D,MAAA;MAAE3B,KAAK,EAAC,cAAc;MAACgC,WAAW,EAAC;mDAA3DP,KAAA,CAAAwK,eAAe,CAACvG,QAAQ,E,KAGtDpF,mBAAA,CAOM,OAPNgM,WAOM,G,8BANJhM,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAIM,OAJNiM,WAIM,G,gBAHJjM,mBAAA,CACoI;MAD7HwB,IAAI,EAAC,UAAU;MApchC,uBAAAnB,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAoc0CF,KAAA,CAAAwK,eAAe,CAACtG,SAAS,GAAAhE,MAAA;MACvD3B,KAAK,EAAC;uDADwByB,KAAA,CAAAwK,eAAe,CAACtG,SAAS,E,iCAEzDrF,mBAAA,CAAsG;MAA/FN,KAAK,EAAC;IAAgF,4B,KAIjGM,mBAAA,CAOM,OAPNkM,WAOM,G,8BANJlM,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MAhdjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA4cyBF,KAAA,CAAAwK,eAAe,CAAChC,QAAQ,GAAAtI,MAAA;MAAE3B,KAAK,EAAC,aAAa;MAAEkK,QAAM,EAAAvJ,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEd,QAAA,CAAA4L,6BAA6B;2BACnGrM,mBAAA,CAES2D,SAAA,QA/cnBC,WAAA,CA6cmC5C,IAAA,CAAAgJ,QAAQ,EAAlBC,MAAM;2BAArBjK,mBAAA,CAES;QAF2BH,GAAG,EAAEoK,MAAM,CAACxF,EAAE;QAAGvC,KAAK,EAAE+H,MAAM,CAACxF;0BAC9DwF,MAAM,CAAClF,IAAI,IAAG,UAAQ,GAAAvC,gBAAA,CAAGyH,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA1H,gBAAA,CAAGyH,MAAM,CAACE,UAAU,IAAG,KAC9E,uBA/cVmC,WAAA;6FA4cyBjL,KAAA,CAAAwK,eAAe,CAAChC,QAAQ,E,KAO3C3J,mBAAA,CAiBM,OAjBNqM,YAiBM,GAhBJrM,mBAAA,CAOM,OAPNsM,YAOM,G,8BANJtM,mBAAA,CAAuC;MAAhCN,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BM,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEd,QAAA,CAAA4L,6BAA6B;MAAI3K,IAAI,EAAC,QAAQ;MAC5D9B,KAAK,EAAC;QACNe,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;sCAxdjE4B,gBAAA,CAwd0E,QAEhE,G,KAEFtB,mBAAA,CAOM,OAPNuM,YAOM,G,gBANJvM,mBAAA,CAC2C;MADnCwB,IAAI,EAAEL,KAAA,CAAAgF,kBAAkB,CAACqG,UAAU;MA7drD,uBAAAnM,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA6dsFF,KAAA,CAAAwK,eAAe,CAACvF,QAAQ,GAAA/E,MAAA;MAAEgF,QAAQ,EAAR,EAAQ;MAC5G3G,KAAK,EAAC;4BA9dlB+M,YAAA,I,iBA6dsFtL,KAAA,CAAAwK,eAAe,CAACvF,QAAQ,E,GAEpGpG,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEF,KAAA,CAAAgF,kBAAkB,CAACqG,UAAU,IAAIrL,KAAA,CAAAgF,kBAAkB,CAACqG,UAAU;MAAEhL,IAAI,EAAC,QAAQ;MAC3F9B,KAAK,EAAC;QACNe,YAAA,CAA0GC,4BAAA;MAAtFC,IAAI,UAAUQ,KAAA,CAAAgF,kBAAkB,CAACqG,UAAU;MAAyB9M,KAAK,EAAC;;IAje1G6D,CAAA;6DAueIxD,mBAAA,cAAiB,EACjBU,YAAA,CAiEYkI,oBAAA;IAziBhBzF,UAAA,EAwewB/B,KAAA,CAAAuL,gBAAgB,CAAC7D,IAAI;IAxe7C,uBAAAxI,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAwewBF,KAAA,CAAAuL,gBAAgB,CAAC7D,IAAI,GAAAxH,MAAA;IAAEyH,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAAC6D,IAAI,EAAC,IAAI;IACpF5D,SAAO,EAAExI,QAAA,CAAAqM,oBAAoB;IAAG3D,OAAO,EAAE9H,KAAA,CAAA+H;;IAzehD7F,OAAA,EAAAC,QAAA,CA0eM,MAaM,CAbNtD,mBAAA,CAaM,OAbN6M,YAaM,G,8BAZJ7M,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAIM,OAJN8M,YAIM,GAHJrM,YAAA,CAEiBwC,yBAAA;MA/e3BC,UAAA,EA6emC/B,KAAA,CAAA4L,cAAc;MA7ejD,wB,sCA6emC5L,KAAA,CAAA4L,cAAc,GAAA1L,MAAA,GAAsBd,QAAA,CAAAyM,oBAAoB;;MA7e3F3J,OAAA,EAAAC,QAAA,CA6e6F,MAEnFjD,MAAA,UAAAA,MAAA,SA/eViB,gBAAA,CA6e6F,MAEnF,E;MA/eViC,CAAA;gEAifQvD,mBAAA,CAIM,OAJNiN,YAIM,I,kBAHJnN,mBAAA,CAEiB2D,SAAA,QApf3BC,WAAA,CAkfyC5C,IAAA,CAAAoM,KAAK,EAAbxI,IAAI;2BAA3ByI,YAAA,CAEiBlK,yBAAA;QAFsBtD,GAAG,EAAE+E,IAAI,CAACH,EAAE;QAlf7DrB,UAAA,EAkfwE/B,KAAA,CAAAuL,gBAAgB,CAACU,aAAa,CAAC1I,IAAI,CAACH,EAAE;QAlf9G,uBAAAlD,MAAA,IAkfwEF,KAAA,CAAAuL,gBAAgB,CAACU,aAAa,CAAC1I,IAAI,CAACH,EAAE,IAAAlD;;QAlf9GgC,OAAA,EAAAC,QAAA,CAmfY,MAAe,CAnf3BhC,gBAAA,CAAAgB,gBAAA,CAmfeoC,IAAI,CAACG,IAAI,IAAG,IAAE,GAAAvC,gBAAA,CAAGoC,IAAI,CAACM,EAAE,IAAG,IAChC,gB;QApfVzB,CAAA;;sCAsfQvD,mBAAA,CAAyD,KAAzDqN,YAAyD,EAApC,MAAI,GAAA/K,gBAAA,CAAG/B,QAAA,CAAA+M,kBAAkB,IAAG,MAAI,gB,GAGvDtN,mBAAA,CAOM,OAPNuN,YAOM,G,8BANJvN,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MA/fjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA2fyBF,KAAA,CAAAuL,gBAAgB,CAAC/C,QAAQ,GAAAtI,MAAA;MAAE3B,KAAK,EAAC;2BAChDI,mBAAA,CAES2D,SAAA,QA9fnBC,WAAA,CA4fmC5C,IAAA,CAAAgJ,QAAQ,EAAlBC,MAAM;2BAArBjK,mBAAA,CAES;QAF2BH,GAAG,EAAEoK,MAAM,CAACxF,EAAE;QAAGvC,KAAK,EAAE+H,MAAM,CAACxF;0BAC9DwF,MAAM,CAAClF,IAAI,IAAG,UAAQ,GAAAvC,gBAAA,CAAGyH,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA1H,gBAAA,CAAGyH,MAAM,CAACE,UAAU,IAAG,KAC9E,uBA9fVuD,YAAA;6EA2fyBrM,KAAA,CAAAuL,gBAAgB,CAAC/C,QAAQ,E,KAO5C3J,mBAAA,CAyBM,OAzBNyN,YAyBM,G,8BAxBJzN,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CASM,OATN0N,YASM,GARJ1N,mBAAA,CAGQ,SAHR2N,YAGQ,G,gBAFN3N,mBAAA,CAA4F;MAArFwB,IAAI,EAAC,OAAO;MAtgB/B,uBAAAnB,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAsgByCF,KAAA,CAAAuL,gBAAgB,CAACkB,aAAa,GAAAvM,MAAA;MAAEW,KAAK,EAAC,WAAW;MAACtC,KAAK,EAAC;oDAAxDyB,KAAA,CAAAuL,gBAAgB,CAACkB,aAAa,E,iCAC3D5N,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHR6N,YAGQ,G,gBAFN7N,mBAAA,CAA4F;MAArFwB,IAAI,EAAC,OAAO;MA1gB/B,uBAAAnB,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA0gByCF,KAAA,CAAAuL,gBAAgB,CAACkB,aAAa,GAAAvM,MAAA;MAAEW,KAAK,EAAC,WAAW;MAACtC,KAAK,EAAC;oDAAxDyB,KAAA,CAAAuL,gBAAgB,CAACkB,aAAa,E,iCAC3D5N,mBAAA,CAAiB,cAAX,MAAI,qB,KAIHmB,KAAA,CAAAuL,gBAAgB,CAACkB,aAAa,oB,cAAzC9N,mBAAA,CAWM,OAXNgO,YAWM,GAVJ9N,mBAAA,CASM,OATN+N,YASM,GARJ/N,mBAAA,CAGM,c,8BAFJA,mBAAA,CAAoC;MAA7BN,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BM,mBAAA,CAAiF;MAA1EwB,IAAI,EAAC,MAAM;MAnhBhC,uBAAAnB,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAmhB0CF,KAAA,CAAAuL,gBAAgB,CAACsB,aAAa,GAAA3M,MAAA;MAAE3B,KAAK,EAAC;mDAAtCyB,KAAA,CAAAuL,gBAAgB,CAACsB,aAAa,E,KAE5DhO,mBAAA,CAGM,c,8BAFJA,mBAAA,CAAoC;MAA7BN,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BM,mBAAA,CAAiF;MAA1EwB,IAAI,EAAC,MAAM;MAvhBhC,uBAAAnB,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAuhB0CF,KAAA,CAAAuL,gBAAgB,CAACuB,aAAa,GAAA5M,MAAA;MAAE3B,KAAK,EAAC;mDAAtCyB,KAAA,CAAAuL,gBAAgB,CAACuB,aAAa,E,WAvhBxElO,mBAAA,e,GA6hBMC,mBAAA,CAWM,OAXNkO,YAWM,G,8BAVJlO,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Be,YAAA,CAEiBwC,yBAAA;MAjiBzBC,UAAA,EA+hBiC/B,KAAA,CAAAuL,gBAAgB,CAACyB,YAAY;MA/hB9D,uBAAA9N,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA+hBiCF,KAAA,CAAAuL,gBAAgB,CAACyB,YAAY,GAAA9M,MAAA;;MA/hB9DgC,OAAA,EAAAC,QAAA,CA+hBgE,MAExDjD,MAAA,UAAAA,MAAA,SAjiBRiB,gBAAA,CA+hBgE,YAExD,E;MAjiBRiC,CAAA;uCAkiBQ9C,YAAA,CAEiBwC,yBAAA;MApiBzBC,UAAA,EAkiBiC/B,KAAA,CAAAuL,gBAAgB,CAAC0B,WAAW;MAliB7D,uBAAA/N,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAkiBiCF,KAAA,CAAAuL,gBAAgB,CAAC0B,WAAW,GAAA/M,MAAA;;MAliB7DgC,OAAA,EAAAC,QAAA,CAkiB+D,MAEvDjD,MAAA,UAAAA,MAAA,SApiBRiB,gBAAA,CAkiB+D,UAEvD,E;MApiBRiC,CAAA;uCAqiBQ9C,YAAA,CAEiBwC,yBAAA;MAviBzBC,UAAA,EAqiBiC/B,KAAA,CAAAuL,gBAAgB,CAAC2B,gBAAgB;MAriBlE,uBAAAhO,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAqiBiCF,KAAA,CAAAuL,gBAAgB,CAAC2B,gBAAgB,GAAAhN,MAAA;;MAriBlEgC,OAAA,EAAAC,QAAA,CAqiBoE,MAE5DjD,MAAA,UAAAA,MAAA,SAviBRiB,gBAAA,CAqiBoE,aAE5D,E;MAviBRiC,CAAA;;IAAAA,CAAA;6DA2iBIxD,mBAAA,cAAiB,EACjBU,YAAA,CA8BYkI,oBAAA;IA1kBhBzF,UAAA,EA4iBwB/B,KAAA,CAAAmN,eAAe,CAACzF,IAAI;IA5iB5C,uBAAAxI,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA4iBwBF,KAAA,CAAAmN,eAAe,CAACzF,IAAI,GAAAxH,MAAA;IAAEyH,KAAK,EAAC,UAAU;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAExI,QAAA,CAAAgO,gBAAgB;IACtGtF,OAAO,EAAE9H,KAAA,CAAA+H;;IA7iBhB7F,OAAA,EAAAC,QAAA,CA8iBM,MAQM,CARNtD,mBAAA,CAQM,OARNwO,YAQM,G,8BAPJxO,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAKM,OALNyO,YAKM,I,kBAJJ3O,mBAAA,CAGiB2D,SAAA,QApjB3BC,WAAA,CAijByCnD,QAAA,CAAAmO,iBAAiB,EAAzBhK,IAAI;2BAA3ByI,YAAA,CAGiBlK,yBAAA;QAHkCtD,GAAG,EAAE+E,IAAI,CAACH,EAAE;QAjjBzErB,UAAA,EAkjBqB/B,KAAA,CAAAmN,eAAe,CAAClB,aAAa,CAAC1I,IAAI,CAACH,EAAE;QAljB1D,uBAAAlD,MAAA,IAkjBqBF,KAAA,CAAAmN,eAAe,CAAClB,aAAa,CAAC1I,IAAI,CAACH,EAAE,IAAAlD;;QAljB1DgC,OAAA,EAAAC,QAAA,CAmjBY,MAAe,CAnjB3BhC,gBAAA,CAAAgB,gBAAA,CAmjBeoC,IAAI,CAACG,IAAI,IAAG,IAAE,GAAAvC,gBAAA,CAAGoC,IAAI,CAACM,EAAE,IAAG,IAChC,gB;QApjBVzB,CAAA;;wCAwjBMvD,mBAAA,CAOM,OAPN2O,YAOM,G,8BANJ3O,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCM,mBAAA,CAIS;MA9jBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA0jByBF,KAAA,CAAAmN,eAAe,CAAC3E,QAAQ,GAAAtI,MAAA;MAAE3B,KAAK,EAAC;2BAC/CI,mBAAA,CAES2D,SAAA,QA7jBnBC,WAAA,CA2jBmC5C,IAAA,CAAAgJ,QAAQ,EAAlBC,MAAM;2BAArBjK,mBAAA,CAES;QAF2BH,GAAG,EAAEoK,MAAM,CAACxF,EAAE;QAAGvC,KAAK,EAAE+H,MAAM,CAACxF;0BAC9DwF,MAAM,CAAClF,IAAI,wBA5jB1B+J,YAAA;6EA0jByBzN,KAAA,CAAAmN,eAAe,CAAC3E,QAAQ,E,KAO3C3J,mBAAA,CAQM,OARN6O,YAQM,G,8BAPJ7O,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Be,YAAA,CAEiBwC,yBAAA;MArkBzBC,UAAA,EAmkBiC/B,KAAA,CAAAmN,eAAe,CAACQ,iBAAiB;MAnkBlE,uBAAAzO,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAmkBiCF,KAAA,CAAAmN,eAAe,CAACQ,iBAAiB,GAAAzN,MAAA;;MAnkBlEgC,OAAA,EAAAC,QAAA,CAmkBoE,MAE5DjD,MAAA,UAAAA,MAAA,SArkBRiB,gBAAA,CAmkBoE,eAE5D,E;MArkBRiC,CAAA;uCAskBQ9C,YAAA,CAEiBwC,yBAAA;MAxkBzBC,UAAA,EAskBiC/B,KAAA,CAAAmN,eAAe,CAACS,iBAAiB;MAtkBlE,uBAAA1O,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAskBiCF,KAAA,CAAAmN,eAAe,CAACS,iBAAiB,GAAA1N,MAAA;;MAtkBlEgC,OAAA,EAAAC,QAAA,CAskBoE,MAE5DjD,MAAA,UAAAA,MAAA,SAxkBRiB,gBAAA,CAskBoE,aAE5D,E;MAxkBRiC,CAAA;;IAAAA,CAAA;6DA4kBIxD,mBAAA,cAAiB,EACjBU,YAAA,CAyCYkI,oBAAA;IAtnBhBzF,UAAA,EA6kBwB/B,KAAA,CAAA6N,mBAAmB,CAACnG,IAAI;IA7kBhD,uBAAAxI,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA6kBwBF,KAAA,CAAA6N,mBAAmB,CAACnG,IAAI,GAAAxH,MAAA;IAAEyH,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAACnI,IAAI,EAAC,sBAAsB;IAACsO,MAAM,EAAN,EAAM;IAChHlG,SAAO,EAAExI,QAAA,CAAA2O,cAAc;IAAGjG,OAAO,EAAE9H,KAAA,CAAA+H;;IA9kB1C7F,OAAA,EAAAC,QAAA,CA+kBM,MAEM,C,8BAFNtD,mBAAA,CAEM;MAFDN,KAAK,EAAC;IAA4C,IACrDM,mBAAA,CAA+C,WAA5C,0CAAwC,E,sBAG7CA,mBAAA,CAQM,OARNmP,YAQM,G,8BAPJnP,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAKM,OALNoP,YAKM,I,kBAJJtP,mBAAA,CAGiB2D,SAAA,QAzlB3BC,WAAA,CAslByCnD,QAAA,CAAAmO,iBAAiB,EAAzBhK,IAAI;2BAA3ByI,YAAA,CAGiBlK,yBAAA;QAHkCtD,GAAG,EAAE+E,IAAI,CAACH,EAAE;QAtlBzErB,UAAA,EAulBqB/B,KAAA,CAAA6N,mBAAmB,CAAC5B,aAAa,CAAC1I,IAAI,CAACH,EAAE;QAvlB9D,uBAAAlD,MAAA,IAulBqBF,KAAA,CAAA6N,mBAAmB,CAAC5B,aAAa,CAAC1I,IAAI,CAACH,EAAE,IAAAlD;;QAvlB9DgC,OAAA,EAAAC,QAAA,CAwlBY,MAAe,CAxlB3BhC,gBAAA,CAAAgB,gBAAA,CAwlBeoC,IAAI,CAACG,IAAI,IAAG,IAAE,GAAAvC,gBAAA,CAAGoC,IAAI,CAACM,EAAE,IAAG,IAChC,gB;QAzlBVzB,CAAA;;wCA6lBMvD,mBAAA,CAOM,OAPNqP,YAOM,G,8BANJrP,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCM,mBAAA,CAIS;MAnmBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA+lByBF,KAAA,CAAA6N,mBAAmB,CAACrF,QAAQ,GAAAtI,MAAA;MAAE3B,KAAK,EAAC;2BACnDI,mBAAA,CAES2D,SAAA,QAlmBnBC,WAAA,CAgmBmCnD,QAAA,CAAA+O,iBAAiB,EAA3BvF,MAAM;2BAArBjK,mBAAA,CAES;QAFoCH,GAAG,EAAEoK,MAAM,CAACxF,EAAE;QAAGvC,KAAK,EAAE+H,MAAM,CAACxF;0BACvEwF,MAAM,CAAClF,IAAI,IAAG,UAAQ,GAAAvC,gBAAA,CAAGyH,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA1H,gBAAA,CAAGyH,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAlmBVsF,YAAA;6EA+lByBpO,KAAA,CAAA6N,mBAAmB,CAACrF,QAAQ,E,KAO/C3J,mBAAA,CASM,OATNwP,YASM,G,8BARJxP,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAMS;MA9mBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAwmByBF,KAAA,CAAA6N,mBAAmB,CAACS,MAAM,GAAApO,MAAA;MAAE3B,KAAK,EAAC;sCACjDM,mBAAA,CAAiD;MAAzCgC,KAAK,EAAC;IAAmB,GAAC,QAAM,qBACxChC,mBAAA,CAA2C;MAAnCgC,KAAK,EAAC;IAAe,GAAC,MAAI,qBAClChC,mBAAA,CAA6C;MAArCgC,KAAK,EAAC;IAAiB,GAAC,MAAI,qBACpChC,mBAAA,CAAwC;MAAhCgC,KAAK,EAAC;IAAY,GAAC,MAAI,qBAC/BhC,mBAAA,CAAmC;MAA3BgC,KAAK,EAAC;IAAO,GAAC,MAAI,oB,2CALXb,KAAA,CAAA6N,mBAAmB,CAACS,MAAM,E,KAS7CzP,mBAAA,CAIM,OAJN0P,YAIM,G,8BAHJ1P,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CACuC;MApnB/C,uBAAAK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAmnB2BF,KAAA,CAAA6N,mBAAmB,CAACW,WAAW,GAAAtO,MAAA;MAAE3B,KAAK,EAAC,cAAc;MAACkQ,IAAI,EAAC,GAAG;MAC/ElO,WAAW,EAAC;mDADKP,KAAA,CAAA6N,mBAAmB,CAACW,WAAW,E;IAnnB1DpM,CAAA;6DAwnBIxD,mBAAA,cAAiB,EACjBU,YAAA,CAqGYkI,oBAAA;IA9tBhBzF,UAAA,EAynBwB/B,KAAA,CAAA0O,oBAAoB,CAAChH,IAAI;IAznBjD,uBAAAxI,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAynBwBF,KAAA,CAAA0O,oBAAoB,CAAChH,IAAI,GAAAxH,MAAA;IAAEyH,KAAK,EAAC,QAAQ;IAAC6D,IAAI,EAAC,IAAI;IAAE5D,SAAO,EAAExI,QAAA,CAAAuP,gBAAgB;IAC/F7G,OAAO,EAAE9H,KAAA,CAAA+H;;IA1nBhB7F,OAAA,EAAAC,QAAA,CA2nBM,MAaM,CAbNtD,mBAAA,CAaM,OAbN+P,YAaM,G,8BAZJ/P,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAIM,OAJNgQ,YAIM,GAHJvP,YAAA,CAEiBwC,yBAAA;MAhoB3BC,UAAA,EA8nBmCpC,IAAA,CAAAmP,iBAAiB;MA9nBpD,wB,sCA8nBmCnP,IAAA,CAAAmP,iBAAiB,GAAA5O,MAAA,GAAsBP,IAAA,CAAAoP,uBAAuB;;MA9nBjG7M,OAAA,EAAAC,QAAA,CA8nBmG,MAEzFjD,MAAA,UAAAA,MAAA,SAhoBViB,gBAAA,CA8nBmG,MAEzF,E;MAhoBViC,CAAA;gEAkoBQvD,mBAAA,CAIM,OAJNmQ,YAIM,I,kBAHJrQ,mBAAA,CAEiB2D,SAAA,QAroB3BC,WAAA,CAmoByC5C,IAAA,CAAAoM,KAAK,EAAbxI,IAAI;2BAA3ByI,YAAA,CAEiBlK,yBAAA;QAFsBtD,GAAG,EAAE+E,IAAI,CAACH,EAAE;QAnoB7DrB,UAAA,EAmoBwE/B,KAAA,CAAA0O,oBAAoB,CAACzC,aAAa,CAAC1I,IAAI,CAACH,EAAE;QAnoBlH,uBAAAlD,MAAA,IAmoBwEF,KAAA,CAAA0O,oBAAoB,CAACzC,aAAa,CAAC1I,IAAI,CAACH,EAAE,IAAAlD;;QAnoBlHgC,OAAA,EAAAC,QAAA,CAooBY,MAAe,CApoB3BhC,gBAAA,CAAAgB,gBAAA,CAooBeoC,IAAI,CAACG,IAAI,IAAG,IAAE,GAAAvC,gBAAA,CAAGoC,IAAI,CAACM,EAAE,IAAG,IAChC,gB;QAroBVzB,CAAA;;sCAuoBQvD,mBAAA,CAAiE,KAAjEoQ,YAAiE,EAA5C,MAAI,GAAA9N,gBAAA,CAAG/B,QAAA,CAAA8P,0BAA0B,IAAG,MAAI,gB,GAG/DrQ,mBAAA,CA8DM,OA9DNsQ,YA8DM,G,8BA7DJtQ,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CA2DM,OA3DNuQ,YA2DM,GA1DJvQ,mBAAA,CAIM,OAJNwQ,YAIM,G,8BAHJxQ,mBAAA,CAA0E;MAAnEN,KAAK,EAAC;IAAY,IA9oBrC4B,gBAAA,CA8oBsC,OAAK,GAAAtB,mBAAA,CAAmC;MAA7BN,KAAK,EAAC;IAAc,GAAC,GAAC,E,sCAC3DM,mBAAA,CAAyG;MAAlGwB,IAAI,EAAC,MAAM;MA/oB9B,uBAAAnB,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA+oBwCF,KAAA,CAAA0O,oBAAoB,CAACzK,QAAQ,GAAA/D,MAAA;MAAE3B,KAAK,EAAC,cAAc;MAACgC,WAAW,EAAC;mDAAhEP,KAAA,CAAA0O,oBAAoB,CAACzK,QAAQ,E,iCACzDpF,mBAAA,CAA6D;MAAxDN,KAAK,EAAC;IAA4B,GAAC,iBAAe,qB,GAGzDM,mBAAA,CAQM,OARNyQ,YAQM,G,8BAPJzQ,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAKS;MA1pBrB,uBAAAK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAqpB6BF,KAAA,CAAA0O,oBAAoB,CAACa,IAAI,GAAArP,MAAA;MAAE3B,KAAK,EAAC;sCAChDM,mBAAA,CAAkC;MAA1BgC,KAAK,EAAC;IAAO,GAAC,KAAG,qBACzBhC,mBAAA,CAAkC;MAA1BgC,KAAK,EAAC;IAAM,GAAC,MAAI,qBACzBhC,mBAAA,CAAqC;MAA7BgC,KAAK,EAAC;IAAS,GAAC,MAAI,qBAC5BhC,mBAAA,CAAsC;MAA9BgC,KAAK,EAAC;IAAU,GAAC,MAAI,oB,2CAJdb,KAAA,CAAA0O,oBAAoB,CAACa,IAAI,E,KAQ5C1Q,mBAAA,CAKM,OALN2Q,YAKM,GAJJ3Q,mBAAA,CAGQ,SAHR4Q,YAGQ,G,gBAFN5Q,mBAAA,CAAyF;MAAlFwB,IAAI,EAAC,UAAU;MA/pBpC,uBAAAnB,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA+pB8CF,KAAA,CAAA0O,oBAAoB,CAACgB,YAAY,GAAAxP,MAAA;MAAE3B,KAAK,EAAC;uDAAzCyB,KAAA,CAAA0O,oBAAoB,CAACgB,YAAY,E,iCACjE7Q,mBAAA,CAAgC;MAA1BN,KAAK,EAAC;IAAM,GAAC,QAAM,qB,KAI7BM,mBAAA,CAgBM,OAhBN8Q,YAgBM,G,8BAfJ9Q,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAaM,OAbN+Q,YAaM,GAZJ/Q,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEF,KAAA,CAAA0O,oBAAoB,CAACmB,eAAe;MAClDtR,KAAK,EAxqBrBwB,eAAA,EAwqBsB,iFAAiF,EAC/EC,KAAA,CAAA0O,oBAAoB,CAACmB,eAAe;QAC5CvQ,YAAA,CAAyDC,4BAAA;MAArCC,IAAI,EAAE,cAAc;MAAEjB,KAAK,EAAC;sCA1qBhE4B,gBAAA,CA0qByE,QAE3D,G,kBACAtB,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEF,KAAA,CAAA0O,oBAAoB,CAACmB,eAAe;MAClDtR,KAAK,EA9qBrBwB,eAAA,EA8qBsB,iFAAiF,GAC9EC,KAAA,CAAA0O,oBAAoB,CAACmB,eAAe;QAC7CvQ,YAAA,CAA4DC,4BAAA;MAAxCC,IAAI,EAAE,iBAAiB;MAAEjB,KAAK,EAAC;sCAhrBnE4B,gBAAA,CAgrB4E,QAE9D,G,sBAIOH,KAAA,CAAA0O,oBAAoB,CAACmB,eAAe,I,cAA/ClR,mBAAA,CAgBM,OAhBNmR,YAgBM,G,8BAfJjR,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CAaM,OAbNkR,YAaM,G,gBAZJlR,mBAAA,CAC4F;MADpFwB,IAAI,EAAEL,KAAA,CAAAgF,kBAAkB,CAACgL,aAAa;MAzrB5D,uBAAA9Q,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA0rByBF,KAAA,CAAA0O,oBAAoB,CAACzJ,QAAQ,GAAA/E,MAAA;MAAEgF,QAAQ,EAAR,EAAQ;MAAC3G,KAAK,EAAC;4BA1rBvE0R,YAAA,I,iBA0rByBjQ,KAAA,CAAA0O,oBAAoB,CAACzJ,QAAQ,E,GACxCpG,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEF,KAAA,CAAAgF,kBAAkB,CAACgL,aAAa,IAAIhQ,KAAA,CAAAgF,kBAAkB,CAACgL,aAAa;MAAE3P,IAAI,EAAC,QAAQ;MACjG9B,KAAK,EAAC;QACNe,YAAA,CACoBC,4BAAA;MADAC,IAAI,UAAUQ,KAAA,CAAAgF,kBAAkB,CAACgL,aAAa;MAChEzR,KAAK,EAAC;yCAEVM,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEd,QAAA,CAAA8Q,+BAA+B;MAAI7P,IAAI,EAAC,QAAQ;MAC9D9B,KAAK,EAAC;QACNe,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;sCAlsBrE4B,gBAAA,CAksB8E,QAEhE,G,SApsBdvB,mBAAA,e,KA0sBMC,mBAAA,CAOM,OAPNsR,YAOM,G,8BANJtR,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MAhtBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IA4sByBF,KAAA,CAAA0O,oBAAoB,CAAClG,QAAQ,GAAAtI,MAAA;MAAE3B,KAAK,EAAC,aAAa;MAAEkK,QAAM,EAAAvJ,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAAEd,QAAA,CAAA8Q,+BAA+B;2BAC1GvR,mBAAA,CAES2D,SAAA,QA/sBnBC,WAAA,CA6sBmC5C,IAAA,CAAAgJ,QAAQ,EAAlBC,MAAM;2BAArBjK,mBAAA,CAES;QAF2BH,GAAG,EAAEoK,MAAM,CAACxF,EAAE;QAAGvC,KAAK,EAAE+H,MAAM,CAACxF;0BAC9DwF,MAAM,CAAClF,IAAI,IAAG,UAAQ,GAAAvC,gBAAA,CAAGyH,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA1H,gBAAA,CAAGyH,MAAM,CAACE,UAAU,IAAG,KAC9E,uBA/sBVsH,YAAA;6FA4sByBpQ,KAAA,CAAA0O,oBAAoB,CAAClG,QAAQ,E,KAOhD3J,mBAAA,CAUM,OAVNwR,YAUM,G,8BATJxR,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CAOM,OAPNyR,YAOM,GANJhR,YAAA,CAEiBwC,yBAAA;MAxtB3BC,UAAA,EAstBmC/B,KAAA,CAAA0O,oBAAoB,CAAC1B,YAAY;MAttBpE,uBAAA9N,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAstBmCF,KAAA,CAAA0O,oBAAoB,CAAC1B,YAAY,GAAA9M,MAAA;;MAttBpEgC,OAAA,EAAAC,QAAA,CAutBY,MAAkCjD,MAAA,UAAAA,MAAA,SAAlCL,mBAAA,CAAkC;QAA5BN,KAAK,EAAC;MAAM,GAAC,UAAQ,oB;MAvtBvC6D,CAAA;uCAytBU9C,YAAA,CAEiBwC,yBAAA;MA3tB3BC,UAAA,EAytBmC/B,KAAA,CAAA0O,oBAAoB,CAAC6B,cAAc;MAztBtE,uBAAArR,MAAA,SAAAA,MAAA,OAAAgB,MAAA,IAytBmCF,KAAA,CAAA0O,oBAAoB,CAAC6B,cAAc,GAAArQ,MAAA;;MAztBtEgC,OAAA,EAAAC,QAAA,CA0tBY,MAAkCjD,MAAA,UAAAA,MAAA,SAAlCL,mBAAA,CAAkC;QAA5BN,KAAK,EAAC;MAAM,GAAC,UAAQ,oB;MA1tBvC6D,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}