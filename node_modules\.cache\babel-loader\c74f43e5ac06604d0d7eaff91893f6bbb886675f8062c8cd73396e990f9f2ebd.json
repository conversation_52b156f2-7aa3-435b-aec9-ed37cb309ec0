{"ast": null, "code": "import { createStore } from 'vuex';\nexport default createStore({\n  state: {\n    hosts: [{\n      id: 1,\n      name: 'server-001',\n      ip: '*************',\n      status: 'normal',\n      selected: false,\n      accounts: [{\n        id: 1,\n        username: 'root',\n        password: 'Server@2023',\n        lastPasswordChange: '2023-12-15 14:30',\n        passwordExpiryDate: '2024-01-14 14:30',\n        isDefault: true\n      }, {\n        id: 2,\n        username: 'admin',\n        password: 'Admin!2023',\n        lastPasswordChange: '2023-12-10 09:45',\n        passwordExpiryDate: '2024-01-09 09:45',\n        isDefault: false\n      }],\n      policyId: 1\n    }, {\n      id: 2,\n      name: 'server-002',\n      ip: '*************',\n      status: 'normal',\n      selected: false,\n      accounts: [{\n        id: 3,\n        username: 'root',\n        password: 'SecServer#45',\n        lastPasswordChange: '2023-11-20 09:15',\n        passwordExpiryDate: '2024-01-19 09:15',\n        isDefault: true\n      }, {\n        id: 4,\n        username: 'deploy',\n        password: 'Deploy$2023',\n        lastPasswordChange: '2023-11-25 11:20',\n        passwordExpiryDate: '2024-01-24 11:20',\n        isDefault: false\n      }],\n      policyId: 2\n    }, {\n      id: 3,\n      name: 'db-001',\n      ip: '*************',\n      status: 'warning',\n      selected: false,\n      accounts: [{\n        id: 5,\n        username: 'root',\n        password: 'DbPass$789',\n        lastPasswordChange: '2023-10-05 16:45',\n        passwordExpiryDate: '2023-10-12 16:45',\n        isDefault: true\n      }, {\n        id: 6,\n        username: 'dbadmin',\n        password: 'DBA#9876',\n        lastPasswordChange: '2023-10-10 14:30',\n        passwordExpiryDate: '2023-10-17 14:30',\n        isDefault: false\n      }],\n      policyId: 3\n    }, {\n      id: 4,\n      name: 'app-001',\n      ip: '*************',\n      status: 'error',\n      selected: false,\n      accounts: [{\n        id: 7,\n        username: 'root',\n        password: 'App2023!',\n        lastPasswordChange: '2023-09-28 11:20',\n        passwordExpiryDate: '2023-11-27 11:20',\n        isDefault: true\n      }, {\n        id: 8,\n        username: 'appuser',\n        password: 'App@User123',\n        lastPasswordChange: '2023-09-30 15:40',\n        passwordExpiryDate: '2023-11-29 15:40',\n        isDefault: false\n      }],\n      policyId: 2\n    }],\n    policies: [{\n      id: 1,\n      name: '高强度策略',\n      description: '适用于核心生产系统',\n      hostsCount: 45,\n      minLength: 12,\n      expiryDays: 30,\n      requireUppercase: true,\n      requireLowercase: true,\n      requireNumbers: true,\n      requireSpecial: true,\n      forbidUsername: true,\n      historyCount: 5\n    }, {\n      id: 2,\n      name: '标准策略',\n      description: '适用于常规系统',\n      hostsCount: 78,\n      minLength: 10,\n      expiryDays: 60,\n      requireUppercase: true,\n      requireLowercase: true,\n      requireNumbers: true,\n      requireSpecial: false,\n      forbidUsername: true,\n      historyCount: 3\n    }, {\n      id: 3,\n      name: '紧急策略',\n      description: '用于安全事件响应',\n      hostsCount: 12,\n      minLength: 16,\n      expiryDays: 7,\n      requireUppercase: true,\n      requireLowercase: true,\n      requireNumbers: true,\n      requireSpecial: true,\n      forbidUsername: true,\n      historyCount: 10\n    }],\n    tasks: [{\n      id: 1,\n      name: '生产环境密码更新',\n      target: '生产环境所有服务器',\n      schedule: '每月第一个周一 03:00',\n      lastRun: '2023-12-01 03:00',\n      nextRun: '2024-01-01 03:00',\n      status: 'running'\n    }, {\n      id: 2,\n      name: '测试环境密码更新',\n      target: '测试环境数据库服务器',\n      schedule: '每周日 02:00',\n      lastRun: '2023-12-10 02:00',\n      nextRun: '2023-12-17 02:00',\n      status: 'running'\n    }],\n    // 临时存储当前操作的对象\n    currentOperation: {\n      host: null,\n      policy: null,\n      task: null\n    }\n  },\n  getters: {\n    getHostById: state => id => {\n      return state.hosts.find(host => host.id === id);\n    },\n    getPolicyById: state => id => {\n      return state.policies.find(policy => policy.id === id);\n    },\n    getTaskById: state => id => {\n      return state.tasks.find(task => task.id === id);\n    },\n    selectedHosts: state => {\n      return state.hosts.filter(host => host.selected);\n    }\n  },\n  mutations: {\n    // 主机相关\n    toggleHostSelection(state, hostId) {\n      const host = state.hosts.find(h => h.id === hostId);\n      if (host) host.selected = !host.selected;\n    },\n    selectAllHosts(state, isSelected) {\n      state.hosts.forEach(host => host.selected = isSelected);\n    },\n    setCurrentHost(state, host) {\n      state.currentOperation.host = host;\n    },\n    updateHostStatus(state, {\n      hostId,\n      status\n    }) {\n      const host = state.hosts.find(h => h.id === hostId);\n      if (host) host.status = status;\n    },\n    updateHostPassword(state, {\n      hostId,\n      accountId,\n      password,\n      policyId\n    }) {\n      const host = state.hosts.find(h => h.id === hostId);\n      if (host) {\n        // 找到对应的账号\n        const account = host.accounts.find(a => a.id === accountId);\n        if (account) {\n          // 更新密码和更新时间\n          account.password = password;\n          const now = new Date();\n          account.lastPasswordChange = now.toLocaleString('zh-CN');\n          host.status = 'normal';\n\n          // 计算密码过期时间\n          const policy = state.policies.find(p => p.id === (policyId || host.policyId)) || state.policies[0];\n          const expiryDate = new Date(now);\n          expiryDate.setDate(expiryDate.getDate() + policy.expiryDays);\n          account.passwordExpiryDate = expiryDate.toLocaleString('zh-CN');\n        }\n      }\n    },\n    // 策略相关\n    addPolicy(state, policy) {\n      state.policies.push({\n        id: state.policies.length + 1,\n        hostsCount: 0,\n        ...policy\n      });\n    },\n    updatePolicy(state, updatedPolicy) {\n      const index = state.policies.findIndex(p => p.id === updatedPolicy.id);\n      if (index !== -1) {\n        state.policies[index] = {\n          ...state.policies[index],\n          ...updatedPolicy\n        };\n      }\n    },\n    deletePolicy(state, policyId) {\n      state.policies = state.policies.filter(p => p.id !== policyId);\n    },\n    setCurrentPolicy(state, policy) {\n      state.currentOperation.policy = policy;\n    },\n    // 任务相关\n    addTask(state, task) {\n      state.tasks.push({\n        id: state.tasks.length + 1,\n        status: 'pending',\n        ...task\n      });\n    },\n    updateTask(state, updatedTask) {\n      const index = state.tasks.findIndex(t => t.id === updatedTask.id);\n      if (index !== -1) {\n        state.tasks[index] = {\n          ...state.tasks[index],\n          ...updatedTask\n        };\n      }\n    },\n    deleteTask(state, taskId) {\n      state.tasks = state.tasks.filter(t => t.id !== taskId);\n    },\n    setCurrentTask(state, task) {\n      state.currentOperation.task = task;\n    }\n  },\n  actions: {\n    // 主机相关\n    updateHostPassword({\n      commit\n    }, {\n      hostId,\n      accountId,\n      password,\n      policyId,\n      newStatus = 'normal'\n    }) {\n      // 在实际应用中，这里会调用API\n      return new Promise(resolve => {\n        setTimeout(() => {\n          if (password) {\n            commit('updateHostPassword', {\n              hostId,\n              accountId,\n              password,\n              policyId\n            });\n          } else {\n            commit('updateHostStatus', {\n              hostId,\n              status: newStatus\n            });\n          }\n          resolve(true);\n        }, 1000);\n      });\n    },\n    addHostAccount({\n      commit,\n      state\n    }, {\n      hostId,\n      username,\n      password,\n      policyId,\n      isDefault\n    }) {\n      // 在实际应用中，这里会调用API\n      return new Promise(resolve => {\n        setTimeout(() => {\n          const host = state.hosts.find(h => h.id === hostId);\n          if (host) {\n            // 生成新账号ID\n            const newAccountId = Math.max(...state.hosts.flatMap(h => h.accounts.map(a => a.id))) + 1;\n\n            // 如果设为默认，将其他账号设为非默认\n            if (isDefault) {\n              host.accounts.forEach(account => {\n                account.isDefault = false;\n              });\n            }\n\n            // 计算密码过期时间\n            const policy = state.policies.find(p => p.id === policyId) || state.policies[0];\n            const now = new Date();\n            const expiryDate = new Date(now);\n            expiryDate.setDate(expiryDate.getDate() + policy.expiryDays);\n\n            // 创建新账号\n            const newAccount = {\n              id: newAccountId,\n              username,\n              password,\n              lastPasswordChange: now.toLocaleString('zh-CN'),\n              passwordExpiryDate: expiryDate.toLocaleString('zh-CN'),\n              isDefault\n            };\n            host.accounts.push(newAccount);\n            host.status = 'normal';\n          }\n          resolve(true);\n        }, 1000);\n      });\n    },\n    batchUpdateHostPasswords({\n      commit,\n      state,\n      dispatch\n    }, {\n      hostIds,\n      policyId\n    }) {\n      // 在实际应用中，这里会调用API\n      return Promise.all(hostIds.map(hostId => {\n        const host = state.hosts.find(h => h.id === hostId);\n        if (host) {\n          // 为每个账号更新密码\n          return Promise.all(host.accounts.map(account => {\n            const newPassword = this.dispatch('generatePassword', {\n              policyId\n            });\n            return dispatch('updateHostPassword', {\n              hostId,\n              accountId: account.id,\n              password: newPassword,\n              policyId\n            });\n          }));\n        }\n        return Promise.resolve();\n      }));\n    },\n    // 工具方法\n    generatePassword(_, {\n      policyId\n    }) {\n      // 此处简化实现，实际应用中应该根据策略生成符合要求的密码\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';\n      let password = '';\n      for (let i = 0; i < 12; i++) {\n        password += chars.charAt(Math.floor(Math.random() * chars.length));\n      }\n      return password;\n    },\n    // 策略相关\n    applyPolicyToHosts() {\n      // 在实际应用中，这里会调用API\n      return new Promise(resolve => {\n        setTimeout(() => {\n          resolve(true);\n        }, 1500);\n      });\n    },\n    // 任务相关\n    executeTask() {\n      // 在实际应用中，这里会调用API\n      return new Promise(resolve => {\n        setTimeout(() => {\n          resolve(true);\n        }, 2000);\n      });\n    }\n  }\n});", "map": {"version": 3, "names": ["createStore", "state", "hosts", "id", "name", "ip", "status", "selected", "accounts", "username", "password", "lastPasswordChange", "passwordExpiryDate", "isDefault", "policyId", "policies", "description", "hostsCount", "<PERSON><PERSON><PERSON><PERSON>", "expiryDays", "requireUppercase", "requireLowercase", "requireNumbers", "requireSpecial", "forbidUsername", "historyCount", "tasks", "target", "schedule", "lastRun", "nextRun", "currentOperation", "host", "policy", "task", "getters", "getHostById", "find", "getPolicyById", "getTaskById", "selectedHosts", "filter", "mutations", "toggleHostSelection", "hostId", "h", "selectAllHosts", "isSelected", "for<PERSON>ach", "setCurrentHost", "updateHostStatus", "updateHostPassword", "accountId", "account", "a", "now", "Date", "toLocaleString", "p", "expiryDate", "setDate", "getDate", "addPolicy", "push", "length", "updatePolicy", "updatedPolicy", "index", "findIndex", "deletePolicy", "setCurrentPolicy", "addTask", "updateTask", "updatedTask", "t", "deleteTask", "taskId", "setCurrentTask", "actions", "commit", "newStatus", "Promise", "resolve", "setTimeout", "addHostAccount", "newAccountId", "Math", "max", "flatMap", "map", "newAccount", "batchUpdateHostPasswords", "dispatch", "hostIds", "all", "newPassword", "generatePassword", "_", "chars", "i", "char<PERSON>t", "floor", "random", "applyPolicyToHosts", "executeTask"], "sources": ["D:/demo/ooo/pass/src/store/index.js"], "sourcesContent": ["import { createStore } from 'vuex'\r\n\r\nexport default createStore({\r\n    state: {\r\n        hosts: [\r\n            {\r\n                id: 1, name: 'server-001', ip: '*************', status: 'normal', selected: false, accounts: [\r\n                    { id: 1, username: 'root', password: 'Server@2023', lastPasswordChange: '2023-12-15 14:30', passwordExpiryDate: '2024-01-14 14:30', isDefault: true },\r\n                    { id: 2, username: 'admin', password: 'Admin!2023', lastPasswordChange: '2023-12-10 09:45', passwordExpiryDate: '2024-01-09 09:45', isDefault: false }\r\n                ], policyId: 1\r\n            },\r\n            {\r\n                id: 2, name: 'server-002', ip: '*************', status: 'normal', selected: false, accounts: [\r\n                    { id: 3, username: 'root', password: 'SecServer#45', lastPasswordChange: '2023-11-20 09:15', passwordExpiryDate: '2024-01-19 09:15', isDefault: true },\r\n                    { id: 4, username: 'deploy', password: 'Deploy$2023', lastPasswordChange: '2023-11-25 11:20', passwordExpiryDate: '2024-01-24 11:20', isDefault: false }\r\n                ], policyId: 2\r\n            },\r\n            {\r\n                id: 3, name: 'db-001', ip: '*************', status: 'warning', selected: false, accounts: [\r\n                    { id: 5, username: 'root', password: 'DbPass$789', lastPasswordChange: '2023-10-05 16:45', passwordExpiryDate: '2023-10-12 16:45', isDefault: true },\r\n                    { id: 6, username: 'dbadmin', password: 'DBA#9876', lastPasswordChange: '2023-10-10 14:30', passwordExpiryDate: '2023-10-17 14:30', isDefault: false }\r\n                ], policyId: 3\r\n            },\r\n            {\r\n                id: 4, name: 'app-001', ip: '*************', status: 'error', selected: false, accounts: [\r\n                    { id: 7, username: 'root', password: 'App2023!', lastPasswordChange: '2023-09-28 11:20', passwordExpiryDate: '2023-11-27 11:20', isDefault: true },\r\n                    { id: 8, username: 'appuser', password: 'App@User123', lastPasswordChange: '2023-09-30 15:40', passwordExpiryDate: '2023-11-29 15:40', isDefault: false }\r\n                ], policyId: 2\r\n            }\r\n        ],\r\n        policies: [\r\n            {\r\n                id: 1,\r\n                name: '高强度策略',\r\n                description: '适用于核心生产系统',\r\n                hostsCount: 45,\r\n                minLength: 12,\r\n                expiryDays: 30,\r\n                requireUppercase: true,\r\n                requireLowercase: true,\r\n                requireNumbers: true,\r\n                requireSpecial: true,\r\n                forbidUsername: true,\r\n                historyCount: 5\r\n            },\r\n            {\r\n                id: 2,\r\n                name: '标准策略',\r\n                description: '适用于常规系统',\r\n                hostsCount: 78,\r\n                minLength: 10,\r\n                expiryDays: 60,\r\n                requireUppercase: true,\r\n                requireLowercase: true,\r\n                requireNumbers: true,\r\n                requireSpecial: false,\r\n                forbidUsername: true,\r\n                historyCount: 3\r\n            },\r\n            {\r\n                id: 3,\r\n                name: '紧急策略',\r\n                description: '用于安全事件响应',\r\n                hostsCount: 12,\r\n                minLength: 16,\r\n                expiryDays: 7,\r\n                requireUppercase: true,\r\n                requireLowercase: true,\r\n                requireNumbers: true,\r\n                requireSpecial: true,\r\n                forbidUsername: true,\r\n                historyCount: 10\r\n            }\r\n        ],\r\n        tasks: [\r\n            {\r\n                id: 1,\r\n                name: '生产环境密码更新',\r\n                target: '生产环境所有服务器',\r\n                schedule: '每月第一个周一 03:00',\r\n                lastRun: '2023-12-01 03:00',\r\n                nextRun: '2024-01-01 03:00',\r\n                status: 'running'\r\n            },\r\n            {\r\n                id: 2,\r\n                name: '测试环境密码更新',\r\n                target: '测试环境数据库服务器',\r\n                schedule: '每周日 02:00',\r\n                lastRun: '2023-12-10 02:00',\r\n                nextRun: '2023-12-17 02:00',\r\n                status: 'running'\r\n            }\r\n        ],\r\n        // 临时存储当前操作的对象\r\n        currentOperation: {\r\n            host: null,\r\n            policy: null,\r\n            task: null\r\n        }\r\n    },\r\n    getters: {\r\n        getHostById: (state) => (id) => {\r\n            return state.hosts.find(host => host.id === id)\r\n        },\r\n        getPolicyById: (state) => (id) => {\r\n            return state.policies.find(policy => policy.id === id)\r\n        },\r\n        getTaskById: (state) => (id) => {\r\n            return state.tasks.find(task => task.id === id)\r\n        },\r\n        selectedHosts: (state) => {\r\n            return state.hosts.filter(host => host.selected)\r\n        }\r\n    },\r\n    mutations: {\r\n        // 主机相关\r\n        toggleHostSelection(state, hostId) {\r\n            const host = state.hosts.find(h => h.id === hostId)\r\n            if (host) host.selected = !host.selected\r\n        },\r\n        selectAllHosts(state, isSelected) {\r\n            state.hosts.forEach(host => host.selected = isSelected)\r\n        },\r\n        setCurrentHost(state, host) {\r\n            state.currentOperation.host = host\r\n        },\r\n        updateHostStatus(state, { hostId, status }) {\r\n            const host = state.hosts.find(h => h.id === hostId)\r\n            if (host) host.status = status\r\n        },\r\n        updateHostPassword(state, { hostId, accountId, password, policyId }) {\r\n            const host = state.hosts.find(h => h.id === hostId)\r\n            if (host) {\r\n                // 找到对应的账号\r\n                const account = host.accounts.find(a => a.id === accountId)\r\n                if (account) {\r\n                    // 更新密码和更新时间\r\n                    account.password = password\r\n                    const now = new Date()\r\n                    account.lastPasswordChange = now.toLocaleString('zh-CN')\r\n                    host.status = 'normal'\r\n\r\n                    // 计算密码过期时间\r\n                    const policy = state.policies.find(p => p.id === (policyId || host.policyId)) || state.policies[0]\r\n                    const expiryDate = new Date(now)\r\n                    expiryDate.setDate(expiryDate.getDate() + policy.expiryDays)\r\n                    account.passwordExpiryDate = expiryDate.toLocaleString('zh-CN')\r\n                }\r\n            }\r\n        },\r\n\r\n        // 策略相关\r\n        addPolicy(state, policy) {\r\n            state.policies.push({\r\n                id: state.policies.length + 1,\r\n                hostsCount: 0,\r\n                ...policy\r\n            })\r\n        },\r\n        updatePolicy(state, updatedPolicy) {\r\n            const index = state.policies.findIndex(p => p.id === updatedPolicy.id)\r\n            if (index !== -1) {\r\n                state.policies[index] = { ...state.policies[index], ...updatedPolicy }\r\n            }\r\n        },\r\n        deletePolicy(state, policyId) {\r\n            state.policies = state.policies.filter(p => p.id !== policyId)\r\n        },\r\n        setCurrentPolicy(state, policy) {\r\n            state.currentOperation.policy = policy\r\n        },\r\n\r\n        // 任务相关\r\n        addTask(state, task) {\r\n            state.tasks.push({\r\n                id: state.tasks.length + 1,\r\n                status: 'pending',\r\n                ...task\r\n            })\r\n        },\r\n        updateTask(state, updatedTask) {\r\n            const index = state.tasks.findIndex(t => t.id === updatedTask.id)\r\n            if (index !== -1) {\r\n                state.tasks[index] = { ...state.tasks[index], ...updatedTask }\r\n            }\r\n        },\r\n        deleteTask(state, taskId) {\r\n            state.tasks = state.tasks.filter(t => t.id !== taskId)\r\n        },\r\n        setCurrentTask(state, task) {\r\n            state.currentOperation.task = task\r\n        }\r\n    },\r\n    actions: {\r\n        // 主机相关\r\n        updateHostPassword({ commit }, { hostId, accountId, password, policyId, newStatus = 'normal' }) {\r\n            // 在实际应用中，这里会调用API\r\n            return new Promise((resolve) => {\r\n                setTimeout(() => {\r\n                    if (password) {\r\n                        commit('updateHostPassword', { hostId, accountId, password, policyId })\r\n                    } else {\r\n                        commit('updateHostStatus', { hostId, status: newStatus })\r\n                    }\r\n                    resolve(true)\r\n                }, 1000)\r\n            })\r\n        },\r\n\r\n        addHostAccount({ commit, state }, { hostId, username, password, policyId, isDefault }) {\r\n            // 在实际应用中，这里会调用API\r\n            return new Promise((resolve) => {\r\n                setTimeout(() => {\r\n                    const host = state.hosts.find(h => h.id === hostId)\r\n                    if (host) {\r\n                        // 生成新账号ID\r\n                        const newAccountId = Math.max(...state.hosts.flatMap(h => h.accounts.map(a => a.id))) + 1\r\n\r\n                        // 如果设为默认，将其他账号设为非默认\r\n                        if (isDefault) {\r\n                            host.accounts.forEach(account => {\r\n                                account.isDefault = false\r\n                            })\r\n                        }\r\n\r\n                        // 计算密码过期时间\r\n                        const policy = state.policies.find(p => p.id === policyId) || state.policies[0]\r\n                        const now = new Date()\r\n                        const expiryDate = new Date(now)\r\n                        expiryDate.setDate(expiryDate.getDate() + policy.expiryDays)\r\n\r\n                        // 创建新账号\r\n                        const newAccount = {\r\n                            id: newAccountId,\r\n                            username,\r\n                            password,\r\n                            lastPasswordChange: now.toLocaleString('zh-CN'),\r\n                            passwordExpiryDate: expiryDate.toLocaleString('zh-CN'),\r\n                            isDefault\r\n                        }\r\n\r\n                        host.accounts.push(newAccount)\r\n                        host.status = 'normal'\r\n                    }\r\n                    resolve(true)\r\n                }, 1000)\r\n            })\r\n        },\r\n\r\n        batchUpdateHostPasswords({ commit, state, dispatch }, { hostIds, policyId }) {\r\n            // 在实际应用中，这里会调用API\r\n            return Promise.all(\r\n                hostIds.map(hostId => {\r\n                    const host = state.hosts.find(h => h.id === hostId)\r\n                    if (host) {\r\n                        // 为每个账号更新密码\r\n                        return Promise.all(host.accounts.map(account => {\r\n                            const newPassword = this.dispatch('generatePassword', { policyId })\r\n                            return dispatch('updateHostPassword', {\r\n                                hostId,\r\n                                accountId: account.id,\r\n                                password: newPassword,\r\n                                policyId\r\n                            })\r\n                        }))\r\n                    }\r\n                    return Promise.resolve()\r\n                })\r\n            )\r\n        },\r\n\r\n        // 工具方法\r\n        generatePassword(_, { policyId }) {\r\n            // 此处简化实现，实际应用中应该根据策略生成符合要求的密码\r\n            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n            let password = ''\r\n            for (let i = 0; i < 12; i++) {\r\n                password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n            }\r\n            return password\r\n        },\r\n\r\n        // 策略相关\r\n        applyPolicyToHosts() {\r\n            // 在实际应用中，这里会调用API\r\n            return new Promise((resolve) => {\r\n                setTimeout(() => {\r\n                    resolve(true)\r\n                }, 1500)\r\n            })\r\n        },\r\n\r\n        // 任务相关\r\n        executeTask() {\r\n            // 在实际应用中，这里会调用API\r\n            return new Promise((resolve) => {\r\n                setTimeout(() => {\r\n                    resolve(true)\r\n                }, 2000)\r\n            })\r\n        }\r\n    }\r\n}) "], "mappings": "AAAA,SAASA,WAAW,QAAQ,MAAM;AAElC,eAAeA,WAAW,CAAC;EACvBC,KAAK,EAAE;IACHC,KAAK,EAAE,CACH;MACIC,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,YAAY;MAAEC,EAAE,EAAE,eAAe;MAAEC,MAAM,EAAE,QAAQ;MAAEC,QAAQ,EAAE,KAAK;MAAEC,QAAQ,EAAE,CACzF;QAAEL,EAAE,EAAE,CAAC;QAAEM,QAAQ,EAAE,MAAM;QAAEC,QAAQ,EAAE,aAAa;QAAEC,kBAAkB,EAAE,kBAAkB;QAAEC,kBAAkB,EAAE,kBAAkB;QAAEC,SAAS,EAAE;MAAK,CAAC,EACrJ;QAAEV,EAAE,EAAE,CAAC;QAAEM,QAAQ,EAAE,OAAO;QAAEC,QAAQ,EAAE,YAAY;QAAEC,kBAAkB,EAAE,kBAAkB;QAAEC,kBAAkB,EAAE,kBAAkB;QAAEC,SAAS,EAAE;MAAM,CAAC,CACzJ;MAAEC,QAAQ,EAAE;IACjB,CAAC,EACD;MACIX,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,YAAY;MAAEC,EAAE,EAAE,eAAe;MAAEC,MAAM,EAAE,QAAQ;MAAEC,QAAQ,EAAE,KAAK;MAAEC,QAAQ,EAAE,CACzF;QAAEL,EAAE,EAAE,CAAC;QAAEM,QAAQ,EAAE,MAAM;QAAEC,QAAQ,EAAE,cAAc;QAAEC,kBAAkB,EAAE,kBAAkB;QAAEC,kBAAkB,EAAE,kBAAkB;QAAEC,SAAS,EAAE;MAAK,CAAC,EACtJ;QAAEV,EAAE,EAAE,CAAC;QAAEM,QAAQ,EAAE,QAAQ;QAAEC,QAAQ,EAAE,aAAa;QAAEC,kBAAkB,EAAE,kBAAkB;QAAEC,kBAAkB,EAAE,kBAAkB;QAAEC,SAAS,EAAE;MAAM,CAAC,CAC3J;MAAEC,QAAQ,EAAE;IACjB,CAAC,EACD;MACIX,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,QAAQ;MAAEC,EAAE,EAAE,eAAe;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE,KAAK;MAAEC,QAAQ,EAAE,CACtF;QAAEL,EAAE,EAAE,CAAC;QAAEM,QAAQ,EAAE,MAAM;QAAEC,QAAQ,EAAE,YAAY;QAAEC,kBAAkB,EAAE,kBAAkB;QAAEC,kBAAkB,EAAE,kBAAkB;QAAEC,SAAS,EAAE;MAAK,CAAC,EACpJ;QAAEV,EAAE,EAAE,CAAC;QAAEM,QAAQ,EAAE,SAAS;QAAEC,QAAQ,EAAE,UAAU;QAAEC,kBAAkB,EAAE,kBAAkB;QAAEC,kBAAkB,EAAE,kBAAkB;QAAEC,SAAS,EAAE;MAAM,CAAC,CACzJ;MAAEC,QAAQ,EAAE;IACjB,CAAC,EACD;MACIX,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,SAAS;MAAEC,EAAE,EAAE,eAAe;MAAEC,MAAM,EAAE,OAAO;MAAEC,QAAQ,EAAE,KAAK;MAAEC,QAAQ,EAAE,CACrF;QAAEL,EAAE,EAAE,CAAC;QAAEM,QAAQ,EAAE,MAAM;QAAEC,QAAQ,EAAE,UAAU;QAAEC,kBAAkB,EAAE,kBAAkB;QAAEC,kBAAkB,EAAE,kBAAkB;QAAEC,SAAS,EAAE;MAAK,CAAC,EAClJ;QAAEV,EAAE,EAAE,CAAC;QAAEM,QAAQ,EAAE,SAAS;QAAEC,QAAQ,EAAE,aAAa;QAAEC,kBAAkB,EAAE,kBAAkB;QAAEC,kBAAkB,EAAE,kBAAkB;QAAEC,SAAS,EAAE;MAAM,CAAC,CAC5J;MAAEC,QAAQ,EAAE;IACjB,CAAC,CACJ;IACDC,QAAQ,EAAE,CACN;MACIZ,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,OAAO;MACbY,WAAW,EAAE,WAAW;MACxBC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,EAAE;MACdC,gBAAgB,EAAE,IAAI;MACtBC,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,IAAI;MACpBC,YAAY,EAAE;IAClB,CAAC,EACD;MACItB,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,MAAM;MACZY,WAAW,EAAE,SAAS;MACtBC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,EAAE;MACdC,gBAAgB,EAAE,IAAI;MACtBC,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE,IAAI;MACpBC,YAAY,EAAE;IAClB,CAAC,EACD;MACItB,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,MAAM;MACZY,WAAW,EAAE,UAAU;MACvBC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,CAAC;MACbC,gBAAgB,EAAE,IAAI;MACtBC,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,IAAI;MACpBC,YAAY,EAAE;IAClB,CAAC,CACJ;IACDC,KAAK,EAAE,CACH;MACIvB,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,UAAU;MAChBuB,MAAM,EAAE,WAAW;MACnBC,QAAQ,EAAE,eAAe;MACzBC,OAAO,EAAE,kBAAkB;MAC3BC,OAAO,EAAE,kBAAkB;MAC3BxB,MAAM,EAAE;IACZ,CAAC,EACD;MACIH,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,UAAU;MAChBuB,MAAM,EAAE,YAAY;MACpBC,QAAQ,EAAE,WAAW;MACrBC,OAAO,EAAE,kBAAkB;MAC3BC,OAAO,EAAE,kBAAkB;MAC3BxB,MAAM,EAAE;IACZ,CAAC,CACJ;IACD;IACAyB,gBAAgB,EAAE;MACdC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE;IACV;EACJ,CAAC;EACDC,OAAO,EAAE;IACLC,WAAW,EAAGnC,KAAK,IAAME,EAAE,IAAK;MAC5B,OAAOF,KAAK,CAACC,KAAK,CAACmC,IAAI,CAACL,IAAI,IAAIA,IAAI,CAAC7B,EAAE,KAAKA,EAAE,CAAC;IACnD,CAAC;IACDmC,aAAa,EAAGrC,KAAK,IAAME,EAAE,IAAK;MAC9B,OAAOF,KAAK,CAACc,QAAQ,CAACsB,IAAI,CAACJ,MAAM,IAAIA,MAAM,CAAC9B,EAAE,KAAKA,EAAE,CAAC;IAC1D,CAAC;IACDoC,WAAW,EAAGtC,KAAK,IAAME,EAAE,IAAK;MAC5B,OAAOF,KAAK,CAACyB,KAAK,CAACW,IAAI,CAACH,IAAI,IAAIA,IAAI,CAAC/B,EAAE,KAAKA,EAAE,CAAC;IACnD,CAAC;IACDqC,aAAa,EAAGvC,KAAK,IAAK;MACtB,OAAOA,KAAK,CAACC,KAAK,CAACuC,MAAM,CAACT,IAAI,IAAIA,IAAI,CAACzB,QAAQ,CAAC;IACpD;EACJ,CAAC;EACDmC,SAAS,EAAE;IACP;IACAC,mBAAmBA,CAAC1C,KAAK,EAAE2C,MAAM,EAAE;MAC/B,MAAMZ,IAAI,GAAG/B,KAAK,CAACC,KAAK,CAACmC,IAAI,CAACQ,CAAC,IAAIA,CAAC,CAAC1C,EAAE,KAAKyC,MAAM,CAAC;MACnD,IAAIZ,IAAI,EAAEA,IAAI,CAACzB,QAAQ,GAAG,CAACyB,IAAI,CAACzB,QAAQ;IAC5C,CAAC;IACDuC,cAAcA,CAAC7C,KAAK,EAAE8C,UAAU,EAAE;MAC9B9C,KAAK,CAACC,KAAK,CAAC8C,OAAO,CAAChB,IAAI,IAAIA,IAAI,CAACzB,QAAQ,GAAGwC,UAAU,CAAC;IAC3D,CAAC;IACDE,cAAcA,CAAChD,KAAK,EAAE+B,IAAI,EAAE;MACxB/B,KAAK,CAAC8B,gBAAgB,CAACC,IAAI,GAAGA,IAAI;IACtC,CAAC;IACDkB,gBAAgBA,CAACjD,KAAK,EAAE;MAAE2C,MAAM;MAAEtC;IAAO,CAAC,EAAE;MACxC,MAAM0B,IAAI,GAAG/B,KAAK,CAACC,KAAK,CAACmC,IAAI,CAACQ,CAAC,IAAIA,CAAC,CAAC1C,EAAE,KAAKyC,MAAM,CAAC;MACnD,IAAIZ,IAAI,EAAEA,IAAI,CAAC1B,MAAM,GAAGA,MAAM;IAClC,CAAC;IACD6C,kBAAkBA,CAAClD,KAAK,EAAE;MAAE2C,MAAM;MAAEQ,SAAS;MAAE1C,QAAQ;MAAEI;IAAS,CAAC,EAAE;MACjE,MAAMkB,IAAI,GAAG/B,KAAK,CAACC,KAAK,CAACmC,IAAI,CAACQ,CAAC,IAAIA,CAAC,CAAC1C,EAAE,KAAKyC,MAAM,CAAC;MACnD,IAAIZ,IAAI,EAAE;QACN;QACA,MAAMqB,OAAO,GAAGrB,IAAI,CAACxB,QAAQ,CAAC6B,IAAI,CAACiB,CAAC,IAAIA,CAAC,CAACnD,EAAE,KAAKiD,SAAS,CAAC;QAC3D,IAAIC,OAAO,EAAE;UACT;UACAA,OAAO,CAAC3C,QAAQ,GAAGA,QAAQ;UAC3B,MAAM6C,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;UACtBH,OAAO,CAAC1C,kBAAkB,GAAG4C,GAAG,CAACE,cAAc,CAAC,OAAO,CAAC;UACxDzB,IAAI,CAAC1B,MAAM,GAAG,QAAQ;;UAEtB;UACA,MAAM2B,MAAM,GAAGhC,KAAK,CAACc,QAAQ,CAACsB,IAAI,CAACqB,CAAC,IAAIA,CAAC,CAACvD,EAAE,MAAMW,QAAQ,IAAIkB,IAAI,CAAClB,QAAQ,CAAC,CAAC,IAAIb,KAAK,CAACc,QAAQ,CAAC,CAAC,CAAC;UAClG,MAAM4C,UAAU,GAAG,IAAIH,IAAI,CAACD,GAAG,CAAC;UAChCI,UAAU,CAACC,OAAO,CAACD,UAAU,CAACE,OAAO,CAAC,CAAC,GAAG5B,MAAM,CAACd,UAAU,CAAC;UAC5DkC,OAAO,CAACzC,kBAAkB,GAAG+C,UAAU,CAACF,cAAc,CAAC,OAAO,CAAC;QACnE;MACJ;IACJ,CAAC;IAED;IACAK,SAASA,CAAC7D,KAAK,EAAEgC,MAAM,EAAE;MACrBhC,KAAK,CAACc,QAAQ,CAACgD,IAAI,CAAC;QAChB5D,EAAE,EAAEF,KAAK,CAACc,QAAQ,CAACiD,MAAM,GAAG,CAAC;QAC7B/C,UAAU,EAAE,CAAC;QACb,GAAGgB;MACP,CAAC,CAAC;IACN,CAAC;IACDgC,YAAYA,CAAChE,KAAK,EAAEiE,aAAa,EAAE;MAC/B,MAAMC,KAAK,GAAGlE,KAAK,CAACc,QAAQ,CAACqD,SAAS,CAACV,CAAC,IAAIA,CAAC,CAACvD,EAAE,KAAK+D,aAAa,CAAC/D,EAAE,CAAC;MACtE,IAAIgE,KAAK,KAAK,CAAC,CAAC,EAAE;QACdlE,KAAK,CAACc,QAAQ,CAACoD,KAAK,CAAC,GAAG;UAAE,GAAGlE,KAAK,CAACc,QAAQ,CAACoD,KAAK,CAAC;UAAE,GAAGD;QAAc,CAAC;MAC1E;IACJ,CAAC;IACDG,YAAYA,CAACpE,KAAK,EAAEa,QAAQ,EAAE;MAC1Bb,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAAC0B,MAAM,CAACiB,CAAC,IAAIA,CAAC,CAACvD,EAAE,KAAKW,QAAQ,CAAC;IAClE,CAAC;IACDwD,gBAAgBA,CAACrE,KAAK,EAAEgC,MAAM,EAAE;MAC5BhC,KAAK,CAAC8B,gBAAgB,CAACE,MAAM,GAAGA,MAAM;IAC1C,CAAC;IAED;IACAsC,OAAOA,CAACtE,KAAK,EAAEiC,IAAI,EAAE;MACjBjC,KAAK,CAACyB,KAAK,CAACqC,IAAI,CAAC;QACb5D,EAAE,EAAEF,KAAK,CAACyB,KAAK,CAACsC,MAAM,GAAG,CAAC;QAC1B1D,MAAM,EAAE,SAAS;QACjB,GAAG4B;MACP,CAAC,CAAC;IACN,CAAC;IACDsC,UAAUA,CAACvE,KAAK,EAAEwE,WAAW,EAAE;MAC3B,MAAMN,KAAK,GAAGlE,KAAK,CAACyB,KAAK,CAAC0C,SAAS,CAACM,CAAC,IAAIA,CAAC,CAACvE,EAAE,KAAKsE,WAAW,CAACtE,EAAE,CAAC;MACjE,IAAIgE,KAAK,KAAK,CAAC,CAAC,EAAE;QACdlE,KAAK,CAACyB,KAAK,CAACyC,KAAK,CAAC,GAAG;UAAE,GAAGlE,KAAK,CAACyB,KAAK,CAACyC,KAAK,CAAC;UAAE,GAAGM;QAAY,CAAC;MAClE;IACJ,CAAC;IACDE,UAAUA,CAAC1E,KAAK,EAAE2E,MAAM,EAAE;MACtB3E,KAAK,CAACyB,KAAK,GAAGzB,KAAK,CAACyB,KAAK,CAACe,MAAM,CAACiC,CAAC,IAAIA,CAAC,CAACvE,EAAE,KAAKyE,MAAM,CAAC;IAC1D,CAAC;IACDC,cAAcA,CAAC5E,KAAK,EAAEiC,IAAI,EAAE;MACxBjC,KAAK,CAAC8B,gBAAgB,CAACG,IAAI,GAAGA,IAAI;IACtC;EACJ,CAAC;EACD4C,OAAO,EAAE;IACL;IACA3B,kBAAkBA,CAAC;MAAE4B;IAAO,CAAC,EAAE;MAAEnC,MAAM;MAAEQ,SAAS;MAAE1C,QAAQ;MAAEI,QAAQ;MAAEkE,SAAS,GAAG;IAAS,CAAC,EAAE;MAC5F;MACA,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;QAC5BC,UAAU,CAAC,MAAM;UACb,IAAIzE,QAAQ,EAAE;YACVqE,MAAM,CAAC,oBAAoB,EAAE;cAAEnC,MAAM;cAAEQ,SAAS;cAAE1C,QAAQ;cAAEI;YAAS,CAAC,CAAC;UAC3E,CAAC,MAAM;YACHiE,MAAM,CAAC,kBAAkB,EAAE;cAAEnC,MAAM;cAAEtC,MAAM,EAAE0E;YAAU,CAAC,CAAC;UAC7D;UACAE,OAAO,CAAC,IAAI,CAAC;QACjB,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IACN,CAAC;IAEDE,cAAcA,CAAC;MAAEL,MAAM;MAAE9E;IAAM,CAAC,EAAE;MAAE2C,MAAM;MAAEnC,QAAQ;MAAEC,QAAQ;MAAEI,QAAQ;MAAED;IAAU,CAAC,EAAE;MACnF;MACA,OAAO,IAAIoE,OAAO,CAAEC,OAAO,IAAK;QAC5BC,UAAU,CAAC,MAAM;UACb,MAAMnD,IAAI,GAAG/B,KAAK,CAACC,KAAK,CAACmC,IAAI,CAACQ,CAAC,IAAIA,CAAC,CAAC1C,EAAE,KAAKyC,MAAM,CAAC;UACnD,IAAIZ,IAAI,EAAE;YACN;YACA,MAAMqD,YAAY,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGtF,KAAK,CAACC,KAAK,CAACsF,OAAO,CAAC3C,CAAC,IAAIA,CAAC,CAACrC,QAAQ,CAACiF,GAAG,CAACnC,CAAC,IAAIA,CAAC,CAACnD,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;;YAEzF;YACA,IAAIU,SAAS,EAAE;cACXmB,IAAI,CAACxB,QAAQ,CAACwC,OAAO,CAACK,OAAO,IAAI;gBAC7BA,OAAO,CAACxC,SAAS,GAAG,KAAK;cAC7B,CAAC,CAAC;YACN;;YAEA;YACA,MAAMoB,MAAM,GAAGhC,KAAK,CAACc,QAAQ,CAACsB,IAAI,CAACqB,CAAC,IAAIA,CAAC,CAACvD,EAAE,KAAKW,QAAQ,CAAC,IAAIb,KAAK,CAACc,QAAQ,CAAC,CAAC,CAAC;YAC/E,MAAMwC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;YACtB,MAAMG,UAAU,GAAG,IAAIH,IAAI,CAACD,GAAG,CAAC;YAChCI,UAAU,CAACC,OAAO,CAACD,UAAU,CAACE,OAAO,CAAC,CAAC,GAAG5B,MAAM,CAACd,UAAU,CAAC;;YAE5D;YACA,MAAMuE,UAAU,GAAG;cACfvF,EAAE,EAAEkF,YAAY;cAChB5E,QAAQ;cACRC,QAAQ;cACRC,kBAAkB,EAAE4C,GAAG,CAACE,cAAc,CAAC,OAAO,CAAC;cAC/C7C,kBAAkB,EAAE+C,UAAU,CAACF,cAAc,CAAC,OAAO,CAAC;cACtD5C;YACJ,CAAC;YAEDmB,IAAI,CAACxB,QAAQ,CAACuD,IAAI,CAAC2B,UAAU,CAAC;YAC9B1D,IAAI,CAAC1B,MAAM,GAAG,QAAQ;UAC1B;UACA4E,OAAO,CAAC,IAAI,CAAC;QACjB,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IACN,CAAC;IAEDS,wBAAwBA,CAAC;MAAEZ,MAAM;MAAE9E,KAAK;MAAE2F;IAAS,CAAC,EAAE;MAAEC,OAAO;MAAE/E;IAAS,CAAC,EAAE;MACzE;MACA,OAAOmE,OAAO,CAACa,GAAG,CACdD,OAAO,CAACJ,GAAG,CAAC7C,MAAM,IAAI;QAClB,MAAMZ,IAAI,GAAG/B,KAAK,CAACC,KAAK,CAACmC,IAAI,CAACQ,CAAC,IAAIA,CAAC,CAAC1C,EAAE,KAAKyC,MAAM,CAAC;QACnD,IAAIZ,IAAI,EAAE;UACN;UACA,OAAOiD,OAAO,CAACa,GAAG,CAAC9D,IAAI,CAACxB,QAAQ,CAACiF,GAAG,CAACpC,OAAO,IAAI;YAC5C,MAAM0C,WAAW,GAAG,IAAI,CAACH,QAAQ,CAAC,kBAAkB,EAAE;cAAE9E;YAAS,CAAC,CAAC;YACnE,OAAO8E,QAAQ,CAAC,oBAAoB,EAAE;cAClChD,MAAM;cACNQ,SAAS,EAAEC,OAAO,CAAClD,EAAE;cACrBO,QAAQ,EAAEqF,WAAW;cACrBjF;YACJ,CAAC,CAAC;UACN,CAAC,CAAC,CAAC;QACP;QACA,OAAOmE,OAAO,CAACC,OAAO,CAAC,CAAC;MAC5B,CAAC,CACL,CAAC;IACL,CAAC;IAED;IACAc,gBAAgBA,CAACC,CAAC,EAAE;MAAEnF;IAAS,CAAC,EAAE;MAC9B;MACA,MAAMoF,KAAK,GAAG,0EAA0E;MACxF,IAAIxF,QAAQ,GAAG,EAAE;MACjB,KAAK,IAAIyF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QACzBzF,QAAQ,IAAIwF,KAAK,CAACE,MAAM,CAACd,IAAI,CAACe,KAAK,CAACf,IAAI,CAACgB,MAAM,CAAC,CAAC,GAAGJ,KAAK,CAAClC,MAAM,CAAC,CAAC;MACtE;MACA,OAAOtD,QAAQ;IACnB,CAAC;IAED;IACA6F,kBAAkBA,CAAA,EAAG;MACjB;MACA,OAAO,IAAItB,OAAO,CAAEC,OAAO,IAAK;QAC5BC,UAAU,CAAC,MAAM;UACbD,OAAO,CAAC,IAAI,CAAC;QACjB,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IACN,CAAC;IAED;IACAsB,WAAWA,CAAA,EAAG;MACV;MACA,OAAO,IAAIvB,OAAO,CAAEC,OAAO,IAAK;QAC5BC,UAAU,CAAC,MAAM;UACbD,OAAO,CAAC,IAAI,CAAC;QACjB,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IACN;EACJ;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}