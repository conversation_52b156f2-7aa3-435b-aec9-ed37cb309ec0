{"ast": null, "code": "import { mapState } from 'vuex';\nexport default {\n  name: 'App',\n  data() {\n    return {\n      isDarkMode: false,\n      globalSearch: '',\n      showNotifications: false,\n      showUserMenu: false,\n      globalLoading: false,\n      loadingMessage: '处理中...',\n      notifications: [{\n        id: 1,\n        title: '密码即将过期',\n        message: '5台服务器的密码将在3天内过期',\n        time: '2分钟前',\n        icon: 'exclamation-triangle',\n        iconColor: 'text-yellow-500'\n      }, {\n        id: 2,\n        title: '批量更新完成',\n        message: '生产环境密码更新任务已完成',\n        time: '1小时前',\n        icon: 'check-circle',\n        iconColor: 'text-green-500'\n      }, {\n        id: 3,\n        title: '安全警告',\n        message: '检测到弱密码，建议立即更新',\n        time: '3小时前',\n        icon: 'shield-alt',\n        iconColor: 'text-red-500'\n      }]\n    };\n  },\n  computed: {\n    ...mapState(['hosts']),\n    routes() {\n      const baseRoutes = this.$router.options.routes.filter(route => route.meta && route.meta.title);\n\n      // 动态添加徽章信息\n      return baseRoutes.map(route => {\n        const routeCopy = {\n          ...route\n        };\n        if (route.path === '/hosts') {\n          // 计算需要注意的主机数量\n          const warningHosts = this.hosts.filter(host => host.status === 'warning' || host.status === 'error').length;\n          if (warningHosts > 0) {\n            routeCopy.meta = {\n              ...route.meta,\n              badge: warningHosts\n            };\n          }\n        }\n        return routeCopy;\n      });\n    },\n    unreadNotifications() {\n      return this.notifications.length;\n    }\n  },\n  mounted() {\n    // 初始化主题\n    this.initTheme();\n\n    // 点击外部关闭下拉菜单\n    document.addEventListener('click', this.handleClickOutside);\n  },\n  beforeUnmount() {\n    document.removeEventListener('click', this.handleClickOutside);\n  },\n  methods: {\n    initTheme() {\n      // 从本地存储读取主题设置\n      const savedTheme = localStorage.getItem('theme');\n      if (savedTheme) {\n        this.isDarkMode = savedTheme === 'dark';\n      } else {\n        // 检测系统主题偏好\n        this.isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      }\n      this.applyTheme();\n    },\n    toggleTheme() {\n      this.isDarkMode = !this.isDarkMode;\n      this.applyTheme();\n      localStorage.setItem('theme', this.isDarkMode ? 'dark' : 'light');\n    },\n    applyTheme() {\n      if (this.isDarkMode) {\n        document.documentElement.classList.add('dark');\n      } else {\n        document.documentElement.classList.remove('dark');\n      }\n    },\n    performGlobalSearch() {\n      if (this.globalSearch.trim()) {\n        // 实现全局搜索逻辑\n        console.log('搜索:', this.globalSearch);\n        // 这里可以跳转到搜索结果页面或在当前页面显示搜索结果\n      }\n    },\n    handleClickOutside(event) {\n      // 关闭通知下拉菜单\n      if (!event.target.closest('.relative') || !event.target.closest('[data-dropdown=\"notifications\"]')) {\n        this.showNotifications = false;\n      }\n\n      // 关闭用户菜单\n      if (!event.target.closest('.relative') || !event.target.closest('[data-dropdown=\"user\"]')) {\n        this.showUserMenu = false;\n      }\n    },\n    showGlobalLoading(message = '处理中...') {\n      this.loadingMessage = message;\n      this.globalLoading = true;\n    },\n    hideGlobalLoading() {\n      this.globalLoading = false;\n    }\n  }\n};", "map": {"version": 3, "names": ["mapState", "name", "data", "isDarkMode", "globalSearch", "showNotifications", "showUserMenu", "globalLoading", "loadingMessage", "notifications", "id", "title", "message", "time", "icon", "iconColor", "computed", "routes", "baseRoutes", "$router", "options", "filter", "route", "meta", "map", "routeCopy", "path", "warningHosts", "hosts", "host", "status", "length", "badge", "unreadNotifications", "mounted", "initTheme", "document", "addEventListener", "handleClickOutside", "beforeUnmount", "removeEventListener", "methods", "savedTheme", "localStorage", "getItem", "window", "matchMedia", "matches", "applyTheme", "toggleTheme", "setItem", "documentElement", "classList", "add", "remove", "performGlobalSearch", "trim", "console", "log", "event", "target", "closest", "showGlobalLoading", "hideGlobalLoading"], "sources": ["D:\\demo\\ooo\\pass\\src\\App.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\" :class=\"{ 'dark': isDarkMode }\">\r\n        <!-- 顶部导航栏 -->\r\n        <header class=\"header\">\r\n            <div class=\"container mx-auto px-6 py-4\">\r\n                <div class=\"flex justify-between items-center\">\r\n                    <!-- 左侧品牌区域 -->\r\n                    <div class=\"flex items-center space-x-4\">\r\n                        <div class=\"flex items-center space-x-3\">\r\n                            <div class=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg\">\r\n                                <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"text-white text-lg\" />\r\n                            </div>\r\n                            <div>\r\n                                <h1 class=\"text-xl font-bold text-gray-900 dark:text-white\">SecurePass</h1>\r\n                                <p class=\"text-xs text-gray-500 dark:text-gray-400\">企业密码管理平台</p>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 右侧操作区域 -->\r\n                    <div class=\"flex items-center space-x-4\">\r\n                        <!-- 全局搜索 -->\r\n                        <div class=\"relative hidden md:block\">\r\n                            <input\r\n                                type=\"text\"\r\n                                v-model=\"globalSearch\"\r\n                                placeholder=\"全局搜索...\"\r\n                                class=\"w-64 pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-700 border-0 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:text-white dark:placeholder-gray-400\"\r\n                                @keyup.enter=\"performGlobalSearch\"\r\n                            />\r\n                            <font-awesome-icon :icon=\"['fas', 'search']\" class=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\r\n                        </div>\r\n\r\n                        <!-- 通知中心 -->\r\n                        <div class=\"relative\">\r\n                            <button\r\n                                @click=\"showNotifications = !showNotifications\"\r\n                                class=\"relative p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\"\r\n                            >\r\n                                <font-awesome-icon :icon=\"['fas', 'bell']\" class=\"text-lg\" />\r\n                                <span v-if=\"unreadNotifications > 0\" class=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\r\n                                    {{ unreadNotifications }}\r\n                                </span>\r\n                            </button>\r\n\r\n                            <!-- 通知下拉菜单 -->\r\n                            <div v-if=\"showNotifications\" class=\"absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50\">\r\n                                <div class=\"p-4 border-b border-gray-200 dark:border-gray-700\">\r\n                                    <h3 class=\"font-semibold text-gray-900 dark:text-white\">通知中心</h3>\r\n                                </div>\r\n                                <div class=\"max-h-64 overflow-y-auto\">\r\n                                    <div v-for=\"notification in notifications\" :key=\"notification.id\" class=\"p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                                        <div class=\"flex items-start space-x-3\">\r\n                                            <div class=\"flex-shrink-0\">\r\n                                                <font-awesome-icon :icon=\"['fas', notification.icon]\" :class=\"notification.iconColor\" />\r\n                                            </div>\r\n                                            <div class=\"flex-1 min-w-0\">\r\n                                                <p class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ notification.title }}</p>\r\n                                                <p class=\"text-sm text-gray-500 dark:text-gray-400\">{{ notification.message }}</p>\r\n                                                <p class=\"text-xs text-gray-400 dark:text-gray-500 mt-1\">{{ notification.time }}</p>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <!-- 主题切换 -->\r\n                        <button\r\n                            @click=\"toggleTheme\"\r\n                            class=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\"\r\n                        >\r\n                            <font-awesome-icon :icon=\"['fas', isDarkMode ? 'sun' : 'moon']\" class=\"text-lg\" />\r\n                        </button>\r\n\r\n                        <!-- 用户菜单 -->\r\n                        <div class=\"relative\">\r\n                            <button\r\n                                @click=\"showUserMenu = !showUserMenu\"\r\n                                class=\"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\r\n                            >\r\n                                <div class=\"w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center\">\r\n                                    <span class=\"text-white text-sm font-semibold\">管</span>\r\n                                </div>\r\n                                <span class=\"hidden md:block text-sm font-medium text-gray-700 dark:text-gray-300\">管理员</span>\r\n                                <font-awesome-icon :icon=\"['fas', 'chevron-down']\" class=\"text-xs text-gray-400\" />\r\n                            </button>\r\n\r\n                            <!-- 用户下拉菜单 -->\r\n                            <div v-if=\"showUserMenu\" class=\"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50\">\r\n                                <div class=\"py-2\">\r\n                                    <a href=\"#\" class=\"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\">\r\n                                        <font-awesome-icon :icon=\"['fas', 'user']\" class=\"mr-3\" />\r\n                                        个人设置\r\n                                    </a>\r\n                                    <a href=\"#\" class=\"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\">\r\n                                        <font-awesome-icon :icon=\"['fas', 'cog']\" class=\"mr-3\" />\r\n                                        系统设置\r\n                                    </a>\r\n                                    <hr class=\"my-2 border-gray-200 dark:border-gray-700\">\r\n                                    <a href=\"#\" class=\"flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20\">\r\n                                        <font-awesome-icon :icon=\"['fas', 'sign-out-alt']\" class=\"mr-3\" />\r\n                                        退出登录\r\n                                    </a>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n\r\n        <!-- 主要内容区域 -->\r\n        <main class=\"flex-1 bg-gray-50 dark:bg-gray-900\">\r\n            <div class=\"container mx-auto px-6 py-6\">\r\n                <!-- 导航标签 -->\r\n                <nav class=\"nav-tabs\">\r\n                    <router-link v-for=\"route in routes\" :key=\"route.path\" :to=\"route.path\" class=\"nav-item\"\r\n                        active-class=\"active\">\r\n                        <font-awesome-icon :icon=\"['fas', route.meta.icon]\" class=\"mr-2\" />\r\n                        <span>{{ route.meta.title }}</span>\r\n                        <span v-if=\"route.meta.badge\" class=\"ml-2 px-2 py-0.5 bg-red-500 text-white text-xs rounded-full\">\r\n                            {{ route.meta.badge }}\r\n                        </span>\r\n                    </router-link>\r\n                </nav>\r\n\r\n                <!-- 路由视图 -->\r\n                <router-view v-slot=\"{ Component }\">\r\n                    <transition name=\"fade\" mode=\"out-in\">\r\n                        <component :is=\"Component\" />\r\n                    </transition>\r\n                </router-view>\r\n            </div>\r\n        </main>\r\n\r\n        <!-- 全局加载遮罩 -->\r\n        <div v-if=\"globalLoading\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n            <div class=\"bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center space-x-4\">\r\n                <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\r\n                <span class=\"text-gray-700 dark:text-gray-300\">{{ loadingMessage }}</span>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex'\r\n\r\nexport default {\r\n    name: 'App',\r\n    data() {\r\n        return {\r\n            isDarkMode: false,\r\n            globalSearch: '',\r\n            showNotifications: false,\r\n            showUserMenu: false,\r\n            globalLoading: false,\r\n            loadingMessage: '处理中...',\r\n            notifications: [\r\n                {\r\n                    id: 1,\r\n                    title: '密码即将过期',\r\n                    message: '5台服务器的密码将在3天内过期',\r\n                    time: '2分钟前',\r\n                    icon: 'exclamation-triangle',\r\n                    iconColor: 'text-yellow-500'\r\n                },\r\n                {\r\n                    id: 2,\r\n                    title: '批量更新完成',\r\n                    message: '生产环境密码更新任务已完成',\r\n                    time: '1小时前',\r\n                    icon: 'check-circle',\r\n                    iconColor: 'text-green-500'\r\n                },\r\n                {\r\n                    id: 3,\r\n                    title: '安全警告',\r\n                    message: '检测到弱密码，建议立即更新',\r\n                    time: '3小时前',\r\n                    icon: 'shield-alt',\r\n                    iconColor: 'text-red-500'\r\n                }\r\n            ]\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState(['hosts']),\r\n        routes() {\r\n            const baseRoutes = this.$router.options.routes.filter(route => route.meta && route.meta.title)\r\n\r\n            // 动态添加徽章信息\r\n            return baseRoutes.map(route => {\r\n                const routeCopy = { ...route }\r\n                if (route.path === '/hosts') {\r\n                    // 计算需要注意的主机数量\r\n                    const warningHosts = this.hosts.filter(host =>\r\n                        host.status === 'warning' || host.status === 'error'\r\n                    ).length\r\n                    if (warningHosts > 0) {\r\n                        routeCopy.meta = { ...route.meta, badge: warningHosts }\r\n                    }\r\n                }\r\n                return routeCopy\r\n            })\r\n        },\r\n        unreadNotifications() {\r\n            return this.notifications.length\r\n        }\r\n    },\r\n    mounted() {\r\n        // 初始化主题\r\n        this.initTheme()\r\n\r\n        // 点击外部关闭下拉菜单\r\n        document.addEventListener('click', this.handleClickOutside)\r\n    },\r\n    beforeUnmount() {\r\n        document.removeEventListener('click', this.handleClickOutside)\r\n    },\r\n    methods: {\r\n        initTheme() {\r\n            // 从本地存储读取主题设置\r\n            const savedTheme = localStorage.getItem('theme')\r\n            if (savedTheme) {\r\n                this.isDarkMode = savedTheme === 'dark'\r\n            } else {\r\n                // 检测系统主题偏好\r\n                this.isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches\r\n            }\r\n            this.applyTheme()\r\n        },\r\n\r\n        toggleTheme() {\r\n            this.isDarkMode = !this.isDarkMode\r\n            this.applyTheme()\r\n            localStorage.setItem('theme', this.isDarkMode ? 'dark' : 'light')\r\n        },\r\n\r\n        applyTheme() {\r\n            if (this.isDarkMode) {\r\n                document.documentElement.classList.add('dark')\r\n            } else {\r\n                document.documentElement.classList.remove('dark')\r\n            }\r\n        },\r\n\r\n        performGlobalSearch() {\r\n            if (this.globalSearch.trim()) {\r\n                // 实现全局搜索逻辑\r\n                console.log('搜索:', this.globalSearch)\r\n                // 这里可以跳转到搜索结果页面或在当前页面显示搜索结果\r\n            }\r\n        },\r\n\r\n        handleClickOutside(event) {\r\n            // 关闭通知下拉菜单\r\n            if (!event.target.closest('.relative') || !event.target.closest('[data-dropdown=\"notifications\"]')) {\r\n                this.showNotifications = false\r\n            }\r\n\r\n            // 关闭用户菜单\r\n            if (!event.target.closest('.relative') || !event.target.closest('[data-dropdown=\"user\"]')) {\r\n                this.showUserMenu = false\r\n            }\r\n        },\r\n\r\n        showGlobalLoading(message = '处理中...') {\r\n            this.loadingMessage = message\r\n            this.globalLoading = true\r\n        },\r\n\r\n        hideGlobalLoading() {\r\n            this.globalLoading = false\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 全局样式 */\r\n* {\r\n    box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n    font-family: \"Inter\", \"PingFang SC\", \"Microsoft YaHei\", -apple-system, BlinkMacSystemFont, sans-serif;\r\n    background-color: #f8fafc;\r\n    color: #1e293b;\r\n    margin: 0;\r\n    padding: 0;\r\n    line-height: 1.6;\r\n}\r\n\r\n.app-container {\r\n    min-height: 100vh;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n/* 暗色主题 */\r\n.dark body {\r\n    background-color: #0f172a;\r\n    color: #e2e8f0;\r\n}\r\n\r\n/* 头部样式 */\r\n.header {\r\n    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n    border-bottom: 1px solid #e2e8f0;\r\n    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\r\n    position: sticky;\r\n    top: 0;\r\n    z-index: 40;\r\n}\r\n\r\n.dark .header {\r\n    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);\r\n    border-bottom-color: #334155;\r\n}\r\n\r\n/* 导航标签样式 */\r\n.nav-tabs {\r\n    display: flex;\r\n    background: white;\r\n    border-radius: 12px;\r\n    padding: 4px;\r\n    margin-bottom: 2rem;\r\n    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);\r\n    overflow-x: auto;\r\n}\r\n\r\n.dark .nav-tabs {\r\n    background: #1e293b;\r\n    border: 1px solid #334155;\r\n}\r\n\r\n.nav-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px 20px;\r\n    border-radius: 8px;\r\n    color: #64748b;\r\n    text-decoration: none;\r\n    font-weight: 500;\r\n    transition: all 0.2s ease;\r\n    white-space: nowrap;\r\n    position: relative;\r\n}\r\n\r\n.nav-item:hover:not(.active) {\r\n    color: #3b82f6;\r\n    background-color: #f1f5f9;\r\n}\r\n\r\n.dark .nav-item:hover:not(.active) {\r\n    color: #60a5fa;\r\n    background-color: #334155;\r\n}\r\n\r\n.nav-item.active {\r\n    color: #3b82f6;\r\n    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);\r\n    font-weight: 600;\r\n    box-shadow: 0 2px 4px 0 rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n.dark .nav-item.active {\r\n    color: #60a5fa;\r\n    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);\r\n}\r\n\r\n/* 过渡动画 */\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n}\r\n\r\n/* 滚动条样式 */\r\n::-webkit-scrollbar {\r\n    width: 6px;\r\n    height: 6px;\r\n}\r\n\r\n::-webkit-scrollbar-track {\r\n    background: #f1f5f9;\r\n    border-radius: 3px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb {\r\n    background: #cbd5e1;\r\n    border-radius: 3px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb:hover {\r\n    background: #94a3b8;\r\n}\r\n\r\n.dark ::-webkit-scrollbar-track {\r\n    background: #334155;\r\n}\r\n\r\n.dark ::-webkit-scrollbar-thumb {\r\n    background: #64748b;\r\n}\r\n\r\n.dark ::-webkit-scrollbar-thumb:hover {\r\n    background: #94a3b8;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n    .nav-tabs {\r\n        margin: 0 -1rem 2rem -1rem;\r\n        border-radius: 0;\r\n        padding: 4px 1rem;\r\n    }\r\n\r\n    .nav-item {\r\n        padding: 10px 16px;\r\n        font-size: 14px;\r\n    }\r\n}\r\n\r\n/* 动画关键帧 */\r\n@keyframes slideIn {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateX(-20px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateX(0);\r\n    }\r\n}\r\n\r\n@keyframes fadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(20px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* 工具类 */\r\n.animate-slide-in {\r\n    animation: slideIn 0.3s ease-out;\r\n}\r\n\r\n.animate-fade-in-up {\r\n    animation: fadeInUp 0.3s ease-out;\r\n}\r\n</style>"], "mappings": "AAmJA,SAASA,QAAO,QAAS,MAAK;AAE9B,eAAe;EACXC,IAAI,EAAE,KAAK;EACXC,IAAIA,CAAA,EAAG;IACH,OAAO;MACHC,UAAU,EAAE,KAAK;MACjBC,YAAY,EAAE,EAAE;MAChBC,iBAAiB,EAAE,KAAK;MACxBC,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBC,cAAc,EAAE,QAAQ;MACxBC,aAAa,EAAE,CACX;QACIC,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,QAAQ;QACfC,OAAO,EAAE,iBAAiB;QAC1BC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE,sBAAsB;QAC5BC,SAAS,EAAE;MACf,CAAC,EACD;QACIL,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,QAAQ;QACfC,OAAO,EAAE,eAAe;QACxBC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE,cAAc;QACpBC,SAAS,EAAE;MACf,CAAC,EACD;QACIL,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE,eAAe;QACxBC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE,YAAY;QAClBC,SAAS,EAAE;MACf;IAER;EACJ,CAAC;EACDC,QAAQ,EAAE;IACN,GAAGhB,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;IACtBiB,MAAMA,CAAA,EAAG;MACL,MAAMC,UAAS,GAAI,IAAI,CAACC,OAAO,CAACC,OAAO,CAACH,MAAM,CAACI,MAAM,CAACC,KAAI,IAAKA,KAAK,CAACC,IAAG,IAAKD,KAAK,CAACC,IAAI,CAACZ,KAAK;;MAE7F;MACA,OAAOO,UAAU,CAACM,GAAG,CAACF,KAAI,IAAK;QAC3B,MAAMG,SAAQ,GAAI;UAAE,GAAGH;QAAM;QAC7B,IAAIA,KAAK,CAACI,IAAG,KAAM,QAAQ,EAAE;UACzB;UACA,MAAMC,YAAW,GAAI,IAAI,CAACC,KAAK,CAACP,MAAM,CAACQ,IAAG,IACtCA,IAAI,CAACC,MAAK,KAAM,SAAQ,IAAKD,IAAI,CAACC,MAAK,KAAM,OACjD,CAAC,CAACC,MAAK;UACP,IAAIJ,YAAW,GAAI,CAAC,EAAE;YAClBF,SAAS,CAACF,IAAG,GAAI;cAAE,GAAGD,KAAK,CAACC,IAAI;cAAES,KAAK,EAAEL;YAAa;UAC1D;QACJ;QACA,OAAOF,SAAQ;MACnB,CAAC;IACL,CAAC;IACDQ,mBAAmBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACxB,aAAa,CAACsB,MAAK;IACnC;EACJ,CAAC;EACDG,OAAOA,CAAA,EAAG;IACN;IACA,IAAI,CAACC,SAAS,CAAC;;IAEf;IACAC,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,kBAAkB;EAC9D,CAAC;EACDC,aAAaA,CAAA,EAAG;IACZH,QAAQ,CAACI,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACF,kBAAkB;EACjE,CAAC;EACDG,OAAO,EAAE;IACLN,SAASA,CAAA,EAAG;MACR;MACA,MAAMO,UAAS,GAAIC,YAAY,CAACC,OAAO,CAAC,OAAO;MAC/C,IAAIF,UAAU,EAAE;QACZ,IAAI,CAACvC,UAAS,GAAIuC,UAAS,KAAM,MAAK;MAC1C,OAAO;QACH;QACA,IAAI,CAACvC,UAAS,GAAI0C,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAM;MAC9E;MACA,IAAI,CAACC,UAAU,CAAC;IACpB,CAAC;IAEDC,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC9C,UAAS,GAAI,CAAC,IAAI,CAACA,UAAS;MACjC,IAAI,CAAC6C,UAAU,CAAC;MAChBL,YAAY,CAACO,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC/C,UAAS,GAAI,MAAK,GAAI,OAAO;IACpE,CAAC;IAED6C,UAAUA,CAAA,EAAG;MACT,IAAI,IAAI,CAAC7C,UAAU,EAAE;QACjBiC,QAAQ,CAACe,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,MAAM;MACjD,OAAO;QACHjB,QAAQ,CAACe,eAAe,CAACC,SAAS,CAACE,MAAM,CAAC,MAAM;MACpD;IACJ,CAAC;IAEDC,mBAAmBA,CAAA,EAAG;MAClB,IAAI,IAAI,CAACnD,YAAY,CAACoD,IAAI,CAAC,CAAC,EAAE;QAC1B;QACAC,OAAO,CAACC,GAAG,CAAC,KAAK,EAAE,IAAI,CAACtD,YAAY;QACpC;MACJ;IACJ,CAAC;IAEDkC,kBAAkBA,CAACqB,KAAK,EAAE;MACtB;MACA,IAAI,CAACA,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,WAAW,KAAK,CAACF,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,iCAAiC,CAAC,EAAE;QAChG,IAAI,CAACxD,iBAAgB,GAAI,KAAI;MACjC;;MAEA;MACA,IAAI,CAACsD,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,WAAW,KAAK,CAACF,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAE;QACvF,IAAI,CAACvD,YAAW,GAAI,KAAI;MAC5B;IACJ,CAAC;IAEDwD,iBAAiBA,CAAClD,OAAM,GAAI,QAAQ,EAAE;MAClC,IAAI,CAACJ,cAAa,GAAII,OAAM;MAC5B,IAAI,CAACL,aAAY,GAAI,IAAG;IAC5B,CAAC;IAEDwD,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAACxD,aAAY,GAAI,KAAI;IAC7B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}