{"ast": null, "code": "import { parse, icon, config, text } from '@fortawesome/fontawesome-svg-core';\nimport { h, defineComponent, computed, watch } from 'vue';\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nvar commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\nvar humps$1 = {\n  exports: {}\n};\n(function (module) {\n  (function (global) {\n    var _processKeys = function (convert, obj, options) {\n      if (!_isObject(obj) || _isDate(obj) || _isRegExp(obj) || _isBoolean(obj) || _isFunction(obj)) {\n        return obj;\n      }\n      var output,\n        i = 0,\n        l = 0;\n      if (_isArray(obj)) {\n        output = [];\n        for (l = obj.length; i < l; i++) {\n          output.push(_processKeys(convert, obj[i], options));\n        }\n      } else {\n        output = {};\n        for (var key in obj) {\n          if (Object.prototype.hasOwnProperty.call(obj, key)) {\n            output[convert(key, options)] = _processKeys(convert, obj[key], options);\n          }\n        }\n      }\n      return output;\n    };\n\n    // String conversion methods\n\n    var separateWords = function (string, options) {\n      options = options || {};\n      var separator = options.separator || '_';\n      var split = options.split || /(?=[A-Z])/;\n      return string.split(split).join(separator);\n    };\n    var camelize = function (string) {\n      if (_isNumerical(string)) {\n        return string;\n      }\n      string = string.replace(/[\\-_\\s]+(.)?/g, function (match, chr) {\n        return chr ? chr.toUpperCase() : '';\n      });\n      // Ensure 1st char is always lowercase\n      return string.substr(0, 1).toLowerCase() + string.substr(1);\n    };\n    var pascalize = function (string) {\n      var camelized = camelize(string);\n      // Ensure 1st char is always uppercase\n      return camelized.substr(0, 1).toUpperCase() + camelized.substr(1);\n    };\n    var decamelize = function (string, options) {\n      return separateWords(string, options).toLowerCase();\n    };\n\n    // Utilities\n    // Taken from Underscore.js\n\n    var toString = Object.prototype.toString;\n    var _isFunction = function (obj) {\n      return typeof obj === 'function';\n    };\n    var _isObject = function (obj) {\n      return obj === Object(obj);\n    };\n    var _isArray = function (obj) {\n      return toString.call(obj) == '[object Array]';\n    };\n    var _isDate = function (obj) {\n      return toString.call(obj) == '[object Date]';\n    };\n    var _isRegExp = function (obj) {\n      return toString.call(obj) == '[object RegExp]';\n    };\n    var _isBoolean = function (obj) {\n      return toString.call(obj) == '[object Boolean]';\n    };\n\n    // Performant way to determine if obj coerces to a number\n    var _isNumerical = function (obj) {\n      obj = obj - 0;\n      return obj === obj;\n    };\n\n    // Sets up function which handles processing keys\n    // allowing the convert function to be modified by a callback\n    var _processor = function (convert, options) {\n      var callback = options && 'process' in options ? options.process : options;\n      if (typeof callback !== 'function') {\n        return convert;\n      }\n      return function (string, options) {\n        return callback(string, convert, options);\n      };\n    };\n    var humps = {\n      camelize: camelize,\n      decamelize: decamelize,\n      pascalize: pascalize,\n      depascalize: decamelize,\n      camelizeKeys: function (object, options) {\n        return _processKeys(_processor(camelize, options), object);\n      },\n      decamelizeKeys: function (object, options) {\n        return _processKeys(_processor(decamelize, options), object, options);\n      },\n      pascalizeKeys: function (object, options) {\n        return _processKeys(_processor(pascalize, options), object);\n      },\n      depascalizeKeys: function () {\n        return this.decamelizeKeys.apply(this, arguments);\n      }\n    };\n    if (module.exports) {\n      module.exports = humps;\n    } else {\n      global.humps = humps;\n    }\n  })(commonjsGlobal);\n})(humps$1);\nvar humps = humps$1.exports;\nvar _excluded = [\"class\", \"style\"];\n\n/**\n * Converts a CSS style into a plain Javascript object.\n * @param {String} style The style to converts into a plain Javascript object.\n * @returns {Object}\n */\nfunction styleToObject(style) {\n  return style.split(';').map(function (s) {\n    return s.trim();\n  }).filter(function (s) {\n    return s;\n  }).reduce(function (output, pair) {\n    var idx = pair.indexOf(':');\n    var prop = humps.camelize(pair.slice(0, idx));\n    var value = pair.slice(idx + 1).trim();\n    output[prop] = value;\n    return output;\n  }, {});\n}\n\n/**\n * Converts a CSS class list into a plain Javascript object.\n * @param {Array<String>} classes The class list to convert.\n * @returns {Object}\n */\nfunction classToObject(classes) {\n  return classes.split(/\\s+/).reduce(function (output, className) {\n    output[className] = true;\n    return output;\n  }, {});\n}\n\n/**\n * Converts a FontAwesome abstract element of an icon into a Vue VNode.\n * @param {AbstractElement | String} abstractElement The element to convert.\n * @param {Object} props The user-defined props.\n * @param {Object} attrs The user-defined native HTML attributes.\n * @returns {VNode}\n */\nfunction convert(abstractElement) {\n  var props = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var attrs = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  // If the abstract element is a string, we'll just return a string render function\n  if (typeof abstractElement === 'string') {\n    return abstractElement;\n  }\n\n  // Converting abstract element children into Vue VNodes\n  var children = (abstractElement.children || []).map(function (child) {\n    return convert(child);\n  });\n\n  // Converting abstract element attributes into valid Vue format\n  var mixins = Object.keys(abstractElement.attributes || {}).reduce(function (mixins, key) {\n    var value = abstractElement.attributes[key];\n    switch (key) {\n      case 'class':\n        mixins.class = classToObject(value);\n        break;\n      case 'style':\n        mixins.style = styleToObject(value);\n        break;\n      default:\n        mixins.attrs[key] = value;\n    }\n    return mixins;\n  }, {\n    attrs: {},\n    class: {},\n    style: {}\n  });\n\n  // Now, we'll return the VNode\n  attrs.class;\n  var _attrs$style = attrs.style,\n    aStyle = _attrs$style === void 0 ? {} : _attrs$style,\n    otherAttrs = _objectWithoutProperties(attrs, _excluded);\n  return h(abstractElement.tag, _objectSpread2(_objectSpread2(_objectSpread2({}, props), {}, {\n    class: mixins.class,\n    style: _objectSpread2(_objectSpread2({}, mixins.style), aStyle)\n  }, mixins.attrs), otherAttrs), children);\n}\nvar PRODUCTION = false;\ntry {\n  PRODUCTION = process.env.NODE_ENV === 'production';\n} catch (e) {}\nfunction log() {\n  if (!PRODUCTION && console && typeof console.error === 'function') {\n    var _console;\n    (_console = console).error.apply(_console, arguments);\n  }\n}\nfunction objectWithKey(key, value) {\n  return Array.isArray(value) && value.length > 0 || !Array.isArray(value) && value ? _defineProperty({}, key, value) : {};\n}\nfunction classList(props) {\n  var _classes;\n  var classes = (_classes = {\n    'fa-spin': props.spin,\n    'fa-pulse': props.pulse,\n    'fa-fw': props.fixedWidth,\n    'fa-border': props.border,\n    'fa-li': props.listItem,\n    'fa-inverse': props.inverse,\n    'fa-flip': props.flip === true,\n    'fa-flip-horizontal': props.flip === 'horizontal' || props.flip === 'both',\n    'fa-flip-vertical': props.flip === 'vertical' || props.flip === 'both'\n  }, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_classes, \"fa-\".concat(props.size), props.size !== null), \"fa-rotate-\".concat(props.rotation), props.rotation !== null), \"fa-pull-\".concat(props.pull), props.pull !== null), 'fa-swap-opacity', props.swapOpacity), 'fa-bounce', props.bounce), 'fa-shake', props.shake), 'fa-beat', props.beat), 'fa-fade', props.fade), 'fa-beat-fade', props.beatFade), 'fa-flash', props.flash), _defineProperty(_defineProperty(_classes, 'fa-spin-pulse', props.spinPulse), 'fa-spin-reverse', props.spinReverse));\n  return Object.keys(classes).map(function (key) {\n    return classes[key] ? key : null;\n  }).filter(function (key) {\n    return key;\n  });\n}\nfunction normalizeIconArgs(icon) {\n  if (icon && _typeof(icon) === 'object' && icon.prefix && icon.iconName && icon.icon) {\n    return icon;\n  }\n  if (parse.icon) {\n    return parse.icon(icon);\n  }\n  if (icon === null) {\n    return null;\n  }\n  if (_typeof(icon) === 'object' && icon.prefix && icon.iconName) {\n    return icon;\n  }\n  if (Array.isArray(icon) && icon.length === 2) {\n    return {\n      prefix: icon[0],\n      iconName: icon[1]\n    };\n  }\n  if (typeof icon === 'string') {\n    return {\n      prefix: 'fas',\n      iconName: icon\n    };\n  }\n}\nvar FontAwesomeIcon = defineComponent({\n  name: 'FontAwesomeIcon',\n  props: {\n    border: {\n      type: Boolean,\n      default: false\n    },\n    fixedWidth: {\n      type: Boolean,\n      default: false\n    },\n    flip: {\n      type: [Boolean, String],\n      default: false,\n      validator: function validator(value) {\n        return [true, false, 'horizontal', 'vertical', 'both'].indexOf(value) > -1;\n      }\n    },\n    icon: {\n      type: [Object, Array, String],\n      required: true\n    },\n    mask: {\n      type: [Object, Array, String],\n      default: null\n    },\n    maskId: {\n      type: String,\n      default: null\n    },\n    listItem: {\n      type: Boolean,\n      default: false\n    },\n    pull: {\n      type: String,\n      default: null,\n      validator: function validator(value) {\n        return ['right', 'left'].indexOf(value) > -1;\n      }\n    },\n    pulse: {\n      type: Boolean,\n      default: false\n    },\n    rotation: {\n      type: [String, Number],\n      default: null,\n      validator: function validator(value) {\n        return [90, 180, 270].indexOf(Number.parseInt(value, 10)) > -1;\n      }\n    },\n    swapOpacity: {\n      type: Boolean,\n      default: false\n    },\n    size: {\n      type: String,\n      default: null,\n      validator: function validator(value) {\n        return ['2xs', 'xs', 'sm', 'lg', 'xl', '2xl', '1x', '2x', '3x', '4x', '5x', '6x', '7x', '8x', '9x', '10x'].indexOf(value) > -1;\n      }\n    },\n    spin: {\n      type: Boolean,\n      default: false\n    },\n    transform: {\n      type: [String, Object],\n      default: null\n    },\n    symbol: {\n      type: [Boolean, String],\n      default: false\n    },\n    title: {\n      type: String,\n      default: null\n    },\n    titleId: {\n      type: String,\n      default: null\n    },\n    inverse: {\n      type: Boolean,\n      default: false\n    },\n    bounce: {\n      type: Boolean,\n      default: false\n    },\n    shake: {\n      type: Boolean,\n      default: false\n    },\n    beat: {\n      type: Boolean,\n      default: false\n    },\n    fade: {\n      type: Boolean,\n      default: false\n    },\n    beatFade: {\n      type: Boolean,\n      default: false\n    },\n    flash: {\n      type: Boolean,\n      default: false\n    },\n    spinPulse: {\n      type: Boolean,\n      default: false\n    },\n    spinReverse: {\n      type: Boolean,\n      default: false\n    }\n  },\n  setup: function setup(props, _ref) {\n    var attrs = _ref.attrs;\n    var icon$1 = computed(function () {\n      return normalizeIconArgs(props.icon);\n    });\n    var classes = computed(function () {\n      return objectWithKey('classes', classList(props));\n    });\n    var transform = computed(function () {\n      return objectWithKey('transform', typeof props.transform === 'string' ? parse.transform(props.transform) : props.transform);\n    });\n    var mask = computed(function () {\n      return objectWithKey('mask', normalizeIconArgs(props.mask));\n    });\n    var renderedIcon = computed(function () {\n      return icon(icon$1.value, _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, classes.value), transform.value), mask.value), {}, {\n        symbol: props.symbol,\n        title: props.title,\n        titleId: props.titleId,\n        maskId: props.maskId\n      }));\n    });\n    watch(renderedIcon, function (value) {\n      if (!value) {\n        return log('Could not find one or more icon(s)', icon$1.value, mask.value);\n      }\n    }, {\n      immediate: true\n    });\n    var vnode = computed(function () {\n      return renderedIcon.value ? convert(renderedIcon.value.abstract[0], {}, attrs) : null;\n    });\n    return function () {\n      return vnode.value;\n    };\n  }\n});\nvar FontAwesomeLayers = defineComponent({\n  name: 'FontAwesomeLayers',\n  props: {\n    fixedWidth: {\n      type: Boolean,\n      default: false\n    }\n  },\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots;\n    var familyPrefix = config.familyPrefix;\n    var className = computed(function () {\n      return [\"\".concat(familyPrefix, \"-layers\")].concat(_toConsumableArray(props.fixedWidth ? [\"\".concat(familyPrefix, \"-fw\")] : []));\n    });\n    return function () {\n      return h('div', {\n        class: className.value\n      }, slots.default ? slots.default() : []);\n    };\n  }\n});\nvar FontAwesomeLayersText = defineComponent({\n  name: 'FontAwesomeLayersText',\n  props: {\n    value: {\n      type: [String, Number],\n      default: ''\n    },\n    transform: {\n      type: [String, Object],\n      default: null\n    },\n    counter: {\n      type: Boolean,\n      default: false\n    },\n    position: {\n      type: String,\n      default: null,\n      validator: function validator(value) {\n        return ['bottom-left', 'bottom-right', 'top-left', 'top-right'].indexOf(value) > -1;\n      }\n    }\n  },\n  setup: function setup(props, _ref) {\n    var attrs = _ref.attrs;\n    var familyPrefix = config.familyPrefix;\n    var classes = computed(function () {\n      return objectWithKey('classes', [].concat(_toConsumableArray(props.counter ? [\"\".concat(familyPrefix, \"-layers-counter\")] : []), _toConsumableArray(props.position ? [\"\".concat(familyPrefix, \"-layers-\").concat(props.position)] : [])));\n    });\n    var transform = computed(function () {\n      return objectWithKey('transform', typeof props.transform === 'string' ? parse.transform(props.transform) : props.transform);\n    });\n    var abstractElement = computed(function () {\n      var _text = text(props.value.toString(), _objectSpread2(_objectSpread2({}, transform.value), classes.value)),\n        abstract = _text.abstract;\n      if (props.counter) {\n        abstract[0].attributes.class = abstract[0].attributes.class.replace('fa-layers-text', '');\n      }\n      return abstract[0];\n    });\n    var vnode = computed(function () {\n      return convert(abstractElement.value, {}, attrs);\n    });\n    return function () {\n      return vnode.value;\n    };\n  }\n});\nexport { FontAwesomeIcon, FontAwesomeLayers, FontAwesomeLayersText };", "map": {"version": 3, "names": ["parse", "icon", "config", "text", "h", "defineComponent", "computed", "watch", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread2", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_toPrimitive", "Symbol", "toPrimitive", "i", "call", "TypeError", "String", "Number", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_typeof", "iterator", "constructor", "prototype", "obj", "key", "value", "configurable", "writable", "_objectWithoutPropertiesLoose", "source", "excluded", "target", "hasOwnProperty", "indexOf", "_objectWithoutProperties", "sourceSymbolKeys", "propertyIsEnumerable", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "Array", "isArray", "_arrayLikeToArray", "iter", "from", "minLen", "n", "toString", "slice", "name", "test", "len", "arr2", "commonjsGlobal", "globalThis", "window", "global", "self", "humps$1", "exports", "module", "_processKeys", "convert", "options", "_isObject", "_isDate", "_isRegExp", "_isBoolean", "_isFunction", "output", "l", "_isArray", "separateWords", "string", "separator", "split", "join", "camelize", "_isNumerical", "replace", "match", "chr", "toUpperCase", "substr", "toLowerCase", "pascalize", "camelized", "decamelize", "_processor", "callback", "process", "humps", "depascalize", "cameli<PERSON><PERSON><PERSON><PERSON>", "object", "decamelize<PERSON><PERSON>s", "pascal<PERSON><PERSON><PERSON><PERSON>", "depascalize<PERSON>eys", "_excluded", "styleToObject", "style", "map", "s", "trim", "reduce", "pair", "idx", "prop", "classToObject", "classes", "className", "abstractElement", "props", "undefined", "attrs", "children", "child", "mixins", "attributes", "class", "_attrs$style", "aStyle", "otherAttrs", "tag", "PRODUCTION", "env", "NODE_ENV", "log", "console", "error", "_console", "objectWithKey", "classList", "_classes", "spin", "pulse", "fixedWidth", "border", "listItem", "inverse", "flip", "concat", "size", "rotation", "pull", "swapOpacity", "bounce", "shake", "beat", "fade", "beatFade", "flash", "spinPulse", "spinReverse", "normalizeIconArgs", "prefix", "iconName", "FontAwesomeIcon", "type", "Boolean", "default", "validator", "required", "mask", "maskId", "parseInt", "transform", "symbol", "title", "titleId", "setup", "_ref", "icon$1", "renderedIcon", "immediate", "vnode", "abstract", "FontAwesomeLayers", "slots", "familyPrefix", "FontAwesomeLayersText", "counter", "position", "_text"], "sources": ["D:/demo/ooo/pass/node_modules/@fortawesome/vue-fontawesome/index.es.js"], "sourcesContent": ["import { parse, icon, config, text } from '@fortawesome/fontawesome-svg-core';\nimport { h, defineComponent, computed, watch } from 'vue';\n\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n\nvar humps$1 = {exports: {}};\n\n(function (module) {\n(function(global) {\n\n\t  var _processKeys = function(convert, obj, options) {\n\t    if(!_isObject(obj) || _isDate(obj) || _isRegExp(obj) || _isBoolean(obj) || _isFunction(obj)) {\n\t      return obj;\n\t    }\n\n\t    var output,\n\t        i = 0,\n\t        l = 0;\n\n\t    if(_isArray(obj)) {\n\t      output = [];\n\t      for(l=obj.length; i<l; i++) {\n\t        output.push(_processKeys(convert, obj[i], options));\n\t      }\n\t    }\n\t    else {\n\t      output = {};\n\t      for(var key in obj) {\n\t        if(Object.prototype.hasOwnProperty.call(obj, key)) {\n\t          output[convert(key, options)] = _processKeys(convert, obj[key], options);\n\t        }\n\t      }\n\t    }\n\t    return output;\n\t  };\n\n\t  // String conversion methods\n\n\t  var separateWords = function(string, options) {\n\t    options = options || {};\n\t    var separator = options.separator || '_';\n\t    var split = options.split || /(?=[A-Z])/;\n\n\t    return string.split(split).join(separator);\n\t  };\n\n\t  var camelize = function(string) {\n\t    if (_isNumerical(string)) {\n\t      return string;\n\t    }\n\t    string = string.replace(/[\\-_\\s]+(.)?/g, function(match, chr) {\n\t      return chr ? chr.toUpperCase() : '';\n\t    });\n\t    // Ensure 1st char is always lowercase\n\t    return string.substr(0, 1).toLowerCase() + string.substr(1);\n\t  };\n\n\t  var pascalize = function(string) {\n\t    var camelized = camelize(string);\n\t    // Ensure 1st char is always uppercase\n\t    return camelized.substr(0, 1).toUpperCase() + camelized.substr(1);\n\t  };\n\n\t  var decamelize = function(string, options) {\n\t    return separateWords(string, options).toLowerCase();\n\t  };\n\n\t  // Utilities\n\t  // Taken from Underscore.js\n\n\t  var toString = Object.prototype.toString;\n\n\t  var _isFunction = function(obj) {\n\t    return typeof(obj) === 'function';\n\t  };\n\t  var _isObject = function(obj) {\n\t    return obj === Object(obj);\n\t  };\n\t  var _isArray = function(obj) {\n\t    return toString.call(obj) == '[object Array]';\n\t  };\n\t  var _isDate = function(obj) {\n\t    return toString.call(obj) == '[object Date]';\n\t  };\n\t  var _isRegExp = function(obj) {\n\t    return toString.call(obj) == '[object RegExp]';\n\t  };\n\t  var _isBoolean = function(obj) {\n\t    return toString.call(obj) == '[object Boolean]';\n\t  };\n\n\t  // Performant way to determine if obj coerces to a number\n\t  var _isNumerical = function(obj) {\n\t    obj = obj - 0;\n\t    return obj === obj;\n\t  };\n\n\t  // Sets up function which handles processing keys\n\t  // allowing the convert function to be modified by a callback\n\t  var _processor = function(convert, options) {\n\t    var callback = options && 'process' in options ? options.process : options;\n\n\t    if(typeof(callback) !== 'function') {\n\t      return convert;\n\t    }\n\n\t    return function(string, options) {\n\t      return callback(string, convert, options);\n\t    }\n\t  };\n\n\t  var humps = {\n\t    camelize: camelize,\n\t    decamelize: decamelize,\n\t    pascalize: pascalize,\n\t    depascalize: decamelize,\n\t    camelizeKeys: function(object, options) {\n\t      return _processKeys(_processor(camelize, options), object);\n\t    },\n\t    decamelizeKeys: function(object, options) {\n\t      return _processKeys(_processor(decamelize, options), object, options);\n\t    },\n\t    pascalizeKeys: function(object, options) {\n\t      return _processKeys(_processor(pascalize, options), object);\n\t    },\n\t    depascalizeKeys: function () {\n\t      return this.decamelizeKeys.apply(this, arguments);\n\t    }\n\t  };\n\n\t  if (module.exports) {\n\t    module.exports = humps;\n\t  } else {\n\t    global.humps = humps;\n\t  }\n\n\t})(commonjsGlobal);\n} (humps$1));\n\nvar humps = humps$1.exports;\n\nvar _excluded = [\"class\", \"style\"];\n\n/**\n * Converts a CSS style into a plain Javascript object.\n * @param {String} style The style to converts into a plain Javascript object.\n * @returns {Object}\n */\nfunction styleToObject(style) {\n  return style.split(';').map(function (s) {\n    return s.trim();\n  }).filter(function (s) {\n    return s;\n  }).reduce(function (output, pair) {\n    var idx = pair.indexOf(':');\n    var prop = humps.camelize(pair.slice(0, idx));\n    var value = pair.slice(idx + 1).trim();\n    output[prop] = value;\n    return output;\n  }, {});\n}\n\n/**\n * Converts a CSS class list into a plain Javascript object.\n * @param {Array<String>} classes The class list to convert.\n * @returns {Object}\n */\nfunction classToObject(classes) {\n  return classes.split(/\\s+/).reduce(function (output, className) {\n    output[className] = true;\n    return output;\n  }, {});\n}\n\n/**\n * Converts a FontAwesome abstract element of an icon into a Vue VNode.\n * @param {AbstractElement | String} abstractElement The element to convert.\n * @param {Object} props The user-defined props.\n * @param {Object} attrs The user-defined native HTML attributes.\n * @returns {VNode}\n */\nfunction convert(abstractElement) {\n  var props = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var attrs = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  // If the abstract element is a string, we'll just return a string render function\n  if (typeof abstractElement === 'string') {\n    return abstractElement;\n  }\n\n  // Converting abstract element children into Vue VNodes\n  var children = (abstractElement.children || []).map(function (child) {\n    return convert(child);\n  });\n\n  // Converting abstract element attributes into valid Vue format\n  var mixins = Object.keys(abstractElement.attributes || {}).reduce(function (mixins, key) {\n    var value = abstractElement.attributes[key];\n    switch (key) {\n      case 'class':\n        mixins.class = classToObject(value);\n        break;\n      case 'style':\n        mixins.style = styleToObject(value);\n        break;\n      default:\n        mixins.attrs[key] = value;\n    }\n    return mixins;\n  }, {\n    attrs: {},\n    class: {},\n    style: {}\n  });\n\n  // Now, we'll return the VNode\n  attrs.class;\n    var _attrs$style = attrs.style,\n    aStyle = _attrs$style === void 0 ? {} : _attrs$style,\n    otherAttrs = _objectWithoutProperties(attrs, _excluded);\n  return h(abstractElement.tag, _objectSpread2(_objectSpread2(_objectSpread2({}, props), {}, {\n    class: mixins.class,\n    style: _objectSpread2(_objectSpread2({}, mixins.style), aStyle)\n  }, mixins.attrs), otherAttrs), children);\n}\n\nvar PRODUCTION = false;\ntry {\n  PRODUCTION = process.env.NODE_ENV === 'production';\n} catch (e) {}\nfunction log () {\n  if (!PRODUCTION && console && typeof console.error === 'function') {\n    var _console;\n    (_console = console).error.apply(_console, arguments);\n  }\n}\n\nfunction objectWithKey(key, value) {\n  return Array.isArray(value) && value.length > 0 || !Array.isArray(value) && value ? _defineProperty({}, key, value) : {};\n}\nfunction classList(props) {\n  var _classes;\n  var classes = (_classes = {\n    'fa-spin': props.spin,\n    'fa-pulse': props.pulse,\n    'fa-fw': props.fixedWidth,\n    'fa-border': props.border,\n    'fa-li': props.listItem,\n    'fa-inverse': props.inverse,\n    'fa-flip': props.flip === true,\n    'fa-flip-horizontal': props.flip === 'horizontal' || props.flip === 'both',\n    'fa-flip-vertical': props.flip === 'vertical' || props.flip === 'both'\n  }, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_classes, \"fa-\".concat(props.size), props.size !== null), \"fa-rotate-\".concat(props.rotation), props.rotation !== null), \"fa-pull-\".concat(props.pull), props.pull !== null), 'fa-swap-opacity', props.swapOpacity), 'fa-bounce', props.bounce), 'fa-shake', props.shake), 'fa-beat', props.beat), 'fa-fade', props.fade), 'fa-beat-fade', props.beatFade), 'fa-flash', props.flash), _defineProperty(_defineProperty(_classes, 'fa-spin-pulse', props.spinPulse), 'fa-spin-reverse', props.spinReverse));\n  return Object.keys(classes).map(function (key) {\n    return classes[key] ? key : null;\n  }).filter(function (key) {\n    return key;\n  });\n}\n\nfunction normalizeIconArgs(icon) {\n  if (icon && _typeof(icon) === 'object' && icon.prefix && icon.iconName && icon.icon) {\n    return icon;\n  }\n  if (parse.icon) {\n    return parse.icon(icon);\n  }\n  if (icon === null) {\n    return null;\n  }\n  if (_typeof(icon) === 'object' && icon.prefix && icon.iconName) {\n    return icon;\n  }\n  if (Array.isArray(icon) && icon.length === 2) {\n    return {\n      prefix: icon[0],\n      iconName: icon[1]\n    };\n  }\n  if (typeof icon === 'string') {\n    return {\n      prefix: 'fas',\n      iconName: icon\n    };\n  }\n}\nvar FontAwesomeIcon = defineComponent({\n  name: 'FontAwesomeIcon',\n  props: {\n    border: {\n      type: Boolean,\n      default: false\n    },\n    fixedWidth: {\n      type: Boolean,\n      default: false\n    },\n    flip: {\n      type: [Boolean, String],\n      default: false,\n      validator: function validator(value) {\n        return [true, false, 'horizontal', 'vertical', 'both'].indexOf(value) > -1;\n      }\n    },\n    icon: {\n      type: [Object, Array, String],\n      required: true\n    },\n    mask: {\n      type: [Object, Array, String],\n      default: null\n    },\n    maskId: {\n      type: String,\n      default: null\n    },\n    listItem: {\n      type: Boolean,\n      default: false\n    },\n    pull: {\n      type: String,\n      default: null,\n      validator: function validator(value) {\n        return ['right', 'left'].indexOf(value) > -1;\n      }\n    },\n    pulse: {\n      type: Boolean,\n      default: false\n    },\n    rotation: {\n      type: [String, Number],\n      default: null,\n      validator: function validator(value) {\n        return [90, 180, 270].indexOf(Number.parseInt(value, 10)) > -1;\n      }\n    },\n    swapOpacity: {\n      type: Boolean,\n      default: false\n    },\n    size: {\n      type: String,\n      default: null,\n      validator: function validator(value) {\n        return ['2xs', 'xs', 'sm', 'lg', 'xl', '2xl', '1x', '2x', '3x', '4x', '5x', '6x', '7x', '8x', '9x', '10x'].indexOf(value) > -1;\n      }\n    },\n    spin: {\n      type: Boolean,\n      default: false\n    },\n    transform: {\n      type: [String, Object],\n      default: null\n    },\n    symbol: {\n      type: [Boolean, String],\n      default: false\n    },\n    title: {\n      type: String,\n      default: null\n    },\n    titleId: {\n      type: String,\n      default: null\n    },\n    inverse: {\n      type: Boolean,\n      default: false\n    },\n    bounce: {\n      type: Boolean,\n      default: false\n    },\n    shake: {\n      type: Boolean,\n      default: false\n    },\n    beat: {\n      type: Boolean,\n      default: false\n    },\n    fade: {\n      type: Boolean,\n      default: false\n    },\n    beatFade: {\n      type: Boolean,\n      default: false\n    },\n    flash: {\n      type: Boolean,\n      default: false\n    },\n    spinPulse: {\n      type: Boolean,\n      default: false\n    },\n    spinReverse: {\n      type: Boolean,\n      default: false\n    }\n  },\n  setup: function setup(props, _ref) {\n    var attrs = _ref.attrs;\n    var icon$1 = computed(function () {\n      return normalizeIconArgs(props.icon);\n    });\n    var classes = computed(function () {\n      return objectWithKey('classes', classList(props));\n    });\n    var transform = computed(function () {\n      return objectWithKey('transform', typeof props.transform === 'string' ? parse.transform(props.transform) : props.transform);\n    });\n    var mask = computed(function () {\n      return objectWithKey('mask', normalizeIconArgs(props.mask));\n    });\n    var renderedIcon = computed(function () {\n      return icon(icon$1.value, _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, classes.value), transform.value), mask.value), {}, {\n        symbol: props.symbol,\n        title: props.title,\n        titleId: props.titleId,\n        maskId: props.maskId\n      }));\n    });\n    watch(renderedIcon, function (value) {\n      if (!value) {\n        return log('Could not find one or more icon(s)', icon$1.value, mask.value);\n      }\n    }, {\n      immediate: true\n    });\n    var vnode = computed(function () {\n      return renderedIcon.value ? convert(renderedIcon.value.abstract[0], {}, attrs) : null;\n    });\n    return function () {\n      return vnode.value;\n    };\n  }\n});\n\nvar FontAwesomeLayers = defineComponent({\n  name: 'FontAwesomeLayers',\n  props: {\n    fixedWidth: {\n      type: Boolean,\n      default: false\n    }\n  },\n  setup: function setup(props, _ref) {\n    var slots = _ref.slots;\n    var familyPrefix = config.familyPrefix;\n    var className = computed(function () {\n      return [\"\".concat(familyPrefix, \"-layers\")].concat(_toConsumableArray(props.fixedWidth ? [\"\".concat(familyPrefix, \"-fw\")] : []));\n    });\n    return function () {\n      return h('div', {\n        class: className.value\n      }, slots.default ? slots.default() : []);\n    };\n  }\n});\n\nvar FontAwesomeLayersText = defineComponent({\n  name: 'FontAwesomeLayersText',\n  props: {\n    value: {\n      type: [String, Number],\n      default: ''\n    },\n    transform: {\n      type: [String, Object],\n      default: null\n    },\n    counter: {\n      type: Boolean,\n      default: false\n    },\n    position: {\n      type: String,\n      default: null,\n      validator: function validator(value) {\n        return ['bottom-left', 'bottom-right', 'top-left', 'top-right'].indexOf(value) > -1;\n      }\n    }\n  },\n  setup: function setup(props, _ref) {\n    var attrs = _ref.attrs;\n    var familyPrefix = config.familyPrefix;\n    var classes = computed(function () {\n      return objectWithKey('classes', [].concat(_toConsumableArray(props.counter ? [\"\".concat(familyPrefix, \"-layers-counter\")] : []), _toConsumableArray(props.position ? [\"\".concat(familyPrefix, \"-layers-\").concat(props.position)] : [])));\n    });\n    var transform = computed(function () {\n      return objectWithKey('transform', typeof props.transform === 'string' ? parse.transform(props.transform) : props.transform);\n    });\n    var abstractElement = computed(function () {\n      var _text = text(props.value.toString(), _objectSpread2(_objectSpread2({}, transform.value), classes.value)),\n        abstract = _text.abstract;\n      if (props.counter) {\n        abstract[0].attributes.class = abstract[0].attributes.class.replace('fa-layers-text', '');\n      }\n      return abstract[0];\n    });\n    var vnode = computed(function () {\n      return convert(abstractElement.value, {}, attrs);\n    });\n    return function () {\n      return vnode.value;\n    };\n  }\n});\n\nexport { FontAwesomeIcon, FontAwesomeLayers, FontAwesomeLayersText };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,QAAQ,mCAAmC;AAC7E,SAASC,CAAC,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,KAAK;AAEzD,SAASC,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACrB,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EACtB,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAChC,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IACvCC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAC9B,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IACzD,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EACzB;EACA,OAAOJ,CAAC;AACV;AACA,SAASU,cAAcA,CAACZ,CAAC,EAAE;EACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IACzC,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAChDA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAClDe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAChJE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;EACJ;EACA,OAAOD,CAAC;AACV;AACA,SAASoB,YAAYA,CAAClB,CAAC,EAAED,CAAC,EAAE;EAC1B,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EACxC,IAAIF,CAAC,GAAGE,CAAC,CAACmB,MAAM,CAACC,WAAW,CAAC;EAC7B,IAAI,KAAK,CAAC,KAAKtB,CAAC,EAAE;IAChB,IAAIuB,CAAC,GAAGvB,CAAC,CAACwB,IAAI,CAACtB,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IACjC,IAAI,QAAQ,IAAI,OAAOsB,CAAC,EAAE,OAAOA,CAAC;IAClC,MAAM,IAAIE,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAAC,QAAQ,KAAKxB,CAAC,GAAGyB,MAAM,GAAGC,MAAM,EAAEzB,CAAC,CAAC;AAC9C;AACA,SAAS0B,cAAcA,CAAC1B,CAAC,EAAE;EACzB,IAAIqB,CAAC,GAAGH,YAAY,CAAClB,CAAC,EAAE,QAAQ,CAAC;EACjC,OAAO,QAAQ,IAAI,OAAOqB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAC1C;AACA,SAASM,OAAOA,CAACvB,CAAC,EAAE;EAClB,yBAAyB;;EAEzB,OAAOuB,OAAO,GAAG,UAAU,IAAI,OAAOR,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACS,QAAQ,GAAG,UAAUxB,CAAC,EAAE;IAChG,OAAO,OAAOA,CAAC;EACjB,CAAC,GAAG,UAAUA,CAAC,EAAE;IACf,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOe,MAAM,IAAIf,CAAC,CAACyB,WAAW,KAAKV,MAAM,IAAIf,CAAC,KAAKe,MAAM,CAACW,SAAS,GAAG,QAAQ,GAAG,OAAO1B,CAAC;EACrH,CAAC,EAAEuB,OAAO,CAACvB,CAAC,CAAC;AACf;AACA,SAASU,eAAeA,CAACiB,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAE;EACxCD,GAAG,GAAGN,cAAc,CAACM,GAAG,CAAC;EACzB,IAAIA,GAAG,IAAID,GAAG,EAAE;IACd9B,MAAM,CAACgB,cAAc,CAACc,GAAG,EAAEC,GAAG,EAAE;MAC9BC,KAAK,EAAEA,KAAK;MACZ1B,UAAU,EAAE,IAAI;MAChB2B,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLJ,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;EAClB;EACA,OAAOF,GAAG;AACZ;AACA,SAASK,6BAA6BA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EACvD,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAC7B,IAAIE,MAAM,GAAG,CAAC,CAAC;EACf,KAAK,IAAIP,GAAG,IAAIK,MAAM,EAAE;IACtB,IAAIpC,MAAM,CAAC6B,SAAS,CAACU,cAAc,CAAClB,IAAI,CAACe,MAAM,EAAEL,GAAG,CAAC,EAAE;MACrD,IAAIM,QAAQ,CAACG,OAAO,CAACT,GAAG,CAAC,IAAI,CAAC,EAAE;MAChCO,MAAM,CAACP,GAAG,CAAC,GAAGK,MAAM,CAACL,GAAG,CAAC;IAC3B;EACF;EACA,OAAOO,MAAM;AACf;AACA,SAASG,wBAAwBA,CAACL,MAAM,EAAEC,QAAQ,EAAE;EAClD,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAC7B,IAAIE,MAAM,GAAGH,6BAA6B,CAACC,MAAM,EAAEC,QAAQ,CAAC;EAC5D,IAAIN,GAAG,EAAEX,CAAC;EACV,IAAIpB,MAAM,CAACE,qBAAqB,EAAE;IAChC,IAAIwC,gBAAgB,GAAG1C,MAAM,CAACE,qBAAqB,CAACkC,MAAM,CAAC;IAC3D,KAAKhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,gBAAgB,CAAC/B,MAAM,EAAES,CAAC,EAAE,EAAE;MAC5CW,GAAG,GAAGW,gBAAgB,CAACtB,CAAC,CAAC;MACzB,IAAIiB,QAAQ,CAACG,OAAO,CAACT,GAAG,CAAC,IAAI,CAAC,EAAE;MAChC,IAAI,CAAC/B,MAAM,CAAC6B,SAAS,CAACc,oBAAoB,CAACtB,IAAI,CAACe,MAAM,EAAEL,GAAG,CAAC,EAAE;MAC9DO,MAAM,CAACP,GAAG,CAAC,GAAGK,MAAM,CAACL,GAAG,CAAC;IAC3B;EACF;EACA,OAAOO,MAAM;AACf;AACA,SAASM,kBAAkBA,CAACC,GAAG,EAAE;EAC/B,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;AACrH;AACA,SAASH,kBAAkBA,CAACD,GAAG,EAAE;EAC/B,IAAIK,KAAK,CAACC,OAAO,CAACN,GAAG,CAAC,EAAE,OAAOO,iBAAiB,CAACP,GAAG,CAAC;AACvD;AACA,SAASE,gBAAgBA,CAACM,IAAI,EAAE;EAC9B,IAAI,OAAOnC,MAAM,KAAK,WAAW,IAAImC,IAAI,CAACnC,MAAM,CAACS,QAAQ,CAAC,IAAI,IAAI,IAAI0B,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACI,IAAI,CAACD,IAAI,CAAC;AAC3H;AACA,SAASL,2BAA2BA,CAAC7C,CAAC,EAAEoD,MAAM,EAAE;EAC9C,IAAI,CAACpD,CAAC,EAAE;EACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOiD,iBAAiB,CAACjD,CAAC,EAAEoD,MAAM,CAAC;EAC9D,IAAIC,CAAC,GAAGxD,MAAM,CAAC6B,SAAS,CAAC4B,QAAQ,CAACpC,IAAI,CAAClB,CAAC,CAAC,CAACuD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,IAAIF,CAAC,KAAK,QAAQ,IAAIrD,CAAC,CAACyB,WAAW,EAAE4B,CAAC,GAAGrD,CAAC,CAACyB,WAAW,CAAC+B,IAAI;EAC3D,IAAIH,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAON,KAAK,CAACI,IAAI,CAACnD,CAAC,CAAC;EACpD,IAAIqD,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACI,IAAI,CAACJ,CAAC,CAAC,EAAE,OAAOJ,iBAAiB,CAACjD,CAAC,EAAEoD,MAAM,CAAC;AAClH;AACA,SAASH,iBAAiBA,CAACP,GAAG,EAAEgB,GAAG,EAAE;EACnC,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGhB,GAAG,CAAClC,MAAM,EAAEkD,GAAG,GAAGhB,GAAG,CAAClC,MAAM;EACrD,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAE0C,IAAI,GAAG,IAAIZ,KAAK,CAACW,GAAG,CAAC,EAAEzC,CAAC,GAAGyC,GAAG,EAAEzC,CAAC,EAAE,EAAE0C,IAAI,CAAC1C,CAAC,CAAC,GAAGyB,GAAG,CAACzB,CAAC,CAAC;EACrE,OAAO0C,IAAI;AACb;AACA,SAASb,kBAAkBA,CAAA,EAAG;EAC5B,MAAM,IAAI3B,SAAS,CAAC,sIAAsI,CAAC;AAC7J;AAEA,IAAIyC,cAAc,GAAG,OAAOC,UAAU,KAAK,WAAW,GAAGA,UAAU,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,OAAOC,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,CAAC,CAAC;AAE/L,IAAIC,OAAO,GAAG;EAACC,OAAO,EAAE,CAAC;AAAC,CAAC;AAE1B,WAAUC,MAAM,EAAE;EACnB,CAAC,UAASJ,MAAM,EAAE;IAEf,IAAIK,YAAY,GAAG,SAAAA,CAASC,OAAO,EAAE1C,GAAG,EAAE2C,OAAO,EAAE;MACjD,IAAG,CAACC,SAAS,CAAC5C,GAAG,CAAC,IAAI6C,OAAO,CAAC7C,GAAG,CAAC,IAAI8C,SAAS,CAAC9C,GAAG,CAAC,IAAI+C,UAAU,CAAC/C,GAAG,CAAC,IAAIgD,WAAW,CAAChD,GAAG,CAAC,EAAE;QAC3F,OAAOA,GAAG;MACZ;MAEA,IAAIiD,MAAM;QACN3D,CAAC,GAAG,CAAC;QACL4D,CAAC,GAAG,CAAC;MAET,IAAGC,QAAQ,CAACnD,GAAG,CAAC,EAAE;QAChBiD,MAAM,GAAG,EAAE;QACX,KAAIC,CAAC,GAAClD,GAAG,CAACnB,MAAM,EAAES,CAAC,GAAC4D,CAAC,EAAE5D,CAAC,EAAE,EAAE;UAC1B2D,MAAM,CAACxE,IAAI,CAACgE,YAAY,CAACC,OAAO,EAAE1C,GAAG,CAACV,CAAC,CAAC,EAAEqD,OAAO,CAAC,CAAC;QACrD;MACF,CAAC,MACI;QACHM,MAAM,GAAG,CAAC,CAAC;QACX,KAAI,IAAIhD,GAAG,IAAID,GAAG,EAAE;UAClB,IAAG9B,MAAM,CAAC6B,SAAS,CAACU,cAAc,CAAClB,IAAI,CAACS,GAAG,EAAEC,GAAG,CAAC,EAAE;YACjDgD,MAAM,CAACP,OAAO,CAACzC,GAAG,EAAE0C,OAAO,CAAC,CAAC,GAAGF,YAAY,CAACC,OAAO,EAAE1C,GAAG,CAACC,GAAG,CAAC,EAAE0C,OAAO,CAAC;UAC1E;QACF;MACF;MACA,OAAOM,MAAM;IACf,CAAC;;IAED;;IAEA,IAAIG,aAAa,GAAG,SAAAA,CAASC,MAAM,EAAEV,OAAO,EAAE;MAC5CA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;MACvB,IAAIW,SAAS,GAAGX,OAAO,CAACW,SAAS,IAAI,GAAG;MACxC,IAAIC,KAAK,GAAGZ,OAAO,CAACY,KAAK,IAAI,WAAW;MAExC,OAAOF,MAAM,CAACE,KAAK,CAACA,KAAK,CAAC,CAACC,IAAI,CAACF,SAAS,CAAC;IAC5C,CAAC;IAED,IAAIG,QAAQ,GAAG,SAAAA,CAASJ,MAAM,EAAE;MAC9B,IAAIK,YAAY,CAACL,MAAM,CAAC,EAAE;QACxB,OAAOA,MAAM;MACf;MACAA,MAAM,GAAGA,MAAM,CAACM,OAAO,CAAC,eAAe,EAAE,UAASC,KAAK,EAAEC,GAAG,EAAE;QAC5D,OAAOA,GAAG,GAAGA,GAAG,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE;MACrC,CAAC,CAAC;MACF;MACA,OAAOT,MAAM,CAACU,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGX,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED,IAAIE,SAAS,GAAG,SAAAA,CAASZ,MAAM,EAAE;MAC/B,IAAIa,SAAS,GAAGT,QAAQ,CAACJ,MAAM,CAAC;MAChC;MACA,OAAOa,SAAS,CAACH,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAACD,WAAW,CAAC,CAAC,GAAGI,SAAS,CAACH,MAAM,CAAC,CAAC,CAAC;IACnE,CAAC;IAED,IAAII,UAAU,GAAG,SAAAA,CAASd,MAAM,EAAEV,OAAO,EAAE;MACzC,OAAOS,aAAa,CAACC,MAAM,EAAEV,OAAO,CAAC,CAACqB,WAAW,CAAC,CAAC;IACrD,CAAC;;IAED;IACA;;IAEA,IAAIrC,QAAQ,GAAGzD,MAAM,CAAC6B,SAAS,CAAC4B,QAAQ;IAExC,IAAIqB,WAAW,GAAG,SAAAA,CAAShD,GAAG,EAAE;MAC9B,OAAO,OAAOA,GAAI,KAAK,UAAU;IACnC,CAAC;IACD,IAAI4C,SAAS,GAAG,SAAAA,CAAS5C,GAAG,EAAE;MAC5B,OAAOA,GAAG,KAAK9B,MAAM,CAAC8B,GAAG,CAAC;IAC5B,CAAC;IACD,IAAImD,QAAQ,GAAG,SAAAA,CAASnD,GAAG,EAAE;MAC3B,OAAO2B,QAAQ,CAACpC,IAAI,CAACS,GAAG,CAAC,IAAI,gBAAgB;IAC/C,CAAC;IACD,IAAI6C,OAAO,GAAG,SAAAA,CAAS7C,GAAG,EAAE;MAC1B,OAAO2B,QAAQ,CAACpC,IAAI,CAACS,GAAG,CAAC,IAAI,eAAe;IAC9C,CAAC;IACD,IAAI8C,SAAS,GAAG,SAAAA,CAAS9C,GAAG,EAAE;MAC5B,OAAO2B,QAAQ,CAACpC,IAAI,CAACS,GAAG,CAAC,IAAI,iBAAiB;IAChD,CAAC;IACD,IAAI+C,UAAU,GAAG,SAAAA,CAAS/C,GAAG,EAAE;MAC7B,OAAO2B,QAAQ,CAACpC,IAAI,CAACS,GAAG,CAAC,IAAI,kBAAkB;IACjD,CAAC;;IAED;IACA,IAAI0D,YAAY,GAAG,SAAAA,CAAS1D,GAAG,EAAE;MAC/BA,GAAG,GAAGA,GAAG,GAAG,CAAC;MACb,OAAOA,GAAG,KAAKA,GAAG;IACpB,CAAC;;IAED;IACA;IACA,IAAIoE,UAAU,GAAG,SAAAA,CAAS1B,OAAO,EAAEC,OAAO,EAAE;MAC1C,IAAI0B,QAAQ,GAAG1B,OAAO,IAAI,SAAS,IAAIA,OAAO,GAAGA,OAAO,CAAC2B,OAAO,GAAG3B,OAAO;MAE1E,IAAG,OAAO0B,QAAS,KAAK,UAAU,EAAE;QAClC,OAAO3B,OAAO;MAChB;MAEA,OAAO,UAASW,MAAM,EAAEV,OAAO,EAAE;QAC/B,OAAO0B,QAAQ,CAAChB,MAAM,EAAEX,OAAO,EAAEC,OAAO,CAAC;MAC3C,CAAC;IACH,CAAC;IAED,IAAI4B,KAAK,GAAG;MACVd,QAAQ,EAAEA,QAAQ;MAClBU,UAAU,EAAEA,UAAU;MACtBF,SAAS,EAAEA,SAAS;MACpBO,WAAW,EAAEL,UAAU;MACvBM,YAAY,EAAE,SAAAA,CAASC,MAAM,EAAE/B,OAAO,EAAE;QACtC,OAAOF,YAAY,CAAC2B,UAAU,CAACX,QAAQ,EAAEd,OAAO,CAAC,EAAE+B,MAAM,CAAC;MAC5D,CAAC;MACDC,cAAc,EAAE,SAAAA,CAASD,MAAM,EAAE/B,OAAO,EAAE;QACxC,OAAOF,YAAY,CAAC2B,UAAU,CAACD,UAAU,EAAExB,OAAO,CAAC,EAAE+B,MAAM,EAAE/B,OAAO,CAAC;MACvE,CAAC;MACDiC,aAAa,EAAE,SAAAA,CAASF,MAAM,EAAE/B,OAAO,EAAE;QACvC,OAAOF,YAAY,CAAC2B,UAAU,CAACH,SAAS,EAAEtB,OAAO,CAAC,EAAE+B,MAAM,CAAC;MAC7D,CAAC;MACDG,eAAe,EAAE,SAAAA,CAAA,EAAY;QAC3B,OAAO,IAAI,CAACF,cAAc,CAACjG,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;MACnD;IACF,CAAC;IAED,IAAI4D,MAAM,CAACD,OAAO,EAAE;MAClBC,MAAM,CAACD,OAAO,GAAGgC,KAAK;IACxB,CAAC,MAAM;MACLnC,MAAM,CAACmC,KAAK,GAAGA,KAAK;IACtB;EAEF,CAAC,EAAEtC,cAAc,CAAC;AACnB,CAAC,EAAEK,OAAO,CAAC;AAEX,IAAIiC,KAAK,GAAGjC,OAAO,CAACC,OAAO;AAE3B,IAAIuC,SAAS,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;;AAElC;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAACzB,KAAK,CAAC,GAAG,CAAC,CAAC0B,GAAG,CAAC,UAAUC,CAAC,EAAE;IACvC,OAAOA,CAAC,CAACC,IAAI,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC7G,MAAM,CAAC,UAAU4G,CAAC,EAAE;IACrB,OAAOA,CAAC;EACV,CAAC,CAAC,CAACE,MAAM,CAAC,UAAUnC,MAAM,EAAEoC,IAAI,EAAE;IAChC,IAAIC,GAAG,GAAGD,IAAI,CAAC3E,OAAO,CAAC,GAAG,CAAC;IAC3B,IAAI6E,IAAI,GAAGhB,KAAK,CAACd,QAAQ,CAAC4B,IAAI,CAACzD,KAAK,CAAC,CAAC,EAAE0D,GAAG,CAAC,CAAC;IAC7C,IAAIpF,KAAK,GAAGmF,IAAI,CAACzD,KAAK,CAAC0D,GAAG,GAAG,CAAC,CAAC,CAACH,IAAI,CAAC,CAAC;IACtClC,MAAM,CAACsC,IAAI,CAAC,GAAGrF,KAAK;IACpB,OAAO+C,MAAM;EACf,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASuC,aAAaA,CAACC,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAAClC,KAAK,CAAC,KAAK,CAAC,CAAC6B,MAAM,CAAC,UAAUnC,MAAM,EAAEyC,SAAS,EAAE;IAC9DzC,MAAM,CAACyC,SAAS,CAAC,GAAG,IAAI;IACxB,OAAOzC,MAAM;EACf,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASP,OAAOA,CAACiD,eAAe,EAAE;EAChC,IAAIC,KAAK,GAAGhH,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKiH,SAAS,GAAGjH,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAClF,IAAIkH,KAAK,GAAGlH,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKiH,SAAS,GAAGjH,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAClF;EACA,IAAI,OAAO+G,eAAe,KAAK,QAAQ,EAAE;IACvC,OAAOA,eAAe;EACxB;;EAEA;EACA,IAAII,QAAQ,GAAG,CAACJ,eAAe,CAACI,QAAQ,IAAI,EAAE,EAAEd,GAAG,CAAC,UAAUe,KAAK,EAAE;IACnE,OAAOtD,OAAO,CAACsD,KAAK,CAAC;EACvB,CAAC,CAAC;;EAEF;EACA,IAAIC,MAAM,GAAG/H,MAAM,CAACC,IAAI,CAACwH,eAAe,CAACO,UAAU,IAAI,CAAC,CAAC,CAAC,CAACd,MAAM,CAAC,UAAUa,MAAM,EAAEhG,GAAG,EAAE;IACvF,IAAIC,KAAK,GAAGyF,eAAe,CAACO,UAAU,CAACjG,GAAG,CAAC;IAC3C,QAAQA,GAAG;MACT,KAAK,OAAO;QACVgG,MAAM,CAACE,KAAK,GAAGX,aAAa,CAACtF,KAAK,CAAC;QACnC;MACF,KAAK,OAAO;QACV+F,MAAM,CAACjB,KAAK,GAAGD,aAAa,CAAC7E,KAAK,CAAC;QACnC;MACF;QACE+F,MAAM,CAACH,KAAK,CAAC7F,GAAG,CAAC,GAAGC,KAAK;IAC7B;IACA,OAAO+F,MAAM;EACf,CAAC,EAAE;IACDH,KAAK,EAAE,CAAC,CAAC;IACTK,KAAK,EAAE,CAAC,CAAC;IACTnB,KAAK,EAAE,CAAC;EACV,CAAC,CAAC;;EAEF;EACAc,KAAK,CAACK,KAAK;EACT,IAAIC,YAAY,GAAGN,KAAK,CAACd,KAAK;IAC9BqB,MAAM,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,YAAY;IACpDE,UAAU,GAAG3F,wBAAwB,CAACmF,KAAK,EAAEhB,SAAS,CAAC;EACzD,OAAOpH,CAAC,CAACiI,eAAe,CAACY,GAAG,EAAE5H,cAAc,CAACA,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEiH,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IACzFO,KAAK,EAAEF,MAAM,CAACE,KAAK;IACnBnB,KAAK,EAAErG,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEsH,MAAM,CAACjB,KAAK,CAAC,EAAEqB,MAAM;EAChE,CAAC,EAAEJ,MAAM,CAACH,KAAK,CAAC,EAAEQ,UAAU,CAAC,EAAEP,QAAQ,CAAC;AAC1C;AAEA,IAAIS,UAAU,GAAG,KAAK;AACtB,IAAI;EACFA,UAAU,GAAGlC,OAAO,CAACmC,GAAG,CAACC,QAAQ,KAAK,YAAY;AACpD,CAAC,CAAC,OAAO3I,CAAC,EAAE,CAAC;AACb,SAAS4I,GAAGA,CAAA,EAAI;EACd,IAAI,CAACH,UAAU,IAAII,OAAO,IAAI,OAAOA,OAAO,CAACC,KAAK,KAAK,UAAU,EAAE;IACjE,IAAIC,QAAQ;IACZ,CAACA,QAAQ,GAAGF,OAAO,EAAEC,KAAK,CAACnI,KAAK,CAACoI,QAAQ,EAAElI,SAAS,CAAC;EACvD;AACF;AAEA,SAASmI,aAAaA,CAAC9G,GAAG,EAAEC,KAAK,EAAE;EACjC,OAAOkB,KAAK,CAACC,OAAO,CAACnB,KAAK,CAAC,IAAIA,KAAK,CAACrB,MAAM,GAAG,CAAC,IAAI,CAACuC,KAAK,CAACC,OAAO,CAACnB,KAAK,CAAC,IAAIA,KAAK,GAAGnB,eAAe,CAAC,CAAC,CAAC,EAAEkB,GAAG,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1H;AACA,SAAS8G,SAASA,CAACpB,KAAK,EAAE;EACxB,IAAIqB,QAAQ;EACZ,IAAIxB,OAAO,IAAIwB,QAAQ,GAAG;IACxB,SAAS,EAAErB,KAAK,CAACsB,IAAI;IACrB,UAAU,EAAEtB,KAAK,CAACuB,KAAK;IACvB,OAAO,EAAEvB,KAAK,CAACwB,UAAU;IACzB,WAAW,EAAExB,KAAK,CAACyB,MAAM;IACzB,OAAO,EAAEzB,KAAK,CAAC0B,QAAQ;IACvB,YAAY,EAAE1B,KAAK,CAAC2B,OAAO;IAC3B,SAAS,EAAE3B,KAAK,CAAC4B,IAAI,KAAK,IAAI;IAC9B,oBAAoB,EAAE5B,KAAK,CAAC4B,IAAI,KAAK,YAAY,IAAI5B,KAAK,CAAC4B,IAAI,KAAK,MAAM;IAC1E,kBAAkB,EAAE5B,KAAK,CAAC4B,IAAI,KAAK,UAAU,IAAI5B,KAAK,CAAC4B,IAAI,KAAK;EAClE,CAAC,EAAEzI,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACkI,QAAQ,EAAE,KAAK,CAACQ,MAAM,CAAC7B,KAAK,CAAC8B,IAAI,CAAC,EAAE9B,KAAK,CAAC8B,IAAI,KAAK,IAAI,CAAC,EAAE,YAAY,CAACD,MAAM,CAAC7B,KAAK,CAAC+B,QAAQ,CAAC,EAAE/B,KAAK,CAAC+B,QAAQ,KAAK,IAAI,CAAC,EAAE,UAAU,CAACF,MAAM,CAAC7B,KAAK,CAACgC,IAAI,CAAC,EAAEhC,KAAK,CAACgC,IAAI,KAAK,IAAI,CAAC,EAAE,iBAAiB,EAAEhC,KAAK,CAACiC,WAAW,CAAC,EAAE,WAAW,EAAEjC,KAAK,CAACkC,MAAM,CAAC,EAAE,UAAU,EAAElC,KAAK,CAACmC,KAAK,CAAC,EAAE,SAAS,EAAEnC,KAAK,CAACoC,IAAI,CAAC,EAAE,SAAS,EAAEpC,KAAK,CAACqC,IAAI,CAAC,EAAE,cAAc,EAAErC,KAAK,CAACsC,QAAQ,CAAC,EAAE,UAAU,EAAEtC,KAAK,CAACuC,KAAK,CAAC,EAAEpJ,eAAe,CAACA,eAAe,CAACkI,QAAQ,EAAE,eAAe,EAAErB,KAAK,CAACwC,SAAS,CAAC,EAAE,iBAAiB,EAAExC,KAAK,CAACyC,WAAW,CAAC,CAAC;EAC5oB,OAAOnK,MAAM,CAACC,IAAI,CAACsH,OAAO,CAAC,CAACR,GAAG,CAAC,UAAUhF,GAAG,EAAE;IAC7C,OAAOwF,OAAO,CAACxF,GAAG,CAAC,GAAGA,GAAG,GAAG,IAAI;EAClC,CAAC,CAAC,CAAC3B,MAAM,CAAC,UAAU2B,GAAG,EAAE;IACvB,OAAOA,GAAG;EACZ,CAAC,CAAC;AACJ;AAEA,SAASqI,iBAAiBA,CAAC/K,IAAI,EAAE;EAC/B,IAAIA,IAAI,IAAIqC,OAAO,CAACrC,IAAI,CAAC,KAAK,QAAQ,IAAIA,IAAI,CAACgL,MAAM,IAAIhL,IAAI,CAACiL,QAAQ,IAAIjL,IAAI,CAACA,IAAI,EAAE;IACnF,OAAOA,IAAI;EACb;EACA,IAAID,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACC,IAAI,CAACA,IAAI,CAAC;EACzB;EACA,IAAIA,IAAI,KAAK,IAAI,EAAE;IACjB,OAAO,IAAI;EACb;EACA,IAAIqC,OAAO,CAACrC,IAAI,CAAC,KAAK,QAAQ,IAAIA,IAAI,CAACgL,MAAM,IAAIhL,IAAI,CAACiL,QAAQ,EAAE;IAC9D,OAAOjL,IAAI;EACb;EACA,IAAI6D,KAAK,CAACC,OAAO,CAAC9D,IAAI,CAAC,IAAIA,IAAI,CAACsB,MAAM,KAAK,CAAC,EAAE;IAC5C,OAAO;MACL0J,MAAM,EAAEhL,IAAI,CAAC,CAAC,CAAC;MACfiL,QAAQ,EAAEjL,IAAI,CAAC,CAAC;IAClB,CAAC;EACH;EACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAO;MACLgL,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAEjL;IACZ,CAAC;EACH;AACF;AACA,IAAIkL,eAAe,GAAG9K,eAAe,CAAC;EACpCkE,IAAI,EAAE,iBAAiB;EACvB+D,KAAK,EAAE;IACLyB,MAAM,EAAE;MACNqB,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDxB,UAAU,EAAE;MACVsB,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDpB,IAAI,EAAE;MACJkB,IAAI,EAAE,CAACC,OAAO,EAAElJ,MAAM,CAAC;MACvBmJ,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,SAASA,SAASA,CAAC3I,KAAK,EAAE;QACnC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,CAAC,CAACQ,OAAO,CAACR,KAAK,CAAC,GAAG,CAAC,CAAC;MAC5E;IACF,CAAC;IACD3C,IAAI,EAAE;MACJmL,IAAI,EAAE,CAACxK,MAAM,EAAEkD,KAAK,EAAE3B,MAAM,CAAC;MAC7BqJ,QAAQ,EAAE;IACZ,CAAC;IACDC,IAAI,EAAE;MACJL,IAAI,EAAE,CAACxK,MAAM,EAAEkD,KAAK,EAAE3B,MAAM,CAAC;MAC7BmJ,OAAO,EAAE;IACX,CAAC;IACDI,MAAM,EAAE;MACNN,IAAI,EAAEjJ,MAAM;MACZmJ,OAAO,EAAE;IACX,CAAC;IACDtB,QAAQ,EAAE;MACRoB,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDhB,IAAI,EAAE;MACJc,IAAI,EAAEjJ,MAAM;MACZmJ,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,SAASA,SAASA,CAAC3I,KAAK,EAAE;QACnC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAACQ,OAAO,CAACR,KAAK,CAAC,GAAG,CAAC,CAAC;MAC9C;IACF,CAAC;IACDiH,KAAK,EAAE;MACLuB,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDjB,QAAQ,EAAE;MACRe,IAAI,EAAE,CAACjJ,MAAM,EAAEC,MAAM,CAAC;MACtBkJ,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,SAASA,SAASA,CAAC3I,KAAK,EAAE;QACnC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAACQ,OAAO,CAAChB,MAAM,CAACuJ,QAAQ,CAAC/I,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;MAChE;IACF,CAAC;IACD2H,WAAW,EAAE;MACXa,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDlB,IAAI,EAAE;MACJgB,IAAI,EAAEjJ,MAAM;MACZmJ,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,SAASA,SAASA,CAAC3I,KAAK,EAAE;QACnC,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAACQ,OAAO,CAACR,KAAK,CAAC,GAAG,CAAC,CAAC;MAChI;IACF,CAAC;IACDgH,IAAI,EAAE;MACJwB,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDM,SAAS,EAAE;MACTR,IAAI,EAAE,CAACjJ,MAAM,EAAEvB,MAAM,CAAC;MACtB0K,OAAO,EAAE;IACX,CAAC;IACDO,MAAM,EAAE;MACNT,IAAI,EAAE,CAACC,OAAO,EAAElJ,MAAM,CAAC;MACvBmJ,OAAO,EAAE;IACX,CAAC;IACDQ,KAAK,EAAE;MACLV,IAAI,EAAEjJ,MAAM;MACZmJ,OAAO,EAAE;IACX,CAAC;IACDS,OAAO,EAAE;MACPX,IAAI,EAAEjJ,MAAM;MACZmJ,OAAO,EAAE;IACX,CAAC;IACDrB,OAAO,EAAE;MACPmB,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDd,MAAM,EAAE;MACNY,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDb,KAAK,EAAE;MACLW,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDZ,IAAI,EAAE;MACJU,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDX,IAAI,EAAE;MACJS,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDV,QAAQ,EAAE;MACRQ,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDT,KAAK,EAAE;MACLO,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDR,SAAS,EAAE;MACTM,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDP,WAAW,EAAE;MACXK,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX;EACF,CAAC;EACDU,KAAK,EAAE,SAASA,KAAKA,CAAC1D,KAAK,EAAE2D,IAAI,EAAE;IACjC,IAAIzD,KAAK,GAAGyD,IAAI,CAACzD,KAAK;IACtB,IAAI0D,MAAM,GAAG5L,QAAQ,CAAC,YAAY;MAChC,OAAO0K,iBAAiB,CAAC1C,KAAK,CAACrI,IAAI,CAAC;IACtC,CAAC,CAAC;IACF,IAAIkI,OAAO,GAAG7H,QAAQ,CAAC,YAAY;MACjC,OAAOmJ,aAAa,CAAC,SAAS,EAAEC,SAAS,CAACpB,KAAK,CAAC,CAAC;IACnD,CAAC,CAAC;IACF,IAAIsD,SAAS,GAAGtL,QAAQ,CAAC,YAAY;MACnC,OAAOmJ,aAAa,CAAC,WAAW,EAAE,OAAOnB,KAAK,CAACsD,SAAS,KAAK,QAAQ,GAAG5L,KAAK,CAAC4L,SAAS,CAACtD,KAAK,CAACsD,SAAS,CAAC,GAAGtD,KAAK,CAACsD,SAAS,CAAC;IAC7H,CAAC,CAAC;IACF,IAAIH,IAAI,GAAGnL,QAAQ,CAAC,YAAY;MAC9B,OAAOmJ,aAAa,CAAC,MAAM,EAAEuB,iBAAiB,CAAC1C,KAAK,CAACmD,IAAI,CAAC,CAAC;IAC7D,CAAC,CAAC;IACF,IAAIU,YAAY,GAAG7L,QAAQ,CAAC,YAAY;MACtC,OAAOL,IAAI,CAACiM,MAAM,CAACtJ,KAAK,EAAEvB,cAAc,CAACA,cAAc,CAACA,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE8G,OAAO,CAACvF,KAAK,CAAC,EAAEgJ,SAAS,CAAChJ,KAAK,CAAC,EAAE6I,IAAI,CAAC7I,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC3IiJ,MAAM,EAAEvD,KAAK,CAACuD,MAAM;QACpBC,KAAK,EAAExD,KAAK,CAACwD,KAAK;QAClBC,OAAO,EAAEzD,KAAK,CAACyD,OAAO;QACtBL,MAAM,EAAEpD,KAAK,CAACoD;MAChB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IACFnL,KAAK,CAAC4L,YAAY,EAAE,UAAUvJ,KAAK,EAAE;MACnC,IAAI,CAACA,KAAK,EAAE;QACV,OAAOyG,GAAG,CAAC,oCAAoC,EAAE6C,MAAM,CAACtJ,KAAK,EAAE6I,IAAI,CAAC7I,KAAK,CAAC;MAC5E;IACF,CAAC,EAAE;MACDwJ,SAAS,EAAE;IACb,CAAC,CAAC;IACF,IAAIC,KAAK,GAAG/L,QAAQ,CAAC,YAAY;MAC/B,OAAO6L,YAAY,CAACvJ,KAAK,GAAGwC,OAAO,CAAC+G,YAAY,CAACvJ,KAAK,CAAC0J,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE9D,KAAK,CAAC,GAAG,IAAI;IACvF,CAAC,CAAC;IACF,OAAO,YAAY;MACjB,OAAO6D,KAAK,CAACzJ,KAAK;IACpB,CAAC;EACH;AACF,CAAC,CAAC;AAEF,IAAI2J,iBAAiB,GAAGlM,eAAe,CAAC;EACtCkE,IAAI,EAAE,mBAAmB;EACzB+D,KAAK,EAAE;IACLwB,UAAU,EAAE;MACVsB,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX;EACF,CAAC;EACDU,KAAK,EAAE,SAASA,KAAKA,CAAC1D,KAAK,EAAE2D,IAAI,EAAE;IACjC,IAAIO,KAAK,GAAGP,IAAI,CAACO,KAAK;IACtB,IAAIC,YAAY,GAAGvM,MAAM,CAACuM,YAAY;IACtC,IAAIrE,SAAS,GAAG9H,QAAQ,CAAC,YAAY;MACnC,OAAO,CAAC,EAAE,CAAC6J,MAAM,CAACsC,YAAY,EAAE,SAAS,CAAC,CAAC,CAACtC,MAAM,CAAC3G,kBAAkB,CAAC8E,KAAK,CAACwB,UAAU,GAAG,CAAC,EAAE,CAACK,MAAM,CAACsC,YAAY,EAAE,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IAClI,CAAC,CAAC;IACF,OAAO,YAAY;MACjB,OAAOrM,CAAC,CAAC,KAAK,EAAE;QACdyI,KAAK,EAAET,SAAS,CAACxF;MACnB,CAAC,EAAE4J,KAAK,CAAClB,OAAO,GAAGkB,KAAK,CAAClB,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAC1C,CAAC;EACH;AACF,CAAC,CAAC;AAEF,IAAIoB,qBAAqB,GAAGrM,eAAe,CAAC;EAC1CkE,IAAI,EAAE,uBAAuB;EAC7B+D,KAAK,EAAE;IACL1F,KAAK,EAAE;MACLwI,IAAI,EAAE,CAACjJ,MAAM,EAAEC,MAAM,CAAC;MACtBkJ,OAAO,EAAE;IACX,CAAC;IACDM,SAAS,EAAE;MACTR,IAAI,EAAE,CAACjJ,MAAM,EAAEvB,MAAM,CAAC;MACtB0K,OAAO,EAAE;IACX,CAAC;IACDqB,OAAO,EAAE;MACPvB,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDsB,QAAQ,EAAE;MACRxB,IAAI,EAAEjJ,MAAM;MACZmJ,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,SAASA,SAASA,CAAC3I,KAAK,EAAE;QACnC,OAAO,CAAC,aAAa,EAAE,cAAc,EAAE,UAAU,EAAE,WAAW,CAAC,CAACQ,OAAO,CAACR,KAAK,CAAC,GAAG,CAAC,CAAC;MACrF;IACF;EACF,CAAC;EACDoJ,KAAK,EAAE,SAASA,KAAKA,CAAC1D,KAAK,EAAE2D,IAAI,EAAE;IACjC,IAAIzD,KAAK,GAAGyD,IAAI,CAACzD,KAAK;IACtB,IAAIiE,YAAY,GAAGvM,MAAM,CAACuM,YAAY;IACtC,IAAItE,OAAO,GAAG7H,QAAQ,CAAC,YAAY;MACjC,OAAOmJ,aAAa,CAAC,SAAS,EAAE,EAAE,CAACU,MAAM,CAAC3G,kBAAkB,CAAC8E,KAAK,CAACqE,OAAO,GAAG,CAAC,EAAE,CAACxC,MAAM,CAACsC,YAAY,EAAE,iBAAiB,CAAC,CAAC,GAAG,EAAE,CAAC,EAAEjJ,kBAAkB,CAAC8E,KAAK,CAACsE,QAAQ,GAAG,CAAC,EAAE,CAACzC,MAAM,CAACsC,YAAY,EAAE,UAAU,CAAC,CAACtC,MAAM,CAAC7B,KAAK,CAACsE,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAC3O,CAAC,CAAC;IACF,IAAIhB,SAAS,GAAGtL,QAAQ,CAAC,YAAY;MACnC,OAAOmJ,aAAa,CAAC,WAAW,EAAE,OAAOnB,KAAK,CAACsD,SAAS,KAAK,QAAQ,GAAG5L,KAAK,CAAC4L,SAAS,CAACtD,KAAK,CAACsD,SAAS,CAAC,GAAGtD,KAAK,CAACsD,SAAS,CAAC;IAC7H,CAAC,CAAC;IACF,IAAIvD,eAAe,GAAG/H,QAAQ,CAAC,YAAY;MACzC,IAAIuM,KAAK,GAAG1M,IAAI,CAACmI,KAAK,CAAC1F,KAAK,CAACyB,QAAQ,CAAC,CAAC,EAAEhD,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEuK,SAAS,CAAChJ,KAAK,CAAC,EAAEuF,OAAO,CAACvF,KAAK,CAAC,CAAC;QAC1G0J,QAAQ,GAAGO,KAAK,CAACP,QAAQ;MAC3B,IAAIhE,KAAK,CAACqE,OAAO,EAAE;QACjBL,QAAQ,CAAC,CAAC,CAAC,CAAC1D,UAAU,CAACC,KAAK,GAAGyD,QAAQ,CAAC,CAAC,CAAC,CAAC1D,UAAU,CAACC,KAAK,CAACxC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC;MAC3F;MACA,OAAOiG,QAAQ,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC;IACF,IAAID,KAAK,GAAG/L,QAAQ,CAAC,YAAY;MAC/B,OAAO8E,OAAO,CAACiD,eAAe,CAACzF,KAAK,EAAE,CAAC,CAAC,EAAE4F,KAAK,CAAC;IAClD,CAAC,CAAC;IACF,OAAO,YAAY;MACjB,OAAO6D,KAAK,CAACzJ,KAAK;IACpB,CAAC;EACH;AACF,CAAC,CAAC;AAEF,SAASuI,eAAe,EAAEoB,iBAAiB,EAAEG,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}