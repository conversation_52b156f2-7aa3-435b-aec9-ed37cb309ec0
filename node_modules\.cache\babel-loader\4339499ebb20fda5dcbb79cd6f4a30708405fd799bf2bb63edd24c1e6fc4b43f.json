{"ast": null, "code": "import { mapState } from 'vuex';\nimport BaseModal from '@/components/BaseModal.vue';\nimport StatusBadge from '@/components/StatusBadge.vue';\nimport CustomCheckbox from '@/components/CustomCheckbox.vue';\nexport default {\n  name: 'ScheduledTasks',\n  components: {\n    BaseModal,\n    StatusBadge,\n    CustomCheckbox\n  },\n  data() {\n    return {\n      processing: false,\n      // 新建/编辑任务弹窗\n      taskModal: {\n        show: false,\n        isEdit: false,\n        taskId: null,\n        hostGroup: '',\n        selectedHosts: {},\n        form: {\n          name: '',\n          target: '',\n          policyId: 1,\n          frequency: 'monthly',\n          monthlyType: 'first',\n          dayOfWeek: '1',\n          timeOfDay: '03:00',\n          autoRetry: true,\n          sendNotification: true,\n          detailedLog: true\n        }\n      },\n      // 删除确认弹窗\n      deleteModal: {\n        show: false,\n        taskId: null,\n        taskName: ''\n      },\n      searchText: '',\n      statusFilter: 'all'\n    };\n  },\n  computed: {\n    ...mapState({\n      tasks: state => state.tasks,\n      hosts: state => state.hosts,\n      policies: state => state.policies\n    }),\n    filteredTasks() {\n      return this.tasks.filter(task => {\n        const matchesSearch = task.name.toLowerCase().includes(this.searchText.toLowerCase());\n        const matchesStatus = this.statusFilter === 'all' || task.status === this.statusFilter;\n        return matchesSearch && matchesStatus;\n      });\n    }\n  },\n  methods: {\n    showNewTaskModal() {\n      this.taskModal.isEdit = false;\n      this.taskModal.taskId = null;\n      this.resetTaskForm();\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.taskModal.selectedHosts[host.id] = false;\n      });\n      this.taskModal.show = true;\n    },\n    editTask(task) {\n      this.taskModal.isEdit = true;\n      this.taskModal.taskId = task.id;\n\n      // 解析任务调度信息\n      const scheduleInfo = this.parseSchedule(task.schedule);\n      this.taskModal.form = {\n        name: task.name,\n        target: task.target,\n        policyId: 1,\n        // 默认值，实际应用中应该从任务中获取\n        ...scheduleInfo,\n        autoRetry: true,\n        sendNotification: true,\n        detailedLog: true\n      };\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        // 这里简化处理，实际应用中应该从任务中获取已选主机\n        this.taskModal.selectedHosts[host.id] = false;\n      });\n      this.taskModal.show = true;\n    },\n    confirmDeleteTask(task) {\n      this.deleteModal.taskId = task.id;\n      this.deleteModal.taskName = task.name;\n      this.deleteModal.show = true;\n    },\n    resetTaskForm() {\n      this.taskModal.form = {\n        name: '',\n        target: '',\n        policyId: 1,\n        frequency: 'monthly',\n        monthlyType: 'first',\n        dayOfWeek: '1',\n        timeOfDay: '03:00',\n        autoRetry: true,\n        sendNotification: true,\n        detailedLog: true\n      };\n      this.taskModal.hostGroup = '';\n    },\n    parseSchedule(schedule) {\n      // 简单解析调度表达式，实际应用中可能需要更复杂的逻辑\n      if (schedule.includes('每月')) {\n        return {\n          frequency: 'monthly',\n          monthlyType: 'first',\n          dayOfWeek: '1',\n          timeOfDay: '03:00'\n        };\n      } else if (schedule.includes('每周')) {\n        return {\n          frequency: 'weekly',\n          dayOfWeek: '0',\n          timeOfDay: '02:00'\n        };\n      } else {\n        return {\n          frequency: 'daily',\n          timeOfDay: '03:00'\n        };\n      }\n    },\n    calculateNextRunTime() {\n      // 这是一个简化的版本，实际应用中应该根据任务调度信息计算下次执行时间\n      const now = new Date();\n      const tomorrow = new Date(now);\n      tomorrow.setDate(now.getDate() + 1);\n      return tomorrow.toLocaleDateString('zh-CN') + ' ' + this.taskModal.form.timeOfDay;\n    },\n    getScheduleDescription() {\n      const {\n        frequency,\n        monthlyType,\n        dayOfWeek,\n        timeOfDay\n      } = this.taskModal.form;\n      const dayOfWeekMap = {\n        '0': '周日',\n        '1': '周一',\n        '2': '周二',\n        '3': '周三',\n        '4': '周四',\n        '5': '周五',\n        '6': '周六'\n      };\n      const monthlyTypeMap = {\n        'first': '第一个',\n        'second': '第二个',\n        'third': '第三个',\n        'last': '最后一个'\n      };\n      if (frequency === 'daily') {\n        return `每天 ${timeOfDay}`;\n      } else if (frequency === 'weekly') {\n        return `每${dayOfWeekMap[dayOfWeek]} ${timeOfDay}`;\n      } else if (frequency === 'monthly') {\n        return `每月${monthlyTypeMap[monthlyType]}${dayOfWeekMap[dayOfWeek]} ${timeOfDay}`;\n      } else {\n        return '自定义';\n      }\n    },\n    getSelectedHostsDescription() {\n      const selectedHostIds = Object.entries(this.taskModal.selectedHosts).filter(([_, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        return '请选择至少一台主机';\n      }\n      if (this.taskModal.hostGroup) {\n        return this.taskModal.hostGroup === 'production' ? '生产环境所有服务器' : '测试环境数据库服务器';\n      }\n      return `已选择 ${selectedHostIds.length} 台主机`;\n    },\n    validateTaskForm() {\n      if (!this.taskModal.form.name) {\n        alert('请输入任务名称');\n        return false;\n      }\n      const selectedHostIds = Object.entries(this.taskModal.selectedHosts).filter(([_, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0 && !this.taskModal.hostGroup) {\n        alert('请选择至少一台主机');\n        return false;\n      }\n      return true;\n    },\n    async saveTaskChanges() {\n      if (!this.validateTaskForm()) {\n        return;\n      }\n      this.processing = true;\n      try {\n        const scheduleDescription = this.getScheduleDescription();\n        const targetDescription = this.getSelectedHostsDescription();\n        const taskData = {\n          name: this.taskModal.form.name,\n          target: targetDescription,\n          schedule: scheduleDescription,\n          lastRun: '-',\n          nextRun: this.calculateNextRunTime(),\n          status: 'running',\n          policyId: this.taskModal.form.policyId\n        };\n        if (this.taskModal.isEdit) {\n          this.$store.commit('updateTask', {\n            id: this.taskModal.taskId,\n            ...taskData\n          });\n        } else {\n          this.$store.commit('addTask', taskData);\n        }\n        this.taskModal.show = false;\n\n        // 提示用户操作成功\n        alert(`任务${this.taskModal.isEdit ? '更新' : '创建'}成功！`);\n      } catch (error) {\n        console.error('保存任务失败', error);\n        alert('保存任务失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    async deleteTask() {\n      this.processing = true;\n      try {\n        this.$store.commit('deleteTask', this.deleteModal.taskId);\n        this.deleteModal.show = false;\n\n        // 提示用户操作成功\n        alert('任务删除成功！');\n      } catch (error) {\n        console.error('删除任务失败', error);\n        alert('删除任务失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    }\n  },\n  watch: {\n    'taskModal.hostGroup'(newValue) {\n      if (newValue) {\n        // 根据选择的主机组自动选择对应的主机\n        this.hosts.forEach(host => {\n          if (newValue === 'production') {\n            // 模拟选择生产环境服务器\n            this.taskModal.selectedHosts[host.id] = host.name.includes('server');\n          } else if (newValue === 'test') {\n            // 模拟选择测试环境服务器\n            this.taskModal.selectedHosts[host.id] = false;\n          } else if (newValue === 'database') {\n            // 模拟选择数据库服务器\n            this.taskModal.selectedHosts[host.id] = host.name.includes('db');\n          } else if (newValue === 'application') {\n            // 模拟选择应用服务器\n            this.taskModal.selectedHosts[host.id] = host.name.includes('app');\n          }\n        });\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["mapState", "BaseModal", "StatusBadge", "CustomCheckbox", "name", "components", "data", "processing", "taskModal", "show", "isEdit", "taskId", "hostGroup", "selectedHosts", "form", "target", "policyId", "frequency", "monthlyType", "dayOfWeek", "timeOfDay", "autoRetry", "sendNotification", "detailedLog", "deleteModal", "taskName", "searchText", "statusFilter", "computed", "tasks", "state", "hosts", "policies", "filteredTasks", "filter", "task", "matchesSearch", "toLowerCase", "includes", "matchesStatus", "status", "methods", "showNewTaskModal", "resetTaskForm", "for<PERSON>ach", "host", "id", "editTask", "scheduleInfo", "parseSchedule", "schedule", "confirmDeleteTask", "calculateNextRunTime", "now", "Date", "tomorrow", "setDate", "getDate", "toLocaleDateString", "getScheduleDescription", "dayOfWeekMap", "monthlyTypeMap", "getSelectedHostsDescription", "selectedHostIds", "Object", "entries", "_", "selected", "map", "parseInt", "length", "validateTaskForm", "alert", "saveTaskChanges", "scheduleDescription", "targetDescription", "taskData", "lastRun", "nextRun", "$store", "commit", "error", "console", "deleteTask", "watch", "taskModal.hostGroup", "newValue"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\ScheduledTasks.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 顶部操作区域 -->\r\n    <div class=\"bg-white shadow rounded-lg p-4 mb-6\">\r\n      <div class=\"flex flex-wrap items-center justify-between\">\r\n        <div class=\"flex items-center\">\r\n          <h2 class=\"text-lg font-semibold\">定时任务管理</h2>\r\n        </div>\r\n\r\n        <div class=\"flex items-center space-x-4\">\r\n          <!-- 搜索框 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"searchText\" placeholder=\"搜索任务...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 状态筛选 -->\r\n          <select v-model=\"statusFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有状态</option>\r\n            <option value=\"running\">运行中</option>\r\n            <option value=\"pending\">等待中</option>\r\n            <option value=\"completed\">已完成</option>\r\n            <option value=\"failed\">失败</option>\r\n          </select>\r\n\r\n          <!-- 创建任务按钮 -->\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            @click=\"showNewTaskModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'plus']\" class=\"mr-2\" />\r\n            <span>创建任务</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"space-y-5\">\r\n      <!-- 任务卡片 -->\r\n      <div \r\n        v-for=\"task in filteredTasks\" \r\n        :key=\"task.id\" \r\n        class=\"bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow duration-200\"\r\n      >\r\n        <div class=\"px-4 py-5 sm:p-6\">\r\n          <div class=\"flex justify-between items-start\">\r\n            <div class=\"flex-1\">\r\n              <div class=\"flex items-center\">\r\n                <h3 class=\"text-lg font-medium text-gray-900\">{{ task.name }}</h3>\r\n                <StatusBadge :type=\"task.status\" class=\"ml-3\" />\r\n              </div>\r\n              <p class=\"mt-1 text-sm text-gray-500\">{{ task.target }}</p>\r\n              \r\n              <div class=\"mt-4 grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n                <div class=\"flex items-center\">\r\n                  <div class=\"w-8 h-8 flex-shrink-0 bg-indigo-100 rounded-full flex items-center justify-center\">\r\n                    <font-awesome-icon :icon=\"['fas', 'calendar-check']\" class=\"text-indigo-600\" />\r\n                  </div>\r\n                  <div class=\"ml-3\">\r\n                    <p class=\"text-xs font-medium text-gray-500\">执行计划</p>\r\n                    <p class=\"text-sm font-semibold text-gray-900\">{{ task.schedule }}</p>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div class=\"flex items-center\">\r\n                  <div class=\"w-8 h-8 flex-shrink-0 bg-purple-100 rounded-full flex items-center justify-center\">\r\n                    <font-awesome-icon :icon=\"['fas', 'history']\" class=\"text-purple-600\" />\r\n                  </div>\r\n                  <div class=\"ml-3\">\r\n                    <p class=\"text-xs font-medium text-gray-500\">上次执行</p>\r\n                    <p class=\"text-sm font-semibold text-gray-900\">{{ task.lastRun }}</p>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div class=\"flex items-center\">\r\n                  <div class=\"w-8 h-8 flex-shrink-0 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                    <font-awesome-icon :icon=\"['fas', 'clock']\" class=\"text-blue-600\" />\r\n                  </div>\r\n                  <div class=\"ml-3\">\r\n                    <p class=\"text-xs font-medium text-gray-500\">下次执行</p>\r\n                    <p class=\"text-sm font-semibold text-gray-900\">{{ task.nextRun }}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            <div class=\"ml-4 flex-shrink-0 flex space-x-2\">\r\n              <button \r\n                class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                @click=\"editTask(task)\"\r\n              >\r\n                <font-awesome-icon :icon=\"['fas', 'edit']\" class=\"mr-1\" />\r\n                编辑\r\n              </button>\r\n              <button \r\n                class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\r\n                @click=\"confirmDeleteTask(task)\"\r\n              >\r\n                <font-awesome-icon :icon=\"['fas', 'trash-alt']\" class=\"mr-1\" />\r\n                删除\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 新建/编辑任务弹窗 -->\r\n    <BaseModal v-model=\"taskModal.show\" :title=\"taskModal.isEdit ? '编辑定时密码更新任务' : '创建定时密码更新任务'\"\r\n      :confirm-text=\"taskModal.isEdit ? '保存更改' : '创建任务'\" size=\"lg\" @confirm=\"saveTaskChanges\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">任务名称</label>\r\n        <input type=\"text\" v-model=\"taskModal.form.name\" class=\"form-control\" placeholder=\"输入任务名称\">\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <select v-model=\"taskModal.hostGroup\" class=\"form-select\">\r\n            <option value=\"\">选择主机组</option>\r\n            <option value=\"production\">生产环境服务器</option>\r\n            <option value=\"test\">测试环境服务器</option>\r\n            <option value=\"database\">数据库服务器</option>\r\n            <option value=\"application\">应用服务器</option>\r\n          </select>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"taskModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"taskModal.form.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行计划</label>\r\n        <div class=\"flex flex-wrap gap-2 mb-3\">\r\n          <select v-model=\"taskModal.form.frequency\"\r\n            class=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\">\r\n            <option value=\"daily\">每天</option>\r\n            <option value=\"weekly\">每周</option>\r\n            <option value=\"monthly\">每月</option>\r\n            <option value=\"custom\">自定义</option>\r\n          </select>\r\n\r\n          <select v-if=\"taskModal.form.frequency === 'monthly'\" v-model=\"taskModal.form.monthlyType\"\r\n            class=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\">\r\n            <option value=\"first\">第一个</option>\r\n            <option value=\"second\">第二个</option>\r\n            <option value=\"third\">第三个</option>\r\n            <option value=\"last\">最后一个</option>\r\n          </select>\r\n\r\n          <select v-if=\"taskModal.form.frequency === 'weekly' || taskModal.form.frequency === 'monthly'\"\r\n            v-model=\"taskModal.form.dayOfWeek\"\r\n            class=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\">\r\n            <option value=\"1\">周一</option>\r\n            <option value=\"2\">周二</option>\r\n            <option value=\"3\">周三</option>\r\n            <option value=\"4\">周四</option>\r\n            <option value=\"5\">周五</option>\r\n            <option value=\"6\">周六</option>\r\n            <option value=\"0\">周日</option>\r\n          </select>\r\n\r\n          <input type=\"time\" v-model=\"taskModal.form.timeOfDay\"\r\n            class=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\">\r\n        </div>\r\n        <div class=\"text-sm text-gray-500\">下次执行时间: {{ calculateNextRunTime() }}</div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">任务选项</label>\r\n        <CustomCheckbox v-model=\"taskModal.form.autoRetry\">\r\n          失败后自动重试\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"taskModal.form.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"taskModal.form.detailedLog\">\r\n          记录详细执行日志\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 删除确认弹窗 -->\r\n    <BaseModal v-model=\"deleteModal.show\" title=\"确认删除任务\" confirm-text=\"删除\" danger @confirm=\"deleteTask\"\r\n      :loading=\"processing\">\r\n      <p>您确定要删除任务 <strong>{{ deleteModal.taskName }}</strong> 吗？</p>\r\n      <p class=\"mt-2 text-red-600\">此操作无法撤销，删除后任务将不再执行。</p>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\n\r\nexport default {\r\n  name: 'ScheduledTasks',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox\r\n  },\r\n  data() {\r\n    return {\r\n      processing: false,\r\n\r\n      // 新建/编辑任务弹窗\r\n      taskModal: {\r\n        show: false,\r\n        isEdit: false,\r\n        taskId: null,\r\n        hostGroup: '',\r\n        selectedHosts: {},\r\n        form: {\r\n          name: '',\r\n          target: '',\r\n          policyId: 1,\r\n          frequency: 'monthly',\r\n          monthlyType: 'first',\r\n          dayOfWeek: '1',\r\n          timeOfDay: '03:00',\r\n          autoRetry: true,\r\n          sendNotification: true,\r\n          detailedLog: true\r\n        }\r\n      },\r\n\r\n      // 删除确认弹窗\r\n      deleteModal: {\r\n        show: false,\r\n        taskId: null,\r\n        taskName: ''\r\n      },\r\n\r\n      searchText: '',\r\n      statusFilter: 'all'\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      tasks: state => state.tasks,\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    filteredTasks() {\r\n      return this.tasks.filter(task => {\r\n        const matchesSearch = task.name.toLowerCase().includes(this.searchText.toLowerCase())\r\n        const matchesStatus = this.statusFilter === 'all' || task.status === this.statusFilter\r\n        return matchesSearch && matchesStatus\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    showNewTaskModal() {\r\n      this.taskModal.isEdit = false\r\n      this.taskModal.taskId = null\r\n      this.resetTaskForm()\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.taskModal.selectedHosts[host.id] = false\r\n      })\r\n\r\n      this.taskModal.show = true\r\n    },\r\n\r\n    editTask(task) {\r\n      this.taskModal.isEdit = true\r\n      this.taskModal.taskId = task.id\r\n\r\n      // 解析任务调度信息\r\n      const scheduleInfo = this.parseSchedule(task.schedule)\r\n\r\n      this.taskModal.form = {\r\n        name: task.name,\r\n        target: task.target,\r\n        policyId: 1, // 默认值，实际应用中应该从任务中获取\r\n        ...scheduleInfo,\r\n        autoRetry: true,\r\n        sendNotification: true,\r\n        detailedLog: true\r\n      }\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        // 这里简化处理，实际应用中应该从任务中获取已选主机\r\n        this.taskModal.selectedHosts[host.id] = false\r\n      })\r\n\r\n      this.taskModal.show = true\r\n    },\r\n\r\n    confirmDeleteTask(task) {\r\n      this.deleteModal.taskId = task.id\r\n      this.deleteModal.taskName = task.name\r\n      this.deleteModal.show = true\r\n    },\r\n\r\n    resetTaskForm() {\r\n      this.taskModal.form = {\r\n        name: '',\r\n        target: '',\r\n        policyId: 1,\r\n        frequency: 'monthly',\r\n        monthlyType: 'first',\r\n        dayOfWeek: '1',\r\n        timeOfDay: '03:00',\r\n        autoRetry: true,\r\n        sendNotification: true,\r\n        detailedLog: true\r\n      }\r\n      this.taskModal.hostGroup = ''\r\n    },\r\n\r\n    parseSchedule(schedule) {\r\n      // 简单解析调度表达式，实际应用中可能需要更复杂的逻辑\r\n      if (schedule.includes('每月')) {\r\n        return {\r\n          frequency: 'monthly',\r\n          monthlyType: 'first',\r\n          dayOfWeek: '1',\r\n          timeOfDay: '03:00'\r\n        }\r\n      } else if (schedule.includes('每周')) {\r\n        return {\r\n          frequency: 'weekly',\r\n          dayOfWeek: '0',\r\n          timeOfDay: '02:00'\r\n        }\r\n      } else {\r\n        return {\r\n          frequency: 'daily',\r\n          timeOfDay: '03:00'\r\n        }\r\n      }\r\n    },\r\n\r\n    calculateNextRunTime() {\r\n      // 这是一个简化的版本，实际应用中应该根据任务调度信息计算下次执行时间\r\n      const now = new Date()\r\n      const tomorrow = new Date(now)\r\n      tomorrow.setDate(now.getDate() + 1)\r\n\r\n      return tomorrow.toLocaleDateString('zh-CN') + ' ' + this.taskModal.form.timeOfDay\r\n    },\r\n\r\n    getScheduleDescription() {\r\n      const { frequency, monthlyType, dayOfWeek, timeOfDay } = this.taskModal.form\r\n\r\n      const dayOfWeekMap = {\r\n        '0': '周日',\r\n        '1': '周一',\r\n        '2': '周二',\r\n        '3': '周三',\r\n        '4': '周四',\r\n        '5': '周五',\r\n        '6': '周六'\r\n      }\r\n\r\n      const monthlyTypeMap = {\r\n        'first': '第一个',\r\n        'second': '第二个',\r\n        'third': '第三个',\r\n        'last': '最后一个'\r\n      }\r\n\r\n      if (frequency === 'daily') {\r\n        return `每天 ${timeOfDay}`\r\n      } else if (frequency === 'weekly') {\r\n        return `每${dayOfWeekMap[dayOfWeek]} ${timeOfDay}`\r\n      } else if (frequency === 'monthly') {\r\n        return `每月${monthlyTypeMap[monthlyType]}${dayOfWeekMap[dayOfWeek]} ${timeOfDay}`\r\n      } else {\r\n        return '自定义'\r\n      }\r\n    },\r\n\r\n    getSelectedHostsDescription() {\r\n      const selectedHostIds = Object.entries(this.taskModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        return '请选择至少一台主机'\r\n      }\r\n\r\n      if (this.taskModal.hostGroup) {\r\n        return this.taskModal.hostGroup === 'production' ? '生产环境所有服务器' : '测试环境数据库服务器'\r\n      }\r\n\r\n      return `已选择 ${selectedHostIds.length} 台主机`\r\n    },\r\n\r\n    validateTaskForm() {\r\n      if (!this.taskModal.form.name) {\r\n        alert('请输入任务名称')\r\n        return false\r\n      }\r\n\r\n      const selectedHostIds = Object.entries(this.taskModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0 && !this.taskModal.hostGroup) {\r\n        alert('请选择至少一台主机')\r\n        return false\r\n      }\r\n\r\n      return true\r\n    },\r\n\r\n    async saveTaskChanges() {\r\n      if (!this.validateTaskForm()) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const scheduleDescription = this.getScheduleDescription()\r\n        const targetDescription = this.getSelectedHostsDescription()\r\n\r\n        const taskData = {\r\n          name: this.taskModal.form.name,\r\n          target: targetDescription,\r\n          schedule: scheduleDescription,\r\n          lastRun: '-',\r\n          nextRun: this.calculateNextRunTime(),\r\n          status: 'running',\r\n          policyId: this.taskModal.form.policyId\r\n        }\r\n\r\n        if (this.taskModal.isEdit) {\r\n          this.$store.commit('updateTask', {\r\n            id: this.taskModal.taskId,\r\n            ...taskData\r\n          })\r\n        } else {\r\n          this.$store.commit('addTask', taskData)\r\n        }\r\n\r\n        this.taskModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`任务${this.taskModal.isEdit ? '更新' : '创建'}成功！`)\r\n      } catch (error) {\r\n        console.error('保存任务失败', error)\r\n        alert('保存任务失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async deleteTask() {\r\n      this.processing = true\r\n\r\n      try {\r\n        this.$store.commit('deleteTask', this.deleteModal.taskId)\r\n        this.deleteModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert('任务删除成功！')\r\n      } catch (error) {\r\n        console.error('删除任务失败', error)\r\n        alert('删除任务失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    'taskModal.hostGroup'(newValue) {\r\n      if (newValue) {\r\n        // 根据选择的主机组自动选择对应的主机\r\n        this.hosts.forEach(host => {\r\n          if (newValue === 'production') {\r\n            // 模拟选择生产环境服务器\r\n            this.taskModal.selectedHosts[host.id] = host.name.includes('server')\r\n          } else if (newValue === 'test') {\r\n            // 模拟选择测试环境服务器\r\n            this.taskModal.selectedHosts[host.id] = false\r\n          } else if (newValue === 'database') {\r\n            // 模拟选择数据库服务器\r\n            this.taskModal.selectedHosts[host.id] = host.name.includes('db')\r\n          } else if (newValue === 'application') {\r\n            // 模拟选择应用服务器\r\n            this.taskModal.selectedHosts[host.id] = host.name.includes('app')\r\n          }\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>"], "mappings": "AA8MA,SAASA,QAAO,QAAS,MAAK;AAC9B,OAAOC,SAAQ,MAAO,4BAA2B;AACjD,OAAOC,WAAU,MAAO,8BAA6B;AACrD,OAAOC,cAAa,MAAO,iCAAgC;AAE3D,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,UAAU,EAAE;IACVJ,SAAS;IACTC,WAAW;IACXC;EACF,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,UAAU,EAAE,KAAK;MAEjB;MACAC,SAAS,EAAE;QACTC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,KAAK;QACbC,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE,EAAE;QACbC,aAAa,EAAE,CAAC,CAAC;QACjBC,IAAI,EAAE;UACJV,IAAI,EAAE,EAAE;UACRW,MAAM,EAAE,EAAE;UACVC,QAAQ,EAAE,CAAC;UACXC,SAAS,EAAE,SAAS;UACpBC,WAAW,EAAE,OAAO;UACpBC,SAAS,EAAE,GAAG;UACdC,SAAS,EAAE,OAAO;UAClBC,SAAS,EAAE,IAAI;UACfC,gBAAgB,EAAE,IAAI;UACtBC,WAAW,EAAE;QACf;MACF,CAAC;MAED;MACAC,WAAW,EAAE;QACXf,IAAI,EAAE,KAAK;QACXE,MAAM,EAAE,IAAI;QACZc,QAAQ,EAAE;MACZ,CAAC;MAEDC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE;IAChB;EACF,CAAC;EACDC,QAAQ,EAAE;IACR,GAAG5B,QAAQ,CAAC;MACV6B,KAAK,EAAEC,KAAI,IAAKA,KAAK,CAACD,KAAK;MAC3BE,KAAK,EAAED,KAAI,IAAKA,KAAK,CAACC,KAAK;MAC3BC,QAAQ,EAAEF,KAAI,IAAKA,KAAK,CAACE;IAC3B,CAAC,CAAC;IACFC,aAAaA,CAAA,EAAG;MACd,OAAO,IAAI,CAACJ,KAAK,CAACK,MAAM,CAACC,IAAG,IAAK;QAC/B,MAAMC,aAAY,GAAID,IAAI,CAAC/B,IAAI,CAACiC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACZ,UAAU,CAACW,WAAW,CAAC,CAAC;QACpF,MAAME,aAAY,GAAI,IAAI,CAACZ,YAAW,KAAM,KAAI,IAAKQ,IAAI,CAACK,MAAK,KAAM,IAAI,CAACb,YAAW;QACrF,OAAOS,aAAY,IAAKG,aAAY;MACtC,CAAC;IACH;EACF,CAAC;EACDE,OAAO,EAAE;IACPC,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAAClC,SAAS,CAACE,MAAK,GAAI,KAAI;MAC5B,IAAI,CAACF,SAAS,CAACG,MAAK,GAAI,IAAG;MAC3B,IAAI,CAACgC,aAAa,CAAC;;MAEnB;MACA,IAAI,CAACZ,KAAK,CAACa,OAAO,CAACC,IAAG,IAAK;QACzB,IAAI,CAACrC,SAAS,CAACK,aAAa,CAACgC,IAAI,CAACC,EAAE,IAAI,KAAI;MAC9C,CAAC;MAED,IAAI,CAACtC,SAAS,CAACC,IAAG,GAAI,IAAG;IAC3B,CAAC;IAEDsC,QAAQA,CAACZ,IAAI,EAAE;MACb,IAAI,CAAC3B,SAAS,CAACE,MAAK,GAAI,IAAG;MAC3B,IAAI,CAACF,SAAS,CAACG,MAAK,GAAIwB,IAAI,CAACW,EAAC;;MAE9B;MACA,MAAME,YAAW,GAAI,IAAI,CAACC,aAAa,CAACd,IAAI,CAACe,QAAQ;MAErD,IAAI,CAAC1C,SAAS,CAACM,IAAG,GAAI;QACpBV,IAAI,EAAE+B,IAAI,CAAC/B,IAAI;QACfW,MAAM,EAAEoB,IAAI,CAACpB,MAAM;QACnBC,QAAQ,EAAE,CAAC;QAAE;QACb,GAAGgC,YAAY;QACf3B,SAAS,EAAE,IAAI;QACfC,gBAAgB,EAAE,IAAI;QACtBC,WAAW,EAAE;MACf;;MAEA;MACA,IAAI,CAACQ,KAAK,CAACa,OAAO,CAACC,IAAG,IAAK;QACzB;QACA,IAAI,CAACrC,SAAS,CAACK,aAAa,CAACgC,IAAI,CAACC,EAAE,IAAI,KAAI;MAC9C,CAAC;MAED,IAAI,CAACtC,SAAS,CAACC,IAAG,GAAI,IAAG;IAC3B,CAAC;IAED0C,iBAAiBA,CAAChB,IAAI,EAAE;MACtB,IAAI,CAACX,WAAW,CAACb,MAAK,GAAIwB,IAAI,CAACW,EAAC;MAChC,IAAI,CAACtB,WAAW,CAACC,QAAO,GAAIU,IAAI,CAAC/B,IAAG;MACpC,IAAI,CAACoB,WAAW,CAACf,IAAG,GAAI,IAAG;IAC7B,CAAC;IAEDkC,aAAaA,CAAA,EAAG;MACd,IAAI,CAACnC,SAAS,CAACM,IAAG,GAAI;QACpBV,IAAI,EAAE,EAAE;QACRW,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,CAAC;QACXC,SAAS,EAAE,SAAS;QACpBC,WAAW,EAAE,OAAO;QACpBC,SAAS,EAAE,GAAG;QACdC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,IAAI;QACfC,gBAAgB,EAAE,IAAI;QACtBC,WAAW,EAAE;MACf;MACA,IAAI,CAACf,SAAS,CAACI,SAAQ,GAAI,EAAC;IAC9B,CAAC;IAEDqC,aAAaA,CAACC,QAAQ,EAAE;MACtB;MACA,IAAIA,QAAQ,CAACZ,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC3B,OAAO;UACLrB,SAAS,EAAE,SAAS;UACpBC,WAAW,EAAE,OAAO;UACpBC,SAAS,EAAE,GAAG;UACdC,SAAS,EAAE;QACb;MACF,OAAO,IAAI8B,QAAQ,CAACZ,QAAQ,CAAC,IAAI,CAAC,EAAE;QAClC,OAAO;UACLrB,SAAS,EAAE,QAAQ;UACnBE,SAAS,EAAE,GAAG;UACdC,SAAS,EAAE;QACb;MACF,OAAO;QACL,OAAO;UACLH,SAAS,EAAE,OAAO;UAClBG,SAAS,EAAE;QACb;MACF;IACF,CAAC;IAEDgC,oBAAoBA,CAAA,EAAG;MACrB;MACA,MAAMC,GAAE,GAAI,IAAIC,IAAI,CAAC;MACrB,MAAMC,QAAO,GAAI,IAAID,IAAI,CAACD,GAAG;MAC7BE,QAAQ,CAACC,OAAO,CAACH,GAAG,CAACI,OAAO,CAAC,IAAI,CAAC;MAElC,OAAOF,QAAQ,CAACG,kBAAkB,CAAC,OAAO,IAAI,GAAE,GAAI,IAAI,CAAClD,SAAS,CAACM,IAAI,CAACM,SAAQ;IAClF,CAAC;IAEDuC,sBAAsBA,CAAA,EAAG;MACvB,MAAM;QAAE1C,SAAS;QAAEC,WAAW;QAAEC,SAAS;QAAEC;MAAU,IAAI,IAAI,CAACZ,SAAS,CAACM,IAAG;MAE3E,MAAM8C,YAAW,GAAI;QACnB,GAAG,EAAE,IAAI;QACT,GAAG,EAAE,IAAI;QACT,GAAG,EAAE,IAAI;QACT,GAAG,EAAE,IAAI;QACT,GAAG,EAAE,IAAI;QACT,GAAG,EAAE,IAAI;QACT,GAAG,EAAE;MACP;MAEA,MAAMC,cAAa,GAAI;QACrB,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,KAAK;QACd,MAAM,EAAE;MACV;MAEA,IAAI5C,SAAQ,KAAM,OAAO,EAAE;QACzB,OAAO,MAAMG,SAAS,EAAC;MACzB,OAAO,IAAIH,SAAQ,KAAM,QAAQ,EAAE;QACjC,OAAO,IAAI2C,YAAY,CAACzC,SAAS,CAAC,IAAIC,SAAS,EAAC;MAClD,OAAO,IAAIH,SAAQ,KAAM,SAAS,EAAE;QAClC,OAAO,KAAK4C,cAAc,CAAC3C,WAAW,CAAC,GAAG0C,YAAY,CAACzC,SAAS,CAAC,IAAIC,SAAS,EAAC;MACjF,OAAO;QACL,OAAO,KAAI;MACb;IACF,CAAC;IAED0C,2BAA2BA,CAAA,EAAG;MAC5B,MAAMC,eAAc,GAAIC,MAAM,CAACC,OAAO,CAAC,IAAI,CAACzD,SAAS,CAACK,aAAa,EAChEqB,MAAM,CAAC,CAAC,CAACgC,CAAC,EAAEC,QAAQ,CAAC,KAAKA,QAAQ,EAClCC,GAAG,CAAC,CAAC,CAACtB,EAAE,CAAC,KAAKuB,QAAQ,CAACvB,EAAE,CAAC;MAE7B,IAAIiB,eAAe,CAACO,MAAK,KAAM,CAAC,EAAE;QAChC,OAAO,WAAU;MACnB;MAEA,IAAI,IAAI,CAAC9D,SAAS,CAACI,SAAS,EAAE;QAC5B,OAAO,IAAI,CAACJ,SAAS,CAACI,SAAQ,KAAM,YAAW,GAAI,WAAU,GAAI,YAAW;MAC9E;MAEA,OAAO,OAAOmD,eAAe,CAACO,MAAM,MAAK;IAC3C,CAAC;IAEDC,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAAC,IAAI,CAAC/D,SAAS,CAACM,IAAI,CAACV,IAAI,EAAE;QAC7BoE,KAAK,CAAC,SAAS;QACf,OAAO,KAAI;MACb;MAEA,MAAMT,eAAc,GAAIC,MAAM,CAACC,OAAO,CAAC,IAAI,CAACzD,SAAS,CAACK,aAAa,EAChEqB,MAAM,CAAC,CAAC,CAACgC,CAAC,EAAEC,QAAQ,CAAC,KAAKA,QAAQ,EAClCC,GAAG,CAAC,CAAC,CAACtB,EAAE,CAAC,KAAKuB,QAAQ,CAACvB,EAAE,CAAC;MAE7B,IAAIiB,eAAe,CAACO,MAAK,KAAM,KAAK,CAAC,IAAI,CAAC9D,SAAS,CAACI,SAAS,EAAE;QAC7D4D,KAAK,CAAC,WAAW;QACjB,OAAO,KAAI;MACb;MAEA,OAAO,IAAG;IACZ,CAAC;IAED,MAAMC,eAAeA,CAAA,EAAG;MACtB,IAAI,CAAC,IAAI,CAACF,gBAAgB,CAAC,CAAC,EAAE;QAC5B;MACF;MAEA,IAAI,CAAChE,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAMmE,mBAAkB,GAAI,IAAI,CAACf,sBAAsB,CAAC;QACxD,MAAMgB,iBAAgB,GAAI,IAAI,CAACb,2BAA2B,CAAC;QAE3D,MAAMc,QAAO,GAAI;UACfxE,IAAI,EAAE,IAAI,CAACI,SAAS,CAACM,IAAI,CAACV,IAAI;UAC9BW,MAAM,EAAE4D,iBAAiB;UACzBzB,QAAQ,EAAEwB,mBAAmB;UAC7BG,OAAO,EAAE,GAAG;UACZC,OAAO,EAAE,IAAI,CAAC1B,oBAAoB,CAAC,CAAC;UACpCZ,MAAM,EAAE,SAAS;UACjBxB,QAAQ,EAAE,IAAI,CAACR,SAAS,CAACM,IAAI,CAACE;QAChC;QAEA,IAAI,IAAI,CAACR,SAAS,CAACE,MAAM,EAAE;UACzB,IAAI,CAACqE,MAAM,CAACC,MAAM,CAAC,YAAY,EAAE;YAC/BlC,EAAE,EAAE,IAAI,CAACtC,SAAS,CAACG,MAAM;YACzB,GAAGiE;UACL,CAAC;QACH,OAAO;UACL,IAAI,CAACG,MAAM,CAACC,MAAM,CAAC,SAAS,EAAEJ,QAAQ;QACxC;QAEA,IAAI,CAACpE,SAAS,CAACC,IAAG,GAAI,KAAI;;QAE1B;QACA+D,KAAK,CAAC,KAAK,IAAI,CAAChE,SAAS,CAACE,MAAK,GAAI,IAAG,GAAI,IAAI,KAAK;MACrD,EAAE,OAAOuE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BT,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAACjE,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED,MAAM4E,UAAUA,CAAA,EAAG;MACjB,IAAI,CAAC5E,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,IAAI,CAACwE,MAAM,CAACC,MAAM,CAAC,YAAY,EAAE,IAAI,CAACxD,WAAW,CAACb,MAAM;QACxD,IAAI,CAACa,WAAW,CAACf,IAAG,GAAI,KAAI;;QAE5B;QACA+D,KAAK,CAAC,SAAS;MACjB,EAAE,OAAOS,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BT,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAACjE,UAAS,GAAI,KAAI;MACxB;IACF;EACF,CAAC;EACD6E,KAAK,EAAE;IACL,qBAAqBC,CAACC,QAAQ,EAAE;MAC9B,IAAIA,QAAQ,EAAE;QACZ;QACA,IAAI,CAACvD,KAAK,CAACa,OAAO,CAACC,IAAG,IAAK;UACzB,IAAIyC,QAAO,KAAM,YAAY,EAAE;YAC7B;YACA,IAAI,CAAC9E,SAAS,CAACK,aAAa,CAACgC,IAAI,CAACC,EAAE,IAAID,IAAI,CAACzC,IAAI,CAACkC,QAAQ,CAAC,QAAQ;UACrE,OAAO,IAAIgD,QAAO,KAAM,MAAM,EAAE;YAC9B;YACA,IAAI,CAAC9E,SAAS,CAACK,aAAa,CAACgC,IAAI,CAACC,EAAE,IAAI,KAAI;UAC9C,OAAO,IAAIwC,QAAO,KAAM,UAAU,EAAE;YAClC;YACA,IAAI,CAAC9E,SAAS,CAACK,aAAa,CAACgC,IAAI,CAACC,EAAE,IAAID,IAAI,CAACzC,IAAI,CAACkC,QAAQ,CAAC,IAAI;UACjE,OAAO,IAAIgD,QAAO,KAAM,aAAa,EAAE;YACrC;YACA,IAAI,CAAC9E,SAAS,CAACK,aAAa,CAACgC,IAAI,CAACC,EAAE,IAAID,IAAI,CAACzC,IAAI,CAACkC,QAAQ,CAAC,KAAK;UAClE;QACF,CAAC;MACH;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}