{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, vModelText as _vModelText, withDirectives as _withDirectives, vModelSelect as _vModelSelect, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, withCtx as _withCtx, vModelDynamic as _vModelDynamic, vModelCheckbox as _vModelCheckbox, createBlock as _createBlock, vModelRadio as _vModelRadio } from \"vue\";\nconst _hoisted_1 = {\n  class: \"bg-white shadow rounded-lg p-4 mb-6\"\n};\nconst _hoisted_2 = {\n  class: \"flex flex-wrap items-center justify-between\"\n};\nconst _hoisted_3 = {\n  class: \"flex space-x-3 mb-2 sm:mb-0\"\n};\nconst _hoisted_4 = {\n  class: \"flex items-center space-x-4\"\n};\nconst _hoisted_5 = {\n  class: \"flex items-center border rounded-md overflow-hidden\"\n};\nconst _hoisted_6 = {\n  class: \"relative\"\n};\nconst _hoisted_7 = {\n  class: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n};\nconst _hoisted_8 = {\n  class: \"relative\"\n};\nconst _hoisted_9 = {\n  class: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n};\nconst _hoisted_10 = [\"value\"];\nconst _hoisted_11 = {\n  key: 0,\n  class: \"bg-white rounded-lg shadow overflow-hidden\"\n};\nconst _hoisted_12 = {\n  class: \"px-4 py-3 bg-gray-50 border-b flex justify-between items-center\"\n};\nconst _hoisted_13 = {\n  class: \"text-sm text-gray-700\"\n};\nconst _hoisted_14 = {\n  class: \"font-medium\"\n};\nconst _hoisted_15 = {\n  class: \"font-medium\"\n};\nconst _hoisted_16 = {\n  class: \"flex space-x-2\"\n};\nconst _hoisted_17 = {\n  class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n};\nconst _hoisted_18 = {\n  class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n};\nconst _hoisted_19 = {\n  class: \"min-w-full divide-y divide-gray-200\"\n};\nconst _hoisted_20 = {\n  class: \"bg-gray-50\"\n};\nconst _hoisted_21 = {\n  scope: \"col\",\n  class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n};\nconst _hoisted_22 = {\n  class: \"bg-white divide-y divide-gray-200\"\n};\nconst _hoisted_23 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_24 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_25 = {\n  class: \"ml-2 font-medium text-gray-900\"\n};\nconst _hoisted_26 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_27 = {\n  class: \"text-sm text-gray-900\"\n};\nconst _hoisted_28 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_29 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_30 = {\n  class: \"text-sm font-medium text-gray-900\"\n};\nconst _hoisted_31 = {\n  key: 0,\n  class: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\"\n};\nconst _hoisted_32 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_33 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_34 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_35 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_36 = {\n  key: 1,\n  class: \"ml-1\"\n};\nconst _hoisted_37 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_38 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_39 = {\n  class: \"flex-grow\"\n};\nconst _hoisted_40 = [\"type\", \"value\"];\nconst _hoisted_41 = [\"onClick\"];\nconst _hoisted_42 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_43 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_44 = {\n  class: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\"\n};\nconst _hoisted_45 = {\n  class: \"flex space-x-2\"\n};\nconst _hoisted_46 = [\"onClick\"];\nconst _hoisted_47 = [\"onClick\"];\nconst _hoisted_48 = {\n  key: 0\n};\nconst _hoisted_49 = {\n  colspan: \"9\",\n  class: \"px-6 py-10 text-center\"\n};\nconst _hoisted_50 = {\n  class: \"text-gray-500\"\n};\nconst _hoisted_51 = {\n  class: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\"\n};\nconst _hoisted_52 = {\n  class: \"px-4 py-5 sm:p-6\"\n};\nconst _hoisted_53 = {\n  class: \"flex justify-between items-start mb-4\"\n};\nconst _hoisted_54 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_55 = {\n  class: \"text-lg font-medium text-gray-900\"\n};\nconst _hoisted_56 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_57 = {\n  class: \"space-y-4\"\n};\nconst _hoisted_58 = {\n  class: \"flex justify-between items-center mb-2\"\n};\nconst _hoisted_59 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_60 = {\n  class: \"text-sm font-medium text-gray-900\"\n};\nconst _hoisted_61 = {\n  key: 0,\n  class: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\"\n};\nconst _hoisted_62 = [\"onClick\"];\nconst _hoisted_63 = {\n  class: \"mb-2\"\n};\nconst _hoisted_64 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_65 = [\"type\", \"value\"];\nconst _hoisted_66 = [\"onClick\"];\nconst _hoisted_67 = {\n  class: \"grid grid-cols-2 gap-2 text-xs\"\n};\nconst _hoisted_68 = {\n  class: \"text-gray-900\"\n};\nconst _hoisted_69 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_70 = {\n  class: \"col-span-2 mt-1\"\n};\nconst _hoisted_71 = {\n  class: \"mt-4 flex justify-center\"\n};\nconst _hoisted_72 = [\"onClick\"];\nconst _hoisted_73 = {\n  class: \"mb-4\"\n};\nconst _hoisted_74 = {\n  class: \"px-3 py-2 bg-gray-50 rounded-md\"\n};\nconst _hoisted_75 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_76 = {\n  class: \"flex space-x-3\"\n};\nconst _hoisted_77 = {\n  key: 0,\n  class: \"form-group mb-4\"\n};\nconst _hoisted_78 = [\"value\"];\nconst _hoisted_79 = {\n  class: \"mt-3\"\n};\nconst _hoisted_80 = {\n  class: \"flex justify-between mb-1\"\n};\nconst _hoisted_81 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_82 = [\"type\"];\nconst _hoisted_83 = {\n  key: 1,\n  class: \"space-y-4\"\n};\nconst _hoisted_84 = {\n  class: \"form-group\"\n};\nconst _hoisted_85 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_86 = [\"type\"];\nconst _hoisted_87 = {\n  class: \"form-group\"\n};\nconst _hoisted_88 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_89 = [\"type\"];\nconst _hoisted_90 = {\n  key: 0,\n  class: \"text-sm text-red-500 mt-1\"\n};\nconst _hoisted_91 = {\n  class: \"space-y-2 mt-4\"\n};\nconst _hoisted_92 = {\n  class: \"mb-4\"\n};\nconst _hoisted_93 = {\n  class: \"px-3 py-2 bg-gray-50 rounded-md\"\n};\nconst _hoisted_94 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_95 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_96 = {\n  class: \"relative inline-block w-10 mr-2 align-middle select-none\"\n};\nconst _hoisted_97 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_98 = [\"value\"];\nconst _hoisted_99 = {\n  class: \"form-group\"\n};\nconst _hoisted_100 = {\n  class: \"flex justify-between mb-1\"\n};\nconst _hoisted_101 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_102 = [\"type\"];\nconst _hoisted_103 = {\n  class: \"form-group\"\n};\nconst _hoisted_104 = {\n  class: \"mb-2\"\n};\nconst _hoisted_105 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_106 = {\n  class: \"form-text\"\n};\nconst _hoisted_107 = {\n  class: \"form-group\"\n};\nconst _hoisted_108 = [\"value\"];\nconst _hoisted_109 = {\n  class: \"form-group\"\n};\nconst _hoisted_110 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_111 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_112 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_113 = {\n  key: 0,\n  class: \"mt-3\"\n};\nconst _hoisted_114 = {\n  class: \"grid grid-cols-2 gap-4\"\n};\nconst _hoisted_115 = {\n  class: \"form-group\"\n};\nconst _hoisted_116 = {\n  class: \"form-group\"\n};\nconst _hoisted_117 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_118 = {\n  class: \"form-group\"\n};\nconst _hoisted_119 = [\"value\"];\nconst _hoisted_120 = {\n  class: \"form-group\"\n};\nconst _hoisted_121 = {\n  class: \"form-group\"\n};\nconst _hoisted_122 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_123 = {\n  class: \"form-group\"\n};\nconst _hoisted_124 = [\"value\"];\nconst _hoisted_125 = {\n  class: \"form-group\"\n};\nconst _hoisted_126 = {\n  class: \"form-group\"\n};\nconst _hoisted_127 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_128 = {\n  class: \"mb-2\"\n};\nconst _hoisted_129 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_130 = {\n  class: \"form-text\"\n};\nconst _hoisted_131 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_132 = {\n  class: \"p-4 border border-gray-200 rounded-md\"\n};\nconst _hoisted_133 = {\n  class: \"mb-3\"\n};\nconst _hoisted_134 = {\n  class: \"mb-3\"\n};\nconst _hoisted_135 = {\n  class: \"flex items-center mb-3\"\n};\nconst _hoisted_136 = {\n  class: \"inline-flex items-center\"\n};\nconst _hoisted_137 = {\n  class: \"mb-3\"\n};\nconst _hoisted_138 = {\n  class: \"flex space-x-3\"\n};\nconst _hoisted_139 = {\n  key: 0,\n  class: \"mb-3\"\n};\nconst _hoisted_140 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_141 = [\"type\"];\nconst _hoisted_142 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_143 = [\"value\"];\nconst _hoisted_144 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_145 = {\n  class: \"space-y-2\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_CustomCheckbox = _resolveComponent(\"CustomCheckbox\");\n  const _component_StatusBadge = _resolveComponent(\"StatusBadge\");\n  const _component_PasswordStrengthMeter = _resolveComponent(\"PasswordStrengthMeter\");\n  const _component_BaseModal = _resolveComponent(\"BaseModal\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createCommentVNode(\" 操作按钮 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.showEmergencyReset && $options.showEmergencyReset(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'exclamation-triangle'],\n    class: \"mr-2\"\n  }), _cache[67] || (_cache[67] = _createElementVNode(\"span\", null, \"紧急重置\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.openBatchUpdateModal && $options.openBatchUpdateModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'key'],\n    class: \"mr-2\"\n  }), _cache[68] || (_cache[68] = _createElementVNode(\"span\", null, \"批量更新密码\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.openBatchApplyModal && $options.openBatchApplyModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"mr-2\"\n  }), _cache[69] || (_cache[69] = _createElementVNode(\"span\", null, \"批量应用策略\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n    onClick: _cache[3] || (_cache[3] = (...args) => $options.openBatchAddAccountModal && $options.openBatchAddAccountModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'users'],\n    class: \"mr-2\"\n  }), _cache[70] || (_cache[70] = _createElementVNode(\"span\", null, \"批量添加账号\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" 视图切换 \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1 focus:outline-none\", {\n      'bg-blue-500 text-white': $data.viewMode === 'table',\n      'bg-gray-100 text-gray-600': $data.viewMode !== 'table'\n    }]),\n    onClick: _cache[4] || (_cache[4] = $event => $data.viewMode = 'table')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'table'],\n    class: \"mr-1\"\n  }), _cache[71] || (_cache[71] = _createTextVNode(\" 表格 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1 focus:outline-none\", {\n      'bg-blue-500 text-white': $data.viewMode === 'card',\n      'bg-gray-100 text-gray-600': $data.viewMode !== 'card'\n    }]),\n    onClick: _cache[5] || (_cache[5] = $event => $data.viewMode = 'card')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'th-large'],\n    class: \"mr-1\"\n  }), _cache[72] || (_cache[72] = _createTextVNode(\" 卡片 \"))], 2 /* CLASS */)]), _createCommentVNode(\" 筛选 \"), _createElementVNode(\"div\", _hoisted_6, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.filterText = $event),\n    placeholder: \"筛选主机...\",\n    class: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.filterText]]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"text-gray-400\"\n  })])]), _createCommentVNode(\" 账号筛选 \"), _createElementVNode(\"div\", _hoisted_8, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.accountFilterText = $event),\n    placeholder: \"筛选账号...\",\n    class: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.accountFilterText]]), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'user'],\n    class: \"text-gray-400\"\n  })])]), _createCommentVNode(\" 状态筛选 \"), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.statusFilter = $event),\n    class: \"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n  }, _cache[73] || (_cache[73] = [_createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有状态\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"normal\"\n  }, \"正常\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"warning\"\n  }, \"警告\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"error\"\n  }, \"错误\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.statusFilter]]), _createCommentVNode(\" 显示密码过期选项 \"), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.expiryFilter = $event),\n    class: \"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n  }, _cache[74] || (_cache[74] = [_createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有密码\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"expired\"\n  }, \"已过期\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"expiring-soon\"\n  }, \"即将过期\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"valid\"\n  }, \"有效期内\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.expiryFilter]]), _createCommentVNode(\" 策略筛选 \"), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.policyFilter = $event),\n    class: \"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n  }, [_cache[75] || (_cache[75] = _createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有策略\", -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: policy.id,\n      value: policy.id\n    }, _toDisplayString(policy.name), 9 /* TEXT, PROPS */, _hoisted_10);\n  }), 128 /* KEYED_FRAGMENT */)), _cache[76] || (_cache[76] = _createElementVNode(\"option\", {\n    value: \"none\"\n  }, \"无策略\", -1 /* HOISTED */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.policyFilter]])])])]), _createCommentVNode(\" 主机列表 \"), _createCommentVNode(\" 表格视图 \"), $data.viewMode === 'table' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [_createCommentVNode(\" 账号计数和导出按钮 \"), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_cache[77] || (_cache[77] = _createTextVNode(\" 显示 \")), _createElementVNode(\"span\", _hoisted_14, _toDisplayString($options.filteredAccounts.length), 1 /* TEXT */), _cache[78] || (_cache[78] = _createTextVNode(\" 个账号 (共 \")), _createElementVNode(\"span\", _hoisted_15, _toDisplayString($options.getAllAccounts.length), 1 /* TEXT */), _cache[79] || (_cache[79] = _createTextVNode(\" 个) \"))]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"button\", _hoisted_17, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'file-export'],\n    class: \"mr-1\"\n  }), _cache[80] || (_cache[80] = _createTextVNode(\" 导出 \"))]), _createElementVNode(\"button\", _hoisted_18, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'print'],\n    class: \"mr-1\"\n  }), _cache[81] || (_cache[81] = _createTextVNode(\" 打印 \"))])])]), _createElementVNode(\"table\", _hoisted_19, [_createElementVNode(\"thead\", _hoisted_20, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", _hoisted_21, [_createVNode(_component_CustomCheckbox, {\n    modelValue: $data.selectAll,\n    \"onUpdate:modelValue\": [_cache[11] || (_cache[11] = $event => $data.selectAll = $event), $options.toggleSelectAll]\n  }, {\n    default: _withCtx(() => _cache[82] || (_cache[82] = [_createTextVNode(\" 主机名 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _cache[83] || (_cache[83] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" IP地址 \", -1 /* HOISTED */)), _cache[84] || (_cache[84] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 账号 \", -1 /* HOISTED */)), _cache[85] || (_cache[85] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 最后密码修改时间 \", -1 /* HOISTED */)), _cache[86] || (_cache[86] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码过期时间 \", -1 /* HOISTED */)), _cache[87] || (_cache[87] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码 \", -1 /* HOISTED */)), _cache[88] || (_cache[88] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 状态 \", -1 /* HOISTED */)), _cache[89] || (_cache[89] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 策略 \", -1 /* HOISTED */)), _cache[90] || (_cache[90] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 操作 \", -1 /* HOISTED */))])]), _createElementVNode(\"tbody\", _hoisted_22, [_createCommentVNode(\" 按主机分组显示 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.groupedAccounts, hostGroup => {\n    return _openBlock(), _createElementBlock(_Fragment, {\n      key: hostGroup.hostId\n    }, [_createCommentVNode(\" 账号行 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(hostGroup.accounts, (account, accountIndex) => {\n      return _openBlock(), _createElementBlock(\"tr\", {\n        key: account.id,\n        class: _normalizeClass({\n          'bg-gray-50': accountIndex % 2 === 0,\n          'hover:bg-blue-50': true\n        })\n      }, [_createElementVNode(\"td\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_createVNode(_component_CustomCheckbox, {\n        modelValue: account.host.selected,\n        \"onUpdate:modelValue\": $event => account.host.selected = $event,\n        class: \"ml-4\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"span\", _hoisted_25, _toDisplayString(account.host.name), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])])]), _createElementVNode(\"td\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, _toDisplayString(account.host.ip), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"span\", _hoisted_30, _toDisplayString(account.username), 1 /* TEXT */), account.isDefault ? (_openBlock(), _createElementBlock(\"span\", _hoisted_31, \" 默认 \")) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"td\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, _toDisplayString(account.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_34, [_createElementVNode(\"div\", {\n        class: _normalizeClass({\n          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\n          'bg-red-100 text-red-800': $options.isPasswordExpired(account).status === 'danger' || $options.isPasswordExpired(account).status === 'expired',\n          'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(account).status === 'warning',\n          'bg-gray-100 text-gray-800': $options.isPasswordExpired(account).status === 'normal'\n        })\n      }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(account).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(account).status === 'expired' || $options.isPasswordExpired(account).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_35, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-triangle']\n      })])) : $options.isPasswordExpired(account).status === 'warning' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_36, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-circle']\n      })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]), _createElementVNode(\"td\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_createElementVNode(\"input\", {\n        type: $data.passwordVisibility[account.id] ? 'text' : 'password',\n        value: account.password,\n        readonly: \"\",\n        class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n      }, null, 8 /* PROPS */, _hoisted_40)]), _createElementVNode(\"button\", {\n        onClick: $event => $options.togglePasswordVisibility(account.id),\n        class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $data.passwordVisibility[account.id] ? 'eye-slash' : 'eye'],\n        class: \"text-lg\"\n      }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_41)])]), _createElementVNode(\"td\", _hoisted_42, [_createVNode(_component_StatusBadge, {\n        type: account.host.status\n      }, null, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"td\", _hoisted_43, [_createElementVNode(\"div\", {\n        class: _normalizeClass(['inline-flex items-center px-2 py-0.5 rounded text-xs font-medium', $options.getPolicyColorClass(account.policyId)])\n      }, _toDisplayString($options.getPolicyName(account.policyId)), 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", _hoisted_44, [_createElementVNode(\"div\", _hoisted_45, [_createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.openChangePasswordModal(account.host, account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'key'],\n        class: \"mr-1\"\n      }), _cache[91] || (_cache[91] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_46), _createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.copyPassword(account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'copy'],\n        class: \"mr-1\"\n      }), _cache[92] || (_cache[92] = _createTextVNode(\" 复制 \"))], 8 /* PROPS */, _hoisted_47)])])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))], 64 /* STABLE_FRAGMENT */);\n  }), 128 /* KEYED_FRAGMENT */)), _createCommentVNode(\" 无数据显示 \"), $options.filteredAccounts.length === 0 ? (_openBlock(), _createElementBlock(\"tr\", _hoisted_48, [_createElementVNode(\"td\", _hoisted_49, [_createElementVNode(\"div\", _hoisted_50, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"text-4xl mb-3\"\n  }), _cache[93] || (_cache[93] = _createElementVNode(\"p\", null, \"没有找到匹配的账号数据\", -1 /* HOISTED */))])])])) : _createCommentVNode(\"v-if\", true)])])])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 卡片视图 \"), _createElementVNode(\"div\", _hoisted_51, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredHosts, host => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: host.id,\n      class: \"bg-white overflow-hidden shadow rounded-lg\"\n    }, [_createElementVNode(\"div\", _hoisted_52, [_createCommentVNode(\" 主机头部 \"), _createElementVNode(\"div\", _hoisted_53, [_createElementVNode(\"div\", _hoisted_54, [_createVNode(_component_CustomCheckbox, {\n      modelValue: host.selected,\n      \"onUpdate:modelValue\": $event => host.selected = $event,\n      class: \"mr-2\"\n    }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"]), _createElementVNode(\"div\", null, [_createElementVNode(\"h3\", _hoisted_55, _toDisplayString(host.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_56, _toDisplayString(host.ip), 1 /* TEXT */)])]), _createVNode(_component_StatusBadge, {\n      type: host.status\n    }, null, 8 /* PROPS */, [\"type\"])]), _createCommentVNode(\" 账号列表 \"), _createElementVNode(\"div\", _hoisted_57, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(host.accounts, account => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: account.id,\n        class: _normalizeClass([\"border border-gray-200 rounded-lg p-3\", {\n          'border-green-300 bg-green-50': account.isDefault\n        }])\n      }, [_createElementVNode(\"div\", _hoisted_58, [_createElementVNode(\"div\", _hoisted_59, [_createElementVNode(\"span\", _hoisted_60, _toDisplayString(account.username), 1 /* TEXT */), account.isDefault ? (_openBlock(), _createElementBlock(\"span\", _hoisted_61, \" 默认 \")) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.openChangePasswordModal(host, account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'key'],\n        class: \"mr-1\"\n      }), _cache[94] || (_cache[94] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_62)]), _createCommentVNode(\" 密码展示 \"), _createElementVNode(\"div\", _hoisted_63, [_cache[95] || (_cache[95] = _createElementVNode(\"div\", {\n        class: \"text-xs font-medium text-gray-500 mb-1\"\n      }, \"密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_64, [_createElementVNode(\"input\", {\n        type: $data.passwordVisibility[account.id] ? 'text' : 'password',\n        value: account.password,\n        readonly: \"\",\n        class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n      }, null, 8 /* PROPS */, _hoisted_65), _createElementVNode(\"button\", {\n        onClick: $event => $options.togglePasswordVisibility(account.id),\n        class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $data.passwordVisibility[account.id] ? 'eye-slash' : 'eye'],\n        class: \"text-lg\"\n      }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_66)])]), _createCommentVNode(\" 密码信息区域 \"), _createElementVNode(\"div\", _hoisted_67, [_createElementVNode(\"div\", null, [_cache[96] || (_cache[96] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"最后修改时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_68, _toDisplayString(account.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[97] || (_cache[97] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"密码过期\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n        class: _normalizeClass({\n          'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\n          'bg-red-100 text-red-800': $options.isPasswordExpired(account).status === 'danger' || $options.isPasswordExpired(account).status === 'expired',\n          'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(account).status === 'warning',\n          'bg-gray-100 text-gray-800': $options.isPasswordExpired(account).status === 'normal'\n        })\n      }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(account).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(account).status === 'expired' || $options.isPasswordExpired(account).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_69, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-triangle']\n      })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_70, [_cache[98] || (_cache[98] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"密码策略\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n        class: _normalizeClass(['inline-flex items-center px-2 py-0.5 rounded text-xs font-medium', $options.getPolicyColorClass(account.policyId)])\n      }, _toDisplayString($options.getPolicyName(account.policyId)), 3 /* TEXT, CLASS */)])])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 添加账号按钮 \"), _createElementVNode(\"div\", _hoisted_71, [_createElementVNode(\"button\", {\n      class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n      onClick: $event => $options.openAddAccountModal(host)\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'plus'],\n      class: \"mr-1\"\n    }), _cache[99] || (_cache[99] = _createTextVNode(\" 添加账号 \"))], 8 /* PROPS */, _hoisted_72)])])]);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 修改密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.changePasswordModal.show,\n    \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.changePasswordModal.show = $event),\n    title: \"修改密码\",\n    onConfirm: $options.updatePassword,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_73, [_cache[103] || (_cache[103] = _createElementVNode(\"div\", {\n      class: \"font-medium mb-2\"\n    }, \"主机信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_74, [_createElementVNode(\"div\", null, [_cache[100] || (_cache[100] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"主机名:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[101] || (_cache[101] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"IP地址:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.ip), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[102] || (_cache[102] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"账号:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentAccount.username), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_75, [_cache[106] || (_cache[106] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_76, [_createElementVNode(\"button\", {\n      onClick: _cache[12] || (_cache[12] = $event => $data.changePasswordModal.method = 'auto'),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", $data.changePasswordModal.method === 'auto' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-2\"\n    }), _cache[104] || (_cache[104] = _createTextVNode(\" 自动生成 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n      onClick: _cache[13] || (_cache[13] = $event => $data.changePasswordModal.method = 'manual'),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", $data.changePasswordModal.method === 'manual' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'edit'],\n      class: \"mr-2\"\n    }), _cache[105] || (_cache[105] = _createTextVNode(\" 手动输入 \"))], 2 /* CLASS */)])]), $data.changePasswordModal.method === 'auto' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_77, [_cache[109] || (_cache[109] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.changePasswordModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[15] || (_cache[15] = $event => $options.generatePassword())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_78);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.changePasswordModal.policyId]]), _createElementVNode(\"div\", _hoisted_79, [_createElementVNode(\"div\", _hoisted_80, [_cache[108] || (_cache[108] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      onClick: _cache[16] || (_cache[16] = $event => $options.generatePassword()),\n      type: \"button\",\n      class: \"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[107] || (_cache[107] = _createTextVNode(\" 重新生成 \"))])]), _createElementVNode(\"div\", _hoisted_81, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.generated ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.changePasswordModal.generatedPassword = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_82), [[_vModelDynamic, $data.changePasswordModal.generatedPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[18] || (_cache[18] = $event => $data.passwordVisibility.generated = !$data.passwordVisibility.generated),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.generated ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_83, [_createElementVNode(\"div\", _hoisted_84, [_cache[110] || (_cache[110] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"新密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_85, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.new ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[19] || (_cache[19] = $event => $data.changePasswordModal.newPassword = $event),\n      class: \"form-control flex-1\",\n      placeholder: \"输入新密码\"\n    }, null, 8 /* PROPS */, _hoisted_86), [[_vModelDynamic, $data.changePasswordModal.newPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[20] || (_cache[20] = $event => $data.passwordVisibility.new = !$data.passwordVisibility.new),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.new ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])]), _createElementVNode(\"div\", _hoisted_87, [_cache[111] || (_cache[111] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"确认密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_88, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.confirm ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[21] || (_cache[21] = $event => $data.changePasswordModal.confirmPassword = $event),\n      class: _normalizeClass([\"form-control flex-1\", {\n        'border-red-500': $options.passwordMismatch\n      }]),\n      placeholder: \"再次输入新密码\"\n    }, null, 10 /* CLASS, PROPS */, _hoisted_89), [[_vModelDynamic, $data.changePasswordModal.confirmPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[22] || (_cache[22] = $event => $data.passwordVisibility.confirm = !$data.passwordVisibility.confirm),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.confirm ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])]), $options.passwordMismatch ? (_openBlock(), _createElementBlock(\"div\", _hoisted_90, \"两次输入的密码不一致\")) : _createCommentVNode(\"v-if\", true)]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.newPassword\n    }, null, 8 /* PROPS */, [\"password\"])])), _createElementVNode(\"div\", _hoisted_91, [_cache[115] || (_cache[115] = _createElementVNode(\"div\", {\n      class: \"form-label font-medium\"\n    }, \"执行选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.executeImmediately,\n      \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.changePasswordModal.executeImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[112] || (_cache[112] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"立即执行\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.saveHistory,\n      \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.changePasswordModal.saveHistory = $event)\n    }, {\n      default: _withCtx(() => _cache[113] || (_cache[113] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"保存历史记录\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.logAudit,\n      \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.changePasswordModal.logAudit = $event)\n    }, {\n      default: _withCtx(() => _cache[114] || (_cache[114] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"记录审计日志\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 添加账号弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.addAccountModal.show,\n    \"onUpdate:modelValue\": _cache[34] || (_cache[34] = $event => $data.addAccountModal.show = $event),\n    title: \"添加账号\",\n    onConfirm: $options.addAccount,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_92, [_cache[118] || (_cache[118] = _createElementVNode(\"div\", {\n      class: \"font-medium mb-2\"\n    }, \"主机信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_93, [_createElementVNode(\"div\", null, [_cache[116] || (_cache[116] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"主机名:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[117] || (_cache[117] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"IP地址:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.ip), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_94, [_cache[119] || (_cache[119] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"账号名称\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.addAccountModal.username = $event),\n      class: \"form-control\",\n      placeholder: \"输入账号名称\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addAccountModal.username]])]), _createElementVNode(\"div\", _hoisted_95, [_cache[121] || (_cache[121] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"设为默认账号\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_96, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $data.addAccountModal.isDefault = $event),\n      class: \"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.addAccountModal.isDefault]]), _cache[120] || (_cache[120] = _createElementVNode(\"label\", {\n      class: \"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"\n    }, null, -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_97, [_cache[122] || (_cache[122] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[29] || (_cache[29] = $event => $data.addAccountModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[30] || (_cache[30] = $event => $options.generatePasswordForNewAccount())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_98);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.addAccountModal.policyId]])]), _createElementVNode(\"div\", _hoisted_99, [_createElementVNode(\"div\", _hoisted_100, [_cache[124] || (_cache[124] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      onClick: _cache[31] || (_cache[31] = $event => $options.generatePasswordForNewAccount()),\n      type: \"button\",\n      class: \"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[123] || (_cache[123] = _createTextVNode(\" 重新生成 \"))])]), _createElementVNode(\"div\", _hoisted_101, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.newAccount ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[32] || (_cache[32] = $event => $data.addAccountModal.password = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_102), [[_vModelDynamic, $data.addAccountModal.password]]), _createElementVNode(\"button\", {\n      onClick: _cache[33] || (_cache[33] = $event => $data.passwordVisibility.newAccount = !$data.passwordVisibility.newAccount),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.newAccount ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量更新密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchUpdateModal.show,\n    \"onUpdate:modelValue\": _cache[44] || (_cache[44] = $event => $data.batchUpdateModal.show = $event),\n    title: \"批量更新密码\",\n    \"confirm-text\": \"开始更新\",\n    size: \"lg\",\n    onConfirm: $options.batchUpdatePasswords,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_103, [_cache[126] || (_cache[126] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_104, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatch,\n      \"onUpdate:modelValue\": [_cache[35] || (_cache[35] = $event => $data.selectAllBatch = $event), $options.toggleSelectAllBatch]\n    }, {\n      default: _withCtx(() => _cache[125] || (_cache[125] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_105, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchUpdateModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchUpdateModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_106, \"已选择 \" + _toDisplayString($options.selectedHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_107, [_cache[127] || (_cache[127] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[36] || (_cache[36] = $event => $data.batchUpdateModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_108);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchUpdateModal.policyId]])]), _createElementVNode(\"div\", _hoisted_109, [_cache[132] || (_cache[132] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_110, [_createElementVNode(\"label\", _hoisted_111, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[37] || (_cache[37] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"immediate\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[128] || (_cache[128] = _createElementVNode(\"span\", null, \"立即执行\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_112, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[38] || (_cache[38] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"scheduled\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[129] || (_cache[129] = _createElementVNode(\"span\", null, \"定时执行\", -1 /* HOISTED */))])]), $data.batchUpdateModal.executionTime === 'scheduled' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_113, [_createElementVNode(\"div\", _hoisted_114, [_createElementVNode(\"div\", null, [_cache[130] || (_cache[130] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"日期\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"date\",\n      \"onUpdate:modelValue\": _cache[39] || (_cache[39] = $event => $data.batchUpdateModal.scheduledDate = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledDate]])]), _createElementVNode(\"div\", null, [_cache[131] || (_cache[131] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"时间\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"time\",\n      \"onUpdate:modelValue\": _cache[40] || (_cache[40] = $event => $data.batchUpdateModal.scheduledTime = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledTime]])])])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_115, [_cache[136] || (_cache[136] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[41] || (_cache[41] = $event => $data.batchUpdateModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[133] || (_cache[133] = [_createTextVNode(\" 忽略错误继续执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.detailedLog,\n      \"onUpdate:modelValue\": _cache[42] || (_cache[42] = $event => $data.batchUpdateModal.detailedLog = $event)\n    }, {\n      default: _withCtx(() => _cache[134] || (_cache[134] = [_createTextVNode(\" 记录详细日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.sendNotification,\n      \"onUpdate:modelValue\": _cache[43] || (_cache[43] = $event => $data.batchUpdateModal.sendNotification = $event)\n    }, {\n      default: _withCtx(() => _cache[135] || (_cache[135] = [_createTextVNode(\" 执行完成后发送通知 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量应用策略弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchApplyModal.show,\n    \"onUpdate:modelValue\": _cache[48] || (_cache[48] = $event => $data.batchApplyModal.show = $event),\n    title: \"批量应用密码策略\",\n    \"confirm-text\": \"应用策略\",\n    onConfirm: $options.batchApplyPolicy,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_116, [_cache[137] || (_cache[137] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_117, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchApplyModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchApplyModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_118, [_cache[138] || (_cache[138] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[45] || (_cache[45] = $event => $data.batchApplyModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name), 9 /* TEXT, PROPS */, _hoisted_119);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchApplyModal.policyId]])]), _createElementVNode(\"div\", _hoisted_120, [_cache[141] || (_cache[141] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.updateImmediately,\n      \"onUpdate:modelValue\": _cache[46] || (_cache[46] = $event => $data.batchApplyModal.updateImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[139] || (_cache[139] = [_createTextVNode(\" 立即更新密码以符合策略 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.applyOnNextUpdate,\n      \"onUpdate:modelValue\": _cache[47] || (_cache[47] = $event => $data.batchApplyModal.applyOnNextUpdate = $event)\n    }, {\n      default: _withCtx(() => _cache[140] || (_cache[140] = [_createTextVNode(\" 下次密码更新时应用 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 紧急重置密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.emergencyResetModal.show,\n    \"onUpdate:modelValue\": _cache[52] || (_cache[52] = $event => $data.emergencyResetModal.show = $event),\n    title: \"紧急密码重置\",\n    \"confirm-text\": \"立即重置\",\n    icon: \"exclamation-triangle\",\n    danger: \"\",\n    onConfirm: $options.emergencyReset,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_cache[147] || (_cache[147] = _createElementVNode(\"div\", {\n      class: \"bg-red-50 text-red-700 p-3 rounded-md mb-4\"\n    }, [_createElementVNode(\"p\", null, \"紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_121, [_cache[142] || (_cache[142] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_122, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.emergencyResetModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.emergencyResetModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_123, [_cache[143] || (_cache[143] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用紧急策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[49] || (_cache[49] = $event => $data.emergencyResetModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.emergencyPolicies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_124);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.policyId]])]), _createElementVNode(\"div\", _hoisted_125, [_cache[145] || (_cache[145] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"操作原因\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[50] || (_cache[50] = $event => $data.emergencyResetModal.reason = $event),\n      class: \"form-select\"\n    }, _cache[144] || (_cache[144] = [_createElementVNode(\"option\", {\n      value: \"security_incident\"\n    }, \"安全事件响应\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"password_leak\"\n    }, \"密码泄露\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"abnormal_access\"\n    }, \"异常访问\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"compliance\"\n    }, \"合规要求\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"other\"\n    }, \"其他原因\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.reason]])]), _createElementVNode(\"div\", _hoisted_126, [_cache[146] || (_cache[146] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"附加说明\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n      \"onUpdate:modelValue\": _cache[51] || (_cache[51] = $event => $data.emergencyResetModal.description = $event),\n      class: \"form-control\",\n      rows: \"2\",\n      placeholder: \"请输入重置原因详细说明\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.emergencyResetModal.description]])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量添加账号弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchAddAccountModal.show,\n    \"onUpdate:modelValue\": _cache[66] || (_cache[66] = $event => $data.batchAddAccountModal.show = $event),\n    title: \"批量添加账号\",\n    size: \"lg\",\n    onConfirm: $options.batchAddAccounts,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_127, [_cache[149] || (_cache[149] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_128, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatchAdd,\n      \"onUpdate:modelValue\": [_cache[53] || (_cache[53] = $event => $data.selectAllBatchAdd = $event), $options.toggleSelectAllBatchAdd]\n    }, {\n      default: _withCtx(() => _cache[148] || (_cache[148] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_129, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchAddAccountModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchAddAccountModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_130, \"已选择 \" + _toDisplayString($options.selectedBatchAddHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_131, [_cache[160] || (_cache[160] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"账号信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_132, [_createElementVNode(\"div\", _hoisted_133, [_cache[150] || (_cache[150] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, [_createTextVNode(\"账号名称 \"), _createElementVNode(\"span\", {\n      class: \"text-red-500\"\n    }, \"*\")], -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[54] || (_cache[54] = $event => $data.batchAddAccountModal.username = $event),\n      class: \"form-control\",\n      placeholder: \"输入统一账号名称\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchAddAccountModal.username]]), _cache[151] || (_cache[151] = _createElementVNode(\"div\", {\n      class: \"text-xs text-gray-500 mt-1\"\n    }, \"将在所有选中主机上创建同名账号\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_134, [_cache[153] || (_cache[153] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"账号角色\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[55] || (_cache[55] = $event => $data.batchAddAccountModal.role = $event),\n      class: \"form-select\"\n    }, _cache[152] || (_cache[152] = [_createElementVNode(\"option\", {\n      value: \"admin\"\n    }, \"管理员\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"user\"\n    }, \"普通用户\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"service\"\n    }, \"服务账号\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"readonly\"\n    }, \"只读账号\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchAddAccountModal.role]])]), _createElementVNode(\"div\", _hoisted_135, [_createElementVNode(\"label\", _hoisted_136, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[56] || (_cache[56] = $event => $data.batchAddAccountModal.setAsDefault = $event),\n      class: \"form-checkbox\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.batchAddAccountModal.setAsDefault]]), _cache[154] || (_cache[154] = _createElementVNode(\"span\", {\n      class: \"ml-2\"\n    }, \"设为默认账号\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_137, [_cache[157] || (_cache[157] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_138, [_createElementVNode(\"button\", {\n      onClick: _cache[57] || (_cache[57] = $event => $data.batchAddAccountModal.useSamePassword = true),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", $data.batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'key'],\n      class: \"mr-2\"\n    }), _cache[155] || (_cache[155] = _createTextVNode(\" 同一密码 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n      onClick: _cache[58] || (_cache[58] = $event => $data.batchAddAccountModal.useSamePassword = false),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", !$data.batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'random'],\n      class: \"mr-2\"\n    }), _cache[156] || (_cache[156] = _createTextVNode(\" 随机密码 \"))], 2 /* CLASS */)])]), $data.batchAddAccountModal.useSamePassword ? (_openBlock(), _createElementBlock(\"div\", _hoisted_139, [_cache[159] || (_cache[159] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"统一密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_140, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.batchPassword ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[59] || (_cache[59] = $event => $data.batchAddAccountModal.password = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_141), [[_vModelDynamic, $data.batchAddAccountModal.password]]), _createElementVNode(\"button\", {\n      onClick: _cache[60] || (_cache[60] = $event => $data.passwordVisibility.batchPassword = !$data.passwordVisibility.batchPassword),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.batchPassword ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])]), _createElementVNode(\"button\", {\n      onClick: _cache[61] || (_cache[61] = $event => $options.generatePasswordForBatchAccount()),\n      type: \"button\",\n      class: \"ml-2 px-3 py-1.5 border border-gray-300 text-xs rounded-md\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[158] || (_cache[158] = _createTextVNode(\" 重新生成 \"))])])])) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_142, [_cache[161] || (_cache[161] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[62] || (_cache[62] = $event => $data.batchAddAccountModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[63] || (_cache[63] = $event => $options.generatePasswordForBatchAccount())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_143);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.batchAddAccountModal.policyId]])]), _createElementVNode(\"div\", _hoisted_144, [_cache[164] || (_cache[164] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_145, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchAddAccountModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[64] || (_cache[64] = $event => $data.batchAddAccountModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[162] || (_cache[162] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"忽略错误继续执行\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchAddAccountModal.generateReport,\n      \"onUpdate:modelValue\": _cache[65] || (_cache[65] = $event => $data.batchAddAccountModal.generateReport = $event)\n    }, {\n      default: _withCtx(() => _cache[163] || (_cache[163] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"生成账号创建报告\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "scope", "colspan", "_createElementBlock", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "args", "$options", "showEmergencyReset", "_createVNode", "_component_font_awesome_icon", "icon", "openBatchUpdateModal", "openBatchApplyModal", "openBatchAddAccountModal", "_hoisted_4", "_hoisted_5", "_normalizeClass", "$data", "viewMode", "$event", "_createTextVNode", "_hoisted_6", "type", "filterText", "placeholder", "_hoisted_7", "_hoisted_8", "accountFilterText", "_hoisted_9", "statusFilter", "value", "expiryFilter", "policyFilter", "_Fragment", "_renderList", "_ctx", "policies", "policy", "id", "name", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_toDisplayString", "filteredAccounts", "length", "_hoisted_15", "getAllAccounts", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_component_CustomCheckbox", "modelValue", "selectAll", "toggleSelectAll", "default", "_withCtx", "_", "_hoisted_22", "groupedAccounts", "hostGroup", "hostId", "accounts", "account", "accountIndex", "_hoisted_23", "_hoisted_24", "host", "selected", "_hoisted_25", "_hoisted_26", "_hoisted_27", "ip", "_hoisted_28", "_hoisted_29", "_hoisted_30", "username", "isDefault", "_hoisted_31", "_hoisted_32", "_hoisted_33", "lastPasswordChange", "_hoisted_34", "isPasswordExpired", "status", "text", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "passwordVisibility", "password", "readonly", "_hoisted_40", "togglePasswordVisibility", "_hoisted_41", "_hoisted_42", "_component_StatusBadge", "_hoisted_43", "getPolicyColorClass", "policyId", "getPolicyName", "_hoisted_44", "_hoisted_45", "openChangePasswordModal", "_hoisted_46", "copyPassword", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_hoisted_50", "_hoisted_51", "filteredHosts", "_hoisted_52", "_hoisted_53", "_hoisted_54", "_hoisted_55", "_hoisted_56", "_hoisted_57", "_hoisted_58", "_hoisted_59", "_hoisted_60", "_hoisted_61", "_hoisted_62", "_hoisted_63", "_hoisted_64", "_hoisted_65", "_hoisted_66", "_hoisted_67", "_hoisted_68", "_hoisted_69", "_hoisted_70", "_hoisted_71", "openAddAccountModal", "_hoisted_72", "_component_BaseModal", "changePasswordModal", "show", "title", "onConfirm", "updatePassword", "loading", "processing", "_hoisted_73", "_hoisted_74", "currentHost", "currentAccount", "_hoisted_75", "_hoisted_76", "method", "_hoisted_77", "onChange", "generatePassword", "<PERSON><PERSON><PERSON><PERSON>", "expiryDays", "_hoisted_78", "_hoisted_79", "_hoisted_80", "_hoisted_81", "generated", "generatedPassword", "_hoisted_82", "_hoisted_83", "_hoisted_84", "_hoisted_85", "new", "newPassword", "_hoisted_86", "_hoisted_87", "_hoisted_88", "confirm", "confirmPassword", "passwordMismatch", "_hoisted_89", "_hoisted_90", "_component_PasswordStrengthMeter", "_hoisted_91", "executeImmediately", "saveHistory", "logAudit", "addAccountModal", "addAccount", "_hoisted_92", "_hoisted_93", "_hoisted_94", "_hoisted_95", "_hoisted_96", "_hoisted_97", "generatePasswordForNewAccount", "_hoisted_98", "_hoisted_99", "_hoisted_100", "_hoisted_101", "newAccount", "_hoisted_102", "batchUpdateModal", "size", "batchUpdatePasswords", "_hoisted_103", "_hoisted_104", "selectAllBatch", "toggleSelectAllBatch", "_hoisted_105", "hosts", "_createBlock", "selectedHosts", "_hoisted_106", "selectedHostsCount", "_hoisted_107", "_hoisted_108", "_hoisted_109", "_hoisted_110", "_hoisted_111", "executionTime", "_hoisted_112", "_hoisted_113", "_hoisted_114", "scheduledDate", "scheduledTime", "_hoisted_115", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "batchApplyPolicy", "_hoisted_116", "_hoisted_117", "selectedHostsList", "_hoisted_118", "_hoisted_119", "_hoisted_120", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "danger", "emergencyReset", "_hoisted_121", "_hoisted_122", "_hoisted_123", "emergencyPolicies", "_hoisted_124", "_hoisted_125", "reason", "_hoisted_126", "description", "rows", "batchAddAccountModal", "batchAddAccounts", "_hoisted_127", "_hoisted_128", "selectAllBatchAdd", "toggleSelectAllBatchAdd", "_hoisted_129", "_hoisted_130", "selectedBatchAddHostsCount", "_hoisted_131", "_hoisted_132", "_hoisted_133", "_hoisted_134", "role", "_hoisted_135", "_hoisted_136", "setAsDefault", "_hoisted_137", "_hoisted_138", "useSamePassword", "_hoisted_139", "_hoisted_140", "batchPassword", "_hoisted_141", "generatePasswordForBatchAccount", "_hoisted_142", "_hoisted_143", "_hoisted_144", "_hoisted_145", "generateReport"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"bg-white shadow rounded-lg p-4 mb-6\">\r\n      <div class=\"flex flex-wrap items-center justify-between\">\r\n        <div class=\"flex space-x-3 mb-2 sm:mb-0\">\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\r\n            @click=\"showEmergencyReset\">\r\n            <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2\" />\r\n            <span>紧急重置</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            @click=\"openBatchUpdateModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n            <span>批量更新密码</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\r\n            @click=\"openBatchApplyModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n            <span>批量应用策略</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\r\n            @click=\"openBatchAddAccountModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'users']\" class=\"mr-2\" />\r\n            <span>批量添加账号</span>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"flex items-center space-x-4\">\r\n          <!-- 视图切换 -->\r\n          <div class=\"flex items-center border rounded-md overflow-hidden\">\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'table', 'bg-gray-100 text-gray-600': viewMode !== 'table' }\"\r\n              @click=\"viewMode = 'table'\">\r\n              <font-awesome-icon :icon=\"['fas', 'table']\" class=\"mr-1\" />\r\n              表格\r\n            </button>\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'card', 'bg-gray-100 text-gray-600': viewMode !== 'card' }\"\r\n              @click=\"viewMode = 'card'\">\r\n              <font-awesome-icon :icon=\"['fas', 'th-large']\" class=\"mr-1\" />\r\n              卡片\r\n            </button>\r\n          </div>\r\n\r\n          <!-- 筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"filterText\" placeholder=\"筛选主机...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 账号筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"accountFilterText\" placeholder=\"筛选账号...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'user']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 状态筛选 -->\r\n          <select v-model=\"statusFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有状态</option>\r\n            <option value=\"normal\">正常</option>\r\n            <option value=\"warning\">警告</option>\r\n            <option value=\"error\">错误</option>\r\n          </select>\r\n\r\n          <!-- 显示密码过期选项 -->\r\n          <select v-model=\"expiryFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有密码</option>\r\n            <option value=\"expired\">已过期</option>\r\n            <option value=\"expiring-soon\">即将过期</option>\r\n            <option value=\"valid\">有效期内</option>\r\n          </select>\r\n          \r\n          <!-- 策略筛选 -->\r\n          <select v-model=\"policyFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有策略</option>\r\n            <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n              {{ policy.name }}\r\n            </option>\r\n            <option value=\"none\">无策略</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <!-- 表格视图 -->\r\n    <div v-if=\"viewMode === 'table'\" class=\"bg-white rounded-lg shadow overflow-hidden\">\r\n      <!-- 账号计数和导出按钮 -->\r\n      <div class=\"px-4 py-3 bg-gray-50 border-b flex justify-between items-center\">\r\n        <div class=\"text-sm text-gray-700\">\r\n          显示 <span class=\"font-medium\">{{ filteredAccounts.length }}</span> 个账号\r\n          (共 <span class=\"font-medium\">{{ getAllAccounts.length }}</span> 个)\r\n        </div>\r\n        <div class=\"flex space-x-2\">\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\r\n            <font-awesome-icon :icon=\"['fas', 'file-export']\" class=\"mr-1\" />\r\n            导出\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\r\n            <font-awesome-icon :icon=\"['fas', 'print']\" class=\"mr-1\" />\r\n            打印\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <table class=\"min-w-full divide-y divide-gray-200\">\r\n        <thead class=\"bg-gray-50\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n                主机名\r\n              </CustomCheckbox>\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              IP地址\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              账号\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              最后密码修改时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码过期时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              状态\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              策略\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              操作\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"bg-white divide-y divide-gray-200\">\r\n          <!-- 按主机分组显示 -->\r\n          <template v-for=\"hostGroup in groupedAccounts\" :key=\"hostGroup.hostId\">\r\n            <!-- 账号行 -->\r\n            <tr v-for=\"(account, accountIndex) in hostGroup.accounts\" :key=\"account.id\"\r\n              :class=\"{ 'bg-gray-50': accountIndex % 2 === 0, 'hover:bg-blue-50': true }\">\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <CustomCheckbox v-model=\"account.host.selected\" class=\"ml-4\">\r\n                    <span class=\"ml-2 font-medium text-gray-900\">{{ account.host.name }}</span>\r\n                  </CustomCheckbox>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-900\">{{ account.host.ip }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-500\">{{ account.lastPasswordChange || '-' }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div :class=\"{\r\n                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\r\n                  'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                  'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                  'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                }\">\r\n                  {{ isPasswordExpired(account).text }}\r\n                  <span\r\n                    v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                    class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                  </span>\r\n                  <span v-else-if=\"isPasswordExpired(account).status === 'warning'\" class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" />\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <div class=\"flex-grow\">\r\n                    <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\"\r\n                      readonly\r\n                      class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  </div>\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <StatusBadge :type=\"account.host.status\" />\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div :class=\"['inline-flex items-center px-2 py-0.5 rounded text-xs font-medium', getPolicyColorClass(account.policyId)]\">\r\n                  {{ getPolicyName(account.policyId) }}\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                <div class=\"flex space-x-2\">\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"openChangePasswordModal(account.host, account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                    修改密码\r\n                  </button>\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"copyPassword(account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'copy']\" class=\"mr-1\" />\r\n                    复制\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          </template>\r\n          <!-- 无数据显示 -->\r\n          <tr v-if=\"filteredAccounts.length === 0\">\r\n            <td colspan=\"9\" class=\"px-6 py-10 text-center\">\r\n              <div class=\"text-gray-500\">\r\n                <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-4xl mb-3\" />\r\n                <p>没有找到匹配的账号数据</p>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <!-- 卡片视图 -->\r\n    <div v-else class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\">\r\n      <div v-for=\"host in filteredHosts\" :key=\"host.id\" class=\"bg-white overflow-hidden shadow rounded-lg\">\r\n        <div class=\"px-4 py-5 sm:p-6\">\r\n          <!-- 主机头部 -->\r\n          <div class=\"flex justify-between items-start mb-4\">\r\n            <div class=\"flex items-center\">\r\n              <CustomCheckbox v-model=\"host.selected\" class=\"mr-2\" />\r\n              <div>\r\n                <h3 class=\"text-lg font-medium text-gray-900\">{{ host.name }}</h3>\r\n                <p class=\"text-sm text-gray-500\">{{ host.ip }}</p>\r\n              </div>\r\n            </div>\r\n            <StatusBadge :type=\"host.status\" />\r\n          </div>\r\n\r\n          <!-- 账号列表 -->\r\n          <div class=\"space-y-4\">\r\n            <div v-for=\"account in host.accounts\" :key=\"account.id\" class=\"border border-gray-200 rounded-lg p-3\"\r\n              :class=\"{ 'border-green-300 bg-green-50': account.isDefault }\">\r\n              <div class=\"flex justify-between items-center mb-2\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n                <button\r\n                  class=\"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                  @click=\"openChangePasswordModal(host, account)\">\r\n                  <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                  修改密码\r\n                </button>\r\n              </div>\r\n\r\n              <!-- 密码展示 -->\r\n              <div class=\"mb-2\">\r\n                <div class=\"text-xs font-medium text-gray-500 mb-1\">密码</div>\r\n                <div class=\"flex items-center\">\r\n                  <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\" readonly\r\n                    class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 密码信息区域 -->\r\n              <div class=\"grid grid-cols-2 gap-2 text-xs\">\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">最后修改时间</div>\r\n                  <div class=\"text-gray-900\">{{ account.lastPasswordChange || '-' }}</div>\r\n                </div>\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">密码过期</div>\r\n                  <div :class=\"{\r\n                    'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\r\n                    'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                    'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                    'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                  }\">\r\n                    {{ isPasswordExpired(account).text }}\r\n                    <span\r\n                      v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                      class=\"ml-1\">\r\n                      <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-span-2 mt-1\">\r\n                  <div class=\"font-medium text-gray-500 mb-1\">密码策略</div>\r\n                  <div :class=\"['inline-flex items-center px-2 py-0.5 rounded text-xs font-medium', getPolicyColorClass(account.policyId)]\">\r\n                    {{ getPolicyName(account.policyId) }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 添加账号按钮 -->\r\n          <div class=\"mt-4 flex justify-center\">\r\n            <button\r\n              class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n              @click=\"openAddAccountModal(host)\">\r\n              <font-awesome-icon :icon=\"['fas', 'plus']\" class=\"mr-1\" />\r\n              添加账号\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" @confirm=\"updatePassword\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n          <div><span class=\"font-medium\">账号:</span> {{ currentAccount.username }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-3\">\r\n          <button @click=\"changePasswordModal.method = 'auto'\"\r\n            class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n            :class=\"changePasswordModal.method === 'auto' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-2\" />\r\n            自动生成\r\n          </button>\r\n          <button @click=\"changePasswordModal.method = 'manual'\"\r\n            class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n            :class=\"changePasswordModal.method === 'manual' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n            <font-awesome-icon :icon=\"['fas', 'edit']\" class=\"mr-2\" />\r\n            手动输入\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-if=\"changePasswordModal.method === 'auto'\" class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"changePasswordModal.policyId\" class=\"form-select\" @change=\"generatePassword()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n\r\n        <div class=\"mt-3\">\r\n          <div class=\"flex justify-between mb-1\">\r\n            <label class=\"form-label\">生成的密码</label>\r\n            <button @click=\"generatePassword()\" type=\"button\"\r\n              class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n              重新生成\r\n            </button>\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.generated ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.generatedPassword\" readonly class=\"form-control flex-1 bg-gray-50\" />\r\n            <button @click=\"passwordVisibility.generated = !passwordVisibility.generated\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.generated ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else class=\"space-y-4\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.new ? 'text' : 'password'\" v-model=\"changePasswordModal.newPassword\"\r\n              class=\"form-control flex-1\" placeholder=\"输入新密码\" />\r\n            <button @click=\"passwordVisibility.new = !passwordVisibility.new\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.new ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.confirm ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.confirmPassword\" class=\"form-control flex-1\"\r\n              :class=\"{ 'border-red-500': passwordMismatch }\" placeholder=\"再次输入新密码\" />\r\n            <button @click=\"passwordVisibility.confirm = !passwordVisibility.confirm\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.confirm ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n          <div v-if=\"passwordMismatch\" class=\"text-sm text-red-500 mt-1\">两次输入的密码不一致</div>\r\n        </div>\r\n\r\n        <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n      </div>\r\n\r\n      <div class=\"space-y-2 mt-4\">\r\n        <div class=\"form-label font-medium\">执行选项</div>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          <span class=\"ml-2\">立即执行</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          <span class=\"ml-2\">保存历史记录</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          <span class=\"ml-2\">记录审计日志</span>\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 添加账号弹窗 -->\r\n    <BaseModal v-model=\"addAccountModal.show\" title=\"添加账号\" @confirm=\"addAccount\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">账号名称</label>\r\n        <input type=\"text\" v-model=\"addAccountModal.username\" class=\"form-control\" placeholder=\"输入账号名称\" />\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">设为默认账号</label>\r\n        <div class=\"relative inline-block w-10 mr-2 align-middle select-none\">\r\n          <input type=\"checkbox\" v-model=\"addAccountModal.isDefault\"\r\n            class=\"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\" />\r\n          <label class=\"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"></label>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"addAccountModal.policyId\" class=\"form-select\" @change=\"generatePasswordForNewAccount()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <div class=\"flex justify-between mb-1\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <button @click=\"generatePasswordForNewAccount()\" type=\"button\"\r\n            class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n            重新生成\r\n          </button>\r\n        </div>\r\n        <div class=\"flex items-center\">\r\n          <input :type=\"passwordVisibility.newAccount ? 'text' : 'password'\" v-model=\"addAccountModal.password\" readonly\r\n            class=\"form-control flex-1 bg-gray-50\" />\r\n          <button @click=\"passwordVisibility.newAccount = !passwordVisibility.newAccount\" type=\"button\"\r\n            class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', passwordVisibility.newAccount ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量添加账号弹窗 -->\r\n    <BaseModal v-model=\"batchAddAccountModal.show\" title=\"批量添加账号\" size=\"lg\" @confirm=\"batchAddAccounts\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatchAdd\" @update:modelValue=\"toggleSelectAllBatchAdd\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchAddAccountModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedBatchAddHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">账号信息</label>\r\n        <div class=\"p-4 border border-gray-200 rounded-md\">\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">账号名称 <span class=\"text-red-500\">*</span></label>\r\n            <input type=\"text\" v-model=\"batchAddAccountModal.username\" class=\"form-control\" placeholder=\"输入统一账号名称\" />\r\n            <div class=\"text-xs text-gray-500 mt-1\">将在所有选中主机上创建同名账号</div>\r\n          </div>\r\n\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">账号角色</label>\r\n            <select v-model=\"batchAddAccountModal.role\" class=\"form-select\">\r\n              <option value=\"admin\">管理员</option>\r\n              <option value=\"user\">普通用户</option>\r\n              <option value=\"service\">服务账号</option>\r\n              <option value=\"readonly\">只读账号</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div class=\"flex items-center mb-3\">\r\n            <label class=\"inline-flex items-center\">\r\n              <input type=\"checkbox\" v-model=\"batchAddAccountModal.setAsDefault\" class=\"form-checkbox\">\r\n              <span class=\"ml-2\">设为默认账号</span>\r\n            </label>\r\n          </div>\r\n\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">密码生成方式</label>\r\n            <div class=\"flex space-x-3\">\r\n              <button @click=\"batchAddAccountModal.useSamePassword = true\"\r\n                class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n                :class=\"batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n                <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n                同一密码\r\n              </button>\r\n              <button @click=\"batchAddAccountModal.useSamePassword = false\"\r\n                class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n                :class=\"!batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n                <font-awesome-icon :icon=\"['fas', 'random']\" class=\"mr-2\" />\r\n                随机密码\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <div v-if=\"batchAddAccountModal.useSamePassword\" class=\"mb-3\">\r\n            <label class=\"form-label\">统一密码</label>\r\n            <div class=\"flex items-center\">\r\n              <input :type=\"passwordVisibility.batchPassword ? 'text' : 'password'\"\r\n                v-model=\"batchAddAccountModal.password\" readonly class=\"form-control flex-1 bg-gray-50\" />\r\n              <button @click=\"passwordVisibility.batchPassword = !passwordVisibility.batchPassword\" type=\"button\"\r\n                class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                <font-awesome-icon :icon=\"['fas', passwordVisibility.batchPassword ? 'eye-slash' : 'eye']\"\r\n                  class=\"text-lg\" />\r\n              </button>\r\n              <button @click=\"generatePasswordForBatchAccount()\" type=\"button\"\r\n                class=\"ml-2 px-3 py-1.5 border border-gray-300 text-xs rounded-md\">\r\n                <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n                重新生成\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchAddAccountModal.policyId\" class=\"form-select\" @change=\"generatePasswordForBatchAccount()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <div class=\"space-y-2\">\r\n          <CustomCheckbox v-model=\"batchAddAccountModal.ignoreErrors\">\r\n            <span class=\"ml-2\">忽略错误继续执行</span>\r\n          </CustomCheckbox>\r\n          <CustomCheckbox v-model=\"batchAddAccountModal.generateReport\">\r\n            <span class=\"ml-2\">生成账号创建报告</span>\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      selectAllBatchAdd: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      currentAccount: {},\r\n      passwordVisibility: {\r\n        generated: false,\r\n        new: false,\r\n        confirm: false,\r\n        newAccount: false,\r\n        batchPassword: false\r\n      },\r\n      viewMode: 'table',\r\n      filterText: '',\r\n      accountFilterText: '',\r\n      statusFilter: 'all',\r\n      expiryFilter: 'all',\r\n      policyFilter: 'all',\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      },\r\n\r\n      // 添加账号弹窗\r\n      addAccountModal: {\r\n        show: false,\r\n        username: '',\r\n        password: '',\r\n        isDefault: false,\r\n        policyId: 1\r\n      },\r\n\r\n      // 批量添加账号弹窗\r\n      batchAddAccountModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        username: '',\r\n        password: '',\r\n        role: 'admin',\r\n        setAsDefault: false,\r\n        useSamePassword: true,\r\n        policyId: 1\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    // 获取策略名称\r\n    getPolicyName() {\r\n      return (policyId) => {\r\n        if (!policyId) return '无';\r\n        const policy = this.policies.find(p => p.id === policyId);\r\n        return policy ? policy.name : '无';\r\n      };\r\n    },\r\n\r\n    // 获取策略颜色类\r\n    getPolicyColorClass() {\r\n      return (policyId) => {\r\n        if (!policyId) return 'bg-gray-100 text-gray-800';\r\n        \r\n        // 根据策略ID返回对应的颜色类\r\n        const colorMap = {\r\n          1: 'bg-red-100 text-red-800',    // 高强度策略\r\n          2: 'bg-blue-100 text-blue-800',  // 标准策略\r\n          3: 'bg-purple-100 text-purple-800' // 紧急策略\r\n        };\r\n        \r\n        return colorMap[policyId] || 'bg-gray-100 text-gray-800';\r\n      };\r\n    },\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    },\r\n\r\n    // 过滤后的主机列表\r\n    filteredHosts() {\r\n      return this.hosts.filter(host => {\r\n        // 文本过滤\r\n        const textMatch = this.filterText === '' ||\r\n          host.name.toLowerCase().includes(this.filterText.toLowerCase()) ||\r\n          host.ip.includes(this.filterText);\r\n\r\n        // 状态过滤\r\n        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;\r\n\r\n        return textMatch && statusMatch;\r\n      });\r\n    },\r\n\r\n    // 获取所有账号（扁平化处理）\r\n    getAllAccounts() {\r\n      // 为每个账号添加主机引用\r\n      const accounts = [];\r\n      this.filteredHosts.forEach(host => {\r\n        host.accounts.forEach(account => {\r\n          accounts.push({\r\n            ...account,\r\n            host: host\r\n          });\r\n        });\r\n      });\r\n      return accounts;\r\n    },\r\n\r\n    // 筛选后的账号\r\n    filteredAccounts() {\r\n      return this.getAllAccounts.filter(account => {\r\n        // 账号名称筛选\r\n        const accountMatch = this.accountFilterText === '' ||\r\n          account.username.toLowerCase().includes(this.accountFilterText.toLowerCase());\r\n\r\n        // 密码过期筛选\r\n        let expiryMatch = true;\r\n        if (this.expiryFilter !== 'all') {\r\n          const expiryStatus = this.isPasswordExpired(account).status;\r\n          if (this.expiryFilter === 'expired') {\r\n            expiryMatch = expiryStatus === 'expired';\r\n          } else if (this.expiryFilter === 'expiring-soon') {\r\n            expiryMatch = expiryStatus === 'danger' || expiryStatus === 'warning';\r\n          } else if (this.expiryFilter === 'valid') {\r\n            expiryMatch = expiryStatus === 'normal';\r\n          }\r\n        }\r\n\r\n        // 策略筛选\r\n        let policyMatch = true;\r\n        if (this.policyFilter !== 'all') {\r\n          if (this.policyFilter === 'none') {\r\n            policyMatch = !account.policyId;\r\n          } else {\r\n            policyMatch = account.policyId === parseInt(this.policyFilter);\r\n          }\r\n        }\r\n\r\n        return accountMatch && expiryMatch && policyMatch;\r\n      });\r\n    },\r\n\r\n    // 分组后的账号列表\r\n    groupedAccounts() {\r\n      // 按主机ID分组\r\n      const groups = {};\r\n      this.filteredAccounts.forEach(account => {\r\n        const hostId = account.host.id;\r\n        if (!groups[hostId]) {\r\n          groups[hostId] = {\r\n            hostId: hostId,\r\n            hostName: account.host.name,\r\n            hostIp: account.host.ip,\r\n            host: account.host,\r\n            accounts: []\r\n          };\r\n        }\r\n        groups[hostId].accounts.push(account);\r\n      });\r\n\r\n      // 转换为数组\r\n      return Object.values(groups);\r\n    },\r\n\r\n    selectedBatchAddHostsCount() {\r\n      return Object.values(this.batchAddAccountModal.selectedHosts).filter(Boolean).length\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    toggleSelectAllBatchAdd(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchAddAccountModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host, account) {\r\n      this.currentHost = host\r\n      this.currentAccount = account\r\n      this.changePasswordModal.show = true\r\n      this.changePasswordModal.generatedPassword = this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword(policy) {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policyObj ? policyObj.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      if (this.changePasswordModal && !policy) {\r\n        this.changePasswordModal.generatedPassword = password\r\n      }\r\n\r\n      return password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto'\r\n          ? this.changePasswordModal.generatedPassword\r\n          : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          accountId: this.currentAccount.id,\r\n          password: password,\r\n          policyId: this.changePasswordModal.policyId\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的 ${this.currentAccount.username} 账号密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取所选策略\r\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取紧急策略\r\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    togglePasswordVisibility(hostId) {\r\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]\r\n    },\r\n\r\n    isPasswordExpired(account) {\r\n      if (!account.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }\r\n\r\n      // 解析过期时间\r\n      const expiryDate = new Date(account.passwordExpiryDate)\r\n      const now = new Date()\r\n\r\n      // 如果已过期\r\n      if (expiryDate < now) {\r\n        return {\r\n          status: 'expired',\r\n          days: 0,\r\n          text: '已过期'\r\n        }\r\n      }\r\n\r\n      // 计算剩余天数和小时数\r\n      const diffTime = expiryDate - now\r\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))\r\n      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n\r\n      // 根据剩余时间确定状态\r\n      let status = 'normal'\r\n      if (diffDays < 7) {\r\n        status = 'danger'  // 少于7天\r\n      } else if (diffDays < 14) {\r\n        status = 'warning' // 少于14天\r\n      }\r\n\r\n      // 格式化显示文本\r\n      let text = ''\r\n      if (diffDays > 0) {\r\n        text += `${diffDays}天`\r\n      }\r\n      if (diffHours > 0 || diffDays === 0) {\r\n        text += `${diffHours}小时`\r\n      }\r\n\r\n      return { status, days: diffDays, text: `剩余${text}` }\r\n    },\r\n\r\n    openAddAccountModal(host) {\r\n      this.currentHost = host\r\n      this.addAccountModal.show = true\r\n    },\r\n\r\n    async addAccount() {\r\n      if (!this.addAccountModal.username || !this.addAccountModal.password) {\r\n        alert('请填写完整的账号信息！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('addHostAccount', {\r\n          hostId: this.currentHost.id,\r\n          username: this.addAccountModal.username,\r\n          password: this.addAccountModal.password,\r\n          policyId: this.addAccountModal.policyId,\r\n          isDefault: this.addAccountModal.isDefault\r\n        })\r\n\r\n        this.addAccountModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为主机 ${this.currentHost.name} 添加账号！`)\r\n      } catch (error) {\r\n        console.error('添加账号失败', error)\r\n        alert('添加账号失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    generatePasswordForNewAccount() {\r\n      this.addAccountModal.password = this.generatePassword()\r\n    },\r\n\r\n    // 复制密码到剪贴板\r\n    copyPassword(account) {\r\n      // 创建一个临时输入框\r\n      const tempInput = document.createElement('input');\r\n      tempInput.value = account.password;\r\n      document.body.appendChild(tempInput);\r\n      tempInput.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(tempInput);\r\n\r\n      // 显示提示\r\n      alert(`已复制 ${account.host.name} 的 ${account.username} 账号密码到剪贴板！`);\r\n    },\r\n\r\n    generatePasswordForBatchAccount() {\r\n      this.batchAddAccountModal.password = this.generatePassword()\r\n    },\r\n\r\n    async batchAddAccounts() {\r\n      if (!this.batchAddAccountModal.username) {\r\n        alert('请填写账号名称！')\r\n        return\r\n      }\r\n\r\n      const selectedHostIds = Object.entries(this.batchAddAccountModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('batchAddAccounts', {\r\n          hostIds: selectedHostIds,\r\n          username: this.batchAddAccountModal.username,\r\n          password: this.batchAddAccountModal.useSamePassword ? this.batchAddAccountModal.password : null,\r\n          role: this.batchAddAccountModal.role,\r\n          isDefault: this.batchAddAccountModal.setAsDefault,\r\n          policyId: this.batchAddAccountModal.policyId\r\n        })\r\n\r\n        this.batchAddAccountModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机添加账号！`)\r\n      } catch (error) {\r\n        console.error('批量添加账号失败', error)\r\n        alert('批量添加账号失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    openBatchAddAccountModal() {\r\n      this.batchAddAccountModal.show = true\r\n      this.batchAddAccountModal.selectedHosts = {}\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchAddAccountModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 生成初始密码\r\n      this.generatePasswordForBatchAccount()\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": ";;EAGSA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAA6C;;EACjDA,KAAK,EAAC;AAA6B;;EA2BnCA,KAAK,EAAC;AAA6B;;EAEjCA,KAAK,EAAC;AAAqD;;EAgB3DA,KAAK,EAAC;AAAU;;EAGdA,KAAK,EAAC;AAAsE;;EAM9EA,KAAK,EAAC;AAAU;;EAGdA,KAAK,EAAC;AAAsE;oBA9D7F;;EAAAC,GAAA;EAoGqCD,KAAK,EAAC;;;EAEhCA,KAAK,EAAC;AAAiE;;EACrEA,KAAK,EAAC;AAAuB;;EACvBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EAEzBA,KAAK,EAAC;AAAgB;;EAEvBA,KAAK,EAAC;AAAsN;;EAK5NA,KAAK,EAAC;AAAsN;;EAO3NA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAAY;;EAEjBE,KAAK,EAAC,KAAK;EAACF,KAAK,EAAC;;;EA+BnBA,KAAK,EAAC;AAAmC;;EAMtCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EAEpBA,KAAK,EAAC;AAAgC;;EAI9CA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAmC;;EA7KjEC,GAAA;EA+KoBD,KAAK,EAAC;;;EAKRA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAvLrDC,GAAA;EAiMoBD,KAAK,EAAC;;;EAjM1BC,GAAA;EAoMoFD,KAAK,EAAC;;;EAKxEA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAW;oBA3MxC;oBAAA;;EAuNkBA,KAAK,EAAC;AAA6B;;EAGnCA,KAAK,EAAC;AAA6B;;EAKnCA,KAAK,EAAC;AAAmD;;EACtDA,KAAK,EAAC;AAAgB;oBAhO3C;oBAAA;;EAAAC,GAAA;AAAA;;EAmPgBE,OAAO,EAAC,GAAG;EAACH,KAAK,EAAC;;;EACfA,KAAK,EAAC;AAAe;;EAWxBA,KAAK,EAAC;AAAsD;;EAE/DA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAuC;;EAC3CA,KAAK,EAAC;AAAmB;;EAGtBA,KAAK,EAAC;AAAmC;;EAC1CA,KAAK,EAAC;AAAuB;;EAOjCA,KAAK,EAAC;AAAW;;EAGbA,KAAK,EAAC;AAAwC;;EAC5CA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAmC;;EApRjEC,GAAA;EAsRoBD,KAAK,EAAC;;oBAtR1B;;EAmSmBA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAmB;oBArS9C;oBAAA;;EAiTmBA,KAAK,EAAC;AAAgC;;EAGlCA,KAAK,EAAC;AAAe;;EApT5CC,GAAA;EAiUsBD,KAAK,EAAC;;;EAKPA,KAAK,EAAC;AAAiB;;EAW7BA,KAAK,EAAC;AAA0B;oBAjV/C;;EA+VWA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAiC;;EAOzCA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAgB;;EA1WnCC,GAAA;EA0XwDD,KAAK,EAAC;;oBA1X9D;;EAkYaA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAA2B;;EAQjCA,KAAK,EAAC;AAAmB;oBA3YxC;;EAAAC,GAAA;EAsZkBD,KAAK,EAAC;;;EACXA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;oBAzZxC;;EAmaaA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;oBAraxC;;EAAAC,GAAA;EA8auCD,KAAK,EAAC;;;EAMlCA,KAAK,EAAC;AAAgB;;EAgBtBA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAiC;;EAMzCA,KAAK,EAAC;AAAiB;;EAKvBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAA0D;;EAOlEA,KAAK,EAAC;AAAiB;oBA1dlC;;EAmeWA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAA2B;;EAQjCA,KAAK,EAAC;AAAmB;qBA5etC;;EA0fWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAY;qBAzgB7B;;EAkhBWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EAzhB1CC,GAAA;EA+hBmED,KAAK,EAAC;;;EAC1DA,KAAK,EAAC;AAAwB;;EAalCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;qBAxkB7B;;EAilBWA,KAAK,EAAC;AAAY;;EAkBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;qBA7mB7B;;EAsnBWA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;EAUlBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAuC;;EAC3CA,KAAK,EAAC;AAAM;;EAMZA,KAAK,EAAC;AAAM;;EAUZA,KAAK,EAAC;AAAwB;;EAC1BA,KAAK,EAAC;AAA0B;;EAMpCA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAgB;;EAtrBvCC,GAAA;EAssB2DD,KAAK,EAAC;;;EAEhDA,KAAK,EAAC;AAAmB;qBAxsB1C;;EA0tBWA,KAAK,EAAC;AAAiB;qBA1tBlC;;EAmuBWA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAW;;;;;;;uBApuB5BI,mBAAA,CA8uBM,cA7uBJC,mBAAA,UAAa,EACbC,mBAAA,CA6FM,OA7FNC,UA6FM,GA5FJD,mBAAA,CA2FM,OA3FNE,UA2FM,GA1FJF,mBAAA,CAyBM,OAzBNG,UAyBM,GAxBJH,mBAAA,CAKS;IAJPN,KAAK,EAAC,qNAAqN;IAC1NU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,kBAAA,IAAAD,QAAA,CAAAC,kBAAA,IAAAF,IAAA,CAAkB;MAC1BG,YAAA,CAA0EC,4BAAA;IAAtDC,IAAI,EAAE,+BAA+B;IAAEjB,KAAK,EAAC;kCACjEM,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAKS;IAJPN,KAAK,EAAC,wNAAwN;IAC7NU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAK,oBAAA,IAAAL,QAAA,CAAAK,oBAAA,IAAAN,IAAA,CAAoB;MAC5BG,YAAA,CAAyDC,4BAAA;IAArCC,IAAI,EAAE,cAAc;IAAEjB,KAAK,EAAC;kCAChDM,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAKS;IAJPN,KAAK,EAAC,8NAA8N;IACnOU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAM,mBAAA,IAAAN,QAAA,CAAAM,mBAAA,IAAAP,IAAA,CAAmB;MAC3BG,YAAA,CAAgEC,4BAAA;IAA5CC,IAAI,EAAE,qBAAqB;IAAEjB,KAAK,EAAC;kCACvDM,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAKS;IAJPN,KAAK,EAAC,2NAA2N;IAChOU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAO,wBAAA,IAAAP,QAAA,CAAAO,wBAAA,IAAAR,IAAA,CAAwB;MAChCG,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEjB,KAAK,EAAC;kCAClDM,mBAAA,CAAmB,cAAb,QAAM,qB,KAIhBA,mBAAA,CA8DM,OA9DNe,UA8DM,GA7DJhB,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNgB,UAaM,GAZJhB,mBAAA,CAKS;IALDN,KAAK,EAnCzBuB,eAAA,EAmC0B,8BAA8B;MAAA,0BACNC,KAAA,CAAAC,QAAQ;MAAA,6BAA2CD,KAAA,CAAAC,QAAQ;IAAA;IAC9Ff,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAe,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAChBV,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEjB,KAAK,EAAC;kCAtChE2B,gBAAA,CAsCyE,MAE7D,G,kBACArB,mBAAA,CAKS;IALDN,KAAK,EAzCzBuB,eAAA,EAyC0B,8BAA8B;MAAA,0BACNC,KAAA,CAAAC,QAAQ;MAAA,6BAA0CD,KAAA,CAAAC,QAAQ;IAAA;IAC7Ff,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAe,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAChBV,YAAA,CAA8DC,4BAAA;IAA1CC,IAAI,EAAE,mBAAmB;IAAEjB,KAAK,EAAC;kCA5CnE2B,gBAAA,CA4C4E,MAEhE,G,oBAGFtB,mBAAA,QAAW,EACXC,mBAAA,CAMM,OANNsB,UAMM,G,gBALJtB,mBAAA,CAC2L;IADpLuB,IAAI,EAAC,MAAM;IAnD9B,uBAAAlB,MAAA,QAAAA,MAAA,MAAAe,MAAA,IAmDwCF,KAAA,CAAAM,UAAU,GAAAJ,MAAA;IAAEK,WAAW,EAAC,SAAS;IAC3D/B,KAAK,EAAC;iDADoBwB,KAAA,CAAAM,UAAU,E,GAEtCxB,mBAAA,CAEM,OAFN0B,UAEM,GADJjB,YAAA,CAAqEC,4BAAA;IAAjDC,IAAI,EAAE,iBAAiB;IAAEjB,KAAK,EAAC;UAIvDK,mBAAA,UAAa,EACbC,mBAAA,CAMM,OANN2B,UAMM,G,gBALJ3B,mBAAA,CAC2L;IADpLuB,IAAI,EAAC,MAAM;IA5D9B,uBAAAlB,MAAA,QAAAA,MAAA,MAAAe,MAAA,IA4DwCF,KAAA,CAAAU,iBAAiB,GAAAR,MAAA;IAAEK,WAAW,EAAC,SAAS;IAClE/B,KAAK,EAAC;iDADoBwB,KAAA,CAAAU,iBAAiB,E,GAE7C5B,mBAAA,CAEM,OAFN6B,UAEM,GADJpB,YAAA,CAAmEC,4BAAA;IAA/CC,IAAI,EAAE,eAAe;IAAEjB,KAAK,EAAC;UAIrDK,mBAAA,UAAa,E,gBACbC,mBAAA,CAMS;IA1EnB,uBAAAK,MAAA,QAAAA,MAAA,MAAAe,MAAA,IAoE2BF,KAAA,CAAAY,YAAY,GAAAV,MAAA;IAC3B1B,KAAK,EAAC;kCACNM,mBAAA,CAAiC;IAAzB+B,KAAK,EAAC;EAAK,GAAC,MAAI,qBACxB/B,mBAAA,CAAkC;IAA1B+B,KAAK,EAAC;EAAQ,GAAC,IAAE,qBACzB/B,mBAAA,CAAmC;IAA3B+B,KAAK,EAAC;EAAS,GAAC,IAAE,qBAC1B/B,mBAAA,CAAiC;IAAzB+B,KAAK,EAAC;EAAO,GAAC,IAAE,oB,2CALTb,KAAA,CAAAY,YAAY,E,GAQ7B/B,mBAAA,cAAiB,E,gBACjBC,mBAAA,CAMS;IAnFnB,uBAAAK,MAAA,QAAAA,MAAA,MAAAe,MAAA,IA6E2BF,KAAA,CAAAc,YAAY,GAAAZ,MAAA;IAC3B1B,KAAK,EAAC;kCACNM,mBAAA,CAAiC;IAAzB+B,KAAK,EAAC;EAAK,GAAC,MAAI,qBACxB/B,mBAAA,CAAoC;IAA5B+B,KAAK,EAAC;EAAS,GAAC,KAAG,qBAC3B/B,mBAAA,CAA2C;IAAnC+B,KAAK,EAAC;EAAe,GAAC,MAAI,qBAClC/B,mBAAA,CAAmC;IAA3B+B,KAAK,EAAC;EAAO,GAAC,MAAI,oB,2CALXb,KAAA,CAAAc,YAAY,E,GAQ7BjC,mBAAA,UAAa,E,gBACbC,mBAAA,CAOS;IA7FnB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAsF2BF,KAAA,CAAAe,YAAY,GAAAb,MAAA;IAC3B1B,KAAK,EAAC;kCACNM,mBAAA,CAAiC;IAAzB+B,KAAK,EAAC;EAAK,GAAC,MAAI,uB,kBACxBjC,mBAAA,CAESoC,SAAA,QA3FrBC,WAAA,CAyFqCC,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;yBAArBxC,mBAAA,CAES;MAF2BH,GAAG,EAAE2C,MAAM,CAACC,EAAE;MAAGR,KAAK,EAAEO,MAAM,CAACC;wBAC9DD,MAAM,CAACE,IAAI,wBA1F5BC,WAAA;8DA4FYzC,mBAAA,CAAiC;IAAzB+B,KAAK,EAAC;EAAM,GAAC,KAAG,qB,0CANTb,KAAA,CAAAe,YAAY,E,SAYnClC,mBAAA,UAAa,EACbA,mBAAA,UAAa,EACFmB,KAAA,CAAAC,QAAQ,gB,cAAnBrB,mBAAA,CAwJM,OAxJN4C,WAwJM,GAvJJ3C,mBAAA,eAAkB,EAClBC,mBAAA,CAiBM,OAjBN2C,WAiBM,GAhBJ3C,mBAAA,CAGM,OAHN4C,WAGM,G,4BA1GdvB,gBAAA,CAuG2C,MAC9B,IAAArB,mBAAA,CAA8D,QAA9D6C,WAA8D,EAAAC,gBAAA,CAAjCvC,QAAA,CAAAwC,gBAAgB,CAACC,MAAM,kB,4BAxGjE3B,gBAAA,CAwG2E,UAC9D,IAAArB,mBAAA,CAA4D,QAA5DiD,WAA4D,EAAAH,gBAAA,CAA/BvC,QAAA,CAAA2C,cAAc,CAACF,MAAM,kB,4BAzG/D3B,gBAAA,CAyGyE,MACjE,G,GACArB,mBAAA,CAWM,OAXNmD,WAWM,GAVJnD,mBAAA,CAIS,UAJToD,WAIS,GAFP3C,YAAA,CAAiEC,4BAAA;IAA7CC,IAAI,EAAE,sBAAsB;IAAEjB,KAAK,EAAC;kCA9GpE2B,gBAAA,CA8G6E,MAEnE,G,GACArB,mBAAA,CAIS,UAJTqD,WAIS,GAFP5C,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEjB,KAAK,EAAC;kCAnH9D2B,gBAAA,CAmHuE,MAE7D,G,OAIJrB,mBAAA,CAkIQ,SAlIRsD,WAkIQ,GAjINtD,mBAAA,CAgCQ,SAhCRuD,WAgCQ,GA/BNvD,mBAAA,CA8BK,aA7BHA,mBAAA,CAIK,MAJLwD,WAIK,GAHH/C,YAAA,CAEiBgD,yBAAA;IA/H/BC,UAAA,EA6HuCxC,KAAA,CAAAyC,SAAS;IA7HhD,wB,sCA6HuCzC,KAAA,CAAAyC,SAAS,GAAAvC,MAAA,GAAsBb,QAAA,CAAAqD,eAAe;;IA7HrFC,OAAA,EAAAC,QAAA,CA6HuF,MAEzEzD,MAAA,SAAAA,MAAA,QA/HdgB,gBAAA,CA6HuF,OAEzE,E;IA/Hd0C,CAAA;0FAiIY/D,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,QAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,YAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,UAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,qB,KAGJM,mBAAA,CA+FQ,SA/FRgE,WA+FQ,GA9FNjE,mBAAA,aAAgB,G,kBAChBD,mBAAA,CAmFWoC,SAAA,QAhPrBC,WAAA,CA6JwC5B,QAAA,CAAA0D,eAAe,EAA5BC,SAAS;yBA7JpCpE,mBAAA,CAAAoC,SAAA;MAAAvC,GAAA,EA6J+DuE,SAAS,CAACC;QAC7DpE,mBAAA,SAAY,G,kBACZD,mBAAA,CAgFKoC,SAAA,QA/OjBC,WAAA,CA+JkD+B,SAAS,CAACE,QAAQ,EA/JpE,CA+JwBC,OAAO,EAAEC,YAAY;2BAAjCxE,mBAAA,CAgFK;QAhFsDH,GAAG,EAAE0E,OAAO,CAAC9B,EAAE;QACvE7C,KAAK,EAhKpBuB,eAAA;UAAA,cAgKsCqD,YAAY;UAAA;QAAA;UACpCtE,mBAAA,CAMK,MANLuE,WAMK,GALHvE,mBAAA,CAIM,OAJNwE,WAIM,GAHJ/D,YAAA,CAEiBgD,yBAAA;QArKnCC,UAAA,EAmK2CW,OAAO,CAACI,IAAI,CAACC,QAAQ;QAnKhE,uBAAAtD,MAAA,IAmK2CiD,OAAO,CAACI,IAAI,CAACC,QAAQ,GAAAtD,MAAA;QAAE1B,KAAK,EAAC;;QAnKxEmE,OAAA,EAAAC,QAAA,CAoKoB,MAA2E,CAA3E9D,mBAAA,CAA2E,QAA3E2E,WAA2E,EAAA7B,gBAAA,CAA3BuB,OAAO,CAACI,IAAI,CAACjC,IAAI,iB;QApKrFuB,CAAA;sFAwKc/D,mBAAA,CAEK,MAFL4E,WAEK,GADH5E,mBAAA,CAA8D,OAA9D6E,WAA8D,EAAA/B,gBAAA,CAAxBuB,OAAO,CAACI,IAAI,CAACK,EAAE,iB,GAEvD9E,mBAAA,CAQK,MARL+E,WAQK,GAPH/E,mBAAA,CAMM,OANNgF,WAMM,GALJhF,mBAAA,CAA6E,QAA7EiF,WAA6E,EAAAnC,gBAAA,CAA1BuB,OAAO,CAACa,QAAQ,kBACvDb,OAAO,CAACc,SAAS,I,cAA7BrF,mBAAA,CAGO,QAHPsF,WAGO,EAFqG,MAE5G,KAjLlBrF,mBAAA,e,KAoLcC,mBAAA,CAEK,MAFLqF,WAEK,GADHrF,mBAAA,CAAgF,OAAhFsF,WAAgF,EAAAxC,gBAAA,CAA1CuB,OAAO,CAACkB,kBAAkB,wB,GAElEvF,mBAAA,CAiBK,MAjBLwF,WAiBK,GAhBHxF,mBAAA,CAeM;QAfAN,KAAK,EAxL3BuB,eAAA;;qCAwLiLV,QAAA,CAAAkF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM,iBAAiBnF,QAAA,CAAAkF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM;2CAAoEnF,QAAA,CAAAkF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM;uCAAgEnF,QAAA,CAAAkF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM;;UAxL1crE,gBAAA,CAAAyB,gBAAA,CA8LqBvC,QAAA,CAAAkF,iBAAiB,CAACpB,OAAO,EAAEsB,IAAI,IAAG,GACrC,iBACQpF,QAAA,CAAAkF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM,kBAAkBnF,QAAA,CAAAkF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM,iB,cAD5F5F,mBAAA,CAIO,QAJP8F,WAIO,GADLnF,YAAA,CAA6DC,4BAAA;QAAzCC,IAAI,EAAE;MAA+B,G,KAE1CJ,QAAA,CAAAkF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM,kB,cAAlD5F,mBAAA,CAEO,QAFP+F,WAEO,GADLpF,YAAA,CAA2DC,4BAAA;QAAvCC,IAAI,EAAE;MAA6B,G,KArM3EZ,mBAAA,e,oBAyMcC,mBAAA,CAaK,MAbL8F,WAaK,GAZH9F,mBAAA,CAWM,OAXN+F,WAWM,GAVJ/F,mBAAA,CAIM,OAJNgG,WAIM,GAHJhG,mBAAA,CAEkG;QAF1FuB,IAAI,EAAEL,KAAA,CAAA+E,kBAAkB,CAAC5B,OAAO,CAAC9B,EAAE;QAA0BR,KAAK,EAAEsC,OAAO,CAAC6B,QAAQ;QAC1FC,QAAQ,EAAR,EAAQ;QACRzG,KAAK,EAAC;8BA9M5B0G,WAAA,E,GAgNkBpG,mBAAA,CAIS;QAJAI,OAAK,EAAAgB,MAAA,IAAEb,QAAA,CAAA8F,wBAAwB,CAAChC,OAAO,CAAC9B,EAAE;QACjD7C,KAAK,EAAC;UACNe,YAAA,CACoBC,4BAAA;QADAC,IAAI,UAAUO,KAAA,CAAA+E,kBAAkB,CAAC5B,OAAO,CAAC9B,EAAE;QAC7D7C,KAAK,EAAC;yDAnN5B4G,WAAA,E,KAuNctG,mBAAA,CAEK,MAFLuG,WAEK,GADH9F,YAAA,CAA2C+F,sBAAA;QAA7BjF,IAAI,EAAE8C,OAAO,CAACI,IAAI,CAACiB;2CAEnC1F,mBAAA,CAIK,MAJLyG,WAIK,GAHHzG,mBAAA,CAEM;QAFAN,KAAK,EA3N3BuB,eAAA,sEA2NkGV,QAAA,CAAAmG,mBAAmB,CAACrC,OAAO,CAACsC,QAAQ;0BACjHpG,QAAA,CAAAqG,aAAa,CAACvC,OAAO,CAACsC,QAAQ,yB,GAGrC3G,mBAAA,CAeK,MAfL6G,WAeK,GAdH7G,mBAAA,CAaM,OAbN8G,WAaM,GAZJ9G,mBAAA,CAKS;QAJPN,KAAK,EAAC,0NAA0N;QAC/NU,OAAK,EAAAgB,MAAA,IAAEb,QAAA,CAAAwG,uBAAuB,CAAC1C,OAAO,CAACI,IAAI,EAAEJ,OAAO;UACrD5D,YAAA,CAAyDC,4BAAA;QAArCC,IAAI,EAAE,cAAc;QAAEjB,KAAK,EAAC;sCApOpE2B,gBAAA,CAoO6E,QAE3D,G,iBAtOlB2F,WAAA,GAuOkBhH,mBAAA,CAKS;QAJPN,KAAK,EAAC,sNAAsN;QAC3NU,OAAK,EAAAgB,MAAA,IAAEb,QAAA,CAAA0G,YAAY,CAAC5C,OAAO;UAC5B5D,YAAA,CAA0DC,4BAAA;QAAtCC,IAAI,EAAE,eAAe;QAAEjB,KAAK,EAAC;sCA1OrE2B,gBAAA,CA0O8E,MAE5D,G,iBA5OlB6F,WAAA,E;;kCAiPUnH,mBAAA,WAAc,EACJQ,QAAA,CAAAwC,gBAAgB,CAACC,MAAM,U,cAAjClD,mBAAA,CAOK,MAzPfqH,WAAA,GAmPYnH,mBAAA,CAKK,MALLoH,WAKK,GAJHpH,mBAAA,CAGM,OAHNqH,WAGM,GAFJ5G,YAAA,CAAqEC,4BAAA;IAAjDC,IAAI,EAAE,iBAAiB;IAAEjB,KAAK,EAAC;kCACnDM,mBAAA,CAAkB,WAAf,aAAW,qB,SAtP9BD,mBAAA,e,wBA+PID,mBAAA,CA4FMoC,SAAA;IA3VVvC,GAAA;EAAA,IA8PII,mBAAA,UAAa,EACbC,mBAAA,CA4FM,OA5FNsH,WA4FM,I,kBA3FJxH,mBAAA,CA0FMoC,SAAA,QA1VZC,WAAA,CAgQ0B5B,QAAA,CAAAgH,aAAa,EAArB9C,IAAI;yBAAhB3E,mBAAA,CA0FM;MA1F8BH,GAAG,EAAE8E,IAAI,CAAClC,EAAE;MAAE7C,KAAK,EAAC;QACtDM,mBAAA,CAwFM,OAxFNwH,WAwFM,GAvFJzH,mBAAA,UAAa,EACbC,mBAAA,CASM,OATNyH,WASM,GARJzH,mBAAA,CAMM,OANN0H,WAMM,GALJjH,YAAA,CAAuDgD,yBAAA;MArQrEC,UAAA,EAqQuCe,IAAI,CAACC,QAAQ;MArQpD,uBAAAtD,MAAA,IAqQuCqD,IAAI,CAACC,QAAQ,GAAAtD,MAAA;MAAE1B,KAAK,EAAC;oEAC9CM,mBAAA,CAGM,cAFJA,mBAAA,CAAkE,MAAlE2H,WAAkE,EAAA7E,gBAAA,CAAjB2B,IAAI,CAACjC,IAAI,kBAC1DxC,mBAAA,CAAkD,KAAlD4H,WAAkD,EAAA9E,gBAAA,CAAd2B,IAAI,CAACK,EAAE,iB,KAG/CrE,YAAA,CAAmC+F,sBAAA;MAArBjF,IAAI,EAAEkD,IAAI,CAACiB;yCAG3B3F,mBAAA,UAAa,EACbC,mBAAA,CA+DM,OA/DN6H,WA+DM,I,kBA9DJ/H,mBAAA,CA6DMoC,SAAA,QA7UlBC,WAAA,CAgRmCsC,IAAI,CAACL,QAAQ,EAAxBC,OAAO;2BAAnBvE,mBAAA,CA6DM;QA7DiCH,GAAG,EAAE0E,OAAO,CAAC9B,EAAE;QAAE7C,KAAK,EAhRzEuB,eAAA,EAgR0E,uCAAuC;UAAA,gCACzDoD,OAAO,CAACc;QAAS;UAC3DnF,mBAAA,CAcM,OAdN8H,WAcM,GAbJ9H,mBAAA,CAMM,OANN+H,WAMM,GALJ/H,mBAAA,CAA6E,QAA7EgI,WAA6E,EAAAlF,gBAAA,CAA1BuB,OAAO,CAACa,QAAQ,kBACvDb,OAAO,CAACc,SAAS,I,cAA7BrF,mBAAA,CAGO,QAHPmI,WAGO,EAFqG,MAE5G,KAxRlBlI,mBAAA,e,GA0RgBC,mBAAA,CAKS;QAJPN,KAAK,EAAC,wNAAwN;QAC7NU,OAAK,EAAAgB,MAAA,IAAEb,QAAA,CAAAwG,uBAAuB,CAACtC,IAAI,EAAEJ,OAAO;UAC7C5D,YAAA,CAAyDC,4BAAA;QAArCC,IAAI,EAAE,cAAc;QAAEjB,KAAK,EAAC;sCA7RlE2B,gBAAA,CA6R2E,QAE3D,G,iBA/RhB6G,WAAA,E,GAkScnI,mBAAA,UAAa,EACbC,mBAAA,CAWM,OAXNmI,WAWM,G,4BAVJnI,mBAAA,CAA4D;QAAvDN,KAAK,EAAC;MAAwC,GAAC,IAAE,sBACtDM,mBAAA,CAQM,OARNoI,WAQM,GAPJpI,mBAAA,CACkG;QAD1FuB,IAAI,EAAEL,KAAA,CAAA+E,kBAAkB,CAAC5B,OAAO,CAAC9B,EAAE;QAA0BR,KAAK,EAAEsC,OAAO,CAAC6B,QAAQ;QAAEC,QAAQ,EAAR,EAAQ;QACpGzG,KAAK,EAAC;8BAvS1B2I,WAAA,GAwSkBrI,mBAAA,CAIS;QAJAI,OAAK,EAAAgB,MAAA,IAAEb,QAAA,CAAA8F,wBAAwB,CAAChC,OAAO,CAAC9B,EAAE;QACjD7C,KAAK,EAAC;UACNe,YAAA,CACoBC,4BAAA;QADAC,IAAI,UAAUO,KAAA,CAAA+E,kBAAkB,CAAC5B,OAAO,CAAC9B,EAAE;QAC7D7C,KAAK,EAAC;yDA3S5B4I,WAAA,E,KAgTcvI,mBAAA,YAAe,EACfC,mBAAA,CA2BM,OA3BNuI,WA2BM,GA1BJvI,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAwD;QAAnDN,KAAK,EAAC;MAAgC,GAAC,QAAM,sBAClDM,mBAAA,CAAwE,OAAxEwI,WAAwE,EAAA1F,gBAAA,CAA1CuB,OAAO,CAACkB,kBAAkB,wB,GAE1DvF,mBAAA,CAeM,c,4BAdJA,mBAAA,CAAsD;QAAjDN,KAAK,EAAC;MAAgC,GAAC,MAAI,sBAChDM,mBAAA,CAYM;QAZAN,KAAK,EAxT7BuB,eAAA;;qCAwTqLV,QAAA,CAAAkF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM,iBAAiBnF,QAAA,CAAAkF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM;2CAAsEnF,QAAA,CAAAkF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM;uCAAkEnF,QAAA,CAAAkF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM;;UAxTldrE,gBAAA,CAAAyB,gBAAA,CA8TuBvC,QAAA,CAAAkF,iBAAiB,CAACpB,OAAO,EAAEsB,IAAI,IAAG,GACrC,iBACQpF,QAAA,CAAAkF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM,kBAAkBnF,QAAA,CAAAkF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM,iB,cAD5F5F,mBAAA,CAIO,QAJP2I,WAIO,GADLhI,YAAA,CAA6DC,4BAAA;QAAzCC,IAAI,EAAE;MAA+B,G,KAlU/EZ,mBAAA,e,oBAsUgBC,mBAAA,CAKM,OALN0I,WAKM,G,4BAJJ1I,mBAAA,CAAsD;QAAjDN,KAAK,EAAC;MAAgC,GAAC,MAAI,sBAChDM,mBAAA,CAEM;QAFAN,KAAK,EAxU7BuB,eAAA,sEAwUoGV,QAAA,CAAAmG,mBAAmB,CAACrC,OAAO,CAACsC,QAAQ;0BACjHpG,QAAA,CAAAqG,aAAa,CAACvC,OAAO,CAACsC,QAAQ,yB;sCAO3C5G,mBAAA,YAAe,EACfC,mBAAA,CAOM,OAPN2I,WAOM,GANJ3I,mBAAA,CAKS;MAJPN,KAAK,EAAC,sNAAsN;MAC3NU,OAAK,EAAAgB,MAAA,IAAEb,QAAA,CAAAqI,mBAAmB,CAACnE,IAAI;QAChChE,YAAA,CAA0DC,4BAAA;MAAtCC,IAAI,EAAE,eAAe;MAAEjB,KAAK,EAAC;oCArV/D2B,gBAAA,CAqVwE,QAE5D,G,iBAvVZwH,WAAA,E;sFA6VI9I,mBAAA,YAAe,EACfU,YAAA,CAkGYqI,oBAAA;IAhchBpF,UAAA,EA8VwBxC,KAAA,CAAA6H,mBAAmB,CAACC,IAAI;IA9VhD,uBAAA3I,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA8VwBF,KAAA,CAAA6H,mBAAmB,CAACC,IAAI,GAAA5H,MAAA;IAAE6H,KAAK,EAAC,MAAM;IAAEC,SAAO,EAAE3I,QAAA,CAAA4I,cAAc;IAAGC,OAAO,EAAElI,KAAA,CAAAmI;;IA9VnGxF,OAAA,EAAAC,QAAA,CA+VM,MAOM,CAPN9D,mBAAA,CAOM,OAPNsJ,WAOM,G,8BANJtJ,mBAAA,CAAwC;MAAnCN,KAAK,EAAC;IAAkB,GAAC,MAAI,sBAClCM,mBAAA,CAIM,OAJNuJ,WAIM,GAHJvJ,mBAAA,CAAuE,c,8BAAlEA,mBAAA,CAAqC;MAA/BN,KAAK,EAAC;IAAa,GAAC,MAAI,sBAlW7C2B,gBAAA,CAkWoD,GAAC,GAAAyB,gBAAA,CAAG5B,KAAA,CAAAsI,WAAW,CAAChH,IAAI,iB,GAC9DxC,mBAAA,CAAsE,c,8BAAjEA,mBAAA,CAAsC;MAAhCN,KAAK,EAAC;IAAa,GAAC,OAAK,sBAnW9C2B,gBAAA,CAmWqD,GAAC,GAAAyB,gBAAA,CAAG5B,KAAA,CAAAsI,WAAW,CAAC1E,EAAE,iB,GAC7D9E,mBAAA,CAA6E,c,8BAAxEA,mBAAA,CAAoC;MAA9BN,KAAK,EAAC;IAAa,GAAC,KAAG,sBApW5C2B,gBAAA,CAoWmD,GAAC,GAAAyB,gBAAA,CAAG5B,KAAA,CAAAuI,cAAc,CAACvE,QAAQ,iB,OAIxElF,mBAAA,CAgBM,OAhBN0J,WAgBM,G,8BAfJ1J,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAaM,OAbN2J,WAaM,GAZJ3J,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAA6H,mBAAmB,CAACa,MAAM;MACxClK,KAAK,EA5WjBuB,eAAA,EA4WkB,iFAAiF,EAC/EC,KAAA,CAAA6H,mBAAmB,CAACa,MAAM;QAClCnJ,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;sCA9WjE2B,gBAAA,CA8W0E,QAEhE,G,kBACArB,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAA6H,mBAAmB,CAACa,MAAM;MACxClK,KAAK,EAlXjBuB,eAAA,EAkXkB,iFAAiF,EAC/EC,KAAA,CAAA6H,mBAAmB,CAACa,MAAM;QAClCnJ,YAAA,CAA0DC,4BAAA;MAAtCC,IAAI,EAAE,eAAe;MAAEjB,KAAK,EAAC;sCApX7D2B,gBAAA,CAoXsE,QAE5D,G,sBAIOH,KAAA,CAAA6H,mBAAmB,CAACa,MAAM,e,cAArC9J,mBAAA,CA0BM,OA1BN+J,WA0BM,G,8BAzBJ7J,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MAhYjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA4XyBF,KAAA,CAAA6H,mBAAmB,CAACpC,QAAQ,GAAAvF,MAAA;MAAE1B,KAAK,EAAC,aAAa;MAAEoK,QAAM,EAAAzJ,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEb,QAAA,CAAAwJ,gBAAgB;2BAC1FjK,mBAAA,CAESoC,SAAA,QA/XnBC,WAAA,CA6XmCC,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArBxC,mBAAA,CAES;QAF2BH,GAAG,EAAE2C,MAAM,CAACC,EAAE;QAAGR,KAAK,EAAEO,MAAM,CAACC;0BAC9DD,MAAM,CAACE,IAAI,IAAG,UAAQ,GAAAM,gBAAA,CAAGR,MAAM,CAAC0H,SAAS,IAAG,QAAM,GAAAlH,gBAAA,CAAGR,MAAM,CAAC2H,UAAU,IAAG,KAC9E,uBA/XVC,WAAA;6FA4XyBhJ,KAAA,CAAA6H,mBAAmB,CAACpC,QAAQ,E,GAM7C3G,mBAAA,CAiBM,OAjBNmK,WAiBM,GAhBJnK,mBAAA,CAOM,OAPNoK,WAOM,G,8BANJpK,mBAAA,CAAuC;MAAhCN,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BM,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEb,QAAA,CAAAwJ,gBAAgB;MAAIxI,IAAI,EAAC,QAAQ;MAC/C7B,KAAK,EAAC;QACNe,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;sCAvYnE2B,gBAAA,CAuY4E,QAEhE,G,KAEFrB,mBAAA,CAOM,OAPNqK,WAOM,G,gBANJrK,mBAAA,CACoG;MAD5FuB,IAAI,EAAEL,KAAA,CAAA+E,kBAAkB,CAACqE,SAAS;MA5YtD,uBAAAjK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA6YuBF,KAAA,CAAA6H,mBAAmB,CAACwB,iBAAiB,GAAAnJ,MAAA;MAAE+E,QAAQ,EAAR,EAAQ;MAACzG,KAAK,EAAC;4BA7Y7E8K,WAAA,I,iBA6YuBtJ,KAAA,CAAA6H,mBAAmB,CAACwB,iBAAiB,E,GAChDvK,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAA+E,kBAAkB,CAACqE,SAAS,IAAIpJ,KAAA,CAAA+E,kBAAkB,CAACqE,SAAS;MAAE/I,IAAI,EAAC,QAAQ;MACzF7B,KAAK,EAAC;QACNe,YAAA,CAAyGC,4BAAA;MAArFC,IAAI,UAAUO,KAAA,CAAA+E,kBAAkB,CAACqE,SAAS;MAAyB5K,KAAK,EAAC;gEAMrGI,mBAAA,CA4BM,OA5BN2K,WA4BM,GA3BJzK,mBAAA,CAUM,OAVN0K,WAUM,G,8BATJ1K,mBAAA,CAAqC;MAA9BN,KAAK,EAAC;IAAY,GAAC,KAAG,sBAC7BM,mBAAA,CAOM,OAPN2K,WAOM,G,gBANJ3K,mBAAA,CACoD;MAD5CuB,IAAI,EAAEL,KAAA,CAAA+E,kBAAkB,CAAC2E,GAAG;MA1ZhD,uBAAAvK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA0ZiFF,KAAA,CAAA6H,mBAAmB,CAAC8B,WAAW,GAAAzJ,MAAA;MAClG1B,KAAK,EAAC,qBAAqB;MAAC+B,WAAW,EAAC;4BA3ZtDqJ,WAAA,I,iBA0ZiF5J,KAAA,CAAA6H,mBAAmB,CAAC8B,WAAW,E,GAEpG7K,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAA+E,kBAAkB,CAAC2E,GAAG,IAAI1J,KAAA,CAAA+E,kBAAkB,CAAC2E,GAAG;MAAErJ,IAAI,EAAC,QAAQ;MAC7E7B,KAAK,EAAC;QACNe,YAAA,CAAmGC,4BAAA;MAA/EC,IAAI,UAAUO,KAAA,CAAA+E,kBAAkB,CAAC2E,GAAG;MAAyBlL,KAAK,EAAC;6CAK7FM,mBAAA,CAYM,OAZN+K,WAYM,G,8BAXJ/K,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CAQM,OARNgL,WAQM,G,gBAPJhL,mBAAA,CAE0E;MAFlEuB,IAAI,EAAEL,KAAA,CAAA+E,kBAAkB,CAACgF,OAAO;MAtapD,uBAAA5K,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAuauBF,KAAA,CAAA6H,mBAAmB,CAACmC,eAAe,GAAA9J,MAAA;MAAE1B,KAAK,EAvajEuB,eAAA,EAuakE,qBAAqB;QAAA,kBAC7CV,QAAA,CAAA4K;MAAgB;MAAI1J,WAAW,EAAC;oCAxa1E2J,WAAA,I,iBAuauBlK,KAAA,CAAA6H,mBAAmB,CAACmC,eAAe,E,GAE9ClL,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAA+E,kBAAkB,CAACgF,OAAO,IAAI/J,KAAA,CAAA+E,kBAAkB,CAACgF,OAAO;MAAE1J,IAAI,EAAC,QAAQ;MACrF7B,KAAK,EAAC;QACNe,YAAA,CAAuGC,4BAAA;MAAnFC,IAAI,UAAUO,KAAA,CAAA+E,kBAAkB,CAACgF,OAAO;MAAyBvL,KAAK,EAAC;2CAGpFa,QAAA,CAAA4K,gBAAgB,I,cAA3BrL,mBAAA,CAA+E,OAA/EuL,WAA+E,EAAhB,YAAU,KA9anFtL,mBAAA,e,GAibQU,YAAA,CAAqE6K,gCAAA;MAA7CpF,QAAQ,EAAEhF,KAAA,CAAA6H,mBAAmB,CAAC8B;8CAGxD7K,mBAAA,CAWM,OAXNuL,WAWM,G,8BAVJvL,mBAAA,CAA8C;MAAzCN,KAAK,EAAC;IAAwB,GAAC,MAAI,sBACxCe,YAAA,CAEiBgD,yBAAA;MAxbzBC,UAAA,EAsbiCxC,KAAA,CAAA6H,mBAAmB,CAACyC,kBAAkB;MAtbvE,uBAAAnL,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAsbiCF,KAAA,CAAA6H,mBAAmB,CAACyC,kBAAkB,GAAApK,MAAA;;MAtbvEyC,OAAA,EAAAC,QAAA,CAubU,MAA8BzD,MAAA,UAAAA,MAAA,SAA9BL,mBAAA,CAA8B;QAAxBN,KAAK,EAAC;MAAM,GAAC,MAAI,oB;MAvbjCqE,CAAA;uCAybQtD,YAAA,CAEiBgD,yBAAA;MA3bzBC,UAAA,EAybiCxC,KAAA,CAAA6H,mBAAmB,CAAC0C,WAAW;MAzbhE,uBAAApL,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAybiCF,KAAA,CAAA6H,mBAAmB,CAAC0C,WAAW,GAAArK,MAAA;;MAzbhEyC,OAAA,EAAAC,QAAA,CA0bU,MAAgCzD,MAAA,UAAAA,MAAA,SAAhCL,mBAAA,CAAgC;QAA1BN,KAAK,EAAC;MAAM,GAAC,QAAM,oB;MA1bnCqE,CAAA;uCA4bQtD,YAAA,CAEiBgD,yBAAA;MA9bzBC,UAAA,EA4biCxC,KAAA,CAAA6H,mBAAmB,CAAC2C,QAAQ;MA5b7D,uBAAArL,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA4biCF,KAAA,CAAA6H,mBAAmB,CAAC2C,QAAQ,GAAAtK,MAAA;;MA5b7DyC,OAAA,EAAAC,QAAA,CA6bU,MAAgCzD,MAAA,UAAAA,MAAA,SAAhCL,mBAAA,CAAgC;QAA1BN,KAAK,EAAC;MAAM,GAAC,QAAM,oB;MA7bnCqE,CAAA;;IAAAA,CAAA;6DAkcIhE,mBAAA,YAAe,EACfU,YAAA,CAkDYqI,oBAAA;IArfhBpF,UAAA,EAmcwBxC,KAAA,CAAAyK,eAAe,CAAC3C,IAAI;IAnc5C,uBAAA3I,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAmcwBF,KAAA,CAAAyK,eAAe,CAAC3C,IAAI,GAAA5H,MAAA;IAAE6H,KAAK,EAAC,MAAM;IAAEC,SAAO,EAAE3I,QAAA,CAAAqL,UAAU;IAAGxC,OAAO,EAAElI,KAAA,CAAAmI;;IAnc3FxF,OAAA,EAAAC,QAAA,CAocM,MAMM,CANN9D,mBAAA,CAMM,OANN6L,WAMM,G,8BALJ7L,mBAAA,CAAwC;MAAnCN,KAAK,EAAC;IAAkB,GAAC,MAAI,sBAClCM,mBAAA,CAGM,OAHN8L,WAGM,GAFJ9L,mBAAA,CAAuE,c,8BAAlEA,mBAAA,CAAqC;MAA/BN,KAAK,EAAC;IAAa,GAAC,MAAI,sBAvc7C2B,gBAAA,CAucoD,GAAC,GAAAyB,gBAAA,CAAG5B,KAAA,CAAAsI,WAAW,CAAChH,IAAI,iB,GAC9DxC,mBAAA,CAAsE,c,8BAAjEA,mBAAA,CAAsC;MAAhCN,KAAK,EAAC;IAAa,GAAC,OAAK,sBAxc9C2B,gBAAA,CAwcqD,GAAC,GAAAyB,gBAAA,CAAG5B,KAAA,CAAAsI,WAAW,CAAC1E,EAAE,iB,OAIjE9E,mBAAA,CAGM,OAHN+L,WAGM,G,8BAFJ/L,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAAkG;MAA3FuB,IAAI,EAAC,MAAM;MA9c1B,uBAAAlB,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA8coCF,KAAA,CAAAyK,eAAe,CAACzG,QAAQ,GAAA9D,MAAA;MAAE1B,KAAK,EAAC,cAAc;MAAC+B,WAAW,EAAC;mDAA3DP,KAAA,CAAAyK,eAAe,CAACzG,QAAQ,E,KAGtDlF,mBAAA,CAOM,OAPNgM,WAOM,G,8BANJhM,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAIM,OAJNiM,WAIM,G,gBAHJjM,mBAAA,CACoI;MAD7HuB,IAAI,EAAC,UAAU;MApdhC,uBAAAlB,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAod0CF,KAAA,CAAAyK,eAAe,CAACxG,SAAS,GAAA/D,MAAA;MACvD1B,KAAK,EAAC;uDADwBwB,KAAA,CAAAyK,eAAe,CAACxG,SAAS,E,iCAEzDnF,mBAAA,CAAsG;MAA/FN,KAAK,EAAC;IAAgF,4B,KAIjGM,mBAAA,CAOM,OAPNkM,WAOM,G,8BANJlM,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MAhejB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA4dyBF,KAAA,CAAAyK,eAAe,CAAChF,QAAQ,GAAAvF,MAAA;MAAE1B,KAAK,EAAC,aAAa;MAAEoK,QAAM,EAAAzJ,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEb,QAAA,CAAA4L,6BAA6B;2BACnGrM,mBAAA,CAESoC,SAAA,QA/dnBC,WAAA,CA6dmCC,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArBxC,mBAAA,CAES;QAF2BH,GAAG,EAAE2C,MAAM,CAACC,EAAE;QAAGR,KAAK,EAAEO,MAAM,CAACC;0BAC9DD,MAAM,CAACE,IAAI,IAAG,UAAQ,GAAAM,gBAAA,CAAGR,MAAM,CAAC0H,SAAS,IAAG,QAAM,GAAAlH,gBAAA,CAAGR,MAAM,CAAC2H,UAAU,IAAG,KAC9E,uBA/dVmC,WAAA;6FA4dyBlL,KAAA,CAAAyK,eAAe,CAAChF,QAAQ,E,KAO3C3G,mBAAA,CAiBM,OAjBNqM,WAiBM,GAhBJrM,mBAAA,CAOM,OAPNsM,YAOM,G,8BANJtM,mBAAA,CAAuC;MAAhCN,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BM,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEb,QAAA,CAAA4L,6BAA6B;MAAI5K,IAAI,EAAC,QAAQ;MAC5D7B,KAAK,EAAC;QACNe,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;sCAxejE2B,gBAAA,CAwe0E,QAEhE,G,KAEFrB,mBAAA,CAOM,OAPNuM,YAOM,G,gBANJvM,mBAAA,CAC2C;MADnCuB,IAAI,EAAEL,KAAA,CAAA+E,kBAAkB,CAACuG,UAAU;MA7erD,uBAAAnM,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA6esFF,KAAA,CAAAyK,eAAe,CAACzF,QAAQ,GAAA9E,MAAA;MAAE+E,QAAQ,EAAR,EAAQ;MAC5GzG,KAAK,EAAC;4BA9elB+M,YAAA,I,iBA6esFvL,KAAA,CAAAyK,eAAe,CAACzF,QAAQ,E,GAEpGlG,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAA+E,kBAAkB,CAACuG,UAAU,IAAItL,KAAA,CAAA+E,kBAAkB,CAACuG,UAAU;MAAEjL,IAAI,EAAC,QAAQ;MAC3F7B,KAAK,EAAC;QACNe,YAAA,CAA0GC,4BAAA;MAAtFC,IAAI,UAAUO,KAAA,CAAA+E,kBAAkB,CAACuG,UAAU;MAAyB9M,KAAK,EAAC;;IAjf1GqE,CAAA;6DAufIhE,mBAAA,cAAiB,EACjBU,YAAA,CAiEYqI,oBAAA;IAzjBhBpF,UAAA,EAwfwBxC,KAAA,CAAAwL,gBAAgB,CAAC1D,IAAI;IAxf7C,uBAAA3I,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAwfwBF,KAAA,CAAAwL,gBAAgB,CAAC1D,IAAI,GAAA5H,MAAA;IAAE6H,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAAC0D,IAAI,EAAC,IAAI;IACpFzD,SAAO,EAAE3I,QAAA,CAAAqM,oBAAoB;IAAGxD,OAAO,EAAElI,KAAA,CAAAmI;;IAzfhDxF,OAAA,EAAAC,QAAA,CA0fM,MAaM,CAbN9D,mBAAA,CAaM,OAbN6M,YAaM,G,8BAZJ7M,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAIM,OAJN8M,YAIM,GAHJrM,YAAA,CAEiBgD,yBAAA;MA/f3BC,UAAA,EA6fmCxC,KAAA,CAAA6L,cAAc;MA7fjD,wB,sCA6fmC7L,KAAA,CAAA6L,cAAc,GAAA3L,MAAA,GAAsBb,QAAA,CAAAyM,oBAAoB;;MA7f3FnJ,OAAA,EAAAC,QAAA,CA6f6F,MAEnFzD,MAAA,UAAAA,MAAA,SA/fVgB,gBAAA,CA6f6F,MAEnF,E;MA/fV0C,CAAA;gEAigBQ/D,mBAAA,CAIM,OAJNiN,YAIM,I,kBAHJnN,mBAAA,CAEiBoC,SAAA,QApgB3BC,WAAA,CAkgByCC,IAAA,CAAA8K,KAAK,EAAbzI,IAAI;2BAA3B0I,YAAA,CAEiB1J,yBAAA;QAFsB9D,GAAG,EAAE8E,IAAI,CAAClC,EAAE;QAlgB7DmB,UAAA,EAkgBwExC,KAAA,CAAAwL,gBAAgB,CAACU,aAAa,CAAC3I,IAAI,CAAClC,EAAE;QAlgB9G,uBAAAnB,MAAA,IAkgBwEF,KAAA,CAAAwL,gBAAgB,CAACU,aAAa,CAAC3I,IAAI,CAAClC,EAAE,IAAAnB;;QAlgB9GyC,OAAA,EAAAC,QAAA,CAmgBY,MAAe,CAngB3BzC,gBAAA,CAAAyB,gBAAA,CAmgBe2B,IAAI,CAACjC,IAAI,IAAG,IAAE,GAAAM,gBAAA,CAAG2B,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QApgBVf,CAAA;;sCAsgBQ/D,mBAAA,CAAyD,KAAzDqN,YAAyD,EAApC,MAAI,GAAAvK,gBAAA,CAAGvC,QAAA,CAAA+M,kBAAkB,IAAG,MAAI,gB,GAGvDtN,mBAAA,CAOM,OAPNuN,YAOM,G,8BANJvN,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MA/gBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA2gByBF,KAAA,CAAAwL,gBAAgB,CAAC/F,QAAQ,GAAAvF,MAAA;MAAE1B,KAAK,EAAC;2BAChDI,mBAAA,CAESoC,SAAA,QA9gBnBC,WAAA,CA4gBmCC,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArBxC,mBAAA,CAES;QAF2BH,GAAG,EAAE2C,MAAM,CAACC,EAAE;QAAGR,KAAK,EAAEO,MAAM,CAACC;0BAC9DD,MAAM,CAACE,IAAI,IAAG,UAAQ,GAAAM,gBAAA,CAAGR,MAAM,CAAC0H,SAAS,IAAG,QAAM,GAAAlH,gBAAA,CAAGR,MAAM,CAAC2H,UAAU,IAAG,KAC9E,uBA9gBVuD,YAAA;6EA2gByBtM,KAAA,CAAAwL,gBAAgB,CAAC/F,QAAQ,E,KAO5C3G,mBAAA,CAyBM,OAzBNyN,YAyBM,G,8BAxBJzN,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CASM,OATN0N,YASM,GARJ1N,mBAAA,CAGQ,SAHR2N,YAGQ,G,gBAFN3N,mBAAA,CAA4F;MAArFuB,IAAI,EAAC,OAAO;MAthB/B,uBAAAlB,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAshByCF,KAAA,CAAAwL,gBAAgB,CAACkB,aAAa,GAAAxM,MAAA;MAAEW,KAAK,EAAC,WAAW;MAACrC,KAAK,EAAC;oDAAxDwB,KAAA,CAAAwL,gBAAgB,CAACkB,aAAa,E,iCAC3D5N,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHR6N,YAGQ,G,gBAFN7N,mBAAA,CAA4F;MAArFuB,IAAI,EAAC,OAAO;MA1hB/B,uBAAAlB,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA0hByCF,KAAA,CAAAwL,gBAAgB,CAACkB,aAAa,GAAAxM,MAAA;MAAEW,KAAK,EAAC,WAAW;MAACrC,KAAK,EAAC;oDAAxDwB,KAAA,CAAAwL,gBAAgB,CAACkB,aAAa,E,iCAC3D5N,mBAAA,CAAiB,cAAX,MAAI,qB,KAIHkB,KAAA,CAAAwL,gBAAgB,CAACkB,aAAa,oB,cAAzC9N,mBAAA,CAWM,OAXNgO,YAWM,GAVJ9N,mBAAA,CASM,OATN+N,YASM,GARJ/N,mBAAA,CAGM,c,8BAFJA,mBAAA,CAAoC;MAA7BN,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BM,mBAAA,CAAiF;MAA1EuB,IAAI,EAAC,MAAM;MAniBhC,uBAAAlB,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAmiB0CF,KAAA,CAAAwL,gBAAgB,CAACsB,aAAa,GAAA5M,MAAA;MAAE1B,KAAK,EAAC;mDAAtCwB,KAAA,CAAAwL,gBAAgB,CAACsB,aAAa,E,KAE5DhO,mBAAA,CAGM,c,8BAFJA,mBAAA,CAAoC;MAA7BN,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BM,mBAAA,CAAiF;MAA1EuB,IAAI,EAAC,MAAM;MAviBhC,uBAAAlB,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAuiB0CF,KAAA,CAAAwL,gBAAgB,CAACuB,aAAa,GAAA7M,MAAA;MAAE1B,KAAK,EAAC;mDAAtCwB,KAAA,CAAAwL,gBAAgB,CAACuB,aAAa,E,WAviBxElO,mBAAA,e,GA6iBMC,mBAAA,CAWM,OAXNkO,YAWM,G,8BAVJlO,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Be,YAAA,CAEiBgD,yBAAA;MAjjBzBC,UAAA,EA+iBiCxC,KAAA,CAAAwL,gBAAgB,CAACyB,YAAY;MA/iB9D,uBAAA9N,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA+iBiCF,KAAA,CAAAwL,gBAAgB,CAACyB,YAAY,GAAA/M,MAAA;;MA/iB9DyC,OAAA,EAAAC,QAAA,CA+iBgE,MAExDzD,MAAA,UAAAA,MAAA,SAjjBRgB,gBAAA,CA+iBgE,YAExD,E;MAjjBR0C,CAAA;uCAkjBQtD,YAAA,CAEiBgD,yBAAA;MApjBzBC,UAAA,EAkjBiCxC,KAAA,CAAAwL,gBAAgB,CAAC0B,WAAW;MAljB7D,uBAAA/N,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAkjBiCF,KAAA,CAAAwL,gBAAgB,CAAC0B,WAAW,GAAAhN,MAAA;;MAljB7DyC,OAAA,EAAAC,QAAA,CAkjB+D,MAEvDzD,MAAA,UAAAA,MAAA,SApjBRgB,gBAAA,CAkjB+D,UAEvD,E;MApjBR0C,CAAA;uCAqjBQtD,YAAA,CAEiBgD,yBAAA;MAvjBzBC,UAAA,EAqjBiCxC,KAAA,CAAAwL,gBAAgB,CAAC2B,gBAAgB;MArjBlE,uBAAAhO,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAqjBiCF,KAAA,CAAAwL,gBAAgB,CAAC2B,gBAAgB,GAAAjN,MAAA;;MArjBlEyC,OAAA,EAAAC,QAAA,CAqjBoE,MAE5DzD,MAAA,UAAAA,MAAA,SAvjBRgB,gBAAA,CAqjBoE,aAE5D,E;MAvjBR0C,CAAA;;IAAAA,CAAA;6DA2jBIhE,mBAAA,cAAiB,EACjBU,YAAA,CA8BYqI,oBAAA;IA1lBhBpF,UAAA,EA4jBwBxC,KAAA,CAAAoN,eAAe,CAACtF,IAAI;IA5jB5C,uBAAA3I,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA4jBwBF,KAAA,CAAAoN,eAAe,CAACtF,IAAI,GAAA5H,MAAA;IAAE6H,KAAK,EAAC,UAAU;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAE3I,QAAA,CAAAgO,gBAAgB;IACtGnF,OAAO,EAAElI,KAAA,CAAAmI;;IA7jBhBxF,OAAA,EAAAC,QAAA,CA8jBM,MAQM,CARN9D,mBAAA,CAQM,OARNwO,YAQM,G,8BAPJxO,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAKM,OALNyO,YAKM,I,kBAJJ3O,mBAAA,CAGiBoC,SAAA,QApkB3BC,WAAA,CAikByC5B,QAAA,CAAAmO,iBAAiB,EAAzBjK,IAAI;2BAA3B0I,YAAA,CAGiB1J,yBAAA;QAHkC9D,GAAG,EAAE8E,IAAI,CAAClC,EAAE;QAjkBzEmB,UAAA,EAkkBqBxC,KAAA,CAAAoN,eAAe,CAAClB,aAAa,CAAC3I,IAAI,CAAClC,EAAE;QAlkB1D,uBAAAnB,MAAA,IAkkBqBF,KAAA,CAAAoN,eAAe,CAAClB,aAAa,CAAC3I,IAAI,CAAClC,EAAE,IAAAnB;;QAlkB1DyC,OAAA,EAAAC,QAAA,CAmkBY,MAAe,CAnkB3BzC,gBAAA,CAAAyB,gBAAA,CAmkBe2B,IAAI,CAACjC,IAAI,IAAG,IAAE,GAAAM,gBAAA,CAAG2B,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QApkBVf,CAAA;;wCAwkBM/D,mBAAA,CAOM,OAPN2O,YAOM,G,8BANJ3O,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCM,mBAAA,CAIS;MA9kBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA0kByBF,KAAA,CAAAoN,eAAe,CAAC3H,QAAQ,GAAAvF,MAAA;MAAE1B,KAAK,EAAC;2BAC/CI,mBAAA,CAESoC,SAAA,QA7kBnBC,WAAA,CA2kBmCC,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArBxC,mBAAA,CAES;QAF2BH,GAAG,EAAE2C,MAAM,CAACC,EAAE;QAAGR,KAAK,EAAEO,MAAM,CAACC;0BAC9DD,MAAM,CAACE,IAAI,wBA5kB1BoM,YAAA;6EA0kByB1N,KAAA,CAAAoN,eAAe,CAAC3H,QAAQ,E,KAO3C3G,mBAAA,CAQM,OARN6O,YAQM,G,8BAPJ7O,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Be,YAAA,CAEiBgD,yBAAA;MArlBzBC,UAAA,EAmlBiCxC,KAAA,CAAAoN,eAAe,CAACQ,iBAAiB;MAnlBlE,uBAAAzO,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAmlBiCF,KAAA,CAAAoN,eAAe,CAACQ,iBAAiB,GAAA1N,MAAA;;MAnlBlEyC,OAAA,EAAAC,QAAA,CAmlBoE,MAE5DzD,MAAA,UAAAA,MAAA,SArlBRgB,gBAAA,CAmlBoE,eAE5D,E;MArlBR0C,CAAA;uCAslBQtD,YAAA,CAEiBgD,yBAAA;MAxlBzBC,UAAA,EAslBiCxC,KAAA,CAAAoN,eAAe,CAACS,iBAAiB;MAtlBlE,uBAAA1O,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAslBiCF,KAAA,CAAAoN,eAAe,CAACS,iBAAiB,GAAA3N,MAAA;;MAtlBlEyC,OAAA,EAAAC,QAAA,CAslBoE,MAE5DzD,MAAA,UAAAA,MAAA,SAxlBRgB,gBAAA,CAslBoE,aAE5D,E;MAxlBR0C,CAAA;;IAAAA,CAAA;6DA4lBIhE,mBAAA,cAAiB,EACjBU,YAAA,CAyCYqI,oBAAA;IAtoBhBpF,UAAA,EA6lBwBxC,KAAA,CAAA8N,mBAAmB,CAAChG,IAAI;IA7lBhD,uBAAA3I,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA6lBwBF,KAAA,CAAA8N,mBAAmB,CAAChG,IAAI,GAAA5H,MAAA;IAAE6H,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAACtI,IAAI,EAAC,sBAAsB;IAACsO,MAAM,EAAN,EAAM;IAChH/F,SAAO,EAAE3I,QAAA,CAAA2O,cAAc;IAAG9F,OAAO,EAAElI,KAAA,CAAAmI;;IA9lB1CxF,OAAA,EAAAC,QAAA,CA+lBM,MAEM,C,8BAFN9D,mBAAA,CAEM;MAFDN,KAAK,EAAC;IAA4C,IACrDM,mBAAA,CAA+C,WAA5C,0CAAwC,E,sBAG7CA,mBAAA,CAQM,OARNmP,YAQM,G,8BAPJnP,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAKM,OALNoP,YAKM,I,kBAJJtP,mBAAA,CAGiBoC,SAAA,QAzmB3BC,WAAA,CAsmByC5B,QAAA,CAAAmO,iBAAiB,EAAzBjK,IAAI;2BAA3B0I,YAAA,CAGiB1J,yBAAA;QAHkC9D,GAAG,EAAE8E,IAAI,CAAClC,EAAE;QAtmBzEmB,UAAA,EAumBqBxC,KAAA,CAAA8N,mBAAmB,CAAC5B,aAAa,CAAC3I,IAAI,CAAClC,EAAE;QAvmB9D,uBAAAnB,MAAA,IAumBqBF,KAAA,CAAA8N,mBAAmB,CAAC5B,aAAa,CAAC3I,IAAI,CAAClC,EAAE,IAAAnB;;QAvmB9DyC,OAAA,EAAAC,QAAA,CAwmBY,MAAe,CAxmB3BzC,gBAAA,CAAAyB,gBAAA,CAwmBe2B,IAAI,CAACjC,IAAI,IAAG,IAAE,GAAAM,gBAAA,CAAG2B,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QAzmBVf,CAAA;;wCA6mBM/D,mBAAA,CAOM,OAPNqP,YAOM,G,8BANJrP,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCM,mBAAA,CAIS;MAnnBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA+mByBF,KAAA,CAAA8N,mBAAmB,CAACrI,QAAQ,GAAAvF,MAAA;MAAE1B,KAAK,EAAC;2BACnDI,mBAAA,CAESoC,SAAA,QAlnBnBC,WAAA,CAgnBmC5B,QAAA,CAAA+O,iBAAiB,EAA3BhN,MAAM;2BAArBxC,mBAAA,CAES;QAFoCH,GAAG,EAAE2C,MAAM,CAACC,EAAE;QAAGR,KAAK,EAAEO,MAAM,CAACC;0BACvED,MAAM,CAACE,IAAI,IAAG,UAAQ,GAAAM,gBAAA,CAAGR,MAAM,CAAC0H,SAAS,IAAG,QAAM,GAAAlH,gBAAA,CAAGR,MAAM,CAAC2H,UAAU,IAAG,KAC9E,uBAlnBVsF,YAAA;6EA+mByBrO,KAAA,CAAA8N,mBAAmB,CAACrI,QAAQ,E,KAO/C3G,mBAAA,CASM,OATNwP,YASM,G,8BARJxP,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAMS;MA9nBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAwnByBF,KAAA,CAAA8N,mBAAmB,CAACS,MAAM,GAAArO,MAAA;MAAE1B,KAAK,EAAC;sCACjDM,mBAAA,CAAiD;MAAzC+B,KAAK,EAAC;IAAmB,GAAC,QAAM,qBACxC/B,mBAAA,CAA2C;MAAnC+B,KAAK,EAAC;IAAe,GAAC,MAAI,qBAClC/B,mBAAA,CAA6C;MAArC+B,KAAK,EAAC;IAAiB,GAAC,MAAI,qBACpC/B,mBAAA,CAAwC;MAAhC+B,KAAK,EAAC;IAAY,GAAC,MAAI,qBAC/B/B,mBAAA,CAAmC;MAA3B+B,KAAK,EAAC;IAAO,GAAC,MAAI,oB,2CALXb,KAAA,CAAA8N,mBAAmB,CAACS,MAAM,E,KAS7CzP,mBAAA,CAIM,OAJN0P,YAIM,G,8BAHJ1P,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CACuC;MApoB/C,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAmoB2BF,KAAA,CAAA8N,mBAAmB,CAACW,WAAW,GAAAvO,MAAA;MAAE1B,KAAK,EAAC,cAAc;MAACkQ,IAAI,EAAC,GAAG;MAC/EnO,WAAW,EAAC;mDADKP,KAAA,CAAA8N,mBAAmB,CAACW,WAAW,E;IAnoB1D5L,CAAA;6DAwoBIhE,mBAAA,cAAiB,EACjBU,YAAA,CAqGYqI,oBAAA;IA9uBhBpF,UAAA,EAyoBwBxC,KAAA,CAAA2O,oBAAoB,CAAC7G,IAAI;IAzoBjD,uBAAA3I,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAyoBwBF,KAAA,CAAA2O,oBAAoB,CAAC7G,IAAI,GAAA5H,MAAA;IAAE6H,KAAK,EAAC,QAAQ;IAAC0D,IAAI,EAAC,IAAI;IAAEzD,SAAO,EAAE3I,QAAA,CAAAuP,gBAAgB;IAC/F1G,OAAO,EAAElI,KAAA,CAAAmI;;IA1oBhBxF,OAAA,EAAAC,QAAA,CA2oBM,MAaM,CAbN9D,mBAAA,CAaM,OAbN+P,YAaM,G,8BAZJ/P,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAIM,OAJNgQ,YAIM,GAHJvP,YAAA,CAEiBgD,yBAAA;MAhpB3BC,UAAA,EA8oBmCxC,KAAA,CAAA+O,iBAAiB;MA9oBpD,wB,sCA8oBmC/O,KAAA,CAAA+O,iBAAiB,GAAA7O,MAAA,GAAsBb,QAAA,CAAA2P,uBAAuB;;MA9oBjGrM,OAAA,EAAAC,QAAA,CA8oBmG,MAEzFzD,MAAA,UAAAA,MAAA,SAhpBVgB,gBAAA,CA8oBmG,MAEzF,E;MAhpBV0C,CAAA;gEAkpBQ/D,mBAAA,CAIM,OAJNmQ,YAIM,I,kBAHJrQ,mBAAA,CAEiBoC,SAAA,QArpB3BC,WAAA,CAmpByCC,IAAA,CAAA8K,KAAK,EAAbzI,IAAI;2BAA3B0I,YAAA,CAEiB1J,yBAAA;QAFsB9D,GAAG,EAAE8E,IAAI,CAAClC,EAAE;QAnpB7DmB,UAAA,EAmpBwExC,KAAA,CAAA2O,oBAAoB,CAACzC,aAAa,CAAC3I,IAAI,CAAClC,EAAE;QAnpBlH,uBAAAnB,MAAA,IAmpBwEF,KAAA,CAAA2O,oBAAoB,CAACzC,aAAa,CAAC3I,IAAI,CAAClC,EAAE,IAAAnB;;QAnpBlHyC,OAAA,EAAAC,QAAA,CAopBY,MAAe,CAppB3BzC,gBAAA,CAAAyB,gBAAA,CAopBe2B,IAAI,CAACjC,IAAI,IAAG,IAAE,GAAAM,gBAAA,CAAG2B,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QArpBVf,CAAA;;sCAupBQ/D,mBAAA,CAAiE,KAAjEoQ,YAAiE,EAA5C,MAAI,GAAAtN,gBAAA,CAAGvC,QAAA,CAAA8P,0BAA0B,IAAG,MAAI,gB,GAG/DrQ,mBAAA,CA8DM,OA9DNsQ,YA8DM,G,8BA7DJtQ,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CA2DM,OA3DNuQ,YA2DM,GA1DJvQ,mBAAA,CAIM,OAJNwQ,YAIM,G,8BAHJxQ,mBAAA,CAA0E;MAAnEN,KAAK,EAAC;IAAY,IA9pBrC2B,gBAAA,CA8pBsC,OAAK,GAAArB,mBAAA,CAAmC;MAA7BN,KAAK,EAAC;IAAc,GAAC,GAAC,E,sCAC3DM,mBAAA,CAAyG;MAAlGuB,IAAI,EAAC,MAAM;MA/pB9B,uBAAAlB,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA+pBwCF,KAAA,CAAA2O,oBAAoB,CAAC3K,QAAQ,GAAA9D,MAAA;MAAE1B,KAAK,EAAC,cAAc;MAAC+B,WAAW,EAAC;mDAAhEP,KAAA,CAAA2O,oBAAoB,CAAC3K,QAAQ,E,iCACzDlF,mBAAA,CAA6D;MAAxDN,KAAK,EAAC;IAA4B,GAAC,iBAAe,qB,GAGzDM,mBAAA,CAQM,OARNyQ,YAQM,G,8BAPJzQ,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAKS;MA1qBrB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAqqB6BF,KAAA,CAAA2O,oBAAoB,CAACa,IAAI,GAAAtP,MAAA;MAAE1B,KAAK,EAAC;sCAChDM,mBAAA,CAAkC;MAA1B+B,KAAK,EAAC;IAAO,GAAC,KAAG,qBACzB/B,mBAAA,CAAkC;MAA1B+B,KAAK,EAAC;IAAM,GAAC,MAAI,qBACzB/B,mBAAA,CAAqC;MAA7B+B,KAAK,EAAC;IAAS,GAAC,MAAI,qBAC5B/B,mBAAA,CAAsC;MAA9B+B,KAAK,EAAC;IAAU,GAAC,MAAI,oB,2CAJdb,KAAA,CAAA2O,oBAAoB,CAACa,IAAI,E,KAQ5C1Q,mBAAA,CAKM,OALN2Q,YAKM,GAJJ3Q,mBAAA,CAGQ,SAHR4Q,YAGQ,G,gBAFN5Q,mBAAA,CAAyF;MAAlFuB,IAAI,EAAC,UAAU;MA/qBpC,uBAAAlB,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA+qB8CF,KAAA,CAAA2O,oBAAoB,CAACgB,YAAY,GAAAzP,MAAA;MAAE1B,KAAK,EAAC;uDAAzCwB,KAAA,CAAA2O,oBAAoB,CAACgB,YAAY,E,iCACjE7Q,mBAAA,CAAgC;MAA1BN,KAAK,EAAC;IAAM,GAAC,QAAM,qB,KAI7BM,mBAAA,CAgBM,OAhBN8Q,YAgBM,G,8BAfJ9Q,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAaM,OAbN+Q,YAaM,GAZJ/Q,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAA2O,oBAAoB,CAACmB,eAAe;MAClDtR,KAAK,EAxrBrBuB,eAAA,EAwrBsB,iFAAiF,EAC/EC,KAAA,CAAA2O,oBAAoB,CAACmB,eAAe;QAC5CvQ,YAAA,CAAyDC,4BAAA;MAArCC,IAAI,EAAE,cAAc;MAAEjB,KAAK,EAAC;sCA1rBhE2B,gBAAA,CA0rByE,QAE3D,G,kBACArB,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAA2O,oBAAoB,CAACmB,eAAe;MAClDtR,KAAK,EA9rBrBuB,eAAA,EA8rBsB,iFAAiF,GAC9EC,KAAA,CAAA2O,oBAAoB,CAACmB,eAAe;QAC7CvQ,YAAA,CAA4DC,4BAAA;MAAxCC,IAAI,EAAE,iBAAiB;MAAEjB,KAAK,EAAC;sCAhsBnE2B,gBAAA,CAgsB4E,QAE9D,G,sBAIOH,KAAA,CAAA2O,oBAAoB,CAACmB,eAAe,I,cAA/ClR,mBAAA,CAgBM,OAhBNmR,YAgBM,G,8BAfJjR,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CAaM,OAbNkR,YAaM,G,gBAZJlR,mBAAA,CAC4F;MADpFuB,IAAI,EAAEL,KAAA,CAAA+E,kBAAkB,CAACkL,aAAa;MAzsB5D,uBAAA9Q,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA0sByBF,KAAA,CAAA2O,oBAAoB,CAAC3J,QAAQ,GAAA9E,MAAA;MAAE+E,QAAQ,EAAR,EAAQ;MAACzG,KAAK,EAAC;4BA1sBvE0R,YAAA,I,iBA0sByBlQ,KAAA,CAAA2O,oBAAoB,CAAC3J,QAAQ,E,GACxClG,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAA+E,kBAAkB,CAACkL,aAAa,IAAIjQ,KAAA,CAAA+E,kBAAkB,CAACkL,aAAa;MAAE5P,IAAI,EAAC,QAAQ;MACjG7B,KAAK,EAAC;QACNe,YAAA,CACoBC,4BAAA;MADAC,IAAI,UAAUO,KAAA,CAAA+E,kBAAkB,CAACkL,aAAa;MAChEzR,KAAK,EAAC;yCAEVM,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEb,QAAA,CAAA8Q,+BAA+B;MAAI9P,IAAI,EAAC,QAAQ;MAC9D7B,KAAK,EAAC;QACNe,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;sCAltBrE2B,gBAAA,CAktB8E,QAEhE,G,SAptBdtB,mBAAA,e,KA0tBMC,mBAAA,CAOM,OAPNsR,YAOM,G,8BANJtR,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MAhuBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA4tByBF,KAAA,CAAA2O,oBAAoB,CAAClJ,QAAQ,GAAAvF,MAAA;MAAE1B,KAAK,EAAC,aAAa;MAAEoK,QAAM,EAAAzJ,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEb,QAAA,CAAA8Q,+BAA+B;2BAC1GvR,mBAAA,CAESoC,SAAA,QA/tBnBC,WAAA,CA6tBmCC,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArBxC,mBAAA,CAES;QAF2BH,GAAG,EAAE2C,MAAM,CAACC,EAAE;QAAGR,KAAK,EAAEO,MAAM,CAACC;0BAC9DD,MAAM,CAACE,IAAI,IAAG,UAAQ,GAAAM,gBAAA,CAAGR,MAAM,CAAC0H,SAAS,IAAG,QAAM,GAAAlH,gBAAA,CAAGR,MAAM,CAAC2H,UAAU,IAAG,KAC9E,uBA/tBVsH,YAAA;6FA4tByBrQ,KAAA,CAAA2O,oBAAoB,CAAClJ,QAAQ,E,KAOhD3G,mBAAA,CAUM,OAVNwR,YAUM,G,8BATJxR,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CAOM,OAPNyR,YAOM,GANJhR,YAAA,CAEiBgD,yBAAA;MAxuB3BC,UAAA,EAsuBmCxC,KAAA,CAAA2O,oBAAoB,CAAC1B,YAAY;MAtuBpE,uBAAA9N,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAsuBmCF,KAAA,CAAA2O,oBAAoB,CAAC1B,YAAY,GAAA/M,MAAA;;MAtuBpEyC,OAAA,EAAAC,QAAA,CAuuBY,MAAkCzD,MAAA,UAAAA,MAAA,SAAlCL,mBAAA,CAAkC;QAA5BN,KAAK,EAAC;MAAM,GAAC,UAAQ,oB;MAvuBvCqE,CAAA;uCAyuBUtD,YAAA,CAEiBgD,yBAAA;MA3uB3BC,UAAA,EAyuBmCxC,KAAA,CAAA2O,oBAAoB,CAAC6B,cAAc;MAzuBtE,uBAAArR,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAyuBmCF,KAAA,CAAA2O,oBAAoB,CAAC6B,cAAc,GAAAtQ,MAAA;;MAzuBtEyC,OAAA,EAAAC,QAAA,CA0uBY,MAAkCzD,MAAA,UAAAA,MAAA,SAAlCL,mBAAA,CAAkC;QAA5BN,KAAK,EAAC;MAAM,GAAC,UAAQ,oB;MA1uBvCqE,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}