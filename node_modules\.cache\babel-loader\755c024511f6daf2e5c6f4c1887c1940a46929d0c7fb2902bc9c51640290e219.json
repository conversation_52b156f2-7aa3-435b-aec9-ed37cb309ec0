{"ast": null, "code": "export default {\n  name: 'BaseModal',\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    title: {\n      type: String,\n      required: true\n    },\n    confirmText: {\n      type: String,\n      default: '确认'\n    },\n    icon: {\n      type: String,\n      default: ''\n    },\n    size: {\n      type: String,\n      default: 'md',\n      validator: value => ['sm', 'md', 'lg'].includes(value)\n    },\n    danger: {\n      type: Boolean,\n      default: false\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['update:modelValue', 'confirm'],\n  methods: {\n    close() {\n      this.$emit('update:modelValue', false);\n    },\n    confirm() {\n      this.$emit('confirm');\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "modelValue", "type", "Boolean", "default", "title", "String", "required", "confirmText", "icon", "size", "validator", "value", "includes", "danger", "loading", "emits", "methods", "close", "$emit", "confirm"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\BaseModal.vue"], "sourcesContent": ["<template>\r\n  <teleport to=\"body\">\r\n    <transition name=\"modal\">\r\n      <div v-if=\"modelValue\" class=\"modal-backdrop\" @click.self=\"close\">\r\n        <div :class=\"['modal-container', { 'modal-lg': size === 'lg' }]\">\r\n          <div class=\"modal-header\">\r\n            <h3 class=\"text-lg font-semibold\" :class=\"{ 'text-red-600': danger }\">\r\n              <font-awesome-icon v-if=\"icon\" :icon=\"['fas', icon]\" class=\"mr-2\" />\r\n              {{ title }}\r\n            </h3>\r\n            <button @click=\"close\" class=\"text-gray-400 hover:text-gray-600\">\r\n              <font-awesome-icon :icon=\"['fas', 'times']\" />\r\n            </button>\r\n          </div>\r\n          \r\n          <div class=\"modal-body\">\r\n            <slot></slot>\r\n          </div>\r\n          \r\n          <div class=\"modal-footer\">\r\n            <slot name=\"footer\">\r\n              <button @click=\"close\" class=\"btn-outline\">\r\n                取消\r\n              </button>\r\n              <button \r\n                @click=\"confirm\"\r\n                :class=\"[\r\n                  'btn-primary', \r\n                  { 'bg-red-600 hover:bg-red-700': danger }\r\n                ]\"\r\n                :disabled=\"loading\"\r\n              >\r\n                <span v-if=\"loading\" class=\"inline-block animate-spin mr-2\">\r\n                  <font-awesome-icon :icon=\"['fas', 'sync-alt']\" />\r\n                </span>\r\n                {{ confirmText }}\r\n              </button>\r\n            </slot>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </transition>\r\n  </teleport>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'BaseModal',\r\n  props: {\r\n    modelValue: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    title: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    confirmText: {\r\n      type: String,\r\n      default: '确认'\r\n    },\r\n    icon: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    size: {\r\n      type: String,\r\n      default: 'md',\r\n      validator: (value) => ['sm', 'md', 'lg'].includes(value)\r\n    },\r\n    danger: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  emits: ['update:modelValue', 'confirm'],\r\n  methods: {\r\n    close() {\r\n      this.$emit('update:modelValue', false)\r\n    },\r\n    confirm() {\r\n      this.$emit('confirm')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.modal-lg {\r\n  max-width: 600px;\r\n}\r\n\r\n.modal-enter-active,\r\n.modal-leave-active {\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.modal-enter-from,\r\n.modal-leave-to {\r\n  opacity: 0;\r\n}\r\n\r\n.modal-enter-active .modal-container,\r\n.modal-leave-active .modal-container {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.modal-enter-from .modal-container,\r\n.modal-leave-to .modal-container {\r\n  transform: translateY(-20px);\r\n}\r\n</style> "], "mappings": "AA8CA,eAAe;EACbA,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE;IACLC,UAAU,EAAE;MACVC,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACLH,IAAI,EAAEI,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,WAAW,EAAE;MACXN,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX,CAAC;IACDK,IAAI,EAAE;MACJP,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX,CAAC;IACDM,IAAI,EAAE;MACJR,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE,IAAI;MACbO,SAAS,EAAGC,KAAK,IAAK,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAACC,QAAQ,CAACD,KAAK;IACzD,CAAC;IACDE,MAAM,EAAE;MACNZ,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDW,OAAO,EAAE;MACPb,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX;EACF,CAAC;EACDY,KAAK,EAAE,CAAC,mBAAmB,EAAE,SAAS,CAAC;EACvCC,OAAO,EAAE;IACPC,KAAKA,CAAA,EAAG;MACN,IAAI,CAACC,KAAK,CAAC,mBAAmB,EAAE,KAAK;IACvC,CAAC;IACDC,OAAOA,CAAA,EAAG;MACR,IAAI,CAACD,KAAK,CAAC,SAAS;IACtB;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}