{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, vModelText as _vModelText, withDirectives as _withDirectives, vModelSelect as _vModelSelect, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, withCtx as _withCtx, resolveDirective as _resolveDirective, vModelDynamic as _vModelDynamic, vModelCheckbox as _vModelCheckbox, createBlock as _createBlock, vModelRadio as _vModelRadio } from \"vue\";\nconst _hoisted_1 = {\n  class: \"space-y-6\"\n};\nconst _hoisted_2 = {\n  class: \"bg-white dark:bg-gray-800 shadow-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6\"\n};\nconst _hoisted_3 = {\n  class: \"flex flex-wrap items-center justify-between gap-4\"\n};\nconst _hoisted_4 = {\n  class: \"flex flex-wrap gap-3\"\n};\nconst _hoisted_5 = {\n  class: \"flex flex-wrap items-center gap-3\"\n};\nconst _hoisted_6 = {\n  class: \"flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1\"\n};\nconst _hoisted_7 = {\n  class: \"relative\"\n};\nconst _hoisted_8 = {\n  class: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n};\nconst _hoisted_9 = {\n  class: \"relative\"\n};\nconst _hoisted_10 = {\n  class: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n};\nconst _hoisted_11 = {\n  class: \"flex gap-2\"\n};\nconst _hoisted_12 = [\"value\"];\nconst _hoisted_13 = {\n  key: 0,\n  class: \"bg-white rounded-lg shadow overflow-hidden\"\n};\nconst _hoisted_14 = {\n  class: \"px-4 py-3 bg-gray-50 border-b flex justify-between items-center\"\n};\nconst _hoisted_15 = {\n  class: \"text-sm text-gray-700\"\n};\nconst _hoisted_16 = {\n  class: \"font-medium\"\n};\nconst _hoisted_17 = {\n  class: \"font-medium\"\n};\nconst _hoisted_18 = {\n  class: \"flex space-x-2\"\n};\nconst _hoisted_19 = {\n  class: \"min-w-full divide-y divide-gray-200\"\n};\nconst _hoisted_20 = {\n  class: \"bg-gray-50\"\n};\nconst _hoisted_21 = {\n  scope: \"col\",\n  class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n};\nconst _hoisted_22 = {\n  class: \"bg-white divide-y divide-gray-200\"\n};\nconst _hoisted_23 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_24 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_25 = {\n  class: \"ml-2 font-medium text-gray-900\"\n};\nconst _hoisted_26 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_27 = {\n  class: \"text-sm text-gray-900\"\n};\nconst _hoisted_28 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_29 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_30 = {\n  class: \"text-sm font-medium text-gray-900\"\n};\nconst _hoisted_31 = {\n  key: 0,\n  class: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\"\n};\nconst _hoisted_32 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_33 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_34 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_35 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_36 = {\n  key: 1,\n  class: \"ml-1\"\n};\nconst _hoisted_37 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_38 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_39 = {\n  class: \"flex-grow\"\n};\nconst _hoisted_40 = [\"type\", \"value\"];\nconst _hoisted_41 = [\"onClick\"];\nconst _hoisted_42 = {\n  class: \"ml-2\"\n};\nconst _hoisted_43 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_44 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_45 = {\n  class: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\"\n};\nconst _hoisted_46 = {\n  class: \"flex space-x-2\"\n};\nconst _hoisted_47 = [\"onClick\"];\nconst _hoisted_48 = [\"onClick\"];\nconst _hoisted_49 = {\n  key: 0\n};\nconst _hoisted_50 = {\n  colspan: \"9\",\n  class: \"px-6 py-10 text-center\"\n};\nconst _hoisted_51 = {\n  class: \"text-gray-500\"\n};\nconst _hoisted_52 = {\n  class: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\"\n};\nconst _hoisted_53 = {\n  class: \"px-4 py-5 sm:p-6\"\n};\nconst _hoisted_54 = {\n  class: \"flex flex-wrap justify-between items-start mb-4\"\n};\nconst _hoisted_55 = {\n  class: \"flex items-center mb-2 sm:mb-0\"\n};\nconst _hoisted_56 = {\n  class: \"text-lg font-medium text-gray-900\"\n};\nconst _hoisted_57 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_58 = {\n  class: \"space-y-4\"\n};\nconst _hoisted_59 = {\n  class: \"flex flex-wrap justify-between items-center mb-2\"\n};\nconst _hoisted_60 = {\n  class: \"flex items-center mb-2 sm:mb-0\"\n};\nconst _hoisted_61 = {\n  class: \"text-sm font-medium text-gray-900\"\n};\nconst _hoisted_62 = {\n  key: 0,\n  class: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\"\n};\nconst _hoisted_63 = [\"onClick\"];\nconst _hoisted_64 = {\n  class: \"mb-2\"\n};\nconst _hoisted_65 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_66 = [\"type\", \"value\"];\nconst _hoisted_67 = [\"onClick\"];\nconst _hoisted_68 = {\n  class: \"ml-2\"\n};\nconst _hoisted_69 = {\n  class: \"grid grid-cols-2 gap-2 text-xs\"\n};\nconst _hoisted_70 = {\n  class: \"text-gray-900\"\n};\nconst _hoisted_71 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_72 = {\n  class: \"col-span-2 mt-1\"\n};\nconst _hoisted_73 = {\n  class: \"mt-4 flex justify-center\"\n};\nconst _hoisted_74 = [\"onClick\"];\nconst _hoisted_75 = {\n  class: \"mb-6\"\n};\nconst _hoisted_76 = {\n  class: \"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\"\n};\nconst _hoisted_77 = {\n  class: \"flex items-center space-x-3\"\n};\nconst _hoisted_78 = {\n  class: \"w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center\"\n};\nconst _hoisted_79 = {\n  class: \"font-semibold text-gray-900 dark:text-white\"\n};\nconst _hoisted_80 = {\n  class: \"text-sm text-gray-600 dark:text-gray-400\"\n};\nconst _hoisted_81 = {\n  class: \"mb-6\"\n};\nconst _hoisted_82 = {\n  class: \"grid grid-cols-2 gap-3\"\n};\nconst _hoisted_83 = {\n  key: 0,\n  class: \"mb-6\"\n};\nconst _hoisted_84 = {\n  class: \"bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700\"\n};\nconst _hoisted_85 = {\n  class: \"mb-6\"\n};\nconst _hoisted_86 = {\n  class: \"bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 space-y-4\"\n};\nconst _hoisted_87 = {\n  class: \"relative\"\n};\nconst _hoisted_88 = [\"type\"];\nconst _hoisted_89 = {\n  class: \"relative\"\n};\nconst _hoisted_90 = [\"type\"];\nconst _hoisted_91 = {\n  key: 0,\n  class: \"mt-2 text-sm text-red-600 dark:text-red-400 flex items-center\"\n};\nconst _hoisted_92 = {\n  key: 0\n};\nconst _hoisted_93 = {\n  class: \"space-y-2 mt-4\"\n};\nconst _hoisted_94 = {\n  class: \"mb-4\"\n};\nconst _hoisted_95 = {\n  class: \"px-3 py-2 bg-gray-50 rounded-md\"\n};\nconst _hoisted_96 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_97 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_98 = {\n  class: \"relative inline-block w-10 mr-2 align-middle select-none\"\n};\nconst _hoisted_99 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_100 = [\"value\"];\nconst _hoisted_101 = {\n  class: \"form-group\"\n};\nconst _hoisted_102 = {\n  class: \"flex justify-between mb-1\"\n};\nconst _hoisted_103 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_104 = [\"type\"];\nconst _hoisted_105 = {\n  class: \"form-group\"\n};\nconst _hoisted_106 = {\n  class: \"mb-2\"\n};\nconst _hoisted_107 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_108 = {\n  class: \"form-text\"\n};\nconst _hoisted_109 = {\n  class: \"form-group\"\n};\nconst _hoisted_110 = [\"value\"];\nconst _hoisted_111 = {\n  class: \"form-group\"\n};\nconst _hoisted_112 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_113 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_114 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_115 = {\n  key: 0,\n  class: \"mt-3\"\n};\nconst _hoisted_116 = {\n  class: \"grid grid-cols-2 gap-4\"\n};\nconst _hoisted_117 = {\n  class: \"form-group\"\n};\nconst _hoisted_118 = {\n  class: \"form-group\"\n};\nconst _hoisted_119 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_120 = {\n  class: \"form-group\"\n};\nconst _hoisted_121 = [\"value\"];\nconst _hoisted_122 = {\n  class: \"form-group\"\n};\nconst _hoisted_123 = {\n  class: \"form-group\"\n};\nconst _hoisted_124 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_125 = {\n  class: \"form-group\"\n};\nconst _hoisted_126 = [\"value\"];\nconst _hoisted_127 = {\n  class: \"form-group\"\n};\nconst _hoisted_128 = {\n  class: \"form-group\"\n};\nconst _hoisted_129 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_130 = {\n  class: \"mb-2\"\n};\nconst _hoisted_131 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_132 = {\n  class: \"form-text\"\n};\nconst _hoisted_133 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_134 = {\n  class: \"p-4 border border-gray-200 rounded-md\"\n};\nconst _hoisted_135 = {\n  class: \"mb-3\"\n};\nconst _hoisted_136 = {\n  class: \"mb-3\"\n};\nconst _hoisted_137 = {\n  class: \"flex items-center mb-3\"\n};\nconst _hoisted_138 = {\n  class: \"inline-flex items-center\"\n};\nconst _hoisted_139 = {\n  class: \"mb-3\"\n};\nconst _hoisted_140 = {\n  class: \"flex space-x-3\"\n};\nconst _hoisted_141 = {\n  key: 0,\n  class: \"mb-3\"\n};\nconst _hoisted_142 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_143 = [\"type\"];\nconst _hoisted_144 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_145 = [\"value\"];\nconst _hoisted_146 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_147 = {\n  class: \"space-y-2\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_SecurityDashboard = _resolveComponent(\"SecurityDashboard\");\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_CustomCheckbox = _resolveComponent(\"CustomCheckbox\");\n  const _component_StatusBadge = _resolveComponent(\"StatusBadge\");\n  const _component_AdvancedPasswordGenerator = _resolveComponent(\"AdvancedPasswordGenerator\");\n  const _component_PasswordStrengthMeter = _resolveComponent(\"PasswordStrengthMeter\");\n  const _component_BaseModal = _resolveComponent(\"BaseModal\");\n  const _directive_tooltip = _resolveDirective(\"tooltip\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 安全仪表板 \"), _createVNode(_component_SecurityDashboard, {\n    hosts: _ctx.hosts\n  }, null, 8 /* PROPS */, [\"hosts\"]), _createCommentVNode(\" 操作工具栏 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" 主要操作按钮 \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.showEmergencyReset && $options.showEmergencyReset(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'exclamation-triangle'],\n    class: \"mr-2\"\n  }), _cache[66] || (_cache[66] = _createElementVNode(\"span\", null, \"紧急重置\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.openBatchUpdateModal && $options.openBatchUpdateModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'key'],\n    class: \"mr-2\"\n  }), _cache[67] || (_cache[67] = _createElementVNode(\"span\", null, \"批量更新密码\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.openBatchApplyModal && $options.openBatchApplyModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"mr-2\"\n  }), _cache[68] || (_cache[68] = _createElementVNode(\"span\", null, \"批量应用策略\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200\",\n    onClick: _cache[3] || (_cache[3] = (...args) => $options.openBatchAddAccountModal && $options.openBatchAddAccountModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'users'],\n    class: \"mr-2\"\n  }), _cache[69] || (_cache[69] = _createElementVNode(\"span\", null, \"批量添加账号\", -1 /* HOISTED */))])]), _createCommentVNode(\" 筛选和视图控制 \"), _createElementVNode(\"div\", _hoisted_5, [_createCommentVNode(\" 视图切换 \"), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200 focus:outline-none\", $data.viewMode === 'table' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white']),\n    onClick: _cache[4] || (_cache[4] = $event => $data.viewMode = 'table')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'table'],\n    class: \"mr-1.5\"\n  }), _cache[70] || (_cache[70] = _createTextVNode(\" 表格 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200 focus:outline-none\", $data.viewMode === 'card' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white']),\n    onClick: _cache[5] || (_cache[5] = $event => $data.viewMode = 'card')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'th-large'],\n    class: \"mr-1.5\"\n  }), _cache[71] || (_cache[71] = _createTextVNode(\" 卡片 \"))], 2 /* CLASS */)]), _createCommentVNode(\" 搜索框 \"), _createElementVNode(\"div\", _hoisted_7, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.filterText = $event),\n    placeholder: \"搜索主机...\",\n    class: \"w-48 pl-10 pr-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white dark:placeholder-gray-400\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.filterText]]), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"text-gray-400\"\n  })])]), _createCommentVNode(\" 账号筛选 \"), _createElementVNode(\"div\", _hoisted_9, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.accountFilterText = $event),\n    placeholder: \"搜索账号...\",\n    class: \"w-48 pl-10 pr-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white dark:placeholder-gray-400\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.accountFilterText]]), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'user'],\n    class: \"text-gray-400\"\n  })])]), _createCommentVNode(\" 筛选下拉菜单 \"), _createElementVNode(\"div\", _hoisted_11, [_withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.statusFilter = $event),\n    class: \"px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white\"\n  }, _cache[72] || (_cache[72] = [_createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有状态\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"normal\"\n  }, \"正常\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"warning\"\n  }, \"警告\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"error\"\n  }, \"错误\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.statusFilter]]), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.expiryFilter = $event),\n    class: \"px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white\"\n  }, _cache[73] || (_cache[73] = [_createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有密码\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"expired\"\n  }, \"已过期\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"expiring-soon\"\n  }, \"即将过期\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"valid\"\n  }, \"有效期内\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.expiryFilter]]), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.policyFilter = $event),\n    class: \"px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white\"\n  }, [_cache[74] || (_cache[74] = _createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有策略\", -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: policy.id,\n      value: policy.id\n    }, _toDisplayString(policy.name), 9 /* TEXT, PROPS */, _hoisted_12);\n  }), 128 /* KEYED_FRAGMENT */)), _cache[75] || (_cache[75] = _createElementVNode(\"option\", {\n    value: \"none\"\n  }, \"无策略\", -1 /* HOISTED */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.policyFilter]])])])])]), _createCommentVNode(\" 主机列表 \"), _createCommentVNode(\" 表格视图 \"), $data.viewMode === 'table' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_createCommentVNode(\" 账号计数和导出按钮 \"), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_cache[76] || (_cache[76] = _createTextVNode(\" 显示 \")), _createElementVNode(\"span\", _hoisted_16, _toDisplayString($options.filteredAccounts.length), 1 /* TEXT */), _cache[77] || (_cache[77] = _createTextVNode(\" 个账号 (共 \")), _createElementVNode(\"span\", _hoisted_17, _toDisplayString($options.getAllAccounts.length), 1 /* TEXT */), _cache[78] || (_cache[78] = _createTextVNode(\" 个) \"))]), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n    onClick: _cache[11] || (_cache[11] = (...args) => $options.exportPasswordsToCSV && $options.exportPasswordsToCSV(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'file-export'],\n    class: \"mr-1\"\n  }), _cache[79] || (_cache[79] = _createTextVNode(\" 导出 \"))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n    onClick: _cache[12] || (_cache[12] = (...args) => $options.printPasswordData && $options.printPasswordData(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'print'],\n    class: \"mr-1\"\n  }), _cache[80] || (_cache[80] = _createTextVNode(\" 打印 \"))])])]), _createElementVNode(\"table\", _hoisted_19, [_createElementVNode(\"thead\", _hoisted_20, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", _hoisted_21, [_createVNode(_component_CustomCheckbox, {\n    modelValue: $data.selectAll,\n    \"onUpdate:modelValue\": [_cache[13] || (_cache[13] = $event => $data.selectAll = $event), $options.toggleSelectAll]\n  }, {\n    default: _withCtx(() => _cache[81] || (_cache[81] = [_createTextVNode(\" 主机名 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _cache[82] || (_cache[82] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" IP地址 \", -1 /* HOISTED */)), _cache[83] || (_cache[83] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 账号 \", -1 /* HOISTED */)), _cache[84] || (_cache[84] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 最后密码修改时间 \", -1 /* HOISTED */)), _cache[85] || (_cache[85] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码过期时间 \", -1 /* HOISTED */)), _cache[86] || (_cache[86] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码 \", -1 /* HOISTED */)), _cache[87] || (_cache[87] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 状态 \", -1 /* HOISTED */)), _cache[88] || (_cache[88] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 策略 \", -1 /* HOISTED */)), _cache[89] || (_cache[89] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 操作 \", -1 /* HOISTED */))])]), _createElementVNode(\"tbody\", _hoisted_22, [_createCommentVNode(\" 按主机分组显示 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.groupedAccounts, hostGroup => {\n    return _openBlock(), _createElementBlock(_Fragment, {\n      key: hostGroup.hostId\n    }, [_createCommentVNode(\" 账号行 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(hostGroup.accounts, (account, accountIndex) => {\n      return _openBlock(), _createElementBlock(\"tr\", {\n        key: account.id,\n        class: _normalizeClass({\n          'bg-gray-50': accountIndex % 2 === 0,\n          'hover:bg-blue-50': true\n        })\n      }, [_createElementVNode(\"td\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_createVNode(_component_CustomCheckbox, {\n        modelValue: account.host.selected,\n        \"onUpdate:modelValue\": $event => account.host.selected = $event,\n        class: \"ml-4\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"span\", _hoisted_25, _toDisplayString(account.host.name), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])])]), _createElementVNode(\"td\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, _toDisplayString(account.host.ip), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"span\", _hoisted_30, _toDisplayString(account.username), 1 /* TEXT */), account.isDefault ? (_openBlock(), _createElementBlock(\"span\", _hoisted_31, \" 默认 \")) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"td\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, _toDisplayString(account.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_34, [_createElementVNode(\"div\", {\n        class: _normalizeClass({\n          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\n          'bg-red-100 text-red-800': $options.isPasswordExpired(account).status === 'danger' || $options.isPasswordExpired(account).status === 'expired',\n          'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(account).status === 'warning',\n          'bg-gray-100 text-gray-800': $options.isPasswordExpired(account).status === 'normal'\n        })\n      }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(account).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(account).status === 'expired' || $options.isPasswordExpired(account).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_35, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-triangle']\n      })])) : $options.isPasswordExpired(account).status === 'warning' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_36, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-circle']\n      })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]), _createElementVNode(\"td\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_createElementVNode(\"input\", {\n        type: $data.passwordVisibility[account.id] ? 'text' : 'password',\n        value: account.password,\n        readonly: \"\",\n        class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n      }, null, 8 /* PROPS */, _hoisted_40)]), _createElementVNode(\"button\", {\n        onClick: $event => $options.togglePasswordVisibility(account.id),\n        class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $data.passwordVisibility[account.id] ? 'eye-slash' : 'eye'],\n        class: \"text-lg\"\n      }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_41), _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_42, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $options.isPasswordCompliant(account).compliant ? 'check-circle' : 'exclamation-circle'],\n        class: _normalizeClass($options.isPasswordCompliant(account).compliant ? 'text-green-500' : 'text-red-500')\n      }, null, 8 /* PROPS */, [\"icon\", \"class\"])])), [[_directive_tooltip, $options.isPasswordCompliant(account).text]])])]), _createElementVNode(\"td\", _hoisted_43, [_createVNode(_component_StatusBadge, {\n        type: account.host.status\n      }, null, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"td\", _hoisted_44, [_createElementVNode(\"div\", {\n        class: _normalizeClass(['inline-flex items-center px-2 py-0.5 rounded text-xs font-medium', $options.getPolicyColorClass(account.policyId)])\n      }, _toDisplayString($options.getPolicyName(account.policyId)), 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", _hoisted_45, [_createElementVNode(\"div\", _hoisted_46, [_createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.openChangePasswordModal(account.host, account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'key'],\n        class: \"mr-1\"\n      }), _cache[90] || (_cache[90] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_47), _createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.copyPassword(account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'copy'],\n        class: \"mr-1\"\n      }), _cache[91] || (_cache[91] = _createTextVNode(\" 复制 \"))], 8 /* PROPS */, _hoisted_48)])])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))], 64 /* STABLE_FRAGMENT */);\n  }), 128 /* KEYED_FRAGMENT */)), _createCommentVNode(\" 无数据显示 \"), $options.filteredAccounts.length === 0 ? (_openBlock(), _createElementBlock(\"tr\", _hoisted_49, [_createElementVNode(\"td\", _hoisted_50, [_createElementVNode(\"div\", _hoisted_51, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"text-4xl mb-3\"\n  }), _cache[92] || (_cache[92] = _createElementVNode(\"p\", null, \"没有找到匹配的账号数据\", -1 /* HOISTED */))])])])) : _createCommentVNode(\"v-if\", true)])])])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 卡片视图 \"), _createElementVNode(\"div\", _hoisted_52, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredHosts, host => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: host.id,\n      class: \"bg-white overflow-hidden shadow rounded-lg\"\n    }, [_createElementVNode(\"div\", _hoisted_53, [_createCommentVNode(\" 主机头部 \"), _createElementVNode(\"div\", _hoisted_54, [_createElementVNode(\"div\", _hoisted_55, [_createVNode(_component_CustomCheckbox, {\n      modelValue: host.selected,\n      \"onUpdate:modelValue\": $event => host.selected = $event,\n      class: \"mr-2\"\n    }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"]), _createElementVNode(\"div\", null, [_createElementVNode(\"h3\", _hoisted_56, _toDisplayString(host.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_57, _toDisplayString(host.ip), 1 /* TEXT */)])]), _createVNode(_component_StatusBadge, {\n      type: host.status\n    }, null, 8 /* PROPS */, [\"type\"])]), _createCommentVNode(\" 账号列表 \"), _createElementVNode(\"div\", _hoisted_58, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(host.accounts, account => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: account.id,\n        class: _normalizeClass([\"border border-gray-200 rounded-lg p-3\", {\n          'border-green-300 bg-green-50': account.isDefault\n        }])\n      }, [_createElementVNode(\"div\", _hoisted_59, [_createElementVNode(\"div\", _hoisted_60, [_createElementVNode(\"span\", _hoisted_61, _toDisplayString(account.username), 1 /* TEXT */), account.isDefault ? (_openBlock(), _createElementBlock(\"span\", _hoisted_62, \" 默认 \")) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.openChangePasswordModal(host, account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'key'],\n        class: \"mr-1\"\n      }), _cache[93] || (_cache[93] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_63)]), _createCommentVNode(\" 密码展示 \"), _createElementVNode(\"div\", _hoisted_64, [_cache[94] || (_cache[94] = _createElementVNode(\"div\", {\n        class: \"text-xs font-medium text-gray-500 mb-1\"\n      }, \"密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_65, [_createElementVNode(\"input\", {\n        type: $data.passwordVisibility[account.id] ? 'text' : 'password',\n        value: account.password,\n        readonly: \"\",\n        class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n      }, null, 8 /* PROPS */, _hoisted_66), _createElementVNode(\"button\", {\n        onClick: $event => $options.togglePasswordVisibility(account.id),\n        class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $data.passwordVisibility[account.id] ? 'eye-slash' : 'eye'],\n        class: \"text-lg\"\n      }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_67), _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_68, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $options.isPasswordCompliant(account).compliant ? 'check-circle' : 'exclamation-circle'],\n        class: _normalizeClass($options.isPasswordCompliant(account).compliant ? 'text-green-500' : 'text-red-500')\n      }, null, 8 /* PROPS */, [\"icon\", \"class\"])])), [[_directive_tooltip, $options.isPasswordCompliant(account).text]])])]), _createCommentVNode(\" 密码信息区域 \"), _createElementVNode(\"div\", _hoisted_69, [_createElementVNode(\"div\", null, [_cache[95] || (_cache[95] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"最后修改时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_70, _toDisplayString(account.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[96] || (_cache[96] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"密码过期\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n        class: _normalizeClass({\n          'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\n          'bg-red-100 text-red-800': $options.isPasswordExpired(account).status === 'danger' || $options.isPasswordExpired(account).status === 'expired',\n          'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(account).status === 'warning',\n          'bg-gray-100 text-gray-800': $options.isPasswordExpired(account).status === 'normal'\n        })\n      }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(account).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(account).status === 'expired' || $options.isPasswordExpired(account).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_71, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-triangle']\n      })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_72, [_cache[97] || (_cache[97] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"密码策略\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n        class: _normalizeClass(['inline-flex items-center px-2 py-0.5 rounded text-xs font-medium', $options.getPolicyColorClass(account.policyId)])\n      }, _toDisplayString($options.getPolicyName(account.policyId)), 3 /* TEXT, CLASS */)])])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 添加账号按钮 \"), _createElementVNode(\"div\", _hoisted_73, [_createElementVNode(\"button\", {\n      class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n      onClick: $event => $options.openAddAccountModal(host)\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'plus'],\n      class: \"mr-1\"\n    }), _cache[98] || (_cache[98] = _createTextVNode(\" 添加账号 \"))], 8 /* PROPS */, _hoisted_74)])])]);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 修改密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.changePasswordModal.show,\n    \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.changePasswordModal.show = $event),\n    title: \"修改密码\",\n    size: \"lg\",\n    onConfirm: $options.updatePassword,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createCommentVNode(\" 主机信息卡片 \"), _createElementVNode(\"div\", _hoisted_75, [_createElementVNode(\"div\", _hoisted_76, [_createElementVNode(\"div\", _hoisted_77, [_createElementVNode(\"div\", _hoisted_78, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'server'],\n      class: \"text-blue-600 dark:text-blue-400\"\n    })]), _createElementVNode(\"div\", null, [_createElementVNode(\"h4\", _hoisted_79, _toDisplayString($data.currentHost.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_80, [_createElementVNode(\"span\", null, _toDisplayString($data.currentHost.ip), 1 /* TEXT */), _cache[99] || (_cache[99] = _createTextVNode(\" • \")), _createElementVNode(\"span\", null, \"账号: \" + _toDisplayString($data.currentAccount.username), 1 /* TEXT */)])])])])]), _createCommentVNode(\" 密码生成方式选择 \"), _createElementVNode(\"div\", _hoisted_81, [_cache[102] || (_cache[102] = _createElementVNode(\"label\", {\n      class: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_82, [_createElementVNode(\"button\", {\n      onClick: _cache[14] || (_cache[14] = $event => $data.changePasswordModal.method = 'auto'),\n      class: _normalizeClass([\"flex items-center justify-center px-4 py-3 border-2 rounded-lg transition-all duration-200 focus:outline-none\", $data.changePasswordModal.method === 'auto' ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 text-gray-700 dark:text-gray-300'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'magic'],\n      class: \"mr-2\"\n    }), _cache[100] || (_cache[100] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"智能生成\", -1 /* HOISTED */))], 2 /* CLASS */), _createElementVNode(\"button\", {\n      onClick: _cache[15] || (_cache[15] = $event => $data.changePasswordModal.method = 'manual'),\n      class: _normalizeClass([\"flex items-center justify-center px-4 py-3 border-2 rounded-lg transition-all duration-200 focus:outline-none\", $data.changePasswordModal.method === 'manual' ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 text-gray-700 dark:text-gray-300'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'edit'],\n      class: \"mr-2\"\n    }), _cache[101] || (_cache[101] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"手动输入\", -1 /* HOISTED */))], 2 /* CLASS */)])]), _createCommentVNode(\" 智能生成模式 \"), $data.changePasswordModal.method === 'auto' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_83, [_createElementVNode(\"div\", _hoisted_84, [_createVNode(_component_AdvancedPasswordGenerator, {\n      \"initial-options\": _ctx.getPasswordGeneratorOptions(),\n      onPasswordGenerated: _ctx.onPasswordGenerated\n    }, null, 8 /* PROPS */, [\"initial-options\", \"onPasswordGenerated\"])])])) : (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createCommentVNode(\" 手动输入模式 \"), _createElementVNode(\"div\", _hoisted_85, [_createElementVNode(\"div\", _hoisted_86, [_createElementVNode(\"div\", null, [_cache[103] || (_cache[103] = _createElementVNode(\"label\", {\n      class: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\n    }, \"新密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_87, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.new ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.changePasswordModal.newPassword = $event),\n      class: \"w-full px-4 py-3 pr-12 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white\",\n      placeholder: \"输入新密码\",\n      onInput: _cache[17] || (_cache[17] = (...args) => _ctx.validatePassword && _ctx.validatePassword(...args))\n    }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_88), [[_vModelDynamic, $data.changePasswordModal.newPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[18] || (_cache[18] = $event => $data.passwordVisibility.new = !$data.passwordVisibility.new),\n      type: \"button\",\n      class: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.new ? 'eye-slash' : 'eye']\n    }, null, 8 /* PROPS */, [\"icon\"])])])]), _createElementVNode(\"div\", null, [_cache[105] || (_cache[105] = _createElementVNode(\"label\", {\n      class: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\n    }, \"确认密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_89, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.confirm ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[19] || (_cache[19] = $event => $data.changePasswordModal.confirmPassword = $event),\n      class: _normalizeClass([\"w-full px-4 py-3 pr-12 bg-white dark:bg-gray-700 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white\", $options.passwordMismatch ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 dark:border-gray-600']),\n      placeholder: \"再次输入新密码\",\n      onInput: _cache[20] || (_cache[20] = (...args) => _ctx.validatePassword && _ctx.validatePassword(...args))\n    }, null, 42 /* CLASS, PROPS, NEED_HYDRATION */, _hoisted_90), [[_vModelDynamic, $data.changePasswordModal.confirmPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[21] || (_cache[21] = $event => $data.passwordVisibility.confirm = !$data.passwordVisibility.confirm),\n      type: \"button\",\n      class: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.confirm ? 'eye-slash' : 'eye']\n    }, null, 8 /* PROPS */, [\"icon\"])])]), $options.passwordMismatch ? (_openBlock(), _createElementBlock(\"div\", _hoisted_91, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'exclamation-circle'],\n      class: \"mr-1\"\n    }), _cache[104] || (_cache[104] = _createTextVNode(\" 两次输入的密码不一致 \"))])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 密码强度指示器 \"), $data.changePasswordModal.newPassword ? (_openBlock(), _createElementBlock(\"div\", _hoisted_92, [_createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.newPassword\n    }, null, 8 /* PROPS */, [\"password\"])])) : _createCommentVNode(\"v-if\", true)])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createElementVNode(\"div\", _hoisted_93, [_cache[109] || (_cache[109] = _createElementVNode(\"div\", {\n      class: \"form-label font-medium\"\n    }, \"执行选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.executeImmediately,\n      \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $data.changePasswordModal.executeImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[106] || (_cache[106] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"立即执行\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.saveHistory,\n      \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.changePasswordModal.saveHistory = $event)\n    }, {\n      default: _withCtx(() => _cache[107] || (_cache[107] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"保存历史记录\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.logAudit,\n      \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.changePasswordModal.logAudit = $event)\n    }, {\n      default: _withCtx(() => _cache[108] || (_cache[108] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"记录审计日志\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 添加账号弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.addAccountModal.show,\n    \"onUpdate:modelValue\": _cache[33] || (_cache[33] = $event => $data.addAccountModal.show = $event),\n    title: \"添加账号\",\n    onConfirm: $options.addAccount,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_94, [_cache[112] || (_cache[112] = _createElementVNode(\"div\", {\n      class: \"font-medium mb-2\"\n    }, \"主机信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_95, [_createElementVNode(\"div\", null, [_cache[110] || (_cache[110] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"主机名:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[111] || (_cache[111] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"IP地址:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.ip), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_96, [_cache[113] || (_cache[113] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"账号名称\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.addAccountModal.username = $event),\n      class: \"form-control\",\n      placeholder: \"输入账号名称\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addAccountModal.username]])]), _createElementVNode(\"div\", _hoisted_97, [_cache[115] || (_cache[115] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"设为默认账号\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_98, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.addAccountModal.isDefault = $event),\n      class: \"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.addAccountModal.isDefault]]), _cache[114] || (_cache[114] = _createElementVNode(\"label\", {\n      class: \"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"\n    }, null, -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_99, [_cache[116] || (_cache[116] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $data.addAccountModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[29] || (_cache[29] = $event => $options.generatePasswordForNewAccount())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_100);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.addAccountModal.policyId]])]), _createElementVNode(\"div\", _hoisted_101, [_createElementVNode(\"div\", _hoisted_102, [_cache[118] || (_cache[118] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      onClick: _cache[30] || (_cache[30] = $event => $options.generatePasswordForNewAccount()),\n      type: \"button\",\n      class: \"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[117] || (_cache[117] = _createTextVNode(\" 重新生成 \"))])]), _createElementVNode(\"div\", _hoisted_103, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.newAccount ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[31] || (_cache[31] = $event => $data.addAccountModal.password = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_104), [[_vModelDynamic, $data.addAccountModal.password]]), _createElementVNode(\"button\", {\n      onClick: _cache[32] || (_cache[32] = $event => $data.passwordVisibility.newAccount = !$data.passwordVisibility.newAccount),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.newAccount ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量更新密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchUpdateModal.show,\n    \"onUpdate:modelValue\": _cache[43] || (_cache[43] = $event => $data.batchUpdateModal.show = $event),\n    title: \"批量更新密码\",\n    \"confirm-text\": \"开始更新\",\n    size: \"lg\",\n    onConfirm: $options.batchUpdatePasswords,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_105, [_cache[120] || (_cache[120] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_106, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatch,\n      \"onUpdate:modelValue\": [_cache[34] || (_cache[34] = $event => $data.selectAllBatch = $event), $options.toggleSelectAllBatch]\n    }, {\n      default: _withCtx(() => _cache[119] || (_cache[119] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_107, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchUpdateModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchUpdateModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_108, \"已选择 \" + _toDisplayString($options.selectedHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_109, [_cache[121] || (_cache[121] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[35] || (_cache[35] = $event => $data.batchUpdateModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_110);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchUpdateModal.policyId]])]), _createElementVNode(\"div\", _hoisted_111, [_cache[126] || (_cache[126] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_112, [_createElementVNode(\"label\", _hoisted_113, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[36] || (_cache[36] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"immediate\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[122] || (_cache[122] = _createElementVNode(\"span\", null, \"立即执行\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_114, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[37] || (_cache[37] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"scheduled\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[123] || (_cache[123] = _createElementVNode(\"span\", null, \"定时执行\", -1 /* HOISTED */))])]), $data.batchUpdateModal.executionTime === 'scheduled' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_115, [_createElementVNode(\"div\", _hoisted_116, [_createElementVNode(\"div\", null, [_cache[124] || (_cache[124] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"日期\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"date\",\n      \"onUpdate:modelValue\": _cache[38] || (_cache[38] = $event => $data.batchUpdateModal.scheduledDate = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledDate]])]), _createElementVNode(\"div\", null, [_cache[125] || (_cache[125] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"时间\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"time\",\n      \"onUpdate:modelValue\": _cache[39] || (_cache[39] = $event => $data.batchUpdateModal.scheduledTime = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledTime]])])])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_117, [_cache[130] || (_cache[130] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[40] || (_cache[40] = $event => $data.batchUpdateModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[127] || (_cache[127] = [_createTextVNode(\" 忽略错误继续执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.detailedLog,\n      \"onUpdate:modelValue\": _cache[41] || (_cache[41] = $event => $data.batchUpdateModal.detailedLog = $event)\n    }, {\n      default: _withCtx(() => _cache[128] || (_cache[128] = [_createTextVNode(\" 记录详细日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.sendNotification,\n      \"onUpdate:modelValue\": _cache[42] || (_cache[42] = $event => $data.batchUpdateModal.sendNotification = $event)\n    }, {\n      default: _withCtx(() => _cache[129] || (_cache[129] = [_createTextVNode(\" 执行完成后发送通知 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量应用策略弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchApplyModal.show,\n    \"onUpdate:modelValue\": _cache[47] || (_cache[47] = $event => $data.batchApplyModal.show = $event),\n    title: \"批量应用密码策略\",\n    \"confirm-text\": \"应用策略\",\n    onConfirm: $options.batchApplyPolicy,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_118, [_cache[131] || (_cache[131] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_119, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchApplyModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchApplyModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_120, [_cache[132] || (_cache[132] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[44] || (_cache[44] = $event => $data.batchApplyModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name), 9 /* TEXT, PROPS */, _hoisted_121);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchApplyModal.policyId]])]), _createElementVNode(\"div\", _hoisted_122, [_cache[135] || (_cache[135] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.updateImmediately,\n      \"onUpdate:modelValue\": _cache[45] || (_cache[45] = $event => $data.batchApplyModal.updateImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[133] || (_cache[133] = [_createTextVNode(\" 立即更新密码以符合策略 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.applyOnNextUpdate,\n      \"onUpdate:modelValue\": _cache[46] || (_cache[46] = $event => $data.batchApplyModal.applyOnNextUpdate = $event)\n    }, {\n      default: _withCtx(() => _cache[134] || (_cache[134] = [_createTextVNode(\" 下次密码更新时应用 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 紧急重置密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.emergencyResetModal.show,\n    \"onUpdate:modelValue\": _cache[51] || (_cache[51] = $event => $data.emergencyResetModal.show = $event),\n    title: \"紧急密码重置\",\n    \"confirm-text\": \"立即重置\",\n    icon: \"exclamation-triangle\",\n    danger: \"\",\n    onConfirm: $options.emergencyReset,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_cache[141] || (_cache[141] = _createElementVNode(\"div\", {\n      class: \"bg-red-50 text-red-700 p-3 rounded-md mb-4\"\n    }, [_createElementVNode(\"p\", null, \"紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_123, [_cache[136] || (_cache[136] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_124, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.emergencyResetModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.emergencyResetModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_125, [_cache[137] || (_cache[137] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用紧急策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[48] || (_cache[48] = $event => $data.emergencyResetModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.emergencyPolicies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_126);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.policyId]])]), _createElementVNode(\"div\", _hoisted_127, [_cache[139] || (_cache[139] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"操作原因\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[49] || (_cache[49] = $event => $data.emergencyResetModal.reason = $event),\n      class: \"form-select\"\n    }, _cache[138] || (_cache[138] = [_createElementVNode(\"option\", {\n      value: \"security_incident\"\n    }, \"安全事件响应\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"password_leak\"\n    }, \"密码泄露\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"abnormal_access\"\n    }, \"异常访问\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"compliance\"\n    }, \"合规要求\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"other\"\n    }, \"其他原因\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.reason]])]), _createElementVNode(\"div\", _hoisted_128, [_cache[140] || (_cache[140] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"附加说明\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n      \"onUpdate:modelValue\": _cache[50] || (_cache[50] = $event => $data.emergencyResetModal.description = $event),\n      class: \"form-control\",\n      rows: \"2\",\n      placeholder: \"请输入重置原因详细说明\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.emergencyResetModal.description]])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量添加账号弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchAddAccountModal.show,\n    \"onUpdate:modelValue\": _cache[65] || (_cache[65] = $event => $data.batchAddAccountModal.show = $event),\n    title: \"批量添加账号\",\n    size: \"lg\",\n    onConfirm: $options.batchAddAccounts,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_129, [_cache[143] || (_cache[143] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_130, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatchAdd,\n      \"onUpdate:modelValue\": [_cache[52] || (_cache[52] = $event => $data.selectAllBatchAdd = $event), $options.toggleSelectAllBatchAdd]\n    }, {\n      default: _withCtx(() => _cache[142] || (_cache[142] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_131, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchAddAccountModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchAddAccountModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_132, \"已选择 \" + _toDisplayString($options.selectedBatchAddHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_133, [_cache[154] || (_cache[154] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"账号信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_134, [_createElementVNode(\"div\", _hoisted_135, [_cache[144] || (_cache[144] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, [_createTextVNode(\"账号名称 \"), _createElementVNode(\"span\", {\n      class: \"text-red-500\"\n    }, \"*\")], -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[53] || (_cache[53] = $event => $data.batchAddAccountModal.username = $event),\n      class: \"form-control\",\n      placeholder: \"输入统一账号名称\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchAddAccountModal.username]]), _cache[145] || (_cache[145] = _createElementVNode(\"div\", {\n      class: \"text-xs text-gray-500 mt-1\"\n    }, \"将在所有选中主机上创建同名账号\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_136, [_cache[147] || (_cache[147] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"账号角色\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[54] || (_cache[54] = $event => $data.batchAddAccountModal.role = $event),\n      class: \"form-select\"\n    }, _cache[146] || (_cache[146] = [_createElementVNode(\"option\", {\n      value: \"admin\"\n    }, \"管理员\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"user\"\n    }, \"普通用户\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"service\"\n    }, \"服务账号\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"readonly\"\n    }, \"只读账号\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchAddAccountModal.role]])]), _createElementVNode(\"div\", _hoisted_137, [_createElementVNode(\"label\", _hoisted_138, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[55] || (_cache[55] = $event => $data.batchAddAccountModal.setAsDefault = $event),\n      class: \"form-checkbox\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.batchAddAccountModal.setAsDefault]]), _cache[148] || (_cache[148] = _createElementVNode(\"span\", {\n      class: \"ml-2\"\n    }, \"设为默认账号\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_139, [_cache[151] || (_cache[151] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_140, [_createElementVNode(\"button\", {\n      onClick: _cache[56] || (_cache[56] = $event => $data.batchAddAccountModal.useSamePassword = true),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", $data.batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'key'],\n      class: \"mr-2\"\n    }), _cache[149] || (_cache[149] = _createTextVNode(\" 同一密码 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n      onClick: _cache[57] || (_cache[57] = $event => $data.batchAddAccountModal.useSamePassword = false),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", !$data.batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'random'],\n      class: \"mr-2\"\n    }), _cache[150] || (_cache[150] = _createTextVNode(\" 随机密码 \"))], 2 /* CLASS */)])]), $data.batchAddAccountModal.useSamePassword ? (_openBlock(), _createElementBlock(\"div\", _hoisted_141, [_cache[153] || (_cache[153] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"统一密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_142, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.batchPassword ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[58] || (_cache[58] = $event => $data.batchAddAccountModal.password = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_143), [[_vModelDynamic, $data.batchAddAccountModal.password]]), _createElementVNode(\"button\", {\n      onClick: _cache[59] || (_cache[59] = $event => $data.passwordVisibility.batchPassword = !$data.passwordVisibility.batchPassword),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.batchPassword ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])]), _createElementVNode(\"button\", {\n      onClick: _cache[60] || (_cache[60] = $event => $options.generatePasswordForBatchAccount()),\n      type: \"button\",\n      class: \"ml-2 px-3 py-1.5 border border-gray-300 text-xs rounded-md\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[152] || (_cache[152] = _createTextVNode(\" 重新生成 \"))])])])) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_144, [_cache[155] || (_cache[155] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[61] || (_cache[61] = $event => $data.batchAddAccountModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[62] || (_cache[62] = $event => $options.generatePasswordForBatchAccount())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_145);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.batchAddAccountModal.policyId]])]), _createElementVNode(\"div\", _hoisted_146, [_cache[158] || (_cache[158] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_147, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchAddAccountModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[63] || (_cache[63] = $event => $data.batchAddAccountModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[156] || (_cache[156] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"忽略错误继续执行\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchAddAccountModal.generateReport,\n      \"onUpdate:modelValue\": _cache[64] || (_cache[64] = $event => $data.batchAddAccountModal.generateReport = $event)\n    }, {\n      default: _withCtx(() => _cache[157] || (_cache[157] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"生成账号创建报告\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "scope", "colspan", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createVNode", "_component_SecurityDashboard", "hosts", "_ctx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "onClick", "_cache", "args", "$options", "showEmergencyReset", "_component_font_awesome_icon", "icon", "openBatchUpdateModal", "openBatchApplyModal", "openBatchAddAccountModal", "_hoisted_5", "_hoisted_6", "_normalizeClass", "$data", "viewMode", "$event", "_createTextVNode", "_hoisted_7", "type", "filterText", "placeholder", "_hoisted_8", "_hoisted_9", "accountFilterText", "_hoisted_10", "_hoisted_11", "statusFilter", "value", "expiryFilter", "policyFilter", "_Fragment", "_renderList", "policies", "policy", "id", "name", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_toDisplayString", "filteredAccounts", "length", "_hoisted_17", "getAllAccounts", "_hoisted_18", "exportPasswordsToCSV", "printPasswordData", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_component_CustomCheckbox", "modelValue", "selectAll", "toggleSelectAll", "default", "_withCtx", "_", "_hoisted_22", "groupedAccounts", "hostGroup", "hostId", "accounts", "account", "accountIndex", "_hoisted_23", "_hoisted_24", "host", "selected", "_hoisted_25", "_hoisted_26", "_hoisted_27", "ip", "_hoisted_28", "_hoisted_29", "_hoisted_30", "username", "isDefault", "_hoisted_31", "_hoisted_32", "_hoisted_33", "lastPasswordChange", "_hoisted_34", "isPasswordExpired", "status", "text", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "passwordVisibility", "password", "readonly", "_hoisted_40", "togglePasswordVisibility", "_hoisted_41", "_hoisted_42", "isPasswordCompliant", "compliant", "_hoisted_43", "_component_StatusBadge", "_hoisted_44", "getPolicyColorClass", "policyId", "getPolicyName", "_hoisted_45", "_hoisted_46", "openChangePasswordModal", "_hoisted_47", "copyPassword", "_hoisted_48", "_hoisted_49", "_hoisted_50", "_hoisted_51", "_hoisted_52", "filteredHosts", "_hoisted_53", "_hoisted_54", "_hoisted_55", "_hoisted_56", "_hoisted_57", "_hoisted_58", "_hoisted_59", "_hoisted_60", "_hoisted_61", "_hoisted_62", "_hoisted_63", "_hoisted_64", "_hoisted_65", "_hoisted_66", "_hoisted_67", "_hoisted_68", "_hoisted_69", "_hoisted_70", "_hoisted_71", "_hoisted_72", "_hoisted_73", "openAddAccountModal", "_hoisted_74", "_component_BaseModal", "changePasswordModal", "show", "title", "size", "onConfirm", "updatePassword", "loading", "processing", "_hoisted_75", "_hoisted_76", "_hoisted_77", "_hoisted_78", "_hoisted_79", "currentHost", "_hoisted_80", "currentAccount", "_hoisted_81", "_hoisted_82", "method", "_hoisted_83", "_hoisted_84", "_component_AdvancedPasswordGenerator", "getPasswordGeneratorOptions", "onPasswordGenerated", "_hoisted_85", "_hoisted_86", "_hoisted_87", "new", "newPassword", "onInput", "validatePassword", "_hoisted_88", "_hoisted_89", "confirm", "confirmPassword", "passwordMismatch", "_hoisted_90", "_hoisted_91", "_hoisted_92", "_component_PasswordStrengthMeter", "_hoisted_93", "executeImmediately", "saveHistory", "logAudit", "addAccountModal", "addAccount", "_hoisted_94", "_hoisted_95", "_hoisted_96", "_hoisted_97", "_hoisted_98", "_hoisted_99", "onChange", "generatePasswordForNewAccount", "<PERSON><PERSON><PERSON><PERSON>", "expiryDays", "_hoisted_100", "_hoisted_101", "_hoisted_102", "_hoisted_103", "newAccount", "_hoisted_104", "batchUpdateModal", "batchUpdatePasswords", "_hoisted_105", "_hoisted_106", "selectAllBatch", "toggleSelectAllBatch", "_hoisted_107", "_createBlock", "selectedHosts", "_hoisted_108", "selectedHostsCount", "_hoisted_109", "_hoisted_110", "_hoisted_111", "_hoisted_112", "_hoisted_113", "executionTime", "_hoisted_114", "_hoisted_115", "_hoisted_116", "scheduledDate", "scheduledTime", "_hoisted_117", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "batchApplyPolicy", "_hoisted_118", "_hoisted_119", "selectedHostsList", "_hoisted_120", "_hoisted_121", "_hoisted_122", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "danger", "emergencyReset", "_hoisted_123", "_hoisted_124", "_hoisted_125", "emergencyPolicies", "_hoisted_126", "_hoisted_127", "reason", "_hoisted_128", "description", "rows", "batchAddAccountModal", "batchAddAccounts", "_hoisted_129", "_hoisted_130", "selectAllBatchAdd", "toggleSelectAllBatchAdd", "_hoisted_131", "_hoisted_132", "selectedBatchAddHostsCount", "_hoisted_133", "_hoisted_134", "_hoisted_135", "_hoisted_136", "role", "_hoisted_137", "_hoisted_138", "setAsDefault", "_hoisted_139", "_hoisted_140", "useSamePassword", "_hoisted_141", "_hoisted_142", "batchPassword", "_hoisted_143", "generatePasswordForBatchAccount", "_hoisted_144", "_hoisted_145", "_hoisted_146", "_hoisted_147", "generateReport"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div class=\"space-y-6\">\r\n    <!-- 安全仪表板 -->\r\n    <SecurityDashboard :hosts=\"hosts\" />\r\n\r\n    <!-- 操作工具栏 -->\r\n    <div class=\"bg-white dark:bg-gray-800 shadow-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6\">\r\n      <div class=\"flex flex-wrap items-center justify-between gap-4\">\r\n        <!-- 主要操作按钮 -->\r\n        <div class=\"flex flex-wrap gap-3\">\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200\"\r\n            @click=\"showEmergencyReset\">\r\n            <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2\" />\r\n            <span>紧急重置</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200\"\r\n            @click=\"openBatchUpdateModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n            <span>批量更新密码</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200\"\r\n            @click=\"openBatchApplyModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n            <span>批量应用策略</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200\"\r\n            @click=\"openBatchAddAccountModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'users']\" class=\"mr-2\" />\r\n            <span>批量添加账号</span>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- 筛选和视图控制 -->\r\n        <div class=\"flex flex-wrap items-center gap-3\">\r\n          <!-- 视图切换 -->\r\n          <div class=\"flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1\">\r\n            <button\r\n              class=\"px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200 focus:outline-none\"\r\n              :class=\"viewMode === 'table'\r\n                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\r\n                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'\"\r\n              @click=\"viewMode = 'table'\">\r\n              <font-awesome-icon :icon=\"['fas', 'table']\" class=\"mr-1.5\" />\r\n              表格\r\n            </button>\r\n            <button\r\n              class=\"px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200 focus:outline-none\"\r\n              :class=\"viewMode === 'card'\r\n                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\r\n                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'\"\r\n              @click=\"viewMode = 'card'\">\r\n              <font-awesome-icon :icon=\"['fas', 'th-large']\" class=\"mr-1.5\" />\r\n              卡片\r\n            </button>\r\n          </div>\r\n\r\n          <!-- 搜索框 -->\r\n          <div class=\"relative\">\r\n            <input\r\n              type=\"text\"\r\n              v-model=\"filterText\"\r\n              placeholder=\"搜索主机...\"\r\n              class=\"w-48 pl-10 pr-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white dark:placeholder-gray-400\"\r\n            />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 账号筛选 -->\r\n          <div class=\"relative\">\r\n            <input\r\n              type=\"text\"\r\n              v-model=\"accountFilterText\"\r\n              placeholder=\"搜索账号...\"\r\n              class=\"w-48 pl-10 pr-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white dark:placeholder-gray-400\"\r\n            />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'user']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 筛选下拉菜单 -->\r\n          <div class=\"flex gap-2\">\r\n            <select\r\n              v-model=\"statusFilter\"\r\n              class=\"px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white\"\r\n            >\r\n              <option value=\"all\">所有状态</option>\r\n              <option value=\"normal\">正常</option>\r\n              <option value=\"warning\">警告</option>\r\n              <option value=\"error\">错误</option>\r\n            </select>\r\n\r\n            <select\r\n              v-model=\"expiryFilter\"\r\n              class=\"px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white\"\r\n            >\r\n              <option value=\"all\">所有密码</option>\r\n              <option value=\"expired\">已过期</option>\r\n              <option value=\"expiring-soon\">即将过期</option>\r\n              <option value=\"valid\">有效期内</option>\r\n            </select>\r\n\r\n            <select\r\n              v-model=\"policyFilter\"\r\n              class=\"px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white\"\r\n            >\r\n              <option value=\"all\">所有策略</option>\r\n              <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n                {{ policy.name }}\r\n              </option>\r\n              <option value=\"none\">无策略</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <!-- 表格视图 -->\r\n    <div v-if=\"viewMode === 'table'\" class=\"bg-white rounded-lg shadow overflow-hidden\">\r\n      <!-- 账号计数和导出按钮 -->\r\n      <div class=\"px-4 py-3 bg-gray-50 border-b flex justify-between items-center\">\r\n        <div class=\"text-sm text-gray-700\">\r\n          显示 <span class=\"font-medium\">{{ filteredAccounts.length }}</span> 个账号\r\n          (共 <span class=\"font-medium\">{{ getAllAccounts.length }}</span> 个)\r\n        </div>\r\n        <div class=\"flex space-x-2\">\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            @click=\"exportPasswordsToCSV\">\r\n            <font-awesome-icon :icon=\"['fas', 'file-export']\" class=\"mr-1\" />\r\n            导出\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            @click=\"printPasswordData\">\r\n            <font-awesome-icon :icon=\"['fas', 'print']\" class=\"mr-1\" />\r\n            打印\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <table class=\"min-w-full divide-y divide-gray-200\">\r\n        <thead class=\"bg-gray-50\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n                主机名\r\n              </CustomCheckbox>\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              IP地址\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              账号\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              最后密码修改时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码过期时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              状态\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              策略\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              操作\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"bg-white divide-y divide-gray-200\">\r\n          <!-- 按主机分组显示 -->\r\n          <template v-for=\"hostGroup in groupedAccounts\" :key=\"hostGroup.hostId\">\r\n            <!-- 账号行 -->\r\n            <tr v-for=\"(account, accountIndex) in hostGroup.accounts\" :key=\"account.id\"\r\n              :class=\"{ 'bg-gray-50': accountIndex % 2 === 0, 'hover:bg-blue-50': true }\">\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <CustomCheckbox v-model=\"account.host.selected\" class=\"ml-4\">\r\n                    <span class=\"ml-2 font-medium text-gray-900\">{{ account.host.name }}</span>\r\n                  </CustomCheckbox>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-900\">{{ account.host.ip }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-500\">{{ account.lastPasswordChange || '-' }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div :class=\"{\r\n                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\r\n                  'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                  'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                  'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                }\">\r\n                  {{ isPasswordExpired(account).text }}\r\n                  <span\r\n                    v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                    class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                  </span>\r\n                  <span v-else-if=\"isPasswordExpired(account).status === 'warning'\" class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" />\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <div class=\"flex-grow\">\r\n                    <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\"\r\n                      readonly\r\n                      class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  </div>\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                  <div class=\"ml-2\" v-tooltip=\"isPasswordCompliant(account).text\">\r\n                    <font-awesome-icon \r\n                      :icon=\"['fas', isPasswordCompliant(account).compliant ? 'check-circle' : 'exclamation-circle']\" \r\n                      :class=\"isPasswordCompliant(account).compliant ? 'text-green-500' : 'text-red-500'\" \r\n                    />\r\n                  </div>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <StatusBadge :type=\"account.host.status\" />\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div :class=\"['inline-flex items-center px-2 py-0.5 rounded text-xs font-medium', getPolicyColorClass(account.policyId)]\">\r\n                  {{ getPolicyName(account.policyId) }}\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                <div class=\"flex space-x-2\">\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"openChangePasswordModal(account.host, account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                    修改密码\r\n                  </button>\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"copyPassword(account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'copy']\" class=\"mr-1\" />\r\n                    复制\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          </template>\r\n          <!-- 无数据显示 -->\r\n          <tr v-if=\"filteredAccounts.length === 0\">\r\n            <td colspan=\"9\" class=\"px-6 py-10 text-center\">\r\n              <div class=\"text-gray-500\">\r\n                <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-4xl mb-3\" />\r\n                <p>没有找到匹配的账号数据</p>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <!-- 卡片视图 -->\r\n    <div v-else class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\">\r\n      <div v-for=\"host in filteredHosts\" :key=\"host.id\" class=\"bg-white overflow-hidden shadow rounded-lg\">\r\n        <div class=\"px-4 py-5 sm:p-6\">\r\n          <!-- 主机头部 -->\r\n          <div class=\"flex flex-wrap justify-between items-start mb-4\">\r\n            <div class=\"flex items-center mb-2 sm:mb-0\">\r\n              <CustomCheckbox v-model=\"host.selected\" class=\"mr-2\" />\r\n              <div>\r\n                <h3 class=\"text-lg font-medium text-gray-900\">{{ host.name }}</h3>\r\n                <p class=\"text-sm text-gray-500\">{{ host.ip }}</p>\r\n              </div>\r\n            </div>\r\n            <StatusBadge :type=\"host.status\" />\r\n          </div>\r\n\r\n          <!-- 账号列表 -->\r\n          <div class=\"space-y-4\">\r\n            <div v-for=\"account in host.accounts\" :key=\"account.id\" class=\"border border-gray-200 rounded-lg p-3\"\r\n              :class=\"{ 'border-green-300 bg-green-50': account.isDefault }\">\r\n              <div class=\"flex flex-wrap justify-between items-center mb-2\">\r\n                <div class=\"flex items-center mb-2 sm:mb-0\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n                <button\r\n                  class=\"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                  @click=\"openChangePasswordModal(host, account)\">\r\n                  <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                  修改密码\r\n                </button>\r\n              </div>\r\n\r\n              <!-- 密码展示 -->\r\n              <div class=\"mb-2\">\r\n                <div class=\"text-xs font-medium text-gray-500 mb-1\">密码</div>\r\n                <div class=\"flex items-center\">\r\n                  <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\" readonly\r\n                    class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                  <div class=\"ml-2\" v-tooltip=\"isPasswordCompliant(account).text\">\r\n                    <font-awesome-icon \r\n                      :icon=\"['fas', isPasswordCompliant(account).compliant ? 'check-circle' : 'exclamation-circle']\" \r\n                      :class=\"isPasswordCompliant(account).compliant ? 'text-green-500' : 'text-red-500'\" \r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 密码信息区域 -->\r\n              <div class=\"grid grid-cols-2 gap-2 text-xs\">\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">最后修改时间</div>\r\n                  <div class=\"text-gray-900\">{{ account.lastPasswordChange || '-' }}</div>\r\n                </div>\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">密码过期</div>\r\n                  <div :class=\"{\r\n                    'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\r\n                    'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                    'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                    'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                  }\">\r\n                    {{ isPasswordExpired(account).text }}\r\n                    <span\r\n                      v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                      class=\"ml-1\">\r\n                      <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-span-2 mt-1\">\r\n                  <div class=\"font-medium text-gray-500 mb-1\">密码策略</div>\r\n                  <div :class=\"['inline-flex items-center px-2 py-0.5 rounded text-xs font-medium', getPolicyColorClass(account.policyId)]\">\r\n                    {{ getPolicyName(account.policyId) }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 添加账号按钮 -->\r\n          <div class=\"mt-4 flex justify-center\">\r\n            <button\r\n              class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n              @click=\"openAddAccountModal(host)\">\r\n              <font-awesome-icon :icon=\"['fas', 'plus']\" class=\"mr-1\" />\r\n              添加账号\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" size=\"lg\" @confirm=\"updatePassword\" :loading=\"processing\">\r\n      <!-- 主机信息卡片 -->\r\n      <div class=\"mb-6\">\r\n        <div class=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\r\n          <div class=\"flex items-center space-x-3\">\r\n            <div class=\"w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center\">\r\n              <font-awesome-icon :icon=\"['fas', 'server']\" class=\"text-blue-600 dark:text-blue-400\" />\r\n            </div>\r\n            <div>\r\n              <h4 class=\"font-semibold text-gray-900 dark:text-white\">{{ currentHost.name }}</h4>\r\n              <div class=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                <span>{{ currentHost.ip }}</span> • <span>账号: {{ currentAccount.username }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 密码生成方式选择 -->\r\n      <div class=\"mb-6\">\r\n        <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">密码生成方式</label>\r\n        <div class=\"grid grid-cols-2 gap-3\">\r\n          <button\r\n            @click=\"changePasswordModal.method = 'auto'\"\r\n            class=\"flex items-center justify-center px-4 py-3 border-2 rounded-lg transition-all duration-200 focus:outline-none\"\r\n            :class=\"changePasswordModal.method === 'auto'\r\n              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'\r\n              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 text-gray-700 dark:text-gray-300'\"\r\n          >\r\n            <font-awesome-icon :icon=\"['fas', 'magic']\" class=\"mr-2\" />\r\n            <span class=\"font-medium\">智能生成</span>\r\n          </button>\r\n          <button\r\n            @click=\"changePasswordModal.method = 'manual'\"\r\n            class=\"flex items-center justify-center px-4 py-3 border-2 rounded-lg transition-all duration-200 focus:outline-none\"\r\n            :class=\"changePasswordModal.method === 'manual'\r\n              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'\r\n              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 text-gray-700 dark:text-gray-300'\"\r\n          >\r\n            <font-awesome-icon :icon=\"['fas', 'edit']\" class=\"mr-2\" />\r\n            <span class=\"font-medium\">手动输入</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 智能生成模式 -->\r\n      <div v-if=\"changePasswordModal.method === 'auto'\" class=\"mb-6\">\r\n        <div class=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700\">\r\n          <AdvancedPasswordGenerator\r\n            :initial-options=\"getPasswordGeneratorOptions()\"\r\n            @password-generated=\"onPasswordGenerated\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 手动输入模式 -->\r\n      <div v-else class=\"mb-6\">\r\n        <div class=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 space-y-4\">\r\n          <div>\r\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">新密码</label>\r\n            <div class=\"relative\">\r\n              <input\r\n                :type=\"passwordVisibility.new ? 'text' : 'password'\"\r\n                v-model=\"changePasswordModal.newPassword\"\r\n                class=\"w-full px-4 py-3 pr-12 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white\"\r\n                placeholder=\"输入新密码\"\r\n                @input=\"validatePassword\"\r\n              />\r\n              <button\r\n                @click=\"passwordVisibility.new = !passwordVisibility.new\"\r\n                type=\"button\"\r\n                class=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\"\r\n              >\r\n                <font-awesome-icon :icon=\"['fas', passwordVisibility.new ? 'eye-slash' : 'eye']\" />\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <div>\r\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">确认密码</label>\r\n            <div class=\"relative\">\r\n              <input\r\n                :type=\"passwordVisibility.confirm ? 'text' : 'password'\"\r\n                v-model=\"changePasswordModal.confirmPassword\"\r\n                class=\"w-full px-4 py-3 pr-12 bg-white dark:bg-gray-700 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white\"\r\n                :class=\"passwordMismatch\r\n                  ? 'border-red-500 focus:ring-red-500 focus:border-red-500'\r\n                  : 'border-gray-300 dark:border-gray-600'\"\r\n                placeholder=\"再次输入新密码\"\r\n                @input=\"validatePassword\"\r\n              />\r\n              <button\r\n                @click=\"passwordVisibility.confirm = !passwordVisibility.confirm\"\r\n                type=\"button\"\r\n                class=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\"\r\n              >\r\n                <font-awesome-icon :icon=\"['fas', passwordVisibility.confirm ? 'eye-slash' : 'eye']\" />\r\n              </button>\r\n            </div>\r\n            <div v-if=\"passwordMismatch\" class=\"mt-2 text-sm text-red-600 dark:text-red-400 flex items-center\">\r\n              <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" class=\"mr-1\" />\r\n              两次输入的密码不一致\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 密码强度指示器 -->\r\n          <div v-if=\"changePasswordModal.newPassword\">\r\n            <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"space-y-2 mt-4\">\r\n        <div class=\"form-label font-medium\">执行选项</div>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          <span class=\"ml-2\">立即执行</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          <span class=\"ml-2\">保存历史记录</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          <span class=\"ml-2\">记录审计日志</span>\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 添加账号弹窗 -->\r\n    <BaseModal v-model=\"addAccountModal.show\" title=\"添加账号\" @confirm=\"addAccount\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">账号名称</label>\r\n        <input type=\"text\" v-model=\"addAccountModal.username\" class=\"form-control\" placeholder=\"输入账号名称\" />\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">设为默认账号</label>\r\n        <div class=\"relative inline-block w-10 mr-2 align-middle select-none\">\r\n          <input type=\"checkbox\" v-model=\"addAccountModal.isDefault\"\r\n            class=\"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\" />\r\n          <label class=\"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"></label>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"addAccountModal.policyId\" class=\"form-select\" @change=\"generatePasswordForNewAccount()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <div class=\"flex justify-between mb-1\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <button @click=\"generatePasswordForNewAccount()\" type=\"button\"\r\n            class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n            重新生成\r\n          </button>\r\n        </div>\r\n        <div class=\"flex items-center\">\r\n          <input :type=\"passwordVisibility.newAccount ? 'text' : 'password'\" v-model=\"addAccountModal.password\" readonly\r\n            class=\"form-control flex-1 bg-gray-50\" />\r\n          <button @click=\"passwordVisibility.newAccount = !passwordVisibility.newAccount\" type=\"button\"\r\n            class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', passwordVisibility.newAccount ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量添加账号弹窗 -->\r\n    <BaseModal v-model=\"batchAddAccountModal.show\" title=\"批量添加账号\" size=\"lg\" @confirm=\"batchAddAccounts\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatchAdd\" @update:modelValue=\"toggleSelectAllBatchAdd\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchAddAccountModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedBatchAddHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">账号信息</label>\r\n        <div class=\"p-4 border border-gray-200 rounded-md\">\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">账号名称 <span class=\"text-red-500\">*</span></label>\r\n            <input type=\"text\" v-model=\"batchAddAccountModal.username\" class=\"form-control\" placeholder=\"输入统一账号名称\" />\r\n            <div class=\"text-xs text-gray-500 mt-1\">将在所有选中主机上创建同名账号</div>\r\n          </div>\r\n\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">账号角色</label>\r\n            <select v-model=\"batchAddAccountModal.role\" class=\"form-select\">\r\n              <option value=\"admin\">管理员</option>\r\n              <option value=\"user\">普通用户</option>\r\n              <option value=\"service\">服务账号</option>\r\n              <option value=\"readonly\">只读账号</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div class=\"flex items-center mb-3\">\r\n            <label class=\"inline-flex items-center\">\r\n              <input type=\"checkbox\" v-model=\"batchAddAccountModal.setAsDefault\" class=\"form-checkbox\">\r\n              <span class=\"ml-2\">设为默认账号</span>\r\n            </label>\r\n          </div>\r\n\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">密码生成方式</label>\r\n            <div class=\"flex space-x-3\">\r\n              <button @click=\"batchAddAccountModal.useSamePassword = true\"\r\n                class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n                :class=\"batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n                <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n                同一密码\r\n              </button>\r\n              <button @click=\"batchAddAccountModal.useSamePassword = false\"\r\n                class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n                :class=\"!batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n                <font-awesome-icon :icon=\"['fas', 'random']\" class=\"mr-2\" />\r\n                随机密码\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <div v-if=\"batchAddAccountModal.useSamePassword\" class=\"mb-3\">\r\n            <label class=\"form-label\">统一密码</label>\r\n            <div class=\"flex items-center\">\r\n              <input :type=\"passwordVisibility.batchPassword ? 'text' : 'password'\"\r\n                v-model=\"batchAddAccountModal.password\" readonly class=\"form-control flex-1 bg-gray-50\" />\r\n              <button @click=\"passwordVisibility.batchPassword = !passwordVisibility.batchPassword\" type=\"button\"\r\n                class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                <font-awesome-icon :icon=\"['fas', passwordVisibility.batchPassword ? 'eye-slash' : 'eye']\"\r\n                  class=\"text-lg\" />\r\n              </button>\r\n              <button @click=\"generatePasswordForBatchAccount()\" type=\"button\"\r\n                class=\"ml-2 px-3 py-1.5 border border-gray-300 text-xs rounded-md\">\r\n                <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n                重新生成\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchAddAccountModal.policyId\" class=\"form-select\" @change=\"generatePasswordForBatchAccount()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <div class=\"space-y-2\">\r\n          <CustomCheckbox v-model=\"batchAddAccountModal.ignoreErrors\">\r\n            <span class=\"ml-2\">忽略错误继续执行</span>\r\n          </CustomCheckbox>\r\n          <CustomCheckbox v-model=\"batchAddAccountModal.generateReport\">\r\n            <span class=\"ml-2\">生成账号创建报告</span>\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\nimport SecurityDashboard from '@/components/SecurityDashboard.vue'\r\nimport AdvancedPasswordGenerator from '@/components/AdvancedPasswordGenerator.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter,\r\n    SecurityDashboard,\r\n    AdvancedPasswordGenerator\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      selectAllBatchAdd: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      currentAccount: {},\r\n      passwordVisibility: {\r\n        generated: false,\r\n        new: false,\r\n        confirm: false,\r\n        newAccount: false,\r\n        batchPassword: false\r\n      },\r\n      viewMode: 'table',\r\n      filterText: '',\r\n      accountFilterText: '',\r\n      statusFilter: 'all',\r\n      expiryFilter: 'all',\r\n      policyFilter: 'all',\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      },\r\n\r\n      // 添加账号弹窗\r\n      addAccountModal: {\r\n        show: false,\r\n        username: '',\r\n        password: '',\r\n        isDefault: false,\r\n        policyId: 1\r\n      },\r\n\r\n      // 批量添加账号弹窗\r\n      batchAddAccountModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        username: '',\r\n        password: '',\r\n        role: 'admin',\r\n        setAsDefault: false,\r\n        useSamePassword: true,\r\n        policyId: 1\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    // 获取策略名称\r\n    getPolicyName() {\r\n      return (policyId) => {\r\n        if (!policyId) return '无';\r\n        const policy = this.policies.find(p => p.id === policyId);\r\n        return policy ? policy.name : '无';\r\n      };\r\n    },\r\n\r\n    // 获取策略颜色类\r\n    getPolicyColorClass() {\r\n      return (policyId) => {\r\n        if (!policyId) return 'bg-gray-100 text-gray-800';\r\n        \r\n        // 根据策略ID返回对应的颜色类\r\n        const colorMap = {\r\n          1: 'bg-red-100 text-red-800',    // 高强度策略\r\n          2: 'bg-blue-100 text-blue-800',  // 标准策略\r\n          3: 'bg-purple-100 text-purple-800' // 紧急策略\r\n        };\r\n        \r\n        return colorMap[policyId] || 'bg-gray-100 text-gray-800';\r\n      };\r\n    },\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    },\r\n\r\n    // 过滤后的主机列表\r\n    filteredHosts() {\r\n      return this.hosts.filter(host => {\r\n        // 文本过滤\r\n        const textMatch = this.filterText === '' ||\r\n          host.name.toLowerCase().includes(this.filterText.toLowerCase()) ||\r\n          host.ip.includes(this.filterText);\r\n\r\n        // 状态过滤\r\n        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;\r\n\r\n        return textMatch && statusMatch;\r\n      });\r\n    },\r\n\r\n    // 获取所有账号（扁平化处理）\r\n    getAllAccounts() {\r\n      // 为每个账号添加主机引用\r\n      const accounts = [];\r\n      this.filteredHosts.forEach(host => {\r\n        host.accounts.forEach(account => {\r\n          accounts.push({\r\n            ...account,\r\n            host: host\r\n          });\r\n        });\r\n      });\r\n      return accounts;\r\n    },\r\n\r\n    // 筛选后的账号\r\n    filteredAccounts() {\r\n      return this.getAllAccounts.filter(account => {\r\n        // 账号名称筛选\r\n        const accountMatch = this.accountFilterText === '' ||\r\n          account.username.toLowerCase().includes(this.accountFilterText.toLowerCase());\r\n\r\n        // 密码过期筛选\r\n        let expiryMatch = true;\r\n        if (this.expiryFilter !== 'all') {\r\n          const expiryStatus = this.isPasswordExpired(account).status;\r\n          if (this.expiryFilter === 'expired') {\r\n            expiryMatch = expiryStatus === 'expired';\r\n          } else if (this.expiryFilter === 'expiring-soon') {\r\n            expiryMatch = expiryStatus === 'danger' || expiryStatus === 'warning';\r\n          } else if (this.expiryFilter === 'valid') {\r\n            expiryMatch = expiryStatus === 'normal';\r\n          }\r\n        }\r\n\r\n        // 策略筛选\r\n        let policyMatch = true;\r\n        if (this.policyFilter !== 'all') {\r\n          if (this.policyFilter === 'none') {\r\n            policyMatch = !account.policyId;\r\n          } else {\r\n            policyMatch = account.policyId === parseInt(this.policyFilter);\r\n          }\r\n        }\r\n\r\n        return accountMatch && expiryMatch && policyMatch;\r\n      });\r\n    },\r\n\r\n    // 分组后的账号列表\r\n    groupedAccounts() {\r\n      // 按主机ID分组\r\n      const groups = {};\r\n      this.filteredAccounts.forEach(account => {\r\n        const hostId = account.host.id;\r\n        if (!groups[hostId]) {\r\n          groups[hostId] = {\r\n            hostId: hostId,\r\n            hostName: account.host.name,\r\n            hostIp: account.host.ip,\r\n            host: account.host,\r\n            accounts: []\r\n          };\r\n        }\r\n        groups[hostId].accounts.push(account);\r\n      });\r\n\r\n      // 转换为数组\r\n      return Object.values(groups);\r\n    },\r\n\r\n    selectedBatchAddHostsCount() {\r\n      return Object.values(this.batchAddAccountModal.selectedHosts).filter(Boolean).length\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    toggleSelectAllBatchAdd(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchAddAccountModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host, account) {\r\n      this.currentHost = host\r\n      this.currentAccount = account\r\n      this.changePasswordModal.show = true\r\n      this.changePasswordModal.generatedPassword = this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword(policy) {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policyObj ? policyObj.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      if (this.changePasswordModal && !policy) {\r\n        this.changePasswordModal.generatedPassword = password\r\n      }\r\n\r\n      return password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto'\r\n          ? this.changePasswordModal.generatedPassword\r\n          : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          accountId: this.currentAccount.id,\r\n          password: password,\r\n          policyId: this.changePasswordModal.policyId\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的 ${this.currentAccount.username} 账号密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取所选策略\r\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取紧急策略\r\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    togglePasswordVisibility(hostId) {\r\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]\r\n    },\r\n\r\n    isPasswordExpired(account) {\r\n      if (!account.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }\r\n\r\n      // 解析过期时间\r\n      const expiryDate = new Date(account.passwordExpiryDate)\r\n      const now = new Date()\r\n\r\n      // 如果已过期\r\n      if (expiryDate < now) {\r\n        return {\r\n          status: 'expired',\r\n          days: 0,\r\n          text: '已过期'\r\n        }\r\n      }\r\n\r\n      // 计算剩余天数和小时数\r\n      const diffTime = expiryDate - now\r\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))\r\n      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n\r\n      // 根据剩余时间确定状态\r\n      let status = 'normal'\r\n      if (diffDays < 7) {\r\n        status = 'danger'  // 少于7天\r\n      } else if (diffDays < 14) {\r\n        status = 'warning' // 少于14天\r\n      }\r\n\r\n      // 格式化显示文本\r\n      let text = ''\r\n      if (diffDays > 0) {\r\n        text += `${diffDays}天`\r\n      }\r\n      if (diffHours > 0 || diffDays === 0) {\r\n        text += `${diffHours}小时`\r\n      }\r\n\r\n      return { status, days: diffDays, text: `剩余${text}` }\r\n    },\r\n\r\n    openAddAccountModal(host) {\r\n      this.currentHost = host\r\n      this.addAccountModal.show = true\r\n    },\r\n\r\n    async addAccount() {\r\n      if (!this.addAccountModal.username || !this.addAccountModal.password) {\r\n        alert('请填写完整的账号信息！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('addHostAccount', {\r\n          hostId: this.currentHost.id,\r\n          username: this.addAccountModal.username,\r\n          password: this.addAccountModal.password,\r\n          policyId: this.addAccountModal.policyId,\r\n          isDefault: this.addAccountModal.isDefault\r\n        })\r\n\r\n        this.addAccountModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为主机 ${this.currentHost.name} 添加账号！`)\r\n      } catch (error) {\r\n        console.error('添加账号失败', error)\r\n        alert('添加账号失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    generatePasswordForNewAccount() {\r\n      this.addAccountModal.password = this.generatePassword()\r\n    },\r\n\r\n    // 复制密码到剪贴板\r\n    copyPassword(account) {\r\n      // 创建一个临时输入框\r\n      const tempInput = document.createElement('input');\r\n      tempInput.value = account.password;\r\n      document.body.appendChild(tempInput);\r\n      tempInput.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(tempInput);\r\n\r\n      // 显示提示\r\n      alert(`已复制 ${account.host.name} 的 ${account.username} 账号密码到剪贴板！`);\r\n    },\r\n\r\n    // 导出密码数据为CSV\r\n    exportPasswordsToCSV() {\r\n      // 准备CSV标题行\r\n      const headers = ['主机名', 'IP地址', '账号', '密码', '最后修改时间', '过期时间', '状态', '策略'];\r\n      const csvRows = [headers];\r\n      \r\n      // 添加数据行\r\n      this.filteredAccounts.forEach(account => {\r\n        const row = [\r\n          account.host.name,\r\n          account.host.ip,\r\n          account.username,\r\n          account.password,\r\n          account.lastPasswordChange || '-',\r\n          account.passwordExpiryDate || '-',\r\n          account.host.status,\r\n          this.getPolicyName(account.policyId)\r\n        ];\r\n        csvRows.push(row);\r\n      });\r\n      \r\n      // 转换为CSV格式\r\n      const csvContent = csvRows.map(row => row.map(cell => \r\n        `\"${String(cell).replace(/\"/g, '\"\"')}\"`).join(',')).join('\\n');\r\n      \r\n      // 创建下载链接\r\n      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n      const url = URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.setAttribute('href', url);\r\n      link.setAttribute('download', `密码数据_${new Date().toISOString().slice(0,10)}.csv`);\r\n      link.style.visibility = 'hidden';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    },\r\n\r\n    generatePasswordForBatchAccount() {\r\n      this.batchAddAccountModal.password = this.generatePassword()\r\n    },\r\n\r\n    async batchAddAccounts() {\r\n      if (!this.batchAddAccountModal.username) {\r\n        alert('请填写账号名称！')\r\n        return\r\n      }\r\n\r\n      const selectedHostIds = Object.entries(this.batchAddAccountModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('batchAddAccounts', {\r\n          hostIds: selectedHostIds,\r\n          username: this.batchAddAccountModal.username,\r\n          password: this.batchAddAccountModal.useSamePassword ? this.batchAddAccountModal.password : null,\r\n          role: this.batchAddAccountModal.role,\r\n          isDefault: this.batchAddAccountModal.setAsDefault,\r\n          policyId: this.batchAddAccountModal.policyId\r\n        })\r\n\r\n        this.batchAddAccountModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机添加账号！`)\r\n      } catch (error) {\r\n        console.error('批量添加账号失败', error)\r\n        alert('批量添加账号失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    openBatchAddAccountModal() {\r\n      this.batchAddAccountModal.show = true\r\n      this.batchAddAccountModal.selectedHosts = {}\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchAddAccountModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 生成初始密码\r\n      this.generatePasswordForBatchAccount()\r\n    },\r\n\r\n    printPasswordData() {\r\n      // 使用纯JavaScript方式创建打印内容，避免Vue模板编译问题\r\n      const printWindow = window.open(\"\", \"_blank\");\r\n      if (!printWindow) {\r\n        alert(\"无法打开打印窗口，请允许弹出窗口\");\r\n        return;\r\n      }\r\n      \r\n      const doc = printWindow.document;\r\n      \r\n      // 清空文档\r\n      doc.open();\r\n      doc.write(\"<!DOCTYPE html>\");\r\n      doc.write(\"<html>\");\r\n      doc.write(\"<head>\");\r\n      doc.write(\"<title>主机账号密码报表</title>\");\r\n      \r\n      // 添加样式\r\n      doc.write(\"<style>\");\r\n      doc.write(\"body { font-family: Arial, sans-serif; }\");\r\n      doc.write(\"table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\");\r\n      doc.write(\"th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\");\r\n      doc.write(\"th { background-color: #f2f2f2; }\");\r\n      doc.write(\".print-header { text-align: center; margin-bottom: 20px; }\");\r\n      doc.write(\".print-footer { text-align: center; font-size: 12px; margin-top: 30px; }\");\r\n      doc.write(\".status-normal { color: green; }\");\r\n      doc.write(\".status-warning { color: orange; }\");\r\n      doc.write(\".status-error { color: red; }\");\r\n      doc.write(\"@page { margin: 1cm; }\");\r\n      doc.write(\"</style>\");\r\n      doc.write(\"</head>\");\r\n      doc.write(\"<body>\");\r\n      \r\n      // 添加标题\r\n      doc.write(\"<div class=\\\"print-header\\\">\");\r\n      doc.write(\"<h1>主机账号密码报表</h1>\");\r\n      doc.write(\"<p>生成时间: \" + new Date().toLocaleString(\"zh-CN\") + \"</p>\");\r\n      doc.write(\"</div>\");\r\n      \r\n      // 添加表格\r\n      doc.write(\"<table>\");\r\n      \r\n      // 表头\r\n      doc.write(\"<thead><tr>\");\r\n      [\"主机名\", \"IP地址\", \"账号\", \"最后密码修改时间\", \"密码过期时间\", \"状态\", \"策略\"].forEach(header => {\r\n        doc.write(\"<th>\" + header + \"</th>\");\r\n      });\r\n      doc.write(\"</tr></thead>\");\r\n      \r\n      // 表格内容\r\n      doc.write(\"<tbody>\");\r\n      this.filteredAccounts.forEach(account => {\r\n        const statusClass = \"status-\" + account.host.status;\r\n        doc.write(\"<tr>\");\r\n        doc.write(\"<td>\" + account.host.name + \"</td>\");\r\n        doc.write(\"<td>\" + account.host.ip + \"</td>\");\r\n        doc.write(\"<td>\" + account.username + (account.isDefault ? \" (默认)\" : \"\") + \"</td>\");\r\n        doc.write(\"<td>\" + (account.lastPasswordChange || \"-\") + \"</td>\");\r\n        doc.write(\"<td>\" + (account.passwordExpiryDate || \"-\") + \"</td>\");\r\n        doc.write(\"<td class=\\\"\" + statusClass + \"\\\">\" + account.host.status + \"</td>\");\r\n        doc.write(\"<td>\" + this.getPolicyName(account.policyId) + \"</td>\");\r\n        doc.write(\"</tr>\");\r\n      });\r\n      doc.write(\"</tbody>\");\r\n      doc.write(\"</table>\");\r\n      \r\n      // 添加页脚\r\n      doc.write(\"<div class=\\\"print-footer\\\">\");\r\n      doc.write(\"<p>注意：本文档包含敏感信息，请妥善保管</p>\");\r\n      doc.write(\"</div>\");\r\n      \r\n      // 添加自动打印脚本\r\n      doc.write(\"<\" + \"script\" + \">\");\r\n      doc.write(\"window.onload = function() { window.print(); }\");\r\n      doc.write(\"</\" + \"script\" + \">\");\r\n      \r\n      doc.write(\"</body>\");\r\n      doc.write(\"</html>\");\r\n      doc.close();\r\n    },\r\n\r\n    // 检查密码是否符合策略要求\r\n    isPasswordCompliant(account) {\r\n      if (!account.policyId) return { compliant: true, text: '无策略' };\r\n      \r\n      const password = account.password;\r\n      const policy = this.policies.find(p => p.id === account.policyId);\r\n      \r\n      if (!policy) return { compliant: true, text: '无策略' };\r\n      \r\n      const checks = [];\r\n      \r\n      // 检查长度\r\n      if (password.length < policy.minLength) {\r\n        checks.push(`密码长度不足 ${policy.minLength} 位`);\r\n      }\r\n      \r\n      // 检查大写字母\r\n      if (policy.requireUppercase && !/[A-Z]/.test(password)) {\r\n        checks.push('缺少大写字母');\r\n      }\r\n      \r\n      // 检查小写字母\r\n      if (policy.requireLowercase && !/[a-z]/.test(password)) {\r\n        checks.push('缺少小写字母');\r\n      }\r\n      \r\n      // 检查数字\r\n      if (policy.requireNumbers && !/[0-9]/.test(password)) {\r\n        checks.push('缺少数字');\r\n      }\r\n      \r\n      // 检查特殊字符\r\n      if (policy.requireSpecial && !/[^A-Za-z0-9]/.test(password)) {\r\n        checks.push('缺少特殊字符');\r\n      }\r\n      \r\n      // 检查是否包含用户名\r\n      if (policy.forbidUsername && password.toLowerCase().includes(account.username.toLowerCase())) {\r\n        checks.push('密码包含用户名');\r\n      }\r\n      \r\n      return {\r\n        compliant: checks.length === 0,\r\n        text: checks.length === 0 ? '符合策略' : `不符合: ${checks.join(', ')}`,\r\n        details: checks\r\n      };\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": ";;EACOA,KAAK,EAAC;AAAW;;EAKfA,KAAK,EAAC;AAAgG;;EACpGA,KAAK,EAAC;AAAmD;;EAEvDA,KAAK,EAAC;AAAsB;;EA4B5BA,KAAK,EAAC;AAAmC;;EAEvCA,KAAK,EAAC;AAA+D;;EAsBrEA,KAAK,EAAC;AAAU;;EAOdA,KAAK,EAAC;AAAsE;;EAM9EA,KAAK,EAAC;AAAU;;EAOdA,KAAK,EAAC;AAAsE;;EAM9EA,KAAK,EAAC;AAAY;oBAvFjC;;EAAAC,GAAA;EA6HqCD,KAAK,EAAC;;;EAEhCA,KAAK,EAAC;AAAiE;;EACrEA,KAAK,EAAC;AAAuB;;EACvBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EAEzBA,KAAK,EAAC;AAAgB;;EAgBtBA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAAY;;EAEjBE,KAAK,EAAC,KAAK;EAACF,KAAK,EAAC;;;EA+BnBA,KAAK,EAAC;AAAmC;;EAMtCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EAEpBA,KAAK,EAAC;AAAgC;;EAI9CA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAmC;;EAxMjEC,GAAA;EA0MoBD,KAAK,EAAC;;;EAKRA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAlNrDC,GAAA;EA4NoBD,KAAK,EAAC;;;EA5N1BC,GAAA;EA+NoFD,KAAK,EAAC;;;EAKxEA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAW;oBAtOxC;oBAAA;;EAgPuBA,KAAK,EAAC;AAAM;;EAQjBA,KAAK,EAAC;AAA6B;;EAGnCA,KAAK,EAAC;AAA6B;;EAKnCA,KAAK,EAAC;AAAmD;;EACtDA,KAAK,EAAC;AAAgB;oBAjQ3C;oBAAA;;EAAAC,GAAA;AAAA;;EAoRgBE,OAAO,EAAC,GAAG;EAACH,KAAK,EAAC;;;EACfA,KAAK,EAAC;AAAe;;EAWxBA,KAAK,EAAC;AAAsD;;EAE/DA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAiD;;EACrDA,KAAK,EAAC;AAAgC;;EAGnCA,KAAK,EAAC;AAAmC;;EAC1CA,KAAK,EAAC;AAAuB;;EAOjCA,KAAK,EAAC;AAAW;;EAGbA,KAAK,EAAC;AAAkD;;EACtDA,KAAK,EAAC;AAAgC;;EACnCA,KAAK,EAAC;AAAmC;;EArTjEC,GAAA;EAuToBD,KAAK,EAAC;;oBAvT1B;;EAoUmBA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAmB;oBAtU9C;oBAAA;;EA8UuBA,KAAK,EAAC;AAAM;;EAUhBA,KAAK,EAAC;AAAgC;;EAGlCA,KAAK,EAAC;AAAe;;EA3V5CC,GAAA;EAwWsBD,KAAK,EAAC;;;EAKPA,KAAK,EAAC;AAAiB;;EAW7BA,KAAK,EAAC;AAA0B;oBAxX/C;;EAuYWA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAmJ;;EACvJA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAAuF;;EAI5FA,KAAK,EAAC;AAA6C;;EAClDA,KAAK,EAAC;AAA0C;;EASxDA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAwB;;EA1Z3CC,GAAA;EAmbwDD,KAAK,EAAC;;;EACjDA,KAAK,EAAC;AAAwF;;EASzFA,KAAK,EAAC;AAAM;;EACjBA,KAAK,EAAC;AAAkG;;EAGpGA,KAAK,EAAC;AAAU;oBAjcjC;;EAqdiBA,KAAK,EAAC;AAAU;oBArdjC;;EAAAC,GAAA;EAweyCD,KAAK,EAAC;;;EAxe/CC,GAAA;AAAA;;EAqfWD,KAAK,EAAC;AAAgB;;EAgBtBA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAiC;;EAMzCA,KAAK,EAAC;AAAiB;;EAKvBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAA0D;;EAOlEA,KAAK,EAAC;AAAiB;qBA3hBlC;;EAoiBWA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAA2B;;EAQjCA,KAAK,EAAC;AAAmB;qBA7iBtC;;EA2jBWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAY;qBA1kB7B;;EAmlBWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EA1lB1CC,GAAA;EAgmBmED,KAAK,EAAC;;;EAC1DA,KAAK,EAAC;AAAwB;;EAalCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;qBAzoB7B;;EAkpBWA,KAAK,EAAC;AAAY;;EAkBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;qBA9qB7B;;EAurBWA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;EAUlBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAuC;;EAC3CA,KAAK,EAAC;AAAM;;EAMZA,KAAK,EAAC;AAAM;;EAUZA,KAAK,EAAC;AAAwB;;EAC1BA,KAAK,EAAC;AAA0B;;EAMpCA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAgB;;EAvvBvCC,GAAA;EAuwB2DD,KAAK,EAAC;;;EAEhDA,KAAK,EAAC;AAAmB;qBAzwB1C;;EA2xBWA,KAAK,EAAC;AAAiB;qBA3xBlC;;EAoyBWA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAW;;;;;;;;;;uBAryB5BI,mBAAA,CA+yBM,OA/yBNC,UA+yBM,GA9yBJC,mBAAA,WAAc,EACdC,YAAA,CAAoCC,4BAAA;IAAhBC,KAAK,EAAEC,IAAA,CAAAD;EAAK,oCAEhCH,mBAAA,WAAc,EACdK,mBAAA,CAmHM,OAnHNC,UAmHM,GAlHJD,mBAAA,CAiHM,OAjHNE,UAiHM,GAhHJP,mBAAA,YAAe,EACfK,mBAAA,CAyBM,OAzBNG,UAyBM,GAxBJH,mBAAA,CAKS;IAJPX,KAAK,EAAC,oSAAoS;IACzSe,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,kBAAA,IAAAD,QAAA,CAAAC,kBAAA,IAAAF,IAAA,CAAkB;MAC1BV,YAAA,CAA0Ea,4BAAA;IAAtDC,IAAI,EAAE,+BAA+B;IAAErB,KAAK,EAAC;kCACjEW,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAKS;IAJPX,KAAK,EAAC,ySAAyS;IAC9Se,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAI,oBAAA,IAAAJ,QAAA,CAAAI,oBAAA,IAAAL,IAAA,CAAoB;MAC5BV,YAAA,CAAyDa,4BAAA;IAArCC,IAAI,EAAE,cAAc;IAAErB,KAAK,EAAC;kCAChDW,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAKS;IAJPX,KAAK,EAAC,mTAAmT;IACxTe,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAK,mBAAA,IAAAL,QAAA,CAAAK,mBAAA,IAAAN,IAAA,CAAmB;MAC3BV,YAAA,CAAgEa,4BAAA;IAA5CC,IAAI,EAAE,qBAAqB;IAAErB,KAAK,EAAC;kCACvDW,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAKS;IAJPX,KAAK,EAAC,8SAA8S;IACnTe,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAM,wBAAA,IAAAN,QAAA,CAAAM,wBAAA,IAAAP,IAAA,CAAwB;MAChCV,YAAA,CAA2Da,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAErB,KAAK,EAAC;kCAClDW,mBAAA,CAAmB,cAAb,QAAM,qB,KAIhBL,mBAAA,aAAgB,EAChBK,mBAAA,CAkFM,OAlFNc,UAkFM,GAjFJnB,mBAAA,UAAa,EACbK,mBAAA,CAmBM,OAnBNe,UAmBM,GAlBJf,mBAAA,CAQS;IAPPX,KAAK,EAzCnB2B,eAAA,EAyCoB,2FAA2F,EACzFC,KAAA,CAAAC,QAAQ,e;IAGfd,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAChBtB,YAAA,CAA6Da,4BAAA;IAAzCC,IAAI,EAAE,gBAAgB;IAAErB,KAAK,EAAC;kCA9ChE+B,gBAAA,CA8C2E,MAE/D,G,kBACApB,mBAAA,CAQS;IAPPX,KAAK,EAlDnB2B,eAAA,EAkDoB,2FAA2F,EACzFC,KAAA,CAAAC,QAAQ,c;IAGfd,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAChBtB,YAAA,CAAgEa,4BAAA;IAA5CC,IAAI,EAAE,mBAAmB;IAAErB,KAAK,EAAC;kCAvDnE+B,gBAAA,CAuD8E,MAElE,G,oBAGFzB,mBAAA,SAAY,EACZK,mBAAA,CAUM,OAVNqB,UAUM,G,gBATJrB,mBAAA,CAKE;IAJAsB,IAAI,EAAC,MAAM;IA/DzB,uBAAAjB,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAgEuBF,KAAA,CAAAM,UAAU,GAAAJ,MAAA;IACnBK,WAAW,EAAC,SAAS;IACrBnC,KAAK,EAAC;iDAFG4B,KAAA,CAAAM,UAAU,E,GAIrBvB,mBAAA,CAEM,OAFNyB,UAEM,GADJ7B,YAAA,CAAqEa,4BAAA;IAAjDC,IAAI,EAAE,iBAAiB;IAAErB,KAAK,EAAC;UAIvDM,mBAAA,UAAa,EACbK,mBAAA,CAUM,OAVN0B,UAUM,G,gBATJ1B,mBAAA,CAKE;IAJAsB,IAAI,EAAC,MAAM;IA5EzB,uBAAAjB,MAAA,QAAAA,MAAA,MAAAc,MAAA,IA6EuBF,KAAA,CAAAU,iBAAiB,GAAAR,MAAA;IAC1BK,WAAW,EAAC,SAAS;IACrBnC,KAAK,EAAC;iDAFG4B,KAAA,CAAAU,iBAAiB,E,GAI5B3B,mBAAA,CAEM,OAFN4B,WAEM,GADJhC,YAAA,CAAmEa,4BAAA;IAA/CC,IAAI,EAAE,eAAe;IAAErB,KAAK,EAAC;UAIrDM,mBAAA,YAAe,EACfK,mBAAA,CA+BM,OA/BN6B,WA+BM,G,gBA9BJ7B,mBAAA,CAQS;IAhGrB,uBAAAK,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAyFuBF,KAAA,CAAAa,YAAY,GAAAX,MAAA;IACrB9B,KAAK,EAAC;kCAENW,mBAAA,CAAiC;IAAzB+B,KAAK,EAAC;EAAK,GAAC,MAAI,qBACxB/B,mBAAA,CAAkC;IAA1B+B,KAAK,EAAC;EAAQ,GAAC,IAAE,qBACzB/B,mBAAA,CAAmC;IAA3B+B,KAAK,EAAC;EAAS,GAAC,IAAE,qBAC1B/B,mBAAA,CAAiC;IAAzB+B,KAAK,EAAC;EAAO,GAAC,IAAE,oB,2CANfd,KAAA,CAAAa,YAAY,E,mBASvB9B,mBAAA,CAQS;IA1GrB,uBAAAK,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAmGuBF,KAAA,CAAAe,YAAY,GAAAb,MAAA;IACrB9B,KAAK,EAAC;kCAENW,mBAAA,CAAiC;IAAzB+B,KAAK,EAAC;EAAK,GAAC,MAAI,qBACxB/B,mBAAA,CAAoC;IAA5B+B,KAAK,EAAC;EAAS,GAAC,KAAG,qBAC3B/B,mBAAA,CAA2C;IAAnC+B,KAAK,EAAC;EAAe,GAAC,MAAI,qBAClC/B,mBAAA,CAAmC;IAA3B+B,KAAK,EAAC;EAAO,GAAC,MAAI,oB,2CANjBd,KAAA,CAAAe,YAAY,E,mBASvBhC,mBAAA,CASS;IArHrB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA6GuBF,KAAA,CAAAgB,YAAY,GAAAd,MAAA;IACrB9B,KAAK,EAAC;kCAENW,mBAAA,CAAiC;IAAzB+B,KAAK,EAAC;EAAK,GAAC,MAAI,uB,kBACxBtC,mBAAA,CAESyC,SAAA,QAnHvBC,WAAA,CAiHuCpC,IAAA,CAAAqC,QAAQ,EAAlBC,MAAM;yBAArB5C,mBAAA,CAES;MAF2BH,GAAG,EAAE+C,MAAM,CAACC,EAAE;MAAGP,KAAK,EAAEM,MAAM,CAACC;wBAC9DD,MAAM,CAACE,IAAI,wBAlH9BC,WAAA;8DAoHcxC,mBAAA,CAAiC;IAAzB+B,KAAK,EAAC;EAAM,GAAC,KAAG,qB,0CAPfd,KAAA,CAAAgB,YAAY,E,WAc/BtC,mBAAA,UAAa,EACbA,mBAAA,UAAa,EACFsB,KAAA,CAAAC,QAAQ,gB,cAAnBzB,mBAAA,CAgKM,OAhKNgD,WAgKM,GA/JJ9C,mBAAA,eAAkB,EAClBK,mBAAA,CAmBM,OAnBN0C,WAmBM,GAlBJ1C,mBAAA,CAGM,OAHN2C,WAGM,G,4BAnIdvB,gBAAA,CAgI2C,MAC9B,IAAApB,mBAAA,CAA8D,QAA9D4C,WAA8D,EAAAC,gBAAA,CAAjCtC,QAAA,CAAAuC,gBAAgB,CAACC,MAAM,kB,4BAjIjE3B,gBAAA,CAiI2E,UAC9D,IAAApB,mBAAA,CAA4D,QAA5DgD,WAA4D,EAAAH,gBAAA,CAA/BtC,QAAA,CAAA0C,cAAc,CAACF,MAAM,kB,4BAlI/D3B,gBAAA,CAkIyE,MACjE,G,GACApB,mBAAA,CAaM,OAbNkD,WAaM,GAZJlD,mBAAA,CAKS;IAJPX,KAAK,EAAC,sNAAsN;IAC3Ne,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEC,QAAA,CAAA4C,oBAAA,IAAA5C,QAAA,CAAA4C,oBAAA,IAAA7C,IAAA,CAAoB;MAC5BV,YAAA,CAAiEa,4BAAA;IAA7CC,IAAI,EAAE,sBAAsB;IAAErB,KAAK,EAAC;kCAxIpE+B,gBAAA,CAwI6E,MAEnE,G,GACApB,mBAAA,CAKS;IAJPX,KAAK,EAAC,sNAAsN;IAC3Ne,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEC,QAAA,CAAA6C,iBAAA,IAAA7C,QAAA,CAAA6C,iBAAA,IAAA9C,IAAA,CAAiB;MACzBV,YAAA,CAA2Da,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAErB,KAAK,EAAC;kCA9I9D+B,gBAAA,CA8IuE,MAE7D,G,OAIJpB,mBAAA,CAwIQ,SAxIRqD,WAwIQ,GAvINrD,mBAAA,CAgCQ,SAhCRsD,WAgCQ,GA/BNtD,mBAAA,CA8BK,aA7BHA,mBAAA,CAIK,MAJLuD,WAIK,GAHH3D,YAAA,CAEiB4D,yBAAA;IA1J/BC,UAAA,EAwJuCxC,KAAA,CAAAyC,SAAS;IAxJhD,wB,sCAwJuCzC,KAAA,CAAAyC,SAAS,GAAAvC,MAAA,GAAsBZ,QAAA,CAAAoD,eAAe;;IAxJrFC,OAAA,EAAAC,QAAA,CAwJuF,MAEzExD,MAAA,SAAAA,MAAA,QA1Jde,gBAAA,CAwJuF,OAEzE,E;IA1Jd0C,CAAA;0FA4JY9D,mBAAA,CAEK;IAFDT,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,QAEvG,sB,4BACAW,mBAAA,CAEK;IAFDT,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAW,mBAAA,CAEK;IAFDT,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,YAEvG,sB,4BACAW,mBAAA,CAEK;IAFDT,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,UAEvG,sB,4BACAW,mBAAA,CAEK;IAFDT,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAW,mBAAA,CAEK;IAFDT,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAW,mBAAA,CAEK;IAFDT,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAW,mBAAA,CAEK;IAFDT,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,qB,KAGJW,mBAAA,CAqGQ,SArGR+D,WAqGQ,GApGNpE,mBAAA,aAAgB,G,kBAChBF,mBAAA,CAyFWyC,SAAA,QAjRrBC,WAAA,CAwLwC5B,QAAA,CAAAyD,eAAe,EAA5BC,SAAS;yBAxLpCxE,mBAAA,CAAAyC,SAAA;MAAA5C,GAAA,EAwL+D2E,SAAS,CAACC;QAC7DvE,mBAAA,SAAY,G,kBACZF,mBAAA,CAsFKyC,SAAA,QAhRjBC,WAAA,CA0LkD8B,SAAS,CAACE,QAAQ,EA1LpE,CA0LwBC,OAAO,EAAEC,YAAY;2BAAjC5E,mBAAA,CAsFK;QAtFsDH,GAAG,EAAE8E,OAAO,CAAC9B,EAAE;QACvEjD,KAAK,EA3LpB2B,eAAA;UAAA,cA2LsCqD,YAAY;UAAA;QAAA;UACpCrE,mBAAA,CAMK,MANLsE,WAMK,GALHtE,mBAAA,CAIM,OAJNuE,WAIM,GAHJ3E,YAAA,CAEiB4D,yBAAA;QAhMnCC,UAAA,EA8L2CW,OAAO,CAACI,IAAI,CAACC,QAAQ;QA9LhE,uBAAAtD,MAAA,IA8L2CiD,OAAO,CAACI,IAAI,CAACC,QAAQ,GAAAtD,MAAA;QAAE9B,KAAK,EAAC;;QA9LxEuE,OAAA,EAAAC,QAAA,CA+LoB,MAA2E,CAA3E7D,mBAAA,CAA2E,QAA3E0E,WAA2E,EAAA7B,gBAAA,CAA3BuB,OAAO,CAACI,IAAI,CAACjC,IAAI,iB;QA/LrFuB,CAAA;sFAmMc9D,mBAAA,CAEK,MAFL2E,WAEK,GADH3E,mBAAA,CAA8D,OAA9D4E,WAA8D,EAAA/B,gBAAA,CAAxBuB,OAAO,CAACI,IAAI,CAACK,EAAE,iB,GAEvD7E,mBAAA,CAQK,MARL8E,WAQK,GAPH9E,mBAAA,CAMM,OANN+E,WAMM,GALJ/E,mBAAA,CAA6E,QAA7EgF,WAA6E,EAAAnC,gBAAA,CAA1BuB,OAAO,CAACa,QAAQ,kBACvDb,OAAO,CAACc,SAAS,I,cAA7BzF,mBAAA,CAGO,QAHP0F,WAGO,EAFqG,MAE5G,KA5MlBxF,mBAAA,e,KA+McK,mBAAA,CAEK,MAFLoF,WAEK,GADHpF,mBAAA,CAAgF,OAAhFqF,WAAgF,EAAAxC,gBAAA,CAA1CuB,OAAO,CAACkB,kBAAkB,wB,GAElEtF,mBAAA,CAiBK,MAjBLuF,WAiBK,GAhBHvF,mBAAA,CAeM;QAfAX,KAAK,EAnN3B2B,eAAA;;qCAmNiLT,QAAA,CAAAiF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM,iBAAiBlF,QAAA,CAAAiF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM;2CAAoElF,QAAA,CAAAiF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM;uCAAgElF,QAAA,CAAAiF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM;;UAnN1crE,gBAAA,CAAAyB,gBAAA,CAyNqBtC,QAAA,CAAAiF,iBAAiB,CAACpB,OAAO,EAAEsB,IAAI,IAAG,GACrC,iBACQnF,QAAA,CAAAiF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM,kBAAkBlF,QAAA,CAAAiF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM,iB,cAD5FhG,mBAAA,CAIO,QAJPkG,WAIO,GADL/F,YAAA,CAA6Da,4BAAA;QAAzCC,IAAI,EAAE;MAA+B,G,KAE1CH,QAAA,CAAAiF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM,kB,cAAlDhG,mBAAA,CAEO,QAFPmG,WAEO,GADLhG,YAAA,CAA2Da,4BAAA;QAAvCC,IAAI,EAAE;MAA6B,G,KAhO3Ef,mBAAA,e,oBAoOcK,mBAAA,CAmBK,MAnBL6F,WAmBK,GAlBH7F,mBAAA,CAiBM,OAjBN8F,WAiBM,GAhBJ9F,mBAAA,CAIM,OAJN+F,WAIM,GAHJ/F,mBAAA,CAEkG;QAF1FsB,IAAI,EAAEL,KAAA,CAAA+E,kBAAkB,CAAC5B,OAAO,CAAC9B,EAAE;QAA0BP,KAAK,EAAEqC,OAAO,CAAC6B,QAAQ;QAC1FC,QAAQ,EAAR,EAAQ;QACR7G,KAAK,EAAC;8BAzO5B8G,WAAA,E,GA2OkBnG,mBAAA,CAIS;QAJAI,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAA6F,wBAAwB,CAAChC,OAAO,CAAC9B,EAAE;QACjDjD,KAAK,EAAC;UACNO,YAAA,CACoBa,4BAAA;QADAC,IAAI,UAAUO,KAAA,CAAA+E,kBAAkB,CAAC5B,OAAO,CAAC9B,EAAE;QAC7DjD,KAAK,EAAC;yDA9O5BgH,WAAA,G,+BAgPkB5G,mBAAA,CAKM,OALN6G,WAKM,GAJJ1G,YAAA,CAGEa,4BAAA;QAFCC,IAAI,UAAUH,QAAA,CAAAgG,mBAAmB,CAACnC,OAAO,EAAEoC,SAAS;QACpDnH,KAAK,EAnP5B2B,eAAA,CAmP8BT,QAAA,CAAAgG,mBAAmB,CAACnC,OAAO,EAAEoC,SAAS;2EAHrBjG,QAAA,CAAAgG,mBAAmB,CAACnC,OAAO,EAAEsB,IAAI,E,OAQlE1F,mBAAA,CAEK,MAFLyG,WAEK,GADH7G,YAAA,CAA2C8G,sBAAA;QAA7BpF,IAAI,EAAE8C,OAAO,CAACI,IAAI,CAACiB;2CAEnCzF,mBAAA,CAIK,MAJL2G,WAIK,GAHH3G,mBAAA,CAEM;QAFAX,KAAK,EA5P3B2B,eAAA,sEA4PkGT,QAAA,CAAAqG,mBAAmB,CAACxC,OAAO,CAACyC,QAAQ;0BACjHtG,QAAA,CAAAuG,aAAa,CAAC1C,OAAO,CAACyC,QAAQ,yB,GAGrC7G,mBAAA,CAeK,MAfL+G,WAeK,GAdH/G,mBAAA,CAaM,OAbNgH,WAaM,GAZJhH,mBAAA,CAKS;QAJPX,KAAK,EAAC,0NAA0N;QAC/Ne,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAA0G,uBAAuB,CAAC7C,OAAO,CAACI,IAAI,EAAEJ,OAAO;UACrDxE,YAAA,CAAyDa,4BAAA;QAArCC,IAAI,EAAE,cAAc;QAAErB,KAAK,EAAC;sCArQpE+B,gBAAA,CAqQ6E,QAE3D,G,iBAvQlB8F,WAAA,GAwQkBlH,mBAAA,CAKS;QAJPX,KAAK,EAAC,sNAAsN;QAC3Ne,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAA4G,YAAY,CAAC/C,OAAO;UAC5BxE,YAAA,CAA0Da,4BAAA;QAAtCC,IAAI,EAAE,eAAe;QAAErB,KAAK,EAAC;sCA3QrE+B,gBAAA,CA2Q8E,MAE5D,G,iBA7QlBgG,WAAA,E;;kCAkRUzH,mBAAA,WAAc,EACJY,QAAA,CAAAuC,gBAAgB,CAACC,MAAM,U,cAAjCtD,mBAAA,CAOK,MA1Rf4H,WAAA,GAoRYrH,mBAAA,CAKK,MALLsH,WAKK,GAJHtH,mBAAA,CAGM,OAHNuH,WAGM,GAFJ3H,YAAA,CAAqEa,4BAAA;IAAjDC,IAAI,EAAE,iBAAiB;IAAErB,KAAK,EAAC;kCACnDW,mBAAA,CAAkB,WAAf,aAAW,qB,SAvR9BL,mBAAA,e,wBAgSIF,mBAAA,CAkGMyC,SAAA;IAlYV5C,GAAA;EAAA,IA+RIK,mBAAA,UAAa,EACbK,mBAAA,CAkGM,OAlGNwH,WAkGM,I,kBAjGJ/H,mBAAA,CAgGMyC,SAAA,QAjYZC,WAAA,CAiS0B5B,QAAA,CAAAkH,aAAa,EAArBjD,IAAI;yBAAhB/E,mBAAA,CAgGM;MAhG8BH,GAAG,EAAEkF,IAAI,CAAClC,EAAE;MAAEjD,KAAK,EAAC;QACtDW,mBAAA,CA8FM,OA9FN0H,WA8FM,GA7FJ/H,mBAAA,UAAa,EACbK,mBAAA,CASM,OATN2H,WASM,GARJ3H,mBAAA,CAMM,OANN4H,WAMM,GALJhI,YAAA,CAAuD4D,yBAAA;MAtSrEC,UAAA,EAsSuCe,IAAI,CAACC,QAAQ;MAtSpD,uBAAAtD,MAAA,IAsSuCqD,IAAI,CAACC,QAAQ,GAAAtD,MAAA;MAAE9B,KAAK,EAAC;oEAC9CW,mBAAA,CAGM,cAFJA,mBAAA,CAAkE,MAAlE6H,WAAkE,EAAAhF,gBAAA,CAAjB2B,IAAI,CAACjC,IAAI,kBAC1DvC,mBAAA,CAAkD,KAAlD8H,WAAkD,EAAAjF,gBAAA,CAAd2B,IAAI,CAACK,EAAE,iB,KAG/CjF,YAAA,CAAmC8G,sBAAA;MAArBpF,IAAI,EAAEkD,IAAI,CAACiB;yCAG3B9F,mBAAA,UAAa,EACbK,mBAAA,CAqEM,OArEN+H,WAqEM,I,kBApEJtI,mBAAA,CAmEMyC,SAAA,QApXlBC,WAAA,CAiTmCqC,IAAI,CAACL,QAAQ,EAAxBC,OAAO;2BAAnB3E,mBAAA,CAmEM;QAnEiCH,GAAG,EAAE8E,OAAO,CAAC9B,EAAE;QAAEjD,KAAK,EAjTzE2B,eAAA,EAiT0E,uCAAuC;UAAA,gCACzDoD,OAAO,CAACc;QAAS;UAC3DlF,mBAAA,CAcM,OAdNgI,WAcM,GAbJhI,mBAAA,CAMM,OANNiI,WAMM,GALJjI,mBAAA,CAA6E,QAA7EkI,WAA6E,EAAArF,gBAAA,CAA1BuB,OAAO,CAACa,QAAQ,kBACvDb,OAAO,CAACc,SAAS,I,cAA7BzF,mBAAA,CAGO,QAHP0I,WAGO,EAFqG,MAE5G,KAzTlBxI,mBAAA,e,GA2TgBK,mBAAA,CAKS;QAJPX,KAAK,EAAC,wNAAwN;QAC7Ne,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAA0G,uBAAuB,CAACzC,IAAI,EAAEJ,OAAO;UAC7CxE,YAAA,CAAyDa,4BAAA;QAArCC,IAAI,EAAE,cAAc;QAAErB,KAAK,EAAC;sCA9TlE+B,gBAAA,CA8T2E,QAE3D,G,iBAhUhBgH,WAAA,E,GAmUczI,mBAAA,UAAa,EACbK,mBAAA,CAiBM,OAjBNqI,WAiBM,G,4BAhBJrI,mBAAA,CAA4D;QAAvDX,KAAK,EAAC;MAAwC,GAAC,IAAE,sBACtDW,mBAAA,CAcM,OAdNsI,WAcM,GAbJtI,mBAAA,CACkG;QAD1FsB,IAAI,EAAEL,KAAA,CAAA+E,kBAAkB,CAAC5B,OAAO,CAAC9B,EAAE;QAA0BP,KAAK,EAAEqC,OAAO,CAAC6B,QAAQ;QAAEC,QAAQ,EAAR,EAAQ;QACpG7G,KAAK,EAAC;8BAxU1BkJ,WAAA,GAyUkBvI,mBAAA,CAIS;QAJAI,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAA6F,wBAAwB,CAAChC,OAAO,CAAC9B,EAAE;QACjDjD,KAAK,EAAC;UACNO,YAAA,CACoBa,4BAAA;QADAC,IAAI,UAAUO,KAAA,CAAA+E,kBAAkB,CAAC5B,OAAO,CAAC9B,EAAE;QAC7DjD,KAAK,EAAC;yDA5U5BmJ,WAAA,G,+BA8UkB/I,mBAAA,CAKM,OALNgJ,WAKM,GAJJ7I,YAAA,CAGEa,4BAAA;QAFCC,IAAI,UAAUH,QAAA,CAAAgG,mBAAmB,CAACnC,OAAO,EAAEoC,SAAS;QACpDnH,KAAK,EAjV5B2B,eAAA,CAiV8BT,QAAA,CAAAgG,mBAAmB,CAACnC,OAAO,EAAEoC,SAAS;2EAHrBjG,QAAA,CAAAgG,mBAAmB,CAACnC,OAAO,EAAEsB,IAAI,E,OASlE/F,mBAAA,YAAe,EACfK,mBAAA,CA2BM,OA3BN0I,WA2BM,GA1BJ1I,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAwD;QAAnDX,KAAK,EAAC;MAAgC,GAAC,QAAM,sBAClDW,mBAAA,CAAwE,OAAxE2I,WAAwE,EAAA9F,gBAAA,CAA1CuB,OAAO,CAACkB,kBAAkB,wB,GAE1DtF,mBAAA,CAeM,c,4BAdJA,mBAAA,CAAsD;QAAjDX,KAAK,EAAC;MAAgC,GAAC,MAAI,sBAChDW,mBAAA,CAYM;QAZAX,KAAK,EA/V7B2B,eAAA;;qCA+VqLT,QAAA,CAAAiF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM,iBAAiBlF,QAAA,CAAAiF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM;2CAAsElF,QAAA,CAAAiF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM;uCAAkElF,QAAA,CAAAiF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM;;UA/VldrE,gBAAA,CAAAyB,gBAAA,CAqWuBtC,QAAA,CAAAiF,iBAAiB,CAACpB,OAAO,EAAEsB,IAAI,IAAG,GACrC,iBACQnF,QAAA,CAAAiF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM,kBAAkBlF,QAAA,CAAAiF,iBAAiB,CAACpB,OAAO,EAAEqB,MAAM,iB,cAD5FhG,mBAAA,CAIO,QAJPmJ,WAIO,GADLhJ,YAAA,CAA6Da,4BAAA;QAAzCC,IAAI,EAAE;MAA+B,G,KAzW/Ef,mBAAA,e,oBA6WgBK,mBAAA,CAKM,OALN6I,WAKM,G,4BAJJ7I,mBAAA,CAAsD;QAAjDX,KAAK,EAAC;MAAgC,GAAC,MAAI,sBAChDW,mBAAA,CAEM;QAFAX,KAAK,EA/W7B2B,eAAA,sEA+WoGT,QAAA,CAAAqG,mBAAmB,CAACxC,OAAO,CAACyC,QAAQ;0BACjHtG,QAAA,CAAAuG,aAAa,CAAC1C,OAAO,CAACyC,QAAQ,yB;sCAO3ClH,mBAAA,YAAe,EACfK,mBAAA,CAOM,OAPN8I,WAOM,GANJ9I,mBAAA,CAKS;MAJPX,KAAK,EAAC,sNAAsN;MAC3Ne,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAAwI,mBAAmB,CAACvE,IAAI;QAChC5E,YAAA,CAA0Da,4BAAA;MAAtCC,IAAI,EAAE,eAAe;MAAErB,KAAK,EAAC;oCA5X/D+B,gBAAA,CA4XwE,QAE5D,G,iBA9XZ4H,WAAA,E;sFAoYIrJ,mBAAA,YAAe,EACfC,YAAA,CA4HYqJ,oBAAA;IAjgBhBxF,UAAA,EAqYwBxC,KAAA,CAAAiI,mBAAmB,CAACC,IAAI;IArYhD,uBAAA9I,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAqYwBF,KAAA,CAAAiI,mBAAmB,CAACC,IAAI,GAAAhI,MAAA;IAAEiI,KAAK,EAAC,MAAM;IAACC,IAAI,EAAC,IAAI;IAAEC,SAAO,EAAE/I,QAAA,CAAAgJ,cAAc;IAAGC,OAAO,EAAEvI,KAAA,CAAAwI;;IArY7G7F,OAAA,EAAAC,QAAA,CAsYM,MAAe,CAAflE,mBAAA,YAAe,EACfK,mBAAA,CAcM,OAdN0J,WAcM,GAbJ1J,mBAAA,CAYM,OAZN2J,WAYM,GAXJ3J,mBAAA,CAUM,OAVN4J,WAUM,GATJ5J,mBAAA,CAEM,OAFN6J,WAEM,GADJjK,YAAA,CAAwFa,4BAAA;MAApEC,IAAI,EAAE,iBAAiB;MAAErB,KAAK,EAAC;UAErDW,mBAAA,CAKM,cAJJA,mBAAA,CAAmF,MAAnF8J,WAAmF,EAAAjH,gBAAA,CAAxB5B,KAAA,CAAA8I,WAAW,CAACxH,IAAI,kBAC3EvC,mBAAA,CAEM,OAFNgK,WAEM,GADJhK,mBAAA,CAAiC,cAAA6C,gBAAA,CAAxB5B,KAAA,CAAA8I,WAAW,CAAClF,EAAE,kB,4BAhZvCzD,gBAAA,CAgZiD,KAAG,IAAApB,mBAAA,CAA8C,cAAxC,MAAI,GAAA6C,gBAAA,CAAG5B,KAAA,CAAAgJ,cAAc,CAAChF,QAAQ,iB,WAOlFtF,mBAAA,cAAiB,EACjBK,mBAAA,CAwBM,OAxBNkK,WAwBM,G,8BAvBJlK,mBAAA,CAA6F;MAAtFX,KAAK,EAAC;IAAiE,GAAC,QAAM,sBACrFW,mBAAA,CAqBM,OArBNmK,WAqBM,GApBJnK,mBAAA,CASS;MARNI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAAiI,mBAAmB,CAACkB,MAAM;MAClC/K,KAAK,EA7ZjB2B,eAAA,EA6ZkB,+GAA+G,EAC7GC,KAAA,CAAAiI,mBAAmB,CAACkB,MAAM,c;QAIlCxK,YAAA,CAA2Da,4BAAA;MAAvCC,IAAI,EAAE,gBAAgB;MAAErB,KAAK,EAAC;sCAClDW,mBAAA,CAAqC;MAA/BX,KAAK,EAAC;IAAa,GAAC,MAAI,qB,kBAEhCW,mBAAA,CASS;MARNI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAAiI,mBAAmB,CAACkB,MAAM;MAClC/K,KAAK,EAvajB2B,eAAA,EAuakB,+GAA+G,EAC7GC,KAAA,CAAAiI,mBAAmB,CAACkB,MAAM,gB;QAIlCxK,YAAA,CAA0Da,4BAAA;MAAtCC,IAAI,EAAE,eAAe;MAAErB,KAAK,EAAC;sCACjDW,mBAAA,CAAqC;MAA/BX,KAAK,EAAC;IAAa,GAAC,MAAI,qB,sBAKpCM,mBAAA,YAAe,EACJsB,KAAA,CAAAiI,mBAAmB,CAACkB,MAAM,e,cAArC3K,mBAAA,CAOM,OAPN4K,WAOM,GANJrK,mBAAA,CAKM,OALNsK,WAKM,GAJJ1K,YAAA,CAGE2K,oCAAA;MAFC,iBAAe,EAAExK,IAAA,CAAAyK,2BAA2B;MAC5CC,mBAAkB,EAAE1K,IAAA,CAAA0K;8FAM3BhL,mBAAA,CAsDMyC,SAAA;MAnfZ5C,GAAA;IAAA,IA4bMK,mBAAA,YAAe,EACfK,mBAAA,CAsDM,OAtDN0K,WAsDM,GArDJ1K,mBAAA,CAoDM,OApDN2K,WAoDM,GAnDJ3K,mBAAA,CAkBM,c,8BAjBJA,mBAAA,CAA0F;MAAnFX,KAAK,EAAC;IAAiE,GAAC,KAAG,sBAClFW,mBAAA,CAeM,OAfN4K,WAeM,G,gBAdJ5K,mBAAA,CAME;MALCsB,IAAI,EAAEL,KAAA,CAAA+E,kBAAkB,CAAC6E,GAAG;MAnc7C,uBAAAxK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAocyBF,KAAA,CAAAiI,mBAAmB,CAAC4B,WAAW,GAAA3J,MAAA;MACxC9B,KAAK,EAAC,mMAAmM;MACzMmC,WAAW,EAAC,OAAO;MAClBuJ,OAAK,EAAA1K,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEP,IAAA,CAAAiL,gBAAA,IAAAjL,IAAA,CAAAiL,gBAAA,IAAA1K,IAAA,CAAgB;6CAvcxC2K,WAAA,I,iBAocyBhK,KAAA,CAAAiI,mBAAmB,CAAC4B,WAAW,E,GAK1C9K,mBAAA,CAMS;MALNI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAA+E,kBAAkB,CAAC6E,GAAG,IAAI5J,KAAA,CAAA+E,kBAAkB,CAAC6E,GAAG;MACxDvJ,IAAI,EAAC,QAAQ;MACbjC,KAAK,EAAC;QAENO,YAAA,CAAmFa,4BAAA;MAA/DC,IAAI,UAAUO,KAAA,CAAA+E,kBAAkB,CAAC6E,GAAG;6CAK9D7K,mBAAA,CAyBM,c,8BAxBJA,mBAAA,CAA2F;MAApFX,KAAK,EAAC;IAAiE,GAAC,MAAI,sBACnFW,mBAAA,CAkBM,OAlBNkL,WAkBM,G,gBAjBJlL,mBAAA,CASE;MARCsB,IAAI,EAAEL,KAAA,CAAA+E,kBAAkB,CAACmF,OAAO;MAvdjD,uBAAA9K,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAwdyBF,KAAA,CAAAiI,mBAAmB,CAACkC,eAAe,GAAAjK,MAAA;MAC5C9B,KAAK,EAzdrB2B,eAAA,EAydsB,8JAA8J,EAC5JT,QAAA,CAAA8K,gBAAgB,G;MAGxB7J,WAAW,EAAC,SAAS;MACpBuJ,OAAK,EAAA1K,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEP,IAAA,CAAAiL,gBAAA,IAAAjL,IAAA,CAAAiL,gBAAA,IAAA1K,IAAA,CAAgB;oDA9dxCgL,WAAA,I,iBAwdyBrK,KAAA,CAAAiI,mBAAmB,CAACkC,eAAe,E,GAQ9CpL,mBAAA,CAMS;MALNI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAA+E,kBAAkB,CAACmF,OAAO,IAAIlK,KAAA,CAAA+E,kBAAkB,CAACmF,OAAO;MAChE7J,IAAI,EAAC,QAAQ;MACbjC,KAAK,EAAC;QAENO,YAAA,CAAuFa,4BAAA;MAAnEC,IAAI,UAAUO,KAAA,CAAA+E,kBAAkB,CAACmF,OAAO;2CAGrD5K,QAAA,CAAA8K,gBAAgB,I,cAA3B5L,mBAAA,CAGM,OAHN8L,WAGM,GAFJ3L,YAAA,CAAwEa,4BAAA;MAApDC,IAAI,EAAE,6BAA6B;MAAErB,KAAK,EAAC;sCAze7E+B,gBAAA,CAyesF,cAE1E,G,KA3eZzB,mBAAA,e,GA8eUA,mBAAA,aAAgB,EACLsB,KAAA,CAAAiI,mBAAmB,CAAC4B,WAAW,I,cAA1CrL,mBAAA,CAEM,OAjfhB+L,WAAA,GAgfY5L,YAAA,CAAqE6L,gCAAA;MAA7CxF,QAAQ,EAAEhF,KAAA,CAAAiI,mBAAmB,CAAC4B;+CAhflEnL,mBAAA,e,uDAqfMK,mBAAA,CAWM,OAXN0L,WAWM,G,8BAVJ1L,mBAAA,CAA8C;MAAzCX,KAAK,EAAC;IAAwB,GAAC,MAAI,sBACxCO,YAAA,CAEiB4D,yBAAA;MAzfzBC,UAAA,EAufiCxC,KAAA,CAAAiI,mBAAmB,CAACyC,kBAAkB;MAvfvE,uBAAAtL,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAufiCF,KAAA,CAAAiI,mBAAmB,CAACyC,kBAAkB,GAAAxK,MAAA;;MAvfvEyC,OAAA,EAAAC,QAAA,CAwfU,MAA8BxD,MAAA,UAAAA,MAAA,SAA9BL,mBAAA,CAA8B;QAAxBX,KAAK,EAAC;MAAM,GAAC,MAAI,oB;MAxfjCyE,CAAA;uCA0fQlE,YAAA,CAEiB4D,yBAAA;MA5fzBC,UAAA,EA0fiCxC,KAAA,CAAAiI,mBAAmB,CAAC0C,WAAW;MA1fhE,uBAAAvL,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA0fiCF,KAAA,CAAAiI,mBAAmB,CAAC0C,WAAW,GAAAzK,MAAA;;MA1fhEyC,OAAA,EAAAC,QAAA,CA2fU,MAAgCxD,MAAA,UAAAA,MAAA,SAAhCL,mBAAA,CAAgC;QAA1BX,KAAK,EAAC;MAAM,GAAC,QAAM,oB;MA3fnCyE,CAAA;uCA6fQlE,YAAA,CAEiB4D,yBAAA;MA/fzBC,UAAA,EA6fiCxC,KAAA,CAAAiI,mBAAmB,CAAC2C,QAAQ;MA7f7D,uBAAAxL,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA6fiCF,KAAA,CAAAiI,mBAAmB,CAAC2C,QAAQ,GAAA1K,MAAA;;MA7f7DyC,OAAA,EAAAC,QAAA,CA8fU,MAAgCxD,MAAA,UAAAA,MAAA,SAAhCL,mBAAA,CAAgC;QAA1BX,KAAK,EAAC;MAAM,GAAC,QAAM,oB;MA9fnCyE,CAAA;;IAAAA,CAAA;6DAmgBInE,mBAAA,YAAe,EACfC,YAAA,CAkDYqJ,oBAAA;IAtjBhBxF,UAAA,EAogBwBxC,KAAA,CAAA6K,eAAe,CAAC3C,IAAI;IApgB5C,uBAAA9I,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAogBwBF,KAAA,CAAA6K,eAAe,CAAC3C,IAAI,GAAAhI,MAAA;IAAEiI,KAAK,EAAC,MAAM;IAAEE,SAAO,EAAE/I,QAAA,CAAAwL,UAAU;IAAGvC,OAAO,EAAEvI,KAAA,CAAAwI;;IApgB3F7F,OAAA,EAAAC,QAAA,CAqgBM,MAMM,CANN7D,mBAAA,CAMM,OANNgM,WAMM,G,8BALJhM,mBAAA,CAAwC;MAAnCX,KAAK,EAAC;IAAkB,GAAC,MAAI,sBAClCW,mBAAA,CAGM,OAHNiM,WAGM,GAFJjM,mBAAA,CAAuE,c,8BAAlEA,mBAAA,CAAqC;MAA/BX,KAAK,EAAC;IAAa,GAAC,MAAI,sBAxgB7C+B,gBAAA,CAwgBoD,GAAC,GAAAyB,gBAAA,CAAG5B,KAAA,CAAA8I,WAAW,CAACxH,IAAI,iB,GAC9DvC,mBAAA,CAAsE,c,8BAAjEA,mBAAA,CAAsC;MAAhCX,KAAK,EAAC;IAAa,GAAC,OAAK,sBAzgB9C+B,gBAAA,CAygBqD,GAAC,GAAAyB,gBAAA,CAAG5B,KAAA,CAAA8I,WAAW,CAAClF,EAAE,iB,OAIjE7E,mBAAA,CAGM,OAHNkM,WAGM,G,8BAFJlM,mBAAA,CAAsC;MAA/BX,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BW,mBAAA,CAAkG;MAA3FsB,IAAI,EAAC,MAAM;MA/gB1B,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA+gBoCF,KAAA,CAAA6K,eAAe,CAAC7G,QAAQ,GAAA9D,MAAA;MAAE9B,KAAK,EAAC,cAAc;MAACmC,WAAW,EAAC;mDAA3DP,KAAA,CAAA6K,eAAe,CAAC7G,QAAQ,E,KAGtDjF,mBAAA,CAOM,OAPNmM,WAOM,G,8BANJnM,mBAAA,CAAwC;MAAjCX,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCW,mBAAA,CAIM,OAJNoM,WAIM,G,gBAHJpM,mBAAA,CACoI;MAD7HsB,IAAI,EAAC,UAAU;MArhBhC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAqhB0CF,KAAA,CAAA6K,eAAe,CAAC5G,SAAS,GAAA/D,MAAA;MACvD9B,KAAK,EAAC;uDADwB4B,KAAA,CAAA6K,eAAe,CAAC5G,SAAS,E,iCAEzDlF,mBAAA,CAAsG;MAA/FX,KAAK,EAAC;IAAgF,4B,KAIjGW,mBAAA,CAOM,OAPNqM,WAOM,G,8BANJrM,mBAAA,CAAsC;MAA/BX,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BW,mBAAA,CAIS;MAjiBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA6hByBF,KAAA,CAAA6K,eAAe,CAACjF,QAAQ,GAAA1F,MAAA;MAAE9B,KAAK,EAAC,aAAa;MAAEiN,QAAM,EAAAjM,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEZ,QAAA,CAAAgM,6BAA6B;2BACnG9M,mBAAA,CAESyC,SAAA,QAhiBnBC,WAAA,CA8hBmCpC,IAAA,CAAAqC,QAAQ,EAAlBC,MAAM;2BAArB5C,mBAAA,CAES;QAF2BH,GAAG,EAAE+C,MAAM,CAACC,EAAE;QAAGP,KAAK,EAAEM,MAAM,CAACC;0BAC9DD,MAAM,CAACE,IAAI,IAAG,UAAQ,GAAAM,gBAAA,CAAGR,MAAM,CAACmK,SAAS,IAAG,QAAM,GAAA3J,gBAAA,CAAGR,MAAM,CAACoK,UAAU,IAAG,KAC9E,uBAhiBVC,YAAA;6FA6hByBzL,KAAA,CAAA6K,eAAe,CAACjF,QAAQ,E,KAO3C7G,mBAAA,CAiBM,OAjBN2M,YAiBM,GAhBJ3M,mBAAA,CAOM,OAPN4M,YAOM,G,8BANJ5M,mBAAA,CAAuC;MAAhCX,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BW,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEZ,QAAA,CAAAgM,6BAA6B;MAAIjL,IAAI,EAAC,QAAQ;MAC5DjC,KAAK,EAAC;QACNO,YAAA,CAA8Da,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAErB,KAAK,EAAC;sCAziBjE+B,gBAAA,CAyiB0E,QAEhE,G,KAEFpB,mBAAA,CAOM,OAPN6M,YAOM,G,gBANJ7M,mBAAA,CAC2C;MADnCsB,IAAI,EAAEL,KAAA,CAAA+E,kBAAkB,CAAC8G,UAAU;MA9iBrD,uBAAAzM,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8iBsFF,KAAA,CAAA6K,eAAe,CAAC7F,QAAQ,GAAA9E,MAAA;MAAE+E,QAAQ,EAAR,EAAQ;MAC5G7G,KAAK,EAAC;4BA/iBlB0N,YAAA,I,iBA8iBsF9L,KAAA,CAAA6K,eAAe,CAAC7F,QAAQ,E,GAEpGjG,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAA+E,kBAAkB,CAAC8G,UAAU,IAAI7L,KAAA,CAAA+E,kBAAkB,CAAC8G,UAAU;MAAExL,IAAI,EAAC,QAAQ;MAC3FjC,KAAK,EAAC;QACNO,YAAA,CAA0Ga,4BAAA;MAAtFC,IAAI,UAAUO,KAAA,CAAA+E,kBAAkB,CAAC8G,UAAU;MAAyBzN,KAAK,EAAC;;IAljB1GyE,CAAA;6DAwjBInE,mBAAA,cAAiB,EACjBC,YAAA,CAiEYqJ,oBAAA;IA1nBhBxF,UAAA,EAyjBwBxC,KAAA,CAAA+L,gBAAgB,CAAC7D,IAAI;IAzjB7C,uBAAA9I,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAyjBwBF,KAAA,CAAA+L,gBAAgB,CAAC7D,IAAI,GAAAhI,MAAA;IAAEiI,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAACC,IAAI,EAAC,IAAI;IACpFC,SAAO,EAAE/I,QAAA,CAAA0M,oBAAoB;IAAGzD,OAAO,EAAEvI,KAAA,CAAAwI;;IA1jBhD7F,OAAA,EAAAC,QAAA,CA2jBM,MAaM,CAbN7D,mBAAA,CAaM,OAbNkN,YAaM,G,8BAZJlN,mBAAA,CAAwC;MAAjCX,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCW,mBAAA,CAIM,OAJNmN,YAIM,GAHJvN,YAAA,CAEiB4D,yBAAA;MAhkB3BC,UAAA,EA8jBmCxC,KAAA,CAAAmM,cAAc;MA9jBjD,wB,sCA8jBmCnM,KAAA,CAAAmM,cAAc,GAAAjM,MAAA,GAAsBZ,QAAA,CAAA8M,oBAAoB;;MA9jB3FzJ,OAAA,EAAAC,QAAA,CA8jB6F,MAEnFxD,MAAA,UAAAA,MAAA,SAhkBVe,gBAAA,CA8jB6F,MAEnF,E;MAhkBV0C,CAAA;gEAkkBQ9D,mBAAA,CAIM,OAJNsN,YAIM,I,kBAHJ7N,mBAAA,CAEiByC,SAAA,QArkB3BC,WAAA,CAmkByCpC,IAAA,CAAAD,KAAK,EAAb0E,IAAI;2BAA3B+I,YAAA,CAEiB/J,yBAAA;QAFsBlE,GAAG,EAAEkF,IAAI,CAAClC,EAAE;QAnkB7DmB,UAAA,EAmkBwExC,KAAA,CAAA+L,gBAAgB,CAACQ,aAAa,CAAChJ,IAAI,CAAClC,EAAE;QAnkB9G,uBAAAnB,MAAA,IAmkBwEF,KAAA,CAAA+L,gBAAgB,CAACQ,aAAa,CAAChJ,IAAI,CAAClC,EAAE,IAAAnB;;QAnkB9GyC,OAAA,EAAAC,QAAA,CAokBY,MAAe,CApkB3BzC,gBAAA,CAAAyB,gBAAA,CAokBe2B,IAAI,CAACjC,IAAI,IAAG,IAAE,GAAAM,gBAAA,CAAG2B,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QArkBVf,CAAA;;sCAukBQ9D,mBAAA,CAAyD,KAAzDyN,YAAyD,EAApC,MAAI,GAAA5K,gBAAA,CAAGtC,QAAA,CAAAmN,kBAAkB,IAAG,MAAI,gB,GAGvD1N,mBAAA,CAOM,OAPN2N,YAOM,G,8BANJ3N,mBAAA,CAAsC;MAA/BX,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BW,mBAAA,CAIS;MAhlBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4kByBF,KAAA,CAAA+L,gBAAgB,CAACnG,QAAQ,GAAA1F,MAAA;MAAE9B,KAAK,EAAC;2BAChDI,mBAAA,CAESyC,SAAA,QA/kBnBC,WAAA,CA6kBmCpC,IAAA,CAAAqC,QAAQ,EAAlBC,MAAM;2BAArB5C,mBAAA,CAES;QAF2BH,GAAG,EAAE+C,MAAM,CAACC,EAAE;QAAGP,KAAK,EAAEM,MAAM,CAACC;0BAC9DD,MAAM,CAACE,IAAI,IAAG,UAAQ,GAAAM,gBAAA,CAAGR,MAAM,CAACmK,SAAS,IAAG,QAAM,GAAA3J,gBAAA,CAAGR,MAAM,CAACoK,UAAU,IAAG,KAC9E,uBA/kBVmB,YAAA;6EA4kByB3M,KAAA,CAAA+L,gBAAgB,CAACnG,QAAQ,E,KAO5C7G,mBAAA,CAyBM,OAzBN6N,YAyBM,G,8BAxBJ7N,mBAAA,CAAsC;MAA/BX,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BW,mBAAA,CASM,OATN8N,YASM,GARJ9N,mBAAA,CAGQ,SAHR+N,YAGQ,G,gBAFN/N,mBAAA,CAA4F;MAArFsB,IAAI,EAAC,OAAO;MAvlB/B,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAulByCF,KAAA,CAAA+L,gBAAgB,CAACgB,aAAa,GAAA7M,MAAA;MAAEY,KAAK,EAAC,WAAW;MAAC1C,KAAK,EAAC;oDAAxD4B,KAAA,CAAA+L,gBAAgB,CAACgB,aAAa,E,iCAC3DhO,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHRiO,YAGQ,G,gBAFNjO,mBAAA,CAA4F;MAArFsB,IAAI,EAAC,OAAO;MA3lB/B,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA2lByCF,KAAA,CAAA+L,gBAAgB,CAACgB,aAAa,GAAA7M,MAAA;MAAEY,KAAK,EAAC,WAAW;MAAC1C,KAAK,EAAC;oDAAxD4B,KAAA,CAAA+L,gBAAgB,CAACgB,aAAa,E,iCAC3DhO,mBAAA,CAAiB,cAAX,MAAI,qB,KAIHiB,KAAA,CAAA+L,gBAAgB,CAACgB,aAAa,oB,cAAzCvO,mBAAA,CAWM,OAXNyO,YAWM,GAVJlO,mBAAA,CASM,OATNmO,YASM,GARJnO,mBAAA,CAGM,c,8BAFJA,mBAAA,CAAoC;MAA7BX,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BW,mBAAA,CAAiF;MAA1EsB,IAAI,EAAC,MAAM;MApmBhC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAomB0CF,KAAA,CAAA+L,gBAAgB,CAACoB,aAAa,GAAAjN,MAAA;MAAE9B,KAAK,EAAC;mDAAtC4B,KAAA,CAAA+L,gBAAgB,CAACoB,aAAa,E,KAE5DpO,mBAAA,CAGM,c,8BAFJA,mBAAA,CAAoC;MAA7BX,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BW,mBAAA,CAAiF;MAA1EsB,IAAI,EAAC,MAAM;MAxmBhC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAwmB0CF,KAAA,CAAA+L,gBAAgB,CAACqB,aAAa,GAAAlN,MAAA;MAAE9B,KAAK,EAAC;mDAAtC4B,KAAA,CAAA+L,gBAAgB,CAACqB,aAAa,E,WAxmBxE1O,mBAAA,e,GA8mBMK,mBAAA,CAWM,OAXNsO,YAWM,G,8BAVJtO,mBAAA,CAAsC;MAA/BX,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BO,YAAA,CAEiB4D,yBAAA;MAlnBzBC,UAAA,EAgnBiCxC,KAAA,CAAA+L,gBAAgB,CAACuB,YAAY;MAhnB9D,uBAAAlO,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAgnBiCF,KAAA,CAAA+L,gBAAgB,CAACuB,YAAY,GAAApN,MAAA;;MAhnB9DyC,OAAA,EAAAC,QAAA,CAgnBgE,MAExDxD,MAAA,UAAAA,MAAA,SAlnBRe,gBAAA,CAgnBgE,YAExD,E;MAlnBR0C,CAAA;uCAmnBQlE,YAAA,CAEiB4D,yBAAA;MArnBzBC,UAAA,EAmnBiCxC,KAAA,CAAA+L,gBAAgB,CAACwB,WAAW;MAnnB7D,uBAAAnO,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAmnBiCF,KAAA,CAAA+L,gBAAgB,CAACwB,WAAW,GAAArN,MAAA;;MAnnB7DyC,OAAA,EAAAC,QAAA,CAmnB+D,MAEvDxD,MAAA,UAAAA,MAAA,SArnBRe,gBAAA,CAmnB+D,UAEvD,E;MArnBR0C,CAAA;uCAsnBQlE,YAAA,CAEiB4D,yBAAA;MAxnBzBC,UAAA,EAsnBiCxC,KAAA,CAAA+L,gBAAgB,CAACyB,gBAAgB;MAtnBlE,uBAAApO,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAsnBiCF,KAAA,CAAA+L,gBAAgB,CAACyB,gBAAgB,GAAAtN,MAAA;;MAtnBlEyC,OAAA,EAAAC,QAAA,CAsnBoE,MAE5DxD,MAAA,UAAAA,MAAA,SAxnBRe,gBAAA,CAsnBoE,aAE5D,E;MAxnBR0C,CAAA;;IAAAA,CAAA;6DA4nBInE,mBAAA,cAAiB,EACjBC,YAAA,CA8BYqJ,oBAAA;IA3pBhBxF,UAAA,EA6nBwBxC,KAAA,CAAAyN,eAAe,CAACvF,IAAI;IA7nB5C,uBAAA9I,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA6nBwBF,KAAA,CAAAyN,eAAe,CAACvF,IAAI,GAAAhI,MAAA;IAAEiI,KAAK,EAAC,UAAU;IAAC,cAAY,EAAC,MAAM;IAAEE,SAAO,EAAE/I,QAAA,CAAAoO,gBAAgB;IACtGnF,OAAO,EAAEvI,KAAA,CAAAwI;;IA9nBhB7F,OAAA,EAAAC,QAAA,CA+nBM,MAQM,CARN7D,mBAAA,CAQM,OARN4O,YAQM,G,8BAPJ5O,mBAAA,CAAwC;MAAjCX,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCW,mBAAA,CAKM,OALN6O,YAKM,I,kBAJJpP,mBAAA,CAGiByC,SAAA,QAroB3BC,WAAA,CAkoByC5B,QAAA,CAAAuO,iBAAiB,EAAzBtK,IAAI;2BAA3B+I,YAAA,CAGiB/J,yBAAA;QAHkClE,GAAG,EAAEkF,IAAI,CAAClC,EAAE;QAloBzEmB,UAAA,EAmoBqBxC,KAAA,CAAAyN,eAAe,CAAClB,aAAa,CAAChJ,IAAI,CAAClC,EAAE;QAnoB1D,uBAAAnB,MAAA,IAmoBqBF,KAAA,CAAAyN,eAAe,CAAClB,aAAa,CAAChJ,IAAI,CAAClC,EAAE,IAAAnB;;QAnoB1DyC,OAAA,EAAAC,QAAA,CAooBY,MAAe,CApoB3BzC,gBAAA,CAAAyB,gBAAA,CAooBe2B,IAAI,CAACjC,IAAI,IAAG,IAAE,GAAAM,gBAAA,CAAG2B,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QAroBVf,CAAA;;wCAyoBM9D,mBAAA,CAOM,OAPN+O,YAOM,G,8BANJ/O,mBAAA,CAAwC;MAAjCX,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCW,mBAAA,CAIS;MA/oBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA2oByBF,KAAA,CAAAyN,eAAe,CAAC7H,QAAQ,GAAA1F,MAAA;MAAE9B,KAAK,EAAC;2BAC/CI,mBAAA,CAESyC,SAAA,QA9oBnBC,WAAA,CA4oBmCpC,IAAA,CAAAqC,QAAQ,EAAlBC,MAAM;2BAArB5C,mBAAA,CAES;QAF2BH,GAAG,EAAE+C,MAAM,CAACC,EAAE;QAAGP,KAAK,EAAEM,MAAM,CAACC;0BAC9DD,MAAM,CAACE,IAAI,wBA7oB1ByM,YAAA;6EA2oByB/N,KAAA,CAAAyN,eAAe,CAAC7H,QAAQ,E,KAO3C7G,mBAAA,CAQM,OARNiP,YAQM,G,8BAPJjP,mBAAA,CAAsC;MAA/BX,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BO,YAAA,CAEiB4D,yBAAA;MAtpBzBC,UAAA,EAopBiCxC,KAAA,CAAAyN,eAAe,CAACQ,iBAAiB;MAppBlE,uBAAA7O,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAopBiCF,KAAA,CAAAyN,eAAe,CAACQ,iBAAiB,GAAA/N,MAAA;;MAppBlEyC,OAAA,EAAAC,QAAA,CAopBoE,MAE5DxD,MAAA,UAAAA,MAAA,SAtpBRe,gBAAA,CAopBoE,eAE5D,E;MAtpBR0C,CAAA;uCAupBQlE,YAAA,CAEiB4D,yBAAA;MAzpBzBC,UAAA,EAupBiCxC,KAAA,CAAAyN,eAAe,CAACS,iBAAiB;MAvpBlE,uBAAA9O,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAupBiCF,KAAA,CAAAyN,eAAe,CAACS,iBAAiB,GAAAhO,MAAA;;MAvpBlEyC,OAAA,EAAAC,QAAA,CAupBoE,MAE5DxD,MAAA,UAAAA,MAAA,SAzpBRe,gBAAA,CAupBoE,aAE5D,E;MAzpBR0C,CAAA;;IAAAA,CAAA;6DA6pBInE,mBAAA,cAAiB,EACjBC,YAAA,CAyCYqJ,oBAAA;IAvsBhBxF,UAAA,EA8pBwBxC,KAAA,CAAAmO,mBAAmB,CAACjG,IAAI;IA9pBhD,uBAAA9I,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8pBwBF,KAAA,CAAAmO,mBAAmB,CAACjG,IAAI,GAAAhI,MAAA;IAAEiI,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAAC1I,IAAI,EAAC,sBAAsB;IAAC2O,MAAM,EAAN,EAAM;IAChH/F,SAAO,EAAE/I,QAAA,CAAA+O,cAAc;IAAG9F,OAAO,EAAEvI,KAAA,CAAAwI;;IA/pB1C7F,OAAA,EAAAC,QAAA,CAgqBM,MAEM,C,8BAFN7D,mBAAA,CAEM;MAFDX,KAAK,EAAC;IAA4C,IACrDW,mBAAA,CAA+C,WAA5C,0CAAwC,E,sBAG7CA,mBAAA,CAQM,OARNuP,YAQM,G,8BAPJvP,mBAAA,CAAwC;MAAjCX,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCW,mBAAA,CAKM,OALNwP,YAKM,I,kBAJJ/P,mBAAA,CAGiByC,SAAA,QA1qB3BC,WAAA,CAuqByC5B,QAAA,CAAAuO,iBAAiB,EAAzBtK,IAAI;2BAA3B+I,YAAA,CAGiB/J,yBAAA;QAHkClE,GAAG,EAAEkF,IAAI,CAAClC,EAAE;QAvqBzEmB,UAAA,EAwqBqBxC,KAAA,CAAAmO,mBAAmB,CAAC5B,aAAa,CAAChJ,IAAI,CAAClC,EAAE;QAxqB9D,uBAAAnB,MAAA,IAwqBqBF,KAAA,CAAAmO,mBAAmB,CAAC5B,aAAa,CAAChJ,IAAI,CAAClC,EAAE,IAAAnB;;QAxqB9DyC,OAAA,EAAAC,QAAA,CAyqBY,MAAe,CAzqB3BzC,gBAAA,CAAAyB,gBAAA,CAyqBe2B,IAAI,CAACjC,IAAI,IAAG,IAAE,GAAAM,gBAAA,CAAG2B,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QA1qBVf,CAAA;;wCA8qBM9D,mBAAA,CAOM,OAPNyP,YAOM,G,8BANJzP,mBAAA,CAAwC;MAAjCX,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCW,mBAAA,CAIS;MAprBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAgrByBF,KAAA,CAAAmO,mBAAmB,CAACvI,QAAQ,GAAA1F,MAAA;MAAE9B,KAAK,EAAC;2BACnDI,mBAAA,CAESyC,SAAA,QAnrBnBC,WAAA,CAirBmC5B,QAAA,CAAAmP,iBAAiB,EAA3BrN,MAAM;2BAArB5C,mBAAA,CAES;QAFoCH,GAAG,EAAE+C,MAAM,CAACC,EAAE;QAAGP,KAAK,EAAEM,MAAM,CAACC;0BACvED,MAAM,CAACE,IAAI,IAAG,UAAQ,GAAAM,gBAAA,CAAGR,MAAM,CAACmK,SAAS,IAAG,QAAM,GAAA3J,gBAAA,CAAGR,MAAM,CAACoK,UAAU,IAAG,KAC9E,uBAnrBVkD,YAAA;6EAgrByB1O,KAAA,CAAAmO,mBAAmB,CAACvI,QAAQ,E,KAO/C7G,mBAAA,CASM,OATN4P,YASM,G,8BARJ5P,mBAAA,CAAsC;MAA/BX,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BW,mBAAA,CAMS;MA/rBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAyrByBF,KAAA,CAAAmO,mBAAmB,CAACS,MAAM,GAAA1O,MAAA;MAAE9B,KAAK,EAAC;sCACjDW,mBAAA,CAAiD;MAAzC+B,KAAK,EAAC;IAAmB,GAAC,QAAM,qBACxC/B,mBAAA,CAA2C;MAAnC+B,KAAK,EAAC;IAAe,GAAC,MAAI,qBAClC/B,mBAAA,CAA6C;MAArC+B,KAAK,EAAC;IAAiB,GAAC,MAAI,qBACpC/B,mBAAA,CAAwC;MAAhC+B,KAAK,EAAC;IAAY,GAAC,MAAI,qBAC/B/B,mBAAA,CAAmC;MAA3B+B,KAAK,EAAC;IAAO,GAAC,MAAI,oB,2CALXd,KAAA,CAAAmO,mBAAmB,CAACS,MAAM,E,KAS7C7P,mBAAA,CAIM,OAJN8P,YAIM,G,8BAHJ9P,mBAAA,CAAsC;MAA/BX,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BW,mBAAA,CACuC;MArsB/C,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAosB2BF,KAAA,CAAAmO,mBAAmB,CAACW,WAAW,GAAA5O,MAAA;MAAE9B,KAAK,EAAC,cAAc;MAAC2Q,IAAI,EAAC,GAAG;MAC/ExO,WAAW,EAAC;mDADKP,KAAA,CAAAmO,mBAAmB,CAACW,WAAW,E;IApsB1DjM,CAAA;6DAysBInE,mBAAA,cAAiB,EACjBC,YAAA,CAqGYqJ,oBAAA;IA/yBhBxF,UAAA,EA0sBwBxC,KAAA,CAAAgP,oBAAoB,CAAC9G,IAAI;IA1sBjD,uBAAA9I,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA0sBwBF,KAAA,CAAAgP,oBAAoB,CAAC9G,IAAI,GAAAhI,MAAA;IAAEiI,KAAK,EAAC,QAAQ;IAACC,IAAI,EAAC,IAAI;IAAEC,SAAO,EAAE/I,QAAA,CAAA2P,gBAAgB;IAC/F1G,OAAO,EAAEvI,KAAA,CAAAwI;;IA3sBhB7F,OAAA,EAAAC,QAAA,CA4sBM,MAaM,CAbN7D,mBAAA,CAaM,OAbNmQ,YAaM,G,8BAZJnQ,mBAAA,CAAwC;MAAjCX,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCW,mBAAA,CAIM,OAJNoQ,YAIM,GAHJxQ,YAAA,CAEiB4D,yBAAA;MAjtB3BC,UAAA,EA+sBmCxC,KAAA,CAAAoP,iBAAiB;MA/sBpD,wB,sCA+sBmCpP,KAAA,CAAAoP,iBAAiB,GAAAlP,MAAA,GAAsBZ,QAAA,CAAA+P,uBAAuB;;MA/sBjG1M,OAAA,EAAAC,QAAA,CA+sBmG,MAEzFxD,MAAA,UAAAA,MAAA,SAjtBVe,gBAAA,CA+sBmG,MAEzF,E;MAjtBV0C,CAAA;gEAmtBQ9D,mBAAA,CAIM,OAJNuQ,YAIM,I,kBAHJ9Q,mBAAA,CAEiByC,SAAA,QAttB3BC,WAAA,CAotByCpC,IAAA,CAAAD,KAAK,EAAb0E,IAAI;2BAA3B+I,YAAA,CAEiB/J,yBAAA;QAFsBlE,GAAG,EAAEkF,IAAI,CAAClC,EAAE;QAptB7DmB,UAAA,EAotBwExC,KAAA,CAAAgP,oBAAoB,CAACzC,aAAa,CAAChJ,IAAI,CAAClC,EAAE;QAptBlH,uBAAAnB,MAAA,IAotBwEF,KAAA,CAAAgP,oBAAoB,CAACzC,aAAa,CAAChJ,IAAI,CAAClC,EAAE,IAAAnB;;QAptBlHyC,OAAA,EAAAC,QAAA,CAqtBY,MAAe,CArtB3BzC,gBAAA,CAAAyB,gBAAA,CAqtBe2B,IAAI,CAACjC,IAAI,IAAG,IAAE,GAAAM,gBAAA,CAAG2B,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QAttBVf,CAAA;;sCAwtBQ9D,mBAAA,CAAiE,KAAjEwQ,YAAiE,EAA5C,MAAI,GAAA3N,gBAAA,CAAGtC,QAAA,CAAAkQ,0BAA0B,IAAG,MAAI,gB,GAG/DzQ,mBAAA,CA8DM,OA9DN0Q,YA8DM,G,8BA7DJ1Q,mBAAA,CAAsC;MAA/BX,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BW,mBAAA,CA2DM,OA3DN2Q,YA2DM,GA1DJ3Q,mBAAA,CAIM,OAJN4Q,YAIM,G,8BAHJ5Q,mBAAA,CAA0E;MAAnEX,KAAK,EAAC;IAAY,IA/tBrC+B,gBAAA,CA+tBsC,OAAK,GAAApB,mBAAA,CAAmC;MAA7BX,KAAK,EAAC;IAAc,GAAC,GAAC,E,sCAC3DW,mBAAA,CAAyG;MAAlGsB,IAAI,EAAC,MAAM;MAhuB9B,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAguBwCF,KAAA,CAAAgP,oBAAoB,CAAChL,QAAQ,GAAA9D,MAAA;MAAE9B,KAAK,EAAC,cAAc;MAACmC,WAAW,EAAC;mDAAhEP,KAAA,CAAAgP,oBAAoB,CAAChL,QAAQ,E,iCACzDjF,mBAAA,CAA6D;MAAxDX,KAAK,EAAC;IAA4B,GAAC,iBAAe,qB,GAGzDW,mBAAA,CAQM,OARN6Q,YAQM,G,8BAPJ7Q,mBAAA,CAAsC;MAA/BX,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BW,mBAAA,CAKS;MA3uBrB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAsuB6BF,KAAA,CAAAgP,oBAAoB,CAACa,IAAI,GAAA3P,MAAA;MAAE9B,KAAK,EAAC;sCAChDW,mBAAA,CAAkC;MAA1B+B,KAAK,EAAC;IAAO,GAAC,KAAG,qBACzB/B,mBAAA,CAAkC;MAA1B+B,KAAK,EAAC;IAAM,GAAC,MAAI,qBACzB/B,mBAAA,CAAqC;MAA7B+B,KAAK,EAAC;IAAS,GAAC,MAAI,qBAC5B/B,mBAAA,CAAsC;MAA9B+B,KAAK,EAAC;IAAU,GAAC,MAAI,oB,2CAJdd,KAAA,CAAAgP,oBAAoB,CAACa,IAAI,E,KAQ5C9Q,mBAAA,CAKM,OALN+Q,YAKM,GAJJ/Q,mBAAA,CAGQ,SAHRgR,YAGQ,G,gBAFNhR,mBAAA,CAAyF;MAAlFsB,IAAI,EAAC,UAAU;MAhvBpC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAgvB8CF,KAAA,CAAAgP,oBAAoB,CAACgB,YAAY,GAAA9P,MAAA;MAAE9B,KAAK,EAAC;uDAAzC4B,KAAA,CAAAgP,oBAAoB,CAACgB,YAAY,E,iCACjEjR,mBAAA,CAAgC;MAA1BX,KAAK,EAAC;IAAM,GAAC,QAAM,qB,KAI7BW,mBAAA,CAgBM,OAhBNkR,YAgBM,G,8BAfJlR,mBAAA,CAAwC;MAAjCX,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCW,mBAAA,CAaM,OAbNmR,YAaM,GAZJnR,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAAgP,oBAAoB,CAACmB,eAAe;MAClD/R,KAAK,EAzvBrB2B,eAAA,EAyvBsB,iFAAiF,EAC/EC,KAAA,CAAAgP,oBAAoB,CAACmB,eAAe;QAC5CxR,YAAA,CAAyDa,4BAAA;MAArCC,IAAI,EAAE,cAAc;MAAErB,KAAK,EAAC;sCA3vBhE+B,gBAAA,CA2vByE,QAE3D,G,kBACApB,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAAgP,oBAAoB,CAACmB,eAAe;MAClD/R,KAAK,EA/vBrB2B,eAAA,EA+vBsB,iFAAiF,GAC9EC,KAAA,CAAAgP,oBAAoB,CAACmB,eAAe;QAC7CxR,YAAA,CAA4Da,4BAAA;MAAxCC,IAAI,EAAE,iBAAiB;MAAErB,KAAK,EAAC;sCAjwBnE+B,gBAAA,CAiwB4E,QAE9D,G,sBAIOH,KAAA,CAAAgP,oBAAoB,CAACmB,eAAe,I,cAA/C3R,mBAAA,CAgBM,OAhBN4R,YAgBM,G,8BAfJrR,mBAAA,CAAsC;MAA/BX,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BW,mBAAA,CAaM,OAbNsR,YAaM,G,gBAZJtR,mBAAA,CAC4F;MADpFsB,IAAI,EAAEL,KAAA,CAAA+E,kBAAkB,CAACuL,aAAa;MA1wB5D,uBAAAlR,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA2wByBF,KAAA,CAAAgP,oBAAoB,CAAChK,QAAQ,GAAA9E,MAAA;MAAE+E,QAAQ,EAAR,EAAQ;MAAC7G,KAAK,EAAC;4BA3wBvEmS,YAAA,I,iBA2wByBvQ,KAAA,CAAAgP,oBAAoB,CAAChK,QAAQ,E,GACxCjG,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAA+E,kBAAkB,CAACuL,aAAa,IAAItQ,KAAA,CAAA+E,kBAAkB,CAACuL,aAAa;MAAEjQ,IAAI,EAAC,QAAQ;MACjGjC,KAAK,EAAC;QACNO,YAAA,CACoBa,4BAAA;MADAC,IAAI,UAAUO,KAAA,CAAA+E,kBAAkB,CAACuL,aAAa;MAChElS,KAAK,EAAC;yCAEVW,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEZ,QAAA,CAAAkR,+BAA+B;MAAInQ,IAAI,EAAC,QAAQ;MAC9DjC,KAAK,EAAC;QACNO,YAAA,CAA8Da,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAErB,KAAK,EAAC;sCAnxBrE+B,gBAAA,CAmxB8E,QAEhE,G,SArxBdzB,mBAAA,e,KA2xBMK,mBAAA,CAOM,OAPN0R,YAOM,G,8BANJ1R,mBAAA,CAAsC;MAA/BX,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BW,mBAAA,CAIS;MAjyBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA6xByBF,KAAA,CAAAgP,oBAAoB,CAACpJ,QAAQ,GAAA1F,MAAA;MAAE9B,KAAK,EAAC,aAAa;MAAEiN,QAAM,EAAAjM,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEZ,QAAA,CAAAkR,+BAA+B;2BAC1GhS,mBAAA,CAESyC,SAAA,QAhyBnBC,WAAA,CA8xBmCpC,IAAA,CAAAqC,QAAQ,EAAlBC,MAAM;2BAArB5C,mBAAA,CAES;QAF2BH,GAAG,EAAE+C,MAAM,CAACC,EAAE;QAAGP,KAAK,EAAEM,MAAM,CAACC;0BAC9DD,MAAM,CAACE,IAAI,IAAG,UAAQ,GAAAM,gBAAA,CAAGR,MAAM,CAACmK,SAAS,IAAG,QAAM,GAAA3J,gBAAA,CAAGR,MAAM,CAACoK,UAAU,IAAG,KAC9E,uBAhyBVkF,YAAA;6FA6xByB1Q,KAAA,CAAAgP,oBAAoB,CAACpJ,QAAQ,E,KAOhD7G,mBAAA,CAUM,OAVN4R,YAUM,G,8BATJ5R,mBAAA,CAAsC;MAA/BX,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BW,mBAAA,CAOM,OAPN6R,YAOM,GANJjS,YAAA,CAEiB4D,yBAAA;MAzyB3BC,UAAA,EAuyBmCxC,KAAA,CAAAgP,oBAAoB,CAAC1B,YAAY;MAvyBpE,uBAAAlO,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAuyBmCF,KAAA,CAAAgP,oBAAoB,CAAC1B,YAAY,GAAApN,MAAA;;MAvyBpEyC,OAAA,EAAAC,QAAA,CAwyBY,MAAkCxD,MAAA,UAAAA,MAAA,SAAlCL,mBAAA,CAAkC;QAA5BX,KAAK,EAAC;MAAM,GAAC,UAAQ,oB;MAxyBvCyE,CAAA;uCA0yBUlE,YAAA,CAEiB4D,yBAAA;MA5yB3BC,UAAA,EA0yBmCxC,KAAA,CAAAgP,oBAAoB,CAAC6B,cAAc;MA1yBtE,uBAAAzR,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA0yBmCF,KAAA,CAAAgP,oBAAoB,CAAC6B,cAAc,GAAA3Q,MAAA;;MA1yBtEyC,OAAA,EAAAC,QAAA,CA2yBY,MAAkCxD,MAAA,UAAAA,MAAA,SAAlCL,mBAAA,CAAkC;QAA5BX,KAAK,EAAC;MAAM,GAAC,UAAQ,oB;MA3yBvCyE,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}