{"ast": null, "code": "export default {\n  name: 'SecurityDashboard',\n  props: {\n    hosts: {\n      type: Array,\n      default: () => []\n    }\n  },\n  computed: {\n    totalHosts() {\n      return this.hosts.length;\n    },\n    expiringPasswords() {\n      let count = 0;\n      this.hosts.forEach(host => {\n        host.accounts.forEach(account => {\n          if (this.isPasswordExpiringSoon(account)) {\n            count++;\n          }\n        });\n      });\n      return count;\n    },\n    weakPasswords() {\n      let count = 0;\n      this.hosts.forEach(host => {\n        host.accounts.forEach(account => {\n          if (this.isPasswordWeak(account.password)) {\n            count++;\n          }\n        });\n      });\n      return count;\n    },\n    securityScore() {\n      const total = this.totalHosts * 2; // 假设每台主机有2个账号\n      if (total === 0) return 100;\n      const issues = this.expiringPasswords + this.weakPasswords;\n      const score = Math.max(0, Math.round((1 - issues / total) * 100));\n      return score;\n    },\n    securityScoreClass() {\n      if (this.securityScore >= 80) return 'text-green-600 dark:text-green-400';\n      if (this.securityScore >= 60) return 'text-yellow-600 dark:text-yellow-400';\n      return 'text-red-600 dark:text-red-400';\n    },\n    passwordStatusData() {\n      const total = this.totalHosts * 2;\n      const expired = Math.floor(total * 0.05);\n      const expiring = this.expiringPasswords;\n      const weak = this.weakPasswords;\n      const good = total - expired - expiring - weak;\n      return [{\n        label: '正常',\n        count: good,\n        percentage: good / total * 100,\n        color: 'bg-green-500'\n      }, {\n        label: '即将过期',\n        count: expiring,\n        percentage: expiring / total * 100,\n        color: 'bg-yellow-500'\n      }, {\n        label: '已过期',\n        count: expired,\n        percentage: expired / total * 100,\n        color: 'bg-red-500'\n      }, {\n        label: '弱密码',\n        count: weak,\n        percentage: weak / total * 100,\n        color: 'bg-orange-500'\n      }];\n    },\n    recentActivities() {\n      return [{\n        id: 1,\n        message: '批量更新了生产环境密码',\n        time: '2分钟前',\n        icon: 'key',\n        iconColor: 'text-blue-600',\n        iconBg: 'bg-blue-100 dark:bg-blue-900/30'\n      }, {\n        id: 2,\n        message: '检测到3个弱密码',\n        time: '15分钟前',\n        icon: 'exclamation-triangle',\n        iconColor: 'text-yellow-600',\n        iconBg: 'bg-yellow-100 dark:bg-yellow-900/30'\n      }, {\n        id: 3,\n        message: '新增密码策略\"高强度策略\"',\n        time: '1小时前',\n        icon: 'shield-alt',\n        iconColor: 'text-green-600',\n        iconBg: 'bg-green-100 dark:bg-green-900/30'\n      }, {\n        id: 4,\n        message: '定时任务执行完成',\n        time: '2小时前',\n        icon: 'clock',\n        iconColor: 'text-purple-600',\n        iconBg: 'bg-purple-100 dark:bg-purple-900/30'\n      }];\n    },\n    riskHosts() {\n      return this.hosts.filter(host => {\n        const hasExpiredPasswords = host.accounts.some(account => this.isPasswordExpired(account));\n        const hasWeakPasswords = host.accounts.some(account => this.isPasswordWeak(account.password));\n        return hasExpiredPasswords || hasWeakPasswords || host.status === 'error';\n      }).map(host => {\n        const issues = [];\n        if (host.accounts.some(account => this.isPasswordExpired(account))) {\n          issues.push('密码过期');\n        }\n        if (host.accounts.some(account => this.isPasswordWeak(account.password))) {\n          issues.push('弱密码');\n        }\n        if (host.status === 'error') {\n          issues.push('连接异常');\n        }\n        const riskLevel = issues.length >= 2 ? '高' : '中';\n        const riskLevelClass = riskLevel === '高' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';\n        return {\n          ...host,\n          riskLevel,\n          riskLevelClass,\n          issues\n        };\n      }).slice(0, 5); // 只显示前5个\n    }\n  },\n  methods: {\n    isPasswordExpiringSoon(account) {\n      if (!account.passwordExpiryDate) return false;\n      const expiryDate = new Date(account.passwordExpiryDate);\n      const now = new Date();\n      const daysUntilExpiry = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));\n      return daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n    },\n    isPasswordExpired(account) {\n      if (!account.passwordExpiryDate) return false;\n      const expiryDate = new Date(account.passwordExpiryDate);\n      const now = new Date();\n      return expiryDate < now;\n    },\n    isPasswordWeak(password) {\n      if (!password) return true;\n\n      // 简单的弱密码检测\n      if (password.length < 8) return true;\n      if (!/[A-Z]/.test(password)) return true;\n      if (!/[a-z]/.test(password)) return true;\n      if (!/[0-9]/.test(password)) return true;\n\n      // 检测常见弱密码\n      const weakPatterns = [/^password/i, /^123456/, /^admin/i, /^root/i, /^qwerty/i];\n      return weakPatterns.some(pattern => pattern.test(password));\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "hosts", "type", "Array", "default", "computed", "totalHosts", "length", "expiringPasswords", "count", "for<PERSON>ach", "host", "accounts", "account", "isPasswordExpiringSoon", "weakPasswords", "isPasswordWeak", "password", "securityScore", "total", "issues", "score", "Math", "max", "round", "securityScoreClass", "passwordStatusData", "expired", "floor", "expiring", "weak", "good", "label", "percentage", "color", "recentActivities", "id", "message", "time", "icon", "iconColor", "iconBg", "riskHosts", "filter", "hasExpiredPasswords", "some", "isPasswordExpired", "hasWeakPasswords", "status", "map", "push", "riskLevel", "riskLevelClass", "slice", "methods", "passwordExpiryDate", "expiryDate", "Date", "now", "daysUntilExpiry", "ceil", "test", "weakPatterns", "pattern"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\SecurityDashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"security-dashboard\">\n    <!-- 安全概览卡片 -->\n    <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n      <!-- 总主机数 -->\n      <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center\">\n              <font-awesome-icon :icon=\"['fas', 'server']\" class=\"text-blue-600 dark:text-blue-400 text-xl\" />\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">总主机数</p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ totalHosts }}</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- 密码过期警告 -->\n      <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center\">\n              <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"text-yellow-600 dark:text-yellow-400 text-xl\" />\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">即将过期</p>\n            <p class=\"text-2xl font-bold text-yellow-600 dark:text-yellow-400\">{{ expiringPasswords }}</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- 弱密码数量 -->\n      <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center\">\n              <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"text-red-600 dark:text-red-400 text-xl\" />\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">弱密码</p>\n            <p class=\"text-2xl font-bold text-red-600 dark:text-red-400\">{{ weakPasswords }}</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- 安全评分 -->\n      <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center\">\n              <font-awesome-icon :icon=\"['fas', 'check-circle']\" class=\"text-green-600 dark:text-green-400 text-xl\" />\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">安全评分</p>\n            <p class=\"text-2xl font-bold\" :class=\"securityScoreClass\">{{ securityScore }}/100</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 安全趋势图表和风险分析 -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n      <!-- 密码状态分布 -->\n      <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <h3 class=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">密码状态分布</h3>\n        <div class=\"space-y-4\">\n          <div v-for=\"status in passwordStatusData\" :key=\"status.label\" class=\"flex items-center justify-between\">\n            <div class=\"flex items-center space-x-3\">\n              <div class=\"w-4 h-4 rounded-full\" :class=\"status.color\"></div>\n              <span class=\"text-sm text-gray-700 dark:text-gray-300\">{{ status.label }}</span>\n            </div>\n            <div class=\"flex items-center space-x-2\">\n              <span class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ status.count }}</span>\n              <div class=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                <div class=\"h-2 rounded-full\" :class=\"status.color\" :style=\"{ width: status.percentage + '%' }\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 最近活动 -->\n      <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <h3 class=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">最近活动</h3>\n        <div class=\"space-y-4\">\n          <div v-for=\"activity in recentActivities\" :key=\"activity.id\" class=\"flex items-start space-x-3\">\n            <div class=\"flex-shrink-0\">\n              <div class=\"w-8 h-8 rounded-full flex items-center justify-center\" :class=\"activity.iconBg\">\n                <font-awesome-icon :icon=\"['fas', activity.icon]\" :class=\"activity.iconColor\" class=\"text-sm\" />\n              </div>\n            </div>\n            <div class=\"flex-1 min-w-0\">\n              <p class=\"text-sm text-gray-900 dark:text-white\">{{ activity.message }}</p>\n              <p class=\"text-xs text-gray-500 dark:text-gray-400\">{{ activity.time }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 风险主机列表 -->\n    <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n      <div class=\"flex items-center justify-between mb-4\">\n        <h3 class=\"text-lg font-semibold text-gray-900 dark:text-white\">高风险主机</h3>\n        <button class=\"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300\">\n          查看全部\n        </button>\n      </div>\n      \n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full\">\n          <thead>\n            <tr class=\"border-b border-gray-200 dark:border-gray-700\">\n              <th class=\"text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400\">主机名</th>\n              <th class=\"text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400\">IP地址</th>\n              <th class=\"text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400\">风险等级</th>\n              <th class=\"text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400\">主要问题</th>\n              <th class=\"text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400\">操作</th>\n            </tr>\n          </thead>\n          <tbody class=\"divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-for=\"host in riskHosts\" :key=\"host.id\" class=\"hover:bg-gray-50 dark:hover:bg-gray-700/50\">\n              <td class=\"py-3 px-4\">\n                <div class=\"flex items-center space-x-2\">\n                  <font-awesome-icon :icon=\"['fas', 'server']\" class=\"text-gray-400\" />\n                  <span class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ host.name }}</span>\n                </div>\n              </td>\n              <td class=\"py-3 px-4 text-sm text-gray-500 dark:text-gray-400\">{{ host.ip }}</td>\n              <td class=\"py-3 px-4\">\n                <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\" :class=\"host.riskLevelClass\">\n                  {{ host.riskLevel }}\n                </span>\n              </td>\n              <td class=\"py-3 px-4 text-sm text-gray-500 dark:text-gray-400\">{{ host.issues.join(', ') }}</td>\n              <td class=\"py-3 px-4\">\n                <button class=\"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300\">\n                  立即修复\n                </button>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'SecurityDashboard',\n  props: {\n    hosts: {\n      type: Array,\n      default: () => []\n    }\n  },\n  computed: {\n    totalHosts() {\n      return this.hosts.length\n    },\n    \n    expiringPasswords() {\n      let count = 0\n      this.hosts.forEach(host => {\n        host.accounts.forEach(account => {\n          if (this.isPasswordExpiringSoon(account)) {\n            count++\n          }\n        })\n      })\n      return count\n    },\n    \n    weakPasswords() {\n      let count = 0\n      this.hosts.forEach(host => {\n        host.accounts.forEach(account => {\n          if (this.isPasswordWeak(account.password)) {\n            count++\n          }\n        })\n      })\n      return count\n    },\n    \n    securityScore() {\n      const total = this.totalHosts * 2 // 假设每台主机有2个账号\n      if (total === 0) return 100\n      \n      const issues = this.expiringPasswords + this.weakPasswords\n      const score = Math.max(0, Math.round((1 - issues / total) * 100))\n      return score\n    },\n    \n    securityScoreClass() {\n      if (this.securityScore >= 80) return 'text-green-600 dark:text-green-400'\n      if (this.securityScore >= 60) return 'text-yellow-600 dark:text-yellow-400'\n      return 'text-red-600 dark:text-red-400'\n    },\n    \n    passwordStatusData() {\n      const total = this.totalHosts * 2\n      const expired = Math.floor(total * 0.05)\n      const expiring = this.expiringPasswords\n      const weak = this.weakPasswords\n      const good = total - expired - expiring - weak\n      \n      return [\n        { label: '正常', count: good, percentage: (good / total) * 100, color: 'bg-green-500' },\n        { label: '即将过期', count: expiring, percentage: (expiring / total) * 100, color: 'bg-yellow-500' },\n        { label: '已过期', count: expired, percentage: (expired / total) * 100, color: 'bg-red-500' },\n        { label: '弱密码', count: weak, percentage: (weak / total) * 100, color: 'bg-orange-500' }\n      ]\n    },\n    \n    recentActivities() {\n      return [\n        {\n          id: 1,\n          message: '批量更新了生产环境密码',\n          time: '2分钟前',\n          icon: 'key',\n          iconColor: 'text-blue-600',\n          iconBg: 'bg-blue-100 dark:bg-blue-900/30'\n        },\n        {\n          id: 2,\n          message: '检测到3个弱密码',\n          time: '15分钟前',\n          icon: 'exclamation-triangle',\n          iconColor: 'text-yellow-600',\n          iconBg: 'bg-yellow-100 dark:bg-yellow-900/30'\n        },\n        {\n          id: 3,\n          message: '新增密码策略\"高强度策略\"',\n          time: '1小时前',\n          icon: 'shield-alt',\n          iconColor: 'text-green-600',\n          iconBg: 'bg-green-100 dark:bg-green-900/30'\n        },\n        {\n          id: 4,\n          message: '定时任务执行完成',\n          time: '2小时前',\n          icon: 'clock',\n          iconColor: 'text-purple-600',\n          iconBg: 'bg-purple-100 dark:bg-purple-900/30'\n        }\n      ]\n    },\n    \n    riskHosts() {\n      return this.hosts.filter(host => {\n        const hasExpiredPasswords = host.accounts.some(account => this.isPasswordExpired(account))\n        const hasWeakPasswords = host.accounts.some(account => this.isPasswordWeak(account.password))\n        return hasExpiredPasswords || hasWeakPasswords || host.status === 'error'\n      }).map(host => {\n        const issues = []\n        if (host.accounts.some(account => this.isPasswordExpired(account))) {\n          issues.push('密码过期')\n        }\n        if (host.accounts.some(account => this.isPasswordWeak(account.password))) {\n          issues.push('弱密码')\n        }\n        if (host.status === 'error') {\n          issues.push('连接异常')\n        }\n        \n        const riskLevel = issues.length >= 2 ? '高' : '中'\n        const riskLevelClass = riskLevel === '高' \n          ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'\n          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'\n        \n        return {\n          ...host,\n          riskLevel,\n          riskLevelClass,\n          issues\n        }\n      }).slice(0, 5) // 只显示前5个\n    }\n  },\n  \n  methods: {\n    isPasswordExpiringSoon(account) {\n      if (!account.passwordExpiryDate) return false\n      \n      const expiryDate = new Date(account.passwordExpiryDate)\n      const now = new Date()\n      const daysUntilExpiry = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24))\n      \n      return daysUntilExpiry <= 7 && daysUntilExpiry > 0\n    },\n    \n    isPasswordExpired(account) {\n      if (!account.passwordExpiryDate) return false\n      \n      const expiryDate = new Date(account.passwordExpiryDate)\n      const now = new Date()\n      \n      return expiryDate < now\n    },\n    \n    isPasswordWeak(password) {\n      if (!password) return true\n      \n      // 简单的弱密码检测\n      if (password.length < 8) return true\n      if (!/[A-Z]/.test(password)) return true\n      if (!/[a-z]/.test(password)) return true\n      if (!/[0-9]/.test(password)) return true\n      \n      // 检测常见弱密码\n      const weakPatterns = [\n        /^password/i,\n        /^123456/,\n        /^admin/i,\n        /^root/i,\n        /^qwerty/i\n      ]\n      \n      return weakPatterns.some(pattern => pattern.test(password))\n    }\n  }\n}\n</script>\n"], "mappings": "AA0JA,eAAe;EACbA,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE;IACLC,KAAK,EAAE;MACLC,IAAI,EAAEC,KAAK;MACXC,OAAO,EAAEA,CAAA,KAAM;IACjB;EACF,CAAC;EACDC,QAAQ,EAAE;IACRC,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACL,KAAK,CAACM,MAAK;IACzB,CAAC;IAEDC,iBAAiBA,CAAA,EAAG;MAClB,IAAIC,KAAI,GAAI;MACZ,IAAI,CAACR,KAAK,CAACS,OAAO,CAACC,IAAG,IAAK;QACzBA,IAAI,CAACC,QAAQ,CAACF,OAAO,CAACG,OAAM,IAAK;UAC/B,IAAI,IAAI,CAACC,sBAAsB,CAACD,OAAO,CAAC,EAAE;YACxCJ,KAAK,EAAC;UACR;QACF,CAAC;MACH,CAAC;MACD,OAAOA,KAAI;IACb,CAAC;IAEDM,aAAaA,CAAA,EAAG;MACd,IAAIN,KAAI,GAAI;MACZ,IAAI,CAACR,KAAK,CAACS,OAAO,CAACC,IAAG,IAAK;QACzBA,IAAI,CAACC,QAAQ,CAACF,OAAO,CAACG,OAAM,IAAK;UAC/B,IAAI,IAAI,CAACG,cAAc,CAACH,OAAO,CAACI,QAAQ,CAAC,EAAE;YACzCR,KAAK,EAAC;UACR;QACF,CAAC;MACH,CAAC;MACD,OAAOA,KAAI;IACb,CAAC;IAEDS,aAAaA,CAAA,EAAG;MACd,MAAMC,KAAI,GAAI,IAAI,CAACb,UAAS,GAAI,GAAE;MAClC,IAAIa,KAAI,KAAM,CAAC,EAAE,OAAO,GAAE;MAE1B,MAAMC,MAAK,GAAI,IAAI,CAACZ,iBAAgB,GAAI,IAAI,CAACO,aAAY;MACzD,MAAMM,KAAI,GAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,KAAK,CAAC,CAAC,IAAIJ,MAAK,GAAID,KAAK,IAAI,GAAG,CAAC;MAChE,OAAOE,KAAI;IACb,CAAC;IAEDI,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAACP,aAAY,IAAK,EAAE,EAAE,OAAO,oCAAmC;MACxE,IAAI,IAAI,CAACA,aAAY,IAAK,EAAE,EAAE,OAAO,sCAAqC;MAC1E,OAAO,gCAA+B;IACxC,CAAC;IAEDQ,kBAAkBA,CAAA,EAAG;MACnB,MAAMP,KAAI,GAAI,IAAI,CAACb,UAAS,GAAI;MAChC,MAAMqB,OAAM,GAAIL,IAAI,CAACM,KAAK,CAACT,KAAI,GAAI,IAAI;MACvC,MAAMU,QAAO,GAAI,IAAI,CAACrB,iBAAgB;MACtC,MAAMsB,IAAG,GAAI,IAAI,CAACf,aAAY;MAC9B,MAAMgB,IAAG,GAAIZ,KAAI,GAAIQ,OAAM,GAAIE,QAAO,GAAIC,IAAG;MAE7C,OAAO,CACL;QAAEE,KAAK,EAAE,IAAI;QAAEvB,KAAK,EAAEsB,IAAI;QAAEE,UAAU,EAAGF,IAAG,GAAIZ,KAAK,GAAI,GAAG;QAAEe,KAAK,EAAE;MAAe,CAAC,EACrF;QAAEF,KAAK,EAAE,MAAM;QAAEvB,KAAK,EAAEoB,QAAQ;QAAEI,UAAU,EAAGJ,QAAO,GAAIV,KAAK,GAAI,GAAG;QAAEe,KAAK,EAAE;MAAgB,CAAC,EAChG;QAAEF,KAAK,EAAE,KAAK;QAAEvB,KAAK,EAAEkB,OAAO;QAAEM,UAAU,EAAGN,OAAM,GAAIR,KAAK,GAAI,GAAG;QAAEe,KAAK,EAAE;MAAa,CAAC,EAC1F;QAAEF,KAAK,EAAE,KAAK;QAAEvB,KAAK,EAAEqB,IAAI;QAAEG,UAAU,EAAGH,IAAG,GAAIX,KAAK,GAAI,GAAG;QAAEe,KAAK,EAAE;MAAgB,EACxF;IACF,CAAC;IAEDC,gBAAgBA,CAAA,EAAG;MACjB,OAAO,CACL;QACEC,EAAE,EAAE,CAAC;QACLC,OAAO,EAAE,aAAa;QACtBC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE,KAAK;QACXC,SAAS,EAAE,eAAe;QAC1BC,MAAM,EAAE;MACV,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,OAAO,EAAE,UAAU;QACnBC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,sBAAsB;QAC5BC,SAAS,EAAE,iBAAiB;QAC5BC,MAAM,EAAE;MACV,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,OAAO,EAAE,eAAe;QACxBC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE,YAAY;QAClBC,SAAS,EAAE,gBAAgB;QAC3BC,MAAM,EAAE;MACV,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,OAAO,EAAE,UAAU;QACnBC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE,OAAO;QACbC,SAAS,EAAE,iBAAiB;QAC5BC,MAAM,EAAE;MACV,EACF;IACF,CAAC;IAEDC,SAASA,CAAA,EAAG;MACV,OAAO,IAAI,CAACzC,KAAK,CAAC0C,MAAM,CAAChC,IAAG,IAAK;QAC/B,MAAMiC,mBAAkB,GAAIjC,IAAI,CAACC,QAAQ,CAACiC,IAAI,CAAChC,OAAM,IAAK,IAAI,CAACiC,iBAAiB,CAACjC,OAAO,CAAC;QACzF,MAAMkC,gBAAe,GAAIpC,IAAI,CAACC,QAAQ,CAACiC,IAAI,CAAChC,OAAM,IAAK,IAAI,CAACG,cAAc,CAACH,OAAO,CAACI,QAAQ,CAAC;QAC5F,OAAO2B,mBAAkB,IAAKG,gBAAe,IAAKpC,IAAI,CAACqC,MAAK,KAAM,OAAM;MAC1E,CAAC,CAAC,CAACC,GAAG,CAACtC,IAAG,IAAK;QACb,MAAMS,MAAK,GAAI,EAAC;QAChB,IAAIT,IAAI,CAACC,QAAQ,CAACiC,IAAI,CAAChC,OAAM,IAAK,IAAI,CAACiC,iBAAiB,CAACjC,OAAO,CAAC,CAAC,EAAE;UAClEO,MAAM,CAAC8B,IAAI,CAAC,MAAM;QACpB;QACA,IAAIvC,IAAI,CAACC,QAAQ,CAACiC,IAAI,CAAChC,OAAM,IAAK,IAAI,CAACG,cAAc,CAACH,OAAO,CAACI,QAAQ,CAAC,CAAC,EAAE;UACxEG,MAAM,CAAC8B,IAAI,CAAC,KAAK;QACnB;QACA,IAAIvC,IAAI,CAACqC,MAAK,KAAM,OAAO,EAAE;UAC3B5B,MAAM,CAAC8B,IAAI,CAAC,MAAM;QACpB;QAEA,MAAMC,SAAQ,GAAI/B,MAAM,CAACb,MAAK,IAAK,IAAI,GAAE,GAAI,GAAE;QAC/C,MAAM6C,cAAa,GAAID,SAAQ,KAAM,GAAE,GACnC,8DAA6D,GAC7D,0EAAyE;QAE7E,OAAO;UACL,GAAGxC,IAAI;UACPwC,SAAS;UACTC,cAAc;UACdhC;QACF;MACF,CAAC,CAAC,CAACiC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAE;IACjB;EACF,CAAC;EAEDC,OAAO,EAAE;IACPxC,sBAAsBA,CAACD,OAAO,EAAE;MAC9B,IAAI,CAACA,OAAO,CAAC0C,kBAAkB,EAAE,OAAO,KAAI;MAE5C,MAAMC,UAAS,GAAI,IAAIC,IAAI,CAAC5C,OAAO,CAAC0C,kBAAkB;MACtD,MAAMG,GAAE,GAAI,IAAID,IAAI,CAAC;MACrB,MAAME,eAAc,GAAIrC,IAAI,CAACsC,IAAI,CAAC,CAACJ,UAAS,GAAIE,GAAG,KAAK,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,EAAE,CAAC;MAE5E,OAAOC,eAAc,IAAK,KAAKA,eAAc,GAAI;IACnD,CAAC;IAEDb,iBAAiBA,CAACjC,OAAO,EAAE;MACzB,IAAI,CAACA,OAAO,CAAC0C,kBAAkB,EAAE,OAAO,KAAI;MAE5C,MAAMC,UAAS,GAAI,IAAIC,IAAI,CAAC5C,OAAO,CAAC0C,kBAAkB;MACtD,MAAMG,GAAE,GAAI,IAAID,IAAI,CAAC;MAErB,OAAOD,UAAS,GAAIE,GAAE;IACxB,CAAC;IAED1C,cAAcA,CAACC,QAAQ,EAAE;MACvB,IAAI,CAACA,QAAQ,EAAE,OAAO,IAAG;;MAEzB;MACA,IAAIA,QAAQ,CAACV,MAAK,GAAI,CAAC,EAAE,OAAO,IAAG;MACnC,IAAI,CAAC,OAAO,CAACsD,IAAI,CAAC5C,QAAQ,CAAC,EAAE,OAAO,IAAG;MACvC,IAAI,CAAC,OAAO,CAAC4C,IAAI,CAAC5C,QAAQ,CAAC,EAAE,OAAO,IAAG;MACvC,IAAI,CAAC,OAAO,CAAC4C,IAAI,CAAC5C,QAAQ,CAAC,EAAE,OAAO,IAAG;;MAEvC;MACA,MAAM6C,YAAW,GAAI,CACnB,YAAY,EACZ,SAAS,EACT,SAAS,EACT,QAAQ,EACR,UAAS,CACX;MAEA,OAAOA,YAAY,CAACjB,IAAI,CAACkB,OAAM,IAAKA,OAAO,CAACF,IAAI,CAAC5C,QAAQ,CAAC;IAC5D;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}