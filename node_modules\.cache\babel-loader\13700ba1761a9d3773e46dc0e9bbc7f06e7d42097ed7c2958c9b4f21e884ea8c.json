{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, vModelText as _vModelText, withDirectives as _withDirectives, vModelSelect as _vModelSelect, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, vModelDynamic as _vModelDynamic, vModelCheckbox as _vModelCheckbox, createBlock as _createBlock, vModelRadio as _vModelRadio } from \"vue\";\nconst _hoisted_1 = {\n  class: \"bg-white shadow rounded-lg p-4 mb-6\"\n};\nconst _hoisted_2 = {\n  class: \"flex flex-wrap items-center justify-between\"\n};\nconst _hoisted_3 = {\n  class: \"flex space-x-3 mb-2 sm:mb-0\"\n};\nconst _hoisted_4 = {\n  class: \"flex items-center space-x-4\"\n};\nconst _hoisted_5 = {\n  class: \"flex items-center border rounded-md overflow-hidden\"\n};\nconst _hoisted_6 = {\n  class: \"relative\"\n};\nconst _hoisted_7 = {\n  class: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n};\nconst _hoisted_8 = {\n  key: 0,\n  class: \"bg-white rounded-lg shadow overflow-hidden\"\n};\nconst _hoisted_9 = {\n  class: \"min-w-full divide-y divide-gray-200\"\n};\nconst _hoisted_10 = {\n  class: \"bg-gray-50\"\n};\nconst _hoisted_11 = {\n  scope: \"col\",\n  class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n};\nconst _hoisted_12 = {\n  class: \"bg-white divide-y divide-gray-200\"\n};\nconst _hoisted_13 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_14 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_15 = {\n  class: \"ml-2 font-medium text-gray-900\"\n};\nconst _hoisted_16 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_17 = {\n  class: \"text-sm text-gray-900\"\n};\nconst _hoisted_18 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_19 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_20 = {\n  class: \"text-sm font-medium text-gray-900\"\n};\nconst _hoisted_21 = {\n  key: 0,\n  class: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\"\n};\nconst _hoisted_22 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_23 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_24 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_25 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_26 = {\n  key: 1,\n  class: \"ml-1\"\n};\nconst _hoisted_27 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_28 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_29 = {\n  class: \"flex-grow\"\n};\nconst _hoisted_30 = [\"type\", \"value\"];\nconst _hoisted_31 = [\"onClick\"];\nconst _hoisted_32 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_33 = {\n  class: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\"\n};\nconst _hoisted_34 = [\"onClick\"];\nconst _hoisted_35 = {\n  class: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\"\n};\nconst _hoisted_36 = {\n  class: \"px-4 py-5 sm:p-6\"\n};\nconst _hoisted_37 = {\n  class: \"flex justify-between items-start mb-4\"\n};\nconst _hoisted_38 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_39 = {\n  class: \"text-lg font-medium text-gray-900\"\n};\nconst _hoisted_40 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_41 = {\n  class: \"space-y-4\"\n};\nconst _hoisted_42 = {\n  class: \"flex justify-between items-center mb-2\"\n};\nconst _hoisted_43 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_44 = {\n  class: \"text-sm font-medium text-gray-900\"\n};\nconst _hoisted_45 = {\n  key: 0,\n  class: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\"\n};\nconst _hoisted_46 = [\"onClick\"];\nconst _hoisted_47 = {\n  class: \"mb-2\"\n};\nconst _hoisted_48 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_49 = [\"type\", \"value\"];\nconst _hoisted_50 = [\"onClick\"];\nconst _hoisted_51 = {\n  class: \"grid grid-cols-2 gap-2 text-xs\"\n};\nconst _hoisted_52 = {\n  class: \"text-gray-900\"\n};\nconst _hoisted_53 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_54 = {\n  class: \"mt-4 flex justify-center\"\n};\nconst _hoisted_55 = [\"onClick\"];\nconst _hoisted_56 = {\n  class: \"mb-4\"\n};\nconst _hoisted_57 = {\n  class: \"px-3 py-2 bg-gray-50 rounded-md\"\n};\nconst _hoisted_58 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_59 = {\n  class: \"flex space-x-3\"\n};\nconst _hoisted_60 = {\n  key: 0,\n  class: \"form-group mb-4\"\n};\nconst _hoisted_61 = [\"value\"];\nconst _hoisted_62 = {\n  class: \"mt-3\"\n};\nconst _hoisted_63 = {\n  class: \"flex justify-between mb-1\"\n};\nconst _hoisted_64 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_65 = [\"type\"];\nconst _hoisted_66 = {\n  key: 1,\n  class: \"space-y-4\"\n};\nconst _hoisted_67 = {\n  class: \"form-group\"\n};\nconst _hoisted_68 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_69 = [\"type\"];\nconst _hoisted_70 = {\n  class: \"form-group\"\n};\nconst _hoisted_71 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_72 = [\"type\"];\nconst _hoisted_73 = {\n  key: 0,\n  class: \"text-sm text-red-500 mt-1\"\n};\nconst _hoisted_74 = {\n  class: \"space-y-2 mt-4\"\n};\nconst _hoisted_75 = {\n  class: \"mb-4\"\n};\nconst _hoisted_76 = {\n  class: \"px-3 py-2 bg-gray-50 rounded-md\"\n};\nconst _hoisted_77 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_78 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_79 = {\n  class: \"relative inline-block w-10 mr-2 align-middle select-none\"\n};\nconst _hoisted_80 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_81 = [\"value\"];\nconst _hoisted_82 = {\n  class: \"form-group\"\n};\nconst _hoisted_83 = {\n  class: \"flex justify-between mb-1\"\n};\nconst _hoisted_84 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_85 = [\"type\"];\nconst _hoisted_86 = {\n  class: \"form-group\"\n};\nconst _hoisted_87 = {\n  class: \"mb-2\"\n};\nconst _hoisted_88 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_89 = {\n  class: \"form-text\"\n};\nconst _hoisted_90 = {\n  class: \"form-group\"\n};\nconst _hoisted_91 = [\"value\"];\nconst _hoisted_92 = {\n  class: \"form-group\"\n};\nconst _hoisted_93 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_94 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_95 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_96 = {\n  key: 0,\n  class: \"mt-3\"\n};\nconst _hoisted_97 = {\n  class: \"grid grid-cols-2 gap-4\"\n};\nconst _hoisted_98 = {\n  class: \"form-group\"\n};\nconst _hoisted_99 = {\n  class: \"form-group\"\n};\nconst _hoisted_100 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_101 = {\n  class: \"form-group\"\n};\nconst _hoisted_102 = [\"value\"];\nconst _hoisted_103 = {\n  class: \"form-group\"\n};\nconst _hoisted_104 = {\n  class: \"form-group\"\n};\nconst _hoisted_105 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_106 = {\n  class: \"form-group\"\n};\nconst _hoisted_107 = [\"value\"];\nconst _hoisted_108 = {\n  class: \"form-group\"\n};\nconst _hoisted_109 = {\n  class: \"form-group\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_CustomCheckbox = _resolveComponent(\"CustomCheckbox\");\n  const _component_StatusBadge = _resolveComponent(\"StatusBadge\");\n  const _component_PasswordStrengthMeter = _resolveComponent(\"PasswordStrengthMeter\");\n  const _component_BaseModal = _resolveComponent(\"BaseModal\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createCommentVNode(\" 操作按钮 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.showEmergencyReset && $options.showEmergencyReset(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'exclamation-triangle'],\n    class: \"mr-2\"\n  }), _cache[49] || (_cache[49] = _createElementVNode(\"span\", null, \"紧急重置\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.openBatchUpdateModal && $options.openBatchUpdateModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'key'],\n    class: \"mr-2\"\n  }), _cache[50] || (_cache[50] = _createElementVNode(\"span\", null, \"批量更新密码\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.openBatchApplyModal && $options.openBatchApplyModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"mr-2\"\n  }), _cache[51] || (_cache[51] = _createElementVNode(\"span\", null, \"批量应用策略\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" 视图切换 \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1 focus:outline-none\", {\n      'bg-blue-500 text-white': $data.viewMode === 'table',\n      'bg-gray-100 text-gray-600': $data.viewMode !== 'table'\n    }]),\n    onClick: _cache[3] || (_cache[3] = $event => $data.viewMode = 'table')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'table'],\n    class: \"mr-1\"\n  }), _cache[52] || (_cache[52] = _createTextVNode(\" 表格 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1 focus:outline-none\", {\n      'bg-blue-500 text-white': $data.viewMode === 'card',\n      'bg-gray-100 text-gray-600': $data.viewMode !== 'card'\n    }]),\n    onClick: _cache[4] || (_cache[4] = $event => $data.viewMode = 'card')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'th-large'],\n    class: \"mr-1\"\n  }), _cache[53] || (_cache[53] = _createTextVNode(\" 卡片 \"))], 2 /* CLASS */)]), _createCommentVNode(\" 筛选 \"), _createElementVNode(\"div\", _hoisted_6, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.filterText = $event),\n    placeholder: \"筛选主机...\",\n    class: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.filterText]]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"text-gray-400\"\n  })])]), _createCommentVNode(\" 状态筛选 \"), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.statusFilter = $event),\n    class: \"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n  }, _cache[54] || (_cache[54] = [_createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有状态\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"normal\"\n  }, \"正常\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"warning\"\n  }, \"警告\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"error\"\n  }, \"错误\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.statusFilter]])])])]), _createCommentVNode(\" 主机列表 \"), _createCommentVNode(\" 表格视图 \"), $data.viewMode === 'table' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_createElementVNode(\"table\", _hoisted_9, [_createElementVNode(\"thead\", _hoisted_10, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", _hoisted_11, [_createVNode(_component_CustomCheckbox, {\n    modelValue: $data.selectAll,\n    \"onUpdate:modelValue\": [_cache[7] || (_cache[7] = $event => $data.selectAll = $event), $options.toggleSelectAll]\n  }, {\n    default: _withCtx(() => _cache[55] || (_cache[55] = [_createTextVNode(\" 主机名 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _cache[56] || (_cache[56] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" IP地址 \", -1 /* HOISTED */)), _cache[57] || (_cache[57] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 账号 \", -1 /* HOISTED */)), _cache[58] || (_cache[58] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 最后密码修改时间 \", -1 /* HOISTED */)), _cache[59] || (_cache[59] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码过期时间 \", -1 /* HOISTED */)), _cache[60] || (_cache[60] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码 \", -1 /* HOISTED */)), _cache[61] || (_cache[61] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 状态 \", -1 /* HOISTED */)), _cache[62] || (_cache[62] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 操作 \", -1 /* HOISTED */))])]), _createElementVNode(\"tbody\", _hoisted_12, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.getAllAccounts, (account, index) => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: account.id,\n      class: _normalizeClass({\n        'bg-gray-50': index % 2 === 0,\n        'hover:bg-blue-50': true\n      })\n    }, [_createElementVNode(\"td\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_CustomCheckbox, {\n      modelValue: account.host.selected,\n      \"onUpdate:modelValue\": $event => account.host.selected = $event\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"span\", _hoisted_15, _toDisplayString(account.host.name), 1 /* TEXT */)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])])]), _createElementVNode(\"td\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, _toDisplayString(account.host.ip), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"span\", _hoisted_20, _toDisplayString(account.username), 1 /* TEXT */), account.isDefault ? (_openBlock(), _createElementBlock(\"span\", _hoisted_21, \" 默认 \")) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"td\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, _toDisplayString(account.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_24, [_createElementVNode(\"div\", {\n      class: _normalizeClass({\n        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\n        'bg-red-100 text-red-800': $options.isPasswordExpired(account).status === 'danger' || $options.isPasswordExpired(account).status === 'expired',\n        'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(account).status === 'warning',\n        'bg-gray-100 text-gray-800': $options.isPasswordExpired(account).status === 'normal'\n      })\n    }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(account).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(account).status === 'expired' || $options.isPasswordExpired(account).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_25, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'exclamation-triangle']\n    })])) : $options.isPasswordExpired(account).status === 'warning' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_26, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'exclamation-circle']\n    })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]), _createElementVNode(\"td\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"input\", {\n      type: $data.passwordVisibility[account.id] ? 'text' : 'password',\n      value: account.password,\n      readonly: \"\",\n      class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n    }, null, 8 /* PROPS */, _hoisted_30)]), _createElementVNode(\"button\", {\n      onClick: $event => $options.togglePasswordVisibility(account.id),\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility[account.id] ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_31)])]), _createElementVNode(\"td\", _hoisted_32, [_createVNode(_component_StatusBadge, {\n      type: account.host.status\n    }, null, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"td\", _hoisted_33, [_createElementVNode(\"button\", {\n      class: \"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n      onClick: $event => $options.openChangePasswordModal(account.host, account)\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'key'],\n      class: \"mr-1\"\n    }), _cache[63] || (_cache[63] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_34)])], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */))])])])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 卡片视图 \"), _createElementVNode(\"div\", _hoisted_35, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredHosts, host => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: host.id,\n      class: \"bg-white overflow-hidden shadow rounded-lg\"\n    }, [_createElementVNode(\"div\", _hoisted_36, [_createCommentVNode(\" 主机头部 \"), _createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_createVNode(_component_CustomCheckbox, {\n      modelValue: host.selected,\n      \"onUpdate:modelValue\": $event => host.selected = $event,\n      class: \"mr-2\"\n    }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"]), _createElementVNode(\"div\", null, [_createElementVNode(\"h3\", _hoisted_39, _toDisplayString(host.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_40, _toDisplayString(host.ip), 1 /* TEXT */)])]), _createVNode(_component_StatusBadge, {\n      type: host.status\n    }, null, 8 /* PROPS */, [\"type\"])]), _createCommentVNode(\" 账号列表 \"), _createElementVNode(\"div\", _hoisted_41, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(host.accounts, account => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: account.id,\n        class: _normalizeClass([\"border border-gray-200 rounded-lg p-3\", {\n          'border-green-300 bg-green-50': account.isDefault\n        }])\n      }, [_createElementVNode(\"div\", _hoisted_42, [_createElementVNode(\"div\", _hoisted_43, [_createElementVNode(\"span\", _hoisted_44, _toDisplayString(account.username), 1 /* TEXT */), account.isDefault ? (_openBlock(), _createElementBlock(\"span\", _hoisted_45, \" 默认 \")) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.openChangePasswordModal(host, account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'key'],\n        class: \"mr-1\"\n      }), _cache[64] || (_cache[64] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_46)]), _createCommentVNode(\" 密码展示 \"), _createElementVNode(\"div\", _hoisted_47, [_cache[65] || (_cache[65] = _createElementVNode(\"div\", {\n        class: \"text-xs font-medium text-gray-500 mb-1\"\n      }, \"密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_48, [_createElementVNode(\"input\", {\n        type: $data.passwordVisibility[account.id] ? 'text' : 'password',\n        value: account.password,\n        readonly: \"\",\n        class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n      }, null, 8 /* PROPS */, _hoisted_49), _createElementVNode(\"button\", {\n        onClick: $event => $options.togglePasswordVisibility(account.id),\n        class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $data.passwordVisibility[account.id] ? 'eye-slash' : 'eye'],\n        class: \"text-lg\"\n      }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_50)])]), _createCommentVNode(\" 密码信息区域 \"), _createElementVNode(\"div\", _hoisted_51, [_createElementVNode(\"div\", null, [_cache[66] || (_cache[66] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"最后修改时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_52, _toDisplayString(account.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[67] || (_cache[67] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"密码过期\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n        class: _normalizeClass({\n          'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\n          'bg-red-100 text-red-800': $options.isPasswordExpired(account).status === 'danger' || $options.isPasswordExpired(account).status === 'expired',\n          'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(account).status === 'warning',\n          'bg-gray-100 text-gray-800': $options.isPasswordExpired(account).status === 'normal'\n        })\n      }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(account).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(account).status === 'expired' || $options.isPasswordExpired(account).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_53, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-triangle']\n      })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)])])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 添加账号按钮 \"), _createElementVNode(\"div\", _hoisted_54, [_createElementVNode(\"button\", {\n      class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n      onClick: $event => $options.openAddAccountModal(host)\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'plus'],\n      class: \"mr-1\"\n    }), _cache[68] || (_cache[68] = _createTextVNode(\" 添加账号 \"))], 8 /* PROPS */, _hoisted_55)])])]);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 修改密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.changePasswordModal.show,\n    \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $data.changePasswordModal.show = $event),\n    title: \"修改密码\",\n    onConfirm: $options.updatePassword,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_56, [_cache[72] || (_cache[72] = _createElementVNode(\"div\", {\n      class: \"font-medium mb-2\"\n    }, \"主机信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_57, [_createElementVNode(\"div\", null, [_cache[69] || (_cache[69] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"主机名:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[70] || (_cache[70] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"IP地址:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.ip), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[71] || (_cache[71] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"账号:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentAccount.username), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_58, [_cache[75] || (_cache[75] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_59, [_createElementVNode(\"button\", {\n      onClick: _cache[8] || (_cache[8] = $event => $data.changePasswordModal.method = 'auto'),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", $data.changePasswordModal.method === 'auto' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-2\"\n    }), _cache[73] || (_cache[73] = _createTextVNode(\" 自动生成 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n      onClick: _cache[9] || (_cache[9] = $event => $data.changePasswordModal.method = 'manual'),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", $data.changePasswordModal.method === 'manual' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'edit'],\n      class: \"mr-2\"\n    }), _cache[74] || (_cache[74] = _createTextVNode(\" 手动输入 \"))], 2 /* CLASS */)])]), $data.changePasswordModal.method === 'auto' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_60, [_cache[78] || (_cache[78] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.changePasswordModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[11] || (_cache[11] = $event => $options.generatePassword())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_61);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.changePasswordModal.policyId]]), _createElementVNode(\"div\", _hoisted_62, [_createElementVNode(\"div\", _hoisted_63, [_cache[77] || (_cache[77] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      onClick: _cache[12] || (_cache[12] = $event => $options.generatePassword()),\n      type: \"button\",\n      class: \"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[76] || (_cache[76] = _createTextVNode(\" 重新生成 \"))])]), _createElementVNode(\"div\", _hoisted_64, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.generated ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.changePasswordModal.generatedPassword = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_65), [[_vModelDynamic, $data.changePasswordModal.generatedPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[14] || (_cache[14] = $event => $data.passwordVisibility.generated = !$data.passwordVisibility.generated),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.generated ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_66, [_createElementVNode(\"div\", _hoisted_67, [_cache[79] || (_cache[79] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"新密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_68, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.new ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => $data.changePasswordModal.newPassword = $event),\n      class: \"form-control flex-1\",\n      placeholder: \"输入新密码\"\n    }, null, 8 /* PROPS */, _hoisted_69), [[_vModelDynamic, $data.changePasswordModal.newPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[16] || (_cache[16] = $event => $data.passwordVisibility.new = !$data.passwordVisibility.new),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.new ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])]), _createElementVNode(\"div\", _hoisted_70, [_cache[80] || (_cache[80] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"确认密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_71, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.confirm ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.changePasswordModal.confirmPassword = $event),\n      class: _normalizeClass([\"form-control flex-1\", {\n        'border-red-500': $options.passwordMismatch\n      }]),\n      placeholder: \"再次输入新密码\"\n    }, null, 10 /* CLASS, PROPS */, _hoisted_72), [[_vModelDynamic, $data.changePasswordModal.confirmPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[18] || (_cache[18] = $event => $data.passwordVisibility.confirm = !$data.passwordVisibility.confirm),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.confirm ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])]), $options.passwordMismatch ? (_openBlock(), _createElementBlock(\"div\", _hoisted_73, \"两次输入的密码不一致\")) : _createCommentVNode(\"v-if\", true)]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.newPassword\n    }, null, 8 /* PROPS */, [\"password\"])])), _createElementVNode(\"div\", _hoisted_74, [_cache[84] || (_cache[84] = _createElementVNode(\"div\", {\n      class: \"form-label font-medium\"\n    }, \"执行选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.executeImmediately,\n      \"onUpdate:modelValue\": _cache[19] || (_cache[19] = $event => $data.changePasswordModal.executeImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[81] || (_cache[81] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"立即执行\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.saveHistory,\n      \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $data.changePasswordModal.saveHistory = $event)\n    }, {\n      default: _withCtx(() => _cache[82] || (_cache[82] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"保存历史记录\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.logAudit,\n      \"onUpdate:modelValue\": _cache[21] || (_cache[21] = $event => $data.changePasswordModal.logAudit = $event)\n    }, {\n      default: _withCtx(() => _cache[83] || (_cache[83] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"记录审计日志\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 添加账号弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.addAccountModal.show,\n    \"onUpdate:modelValue\": _cache[30] || (_cache[30] = $event => $data.addAccountModal.show = $event),\n    title: \"添加账号\",\n    onConfirm: $options.addAccount,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_75, [_cache[87] || (_cache[87] = _createElementVNode(\"div\", {\n      class: \"font-medium mb-2\"\n    }, \"主机信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_76, [_createElementVNode(\"div\", null, [_cache[85] || (_cache[85] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"主机名:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[86] || (_cache[86] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"IP地址:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.ip), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_77, [_cache[88] || (_cache[88] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"账号名称\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.addAccountModal.username = $event),\n      class: \"form-control\",\n      placeholder: \"输入账号名称\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addAccountModal.username]])]), _createElementVNode(\"div\", _hoisted_78, [_cache[90] || (_cache[90] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"设为默认账号\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_79, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.addAccountModal.isDefault = $event),\n      class: \"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.addAccountModal.isDefault]]), _cache[89] || (_cache[89] = _createElementVNode(\"label\", {\n      class: \"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"\n    }, null, -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_80, [_cache[91] || (_cache[91] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.addAccountModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[26] || (_cache[26] = $event => $options.generatePasswordForNewAccount())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_81);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.addAccountModal.policyId]])]), _createElementVNode(\"div\", _hoisted_82, [_createElementVNode(\"div\", _hoisted_83, [_cache[93] || (_cache[93] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      onClick: _cache[27] || (_cache[27] = $event => $options.generatePasswordForNewAccount()),\n      type: \"button\",\n      class: \"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[92] || (_cache[92] = _createTextVNode(\" 重新生成 \"))])]), _createElementVNode(\"div\", _hoisted_84, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.newAccount ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $data.addAccountModal.password = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_85), [[_vModelDynamic, $data.addAccountModal.password]]), _createElementVNode(\"button\", {\n      onClick: _cache[29] || (_cache[29] = $event => $data.passwordVisibility.newAccount = !$data.passwordVisibility.newAccount),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.newAccount ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量更新密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchUpdateModal.show,\n    \"onUpdate:modelValue\": _cache[40] || (_cache[40] = $event => $data.batchUpdateModal.show = $event),\n    title: \"批量更新密码\",\n    \"confirm-text\": \"开始更新\",\n    size: \"lg\",\n    onConfirm: $options.batchUpdatePasswords,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_86, [_cache[95] || (_cache[95] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_87, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatch,\n      \"onUpdate:modelValue\": [_cache[31] || (_cache[31] = $event => $data.selectAllBatch = $event), $options.toggleSelectAllBatch]\n    }, {\n      default: _withCtx(() => _cache[94] || (_cache[94] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_88, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchUpdateModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchUpdateModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_89, \"已选择 \" + _toDisplayString($options.selectedHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_90, [_cache[96] || (_cache[96] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[32] || (_cache[32] = $event => $data.batchUpdateModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_91);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchUpdateModal.policyId]])]), _createElementVNode(\"div\", _hoisted_92, [_cache[101] || (_cache[101] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_93, [_createElementVNode(\"label\", _hoisted_94, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[33] || (_cache[33] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"immediate\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[97] || (_cache[97] = _createElementVNode(\"span\", null, \"立即执行\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_95, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[34] || (_cache[34] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"scheduled\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[98] || (_cache[98] = _createElementVNode(\"span\", null, \"定时执行\", -1 /* HOISTED */))])]), $data.batchUpdateModal.executionTime === 'scheduled' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_96, [_createElementVNode(\"div\", _hoisted_97, [_createElementVNode(\"div\", null, [_cache[99] || (_cache[99] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"日期\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"date\",\n      \"onUpdate:modelValue\": _cache[35] || (_cache[35] = $event => $data.batchUpdateModal.scheduledDate = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledDate]])]), _createElementVNode(\"div\", null, [_cache[100] || (_cache[100] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"时间\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"time\",\n      \"onUpdate:modelValue\": _cache[36] || (_cache[36] = $event => $data.batchUpdateModal.scheduledTime = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledTime]])])])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_98, [_cache[105] || (_cache[105] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[37] || (_cache[37] = $event => $data.batchUpdateModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[102] || (_cache[102] = [_createTextVNode(\" 忽略错误继续执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.detailedLog,\n      \"onUpdate:modelValue\": _cache[38] || (_cache[38] = $event => $data.batchUpdateModal.detailedLog = $event)\n    }, {\n      default: _withCtx(() => _cache[103] || (_cache[103] = [_createTextVNode(\" 记录详细日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.sendNotification,\n      \"onUpdate:modelValue\": _cache[39] || (_cache[39] = $event => $data.batchUpdateModal.sendNotification = $event)\n    }, {\n      default: _withCtx(() => _cache[104] || (_cache[104] = [_createTextVNode(\" 执行完成后发送通知 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量应用策略弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchApplyModal.show,\n    \"onUpdate:modelValue\": _cache[44] || (_cache[44] = $event => $data.batchApplyModal.show = $event),\n    title: \"批量应用密码策略\",\n    \"confirm-text\": \"应用策略\",\n    onConfirm: $options.batchApplyPolicy,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_99, [_cache[106] || (_cache[106] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_100, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchApplyModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchApplyModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_101, [_cache[107] || (_cache[107] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[41] || (_cache[41] = $event => $data.batchApplyModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name), 9 /* TEXT, PROPS */, _hoisted_102);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchApplyModal.policyId]])]), _createElementVNode(\"div\", _hoisted_103, [_cache[110] || (_cache[110] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.updateImmediately,\n      \"onUpdate:modelValue\": _cache[42] || (_cache[42] = $event => $data.batchApplyModal.updateImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[108] || (_cache[108] = [_createTextVNode(\" 立即更新密码以符合策略 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.applyOnNextUpdate,\n      \"onUpdate:modelValue\": _cache[43] || (_cache[43] = $event => $data.batchApplyModal.applyOnNextUpdate = $event)\n    }, {\n      default: _withCtx(() => _cache[109] || (_cache[109] = [_createTextVNode(\" 下次密码更新时应用 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 紧急重置密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.emergencyResetModal.show,\n    \"onUpdate:modelValue\": _cache[48] || (_cache[48] = $event => $data.emergencyResetModal.show = $event),\n    title: \"紧急密码重置\",\n    \"confirm-text\": \"立即重置\",\n    icon: \"exclamation-triangle\",\n    danger: \"\",\n    onConfirm: $options.emergencyReset,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_cache[116] || (_cache[116] = _createElementVNode(\"div\", {\n      class: \"bg-red-50 text-red-700 p-3 rounded-md mb-4\"\n    }, [_createElementVNode(\"p\", null, \"紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_104, [_cache[111] || (_cache[111] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_105, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.emergencyResetModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.emergencyResetModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_106, [_cache[112] || (_cache[112] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用紧急策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[45] || (_cache[45] = $event => $data.emergencyResetModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.emergencyPolicies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_107);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.policyId]])]), _createElementVNode(\"div\", _hoisted_108, [_cache[114] || (_cache[114] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"操作原因\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[46] || (_cache[46] = $event => $data.emergencyResetModal.reason = $event),\n      class: \"form-select\"\n    }, _cache[113] || (_cache[113] = [_createElementVNode(\"option\", {\n      value: \"security_incident\"\n    }, \"安全事件响应\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"password_leak\"\n    }, \"密码泄露\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"abnormal_access\"\n    }, \"异常访问\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"compliance\"\n    }, \"合规要求\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"other\"\n    }, \"其他原因\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.reason]])]), _createElementVNode(\"div\", _hoisted_109, [_cache[115] || (_cache[115] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"附加说明\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n      \"onUpdate:modelValue\": _cache[47] || (_cache[47] = $event => $data.emergencyResetModal.description = $event),\n      class: \"form-control\",\n      rows: \"2\",\n      placeholder: \"请输入重置原因详细说明\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.emergencyResetModal.description]])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "scope", "_createElementBlock", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "args", "$options", "showEmergencyReset", "_createVNode", "_component_font_awesome_icon", "icon", "openBatchUpdateModal", "openBatchApplyModal", "_hoisted_4", "_hoisted_5", "_normalizeClass", "$data", "viewMode", "$event", "_createTextVNode", "_hoisted_6", "type", "filterText", "placeholder", "_hoisted_7", "statusFilter", "value", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_component_CustomCheckbox", "modelValue", "selectAll", "toggleSelectAll", "default", "_withCtx", "_", "_hoisted_12", "_Fragment", "_renderList", "getAllAccounts", "account", "index", "id", "_hoisted_13", "_hoisted_14", "host", "selected", "_hoisted_15", "_toDisplayString", "name", "_hoisted_16", "_hoisted_17", "ip", "_hoisted_18", "_hoisted_19", "_hoisted_20", "username", "isDefault", "_hoisted_21", "_hoisted_22", "_hoisted_23", "lastPasswordChange", "_hoisted_24", "isPasswordExpired", "status", "text", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "passwordVisibility", "password", "readonly", "_hoisted_30", "togglePasswordVisibility", "_hoisted_31", "_hoisted_32", "_component_StatusBadge", "_hoisted_33", "openChangePasswordModal", "_hoisted_34", "_hoisted_35", "filteredHosts", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "accounts", "_hoisted_42", "_hoisted_43", "_hoisted_44", "_hoisted_45", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_hoisted_50", "_hoisted_51", "_hoisted_52", "_hoisted_53", "_hoisted_54", "openAddAccountModal", "_hoisted_55", "_component_BaseModal", "changePasswordModal", "show", "title", "onConfirm", "updatePassword", "loading", "processing", "_hoisted_56", "_hoisted_57", "currentHost", "currentAccount", "_hoisted_58", "_hoisted_59", "method", "_hoisted_60", "policyId", "onChange", "generatePassword", "_ctx", "policies", "policy", "<PERSON><PERSON><PERSON><PERSON>", "expiryDays", "_hoisted_61", "_hoisted_62", "_hoisted_63", "_hoisted_64", "generated", "generatedPassword", "_hoisted_65", "_hoisted_66", "_hoisted_67", "_hoisted_68", "new", "newPassword", "_hoisted_69", "_hoisted_70", "_hoisted_71", "confirm", "confirmPassword", "passwordMismatch", "_hoisted_72", "_hoisted_73", "_component_PasswordStrengthMeter", "_hoisted_74", "executeImmediately", "saveHistory", "logAudit", "addAccountModal", "addAccount", "_hoisted_75", "_hoisted_76", "_hoisted_77", "_hoisted_78", "_hoisted_79", "_hoisted_80", "generatePasswordForNewAccount", "_hoisted_81", "_hoisted_82", "_hoisted_83", "_hoisted_84", "newAccount", "_hoisted_85", "batchUpdateModal", "size", "batchUpdatePasswords", "_hoisted_86", "_hoisted_87", "selectAllBatch", "toggleSelectAllBatch", "_hoisted_88", "hosts", "_createBlock", "selectedHosts", "_hoisted_89", "selectedHostsCount", "_hoisted_90", "_hoisted_91", "_hoisted_92", "_hoisted_93", "_hoisted_94", "executionTime", "_hoisted_95", "_hoisted_96", "_hoisted_97", "scheduledDate", "scheduledTime", "_hoisted_98", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "batchApplyPolicy", "_hoisted_99", "_hoisted_100", "selectedHostsList", "_hoisted_101", "_hoisted_102", "_hoisted_103", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "danger", "emergencyReset", "_hoisted_104", "_hoisted_105", "_hoisted_106", "emergencyPolicies", "_hoisted_107", "_hoisted_108", "reason", "_hoisted_109", "description", "rows"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"bg-white shadow rounded-lg p-4 mb-6\">\r\n      <div class=\"flex flex-wrap items-center justify-between\">\r\n        <div class=\"flex space-x-3 mb-2 sm:mb-0\">\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\r\n            @click=\"showEmergencyReset\">\r\n            <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2\" />\r\n            <span>紧急重置</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            @click=\"openBatchUpdateModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n            <span>批量更新密码</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\r\n            @click=\"openBatchApplyModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n            <span>批量应用策略</span>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"flex items-center space-x-4\">\r\n          <!-- 视图切换 -->\r\n          <div class=\"flex items-center border rounded-md overflow-hidden\">\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'table', 'bg-gray-100 text-gray-600': viewMode !== 'table' }\"\r\n              @click=\"viewMode = 'table'\">\r\n              <font-awesome-icon :icon=\"['fas', 'table']\" class=\"mr-1\" />\r\n              表格\r\n            </button>\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'card', 'bg-gray-100 text-gray-600': viewMode !== 'card' }\"\r\n              @click=\"viewMode = 'card'\">\r\n              <font-awesome-icon :icon=\"['fas', 'th-large']\" class=\"mr-1\" />\r\n              卡片\r\n            </button>\r\n          </div>\r\n\r\n          <!-- 筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"filterText\" placeholder=\"筛选主机...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 状态筛选 -->\r\n          <select v-model=\"statusFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有状态</option>\r\n            <option value=\"normal\">正常</option>\r\n            <option value=\"warning\">警告</option>\r\n            <option value=\"error\">错误</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <!-- 表格视图 -->\r\n    <div v-if=\"viewMode === 'table'\" class=\"bg-white rounded-lg shadow overflow-hidden\">\r\n      <table class=\"min-w-full divide-y divide-gray-200\">\r\n        <thead class=\"bg-gray-50\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n                主机名\r\n              </CustomCheckbox>\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              IP地址\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              账号\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              最后密码修改时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码过期时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              状态\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              操作\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"bg-white divide-y divide-gray-200\">\r\n          <tr v-for=\"(account, index) in getAllAccounts\" :key=\"account.id\"\r\n            :class=\"{ 'bg-gray-50': index % 2 === 0, 'hover:bg-blue-50': true }\">\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"flex items-center\">\r\n                <CustomCheckbox v-model=\"account.host.selected\">\r\n                  <span class=\"ml-2 font-medium text-gray-900\">{{ account.host.name }}</span>\r\n                </CustomCheckbox>\r\n              </div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"text-sm text-gray-900\">{{ account.host.ip }}</div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"flex items-center\">\r\n                <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                <span v-if=\"account.isDefault\"\r\n                  class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                  默认\r\n                </span>\r\n              </div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"text-sm text-gray-500\">{{ account.lastPasswordChange || '-' }}</div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div :class=\"{\r\n                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\r\n                'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n              }\">\r\n                {{ isPasswordExpired(account).text }}\r\n                <span\r\n                  v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                  class=\"ml-1\">\r\n                  <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                </span>\r\n                <span v-else-if=\"isPasswordExpired(account).status === 'warning'\" class=\"ml-1\">\r\n                  <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" />\r\n                </span>\r\n              </div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"flex items-center\">\r\n                <div class=\"flex-grow\">\r\n                  <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\" readonly\r\n                    class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                </div>\r\n                <button @click=\"togglePasswordVisibility(account.id)\"\r\n                  class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                  <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                    class=\"text-lg\" />\r\n                </button>\r\n              </div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <StatusBadge :type=\"account.host.status\" />\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n              <button\r\n                class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                @click=\"openChangePasswordModal(account.host, account)\">\r\n                <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                修改密码\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <!-- 卡片视图 -->\r\n    <div v-else class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\">\r\n      <div v-for=\"host in filteredHosts\" :key=\"host.id\" class=\"bg-white overflow-hidden shadow rounded-lg\">\r\n        <div class=\"px-4 py-5 sm:p-6\">\r\n          <!-- 主机头部 -->\r\n          <div class=\"flex justify-between items-start mb-4\">\r\n            <div class=\"flex items-center\">\r\n              <CustomCheckbox v-model=\"host.selected\" class=\"mr-2\" />\r\n              <div>\r\n                <h3 class=\"text-lg font-medium text-gray-900\">{{ host.name }}</h3>\r\n                <p class=\"text-sm text-gray-500\">{{ host.ip }}</p>\r\n              </div>\r\n            </div>\r\n            <StatusBadge :type=\"host.status\" />\r\n          </div>\r\n\r\n          <!-- 账号列表 -->\r\n          <div class=\"space-y-4\">\r\n            <div v-for=\"account in host.accounts\" :key=\"account.id\" class=\"border border-gray-200 rounded-lg p-3\"\r\n              :class=\"{ 'border-green-300 bg-green-50': account.isDefault }\">\r\n              <div class=\"flex justify-between items-center mb-2\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n                <button\r\n                  class=\"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                  @click=\"openChangePasswordModal(host, account)\">\r\n                  <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                  修改密码\r\n                </button>\r\n              </div>\r\n\r\n              <!-- 密码展示 -->\r\n              <div class=\"mb-2\">\r\n                <div class=\"text-xs font-medium text-gray-500 mb-1\">密码</div>\r\n                <div class=\"flex items-center\">\r\n                  <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\" readonly\r\n                    class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 密码信息区域 -->\r\n              <div class=\"grid grid-cols-2 gap-2 text-xs\">\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">最后修改时间</div>\r\n                  <div class=\"text-gray-900\">{{ account.lastPasswordChange || '-' }}</div>\r\n                </div>\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">密码过期</div>\r\n                  <div :class=\"{\r\n                    'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\r\n                    'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                    'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                    'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                  }\">\r\n                    {{ isPasswordExpired(account).text }}\r\n                    <span\r\n                      v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                      class=\"ml-1\">\r\n                      <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 添加账号按钮 -->\r\n          <div class=\"mt-4 flex justify-center\">\r\n            <button\r\n              class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n              @click=\"openAddAccountModal(host)\">\r\n              <font-awesome-icon :icon=\"['fas', 'plus']\" class=\"mr-1\" />\r\n              添加账号\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" @confirm=\"updatePassword\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n          <div><span class=\"font-medium\">账号:</span> {{ currentAccount.username }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-3\">\r\n          <button @click=\"changePasswordModal.method = 'auto'\"\r\n            class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n            :class=\"changePasswordModal.method === 'auto' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-2\" />\r\n            自动生成\r\n          </button>\r\n          <button @click=\"changePasswordModal.method = 'manual'\"\r\n            class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n            :class=\"changePasswordModal.method === 'manual' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n            <font-awesome-icon :icon=\"['fas', 'edit']\" class=\"mr-2\" />\r\n            手动输入\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-if=\"changePasswordModal.method === 'auto'\" class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"changePasswordModal.policyId\" class=\"form-select\" @change=\"generatePassword()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n\r\n        <div class=\"mt-3\">\r\n          <div class=\"flex justify-between mb-1\">\r\n            <label class=\"form-label\">生成的密码</label>\r\n            <button @click=\"generatePassword()\" type=\"button\"\r\n              class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n              重新生成\r\n            </button>\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.generated ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.generatedPassword\" readonly class=\"form-control flex-1 bg-gray-50\" />\r\n            <button @click=\"passwordVisibility.generated = !passwordVisibility.generated\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.generated ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else class=\"space-y-4\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.new ? 'text' : 'password'\" v-model=\"changePasswordModal.newPassword\"\r\n              class=\"form-control flex-1\" placeholder=\"输入新密码\" />\r\n            <button @click=\"passwordVisibility.new = !passwordVisibility.new\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.new ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.confirm ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.confirmPassword\" class=\"form-control flex-1\"\r\n              :class=\"{ 'border-red-500': passwordMismatch }\" placeholder=\"再次输入新密码\" />\r\n            <button @click=\"passwordVisibility.confirm = !passwordVisibility.confirm\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.confirm ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n          <div v-if=\"passwordMismatch\" class=\"text-sm text-red-500 mt-1\">两次输入的密码不一致</div>\r\n        </div>\r\n\r\n        <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n      </div>\r\n\r\n      <div class=\"space-y-2 mt-4\">\r\n        <div class=\"form-label font-medium\">执行选项</div>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          <span class=\"ml-2\">立即执行</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          <span class=\"ml-2\">保存历史记录</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          <span class=\"ml-2\">记录审计日志</span>\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 添加账号弹窗 -->\r\n    <BaseModal v-model=\"addAccountModal.show\" title=\"添加账号\" @confirm=\"addAccount\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">账号名称</label>\r\n        <input type=\"text\" v-model=\"addAccountModal.username\" class=\"form-control\" placeholder=\"输入账号名称\" />\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">设为默认账号</label>\r\n        <div class=\"relative inline-block w-10 mr-2 align-middle select-none\">\r\n          <input type=\"checkbox\" v-model=\"addAccountModal.isDefault\"\r\n            class=\"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\" />\r\n          <label class=\"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"></label>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"addAccountModal.policyId\" class=\"form-select\" @change=\"generatePasswordForNewAccount()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <div class=\"flex justify-between mb-1\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <button @click=\"generatePasswordForNewAccount()\" type=\"button\"\r\n            class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n            重新生成\r\n          </button>\r\n        </div>\r\n        <div class=\"flex items-center\">\r\n          <input :type=\"passwordVisibility.newAccount ? 'text' : 'password'\" v-model=\"addAccountModal.password\" readonly\r\n            class=\"form-control flex-1 bg-gray-50\" />\r\n          <button @click=\"passwordVisibility.newAccount = !passwordVisibility.newAccount\" type=\"button\"\r\n            class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', passwordVisibility.newAccount ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      currentAccount: {},\r\n      passwordVisibility: {\r\n        generated: false,\r\n        new: false,\r\n        confirm: false,\r\n        newAccount: false\r\n      },\r\n      viewMode: 'table',\r\n      filterText: '',\r\n      statusFilter: 'all',\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      },\r\n\r\n      // 添加账号弹窗\r\n      addAccountModal: {\r\n        show: false,\r\n        username: '',\r\n        password: '',\r\n        isDefault: false,\r\n        policyId: 1\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    },\r\n\r\n    // 过滤后的主机列表\r\n    filteredHosts() {\r\n      return this.hosts.filter(host => {\r\n        // 文本过滤\r\n        const textMatch = this.filterText === '' ||\r\n          host.name.toLowerCase().includes(this.filterText.toLowerCase()) ||\r\n          host.ip.includes(this.filterText);\r\n\r\n        // 状态过滤\r\n        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;\r\n\r\n        return textMatch && statusMatch;\r\n      });\r\n    },\r\n\r\n    // 获取所有账号（扁平化处理）\r\n    getAllAccounts() {\r\n      // 为每个账号添加主机引用\r\n      const accounts = [];\r\n      this.filteredHosts.forEach(host => {\r\n        host.accounts.forEach(account => {\r\n          accounts.push({\r\n            ...account,\r\n            host: host\r\n          });\r\n        });\r\n      });\r\n      return accounts;\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host, account) {\r\n      this.currentHost = host\r\n      this.currentAccount = account\r\n      this.changePasswordModal.show = true\r\n      this.changePasswordModal.generatedPassword = this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword(policy) {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policyObj ? policyObj.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      if (this.changePasswordModal && !policy) {\r\n        this.changePasswordModal.generatedPassword = password\r\n      }\r\n\r\n      return password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto'\r\n          ? this.changePasswordModal.generatedPassword\r\n          : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          accountId: this.currentAccount.id,\r\n          password: password,\r\n          policyId: this.changePasswordModal.policyId\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的 ${this.currentAccount.username} 账号密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取所选策略\r\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取紧急策略\r\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    togglePasswordVisibility(hostId) {\r\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]\r\n    },\r\n\r\n    isPasswordExpired(account) {\r\n      if (!account.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }\r\n\r\n      // 解析过期时间\r\n      const expiryDate = new Date(account.passwordExpiryDate)\r\n      const now = new Date()\r\n\r\n      // 如果已过期\r\n      if (expiryDate < now) {\r\n        return {\r\n          status: 'expired',\r\n          days: 0,\r\n          text: '已过期'\r\n        }\r\n      }\r\n\r\n      // 计算剩余天数和小时数\r\n      const diffTime = expiryDate - now\r\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))\r\n      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n\r\n      // 根据剩余时间确定状态\r\n      let status = 'normal'\r\n      if (diffDays < 7) {\r\n        status = 'danger'  // 少于7天\r\n      } else if (diffDays < 14) {\r\n        status = 'warning' // 少于14天\r\n      }\r\n\r\n      // 格式化显示文本\r\n      let text = ''\r\n      if (diffDays > 0) {\r\n        text += `${diffDays}天`\r\n      }\r\n      if (diffHours > 0 || diffDays === 0) {\r\n        text += `${diffHours}小时`\r\n      }\r\n\r\n      return { status, days: diffDays, text: `剩余${text}` }\r\n    },\r\n\r\n    openAddAccountModal(host) {\r\n      this.currentHost = host\r\n      this.addAccountModal.show = true\r\n    },\r\n\r\n    async addAccount() {\r\n      if (!this.addAccountModal.username || !this.addAccountModal.password) {\r\n        alert('请填写完整的账号信息！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('addHostAccount', {\r\n          hostId: this.currentHost.id,\r\n          username: this.addAccountModal.username,\r\n          password: this.addAccountModal.password,\r\n          policyId: this.addAccountModal.policyId,\r\n          isDefault: this.addAccountModal.isDefault\r\n        })\r\n\r\n        this.addAccountModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为主机 ${this.currentHost.name} 添加账号！`)\r\n      } catch (error) {\r\n        console.error('添加账号失败', error)\r\n        alert('添加账号失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    generatePasswordForNewAccount() {\r\n      this.addAccountModal.password = this.generatePassword()\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": ";;EAGSA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAA6C;;EACjDA,KAAK,EAAC;AAA6B;;EAqBnCA,KAAK,EAAC;AAA6B;;EAEjCA,KAAK,EAAC;AAAqD;;EAgB3DA,KAAK,EAAC;AAAU;;EAGdA,KAAK,EAAC;AAAsE;;EA/C7FC,GAAA;EAkEqCD,KAAK,EAAC;;;EAC9BA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAAY;;EAEjBE,KAAK,EAAC,KAAK;EAACF,KAAK,EAAC;;;EA4BnBA,KAAK,EAAC;AAAmC;;EAGxCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EAEpBA,KAAK,EAAC;AAAgC;;EAI9CA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAmC;;EAjH/DC,GAAA;EAmHkBD,KAAK,EAAC;;;EAKRA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EA3HnDC,GAAA;EAqIkBD,KAAK,EAAC;;;EArIxBC,GAAA;EAwIkFD,KAAK,EAAC;;;EAKxEA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAW;oBA/ItC;oBAAA;;EA0JgBA,KAAK,EAAC;AAA6B;;EAGnCA,KAAK,EAAC;AAAmD;oBA7JzE;;EA2KgBA,KAAK,EAAC;AAAsD;;EAE/DA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAuC;;EAC3CA,KAAK,EAAC;AAAmB;;EAGtBA,KAAK,EAAC;AAAmC;;EAC1CA,KAAK,EAAC;AAAuB;;EAOjCA,KAAK,EAAC;AAAW;;EAGbA,KAAK,EAAC;AAAwC;;EAC5CA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAmC;;EAhMjEC,GAAA;EAkMoBD,KAAK,EAAC;;oBAlM1B;;EA+MmBA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAmB;oBAjN9C;oBAAA;;EA6NmBA,KAAK,EAAC;AAAgC;;EAGlCA,KAAK,EAAC;AAAe;;EAhO5CC,GAAA;EA6OsBD,KAAK,EAAC;;;EAUbA,KAAK,EAAC;AAA0B;oBAvP/C;;EAqQWA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAiC;;EAOzCA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAgB;;EAhRnCC,GAAA;EAgSwDD,KAAK,EAAC;;oBAhS9D;;EAwSaA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAA2B;;EAQjCA,KAAK,EAAC;AAAmB;oBAjTxC;;EAAAC,GAAA;EA4TkBD,KAAK,EAAC;;;EACXA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;oBA/TxC;;EAyUaA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;oBA3UxC;;EAAAC,GAAA;EAoVuCD,KAAK,EAAC;;;EAMlCA,KAAK,EAAC;AAAgB;;EAgBtBA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAiC;;EAMzCA,KAAK,EAAC;AAAiB;;EAKvBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAA0D;;EAOlEA,KAAK,EAAC;AAAiB;oBAhYlC;;EAyYWA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAA2B;;EAQjCA,KAAK,EAAC;AAAmB;oBAlZtC;;EAgaWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAY;oBA/a7B;;EAwbWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EA/b1CC,GAAA;EAqcmED,KAAK,EAAC;;;EAC1DA,KAAK,EAAC;AAAwB;;EAalCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;qBA9e7B;;EAufWA,KAAK,EAAC;AAAY;;EAkBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;qBAnhB7B;;EA4hBWA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;;;;;;uBAtiB3BG,mBAAA,CA4iBM,cA3iBJC,mBAAA,UAAa,EACbC,mBAAA,CA2DM,OA3DNC,UA2DM,GA1DJD,mBAAA,CAyDM,OAzDNE,UAyDM,GAxDJF,mBAAA,CAmBM,OAnBNG,UAmBM,GAlBJH,mBAAA,CAKS;IAJPL,KAAK,EAAC,qNAAqN;IAC1NS,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,kBAAA,IAAAD,QAAA,CAAAC,kBAAA,IAAAF,IAAA,CAAkB;MAC1BG,YAAA,CAA0EC,4BAAA;IAAtDC,IAAI,EAAE,+BAA+B;IAAEhB,KAAK,EAAC;kCACjEK,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAKS;IAJPL,KAAK,EAAC,wNAAwN;IAC7NS,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAK,oBAAA,IAAAL,QAAA,CAAAK,oBAAA,IAAAN,IAAA,CAAoB;MAC5BG,YAAA,CAAyDC,4BAAA;IAArCC,IAAI,EAAE,cAAc;IAAEhB,KAAK,EAAC;kCAChDK,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAKS;IAJPL,KAAK,EAAC,8NAA8N;IACnOS,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAM,mBAAA,IAAAN,QAAA,CAAAM,mBAAA,IAAAP,IAAA,CAAmB;MAC3BG,YAAA,CAAgEC,4BAAA;IAA5CC,IAAI,EAAE,qBAAqB;IAAEhB,KAAK,EAAC;kCACvDK,mBAAA,CAAmB,cAAb,QAAM,qB,KAIhBA,mBAAA,CAkCM,OAlCNc,UAkCM,GAjCJf,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNe,UAaM,GAZJf,mBAAA,CAKS;IALDL,KAAK,EA7BzBqB,eAAA,EA6B0B,8BAA8B;MAAA,0BACNC,KAAA,CAAAC,QAAQ;MAAA,6BAA2CD,KAAA,CAAAC,QAAQ;IAAA;IAC9Fd,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAChBT,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEhB,KAAK,EAAC;kCAhChEyB,gBAAA,CAgCyE,MAE7D,G,kBACApB,mBAAA,CAKS;IALDL,KAAK,EAnCzBqB,eAAA,EAmC0B,8BAA8B;MAAA,0BACNC,KAAA,CAAAC,QAAQ;MAAA,6BAA0CD,KAAA,CAAAC,QAAQ;IAAA;IAC7Fd,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAChBT,YAAA,CAA8DC,4BAAA;IAA1CC,IAAI,EAAE,mBAAmB;IAAEhB,KAAK,EAAC;kCAtCnEyB,gBAAA,CAsC4E,MAEhE,G,oBAGFrB,mBAAA,QAAW,EACXC,mBAAA,CAMM,OANNqB,UAMM,G,gBALJrB,mBAAA,CAC2L;IADpLsB,IAAI,EAAC,MAAM;IA7C9B,uBAAAjB,MAAA,QAAAA,MAAA,MAAAc,MAAA,IA6CwCF,KAAA,CAAAM,UAAU,GAAAJ,MAAA;IAAEK,WAAW,EAAC,SAAS;IAC3D7B,KAAK,EAAC;iDADoBsB,KAAA,CAAAM,UAAU,E,GAEtCvB,mBAAA,CAEM,OAFNyB,UAEM,GADJhB,YAAA,CAAqEC,4BAAA;IAAjDC,IAAI,EAAE,iBAAiB;IAAEhB,KAAK,EAAC;UAIvDI,mBAAA,UAAa,E,gBACbC,mBAAA,CAMS;IA3DnB,uBAAAK,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAqD2BF,KAAA,CAAAS,YAAY,GAAAP,MAAA;IAC3BxB,KAAK,EAAC;kCACNK,mBAAA,CAAiC;IAAzB2B,KAAK,EAAC;EAAK,GAAC,MAAI,qBACxB3B,mBAAA,CAAkC;IAA1B2B,KAAK,EAAC;EAAQ,GAAC,IAAE,qBACzB3B,mBAAA,CAAmC;IAA3B2B,KAAK,EAAC;EAAS,GAAC,IAAE,qBAC1B3B,mBAAA,CAAiC;IAAzB2B,KAAK,EAAC;EAAO,GAAC,IAAE,oB,2CALTV,KAAA,CAAAS,YAAY,E,SAWnC3B,mBAAA,UAAa,EACbA,mBAAA,UAAa,EACFkB,KAAA,CAAAC,QAAQ,gB,cAAnBpB,mBAAA,CAsGM,OAtGN8B,UAsGM,GArGJ5B,mBAAA,CAoGQ,SApGR6B,UAoGQ,GAnGN7B,mBAAA,CA6BQ,SA7BR8B,WA6BQ,GA5BN9B,mBAAA,CA2BK,aA1BHA,mBAAA,CAIK,MAJL+B,WAIK,GAHHtB,YAAA,CAEiBuB,yBAAA;IAzE/BC,UAAA,EAuEuChB,KAAA,CAAAiB,SAAS;IAvEhD,wB,oCAuEuCjB,KAAA,CAAAiB,SAAS,GAAAf,MAAA,GAAsBZ,QAAA,CAAA4B,eAAe;;IAvErFC,OAAA,EAAAC,QAAA,CAuEuF,MAEzEhC,MAAA,SAAAA,MAAA,QAzEde,gBAAA,CAuEuF,OAEzE,E;IAzEdkB,CAAA;0FA2EYtC,mBAAA,CAEK;IAFDH,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,QAEvG,sB,4BACAK,mBAAA,CAEK;IAFDH,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAK,mBAAA,CAEK;IAFDH,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,YAEvG,sB,4BACAK,mBAAA,CAEK;IAFDH,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,UAEvG,sB,4BACAK,mBAAA,CAEK;IAFDH,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAK,mBAAA,CAEK;IAFDH,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAK,mBAAA,CAEK;IAFDH,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,qB,KAGJK,mBAAA,CAoEQ,SApERuC,WAoEQ,I,kBAnENzC,mBAAA,CAkEK0C,SAAA,QArKfC,WAAA,CAmGyClC,QAAA,CAAAmC,cAAc,EAnGvD,CAmGsBC,OAAO,EAAEC,KAAK;yBAA1B9C,mBAAA,CAkEK;MAlE2CF,GAAG,EAAE+C,OAAO,CAACE,EAAE;MAC5DlD,KAAK,EApGlBqB,eAAA;QAAA,cAoGoC4B,KAAK;QAAA;MAAA;QAC7B5C,mBAAA,CAMK,MANL8C,WAMK,GALH9C,mBAAA,CAIM,OAJN+C,WAIM,GAHJtC,YAAA,CAEiBuB,yBAAA;MAzGjCC,UAAA,EAuGyCU,OAAO,CAACK,IAAI,CAACC,QAAQ;MAvG9D,uBAAA9B,MAAA,IAuGyCwB,OAAO,CAACK,IAAI,CAACC,QAAQ,GAAA9B;;MAvG9DiB,OAAA,EAAAC,QAAA,CAwGkB,MAA2E,CAA3ErC,mBAAA,CAA2E,QAA3EkD,WAA2E,EAAAC,gBAAA,CAA3BR,OAAO,CAACK,IAAI,CAACI,IAAI,iB;MAxGnFd,CAAA;oFA4GYtC,mBAAA,CAEK,MAFLqD,WAEK,GADHrD,mBAAA,CAA8D,OAA9DsD,WAA8D,EAAAH,gBAAA,CAAxBR,OAAO,CAACK,IAAI,CAACO,EAAE,iB,GAEvDvD,mBAAA,CAQK,MARLwD,WAQK,GAPHxD,mBAAA,CAMM,OANNyD,WAMM,GALJzD,mBAAA,CAA6E,QAA7E0D,WAA6E,EAAAP,gBAAA,CAA1BR,OAAO,CAACgB,QAAQ,kBACvDhB,OAAO,CAACiB,SAAS,I,cAA7B9D,mBAAA,CAGO,QAHP+D,WAGO,EAFqG,MAE5G,KArHhB9D,mBAAA,e,KAwHYC,mBAAA,CAEK,MAFL8D,WAEK,GADH9D,mBAAA,CAAgF,OAAhF+D,WAAgF,EAAAZ,gBAAA,CAA1CR,OAAO,CAACqB,kBAAkB,wB,GAElEhE,mBAAA,CAiBK,MAjBLiE,WAiBK,GAhBHjE,mBAAA,CAeM;MAfAL,KAAK,EA5HzBqB,eAAA;;mCA4H2KT,QAAA,CAAA2D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM,iBAAiB5D,QAAA,CAAA2D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM;yCAAkE5D,QAAA,CAAA2D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM;qCAA8D5D,QAAA,CAAA2D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM;;QA5Hhc/C,gBAAA,CAAA+B,gBAAA,CAkImB5C,QAAA,CAAA2D,iBAAiB,CAACvB,OAAO,EAAEyB,IAAI,IAAG,GACrC,iBACQ7D,QAAA,CAAA2D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM,kBAAkB5D,QAAA,CAAA2D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM,iB,cAD5FrE,mBAAA,CAIO,QAJPuE,WAIO,GADL5D,YAAA,CAA6DC,4BAAA;MAAzCC,IAAI,EAAE;IAA+B,G,KAE1CJ,QAAA,CAAA2D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM,kB,cAAlDrE,mBAAA,CAEO,QAFPwE,WAEO,GADL7D,YAAA,CAA2DC,4BAAA;MAAvCC,IAAI,EAAE;IAA6B,G,KAzIzEZ,mBAAA,e,oBA6IYC,mBAAA,CAYK,MAZLuE,WAYK,GAXHvE,mBAAA,CAUM,OAVNwE,WAUM,GATJxE,mBAAA,CAGM,OAHNyE,WAGM,GAFJzE,mBAAA,CACkG;MAD1FsB,IAAI,EAAEL,KAAA,CAAAyD,kBAAkB,CAAC/B,OAAO,CAACE,EAAE;MAA0BlB,KAAK,EAAEgB,OAAO,CAACgC,QAAQ;MAAEC,QAAQ,EAAR,EAAQ;MACpGjF,KAAK,EAAC;4BAjJ1BkF,WAAA,E,GAmJgB7E,mBAAA,CAIS;MAJAI,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAAuE,wBAAwB,CAACnC,OAAO,CAACE,EAAE;MACjDlD,KAAK,EAAC;QACNc,YAAA,CACoBC,4BAAA;MADAC,IAAI,UAAUM,KAAA,CAAAyD,kBAAkB,CAAC/B,OAAO,CAACE,EAAE;MAC7DlD,KAAK,EAAC;uDAtJ1BoF,WAAA,E,KA0JY/E,mBAAA,CAEK,MAFLgF,WAEK,GADHvE,YAAA,CAA2CwE,sBAAA;MAA7B3D,IAAI,EAAEqB,OAAO,CAACK,IAAI,CAACmB;yCAEnCnE,mBAAA,CAOK,MAPLkF,WAOK,GANHlF,mBAAA,CAKS;MAJPL,KAAK,EAAC,0NAA0N;MAC/NS,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAA4E,uBAAuB,CAACxC,OAAO,CAACK,IAAI,EAAEL,OAAO;QACrDlC,YAAA,CAAyDC,4BAAA;MAArCC,IAAI,EAAE,cAAc;MAAEhB,KAAK,EAAC;oCAjKhEyB,gBAAA,CAiKyE,QAE3D,G,iBAnKdgE,WAAA,E;yDA2KItF,mBAAA,CAsFM0C,SAAA;IAjQV5C,GAAA;EAAA,IA0KIG,mBAAA,UAAa,EACbC,mBAAA,CAsFM,OAtFNqF,WAsFM,I,kBArFJvF,mBAAA,CAoFM0C,SAAA,QAhQZC,WAAA,CA4K0BlC,QAAA,CAAA+E,aAAa,EAArBtC,IAAI;yBAAhBlD,mBAAA,CAoFM;MApF8BF,GAAG,EAAEoD,IAAI,CAACH,EAAE;MAAElD,KAAK,EAAC;QACtDK,mBAAA,CAkFM,OAlFNuF,WAkFM,GAjFJxF,mBAAA,UAAa,EACbC,mBAAA,CASM,OATNwF,WASM,GARJxF,mBAAA,CAMM,OANNyF,WAMM,GALJhF,YAAA,CAAuDuB,yBAAA;MAjLrEC,UAAA,EAiLuCe,IAAI,CAACC,QAAQ;MAjLpD,uBAAA9B,MAAA,IAiLuC6B,IAAI,CAACC,QAAQ,GAAA9B,MAAA;MAAExB,KAAK,EAAC;oEAC9CK,mBAAA,CAGM,cAFJA,mBAAA,CAAkE,MAAlE0F,WAAkE,EAAAvC,gBAAA,CAAjBH,IAAI,CAACI,IAAI,kBAC1DpD,mBAAA,CAAkD,KAAlD2F,WAAkD,EAAAxC,gBAAA,CAAdH,IAAI,CAACO,EAAE,iB,KAG/C9C,YAAA,CAAmCwE,sBAAA;MAArB3D,IAAI,EAAE0B,IAAI,CAACmB;yCAG3BpE,mBAAA,UAAa,EACbC,mBAAA,CAyDM,OAzDN4F,WAyDM,I,kBAxDJ9F,mBAAA,CAuDM0C,SAAA,QAnPlBC,WAAA,CA4LmCO,IAAI,CAAC6C,QAAQ,EAAxBlD,OAAO;2BAAnB7C,mBAAA,CAuDM;QAvDiCF,GAAG,EAAE+C,OAAO,CAACE,EAAE;QAAElD,KAAK,EA5LzEqB,eAAA,EA4L0E,uCAAuC;UAAA,gCACzD2B,OAAO,CAACiB;QAAS;UAC3D5D,mBAAA,CAcM,OAdN8F,WAcM,GAbJ9F,mBAAA,CAMM,OANN+F,WAMM,GALJ/F,mBAAA,CAA6E,QAA7EgG,WAA6E,EAAA7C,gBAAA,CAA1BR,OAAO,CAACgB,QAAQ,kBACvDhB,OAAO,CAACiB,SAAS,I,cAA7B9D,mBAAA,CAGO,QAHPmG,WAGO,EAFqG,MAE5G,KApMlBlG,mBAAA,e,GAsMgBC,mBAAA,CAKS;QAJPL,KAAK,EAAC,wNAAwN;QAC7NS,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAA4E,uBAAuB,CAACnC,IAAI,EAAEL,OAAO;UAC7ClC,YAAA,CAAyDC,4BAAA;QAArCC,IAAI,EAAE,cAAc;QAAEhB,KAAK,EAAC;sCAzMlEyB,gBAAA,CAyM2E,QAE3D,G,iBA3MhB8E,WAAA,E,GA8McnG,mBAAA,UAAa,EACbC,mBAAA,CAWM,OAXNmG,WAWM,G,4BAVJnG,mBAAA,CAA4D;QAAvDL,KAAK,EAAC;MAAwC,GAAC,IAAE,sBACtDK,mBAAA,CAQM,OARNoG,WAQM,GAPJpG,mBAAA,CACkG;QAD1FsB,IAAI,EAAEL,KAAA,CAAAyD,kBAAkB,CAAC/B,OAAO,CAACE,EAAE;QAA0BlB,KAAK,EAAEgB,OAAO,CAACgC,QAAQ;QAAEC,QAAQ,EAAR,EAAQ;QACpGjF,KAAK,EAAC;8BAnN1B0G,WAAA,GAoNkBrG,mBAAA,CAIS;QAJAI,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAAuE,wBAAwB,CAACnC,OAAO,CAACE,EAAE;QACjDlD,KAAK,EAAC;UACNc,YAAA,CACoBC,4BAAA;QADAC,IAAI,UAAUM,KAAA,CAAAyD,kBAAkB,CAAC/B,OAAO,CAACE,EAAE;QAC7DlD,KAAK,EAAC;yDAvN5B2G,WAAA,E,KA4NcvG,mBAAA,YAAe,EACfC,mBAAA,CAqBM,OArBNuG,WAqBM,GApBJvG,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAwD;QAAnDL,KAAK,EAAC;MAAgC,GAAC,QAAM,sBAClDK,mBAAA,CAAwE,OAAxEwG,WAAwE,EAAArD,gBAAA,CAA1CR,OAAO,CAACqB,kBAAkB,wB,GAE1DhE,mBAAA,CAeM,c,4BAdJA,mBAAA,CAAsD;QAAjDL,KAAK,EAAC;MAAgC,GAAC,MAAI,sBAChDK,mBAAA,CAYM;QAZAL,KAAK,EApO7BqB,eAAA;;qCAoOqLT,QAAA,CAAA2D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM,iBAAiB5D,QAAA,CAAA2D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM;2CAAsE5D,QAAA,CAAA2D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM;uCAAkE5D,QAAA,CAAA2D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM;;UApOld/C,gBAAA,CAAA+B,gBAAA,CA0OuB5C,QAAA,CAAA2D,iBAAiB,CAACvB,OAAO,EAAEyB,IAAI,IAAG,GACrC,iBACQ7D,QAAA,CAAA2D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM,kBAAkB5D,QAAA,CAAA2D,iBAAiB,CAACvB,OAAO,EAAEwB,MAAM,iB,cAD5FrE,mBAAA,CAIO,QAJP2G,WAIO,GADLhG,YAAA,CAA6DC,4BAAA;QAAzCC,IAAI,EAAE;MAA+B,G,KA9O/EZ,mBAAA,e;sCAsPUA,mBAAA,YAAe,EACfC,mBAAA,CAOM,OAPN0G,WAOM,GANJ1G,mBAAA,CAKS;MAJPL,KAAK,EAAC,sNAAsN;MAC3NS,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAAoG,mBAAmB,CAAC3D,IAAI;QAChCvC,YAAA,CAA0DC,4BAAA;MAAtCC,IAAI,EAAE,eAAe;MAAEhB,KAAK,EAAC;oCA3P/DyB,gBAAA,CA2PwE,QAE5D,G,iBA7PZwF,WAAA,E;sFAmQI7G,mBAAA,YAAe,EACfU,YAAA,CAkGYoG,oBAAA;IAtWhB5E,UAAA,EAoQwBhB,KAAA,CAAA6F,mBAAmB,CAACC,IAAI;IApQhD,uBAAA1G,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAoQwBF,KAAA,CAAA6F,mBAAmB,CAACC,IAAI,GAAA5F,MAAA;IAAE6F,KAAK,EAAC,MAAM;IAAEC,SAAO,EAAE1G,QAAA,CAAA2G,cAAc;IAAGC,OAAO,EAAElG,KAAA,CAAAmG;;IApQnGhF,OAAA,EAAAC,QAAA,CAqQM,MAOM,CAPNrC,mBAAA,CAOM,OAPNqH,WAOM,G,4BANJrH,mBAAA,CAAwC;MAAnCL,KAAK,EAAC;IAAkB,GAAC,MAAI,sBAClCK,mBAAA,CAIM,OAJNsH,WAIM,GAHJtH,mBAAA,CAAuE,c,4BAAlEA,mBAAA,CAAqC;MAA/BL,KAAK,EAAC;IAAa,GAAC,MAAI,sBAxQ7CyB,gBAAA,CAwQoD,GAAC,GAAA+B,gBAAA,CAAGlC,KAAA,CAAAsG,WAAW,CAACnE,IAAI,iB,GAC9DpD,mBAAA,CAAsE,c,4BAAjEA,mBAAA,CAAsC;MAAhCL,KAAK,EAAC;IAAa,GAAC,OAAK,sBAzQ9CyB,gBAAA,CAyQqD,GAAC,GAAA+B,gBAAA,CAAGlC,KAAA,CAAAsG,WAAW,CAAChE,EAAE,iB,GAC7DvD,mBAAA,CAA6E,c,4BAAxEA,mBAAA,CAAoC;MAA9BL,KAAK,EAAC;IAAa,GAAC,KAAG,sBA1Q5CyB,gBAAA,CA0QmD,GAAC,GAAA+B,gBAAA,CAAGlC,KAAA,CAAAuG,cAAc,CAAC7D,QAAQ,iB,OAIxE3D,mBAAA,CAgBM,OAhBNyH,WAgBM,G,4BAfJzH,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CAaM,OAbN0H,WAaM,GAZJ1H,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAAEF,KAAA,CAAA6F,mBAAmB,CAACa,MAAM;MACxChI,KAAK,EAlRjBqB,eAAA,EAkRkB,iFAAiF,EAC/EC,KAAA,CAAA6F,mBAAmB,CAACa,MAAM;QAClClH,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEhB,KAAK,EAAC;oCApRjEyB,gBAAA,CAoR0E,QAEhE,G,kBACApB,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAAEF,KAAA,CAAA6F,mBAAmB,CAACa,MAAM;MACxChI,KAAK,EAxRjBqB,eAAA,EAwRkB,iFAAiF,EAC/EC,KAAA,CAAA6F,mBAAmB,CAACa,MAAM;QAClClH,YAAA,CAA0DC,4BAAA;MAAtCC,IAAI,EAAE,eAAe;MAAEhB,KAAK,EAAC;oCA1R7DyB,gBAAA,CA0RsE,QAE5D,G,sBAIOH,KAAA,CAAA6F,mBAAmB,CAACa,MAAM,e,cAArC7H,mBAAA,CA0BM,OA1BN8H,WA0BM,G,4BAzBJ5H,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CAIS;MAtSjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAkSyBF,KAAA,CAAA6F,mBAAmB,CAACe,QAAQ,GAAA1G,MAAA;MAAExB,KAAK,EAAC,aAAa;MAAEmI,QAAM,EAAAzH,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEZ,QAAA,CAAAwH,gBAAgB;2BAC1FjI,mBAAA,CAES0C,SAAA,QArSnBC,WAAA,CAmSmCuF,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArBpI,mBAAA,CAES;QAF2BF,GAAG,EAAEsI,MAAM,CAACrF,EAAE;QAAGlB,KAAK,EAAEuG,MAAM,CAACrF;0BAC9DqF,MAAM,CAAC9E,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAG+E,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAhF,gBAAA,CAAG+E,MAAM,CAACE,UAAU,IAAG,KAC9E,uBArSVC,WAAA;6FAkSyBpH,KAAA,CAAA6F,mBAAmB,CAACe,QAAQ,E,GAM7C7H,mBAAA,CAiBM,OAjBNsI,WAiBM,GAhBJtI,mBAAA,CAOM,OAPNuI,WAOM,G,4BANJvI,mBAAA,CAAuC;MAAhCL,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BK,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEZ,QAAA,CAAAwH,gBAAgB;MAAIzG,IAAI,EAAC,QAAQ;MAC/C3B,KAAK,EAAC;QACNc,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEhB,KAAK,EAAC;oCA7SnEyB,gBAAA,CA6S4E,QAEhE,G,KAEFpB,mBAAA,CAOM,OAPNwI,WAOM,G,gBANJxI,mBAAA,CACoG;MAD5FsB,IAAI,EAAEL,KAAA,CAAAyD,kBAAkB,CAAC+D,SAAS;MAlTtD,uBAAApI,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAmTuBF,KAAA,CAAA6F,mBAAmB,CAAC4B,iBAAiB,GAAAvH,MAAA;MAAEyD,QAAQ,EAAR,EAAQ;MAACjF,KAAK,EAAC;4BAnT7EgJ,WAAA,I,iBAmTuB1H,KAAA,CAAA6F,mBAAmB,CAAC4B,iBAAiB,E,GAChD1I,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAAyD,kBAAkB,CAAC+D,SAAS,IAAIxH,KAAA,CAAAyD,kBAAkB,CAAC+D,SAAS;MAAEnH,IAAI,EAAC,QAAQ;MACzF3B,KAAK,EAAC;QACNc,YAAA,CAAyGC,4BAAA;MAArFC,IAAI,UAAUM,KAAA,CAAAyD,kBAAkB,CAAC+D,SAAS;MAAyB9I,KAAK,EAAC;gEAMrGG,mBAAA,CA4BM,OA5BN8I,WA4BM,GA3BJ5I,mBAAA,CAUM,OAVN6I,WAUM,G,4BATJ7I,mBAAA,CAAqC;MAA9BL,KAAK,EAAC;IAAY,GAAC,KAAG,sBAC7BK,mBAAA,CAOM,OAPN8I,WAOM,G,gBANJ9I,mBAAA,CACoD;MAD5CsB,IAAI,EAAEL,KAAA,CAAAyD,kBAAkB,CAACqE,GAAG;MAhUhD,uBAAA1I,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAgUiFF,KAAA,CAAA6F,mBAAmB,CAACkC,WAAW,GAAA7H,MAAA;MAClGxB,KAAK,EAAC,qBAAqB;MAAC6B,WAAW,EAAC;4BAjUtDyH,WAAA,I,iBAgUiFhI,KAAA,CAAA6F,mBAAmB,CAACkC,WAAW,E,GAEpGhJ,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAAyD,kBAAkB,CAACqE,GAAG,IAAI9H,KAAA,CAAAyD,kBAAkB,CAACqE,GAAG;MAAEzH,IAAI,EAAC,QAAQ;MAC7E3B,KAAK,EAAC;QACNc,YAAA,CAAmGC,4BAAA;MAA/EC,IAAI,UAAUM,KAAA,CAAAyD,kBAAkB,CAACqE,GAAG;MAAyBpJ,KAAK,EAAC;6CAK7FK,mBAAA,CAYM,OAZNkJ,WAYM,G,4BAXJlJ,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BK,mBAAA,CAQM,OARNmJ,WAQM,G,gBAPJnJ,mBAAA,CAE0E;MAFlEsB,IAAI,EAAEL,KAAA,CAAAyD,kBAAkB,CAAC0E,OAAO;MA5UpD,uBAAA/I,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA6UuBF,KAAA,CAAA6F,mBAAmB,CAACuC,eAAe,GAAAlI,MAAA;MAAExB,KAAK,EA7UjEqB,eAAA,EA6UkE,qBAAqB;QAAA,kBAC7CT,QAAA,CAAA+I;MAAgB;MAAI9H,WAAW,EAAC;oCA9U1E+H,WAAA,I,iBA6UuBtI,KAAA,CAAA6F,mBAAmB,CAACuC,eAAe,E,GAE9CrJ,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAAyD,kBAAkB,CAAC0E,OAAO,IAAInI,KAAA,CAAAyD,kBAAkB,CAAC0E,OAAO;MAAE9H,IAAI,EAAC,QAAQ;MACrF3B,KAAK,EAAC;QACNc,YAAA,CAAuGC,4BAAA;MAAnFC,IAAI,UAAUM,KAAA,CAAAyD,kBAAkB,CAAC0E,OAAO;MAAyBzJ,KAAK,EAAC;2CAGpFY,QAAA,CAAA+I,gBAAgB,I,cAA3BxJ,mBAAA,CAA+E,OAA/E0J,WAA+E,EAAhB,YAAU,KApVnFzJ,mBAAA,e,GAuVQU,YAAA,CAAqEgJ,gCAAA;MAA7C9E,QAAQ,EAAE1D,KAAA,CAAA6F,mBAAmB,CAACkC;8CAGxDhJ,mBAAA,CAWM,OAXN0J,WAWM,G,4BAVJ1J,mBAAA,CAA8C;MAAzCL,KAAK,EAAC;IAAwB,GAAC,MAAI,sBACxCc,YAAA,CAEiBuB,yBAAA;MA9VzBC,UAAA,EA4ViChB,KAAA,CAAA6F,mBAAmB,CAAC6C,kBAAkB;MA5VvE,uBAAAtJ,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4ViCF,KAAA,CAAA6F,mBAAmB,CAAC6C,kBAAkB,GAAAxI,MAAA;;MA5VvEiB,OAAA,EAAAC,QAAA,CA6VU,MAA8BhC,MAAA,SAAAA,MAAA,QAA9BL,mBAAA,CAA8B;QAAxBL,KAAK,EAAC;MAAM,GAAC,MAAI,oB;MA7VjC2C,CAAA;uCA+VQ7B,YAAA,CAEiBuB,yBAAA;MAjWzBC,UAAA,EA+ViChB,KAAA,CAAA6F,mBAAmB,CAAC8C,WAAW;MA/VhE,uBAAAvJ,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA+ViCF,KAAA,CAAA6F,mBAAmB,CAAC8C,WAAW,GAAAzI,MAAA;;MA/VhEiB,OAAA,EAAAC,QAAA,CAgWU,MAAgChC,MAAA,SAAAA,MAAA,QAAhCL,mBAAA,CAAgC;QAA1BL,KAAK,EAAC;MAAM,GAAC,QAAM,oB;MAhWnC2C,CAAA;uCAkWQ7B,YAAA,CAEiBuB,yBAAA;MApWzBC,UAAA,EAkWiChB,KAAA,CAAA6F,mBAAmB,CAAC+C,QAAQ;MAlW7D,uBAAAxJ,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAkWiCF,KAAA,CAAA6F,mBAAmB,CAAC+C,QAAQ,GAAA1I,MAAA;;MAlW7DiB,OAAA,EAAAC,QAAA,CAmWU,MAAgChC,MAAA,SAAAA,MAAA,QAAhCL,mBAAA,CAAgC;QAA1BL,KAAK,EAAC;MAAM,GAAC,QAAM,oB;MAnWnC2C,CAAA;;IAAAA,CAAA;6DAwWIvC,mBAAA,YAAe,EACfU,YAAA,CAkDYoG,oBAAA;IA3ZhB5E,UAAA,EAyWwBhB,KAAA,CAAA6I,eAAe,CAAC/C,IAAI;IAzW5C,uBAAA1G,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAyWwBF,KAAA,CAAA6I,eAAe,CAAC/C,IAAI,GAAA5F,MAAA;IAAE6F,KAAK,EAAC,MAAM;IAAEC,SAAO,EAAE1G,QAAA,CAAAwJ,UAAU;IAAG5C,OAAO,EAAElG,KAAA,CAAAmG;;IAzW3FhF,OAAA,EAAAC,QAAA,CA0WM,MAMM,CANNrC,mBAAA,CAMM,OANNgK,WAMM,G,4BALJhK,mBAAA,CAAwC;MAAnCL,KAAK,EAAC;IAAkB,GAAC,MAAI,sBAClCK,mBAAA,CAGM,OAHNiK,WAGM,GAFJjK,mBAAA,CAAuE,c,4BAAlEA,mBAAA,CAAqC;MAA/BL,KAAK,EAAC;IAAa,GAAC,MAAI,sBA7W7CyB,gBAAA,CA6WoD,GAAC,GAAA+B,gBAAA,CAAGlC,KAAA,CAAAsG,WAAW,CAACnE,IAAI,iB,GAC9DpD,mBAAA,CAAsE,c,4BAAjEA,mBAAA,CAAsC;MAAhCL,KAAK,EAAC;IAAa,GAAC,OAAK,sBA9W9CyB,gBAAA,CA8WqD,GAAC,GAAA+B,gBAAA,CAAGlC,KAAA,CAAAsG,WAAW,CAAChE,EAAE,iB,OAIjEvD,mBAAA,CAGM,OAHNkK,WAGM,G,4BAFJlK,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CAAkG;MAA3FsB,IAAI,EAAC,MAAM;MApX1B,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAoXoCF,KAAA,CAAA6I,eAAe,CAACnG,QAAQ,GAAAxC,MAAA;MAAExB,KAAK,EAAC,cAAc;MAAC6B,WAAW,EAAC;mDAA3DP,KAAA,CAAA6I,eAAe,CAACnG,QAAQ,E,KAGtD3D,mBAAA,CAOM,OAPNmK,WAOM,G,4BANJnK,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CAIM,OAJNoK,WAIM,G,gBAHJpK,mBAAA,CACoI;MAD7HsB,IAAI,EAAC,UAAU;MA1XhC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA0X0CF,KAAA,CAAA6I,eAAe,CAAClG,SAAS,GAAAzC,MAAA;MACvDxB,KAAK,EAAC;uDADwBsB,KAAA,CAAA6I,eAAe,CAAClG,SAAS,E,+BAEzD5D,mBAAA,CAAsG;MAA/FL,KAAK,EAAC;IAAgF,4B,KAIjGK,mBAAA,CAOM,OAPNqK,WAOM,G,4BANJrK,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CAIS;MAtYjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAkYyBF,KAAA,CAAA6I,eAAe,CAACjC,QAAQ,GAAA1G,MAAA;MAAExB,KAAK,EAAC,aAAa;MAAEmI,QAAM,EAAAzH,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEZ,QAAA,CAAA+J,6BAA6B;2BACnGxK,mBAAA,CAES0C,SAAA,QArYnBC,WAAA,CAmYmCuF,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArBpI,mBAAA,CAES;QAF2BF,GAAG,EAAEsI,MAAM,CAACrF,EAAE;QAAGlB,KAAK,EAAEuG,MAAM,CAACrF;0BAC9DqF,MAAM,CAAC9E,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAG+E,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAhF,gBAAA,CAAG+E,MAAM,CAACE,UAAU,IAAG,KAC9E,uBArYVmC,WAAA;6FAkYyBtJ,KAAA,CAAA6I,eAAe,CAACjC,QAAQ,E,KAO3C7H,mBAAA,CAiBM,OAjBNwK,WAiBM,GAhBJxK,mBAAA,CAOM,OAPNyK,WAOM,G,4BANJzK,mBAAA,CAAuC;MAAhCL,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BK,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEZ,QAAA,CAAA+J,6BAA6B;MAAIhJ,IAAI,EAAC,QAAQ;MAC5D3B,KAAK,EAAC;QACNc,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEhB,KAAK,EAAC;oCA9YjEyB,gBAAA,CA8Y0E,QAEhE,G,KAEFpB,mBAAA,CAOM,OAPN0K,WAOM,G,gBANJ1K,mBAAA,CAC2C;MADnCsB,IAAI,EAAEL,KAAA,CAAAyD,kBAAkB,CAACiG,UAAU;MAnZrD,uBAAAtK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAmZsFF,KAAA,CAAA6I,eAAe,CAACnF,QAAQ,GAAAxD,MAAA;MAAEyD,QAAQ,EAAR,EAAQ;MAC5GjF,KAAK,EAAC;4BApZlBiL,WAAA,I,iBAmZsF3J,KAAA,CAAA6I,eAAe,CAACnF,QAAQ,E,GAEpG3E,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAAyD,kBAAkB,CAACiG,UAAU,IAAI1J,KAAA,CAAAyD,kBAAkB,CAACiG,UAAU;MAAErJ,IAAI,EAAC,QAAQ;MAC3F3B,KAAK,EAAC;QACNc,YAAA,CAA0GC,4BAAA;MAAtFC,IAAI,UAAUM,KAAA,CAAAyD,kBAAkB,CAACiG,UAAU;MAAyBhL,KAAK,EAAC;;IAvZ1G2C,CAAA;6DA6ZIvC,mBAAA,cAAiB,EACjBU,YAAA,CAiEYoG,oBAAA;IA/dhB5E,UAAA,EA8ZwBhB,KAAA,CAAA4J,gBAAgB,CAAC9D,IAAI;IA9Z7C,uBAAA1G,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8ZwBF,KAAA,CAAA4J,gBAAgB,CAAC9D,IAAI,GAAA5F,MAAA;IAAE6F,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAAC8D,IAAI,EAAC,IAAI;IACpF7D,SAAO,EAAE1G,QAAA,CAAAwK,oBAAoB;IAAG5D,OAAO,EAAElG,KAAA,CAAAmG;;IA/ZhDhF,OAAA,EAAAC,QAAA,CAgaM,MAaM,CAbNrC,mBAAA,CAaM,OAbNgL,WAaM,G,4BAZJhL,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CAIM,OAJNiL,WAIM,GAHJxK,YAAA,CAEiBuB,yBAAA;MAra3BC,UAAA,EAmamChB,KAAA,CAAAiK,cAAc;MAnajD,wB,sCAmamCjK,KAAA,CAAAiK,cAAc,GAAA/J,MAAA,GAAsBZ,QAAA,CAAA4K,oBAAoB;;MAna3F/I,OAAA,EAAAC,QAAA,CAma6F,MAEnFhC,MAAA,SAAAA,MAAA,QAraVe,gBAAA,CAma6F,MAEnF,E;MAraVkB,CAAA;gEAuaQtC,mBAAA,CAIM,OAJNoL,WAIM,I,kBAHJtL,mBAAA,CAEiB0C,SAAA,QA1a3BC,WAAA,CAwayCuF,IAAA,CAAAqD,KAAK,EAAbrI,IAAI;2BAA3BsI,YAAA,CAEiBtJ,yBAAA;QAFsBpC,GAAG,EAAEoD,IAAI,CAACH,EAAE;QAxa7DZ,UAAA,EAwawEhB,KAAA,CAAA4J,gBAAgB,CAACU,aAAa,CAACvI,IAAI,CAACH,EAAE;QAxa9G,uBAAA1B,MAAA,IAwawEF,KAAA,CAAA4J,gBAAgB,CAACU,aAAa,CAACvI,IAAI,CAACH,EAAE,IAAA1B;;QAxa9GiB,OAAA,EAAAC,QAAA,CAyaY,MAAe,CAza3BjB,gBAAA,CAAA+B,gBAAA,CAyaeH,IAAI,CAACI,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGH,IAAI,CAACO,EAAE,IAAG,IAChC,gB;QA1aVjB,CAAA;;sCA4aQtC,mBAAA,CAAyD,KAAzDwL,WAAyD,EAApC,MAAI,GAAArI,gBAAA,CAAG5C,QAAA,CAAAkL,kBAAkB,IAAG,MAAI,gB,GAGvDzL,mBAAA,CAOM,OAPN0L,WAOM,G,4BANJ1L,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CAIS;MArbjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAibyBF,KAAA,CAAA4J,gBAAgB,CAAChD,QAAQ,GAAA1G,MAAA;MAAExB,KAAK,EAAC;2BAChDG,mBAAA,CAES0C,SAAA,QApbnBC,WAAA,CAkbmCuF,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArBpI,mBAAA,CAES;QAF2BF,GAAG,EAAEsI,MAAM,CAACrF,EAAE;QAAGlB,KAAK,EAAEuG,MAAM,CAACrF;0BAC9DqF,MAAM,CAAC9E,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAG+E,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAhF,gBAAA,CAAG+E,MAAM,CAACE,UAAU,IAAG,KAC9E,uBApbVuD,WAAA;6EAibyB1K,KAAA,CAAA4J,gBAAgB,CAAChD,QAAQ,E,KAO5C7H,mBAAA,CAyBM,OAzBN4L,WAyBM,G,8BAxBJ5L,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BK,mBAAA,CASM,OATN6L,WASM,GARJ7L,mBAAA,CAGQ,SAHR8L,WAGQ,G,gBAFN9L,mBAAA,CAA4F;MAArFsB,IAAI,EAAC,OAAO;MA5b/B,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4byCF,KAAA,CAAA4J,gBAAgB,CAACkB,aAAa,GAAA5K,MAAA;MAAEQ,KAAK,EAAC,WAAW;MAAChC,KAAK,EAAC;oDAAxDsB,KAAA,CAAA4J,gBAAgB,CAACkB,aAAa,E,+BAC3D/L,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHRgM,WAGQ,G,gBAFNhM,mBAAA,CAA4F;MAArFsB,IAAI,EAAC,OAAO;MAhc/B,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAgcyCF,KAAA,CAAA4J,gBAAgB,CAACkB,aAAa,GAAA5K,MAAA;MAAEQ,KAAK,EAAC,WAAW;MAAChC,KAAK,EAAC;oDAAxDsB,KAAA,CAAA4J,gBAAgB,CAACkB,aAAa,E,+BAC3D/L,mBAAA,CAAiB,cAAX,MAAI,qB,KAIHiB,KAAA,CAAA4J,gBAAgB,CAACkB,aAAa,oB,cAAzCjM,mBAAA,CAWM,OAXNmM,WAWM,GAVJjM,mBAAA,CASM,OATNkM,WASM,GARJlM,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAoC;MAA7BL,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BK,mBAAA,CAAiF;MAA1EsB,IAAI,EAAC,MAAM;MAzchC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAyc0CF,KAAA,CAAA4J,gBAAgB,CAACsB,aAAa,GAAAhL,MAAA;MAAExB,KAAK,EAAC;mDAAtCsB,KAAA,CAAA4J,gBAAgB,CAACsB,aAAa,E,KAE5DnM,mBAAA,CAGM,c,8BAFJA,mBAAA,CAAoC;MAA7BL,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BK,mBAAA,CAAiF;MAA1EsB,IAAI,EAAC,MAAM;MA7chC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA6c0CF,KAAA,CAAA4J,gBAAgB,CAACuB,aAAa,GAAAjL,MAAA;MAAExB,KAAK,EAAC;mDAAtCsB,KAAA,CAAA4J,gBAAgB,CAACuB,aAAa,E,WA7cxErM,mBAAA,e,GAmdMC,mBAAA,CAWM,OAXNqM,WAWM,G,8BAVJrM,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Bc,YAAA,CAEiBuB,yBAAA;MAvdzBC,UAAA,EAqdiChB,KAAA,CAAA4J,gBAAgB,CAACyB,YAAY;MArd9D,uBAAAjM,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAqdiCF,KAAA,CAAA4J,gBAAgB,CAACyB,YAAY,GAAAnL,MAAA;;MArd9DiB,OAAA,EAAAC,QAAA,CAqdgE,MAExDhC,MAAA,UAAAA,MAAA,SAvdRe,gBAAA,CAqdgE,YAExD,E;MAvdRkB,CAAA;uCAwdQ7B,YAAA,CAEiBuB,yBAAA;MA1dzBC,UAAA,EAwdiChB,KAAA,CAAA4J,gBAAgB,CAAC0B,WAAW;MAxd7D,uBAAAlM,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAwdiCF,KAAA,CAAA4J,gBAAgB,CAAC0B,WAAW,GAAApL,MAAA;;MAxd7DiB,OAAA,EAAAC,QAAA,CAwd+D,MAEvDhC,MAAA,UAAAA,MAAA,SA1dRe,gBAAA,CAwd+D,UAEvD,E;MA1dRkB,CAAA;uCA2dQ7B,YAAA,CAEiBuB,yBAAA;MA7dzBC,UAAA,EA2diChB,KAAA,CAAA4J,gBAAgB,CAAC2B,gBAAgB;MA3dlE,uBAAAnM,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA2diCF,KAAA,CAAA4J,gBAAgB,CAAC2B,gBAAgB,GAAArL,MAAA;;MA3dlEiB,OAAA,EAAAC,QAAA,CA2doE,MAE5DhC,MAAA,UAAAA,MAAA,SA7dRe,gBAAA,CA2doE,aAE5D,E;MA7dRkB,CAAA;;IAAAA,CAAA;6DAieIvC,mBAAA,cAAiB,EACjBU,YAAA,CA8BYoG,oBAAA;IAhgBhB5E,UAAA,EAkewBhB,KAAA,CAAAwL,eAAe,CAAC1F,IAAI;IAle5C,uBAAA1G,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAkewBF,KAAA,CAAAwL,eAAe,CAAC1F,IAAI,GAAA5F,MAAA;IAAE6F,KAAK,EAAC,UAAU;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAE1G,QAAA,CAAAmM,gBAAgB;IACtGvF,OAAO,EAAElG,KAAA,CAAAmG;;IAnehBhF,OAAA,EAAAC,QAAA,CAoeM,MAQM,CARNrC,mBAAA,CAQM,OARN2M,WAQM,G,8BAPJ3M,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CAKM,OALN4M,YAKM,I,kBAJJ9M,mBAAA,CAGiB0C,SAAA,QA1e3BC,WAAA,CAueyClC,QAAA,CAAAsM,iBAAiB,EAAzB7J,IAAI;2BAA3BsI,YAAA,CAGiBtJ,yBAAA;QAHkCpC,GAAG,EAAEoD,IAAI,CAACH,EAAE;QAvezEZ,UAAA,EAweqBhB,KAAA,CAAAwL,eAAe,CAAClB,aAAa,CAACvI,IAAI,CAACH,EAAE;QAxe1D,uBAAA1B,MAAA,IAweqBF,KAAA,CAAAwL,eAAe,CAAClB,aAAa,CAACvI,IAAI,CAACH,EAAE,IAAA1B;;QAxe1DiB,OAAA,EAAAC,QAAA,CAyeY,MAAe,CAze3BjB,gBAAA,CAAA+B,gBAAA,CAyeeH,IAAI,CAACI,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGH,IAAI,CAACO,EAAE,IAAG,IAChC,gB;QA1eVjB,CAAA;;wCA8eMtC,mBAAA,CAOM,OAPN8M,YAOM,G,8BANJ9M,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCK,mBAAA,CAIS;MApfjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAgfyBF,KAAA,CAAAwL,eAAe,CAAC5E,QAAQ,GAAA1G,MAAA;MAAExB,KAAK,EAAC;2BAC/CG,mBAAA,CAES0C,SAAA,QAnfnBC,WAAA,CAifmCuF,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArBpI,mBAAA,CAES;QAF2BF,GAAG,EAAEsI,MAAM,CAACrF,EAAE;QAAGlB,KAAK,EAAEuG,MAAM,CAACrF;0BAC9DqF,MAAM,CAAC9E,IAAI,wBAlf1B2J,YAAA;6EAgfyB9L,KAAA,CAAAwL,eAAe,CAAC5E,QAAQ,E,KAO3C7H,mBAAA,CAQM,OARNgN,YAQM,G,8BAPJhN,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Bc,YAAA,CAEiBuB,yBAAA;MA3fzBC,UAAA,EAyfiChB,KAAA,CAAAwL,eAAe,CAACQ,iBAAiB;MAzflE,uBAAA5M,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAyfiCF,KAAA,CAAAwL,eAAe,CAACQ,iBAAiB,GAAA9L,MAAA;;MAzflEiB,OAAA,EAAAC,QAAA,CAyfoE,MAE5DhC,MAAA,UAAAA,MAAA,SA3fRe,gBAAA,CAyfoE,eAE5D,E;MA3fRkB,CAAA;uCA4fQ7B,YAAA,CAEiBuB,yBAAA;MA9fzBC,UAAA,EA4fiChB,KAAA,CAAAwL,eAAe,CAACS,iBAAiB;MA5flE,uBAAA7M,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4fiCF,KAAA,CAAAwL,eAAe,CAACS,iBAAiB,GAAA/L,MAAA;;MA5flEiB,OAAA,EAAAC,QAAA,CA4foE,MAE5DhC,MAAA,UAAAA,MAAA,SA9fRe,gBAAA,CA4foE,aAE5D,E;MA9fRkB,CAAA;;IAAAA,CAAA;6DAkgBIvC,mBAAA,cAAiB,EACjBU,YAAA,CAyCYoG,oBAAA;IA5iBhB5E,UAAA,EAmgBwBhB,KAAA,CAAAkM,mBAAmB,CAACpG,IAAI;IAngBhD,uBAAA1G,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAmgBwBF,KAAA,CAAAkM,mBAAmB,CAACpG,IAAI,GAAA5F,MAAA;IAAE6F,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAACrG,IAAI,EAAC,sBAAsB;IAACyM,MAAM,EAAN,EAAM;IAChHnG,SAAO,EAAE1G,QAAA,CAAA8M,cAAc;IAAGlG,OAAO,EAAElG,KAAA,CAAAmG;;IApgB1ChF,OAAA,EAAAC,QAAA,CAqgBM,MAEM,C,8BAFNrC,mBAAA,CAEM;MAFDL,KAAK,EAAC;IAA4C,IACrDK,mBAAA,CAA+C,WAA5C,0CAAwC,E,sBAG7CA,mBAAA,CAQM,OARNsN,YAQM,G,8BAPJtN,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCK,mBAAA,CAKM,OALNuN,YAKM,I,kBAJJzN,mBAAA,CAGiB0C,SAAA,QA/gB3BC,WAAA,CA4gByClC,QAAA,CAAAsM,iBAAiB,EAAzB7J,IAAI;2BAA3BsI,YAAA,CAGiBtJ,yBAAA;QAHkCpC,GAAG,EAAEoD,IAAI,CAACH,EAAE;QA5gBzEZ,UAAA,EA6gBqBhB,KAAA,CAAAkM,mBAAmB,CAAC5B,aAAa,CAACvI,IAAI,CAACH,EAAE;QA7gB9D,uBAAA1B,MAAA,IA6gBqBF,KAAA,CAAAkM,mBAAmB,CAAC5B,aAAa,CAACvI,IAAI,CAACH,EAAE,IAAA1B;;QA7gB9DiB,OAAA,EAAAC,QAAA,CA8gBY,MAAe,CA9gB3BjB,gBAAA,CAAA+B,gBAAA,CA8gBeH,IAAI,CAACI,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGH,IAAI,CAACO,EAAE,IAAG,IAChC,gB;QA/gBVjB,CAAA;;wCAmhBMtC,mBAAA,CAOM,OAPNwN,YAOM,G,8BANJxN,mBAAA,CAAwC;MAAjCL,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCK,mBAAA,CAIS;MAzhBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAqhByBF,KAAA,CAAAkM,mBAAmB,CAACtF,QAAQ,GAAA1G,MAAA;MAAExB,KAAK,EAAC;2BACnDG,mBAAA,CAES0C,SAAA,QAxhBnBC,WAAA,CAshBmClC,QAAA,CAAAkN,iBAAiB,EAA3BvF,MAAM;2BAArBpI,mBAAA,CAES;QAFoCF,GAAG,EAAEsI,MAAM,CAACrF,EAAE;QAAGlB,KAAK,EAAEuG,MAAM,CAACrF;0BACvEqF,MAAM,CAAC9E,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAG+E,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAhF,gBAAA,CAAG+E,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAxhBVsF,YAAA;6EAqhByBzM,KAAA,CAAAkM,mBAAmB,CAACtF,QAAQ,E,KAO/C7H,mBAAA,CASM,OATN2N,YASM,G,8BARJ3N,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CAMS;MApiBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8hByBF,KAAA,CAAAkM,mBAAmB,CAACS,MAAM,GAAAzM,MAAA;MAAExB,KAAK,EAAC;sCACjDK,mBAAA,CAAiD;MAAzC2B,KAAK,EAAC;IAAmB,GAAC,QAAM,qBACxC3B,mBAAA,CAA2C;MAAnC2B,KAAK,EAAC;IAAe,GAAC,MAAI,qBAClC3B,mBAAA,CAA6C;MAArC2B,KAAK,EAAC;IAAiB,GAAC,MAAI,qBACpC3B,mBAAA,CAAwC;MAAhC2B,KAAK,EAAC;IAAY,GAAC,MAAI,qBAC/B3B,mBAAA,CAAmC;MAA3B2B,KAAK,EAAC;IAAO,GAAC,MAAI,oB,2CALXV,KAAA,CAAAkM,mBAAmB,CAACS,MAAM,E,KAS7C5N,mBAAA,CAIM,OAJN6N,YAIM,G,8BAHJ7N,mBAAA,CAAsC;MAA/BL,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BK,mBAAA,CACuC;MA1iB/C,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAyiB2BF,KAAA,CAAAkM,mBAAmB,CAACW,WAAW,GAAA3M,MAAA;MAAExB,KAAK,EAAC,cAAc;MAACoO,IAAI,EAAC,GAAG;MAC/EvM,WAAW,EAAC;mDADKP,KAAA,CAAAkM,mBAAmB,CAACW,WAAW,E;IAziB1DxL,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}