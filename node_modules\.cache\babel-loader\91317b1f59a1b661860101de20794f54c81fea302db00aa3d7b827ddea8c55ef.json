{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createVNode as _createVNode, renderSlot as _renderSlot, createTextVNode as _createTextVNode, withModifiers as _withModifiers, Teleport as _Teleport } from \"vue\";\nconst _hoisted_1 = {\n  class: \"px-6 py-4 flex items-center justify-between border-b border-gray-200\"\n};\nconst _hoisted_2 = {\n  class: \"flex items-center space-x-3\"\n};\nconst _hoisted_3 = {\n  class: \"text-lg font-medium text-gray-900\"\n};\nconst _hoisted_4 = {\n  class: \"px-6 py-4 max-h-[70vh] overflow-y-auto\"\n};\nconst _hoisted_5 = {\n  class: \"px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-end space-x-3\"\n};\nconst _hoisted_6 = [\"disabled\"];\nconst _hoisted_7 = {\n  key: 0\n};\nconst _hoisted_8 = {\n  key: 1,\n  class: \"flex items-center justify-center\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  return _openBlock(), _createBlock(_Teleport, {\n    to: \"body\"\n  }, [_createCommentVNode(\" 模态框背景遮罩 \"), $props.modelValue ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: _normalizeClass([\"fixed inset-0 z-50 bg-gray-900 bg-opacity-60 backdrop-blur-sm transition-opacity duration-300\", {\n      'opacity-0': !$data.isAnimationComplete,\n      'opacity-100': $data.isAnimationComplete\n    }]),\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.closeModal && $options.closeModal(...args))\n  }, null, 2 /* CLASS */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 模态框容器 \"), $props.modelValue ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 1,\n    class: _normalizeClass([\"fixed inset-0 z-50 flex items-center justify-center p-4 overflow-hidden\", {\n      'opacity-0': !$data.isAnimationComplete,\n      'opacity-100': $data.isAnimationComplete\n    }])\n  }, [_createElementVNode(\"div\", {\n    class: _normalizeClass([\"bg-white rounded-lg shadow-2xl shadow-gray-600/20 w-full relative overflow-hidden transform transition-all duration-300 ease-out\", [$options.modalSize, {\n      'scale-95': !$data.isAnimationComplete,\n      'scale-100': $data.isAnimationComplete\n    }]]),\n    onClick: _cache[4] || (_cache[4] = _withModifiers(() => {}, [\"stop\"]))\n  }, [_createCommentVNode(\" 模态框标题栏 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [$props.icon ? (_openBlock(), _createBlock(_component_font_awesome_icon, {\n    key: 0,\n    icon: ['fas', $props.icon],\n    class: _normalizeClass([\"text-lg\", [$props.danger ? 'text-red-500' : 'text-blue-500']])\n  }, null, 8 /* PROPS */, [\"icon\", \"class\"])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"h3\", _hoisted_3, _toDisplayString($props.title), 1 /* TEXT */)]), _createElementVNode(\"button\", {\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.closeModal && $options.closeModal(...args)),\n    class: \"text-gray-400 hover:text-gray-500 focus:outline-none transition-colors\"\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'times']\n  })])]), _createCommentVNode(\" 模态框内容区 \"), _createElementVNode(\"div\", _hoisted_4, [_renderSlot(_ctx.$slots, \"default\", {}, undefined, true)]), _createCommentVNode(\" 模态框底部按钮区 \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.closeModal && $options.closeModal(...args)),\n    class: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors\"\n  }, \" 取消 \"), _createElementVNode(\"button\", {\n    onClick: _cache[3] || (_cache[3] = (...args) => $options.confirmModal && $options.confirmModal(...args)),\n    class: _normalizeClass([\"px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors\", [$props.danger ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500' : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500']]),\n    disabled: $props.loading\n  }, [!$props.loading ? (_openBlock(), _createElementBlock(\"span\", _hoisted_7, _toDisplayString($props.confirmText || '确认'), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_8, _cache[5] || (_cache[5] = [_createElementVNode(\"svg\", {\n    class: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\"\n  }, [_createElementVNode(\"circle\", {\n    class: \"opacity-25\",\n    cx: \"12\",\n    cy: \"12\",\n    r: \"10\",\n    stroke: \"currentColor\",\n    \"stroke-width\": \"4\"\n  }), _createElementVNode(\"path\", {\n    class: \"opacity-75\",\n    fill: \"currentColor\",\n    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n  })], -1 /* HOISTED */), _createTextVNode(\" 处理中... \")])))], 10 /* CLASS, PROPS */, _hoisted_6)])], 2 /* CLASS */)], 2 /* CLASS */)) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "key", "_createBlock", "_Teleport", "to", "_createCommentVNode", "$props", "modelValue", "_createElementBlock", "_normalizeClass", "$data", "isAnimationComplete", "onClick", "_cache", "args", "$options", "closeModal", "_createElementVNode", "modalSize", "_withModifiers", "_hoisted_1", "_hoisted_2", "icon", "_component_font_awesome_icon", "danger", "_hoisted_3", "_toDisplayString", "title", "_createVNode", "_hoisted_4", "_renderSlot", "_ctx", "$slots", "undefined", "_hoisted_5", "confirmModal", "disabled", "loading", "_hoisted_7", "confirmText", "_hoisted_8", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "d", "_createTextVNode", "_hoisted_6"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\BaseModal.vue"], "sourcesContent": ["<template>\r\n  <Teleport to=\"body\">\r\n    <!-- 模态框背景遮罩 -->\r\n    <div v-if=\"modelValue\"\r\n      class=\"fixed inset-0 z-50 bg-gray-900 bg-opacity-60 backdrop-blur-sm transition-opacity duration-300\"\r\n      :class=\"{ 'opacity-0': !isAnimationComplete, 'opacity-100': isAnimationComplete }\" @click=\"closeModal\"></div>\r\n\r\n    <!-- 模态框容器 -->\r\n    <div v-if=\"modelValue\" class=\"fixed inset-0 z-50 flex items-center justify-center p-4 overflow-hidden\"\r\n      :class=\"{ 'opacity-0': !isAnimationComplete, 'opacity-100': isAnimationComplete }\">\r\n      <div\r\n        class=\"bg-white rounded-lg shadow-2xl shadow-gray-600/20 w-full relative overflow-hidden transform transition-all duration-300 ease-out\"\r\n        :class=\"[modalSize, { 'scale-95': !isAnimationComplete, 'scale-100': isAnimationComplete }]\" @click.stop>\r\n        <!-- 模态框标题栏 -->\r\n        <div class=\"px-6 py-4 flex items-center justify-between border-b border-gray-200\">\r\n          <div class=\"flex items-center space-x-3\">\r\n            <font-awesome-icon v-if=\"icon\" :icon=\"['fas', icon]\" class=\"text-lg\"\r\n              :class=\"[danger ? 'text-red-500' : 'text-blue-500']\" />\r\n            <h3 class=\"text-lg font-medium text-gray-900\">{{ title }}</h3>\r\n          </div>\r\n          <button @click=\"closeModal\" class=\"text-gray-400 hover:text-gray-500 focus:outline-none transition-colors\">\r\n            <font-awesome-icon :icon=\"['fas', 'times']\" />\r\n          </button>\r\n        </div>\r\n\r\n        <!-- 模态框内容区 -->\r\n        <div class=\"px-6 py-4 max-h-[70vh] overflow-y-auto\">\r\n          <slot></slot>\r\n        </div>\r\n\r\n        <!-- 模态框底部按钮区 -->\r\n        <div class=\"px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-end space-x-3\">\r\n          <button @click=\"closeModal\"\r\n            class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors\">\r\n            取消\r\n          </button>\r\n          <button @click=\"confirmModal\"\r\n            class=\"px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors\"\r\n            :class=\"[\r\n              danger\r\n                ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'\r\n                : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'\r\n            ]\" :disabled=\"loading\">\r\n            <span v-if=\"!loading\">{{ confirmText || '确认' }}</span>\r\n            <div v-else class=\"flex items-center justify-center\">\r\n              <svg class=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\"\r\n                viewBox=\"0 0 24 24\">\r\n                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\r\n                <path class=\"opacity-75\" fill=\"currentColor\"\r\n                  d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\">\r\n                </path>\r\n              </svg>\r\n              处理中...\r\n            </div>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </Teleport>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'BaseModal',\r\n  emits: ['update:modelValue', 'confirm'],\r\n  props: {\r\n    modelValue: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    confirmText: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    size: {\r\n      type: String,\r\n      default: 'md'\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    danger: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    icon: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isAnimationComplete: false\r\n    }\r\n  },\r\n  computed: {\r\n    modalSize() {\r\n      const sizes = {\r\n        sm: 'max-w-sm',\r\n        md: 'max-w-md',\r\n        lg: 'max-w-lg',\r\n        xl: 'max-w-xl',\r\n        '2xl': 'max-w-2xl',\r\n        '3xl': 'max-w-3xl',\r\n        '4xl': 'max-w-4xl',\r\n        '5xl': 'max-w-5xl',\r\n        full: 'max-w-full'\r\n      }\r\n      return sizes[this.size] || sizes.md\r\n    }\r\n  },\r\n  watch: {\r\n    modelValue(val) {\r\n      if (val) {\r\n        this.$nextTick(() => {\r\n          this.isAnimationComplete = true\r\n          document.body.classList.add('overflow-hidden')\r\n        })\r\n      } else {\r\n        this.isAnimationComplete = false\r\n        document.body.classList.remove('overflow-hidden')\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    closeModal() {\r\n      this.isAnimationComplete = false\r\n      setTimeout(() => {\r\n        this.$emit('update:modelValue', false)\r\n      }, 200)\r\n    },\r\n    confirmModal() {\r\n      this.$emit('confirm')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 添加一些过渡动画效果 */\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n  opacity: 0;\r\n}\r\n\r\n.scale-enter-active,\r\n.scale-leave-active {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.scale-enter-from,\r\n.scale-leave-to {\r\n  transform: scale(0.95);\r\n}\r\n</style>"], "mappings": ";;EAcaA,KAAK,EAAC;AAAsE;;EAC1EA,KAAK,EAAC;AAA6B;;EAGlCA,KAAK,EAAC;AAAmC;;EAQ5CA,KAAK,EAAC;AAAwC;;EAK9CA,KAAK,EAAC;AAAuF;mBA/B1G;;EAAAC,GAAA;AAAA;;EAAAA,GAAA;EA4CwBD,KAAK,EAAC;;;;uBA3C5BE,YAAA,CAyDWC,SAAA;IAzDDC,EAAE,EAAC;EAAM,IACjBC,mBAAA,aAAgB,EACLC,MAAA,CAAAC,UAAU,I,cAArBC,mBAAA,CAE+G;IALnHP,GAAA;IAIMD,KAAK,EAJXS,eAAA,EAIY,+FAA+F;MAAA,cAC7EC,KAAA,CAAAC,mBAAmB;MAAA,eAAiBD,KAAA,CAAAC;IAAmB;IAAKC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,UAAA,IAAAD,QAAA,CAAAC,UAAA,IAAAF,IAAA,CAAU;6BAL3GT,mBAAA,gBAOIA,mBAAA,WAAc,EACHC,MAAA,CAAAC,UAAU,I,cAArBC,mBAAA,CAiDM;IAzDVP,GAAA;IAQ2BD,KAAK,EARhCS,eAAA,EAQiC,yEAAyE;MAAA,cAC5EC,KAAA,CAAAC,mBAAmB;MAAA,eAAiBD,KAAA,CAAAC;IAAmB;MAC/EM,mBAAA,CA8CM;IA7CJjB,KAAK,EAXbS,eAAA,EAWc,kIAAkI,GAC/HM,QAAA,CAAAG,SAAS;MAAA,aAAiBR,KAAA,CAAAC,mBAAmB;MAAA,aAAeD,KAAA,CAAAC;IAAmB;IAAMC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAZ3GM,cAAA,CAYqG,QAAW;MACxGd,mBAAA,YAAe,EACfY,mBAAA,CASM,OATNG,UASM,GARJH,mBAAA,CAIM,OAJNI,UAIM,GAHqBf,MAAA,CAAAgB,IAAI,I,cAA7BpB,YAAA,CACyDqB,4BAAA;IAjBrEtB,GAAA;IAgB4CqB,IAAI,UAAUhB,MAAA,CAAAgB,IAAI;IAAGtB,KAAK,EAhBtES,eAAA,EAgBuE,SAAS,GACzDH,MAAA,CAAAkB,MAAM;gDAjB7BnB,mBAAA,gBAkBYY,mBAAA,CAA8D,MAA9DQ,UAA8D,EAAAC,gBAAA,CAAbpB,MAAA,CAAAqB,KAAK,iB,GAExDV,mBAAA,CAES;IAFAL,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,UAAA,IAAAD,QAAA,CAAAC,UAAA,IAAAF,IAAA,CAAU;IAAEd,KAAK,EAAC;MAChC4B,YAAA,CAA8CL,4BAAA;IAA1BD,IAAI,EAAE;EAAgB,G,KAI9CjB,mBAAA,YAAe,EACfY,mBAAA,CAEM,OAFNY,UAEM,GADJC,WAAA,CAAaC,IAAA,CAAAC,MAAA,iBAAAC,SAAA,Q,GAGf5B,mBAAA,cAAiB,EACjBY,mBAAA,CAwBM,OAxBNiB,UAwBM,GAvBJjB,mBAAA,CAGS;IAHAL,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,UAAA,IAAAD,QAAA,CAAAC,UAAA,IAAAF,IAAA,CAAU;IACxBd,KAAK,EAAC;KAA8M,MAEtN,GACAiB,mBAAA,CAkBS;IAlBAL,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAoB,YAAA,IAAApB,QAAA,CAAAoB,YAAA,IAAArB,IAAA,CAAY;IAC1Bd,KAAK,EArCjBS,eAAA,EAqCkB,qIAAqI,GAClHH,MAAA,CAAAkB,MAAM,G;IAI3BY,QAAQ,EAAE9B,MAAA,CAAA+B;OACD/B,MAAA,CAAA+B,OAAO,I,cAApB7B,mBAAA,CAAsD,QA3ClE8B,UAAA,EAAAZ,gBAAA,CA2CqCpB,MAAA,CAAAiC,WAAW,6B,cACpC/B,mBAAA,CASM,OATNgC,UASM,EAAA3B,MAAA,QAAAA,MAAA,OARJI,mBAAA,CAMM;IANDjB,KAAK,EAAC,4CAA4C;IAACyC,KAAK,EAAC,4BAA4B;IAACC,IAAI,EAAC,MAAM;IACpGC,OAAO,EAAC;MACR1B,mBAAA,CAAkG;IAA1FjB,KAAK,EAAC,YAAY;IAAC4C,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,MAAM,EAAC,cAAc;IAAC,cAAY,EAAC;MACrF9B,mBAAA,CAEO;IAFDjB,KAAK,EAAC,YAAY;IAAC0C,IAAI,EAAC,cAAc;IAC1CM,CAAC,EAAC;0BAjDpBC,gBAAA,CAmDoB,UAER,E,6BArDZC,UAAA,E,uCAAA7C,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}