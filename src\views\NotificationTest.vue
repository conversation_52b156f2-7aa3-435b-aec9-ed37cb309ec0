<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">通知系统测试</h1>
        <p class="text-gray-600 dark:text-gray-400">测试各种类型的通知消息</p>
      </div>
    </div>

    <!-- 基础通知测试 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">基础通知测试</h3>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <button 
          @click="testSuccess"
          class="inline-flex items-center justify-center px-4 py-3 bg-green-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
        >
          <font-awesome-icon :icon="['fas', 'check-circle']" class="mr-2" />
          成功通知
        </button>
        
        <button 
          @click="testError"
          class="inline-flex items-center justify-center px-4 py-3 bg-red-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
        >
          <font-awesome-icon :icon="['fas', 'exclamation-circle']" class="mr-2" />
          错误通知
        </button>
        
        <button 
          @click="testWarning"
          class="inline-flex items-center justify-center px-4 py-3 bg-yellow-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500"
        >
          <font-awesome-icon :icon="['fas', 'exclamation-triangle']" class="mr-2" />
          警告通知
        </button>
        
        <button 
          @click="testInfo"
          class="inline-flex items-center justify-center px-4 py-3 bg-blue-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <font-awesome-icon :icon="['fas', 'info-circle']" class="mr-2" />
          信息通知
        </button>
      </div>
    </div>

    <!-- 高级通知测试 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">高级通知测试</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <button 
          @click="testPersistent"
          class="inline-flex items-center justify-center px-4 py-3 bg-purple-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          <font-awesome-icon :icon="['fas', 'lock']" class="mr-2" />
          持久通知
        </button>
        
        <button 
          @click="testLongDuration"
          class="inline-flex items-center justify-center px-4 py-3 bg-indigo-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
        >
          <font-awesome-icon :icon="['fas', 'clock']" class="mr-2" />
          长时间通知
        </button>
        
        <button 
          @click="testWithActions"
          class="inline-flex items-center justify-center px-4 py-3 bg-teal-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500"
        >
          <font-awesome-icon :icon="['fas', 'cog']" class="mr-2" />
          带操作按钮
        </button>
        
        <button 
          @click="testMultiple"
          class="inline-flex items-center justify-center px-4 py-3 bg-orange-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <font-awesome-icon :icon="['fas', 'copy']" class="mr-2" />
          多个通知
        </button>
        
        <button 
          @click="testLongMessage"
          class="inline-flex items-center justify-center px-4 py-3 bg-pink-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500"
        >
          <font-awesome-icon :icon="['fas', 'align-left']" class="mr-2" />
          长消息
        </button>
        
        <button 
          @click="testAsync"
          class="inline-flex items-center justify-center px-4 py-3 bg-gray-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          <font-awesome-icon :icon="['fas', 'sync-alt']" class="mr-2" />
          异步操作
        </button>
      </div>
    </div>

    <!-- 使用说明 -->
    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800 p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
        <font-awesome-icon :icon="['fas', 'lightbulb']" class="mr-2 text-blue-500" />
        使用说明
      </h3>
      
      <div class="space-y-2 text-sm text-gray-700 dark:text-gray-300">
        <p>• <strong>成功通知</strong>：绿色，自动关闭，用于操作成功反馈</p>
        <p>• <strong>错误通知</strong>：红色，不自动关闭，需要用户手动关闭</p>
        <p>• <strong>警告通知</strong>：黄色，自动关闭，用于警告信息</p>
        <p>• <strong>信息通知</strong>：蓝色，自动关闭，用于一般信息</p>
        <p>• <strong>持久通知</strong>：不会自动关闭，需要用户手动关闭</p>
        <p>• <strong>长时间通知</strong>：显示时间更长（10秒）</p>
        <p>• <strong>带操作按钮</strong>：包含可点击的操作按钮</p>
        <p>• <strong>多个通知</strong>：同时显示多个通知</p>
      </div>
    </div>
  </div>
</template>

<script>
import { showSuccess, showError, showWarning, showInfo, showNotification } from '@/utils/notification.js'

export default {
  name: 'NotificationTest',
  methods: {
    testSuccess() {
      showSuccess('操作已成功完成！', '成功')
    },
    
    testError() {
      showError('发生了一个错误，请检查您的输入。', '错误')
    },
    
    testWarning() {
      showWarning('这是一个警告消息，请注意。', '警告')
    },
    
    testInfo() {
      showInfo('这是一条信息通知。', '提示')
    },
    
    testPersistent() {
      showNotification({
        type: 'info',
        title: '持久通知',
        message: '这个通知不会自动关闭，需要手动关闭。',
        autoClose: false
      })
    },
    
    testLongDuration() {
      showNotification({
        type: 'warning',
        title: '长时间通知',
        message: '这个通知将显示10秒钟。',
        duration: 10000
      })
    },
    
    testWithActions() {
      showNotification({
        type: 'info',
        title: '确认操作',
        message: '您确定要执行此操作吗？',
        autoClose: false,
        actions: [
          {
            text: '确认',
            handler: () => {
              showSuccess('操作已确认！')
            }
          },
          {
            text: '取消',
            handler: () => {
              showInfo('操作已取消。')
            }
          }
        ]
      })
    },
    
    testMultiple() {
      showSuccess('第一个通知')
      setTimeout(() => showWarning('第二个通知'), 500)
      setTimeout(() => showInfo('第三个通知'), 1000)
      setTimeout(() => showError('第四个通知'), 1500)
    },
    
    testLongMessage() {
      showNotification({
        type: 'info',
        title: '详细信息',
        message: '这是一个包含很长消息内容的通知。它可能包含多行文本，用于显示详细的信息或说明。通知系统会自动调整高度以适应内容。',
        duration: 8000
      })
    },
    
    async testAsync() {
      showInfo('开始异步操作...', '处理中')
      
      try {
        // 模拟异步操作
        await new Promise(resolve => setTimeout(resolve, 2000))
        showSuccess('异步操作完成！', '成功')
      } catch (error) {
        showError('异步操作失败！', '错误')
      }
    }
  }
}
</script>
