{"ast": null, "code": "import { mapState, mapGetters } from 'vuex';\nimport BaseModal from '@/components/BaseModal.vue';\nimport StatusBadge from '@/components/StatusBadge.vue';\nimport CustomCheckbox from '@/components/CustomCheckbox.vue';\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue';\nexport default {\n  name: 'HostManagement',\n  components: {\n    BaseModal,\n    StatusBadge,\n    CustomCheckbox,\n    PasswordStrengthMeter\n  },\n  data() {\n    return {\n      selectAll: false,\n      selectAllBatch: false,\n      processing: false,\n      currentHost: {},\n      // 修改密码弹窗\n      changePasswordModal: {\n        show: false,\n        method: 'auto',\n        policyId: 1,\n        generatedPassword: 'aX7#9pQr$2Lm',\n        newPassword: '',\n        confirmPassword: '',\n        executeImmediately: true,\n        saveHistory: false,\n        logAudit: true\n      },\n      // 批量更新密码弹窗\n      batchUpdateModal: {\n        show: false,\n        selectedHosts: {},\n        policyId: 1,\n        executionTime: 'immediate',\n        scheduledDate: '',\n        scheduledTime: '',\n        ignoreErrors: true,\n        detailedLog: true,\n        sendNotification: false\n      },\n      // 批量应用策略弹窗\n      batchApplyModal: {\n        show: false,\n        selectedHosts: {},\n        policyId: 1,\n        updateImmediately: false,\n        applyOnNextUpdate: true\n      },\n      // 紧急重置密码弹窗\n      emergencyResetModal: {\n        show: false,\n        selectedHosts: {},\n        policyId: 3,\n        // 默认使用紧急策略\n        reason: 'security_incident',\n        description: ''\n      }\n    };\n  },\n  computed: {\n    ...mapState({\n      hosts: state => state.hosts,\n      policies: state => state.policies\n    }),\n    ...mapGetters(['selectedHosts']),\n    passwordMismatch() {\n      return this.changePasswordModal.newPassword && this.changePasswordModal.confirmPassword && this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword;\n    },\n    selectedHostsCount() {\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length;\n    },\n    selectedHostsList() {\n      return this.hosts.filter(host => host.selected);\n    },\n    emergencyPolicies() {\n      // 返回紧急策略和高强度策略\n      return this.policies.filter(p => p.id === 3 || p.id === 1);\n    }\n  },\n  methods: {\n    toggleSelectAll(value) {\n      this.$store.commit('selectAllHosts', value);\n    },\n    toggleSelectAllBatch(value) {\n      this.hosts.forEach(host => {\n        this.batchUpdateModal.selectedHosts[host.id] = value;\n      });\n    },\n    openChangePasswordModal(host) {\n      this.currentHost = host;\n      this.changePasswordModal.show = true;\n      this.generatePassword();\n    },\n    openBatchUpdateModal() {\n      this.batchUpdateModal.show = true;\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected;\n      });\n\n      // 设置默认值\n      const today = new Date();\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0];\n      this.batchUpdateModal.scheduledTime = '03:00';\n    },\n    openBatchApplyModal() {\n      this.batchApplyModal.show = true;\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.batchApplyModal.selectedHosts[host.id] = host.selected;\n      });\n    },\n    showEmergencyReset() {\n      this.emergencyResetModal.show = true;\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected;\n      });\n    },\n    generatePassword() {\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';\n      let password = '';\n\n      // 获取所选策略的最小长度\n      const policy = this.policies.find(p => p.id === this.changePasswordModal.policyId);\n      const minLength = policy ? policy.minLength : 12;\n\n      // 生成随机密码\n      for (let i = 0; i < minLength; i++) {\n        password += chars.charAt(Math.floor(Math.random() * chars.length));\n      }\n      this.changePasswordModal.generatedPassword = password;\n    },\n    async updatePassword() {\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\n        return;\n      }\n      this.processing = true;\n      try {\n        const password = this.changePasswordModal.method === 'auto' ? this.changePasswordModal.generatedPassword : this.changePasswordModal.newPassword;\n        await this.$store.dispatch('updateHostPassword', {\n          hostId: this.currentHost.id,\n          newStatus: 'normal'\n        });\n        this.changePasswordModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功更新主机 ${this.currentHost.name} 的密码！`);\n      } catch (error) {\n        console.error('更新密码失败', error);\n        alert('更新密码失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    async batchUpdatePasswords() {\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts).filter(([id, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        alert('请至少选择一台主机！');\n        return;\n      }\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\n        // 在实际应用中，这里会创建一个定时任务\n        alert('已创建定时密码更新任务！');\n        this.batchUpdateModal.show = false;\n        return;\n      }\n      this.processing = true;\n      try {\n        await this.$store.dispatch('batchUpdateHostPasswords', {\n          hostIds: selectedHostIds,\n          status: 'normal'\n        });\n        this.batchUpdateModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为 ${selectedHostIds.length} 台主机更新密码！`);\n      } catch (error) {\n        console.error('批量更新密码失败', error);\n        alert('批量更新密码失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    async batchApplyPolicy() {\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts).filter(([id, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        alert('请至少选择一台主机！');\n        return;\n      }\n      this.processing = true;\n      try {\n        await this.$store.dispatch('applyPolicyToHosts', {\n          policyId: this.batchApplyModal.policyId,\n          hostIds: selectedHostIds\n        });\n        this.batchApplyModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`);\n      } catch (error) {\n        console.error('应用策略失败', error);\n        alert('应用策略失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    async emergencyReset() {\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts).filter(([id, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        alert('请至少选择一台主机！');\n        return;\n      }\n      this.processing = true;\n      try {\n        await this.$store.dispatch('batchUpdateHostPasswords', {\n          hostIds: selectedHostIds,\n          status: 'normal'\n        });\n        this.emergencyResetModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为 ${selectedHostIds.length} 台主机执行紧急密码重置！`);\n      } catch (error) {\n        console.error('紧急重置失败', error);\n        alert('紧急重置失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    }\n  },\n  created() {\n    // 初始化日期和时间\n    const today = new Date();\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0];\n    this.batchUpdateModal.scheduledTime = '03:00';\n  }\n};", "map": {"version": 3, "names": ["mapState", "mapGetters", "BaseModal", "StatusBadge", "CustomCheckbox", "PasswordStrengthMeter", "name", "components", "data", "selectAll", "selectAllBatch", "processing", "currentHost", "changePasswordModal", "show", "method", "policyId", "generatedPassword", "newPassword", "confirmPassword", "executeImmediately", "saveHistory", "logAudit", "batchUpdateModal", "selectedHosts", "executionTime", "scheduledDate", "scheduledTime", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "reason", "description", "computed", "hosts", "state", "policies", "passwordMismatch", "selectedHostsCount", "Object", "values", "filter", "Boolean", "length", "selectedHostsList", "host", "selected", "emergencyPolicies", "p", "id", "methods", "toggleSelectAll", "value", "$store", "commit", "toggleSelectAllBatch", "for<PERSON>ach", "openChangePasswordModal", "generatePassword", "openBatchUpdateModal", "today", "Date", "toISOString", "split", "openBatchApplyModal", "showEmergencyReset", "chars", "password", "policy", "find", "<PERSON><PERSON><PERSON><PERSON>", "i", "char<PERSON>t", "Math", "floor", "random", "updatePassword", "dispatch", "hostId", "newStatus", "alert", "error", "console", "batchUpdatePasswords", "selectedHostIds", "entries", "map", "parseInt", "hostIds", "status", "batchApplyPolicy", "emergencyReset", "created"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"flex space-x-3 mb-6\">\r\n      <button class=\"btn-outline\" @click=\"showEmergencyReset\">\r\n        <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2 text-red-600\" />\r\n        <span>紧急重置</span>\r\n      </button>\r\n      <button class=\"btn-outline\" @click=\"openBatchUpdateModal\">\r\n        <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n        <span>批量更新密码</span>\r\n      </button>\r\n      <button class=\"btn-outline\" @click=\"openBatchApplyModal\">\r\n        <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n        <span>批量应用策略</span>\r\n      </button>\r\n    </div>\r\n    \r\n    <!-- 主机列表 -->\r\n    <table class=\"data-table\">\r\n      <thead>\r\n        <tr>\r\n          <th>\r\n            <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n              主机名\r\n            </CustomCheckbox>\r\n          </th>\r\n          <th>IP地址</th>\r\n          <th>状态</th>\r\n          <th>操作</th>\r\n        </tr>\r\n      </thead>\r\n      <tbody>\r\n        <tr v-for=\"host in hosts\" :key=\"host.id\">\r\n          <td>\r\n            <CustomCheckbox v-model=\"host.selected\">\r\n              {{ host.name }}\r\n            </CustomCheckbox>\r\n          </td>\r\n          <td>{{ host.ip }}</td>\r\n          <td>\r\n            <StatusBadge :type=\"host.status\" />\r\n          </td>\r\n          <td>\r\n            <button \r\n              class=\"text-blue-600 hover:text-blue-800\"\r\n              @click=\"openChangePasswordModal(host)\"\r\n            >\r\n              修改密码\r\n            </button>\r\n          </td>\r\n        </tr>\r\n      </tbody>\r\n    </table>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal\r\n      v-model=\"changePasswordModal.show\"\r\n      title=\"修改密码\"\r\n      confirm-text=\"确认更新\"\r\n      @confirm=\"updatePassword\"\r\n      :loading=\"processing\"\r\n    >\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">服务器: {{ currentHost.name }}</label>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input \r\n              type=\"radio\" \r\n              v-model=\"changePasswordModal.method\" \r\n              value=\"auto\" \r\n              class=\"mr-2\"\r\n            >\r\n            <span>自动生成</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input \r\n              type=\"radio\"\r\n              v-model=\"changePasswordModal.method\" \r\n              value=\"manual\" \r\n              class=\"mr-2\"\r\n            >\r\n            <span>手动输入</span>\r\n          </label>\r\n        </div>\r\n      </div>\r\n      \r\n      <div v-if=\"changePasswordModal.method === 'auto'\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">选择密码策略</label>\r\n          <select v-model=\"changePasswordModal.policyId\" class=\"form-select\">\r\n            <option \r\n              v-for=\"policy in policies\" \r\n              :key=\"policy.id\" \r\n              :value=\"policy.id\"\r\n            >\r\n              {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n            </option>\r\n          </select>\r\n        </div>\r\n        \r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <div class=\"flex\">\r\n            <input \r\n              type=\"text\" \r\n              v-model=\"changePasswordModal.generatedPassword\" \r\n              class=\"form-control rounded-r-none\" \r\n              readonly\r\n            >\r\n            <button \r\n              class=\"bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded-r-md\"\r\n              @click=\"generatePassword\"\r\n            >\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" />\r\n            </button>\r\n          </div>\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.generatedPassword\" />\r\n        </div>\r\n      </div>\r\n      \r\n      <div v-else>\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <input \r\n            type=\"password\" \r\n            v-model=\"changePasswordModal.newPassword\" \r\n            class=\"form-control\"\r\n          >\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n        </div>\r\n        \r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <input \r\n            type=\"password\" \r\n            v-model=\"changePasswordModal.confirmPassword\" \r\n            class=\"form-control\"\r\n            :class=\"{ 'border-red-500': passwordMismatch }\"\r\n          >\r\n          <div v-if=\"passwordMismatch\" class=\"text-red-500 text-xs mt-1\">\r\n            两次输入的密码不一致\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行选项</label>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          立即执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          保存密码历史记录\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          记录审计日志\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal\r\n      v-model=\"batchUpdateModal.show\"\r\n      title=\"批量更新密码\"\r\n      confirm-text=\"开始更新\"\r\n      size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\"\r\n      :loading=\"processing\"\r\n    >\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox \r\n            v-for=\"host in hosts\" \r\n            :key=\"host.id\"\r\n            v-model=\"batchUpdateModal.selectedHosts[host.id]\"\r\n          >\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option \r\n            v-for=\"policy in policies\" \r\n            :key=\"policy.id\" \r\n            :value=\"policy.id\"\r\n          >\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input \r\n              type=\"radio\" \r\n              v-model=\"batchUpdateModal.executionTime\" \r\n              value=\"immediate\" \r\n              class=\"mr-2\"\r\n            >\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input \r\n              type=\"radio\"\r\n              v-model=\"batchUpdateModal.executionTime\" \r\n              value=\"scheduled\" \r\n              class=\"mr-2\"\r\n            >\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n        \r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input \r\n                type=\"date\" \r\n                v-model=\"batchUpdateModal.scheduledDate\" \r\n                class=\"form-control\"\r\n              >\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input \r\n                type=\"time\" \r\n                v-model=\"batchUpdateModal.scheduledTime\" \r\n                class=\"form-control\"\r\n              >\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal\r\n      v-model=\"batchApplyModal.show\"\r\n      title=\"批量应用密码策略\"\r\n      confirm-text=\"应用策略\"\r\n      @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\"\r\n    >\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox \r\n            v-for=\"host in selectedHostsList\" \r\n            :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\"\r\n          >\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option \r\n            v-for=\"policy in policies\" \r\n            :key=\"policy.id\" \r\n            :value=\"policy.id\"\r\n          >\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal\r\n      v-model=\"emergencyResetModal.show\"\r\n      title=\"紧急密码重置\"\r\n      confirm-text=\"立即重置\"\r\n      icon=\"exclamation-triangle\"\r\n      danger\r\n      @confirm=\"emergencyReset\"\r\n      :loading=\"processing\"\r\n    >\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox \r\n            v-for=\"host in selectedHostsList\" \r\n            :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\"\r\n          >\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option \r\n            v-for=\"policy in emergencyPolicies\" \r\n            :key=\"policy.id\" \r\n            :value=\"policy.id\"\r\n          >\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea \r\n          v-model=\"emergencyResetModal.description\" \r\n          class=\"form-control\" \r\n          rows=\"2\" \r\n          placeholder=\"请输入重置原因详细说明\"\r\n        ></textarea>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      \r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n      \r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n      \r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n      \r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n    \r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword && \r\n        this.changePasswordModal.confirmPassword && \r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n    \r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n    \r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n    \r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n    \r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n    \r\n    openChangePasswordModal(host) {\r\n      this.currentHost = host\r\n      this.changePasswordModal.show = true\r\n      this.generatePassword()\r\n    },\r\n    \r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n      \r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n      \r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n    \r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n      \r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n    \r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n      \r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n    \r\n    generatePassword() {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n      \r\n      // 获取所选策略的最小长度\r\n      const policy = this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policy ? policy.minLength : 12\r\n      \r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n      \r\n      this.changePasswordModal.generatedPassword = password\r\n    },\r\n    \r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n      \r\n      this.processing = true\r\n      \r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto' \r\n          ? this.changePasswordModal.generatedPassword \r\n          : this.changePasswordModal.newPassword\r\n        \r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          newStatus: 'normal'\r\n        })\r\n        \r\n        this.changePasswordModal.show = false\r\n        \r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n    \r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([id, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n      \r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n      \r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n      \r\n      this.processing = true\r\n      \r\n      try {\r\n        await this.$store.dispatch('batchUpdateHostPasswords', {\r\n          hostIds: selectedHostIds,\r\n          status: 'normal'\r\n        })\r\n        \r\n        this.batchUpdateModal.show = false\r\n        \r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n    \r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([id, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n      \r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n      \r\n      this.processing = true\r\n      \r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n        \r\n        this.batchApplyModal.show = false\r\n        \r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n    \r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([id, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n      \r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n      \r\n      this.processing = true\r\n      \r\n      try {\r\n        await this.$store.dispatch('batchUpdateHostPasswords', {\r\n          hostIds: selectedHostIds,\r\n          status: 'normal'\r\n        })\r\n        \r\n        this.emergencyResetModal.show = false\r\n        \r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script> "], "mappings": "AAsXA,SAASA,QAAQ,EAAEC,UAAS,QAAS,MAAK;AAC1C,OAAOC,SAAQ,MAAO,4BAA2B;AACjD,OAAOC,WAAU,MAAO,8BAA6B;AACrD,OAAOC,cAAa,MAAO,iCAAgC;AAC3D,OAAOC,qBAAoB,MAAO,wCAAuC;AAEzE,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,UAAU,EAAE;IACVL,SAAS;IACTC,WAAW;IACXC,cAAc;IACdC;EACF,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,KAAK;MAChBC,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,CAAC,CAAC;MAEf;MACAC,mBAAmB,EAAE;QACnBC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE,CAAC;QACXC,iBAAiB,EAAE,cAAc;QACjCC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE,EAAE;QACnBC,kBAAkB,EAAE,IAAI;QACxBC,WAAW,EAAE,KAAK;QAClBC,QAAQ,EAAE;MACZ,CAAC;MAED;MACAC,gBAAgB,EAAE;QAChBT,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBR,QAAQ,EAAE,CAAC;QACXS,aAAa,EAAE,WAAW;QAC1BC,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,EAAE;QACjBC,YAAY,EAAE,IAAI;QAClBC,WAAW,EAAE,IAAI;QACjBC,gBAAgB,EAAE;MACpB,CAAC;MAED;MACAC,eAAe,EAAE;QACfjB,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBR,QAAQ,EAAE,CAAC;QACXgB,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;MAED;MACAC,mBAAmB,EAAE;QACnBpB,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBR,QAAQ,EAAE,CAAC;QAAE;QACbmB,MAAM,EAAE,mBAAmB;QAC3BC,WAAW,EAAE;MACf;IACF;EACF,CAAC;EACDC,QAAQ,EAAE;IACR,GAAGrC,QAAQ,CAAC;MACVsC,KAAK,EAAEC,KAAI,IAAKA,KAAK,CAACD,KAAK;MAC3BE,QAAQ,EAAED,KAAI,IAAKA,KAAK,CAACC;IAC3B,CAAC,CAAC;IACF,GAAGvC,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC;IAEhCwC,gBAAgBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAAC5B,mBAAmB,CAACK,WAAU,IACxC,IAAI,CAACL,mBAAmB,CAACM,eAAc,IACvC,IAAI,CAACN,mBAAmB,CAACK,WAAU,KAAM,IAAI,CAACL,mBAAmB,CAACM,eAAc;IACpF,CAAC;IAEDuB,kBAAkBA,CAAA,EAAG;MACnB,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACrB,gBAAgB,CAACC,aAAa,CAAC,CAACqB,MAAM,CAACC,OAAO,CAAC,CAACC,MAAK;IACjF,CAAC;IAEDC,iBAAiBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACV,KAAK,CAACO,MAAM,CAACI,IAAG,IAAKA,IAAI,CAACC,QAAQ;IAChD,CAAC;IAEDC,iBAAiBA,CAAA,EAAG;MAClB;MACA,OAAO,IAAI,CAACX,QAAQ,CAACK,MAAM,CAACO,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,KAAKD,CAAC,CAACC,EAAC,KAAM,CAAC;IAC3D;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,eAAeA,CAACC,KAAK,EAAE;MACrB,IAAI,CAACC,MAAM,CAACC,MAAM,CAAC,gBAAgB,EAAEF,KAAK;IAC5C,CAAC;IAEDG,oBAAoBA,CAACH,KAAK,EAAE;MAC1B,IAAI,CAAClB,KAAK,CAACsB,OAAO,CAACX,IAAG,IAAK;QACzB,IAAI,CAAC1B,gBAAgB,CAACC,aAAa,CAACyB,IAAI,CAACI,EAAE,IAAIG,KAAI;MACrD,CAAC;IACH,CAAC;IAEDK,uBAAuBA,CAACZ,IAAI,EAAE;MAC5B,IAAI,CAACrC,WAAU,GAAIqC,IAAG;MACtB,IAAI,CAACpC,mBAAmB,CAACC,IAAG,GAAI,IAAG;MACnC,IAAI,CAACgD,gBAAgB,CAAC;IACxB,CAAC;IAEDC,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAACxC,gBAAgB,CAACT,IAAG,GAAI,IAAG;;MAEhC;MACA,IAAI,CAACwB,KAAK,CAACsB,OAAO,CAACX,IAAG,IAAK;QACzB,IAAI,CAAC1B,gBAAgB,CAACC,aAAa,CAACyB,IAAI,CAACI,EAAE,IAAIJ,IAAI,CAACC,QAAO;MAC7D,CAAC;;MAED;MACA,MAAMc,KAAI,GAAI,IAAIC,IAAI,CAAC;MACvB,IAAI,CAAC1C,gBAAgB,CAACG,aAAY,GAAIsC,KAAK,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MACtE,IAAI,CAAC5C,gBAAgB,CAACI,aAAY,GAAI,OAAM;IAC9C,CAAC;IAEDyC,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACrC,eAAe,CAACjB,IAAG,GAAI,IAAG;;MAE/B;MACA,IAAI,CAACwB,KAAK,CAACsB,OAAO,CAACX,IAAG,IAAK;QACzB,IAAI,CAAClB,eAAe,CAACP,aAAa,CAACyB,IAAI,CAACI,EAAE,IAAIJ,IAAI,CAACC,QAAO;MAC5D,CAAC;IACH,CAAC;IAEDmB,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAACnC,mBAAmB,CAACpB,IAAG,GAAI,IAAG;;MAEnC;MACA,IAAI,CAACwB,KAAK,CAACsB,OAAO,CAACX,IAAG,IAAK;QACzB,IAAI,CAACf,mBAAmB,CAACV,aAAa,CAACyB,IAAI,CAACI,EAAE,IAAIJ,IAAI,CAACC,QAAO;MAChE,CAAC;IACH,CAAC;IAEDY,gBAAgBA,CAAA,EAAG;MACjB,MAAMQ,KAAI,GAAI,0EAAyE;MACvF,IAAIC,QAAO,GAAI,EAAC;;MAEhB;MACA,MAAMC,MAAK,GAAI,IAAI,CAAChC,QAAQ,CAACiC,IAAI,CAACrB,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,IAAI,CAACxC,mBAAmB,CAACG,QAAQ;MACjF,MAAM0D,SAAQ,GAAIF,MAAK,GAAIA,MAAM,CAACE,SAAQ,GAAI,EAAC;;MAE/C;MACA,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAID,SAAS,EAAEC,CAAC,EAAE,EAAE;QAClCJ,QAAO,IAAKD,KAAK,CAACM,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIT,KAAK,CAACvB,MAAM,CAAC;MACnE;MAEA,IAAI,CAAClC,mBAAmB,CAACI,iBAAgB,GAAIsD,QAAO;IACtD,CAAC;IAED,MAAMS,cAAcA,CAAA,EAAG;MACrB,IAAI,IAAI,CAACnE,mBAAmB,CAACE,MAAK,KAAM,QAAO,IAAK,IAAI,CAAC0B,gBAAgB,EAAE;QACzE;MACF;MAEA,IAAI,CAAC9B,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAM4D,QAAO,GAAI,IAAI,CAAC1D,mBAAmB,CAACE,MAAK,KAAM,MAAK,GACtD,IAAI,CAACF,mBAAmB,CAACI,iBAAgB,GACzC,IAAI,CAACJ,mBAAmB,CAACK,WAAU;QAEvC,MAAM,IAAI,CAACuC,MAAM,CAACwB,QAAQ,CAAC,oBAAoB,EAAE;UAC/CC,MAAM,EAAE,IAAI,CAACtE,WAAW,CAACyC,EAAE;UAC3B8B,SAAS,EAAE;QACb,CAAC;QAED,IAAI,CAACtE,mBAAmB,CAACC,IAAG,GAAI,KAAI;;QAEpC;QACAsE,KAAK,CAAC,WAAW,IAAI,CAACxE,WAAW,CAACN,IAAI,OAAO;MAC/C,EAAE,OAAO+E,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAACzE,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED,MAAM4E,oBAAoBA,CAAA,EAAG;MAC3B,MAAMC,eAAc,GAAI7C,MAAM,CAAC8C,OAAO,CAAC,IAAI,CAAClE,gBAAgB,CAACC,aAAa,EACvEqB,MAAM,CAAC,CAAC,CAACQ,EAAE,EAAEH,QAAQ,CAAC,KAAKA,QAAQ,EACnCwC,GAAG,CAAC,CAAC,CAACrC,EAAE,CAAC,KAAKsC,QAAQ,CAACtC,EAAE,CAAC;MAE7B,IAAImC,eAAe,CAACzC,MAAK,KAAM,CAAC,EAAE;QAChCqC,KAAK,CAAC,YAAY;QAClB;MACF;MAEA,IAAI,IAAI,CAAC7D,gBAAgB,CAACE,aAAY,KAAM,WAAW,EAAE;QACvD;QACA2D,KAAK,CAAC,cAAc;QACpB,IAAI,CAAC7D,gBAAgB,CAACT,IAAG,GAAI,KAAI;QACjC;MACF;MAEA,IAAI,CAACH,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAM,IAAI,CAAC8C,MAAM,CAACwB,QAAQ,CAAC,0BAA0B,EAAE;UACrDW,OAAO,EAAEJ,eAAe;UACxBK,MAAM,EAAE;QACV,CAAC;QAED,IAAI,CAACtE,gBAAgB,CAACT,IAAG,GAAI,KAAI;;QAEjC;QACAsE,KAAK,CAAC,QAAQI,eAAe,CAACzC,MAAM,WAAW;MACjD,EAAE,OAAOsC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK;QAC/BD,KAAK,CAAC,eAAe;MACvB,UAAU;QACR,IAAI,CAACzE,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED,MAAMmF,gBAAgBA,CAAA,EAAG;MACvB,MAAMN,eAAc,GAAI7C,MAAM,CAAC8C,OAAO,CAAC,IAAI,CAAC1D,eAAe,CAACP,aAAa,EACtEqB,MAAM,CAAC,CAAC,CAACQ,EAAE,EAAEH,QAAQ,CAAC,KAAKA,QAAQ,EACnCwC,GAAG,CAAC,CAAC,CAACrC,EAAE,CAAC,KAAKsC,QAAQ,CAACtC,EAAE,CAAC;MAE7B,IAAImC,eAAe,CAACzC,MAAK,KAAM,CAAC,EAAE;QAChCqC,KAAK,CAAC,YAAY;QAClB;MACF;MAEA,IAAI,CAACzE,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAM,IAAI,CAAC8C,MAAM,CAACwB,QAAQ,CAAC,oBAAoB,EAAE;UAC/CjE,QAAQ,EAAE,IAAI,CAACe,eAAe,CAACf,QAAQ;UACvC4E,OAAO,EAAEJ;QACX,CAAC;QAED,IAAI,CAACzD,eAAe,CAACjB,IAAG,GAAI,KAAI;;QAEhC;QACAsE,KAAK,CAAC,QAAQI,eAAe,CAACzC,MAAM,aAAa;MACnD,EAAE,OAAOsC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAACzE,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED,MAAMoF,cAAcA,CAAA,EAAG;MACrB,MAAMP,eAAc,GAAI7C,MAAM,CAAC8C,OAAO,CAAC,IAAI,CAACvD,mBAAmB,CAACV,aAAa,EAC1EqB,MAAM,CAAC,CAAC,CAACQ,EAAE,EAAEH,QAAQ,CAAC,KAAKA,QAAQ,EACnCwC,GAAG,CAAC,CAAC,CAACrC,EAAE,CAAC,KAAKsC,QAAQ,CAACtC,EAAE,CAAC;MAE7B,IAAImC,eAAe,CAACzC,MAAK,KAAM,CAAC,EAAE;QAChCqC,KAAK,CAAC,YAAY;QAClB;MACF;MAEA,IAAI,CAACzE,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAM,IAAI,CAAC8C,MAAM,CAACwB,QAAQ,CAAC,0BAA0B,EAAE;UACrDW,OAAO,EAAEJ,eAAe;UACxBK,MAAM,EAAE;QACV,CAAC;QAED,IAAI,CAAC3D,mBAAmB,CAACpB,IAAG,GAAI,KAAI;;QAEpC;QACAsE,KAAK,CAAC,QAAQI,eAAe,CAACzC,MAAM,eAAe;MACrD,EAAE,OAAOsC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAACzE,UAAS,GAAI,KAAI;MACxB;IACF;EACF,CAAC;EACDqF,OAAOA,CAAA,EAAG;IACR;IACA,MAAMhC,KAAI,GAAI,IAAIC,IAAI,CAAC;IACvB,IAAI,CAAC1C,gBAAgB,CAACG,aAAY,GAAIsC,KAAK,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IACtE,IAAI,CAAC5C,gBAAgB,CAACI,aAAY,GAAI,OAAM;EAC9C;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}