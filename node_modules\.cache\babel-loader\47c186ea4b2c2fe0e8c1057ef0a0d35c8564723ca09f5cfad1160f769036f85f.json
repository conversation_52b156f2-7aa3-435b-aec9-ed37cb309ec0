{"ast": null, "code": "import { createApp } from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport store from './store';\nimport './assets/styles/index.css';\nimport { library } from '@fortawesome/fontawesome-svg-core';\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';\nimport { faShieldAlt, faServer, faKey, faClock, faCheck, faCog, faCheckSquare, faCalendarAlt, faTimes, faHistory, faPlus, faSyncAlt, faExclamationTriangle, faLock, faCalendarCheck, faEye, faEyeSlash, faExclamationCircle, faTable, faThLarge, faSearch, faEdit, faTrashAlt,\n// 新增的图标\nfaBell, faUser, faChevronDown, faSignOutAlt, faSun, faMoon, faTachometerAlt, faUsers, faMagic, faCopy, faCheckCircle, faInfoCircle, faDownload, faShieldCheck, faLightbulb, faTasks, faPlay, faPause, faTrash } from '@fortawesome/free-solid-svg-icons';\nlibrary.add(faShieldAlt, faServer, faKey, faClock, faCheck, faCog, faCheckSquare, faCalendarAlt, faTimes, faHistory, faPlus, faSyncAlt, faExclamationTriangle, faLock, faCalendarCheck, faEye, faEyeSlash, faExclamationCircle, faTable, faThLarge, faSearch, faEdit, faTrashAlt);\ncreateApp(App).use(router).use(store).component('font-awesome-icon', FontAwesomeIcon).mount('#app');", "map": {"version": 3, "names": ["createApp", "App", "router", "store", "library", "FontAwesomeIcon", "faShieldAlt", "faServer", "faKey", "faClock", "faCheck", "faCog", "faCheckSquare", "faCalendarAlt", "faTimes", "faHistory", "faPlus", "faSyncAlt", "faExclamationTriangle", "faLock", "faCalendarCheck", "faEye", "faEyeSlash", "faExclamationCircle", "faTable", "faThLarge", "faSearch", "faEdit", "faTrashAlt", "faBell", "faUser", "faChevronDown", "faSignOutAlt", "faSun", "faMoon", "faTachometerAlt", "faUsers", "faMagic", "faCopy", "faCheckCircle", "faInfoCircle", "faDownload", "faShieldCheck", "faLightbulb", "faTasks", "faPlay", "faPause", "faTrash", "add", "use", "component", "mount"], "sources": ["D:/demo/ooo/pass/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport store from './store'\r\nimport './assets/styles/index.css'\r\nimport { library } from '@fortawesome/fontawesome-svg-core'\r\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'\r\nimport {\r\n    faShieldAlt, faServer, faKey, faClock, faCheck,\r\n    faCog, faCheckSquare, faCalendarAlt, faTimes,\r\n    faHistory, faPlus, faSyncAlt, faExclamationTriangle,\r\n    faLock, faCalendarCheck, faEye, faEyeSlash, faExclamationCircle,\r\n    faTable, faThLarge, faSearch, faEdit, faTrashAlt,\r\n    // 新增的图标\r\n    faBell, faUser, faChevronDown, faSignOutAlt, faSun, faMoon,\r\n    faTachometerAlt, faUsers, faMagic, faCopy, faCheckCircle,\r\n    faInfoCircle, faDownload, faShieldCheck, faLightbulb,\r\n    faTasks, faPlay, faPause, faTrash\r\n} from '@fortawesome/free-solid-svg-icons'\r\n\r\nlibrary.add(\r\n    faShieldAlt, faServer, faKey, faClock, faCheck,\r\n    faCog, faCheckSquare, faCalendarAlt, faTimes,\r\n    faHistory, faPlus, faSyncAlt, faExclamationTriangle,\r\n    faLock, faCalendarCheck, faEye, faEyeSlash, faExclamationCircle,\r\n    faTable, faThLarge, faSearch, faEdit, faTrashAlt\r\n)\r\n\r\ncreateApp(App)\r\n    .use(router)\r\n    .use(store)\r\n    .component('font-awesome-icon', FontAwesomeIcon)\r\n    .mount('#app') "], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAO,2BAA2B;AAClC,SAASC,OAAO,QAAQ,mCAAmC;AAC3D,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SACIC,WAAW,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAC9CC,KAAK,EAAEC,aAAa,EAAEC,aAAa,EAAEC,OAAO,EAC5CC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,qBAAqB,EACnDC,MAAM,EAAEC,eAAe,EAAEC,KAAK,EAAEC,UAAU,EAAEC,mBAAmB,EAC/DC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,UAAU;AAChD;AACAC,MAAM,EAAEC,MAAM,EAAEC,aAAa,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAC1DC,eAAe,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,aAAa,EACxDC,YAAY,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,EACpDC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,QAC9B,mCAAmC;AAE1C3C,OAAO,CAAC4C,GAAG,CACP1C,WAAW,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAC9CC,KAAK,EAAEC,aAAa,EAAEC,aAAa,EAAEC,OAAO,EAC5CC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,qBAAqB,EACnDC,MAAM,EAAEC,eAAe,EAAEC,KAAK,EAAEC,UAAU,EAAEC,mBAAmB,EAC/DC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,UAC1C,CAAC;AAED5B,SAAS,CAACC,GAAG,CAAC,CACTgD,GAAG,CAAC/C,MAAM,CAAC,CACX+C,GAAG,CAAC9C,KAAK,CAAC,CACV+C,SAAS,CAAC,mBAAmB,EAAE7C,eAAe,CAAC,CAC/C8C,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}