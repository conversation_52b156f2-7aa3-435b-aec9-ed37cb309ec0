{"ast": null, "code": "export default {\n  name: 'NotificationSystem',\n  data() {\n    return {\n      notifications: [],\n      nextId: 1,\n      timers: new Map()\n    };\n  },\n  mounted() {\n    // 监听全局通知事件\n    this.$eventBus.on('show-notification', this.showNotification);\n    this.$eventBus.on('show-success', (message, title = '成功') => {\n      this.showNotification({\n        type: 'success',\n        title,\n        message\n      });\n    });\n    this.$eventBus.on('show-error', (message, title = '错误') => {\n      this.showNotification({\n        type: 'error',\n        title,\n        message\n      });\n    });\n    this.$eventBus.on('show-warning', (message, title = '警告') => {\n      this.showNotification({\n        type: 'warning',\n        title,\n        message\n      });\n    });\n    this.$eventBus.on('show-info', (message, title = '信息') => {\n      this.showNotification({\n        type: 'info',\n        title,\n        message\n      });\n    });\n  },\n  beforeUnmount() {\n    // 清理事件监听器和定时器\n    this.$eventBus.off('show-notification', this.showNotification);\n    this.$eventBus.off('show-success');\n    this.$eventBus.off('show-error');\n    this.$eventBus.off('show-warning');\n    this.$eventBus.off('show-info');\n    this.timers.forEach(timer => clearInterval(timer.interval));\n  },\n  methods: {\n    showNotification(options) {\n      const notification = {\n        id: this.nextId++,\n        type: options.type || 'info',\n        title: options.title || '',\n        message: options.message || '',\n        actions: options.actions || [],\n        autoClose: options.autoClose !== false,\n        duration: options.duration || 5000,\n        createdAt: Date.now(),\n        progress: 100\n      };\n      this.notifications.push(notification);\n\n      // 设置自动关闭\n      if (notification.autoClose) {\n        this.startAutoClose(notification);\n      }\n\n      // 限制通知数量\n      if (this.notifications.length > 5) {\n        this.removeNotification(this.notifications[0].id);\n      }\n    },\n    removeNotification(id) {\n      const index = this.notifications.findIndex(n => n.id === id);\n      if (index > -1) {\n        this.notifications.splice(index, 1);\n\n        // 清理定时器\n        if (this.timers.has(id)) {\n          clearInterval(this.timers.get(id).interval);\n          this.timers.delete(id);\n        }\n      }\n    },\n    startAutoClose(notification) {\n      const startTime = Date.now();\n      const interval = setInterval(() => {\n        const elapsed = Date.now() - startTime;\n        const progress = Math.max(0, 100 - elapsed / notification.duration * 100);\n        notification.progress = progress;\n        if (progress <= 0) {\n          this.removeNotification(notification.id);\n        }\n      }, 50);\n      this.timers.set(notification.id, {\n        interval,\n        startTime\n      });\n    },\n    handleAction(notification, action) {\n      if (action.handler) {\n        action.handler(notification);\n      }\n      if (action.closeOnClick !== false) {\n        this.removeNotification(notification.id);\n      }\n    },\n    getNotificationClass(type) {\n      const classes = {\n        success: 'border-l-4 border-green-500',\n        error: 'border-l-4 border-red-500',\n        warning: 'border-l-4 border-yellow-500',\n        info: 'border-l-4 border-blue-500'\n      };\n      return classes[type] || classes.info;\n    },\n    getNotificationIcon(type) {\n      const icons = {\n        success: 'check-circle',\n        error: 'exclamation-circle',\n        warning: 'exclamation-triangle',\n        info: 'info-circle'\n      };\n      return icons[type] || icons.info;\n    },\n    getIconClass(type) {\n      const classes = {\n        success: 'text-green-600 dark:text-green-400',\n        error: 'text-red-600 dark:text-red-400',\n        warning: 'text-yellow-600 dark:text-yellow-400',\n        info: 'text-blue-600 dark:text-blue-400'\n      };\n      return classes[type] || classes.info;\n    },\n    getIconBgClass(type) {\n      const classes = {\n        success: 'bg-green-100 dark:bg-green-900/30',\n        error: 'bg-red-100 dark:bg-red-900/30',\n        warning: 'bg-yellow-100 dark:bg-yellow-900/30',\n        info: 'bg-blue-100 dark:bg-blue-900/30'\n      };\n      return classes[type] || classes.info;\n    },\n    getProgressBarClass(type) {\n      const classes = {\n        success: 'bg-green-500',\n        error: 'bg-red-500',\n        warning: 'bg-yellow-500',\n        info: 'bg-blue-500'\n      };\n      return classes[type] || classes.info;\n    },\n    getProgressWidth(notification) {\n      return notification.progress || 0;\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "notifications", "nextId", "timers", "Map", "mounted", "$eventBus", "on", "showNotification", "message", "title", "type", "beforeUnmount", "off", "for<PERSON>ach", "timer", "clearInterval", "interval", "methods", "options", "notification", "id", "actions", "autoClose", "duration", "createdAt", "Date", "now", "progress", "push", "startAutoClose", "length", "removeNotification", "index", "findIndex", "n", "splice", "has", "get", "delete", "startTime", "setInterval", "elapsed", "Math", "max", "set", "handleAction", "action", "handler", "closeOnClick", "getNotificationClass", "classes", "success", "error", "warning", "info", "getNotificationIcon", "icons", "getIconClass", "getIconBgClass", "getProgressBarClass", "getProgressWidth"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\NotificationSystem.vue"], "sourcesContent": ["<template>\n  <!-- 通知容器 -->\n  <div class=\"notification-container\">\n    <!-- 通知列表 -->\n    <transition-group name=\"notification\" tag=\"div\" class=\"fixed top-4 right-4 z-50 space-y-2\">\n      <div\n        v-for=\"notification in notifications\"\n        :key=\"notification.id\"\n        class=\"notification-item max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden\"\n        :class=\"getNotificationClass(notification.type)\"\n      >\n        <div class=\"p-4\">\n          <div class=\"flex items-start\">\n            <!-- 图标 -->\n            <div class=\"flex-shrink-0\">\n              <div class=\"w-8 h-8 rounded-full flex items-center justify-center\" :class=\"getIconBgClass(notification.type)\">\n                <font-awesome-icon \n                  :icon=\"['fas', getNotificationIcon(notification.type)]\" \n                  :class=\"getIconClass(notification.type)\"\n                  class=\"text-sm\"\n                />\n              </div>\n            </div>\n            \n            <!-- 内容 -->\n            <div class=\"ml-3 w-0 flex-1\">\n              <p class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                {{ notification.title }}\n              </p>\n              <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n                {{ notification.message }}\n              </p>\n              \n              <!-- 操作按钮 -->\n              <div v-if=\"notification.actions && notification.actions.length > 0\" class=\"mt-3 flex space-x-2\">\n                <button\n                  v-for=\"action in notification.actions\"\n                  :key=\"action.label\"\n                  @click=\"handleAction(notification, action)\"\n                  class=\"text-xs font-medium px-3 py-1 rounded-md transition-colors\"\n                  :class=\"action.primary \n                    ? 'bg-blue-600 text-white hover:bg-blue-700' \n                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'\"\n                >\n                  {{ action.label }}\n                </button>\n              </div>\n            </div>\n            \n            <!-- 关闭按钮 -->\n            <div class=\"ml-4 flex-shrink-0 flex\">\n              <button\n                @click=\"removeNotification(notification.id)\"\n                class=\"bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                <font-awesome-icon :icon=\"['fas', 'times']\" class=\"text-sm\" />\n              </button>\n            </div>\n          </div>\n          \n          <!-- 进度条（用于自动消失的通知） -->\n          <div v-if=\"notification.autoClose && notification.duration\" class=\"mt-3\">\n            <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1\">\n              <div \n                class=\"h-1 rounded-full transition-all duration-100 ease-linear\"\n                :class=\"getProgressBarClass(notification.type)\"\n                :style=\"{ width: getProgressWidth(notification) + '%' }\"\n              ></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </transition-group>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'NotificationSystem',\n  data() {\n    return {\n      notifications: [],\n      nextId: 1,\n      timers: new Map()\n    }\n  },\n  \n  mounted() {\n    // 监听全局通知事件\n    this.$eventBus.on('show-notification', this.showNotification)\n    this.$eventBus.on('show-success', (message, title = '成功') => {\n      this.showNotification({ type: 'success', title, message })\n    })\n    this.$eventBus.on('show-error', (message, title = '错误') => {\n      this.showNotification({ type: 'error', title, message })\n    })\n    this.$eventBus.on('show-warning', (message, title = '警告') => {\n      this.showNotification({ type: 'warning', title, message })\n    })\n    this.$eventBus.on('show-info', (message, title = '信息') => {\n      this.showNotification({ type: 'info', title, message })\n    })\n  },\n\n  beforeUnmount() {\n    // 清理事件监听器和定时器\n    this.$eventBus.off('show-notification', this.showNotification)\n    this.$eventBus.off('show-success')\n    this.$eventBus.off('show-error')\n    this.$eventBus.off('show-warning')\n    this.$eventBus.off('show-info')\n\n    this.timers.forEach(timer => clearInterval(timer.interval))\n  },\n  \n  methods: {\n    showNotification(options) {\n      const notification = {\n        id: this.nextId++,\n        type: options.type || 'info',\n        title: options.title || '',\n        message: options.message || '',\n        actions: options.actions || [],\n        autoClose: options.autoClose !== false,\n        duration: options.duration || 5000,\n        createdAt: Date.now(),\n        progress: 100\n      }\n      \n      this.notifications.push(notification)\n      \n      // 设置自动关闭\n      if (notification.autoClose) {\n        this.startAutoClose(notification)\n      }\n      \n      // 限制通知数量\n      if (this.notifications.length > 5) {\n        this.removeNotification(this.notifications[0].id)\n      }\n    },\n    \n    removeNotification(id) {\n      const index = this.notifications.findIndex(n => n.id === id)\n      if (index > -1) {\n        this.notifications.splice(index, 1)\n        \n        // 清理定时器\n        if (this.timers.has(id)) {\n          clearInterval(this.timers.get(id).interval)\n          this.timers.delete(id)\n        }\n      }\n    },\n    \n    startAutoClose(notification) {\n      const startTime = Date.now()\n      const interval = setInterval(() => {\n        const elapsed = Date.now() - startTime\n        const progress = Math.max(0, 100 - (elapsed / notification.duration) * 100)\n        \n        notification.progress = progress\n        \n        if (progress <= 0) {\n          this.removeNotification(notification.id)\n        }\n      }, 50)\n      \n      this.timers.set(notification.id, { interval, startTime })\n    },\n    \n    handleAction(notification, action) {\n      if (action.handler) {\n        action.handler(notification)\n      }\n      \n      if (action.closeOnClick !== false) {\n        this.removeNotification(notification.id)\n      }\n    },\n    \n    getNotificationClass(type) {\n      const classes = {\n        success: 'border-l-4 border-green-500',\n        error: 'border-l-4 border-red-500',\n        warning: 'border-l-4 border-yellow-500',\n        info: 'border-l-4 border-blue-500'\n      }\n      return classes[type] || classes.info\n    },\n    \n    getNotificationIcon(type) {\n      const icons = {\n        success: 'check-circle',\n        error: 'exclamation-circle',\n        warning: 'exclamation-triangle',\n        info: 'info-circle'\n      }\n      return icons[type] || icons.info\n    },\n    \n    getIconClass(type) {\n      const classes = {\n        success: 'text-green-600 dark:text-green-400',\n        error: 'text-red-600 dark:text-red-400',\n        warning: 'text-yellow-600 dark:text-yellow-400',\n        info: 'text-blue-600 dark:text-blue-400'\n      }\n      return classes[type] || classes.info\n    },\n    \n    getIconBgClass(type) {\n      const classes = {\n        success: 'bg-green-100 dark:bg-green-900/30',\n        error: 'bg-red-100 dark:bg-red-900/30',\n        warning: 'bg-yellow-100 dark:bg-yellow-900/30',\n        info: 'bg-blue-100 dark:bg-blue-900/30'\n      }\n      return classes[type] || classes.info\n    },\n    \n    getProgressBarClass(type) {\n      const classes = {\n        success: 'bg-green-500',\n        error: 'bg-red-500',\n        warning: 'bg-yellow-500',\n        info: 'bg-blue-500'\n      }\n      return classes[type] || classes.info\n    },\n    \n    getProgressWidth(notification) {\n      return notification.progress || 0\n    }\n  }\n}\n</script>\n\n<style scoped>\n.notification-container {\n  pointer-events: none;\n}\n\n.notification-item {\n  pointer-events: auto;\n}\n\n/* 通知动画 */\n.notification-enter-active {\n  transition: all 0.3s ease-out;\n}\n\n.notification-leave-active {\n  transition: all 0.3s ease-in;\n}\n\n.notification-enter-from {\n  opacity: 0;\n  transform: translateX(100%);\n}\n\n.notification-leave-to {\n  opacity: 0;\n  transform: translateX(100%);\n}\n\n.notification-move {\n  transition: transform 0.3s ease;\n}\n\n/* 响应式设计 */\n@media (max-width: 640px) {\n  .notification-item {\n    max-width: calc(100vw - 2rem);\n    margin: 0 1rem;\n  }\n}\n</style>\n"], "mappings": "AA6EA,eAAe;EACbA,IAAI,EAAE,oBAAoB;EAC1BC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,aAAa,EAAE,EAAE;MACjBC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,IAAIC,GAAG,CAAC;IAClB;EACF,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR;IACA,IAAI,CAACC,SAAS,CAACC,EAAE,CAAC,mBAAmB,EAAE,IAAI,CAACC,gBAAgB;IAC5D,IAAI,CAACF,SAAS,CAACC,EAAE,CAAC,cAAc,EAAE,CAACE,OAAO,EAAEC,KAAI,GAAI,IAAI,KAAK;MAC3D,IAAI,CAACF,gBAAgB,CAAC;QAAEG,IAAI,EAAE,SAAS;QAAED,KAAK;QAAED;MAAQ,CAAC;IAC3D,CAAC;IACD,IAAI,CAACH,SAAS,CAACC,EAAE,CAAC,YAAY,EAAE,CAACE,OAAO,EAAEC,KAAI,GAAI,IAAI,KAAK;MACzD,IAAI,CAACF,gBAAgB,CAAC;QAAEG,IAAI,EAAE,OAAO;QAAED,KAAK;QAAED;MAAQ,CAAC;IACzD,CAAC;IACD,IAAI,CAACH,SAAS,CAACC,EAAE,CAAC,cAAc,EAAE,CAACE,OAAO,EAAEC,KAAI,GAAI,IAAI,KAAK;MAC3D,IAAI,CAACF,gBAAgB,CAAC;QAAEG,IAAI,EAAE,SAAS;QAAED,KAAK;QAAED;MAAQ,CAAC;IAC3D,CAAC;IACD,IAAI,CAACH,SAAS,CAACC,EAAE,CAAC,WAAW,EAAE,CAACE,OAAO,EAAEC,KAAI,GAAI,IAAI,KAAK;MACxD,IAAI,CAACF,gBAAgB,CAAC;QAAEG,IAAI,EAAE,MAAM;QAAED,KAAK;QAAED;MAAQ,CAAC;IACxD,CAAC;EACH,CAAC;EAEDG,aAAaA,CAAA,EAAG;IACd;IACA,IAAI,CAACN,SAAS,CAACO,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACL,gBAAgB;IAC7D,IAAI,CAACF,SAAS,CAACO,GAAG,CAAC,cAAc;IACjC,IAAI,CAACP,SAAS,CAACO,GAAG,CAAC,YAAY;IAC/B,IAAI,CAACP,SAAS,CAACO,GAAG,CAAC,cAAc;IACjC,IAAI,CAACP,SAAS,CAACO,GAAG,CAAC,WAAW;IAE9B,IAAI,CAACV,MAAM,CAACW,OAAO,CAACC,KAAI,IAAKC,aAAa,CAACD,KAAK,CAACE,QAAQ,CAAC;EAC5D,CAAC;EAEDC,OAAO,EAAE;IACPV,gBAAgBA,CAACW,OAAO,EAAE;MACxB,MAAMC,YAAW,GAAI;QACnBC,EAAE,EAAE,IAAI,CAACnB,MAAM,EAAE;QACjBS,IAAI,EAAEQ,OAAO,CAACR,IAAG,IAAK,MAAM;QAC5BD,KAAK,EAAES,OAAO,CAACT,KAAI,IAAK,EAAE;QAC1BD,OAAO,EAAEU,OAAO,CAACV,OAAM,IAAK,EAAE;QAC9Ba,OAAO,EAAEH,OAAO,CAACG,OAAM,IAAK,EAAE;QAC9BC,SAAS,EAAEJ,OAAO,CAACI,SAAQ,KAAM,KAAK;QACtCC,QAAQ,EAAEL,OAAO,CAACK,QAAO,IAAK,IAAI;QAClCC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBC,QAAQ,EAAE;MACZ;MAEA,IAAI,CAAC3B,aAAa,CAAC4B,IAAI,CAACT,YAAY;;MAEpC;MACA,IAAIA,YAAY,CAACG,SAAS,EAAE;QAC1B,IAAI,CAACO,cAAc,CAACV,YAAY;MAClC;;MAEA;MACA,IAAI,IAAI,CAACnB,aAAa,CAAC8B,MAAK,GAAI,CAAC,EAAE;QACjC,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAAC/B,aAAa,CAAC,CAAC,CAAC,CAACoB,EAAE;MAClD;IACF,CAAC;IAEDW,kBAAkBA,CAACX,EAAE,EAAE;MACrB,MAAMY,KAAI,GAAI,IAAI,CAAChC,aAAa,CAACiC,SAAS,CAACC,CAAA,IAAKA,CAAC,CAACd,EAAC,KAAMA,EAAE;MAC3D,IAAIY,KAAI,GAAI,CAAC,CAAC,EAAE;QACd,IAAI,CAAChC,aAAa,CAACmC,MAAM,CAACH,KAAK,EAAE,CAAC;;QAElC;QACA,IAAI,IAAI,CAAC9B,MAAM,CAACkC,GAAG,CAAChB,EAAE,CAAC,EAAE;UACvBL,aAAa,CAAC,IAAI,CAACb,MAAM,CAACmC,GAAG,CAACjB,EAAE,CAAC,CAACJ,QAAQ;UAC1C,IAAI,CAACd,MAAM,CAACoC,MAAM,CAAClB,EAAE;QACvB;MACF;IACF,CAAC;IAEDS,cAAcA,CAACV,YAAY,EAAE;MAC3B,MAAMoB,SAAQ,GAAId,IAAI,CAACC,GAAG,CAAC;MAC3B,MAAMV,QAAO,GAAIwB,WAAW,CAAC,MAAM;QACjC,MAAMC,OAAM,GAAIhB,IAAI,CAACC,GAAG,CAAC,IAAIa,SAAQ;QACrC,MAAMZ,QAAO,GAAIe,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,GAAE,GAAKF,OAAM,GAAItB,YAAY,CAACI,QAAQ,GAAI,GAAG;QAE1EJ,YAAY,CAACQ,QAAO,GAAIA,QAAO;QAE/B,IAAIA,QAAO,IAAK,CAAC,EAAE;UACjB,IAAI,CAACI,kBAAkB,CAACZ,YAAY,CAACC,EAAE;QACzC;MACF,CAAC,EAAE,EAAE;MAEL,IAAI,CAAClB,MAAM,CAAC0C,GAAG,CAACzB,YAAY,CAACC,EAAE,EAAE;QAAEJ,QAAQ;QAAEuB;MAAU,CAAC;IAC1D,CAAC;IAEDM,YAAYA,CAAC1B,YAAY,EAAE2B,MAAM,EAAE;MACjC,IAAIA,MAAM,CAACC,OAAO,EAAE;QAClBD,MAAM,CAACC,OAAO,CAAC5B,YAAY;MAC7B;MAEA,IAAI2B,MAAM,CAACE,YAAW,KAAM,KAAK,EAAE;QACjC,IAAI,CAACjB,kBAAkB,CAACZ,YAAY,CAACC,EAAE;MACzC;IACF,CAAC;IAED6B,oBAAoBA,CAACvC,IAAI,EAAE;MACzB,MAAMwC,OAAM,GAAI;QACdC,OAAO,EAAE,6BAA6B;QACtCC,KAAK,EAAE,2BAA2B;QAClCC,OAAO,EAAE,8BAA8B;QACvCC,IAAI,EAAE;MACR;MACA,OAAOJ,OAAO,CAACxC,IAAI,KAAKwC,OAAO,CAACI,IAAG;IACrC,CAAC;IAEDC,mBAAmBA,CAAC7C,IAAI,EAAE;MACxB,MAAM8C,KAAI,GAAI;QACZL,OAAO,EAAE,cAAc;QACvBC,KAAK,EAAE,oBAAoB;QAC3BC,OAAO,EAAE,sBAAsB;QAC/BC,IAAI,EAAE;MACR;MACA,OAAOE,KAAK,CAAC9C,IAAI,KAAK8C,KAAK,CAACF,IAAG;IACjC,CAAC;IAEDG,YAAYA,CAAC/C,IAAI,EAAE;MACjB,MAAMwC,OAAM,GAAI;QACdC,OAAO,EAAE,oCAAoC;QAC7CC,KAAK,EAAE,gCAAgC;QACvCC,OAAO,EAAE,sCAAsC;QAC/CC,IAAI,EAAE;MACR;MACA,OAAOJ,OAAO,CAACxC,IAAI,KAAKwC,OAAO,CAACI,IAAG;IACrC,CAAC;IAEDI,cAAcA,CAAChD,IAAI,EAAE;MACnB,MAAMwC,OAAM,GAAI;QACdC,OAAO,EAAE,mCAAmC;QAC5CC,KAAK,EAAE,+BAA+B;QACtCC,OAAO,EAAE,qCAAqC;QAC9CC,IAAI,EAAE;MACR;MACA,OAAOJ,OAAO,CAACxC,IAAI,KAAKwC,OAAO,CAACI,IAAG;IACrC,CAAC;IAEDK,mBAAmBA,CAACjD,IAAI,EAAE;MACxB,MAAMwC,OAAM,GAAI;QACdC,OAAO,EAAE,cAAc;QACvBC,KAAK,EAAE,YAAY;QACnBC,OAAO,EAAE,eAAe;QACxBC,IAAI,EAAE;MACR;MACA,OAAOJ,OAAO,CAACxC,IAAI,KAAKwC,OAAO,CAACI,IAAG;IACrC,CAAC;IAEDM,gBAAgBA,CAACzC,YAAY,EAAE;MAC7B,OAAOA,YAAY,CAACQ,QAAO,IAAK;IAClC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}