{"ast": null, "code": "export default {\n  name: 'StatusBadge',\n  props: {\n    type: {\n      type: String,\n      required: true,\n      validator: value => ['normal', 'warning', 'error', 'running'].includes(value)\n    }\n  },\n  computed: {\n    statusText() {\n      const statusMap = {\n        normal: '正常',\n        warning: '密码即将过期',\n        error: '密码已过期',\n        running: '运行中'\n      };\n      return statusMap[this.type] || '未知状态';\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "type", "String", "required", "validator", "value", "includes", "computed", "statusText", "statusMap", "normal", "warning", "error", "running"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\StatusBadge.vue"], "sourcesContent": ["<template>\r\n  <span :class=\"[\r\n    'status-badge',\r\n    `status-${type}`\r\n  ]\">\r\n    {{ statusText }}\r\n  </span>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'StatusBadge',\r\n  props: {\r\n    type: {\r\n      type: String,\r\n      required: true,\r\n      validator: (value) => ['normal', 'warning', 'error', 'running'].includes(value)\r\n    }\r\n  },\r\n  computed: {\r\n    statusText() {\r\n      const statusMap = {\r\n        normal: '正常',\r\n        warning: '密码即将过期',\r\n        error: '密码已过期',\r\n        running: '运行中'\r\n      }\r\n      \r\n      return statusMap[this.type] || '未知状态'\r\n    }\r\n  }\r\n}\r\n</script> "], "mappings": "AAUA,eAAe;EACbA,IAAI,EAAE,aAAa;EACnBC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJA,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAGC,KAAK,IAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,CAAC,CAACC,QAAQ,CAACD,KAAK;IAChF;EACF,CAAC;EACDE,QAAQ,EAAE;IACRC,UAAUA,CAAA,EAAG;MACX,MAAMC,SAAQ,GAAI;QAChBC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,QAAQ;QACjBC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE;MACX;MAEA,OAAOJ,SAAS,CAAC,IAAI,CAACR,IAAI,KAAK,MAAK;IACtC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}