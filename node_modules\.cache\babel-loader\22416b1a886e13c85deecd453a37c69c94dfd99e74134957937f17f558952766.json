{"ast": null, "code": "import { showSuccess, showError, showWarning, showInfo, showNotification } from '@/utils/notification.js';\nexport default {\n  name: 'NotificationTest',\n  methods: {\n    testSuccess() {\n      showSuccess('操作已成功完成！', '成功');\n    },\n    testError() {\n      showError('发生了一个错误，请检查您的输入。', '错误');\n    },\n    testWarning() {\n      showWarning('这是一个警告消息，请注意。', '警告');\n    },\n    testInfo() {\n      showInfo('这是一条信息通知。', '提示');\n    },\n    testPersistent() {\n      showNotification({\n        type: 'info',\n        title: '持久通知',\n        message: '这个通知不会自动关闭，需要手动关闭。',\n        autoClose: false\n      });\n    },\n    testLongDuration() {\n      showNotification({\n        type: 'warning',\n        title: '长时间通知',\n        message: '这个通知将显示10秒钟。',\n        duration: 10000\n      });\n    },\n    testWithActions() {\n      showNotification({\n        type: 'info',\n        title: '确认操作',\n        message: '您确定要执行此操作吗？',\n        autoClose: false,\n        actions: [{\n          text: '确认',\n          handler: () => {\n            showSuccess('操作已确认！');\n          }\n        }, {\n          text: '取消',\n          handler: () => {\n            showInfo('操作已取消。');\n          }\n        }]\n      });\n    },\n    testMultiple() {\n      showSuccess('第一个通知');\n      setTimeout(() => showWarning('第二个通知'), 500);\n      setTimeout(() => showInfo('第三个通知'), 1000);\n      setTimeout(() => showError('第四个通知'), 1500);\n    },\n    testLongMessage() {\n      showNotification({\n        type: 'info',\n        title: '详细信息',\n        message: '这是一个包含很长消息内容的通知。它可能包含多行文本，用于显示详细的信息或说明。通知系统会自动调整高度以适应内容。',\n        duration: 8000\n      });\n    },\n    async testAsync() {\n      showInfo('开始异步操作...', '处理中');\n      try {\n        // 模拟异步操作\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        showSuccess('异步操作完成！', '成功');\n      } catch (error) {\n        showError('异步操作失败！', '错误');\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["showSuccess", "showError", "showWarning", "showInfo", "showNotification", "name", "methods", "testSuccess", "testError", "testWarning", "testInfo", "testPersistent", "type", "title", "message", "autoClose", "testLongDuration", "duration", "testWithActions", "actions", "text", "handler", "testMultiple", "setTimeout", "testLongMessage", "testAsync", "Promise", "resolve", "error"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\NotificationTest.vue"], "sourcesContent": ["<template>\n  <div class=\"space-y-6\">\n    <!-- 页面标题 -->\n    <div class=\"flex items-center justify-between\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">通知系统测试</h1>\n        <p class=\"text-gray-600 dark:text-gray-400\">测试各种类型的通知消息</p>\n      </div>\n    </div>\n\n    <!-- 基础通知测试 -->\n    <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n      <h3 class=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">基础通知测试</h3>\n      \n      <div class=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n        <button \n          @click=\"testSuccess\"\n          class=\"inline-flex items-center justify-center px-4 py-3 bg-green-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500\"\n        >\n          <font-awesome-icon :icon=\"['fas', 'check-circle']\" class=\"mr-2\" />\n          成功通知\n        </button>\n        \n        <button \n          @click=\"testError\"\n          class=\"inline-flex items-center justify-center px-4 py-3 bg-red-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500\"\n        >\n          <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" class=\"mr-2\" />\n          错误通知\n        </button>\n        \n        <button \n          @click=\"testWarning\"\n          class=\"inline-flex items-center justify-center px-4 py-3 bg-yellow-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500\"\n        >\n          <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2\" />\n          警告通知\n        </button>\n        \n        <button \n          @click=\"testInfo\"\n          class=\"inline-flex items-center justify-center px-4 py-3 bg-blue-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        >\n          <font-awesome-icon :icon=\"['fas', 'info-circle']\" class=\"mr-2\" />\n          信息通知\n        </button>\n      </div>\n    </div>\n\n    <!-- 高级通知测试 -->\n    <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n      <h3 class=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">高级通知测试</h3>\n      \n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        <button \n          @click=\"testPersistent\"\n          class=\"inline-flex items-center justify-center px-4 py-3 bg-purple-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500\"\n        >\n          <font-awesome-icon :icon=\"['fas', 'lock']\" class=\"mr-2\" />\n          持久通知\n        </button>\n        \n        <button \n          @click=\"testLongDuration\"\n          class=\"inline-flex items-center justify-center px-4 py-3 bg-indigo-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n        >\n          <font-awesome-icon :icon=\"['fas', 'clock']\" class=\"mr-2\" />\n          长时间通知\n        </button>\n        \n        <button \n          @click=\"testWithActions\"\n          class=\"inline-flex items-center justify-center px-4 py-3 bg-teal-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500\"\n        >\n          <font-awesome-icon :icon=\"['fas', 'cog']\" class=\"mr-2\" />\n          带操作按钮\n        </button>\n        \n        <button \n          @click=\"testMultiple\"\n          class=\"inline-flex items-center justify-center px-4 py-3 bg-orange-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500\"\n        >\n          <font-awesome-icon :icon=\"['fas', 'copy']\" class=\"mr-2\" />\n          多个通知\n        </button>\n        \n        <button \n          @click=\"testLongMessage\"\n          class=\"inline-flex items-center justify-center px-4 py-3 bg-pink-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500\"\n        >\n          <font-awesome-icon :icon=\"['fas', 'align-left']\" class=\"mr-2\" />\n          长消息\n        </button>\n        \n        <button \n          @click=\"testAsync\"\n          class=\"inline-flex items-center justify-center px-4 py-3 bg-gray-600 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500\"\n        >\n          <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-2\" />\n          异步操作\n        </button>\n      </div>\n    </div>\n\n    <!-- 使用说明 -->\n    <div class=\"bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800 p-6\">\n      <h3 class=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n        <font-awesome-icon :icon=\"['fas', 'lightbulb']\" class=\"mr-2 text-blue-500\" />\n        使用说明\n      </h3>\n      \n      <div class=\"space-y-2 text-sm text-gray-700 dark:text-gray-300\">\n        <p>• <strong>成功通知</strong>：绿色，自动关闭，用于操作成功反馈</p>\n        <p>• <strong>错误通知</strong>：红色，不自动关闭，需要用户手动关闭</p>\n        <p>• <strong>警告通知</strong>：黄色，自动关闭，用于警告信息</p>\n        <p>• <strong>信息通知</strong>：蓝色，自动关闭，用于一般信息</p>\n        <p>• <strong>持久通知</strong>：不会自动关闭，需要用户手动关闭</p>\n        <p>• <strong>长时间通知</strong>：显示时间更长（10秒）</p>\n        <p>• <strong>带操作按钮</strong>：包含可点击的操作按钮</p>\n        <p>• <strong>多个通知</strong>：同时显示多个通知</p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { showSuccess, showError, showWarning, showInfo, showNotification } from '@/utils/notification.js'\n\nexport default {\n  name: 'NotificationTest',\n  methods: {\n    testSuccess() {\n      showSuccess('操作已成功完成！', '成功')\n    },\n    \n    testError() {\n      showError('发生了一个错误，请检查您的输入。', '错误')\n    },\n    \n    testWarning() {\n      showWarning('这是一个警告消息，请注意。', '警告')\n    },\n    \n    testInfo() {\n      showInfo('这是一条信息通知。', '提示')\n    },\n    \n    testPersistent() {\n      showNotification({\n        type: 'info',\n        title: '持久通知',\n        message: '这个通知不会自动关闭，需要手动关闭。',\n        autoClose: false\n      })\n    },\n    \n    testLongDuration() {\n      showNotification({\n        type: 'warning',\n        title: '长时间通知',\n        message: '这个通知将显示10秒钟。',\n        duration: 10000\n      })\n    },\n    \n    testWithActions() {\n      showNotification({\n        type: 'info',\n        title: '确认操作',\n        message: '您确定要执行此操作吗？',\n        autoClose: false,\n        actions: [\n          {\n            text: '确认',\n            handler: () => {\n              showSuccess('操作已确认！')\n            }\n          },\n          {\n            text: '取消',\n            handler: () => {\n              showInfo('操作已取消。')\n            }\n          }\n        ]\n      })\n    },\n    \n    testMultiple() {\n      showSuccess('第一个通知')\n      setTimeout(() => showWarning('第二个通知'), 500)\n      setTimeout(() => showInfo('第三个通知'), 1000)\n      setTimeout(() => showError('第四个通知'), 1500)\n    },\n    \n    testLongMessage() {\n      showNotification({\n        type: 'info',\n        title: '详细信息',\n        message: '这是一个包含很长消息内容的通知。它可能包含多行文本，用于显示详细的信息或说明。通知系统会自动调整高度以适应内容。',\n        duration: 8000\n      })\n    },\n    \n    async testAsync() {\n      showInfo('开始异步操作...', '处理中')\n      \n      try {\n        // 模拟异步操作\n        await new Promise(resolve => setTimeout(resolve, 2000))\n        showSuccess('异步操作完成！', '成功')\n      } catch (error) {\n        showError('异步操作失败！', '错误')\n      }\n    }\n  }\n}\n</script>\n"], "mappings": "AA8HA,SAASA,WAAW,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,gBAAe,QAAS,yBAAwB;AAExG,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,OAAO,EAAE;IACPC,WAAWA,CAAA,EAAG;MACZP,WAAW,CAAC,UAAU,EAAE,IAAI;IAC9B,CAAC;IAEDQ,SAASA,CAAA,EAAG;MACVP,SAAS,CAAC,kBAAkB,EAAE,IAAI;IACpC,CAAC;IAEDQ,WAAWA,CAAA,EAAG;MACZP,WAAW,CAAC,eAAe,EAAE,IAAI;IACnC,CAAC;IAEDQ,QAAQA,CAAA,EAAG;MACTP,QAAQ,CAAC,WAAW,EAAE,IAAI;IAC5B,CAAC;IAEDQ,cAAcA,CAAA,EAAG;MACfP,gBAAgB,CAAC;QACfQ,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE,oBAAoB;QAC7BC,SAAS,EAAE;MACb,CAAC;IACH,CAAC;IAEDC,gBAAgBA,CAAA,EAAG;MACjBZ,gBAAgB,CAAC;QACfQ,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,cAAc;QACvBG,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC;IAEDC,eAAeA,CAAA,EAAG;MAChBd,gBAAgB,CAAC;QACfQ,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE,aAAa;QACtBC,SAAS,EAAE,KAAK;QAChBI,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAEA,CAAA,KAAM;YACbrB,WAAW,CAAC,QAAQ;UACtB;QACF,CAAC,EACD;UACEoB,IAAI,EAAE,IAAI;UACVC,OAAO,EAAEA,CAAA,KAAM;YACblB,QAAQ,CAAC,QAAQ;UACnB;QACF;MAEJ,CAAC;IACH,CAAC;IAEDmB,YAAYA,CAAA,EAAG;MACbtB,WAAW,CAAC,OAAO;MACnBuB,UAAU,CAAC,MAAMrB,WAAW,CAAC,OAAO,CAAC,EAAE,GAAG;MAC1CqB,UAAU,CAAC,MAAMpB,QAAQ,CAAC,OAAO,CAAC,EAAE,IAAI;MACxCoB,UAAU,CAAC,MAAMtB,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI;IAC3C,CAAC;IAEDuB,eAAeA,CAAA,EAAG;MAChBpB,gBAAgB,CAAC;QACfQ,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE,0DAA0D;QACnEG,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC;IAED,MAAMQ,SAASA,CAAA,EAAG;MAChBtB,QAAQ,CAAC,WAAW,EAAE,KAAK;MAE3B,IAAI;QACF;QACA,MAAM,IAAIuB,OAAO,CAACC,OAAM,IAAKJ,UAAU,CAACI,OAAO,EAAE,IAAI,CAAC;QACtD3B,WAAW,CAAC,SAAS,EAAE,IAAI;MAC7B,EAAE,OAAO4B,KAAK,EAAE;QACd3B,SAAS,CAAC,SAAS,EAAE,IAAI;MAC3B;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}