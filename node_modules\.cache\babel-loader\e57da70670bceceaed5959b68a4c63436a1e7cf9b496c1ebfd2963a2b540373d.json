{"ast": null, "code": "import { mapState, mapGetters } from 'vuex';\nimport BaseModal from '@/components/BaseModal.vue';\nimport StatusBadge from '@/components/StatusBadge.vue';\nimport CustomCheckbox from '@/components/CustomCheckbox.vue';\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue';\nexport default {\n  name: 'HostManagement',\n  components: {\n    BaseModal,\n    StatusBadge,\n    CustomCheckbox,\n    PasswordStrengthMeter\n  },\n  data() {\n    return {\n      selectAll: false,\n      selectAllBatch: false,\n      processing: false,\n      currentHost: {},\n      passwordVisibility: {},\n      viewMode: 'table',\n      filterText: '',\n      statusFilter: 'all',\n      // 修改密码弹窗\n      changePasswordModal: {\n        show: false,\n        method: 'auto',\n        policyId: 1,\n        generatedPassword: 'aX7#9pQr$2Lm',\n        newPassword: '',\n        confirmPassword: '',\n        executeImmediately: true,\n        saveHistory: false,\n        logAudit: true\n      },\n      // 批量更新密码弹窗\n      batchUpdateModal: {\n        show: false,\n        selectedHosts: {},\n        policyId: 1,\n        executionTime: 'immediate',\n        scheduledDate: '',\n        scheduledTime: '',\n        ignoreErrors: true,\n        detailedLog: true,\n        sendNotification: false\n      },\n      // 批量应用策略弹窗\n      batchApplyModal: {\n        show: false,\n        selectedHosts: {},\n        policyId: 1,\n        updateImmediately: false,\n        applyOnNextUpdate: true\n      },\n      // 紧急重置密码弹窗\n      emergencyResetModal: {\n        show: false,\n        selectedHosts: {},\n        policyId: 3,\n        // 默认使用紧急策略\n        reason: 'security_incident',\n        description: ''\n      }\n    };\n  },\n  computed: {\n    ...mapState({\n      hosts: state => state.hosts,\n      policies: state => state.policies\n    }),\n    ...mapGetters(['selectedHosts']),\n    passwordMismatch() {\n      return this.changePasswordModal.newPassword && this.changePasswordModal.confirmPassword && this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword;\n    },\n    selectedHostsCount() {\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length;\n    },\n    selectedHostsList() {\n      return this.hosts.filter(host => host.selected);\n    },\n    emergencyPolicies() {\n      // 返回紧急策略和高强度策略\n      return this.policies.filter(p => p.id === 3 || p.id === 1);\n    },\n    // 过滤后的主机列表\n    filteredHosts() {\n      return this.hosts.filter(host => {\n        // 文本过滤\n        const textMatch = this.filterText === '' || host.name.toLowerCase().includes(this.filterText.toLowerCase()) || host.ip.includes(this.filterText);\n\n        // 状态过滤\n        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;\n        return textMatch && statusMatch;\n      });\n    }\n  },\n  methods: {\n    toggleSelectAll(value) {\n      this.$store.commit('selectAllHosts', value);\n    },\n    toggleSelectAllBatch(value) {\n      this.hosts.forEach(host => {\n        this.batchUpdateModal.selectedHosts[host.id] = value;\n      });\n    },\n    openChangePasswordModal(host) {\n      this.currentHost = host;\n      this.changePasswordModal.show = true;\n      this.changePasswordModal.generatedPassword = this.generatePassword();\n    },\n    openBatchUpdateModal() {\n      this.batchUpdateModal.show = true;\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected;\n      });\n\n      // 设置默认值\n      const today = new Date();\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0];\n      this.batchUpdateModal.scheduledTime = '03:00';\n    },\n    openBatchApplyModal() {\n      this.batchApplyModal.show = true;\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.batchApplyModal.selectedHosts[host.id] = host.selected;\n      });\n    },\n    showEmergencyReset() {\n      this.emergencyResetModal.show = true;\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected;\n      });\n    },\n    generatePassword(policy) {\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';\n      let password = '';\n\n      // 获取所选策略的最小长度\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId);\n      const minLength = policyObj ? policyObj.minLength : 12;\n\n      // 生成随机密码\n      for (let i = 0; i < minLength; i++) {\n        password += chars.charAt(Math.floor(Math.random() * chars.length));\n      }\n      if (this.changePasswordModal && !policy) {\n        this.changePasswordModal.generatedPassword = password;\n      }\n      return password;\n    },\n    async updatePassword() {\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\n        return;\n      }\n      this.processing = true;\n      try {\n        const password = this.changePasswordModal.method === 'auto' ? this.changePasswordModal.generatedPassword : this.changePasswordModal.newPassword;\n        await this.$store.dispatch('updateHostPassword', {\n          hostId: this.currentHost.id,\n          password: password,\n          policyId: this.changePasswordModal.policyId\n        });\n        this.changePasswordModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功更新主机 ${this.currentHost.name} 的密码！`);\n      } catch (error) {\n        console.error('更新密码失败', error);\n        alert('更新密码失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    async batchUpdatePasswords() {\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts).filter(([_, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        alert('请至少选择一台主机！');\n        return;\n      }\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\n        // 在实际应用中，这里会创建一个定时任务\n        alert('已创建定时密码更新任务！');\n        this.batchUpdateModal.show = false;\n        return;\n      }\n      this.processing = true;\n      try {\n        // 获取所选策略\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId);\n\n        // 为每台主机生成并更新密码\n        await Promise.all(selectedHostIds.map(async hostId => {\n          const newPassword = this.generatePassword(policy);\n          return this.$store.dispatch('updateHostPassword', {\n            hostId: hostId,\n            password: newPassword,\n            policyId: policy.id\n          });\n        }));\n        this.batchUpdateModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为 ${selectedHostIds.length} 台主机更新密码！`);\n      } catch (error) {\n        console.error('批量更新密码失败', error);\n        alert('批量更新密码失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    async batchApplyPolicy() {\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts).filter(([_, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        alert('请至少选择一台主机！');\n        return;\n      }\n      this.processing = true;\n      try {\n        await this.$store.dispatch('applyPolicyToHosts', {\n          policyId: this.batchApplyModal.policyId,\n          hostIds: selectedHostIds\n        });\n        this.batchApplyModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`);\n      } catch (error) {\n        console.error('应用策略失败', error);\n        alert('应用策略失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    async emergencyReset() {\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts).filter(([_, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        alert('请至少选择一台主机！');\n        return;\n      }\n      this.processing = true;\n      try {\n        // 获取紧急策略\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId);\n\n        // 为每台主机生成并更新密码\n        await Promise.all(selectedHostIds.map(async hostId => {\n          const newPassword = this.generatePassword(policy);\n          return this.$store.dispatch('updateHostPassword', {\n            hostId: hostId,\n            password: newPassword,\n            policyId: policy.id\n          });\n        }));\n        this.emergencyResetModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为 ${selectedHostIds.length} 台主机执行紧急密码重置！`);\n      } catch (error) {\n        console.error('紧急重置失败', error);\n        alert('紧急重置失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    togglePasswordVisibility(hostId) {\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId];\n    },\n    isPasswordExpired(host) {\n      if (!host.passwordExpiryDate) return {\n        status: 'normal',\n        days: null,\n        text: '-'\n      };\n\n      // 解析过期时间\n      const expiryDate = new Date(host.passwordExpiryDate);\n      const now = new Date();\n\n      // 如果已过期\n      if (expiryDate < now) {\n        return {\n          status: 'expired',\n          days: 0,\n          text: '已过期'\n        };\n      }\n\n      // 计算剩余天数和小时数\n      const diffTime = expiryDate - now;\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));\n      const diffHours = Math.floor(diffTime % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n\n      // 根据剩余时间确定状态\n      let status = 'normal';\n      if (diffDays < 7) {\n        status = 'danger'; // 少于7天\n      } else if (diffDays < 14) {\n        status = 'warning'; // 少于14天\n      }\n\n      // 格式化显示文本\n      let text = '';\n      if (diffDays > 0) {\n        text += `${diffDays}天`;\n      }\n      if (diffHours > 0 || diffDays === 0) {\n        text += `${diffHours}小时`;\n      }\n      return {\n        status,\n        days: diffDays,\n        text: `剩余${text}`\n      };\n    }\n  },\n  created() {\n    // 初始化日期和时间\n    const today = new Date();\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0];\n    this.batchUpdateModal.scheduledTime = '03:00';\n  }\n};", "map": {"version": 3, "names": ["mapState", "mapGetters", "BaseModal", "StatusBadge", "CustomCheckbox", "PasswordStrengthMeter", "name", "components", "data", "selectAll", "selectAllBatch", "processing", "currentHost", "passwordVisibility", "viewMode", "filterText", "statusFilter", "changePasswordModal", "show", "method", "policyId", "generatedPassword", "newPassword", "confirmPassword", "executeImmediately", "saveHistory", "logAudit", "batchUpdateModal", "selectedHosts", "executionTime", "scheduledDate", "scheduledTime", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "reason", "description", "computed", "hosts", "state", "policies", "passwordMismatch", "selectedHostsCount", "Object", "values", "filter", "Boolean", "length", "selectedHostsList", "host", "selected", "emergencyPolicies", "p", "id", "filteredHosts", "textMatch", "toLowerCase", "includes", "ip", "statusMatch", "status", "methods", "toggleSelectAll", "value", "$store", "commit", "toggleSelectAllBatch", "for<PERSON>ach", "openChangePasswordModal", "generatePassword", "openBatchUpdateModal", "today", "Date", "toISOString", "split", "openBatchApplyModal", "showEmergencyReset", "policy", "chars", "password", "policyObj", "find", "<PERSON><PERSON><PERSON><PERSON>", "i", "char<PERSON>t", "Math", "floor", "random", "updatePassword", "dispatch", "hostId", "alert", "error", "console", "batchUpdatePasswords", "selectedHostIds", "entries", "_", "map", "parseInt", "Promise", "all", "batchApplyPolicy", "hostIds", "emergencyReset", "togglePasswordVisibility", "isPasswordExpired", "passwordExpiryDate", "days", "text", "expiryDate", "now", "diffTime", "diffDays", "diffHours", "created"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"bg-white shadow rounded-lg p-4 mb-6\">\r\n      <div class=\"flex flex-wrap items-center justify-between\">\r\n        <div class=\"flex space-x-3 mb-2 sm:mb-0\">\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\r\n            @click=\"showEmergencyReset\">\r\n            <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2\" />\r\n            <span>紧急重置</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            @click=\"openBatchUpdateModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n            <span>批量更新密码</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\r\n            @click=\"openBatchApplyModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n            <span>批量应用策略</span>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"flex items-center space-x-4\">\r\n          <!-- 视图切换 -->\r\n          <div class=\"flex items-center border rounded-md overflow-hidden\">\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'table', 'bg-gray-100 text-gray-600': viewMode !== 'table' }\"\r\n              @click=\"viewMode = 'table'\">\r\n              <font-awesome-icon :icon=\"['fas', 'table']\" class=\"mr-1\" />\r\n              表格\r\n            </button>\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'card', 'bg-gray-100 text-gray-600': viewMode !== 'card' }\"\r\n              @click=\"viewMode = 'card'\">\r\n              <font-awesome-icon :icon=\"['fas', 'th-large']\" class=\"mr-1\" />\r\n              卡片\r\n            </button>\r\n          </div>\r\n\r\n          <!-- 筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"filterText\" placeholder=\"筛选主机...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 状态筛选 -->\r\n          <select v-model=\"statusFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有状态</option>\r\n            <option value=\"normal\">正常</option>\r\n            <option value=\"warning\">警告</option>\r\n            <option value=\"error\">错误</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <div class=\"bg-white rounded-lg shadow overflow-hidden\">\r\n      <table class=\"min-w-full divide-y divide-gray-200\">\r\n        <thead class=\"bg-gray-50\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n                主机名\r\n              </CustomCheckbox>\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              IP地址\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              最后密码修改时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码过期时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              状态\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              操作\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"bg-white divide-y divide-gray-200\">\r\n          <tr v-for=\"(host, index) in filteredHosts\" :key=\"host.id\"\r\n            :class=\"{ 'bg-gray-50': index % 2 === 0, 'hover:bg-blue-50': true }\">\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"flex items-center\">\r\n                <CustomCheckbox v-model=\"host.selected\">\r\n                  <span class=\"ml-2 font-medium text-gray-900\">{{ host.name }}</span>\r\n                </CustomCheckbox>\r\n              </div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"text-sm text-gray-900\">{{ host.ip }}</div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"text-sm text-gray-500\">{{ host.lastPasswordChange || '-' }}</div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div :class=\"{\r\n                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\r\n                'bg-red-100 text-red-800': isPasswordExpired(host).status === 'danger' || isPasswordExpired(host).status === 'expired',\r\n                'bg-yellow-100 text-yellow-800': isPasswordExpired(host).status === 'warning',\r\n                'bg-gray-100 text-gray-800': isPasswordExpired(host).status === 'normal'\r\n              }\">\r\n                {{ isPasswordExpired(host).text }}\r\n                <span v-if=\"isPasswordExpired(host).status === 'expired' || isPasswordExpired(host).status === 'danger'\"\r\n                  class=\"ml-1\">\r\n                  <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                </span>\r\n                <span v-else-if=\"isPasswordExpired(host).status === 'warning'\" class=\"ml-1\">\r\n                  <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" />\r\n                </span>\r\n              </div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"flex items-center\">\r\n                <div class=\"flex-grow\">\r\n                  <input :type=\"passwordVisibility[host.id] ? 'text' : 'password'\" :value=\"host.password\" readonly\r\n                    class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                </div>\r\n                <button @click=\"togglePasswordVisibility(host.id)\"\r\n                  class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                  <font-awesome-icon :icon=\"['fas', passwordVisibility[host.id] ? 'eye-slash' : 'eye']\"\r\n                    class=\"text-lg\" />\r\n                </button>\r\n              </div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <StatusBadge :type=\"host.status\" />\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n              <button\r\n                class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                @click=\"openChangePasswordModal(host)\">\r\n                <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                修改密码\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" confirm-text=\"确认更新\" @confirm=\"updatePassword\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">服务器: {{ currentHost.name }}</label>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"changePasswordModal.method\" value=\"auto\" class=\"mr-2\">\r\n            <span>自动生成</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"changePasswordModal.method\" value=\"manual\" class=\"mr-2\">\r\n            <span>手动输入</span>\r\n          </label>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-if=\"changePasswordModal.method === 'auto'\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">选择密码策略</label>\r\n          <select v-model=\"changePasswordModal.policyId\" class=\"form-select\">\r\n            <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n              {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n            </option>\r\n          </select>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <div class=\"flex\">\r\n            <input type=\"text\" v-model=\"changePasswordModal.generatedPassword\" class=\"form-control rounded-r-none\"\r\n              readonly>\r\n            <button class=\"bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded-r-md\" @click=\"generatePassword\">\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" />\r\n            </button>\r\n          </div>\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.generatedPassword\" />\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else>\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <input type=\"password\" v-model=\"changePasswordModal.newPassword\" class=\"form-control\">\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <input type=\"password\" v-model=\"changePasswordModal.confirmPassword\" class=\"form-control\"\r\n            :class=\"{ 'border-red-500': passwordMismatch }\">\r\n          <div v-if=\"passwordMismatch\" class=\"text-red-500 text-xs mt-1\">\r\n            两次输入的密码不一致\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行选项</label>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          立即执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          保存密码历史记录\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          记录审计日志\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      passwordVisibility: {},\r\n      viewMode: 'table',\r\n      filterText: '',\r\n      statusFilter: 'all',\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    },\r\n\r\n    // 过滤后的主机列表\r\n    filteredHosts() {\r\n      return this.hosts.filter(host => {\r\n        // 文本过滤\r\n        const textMatch = this.filterText === '' || \r\n          host.name.toLowerCase().includes(this.filterText.toLowerCase()) ||\r\n          host.ip.includes(this.filterText);\r\n        \r\n        // 状态过滤\r\n        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;\r\n        \r\n        return textMatch && statusMatch;\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host) {\r\n      this.currentHost = host\r\n      this.changePasswordModal.show = true\r\n      this.changePasswordModal.generatedPassword = this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword(policy) {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policyObj ? policyObj.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      if (this.changePasswordModal && !policy) {\r\n        this.changePasswordModal.generatedPassword = password\r\n      }\r\n\r\n      return password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto'\r\n          ? this.changePasswordModal.generatedPassword\r\n          : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          password: password,\r\n          policyId: this.changePasswordModal.policyId\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取所选策略\r\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)\r\n\r\n        // 为每台主机生成并更新密码\r\n        await Promise.all(\r\n          selectedHostIds.map(async (hostId) => {\r\n            const newPassword = this.generatePassword(policy)\r\n            return this.$store.dispatch('updateHostPassword', {\r\n              hostId: hostId,\r\n              password: newPassword,\r\n              policyId: policy.id\r\n            })\r\n          })\r\n        )\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取紧急策略\r\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)\r\n\r\n        // 为每台主机生成并更新密码\r\n        await Promise.all(\r\n          selectedHostIds.map(async (hostId) => {\r\n            const newPassword = this.generatePassword(policy)\r\n            return this.$store.dispatch('updateHostPassword', {\r\n              hostId: hostId,\r\n              password: newPassword,\r\n              policyId: policy.id\r\n            })\r\n          })\r\n        )\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    togglePasswordVisibility(hostId) {\r\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]\r\n    },\r\n\r\n    isPasswordExpired(host) {\r\n      if (!host.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }\r\n\r\n      // 解析过期时间\r\n      const expiryDate = new Date(host.passwordExpiryDate)\r\n      const now = new Date()\r\n\r\n      // 如果已过期\r\n      if (expiryDate < now) {\r\n        return {\r\n          status: 'expired',\r\n          days: 0,\r\n          text: '已过期'\r\n        }\r\n      }\r\n\r\n      // 计算剩余天数和小时数\r\n      const diffTime = expiryDate - now\r\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))\r\n      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n\r\n      // 根据剩余时间确定状态\r\n      let status = 'normal'\r\n      if (diffDays < 7) {\r\n        status = 'danger'  // 少于7天\r\n      } else if (diffDays < 14) {\r\n        status = 'warning' // 少于14天\r\n      }\r\n\r\n      // 格式化显示文本\r\n      let text = ''\r\n      if (diffDays > 0) {\r\n        text += `${diffDays}天`\r\n      }\r\n      if (diffHours > 0 || diffDays === 0) {\r\n        text += `${diffHours}小时`\r\n      }\r\n\r\n      return { status, days: diffDays, text: `剩余${text}` }\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": "AA2XA,SAASA,QAAQ,EAAEC,UAAS,QAAS,MAAK;AAC1C,OAAOC,SAAQ,MAAO,4BAA2B;AACjD,OAAOC,WAAU,MAAO,8BAA6B;AACrD,OAAOC,cAAa,MAAO,iCAAgC;AAC3D,OAAOC,qBAAoB,MAAO,wCAAuC;AAEzE,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,UAAU,EAAE;IACVL,SAAS;IACTC,WAAW;IACXC,cAAc;IACdC;EACF,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,KAAK;MAChBC,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,CAAC,CAAC;MACfC,kBAAkB,EAAE,CAAC,CAAC;MACtBC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,KAAK;MAEnB;MACAC,mBAAmB,EAAE;QACnBC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE,CAAC;QACXC,iBAAiB,EAAE,cAAc;QACjCC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE,EAAE;QACnBC,kBAAkB,EAAE,IAAI;QACxBC,WAAW,EAAE,KAAK;QAClBC,QAAQ,EAAE;MACZ,CAAC;MAED;MACAC,gBAAgB,EAAE;QAChBT,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBR,QAAQ,EAAE,CAAC;QACXS,aAAa,EAAE,WAAW;QAC1BC,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,EAAE;QACjBC,YAAY,EAAE,IAAI;QAClBC,WAAW,EAAE,IAAI;QACjBC,gBAAgB,EAAE;MACpB,CAAC;MAED;MACAC,eAAe,EAAE;QACfjB,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBR,QAAQ,EAAE,CAAC;QACXgB,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;MAED;MACAC,mBAAmB,EAAE;QACnBpB,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBR,QAAQ,EAAE,CAAC;QAAE;QACbmB,MAAM,EAAE,mBAAmB;QAC3BC,WAAW,EAAE;MACf;IACF;EACF,CAAC;EACDC,QAAQ,EAAE;IACR,GAAGzC,QAAQ,CAAC;MACV0C,KAAK,EAAEC,KAAI,IAAKA,KAAK,CAACD,KAAK;MAC3BE,QAAQ,EAAED,KAAI,IAAKA,KAAK,CAACC;IAC3B,CAAC,CAAC;IACF,GAAG3C,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC;IAEhC4C,gBAAgBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAAC5B,mBAAmB,CAACK,WAAU,IACxC,IAAI,CAACL,mBAAmB,CAACM,eAAc,IACvC,IAAI,CAACN,mBAAmB,CAACK,WAAU,KAAM,IAAI,CAACL,mBAAmB,CAACM,eAAc;IACpF,CAAC;IAEDuB,kBAAkBA,CAAA,EAAG;MACnB,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACrB,gBAAgB,CAACC,aAAa,CAAC,CAACqB,MAAM,CAACC,OAAO,CAAC,CAACC,MAAK;IACjF,CAAC;IAEDC,iBAAiBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACV,KAAK,CAACO,MAAM,CAACI,IAAG,IAAKA,IAAI,CAACC,QAAQ;IAChD,CAAC;IAEDC,iBAAiBA,CAAA,EAAG;MAClB;MACA,OAAO,IAAI,CAACX,QAAQ,CAACK,MAAM,CAACO,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,KAAKD,CAAC,CAACC,EAAC,KAAM,CAAC;IAC3D,CAAC;IAED;IACAC,aAAaA,CAAA,EAAG;MACd,OAAO,IAAI,CAAChB,KAAK,CAACO,MAAM,CAACI,IAAG,IAAK;QAC/B;QACA,MAAMM,SAAQ,GAAI,IAAI,CAAC5C,UAAS,KAAM,EAAC,IACrCsC,IAAI,CAAC/C,IAAI,CAACsD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC9C,UAAU,CAAC6C,WAAW,CAAC,CAAC,KAC9DP,IAAI,CAACS,EAAE,CAACD,QAAQ,CAAC,IAAI,CAAC9C,UAAU,CAAC;;QAEnC;QACA,MAAMgD,WAAU,GAAI,IAAI,CAAC/C,YAAW,KAAM,KAAI,IAAKqC,IAAI,CAACW,MAAK,KAAM,IAAI,CAAChD,YAAY;QAEpF,OAAO2C,SAAQ,IAAKI,WAAW;MACjC,CAAC,CAAC;IACJ;EACF,CAAC;EACDE,OAAO,EAAE;IACPC,eAAeA,CAACC,KAAK,EAAE;MACrB,IAAI,CAACC,MAAM,CAACC,MAAM,CAAC,gBAAgB,EAAEF,KAAK;IAC5C,CAAC;IAEDG,oBAAoBA,CAACH,KAAK,EAAE;MAC1B,IAAI,CAACzB,KAAK,CAAC6B,OAAO,CAAClB,IAAG,IAAK;QACzB,IAAI,CAAC1B,gBAAgB,CAACC,aAAa,CAACyB,IAAI,CAACI,EAAE,IAAIU,KAAI;MACrD,CAAC;IACH,CAAC;IAEDK,uBAAuBA,CAACnB,IAAI,EAAE;MAC5B,IAAI,CAACzC,WAAU,GAAIyC,IAAG;MACtB,IAAI,CAACpC,mBAAmB,CAACC,IAAG,GAAI,IAAG;MACnC,IAAI,CAACD,mBAAmB,CAACI,iBAAgB,GAAI,IAAI,CAACoD,gBAAgB,CAAC;IACrE,CAAC;IAEDC,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAAC/C,gBAAgB,CAACT,IAAG,GAAI,IAAG;;MAEhC;MACA,IAAI,CAACwB,KAAK,CAAC6B,OAAO,CAAClB,IAAG,IAAK;QACzB,IAAI,CAAC1B,gBAAgB,CAACC,aAAa,CAACyB,IAAI,CAACI,EAAE,IAAIJ,IAAI,CAACC,QAAO;MAC7D,CAAC;;MAED;MACA,MAAMqB,KAAI,GAAI,IAAIC,IAAI,CAAC;MACvB,IAAI,CAACjD,gBAAgB,CAACG,aAAY,GAAI6C,KAAK,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MACtE,IAAI,CAACnD,gBAAgB,CAACI,aAAY,GAAI,OAAM;IAC9C,CAAC;IAEDgD,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAAC5C,eAAe,CAACjB,IAAG,GAAI,IAAG;;MAE/B;MACA,IAAI,CAACwB,KAAK,CAAC6B,OAAO,CAAClB,IAAG,IAAK;QACzB,IAAI,CAAClB,eAAe,CAACP,aAAa,CAACyB,IAAI,CAACI,EAAE,IAAIJ,IAAI,CAACC,QAAO;MAC5D,CAAC;IACH,CAAC;IAED0B,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAAC1C,mBAAmB,CAACpB,IAAG,GAAI,IAAG;;MAEnC;MACA,IAAI,CAACwB,KAAK,CAAC6B,OAAO,CAAClB,IAAG,IAAK;QACzB,IAAI,CAACf,mBAAmB,CAACV,aAAa,CAACyB,IAAI,CAACI,EAAE,IAAIJ,IAAI,CAACC,QAAO;MAChE,CAAC;IACH,CAAC;IAEDmB,gBAAgBA,CAACQ,MAAM,EAAE;MACvB,MAAMC,KAAI,GAAI,0EAAyE;MACvF,IAAIC,QAAO,GAAI,EAAC;;MAEhB;MACA,MAAMC,SAAQ,GAAIH,MAAK,IAAK,IAAI,CAACrC,QAAQ,CAACyC,IAAI,CAAC7B,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,IAAI,CAACxC,mBAAmB,CAACG,QAAQ;MAC9F,MAAMkE,SAAQ,GAAIF,SAAQ,GAAIA,SAAS,CAACE,SAAQ,GAAI,EAAC;;MAErD;MACA,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAID,SAAS,EAAEC,CAAC,EAAE,EAAE;QAClCJ,QAAO,IAAKD,KAAK,CAACM,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIT,KAAK,CAAC/B,MAAM,CAAC;MACnE;MAEA,IAAI,IAAI,CAAClC,mBAAkB,IAAK,CAACgE,MAAM,EAAE;QACvC,IAAI,CAAChE,mBAAmB,CAACI,iBAAgB,GAAI8D,QAAO;MACtD;MAEA,OAAOA,QAAO;IAChB,CAAC;IAED,MAAMS,cAAcA,CAAA,EAAG;MACrB,IAAI,IAAI,CAAC3E,mBAAmB,CAACE,MAAK,KAAM,QAAO,IAAK,IAAI,CAAC0B,gBAAgB,EAAE;QACzE;MACF;MAEA,IAAI,CAAClC,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAMwE,QAAO,GAAI,IAAI,CAAClE,mBAAmB,CAACE,MAAK,KAAM,MAAK,GACtD,IAAI,CAACF,mBAAmB,CAACI,iBAAgB,GACzC,IAAI,CAACJ,mBAAmB,CAACK,WAAU;QAEvC,MAAM,IAAI,CAAC8C,MAAM,CAACyB,QAAQ,CAAC,oBAAoB,EAAE;UAC/CC,MAAM,EAAE,IAAI,CAAClF,WAAW,CAAC6C,EAAE;UAC3B0B,QAAQ,EAAEA,QAAQ;UAClB/D,QAAQ,EAAE,IAAI,CAACH,mBAAmB,CAACG;QACrC,CAAC;QAED,IAAI,CAACH,mBAAmB,CAACC,IAAG,GAAI,KAAI;;QAEpC;QACA6E,KAAK,CAAC,WAAW,IAAI,CAACnF,WAAW,CAACN,IAAI,OAAO;MAC/C,EAAE,OAAO0F,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAACpF,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED,MAAMuF,oBAAoBA,CAAA,EAAG;MAC3B,MAAMC,eAAc,GAAIpD,MAAM,CAACqD,OAAO,CAAC,IAAI,CAACzE,gBAAgB,CAACC,aAAa,EACvEqB,MAAM,CAAC,CAAC,CAACoD,CAAC,EAAE/C,QAAQ,CAAC,KAAKA,QAAQ,EAClCgD,GAAG,CAAC,CAAC,CAAC7C,EAAE,CAAC,KAAK8C,QAAQ,CAAC9C,EAAE,CAAC;MAE7B,IAAI0C,eAAe,CAAChD,MAAK,KAAM,CAAC,EAAE;QAChC4C,KAAK,CAAC,YAAY;QAClB;MACF;MAEA,IAAI,IAAI,CAACpE,gBAAgB,CAACE,aAAY,KAAM,WAAW,EAAE;QACvD;QACAkE,KAAK,CAAC,cAAc;QACpB,IAAI,CAACpE,gBAAgB,CAACT,IAAG,GAAI,KAAI;QACjC;MACF;MAEA,IAAI,CAACP,UAAS,GAAI,IAAG;MAErB,IAAI;QACF;QACA,MAAMsE,MAAK,GAAI,IAAI,CAACrC,QAAQ,CAACyC,IAAI,CAAC7B,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,IAAI,CAAC9B,gBAAgB,CAACP,QAAQ;;QAE9E;QACA,MAAMoF,OAAO,CAACC,GAAG,CACfN,eAAe,CAACG,GAAG,CAAC,MAAOR,MAAM,IAAK;UACpC,MAAMxE,WAAU,GAAI,IAAI,CAACmD,gBAAgB,CAACQ,MAAM;UAChD,OAAO,IAAI,CAACb,MAAM,CAACyB,QAAQ,CAAC,oBAAoB,EAAE;YAChDC,MAAM,EAAEA,MAAM;YACdX,QAAQ,EAAE7D,WAAW;YACrBF,QAAQ,EAAE6D,MAAM,CAACxB;UACnB,CAAC;QACH,CAAC,CACH;QAEA,IAAI,CAAC9B,gBAAgB,CAACT,IAAG,GAAI,KAAI;;QAEjC;QACA6E,KAAK,CAAC,QAAQI,eAAe,CAAChD,MAAM,WAAW;MACjD,EAAE,OAAO6C,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK;QAC/BD,KAAK,CAAC,eAAe;MACvB,UAAU;QACR,IAAI,CAACpF,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED,MAAM+F,gBAAgBA,CAAA,EAAG;MACvB,MAAMP,eAAc,GAAIpD,MAAM,CAACqD,OAAO,CAAC,IAAI,CAACjE,eAAe,CAACP,aAAa,EACtEqB,MAAM,CAAC,CAAC,CAACoD,CAAC,EAAE/C,QAAQ,CAAC,KAAKA,QAAQ,EAClCgD,GAAG,CAAC,CAAC,CAAC7C,EAAE,CAAC,KAAK8C,QAAQ,CAAC9C,EAAE,CAAC;MAE7B,IAAI0C,eAAe,CAAChD,MAAK,KAAM,CAAC,EAAE;QAChC4C,KAAK,CAAC,YAAY;QAClB;MACF;MAEA,IAAI,CAACpF,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAM,IAAI,CAACyD,MAAM,CAACyB,QAAQ,CAAC,oBAAoB,EAAE;UAC/CzE,QAAQ,EAAE,IAAI,CAACe,eAAe,CAACf,QAAQ;UACvCuF,OAAO,EAAER;QACX,CAAC;QAED,IAAI,CAAChE,eAAe,CAACjB,IAAG,GAAI,KAAI;;QAEhC;QACA6E,KAAK,CAAC,QAAQI,eAAe,CAAChD,MAAM,aAAa;MACnD,EAAE,OAAO6C,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAACpF,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED,MAAMiG,cAAcA,CAAA,EAAG;MACrB,MAAMT,eAAc,GAAIpD,MAAM,CAACqD,OAAO,CAAC,IAAI,CAAC9D,mBAAmB,CAACV,aAAa,EAC1EqB,MAAM,CAAC,CAAC,CAACoD,CAAC,EAAE/C,QAAQ,CAAC,KAAKA,QAAQ,EAClCgD,GAAG,CAAC,CAAC,CAAC7C,EAAE,CAAC,KAAK8C,QAAQ,CAAC9C,EAAE,CAAC;MAE7B,IAAI0C,eAAe,CAAChD,MAAK,KAAM,CAAC,EAAE;QAChC4C,KAAK,CAAC,YAAY;QAClB;MACF;MAEA,IAAI,CAACpF,UAAS,GAAI,IAAG;MAErB,IAAI;QACF;QACA,MAAMsE,MAAK,GAAI,IAAI,CAACrC,QAAQ,CAACyC,IAAI,CAAC7B,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,IAAI,CAACnB,mBAAmB,CAAClB,QAAQ;;QAEjF;QACA,MAAMoF,OAAO,CAACC,GAAG,CACfN,eAAe,CAACG,GAAG,CAAC,MAAOR,MAAM,IAAK;UACpC,MAAMxE,WAAU,GAAI,IAAI,CAACmD,gBAAgB,CAACQ,MAAM;UAChD,OAAO,IAAI,CAACb,MAAM,CAACyB,QAAQ,CAAC,oBAAoB,EAAE;YAChDC,MAAM,EAAEA,MAAM;YACdX,QAAQ,EAAE7D,WAAW;YACrBF,QAAQ,EAAE6D,MAAM,CAACxB;UACnB,CAAC;QACH,CAAC,CACH;QAEA,IAAI,CAACnB,mBAAmB,CAACpB,IAAG,GAAI,KAAI;;QAEpC;QACA6E,KAAK,CAAC,QAAQI,eAAe,CAAChD,MAAM,eAAe;MACrD,EAAE,OAAO6C,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAACpF,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAEDkG,wBAAwBA,CAACf,MAAM,EAAE;MAC/B,IAAI,CAACjF,kBAAkB,CAACiF,MAAM,IAAI,CAAC,IAAI,CAACjF,kBAAkB,CAACiF,MAAM;IACnE,CAAC;IAEDgB,iBAAiBA,CAACzD,IAAI,EAAE;MACtB,IAAI,CAACA,IAAI,CAAC0D,kBAAkB,EAAE,OAAO;QAAE/C,MAAM,EAAE,QAAQ;QAAEgD,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAI;;MAE/E;MACA,MAAMC,UAAS,GAAI,IAAItC,IAAI,CAACvB,IAAI,CAAC0D,kBAAkB;MACnD,MAAMI,GAAE,GAAI,IAAIvC,IAAI,CAAC;;MAErB;MACA,IAAIsC,UAAS,GAAIC,GAAG,EAAE;QACpB,OAAO;UACLnD,MAAM,EAAE,SAAS;UACjBgD,IAAI,EAAE,CAAC;UACPC,IAAI,EAAE;QACR;MACF;;MAEA;MACA,MAAMG,QAAO,GAAIF,UAAS,GAAIC,GAAE;MAChC,MAAME,QAAO,GAAI5B,IAAI,CAACC,KAAK,CAAC0B,QAAO,IAAK,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,EAAE,CAAC;MAC5D,MAAME,SAAQ,GAAI7B,IAAI,CAACC,KAAK,CAAE0B,QAAO,IAAK,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,EAAE,CAAC,IAAK,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC;;MAElF;MACA,IAAIpD,MAAK,GAAI,QAAO;MACpB,IAAIqD,QAAO,GAAI,CAAC,EAAE;QAChBrD,MAAK,GAAI,QAAO,EAAG;MACrB,OAAO,IAAIqD,QAAO,GAAI,EAAE,EAAE;QACxBrD,MAAK,GAAI,SAAQ,EAAE;MACrB;;MAEA;MACA,IAAIiD,IAAG,GAAI,EAAC;MACZ,IAAII,QAAO,GAAI,CAAC,EAAE;QAChBJ,IAAG,IAAK,GAAGI,QAAQ,GAAE;MACvB;MACA,IAAIC,SAAQ,GAAI,KAAKD,QAAO,KAAM,CAAC,EAAE;QACnCJ,IAAG,IAAK,GAAGK,SAAS,IAAG;MACzB;MAEA,OAAO;QAAEtD,MAAM;QAAEgD,IAAI,EAAEK,QAAQ;QAAEJ,IAAI,EAAE,KAAKA,IAAI;MAAG;IACrD;EACF,CAAC;EACDM,OAAOA,CAAA,EAAG;IACR;IACA,MAAM5C,KAAI,GAAI,IAAIC,IAAI,CAAC;IACvB,IAAI,CAACjD,gBAAgB,CAACG,aAAY,GAAI6C,KAAK,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IACtE,IAAI,CAACnD,gBAAgB,CAACI,aAAY,GAAI,OAAM;EAC9C;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}