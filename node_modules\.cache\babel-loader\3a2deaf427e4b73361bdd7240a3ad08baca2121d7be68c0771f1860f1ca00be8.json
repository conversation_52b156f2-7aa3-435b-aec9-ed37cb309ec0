{"ast": null, "code": "import { mapState, mapGetters } from 'vuex';\nimport BaseModal from '@/components/BaseModal.vue';\nimport StatusBadge from '@/components/StatusBadge.vue';\nimport CustomCheckbox from '@/components/CustomCheckbox.vue';\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue';\nexport default {\n  name: 'HostManagement',\n  components: {\n    BaseModal,\n    StatusBadge,\n    CustomCheckbox,\n    PasswordStrengthMeter\n  },\n  data() {\n    return {\n      selectAll: false,\n      selectAllBatch: false,\n      processing: false,\n      currentHost: {},\n      currentAccount: {},\n      passwordVisibility: {\n        generated: false,\n        new: false,\n        confirm: false,\n        newAccount: false\n      },\n      viewMode: 'table',\n      filterText: '',\n      accountFilterText: '',\n      statusFilter: 'all',\n      expiryFilter: 'all',\n      // 修改密码弹窗\n      changePasswordModal: {\n        show: false,\n        method: 'auto',\n        policyId: 1,\n        generatedPassword: 'aX7#9pQr$2Lm',\n        newPassword: '',\n        confirmPassword: '',\n        executeImmediately: true,\n        saveHistory: false,\n        logAudit: true\n      },\n      // 批量更新密码弹窗\n      batchUpdateModal: {\n        show: false,\n        selectedHosts: {},\n        policyId: 1,\n        executionTime: 'immediate',\n        scheduledDate: '',\n        scheduledTime: '',\n        ignoreErrors: true,\n        detailedLog: true,\n        sendNotification: false\n      },\n      // 批量应用策略弹窗\n      batchApplyModal: {\n        show: false,\n        selectedHosts: {},\n        policyId: 1,\n        updateImmediately: false,\n        applyOnNextUpdate: true\n      },\n      // 紧急重置密码弹窗\n      emergencyResetModal: {\n        show: false,\n        selectedHosts: {},\n        policyId: 3,\n        // 默认使用紧急策略\n        reason: 'security_incident',\n        description: ''\n      },\n      // 添加账号弹窗\n      addAccountModal: {\n        show: false,\n        username: '',\n        password: '',\n        isDefault: false,\n        policyId: 1\n      }\n    };\n  },\n  computed: {\n    ...mapState({\n      hosts: state => state.hosts,\n      policies: state => state.policies\n    }),\n    ...mapGetters(['selectedHosts']),\n    passwordMismatch() {\n      return this.changePasswordModal.newPassword && this.changePasswordModal.confirmPassword && this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword;\n    },\n    selectedHostsCount() {\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length;\n    },\n    selectedHostsList() {\n      return this.hosts.filter(host => host.selected);\n    },\n    emergencyPolicies() {\n      // 返回紧急策略和高强度策略\n      return this.policies.filter(p => p.id === 3 || p.id === 1);\n    },\n    // 过滤后的主机列表\n    filteredHosts() {\n      return this.hosts.filter(host => {\n        // 文本过滤\n        const textMatch = this.filterText === '' || host.name.toLowerCase().includes(this.filterText.toLowerCase()) || host.ip.includes(this.filterText);\n\n        // 状态过滤\n        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;\n        return textMatch && statusMatch;\n      });\n    },\n    // 获取所有账号（扁平化处理）\n    getAllAccounts() {\n      // 为每个账号添加主机引用\n      const accounts = [];\n      this.filteredHosts.forEach(host => {\n        host.accounts.forEach(account => {\n          accounts.push({\n            ...account,\n            host: host\n          });\n        });\n      });\n      return accounts;\n    },\n    // 筛选后的账号\n    filteredAccounts() {\n      return this.getAllAccounts.filter(account => {\n        // 账号名称筛选\n        const accountMatch = this.accountFilterText === '' || account.username.toLowerCase().includes(this.accountFilterText.toLowerCase());\n\n        // 密码过期筛选\n        let expiryMatch = true;\n        if (this.expiryFilter !== 'all') {\n          const expiryStatus = this.isPasswordExpired(account).status;\n          if (this.expiryFilter === 'expired') {\n            expiryMatch = expiryStatus === 'expired';\n          } else if (this.expiryFilter === 'expiring-soon') {\n            expiryMatch = expiryStatus === 'danger' || expiryStatus === 'warning';\n          } else if (this.expiryFilter === 'valid') {\n            expiryMatch = expiryStatus === 'normal';\n          }\n        }\n        return accountMatch && expiryMatch;\n      });\n    },\n    // 分组后的账号列表\n    groupedAccounts() {\n      // 按主机ID分组\n      const groups = {};\n      this.filteredAccounts.forEach(account => {\n        const hostId = account.host.id;\n        if (!groups[hostId]) {\n          groups[hostId] = {\n            hostId: hostId,\n            hostName: account.host.name,\n            hostIp: account.host.ip,\n            host: account.host,\n            accounts: []\n          };\n        }\n        groups[hostId].accounts.push(account);\n      });\n\n      // 转换为数组\n      return Object.values(groups);\n    }\n  },\n  methods: {\n    toggleSelectAll(value) {\n      this.$store.commit('selectAllHosts', value);\n    },\n    toggleSelectAllBatch(value) {\n      this.hosts.forEach(host => {\n        this.batchUpdateModal.selectedHosts[host.id] = value;\n      });\n    },\n    openChangePasswordModal(host, account) {\n      this.currentHost = host;\n      this.currentAccount = account;\n      this.changePasswordModal.show = true;\n      this.changePasswordModal.generatedPassword = this.generatePassword();\n    },\n    openBatchUpdateModal() {\n      this.batchUpdateModal.show = true;\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected;\n      });\n\n      // 设置默认值\n      const today = new Date();\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0];\n      this.batchUpdateModal.scheduledTime = '03:00';\n    },\n    openBatchApplyModal() {\n      this.batchApplyModal.show = true;\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.batchApplyModal.selectedHosts[host.id] = host.selected;\n      });\n    },\n    showEmergencyReset() {\n      this.emergencyResetModal.show = true;\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected;\n      });\n    },\n    generatePassword(policy) {\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';\n      let password = '';\n\n      // 获取所选策略的最小长度\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId);\n      const minLength = policyObj ? policyObj.minLength : 12;\n\n      // 生成随机密码\n      for (let i = 0; i < minLength; i++) {\n        password += chars.charAt(Math.floor(Math.random() * chars.length));\n      }\n      if (this.changePasswordModal && !policy) {\n        this.changePasswordModal.generatedPassword = password;\n      }\n      return password;\n    },\n    async updatePassword() {\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\n        return;\n      }\n      this.processing = true;\n      try {\n        const password = this.changePasswordModal.method === 'auto' ? this.changePasswordModal.generatedPassword : this.changePasswordModal.newPassword;\n        await this.$store.dispatch('updateHostPassword', {\n          hostId: this.currentHost.id,\n          accountId: this.currentAccount.id,\n          password: password,\n          policyId: this.changePasswordModal.policyId\n        });\n        this.changePasswordModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功更新主机 ${this.currentHost.name} 的 ${this.currentAccount.username} 账号密码！`);\n      } catch (error) {\n        console.error('更新密码失败', error);\n        alert('更新密码失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    async batchUpdatePasswords() {\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts).filter(([_, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        alert('请至少选择一台主机！');\n        return;\n      }\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\n        // 在实际应用中，这里会创建一个定时任务\n        alert('已创建定时密码更新任务！');\n        this.batchUpdateModal.show = false;\n        return;\n      }\n      this.processing = true;\n      try {\n        // 获取所选策略\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId);\n\n        // 为每台主机的每个账号更新密码\n        for (const hostId of selectedHostIds) {\n          const host = this.hosts.find(h => h.id === hostId);\n          if (host) {\n            for (const account of host.accounts) {\n              const newPassword = this.generatePassword(policy);\n              await this.$store.dispatch('updateHostPassword', {\n                hostId: hostId,\n                accountId: account.id,\n                password: newPassword,\n                policyId: policy.id\n              });\n            }\n          }\n        }\n        this.batchUpdateModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号更新密码！`);\n      } catch (error) {\n        console.error('批量更新密码失败', error);\n        alert('批量更新密码失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    async batchApplyPolicy() {\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts).filter(([_, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        alert('请至少选择一台主机！');\n        return;\n      }\n      this.processing = true;\n      try {\n        await this.$store.dispatch('applyPolicyToHosts', {\n          policyId: this.batchApplyModal.policyId,\n          hostIds: selectedHostIds\n        });\n        this.batchApplyModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`);\n      } catch (error) {\n        console.error('应用策略失败', error);\n        alert('应用策略失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    async emergencyReset() {\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts).filter(([_, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        alert('请至少选择一台主机！');\n        return;\n      }\n      this.processing = true;\n      try {\n        // 获取紧急策略\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId);\n\n        // 为每台主机的每个账号更新密码\n        for (const hostId of selectedHostIds) {\n          const host = this.hosts.find(h => h.id === hostId);\n          if (host) {\n            for (const account of host.accounts) {\n              const newPassword = this.generatePassword(policy);\n              await this.$store.dispatch('updateHostPassword', {\n                hostId: hostId,\n                accountId: account.id,\n                password: newPassword,\n                policyId: policy.id\n              });\n            }\n          }\n        }\n        this.emergencyResetModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号执行紧急密码重置！`);\n      } catch (error) {\n        console.error('紧急重置失败', error);\n        alert('紧急重置失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    togglePasswordVisibility(hostId) {\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId];\n    },\n    isPasswordExpired(account) {\n      if (!account.passwordExpiryDate) return {\n        status: 'normal',\n        days: null,\n        text: '-'\n      };\n\n      // 解析过期时间\n      const expiryDate = new Date(account.passwordExpiryDate);\n      const now = new Date();\n\n      // 如果已过期\n      if (expiryDate < now) {\n        return {\n          status: 'expired',\n          days: 0,\n          text: '已过期'\n        };\n      }\n\n      // 计算剩余天数和小时数\n      const diffTime = expiryDate - now;\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));\n      const diffHours = Math.floor(diffTime % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n\n      // 根据剩余时间确定状态\n      let status = 'normal';\n      if (diffDays < 7) {\n        status = 'danger'; // 少于7天\n      } else if (diffDays < 14) {\n        status = 'warning'; // 少于14天\n      }\n\n      // 格式化显示文本\n      let text = '';\n      if (diffDays > 0) {\n        text += `${diffDays}天`;\n      }\n      if (diffHours > 0 || diffDays === 0) {\n        text += `${diffHours}小时`;\n      }\n      return {\n        status,\n        days: diffDays,\n        text: `剩余${text}`\n      };\n    },\n    openAddAccountModal(host) {\n      this.currentHost = host;\n      this.addAccountModal.show = true;\n    },\n    async addAccount() {\n      if (!this.addAccountModal.username || !this.addAccountModal.password) {\n        alert('请填写完整的账号信息！');\n        return;\n      }\n      this.processing = true;\n      try {\n        await this.$store.dispatch('addHostAccount', {\n          hostId: this.currentHost.id,\n          username: this.addAccountModal.username,\n          password: this.addAccountModal.password,\n          policyId: this.addAccountModal.policyId,\n          isDefault: this.addAccountModal.isDefault\n        });\n        this.addAccountModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为主机 ${this.currentHost.name} 添加账号！`);\n      } catch (error) {\n        console.error('添加账号失败', error);\n        alert('添加账号失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    generatePasswordForNewAccount() {\n      this.addAccountModal.password = this.generatePassword();\n    },\n    // 复制密码到剪贴板\n    copyPassword(account) {\n      // 创建一个临时输入框\n      const tempInput = document.createElement('input');\n      tempInput.value = account.password;\n      document.body.appendChild(tempInput);\n      tempInput.select();\n      document.execCommand('copy');\n      document.body.removeChild(tempInput);\n\n      // 显示提示\n      alert(`已复制 ${account.host.name} 的 ${account.username} 账号密码到剪贴板！`);\n    }\n  },\n  created() {\n    // 初始化日期和时间\n    const today = new Date();\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0];\n    this.batchUpdateModal.scheduledTime = '03:00';\n  }\n};", "map": {"version": 3, "names": ["mapState", "mapGetters", "BaseModal", "StatusBadge", "CustomCheckbox", "PasswordStrengthMeter", "name", "components", "data", "selectAll", "selectAllBatch", "processing", "currentHost", "currentAccount", "passwordVisibility", "generated", "new", "confirm", "newAccount", "viewMode", "filterText", "accountFilterText", "statusFilter", "expiryFilter", "changePasswordModal", "show", "method", "policyId", "generatedPassword", "newPassword", "confirmPassword", "executeImmediately", "saveHistory", "logAudit", "batchUpdateModal", "selectedHosts", "executionTime", "scheduledDate", "scheduledTime", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "reason", "description", "addAccountModal", "username", "password", "isDefault", "computed", "hosts", "state", "policies", "passwordMismatch", "selectedHostsCount", "Object", "values", "filter", "Boolean", "length", "selectedHostsList", "host", "selected", "emergencyPolicies", "p", "id", "filteredHosts", "textMatch", "toLowerCase", "includes", "ip", "statusMatch", "status", "getAllAccounts", "accounts", "for<PERSON>ach", "account", "push", "filteredAccounts", "accountMatch", "expiryMatch", "expiry<PERSON>tatus", "isPasswordExpired", "groupedAccounts", "groups", "hostId", "hostName", "hostIp", "methods", "toggleSelectAll", "value", "$store", "commit", "toggleSelectAllBatch", "openChangePasswordModal", "generatePassword", "openBatchUpdateModal", "today", "Date", "toISOString", "split", "openBatchApplyModal", "showEmergencyReset", "policy", "chars", "policyObj", "find", "<PERSON><PERSON><PERSON><PERSON>", "i", "char<PERSON>t", "Math", "floor", "random", "updatePassword", "dispatch", "accountId", "alert", "error", "console", "batchUpdatePasswords", "selectedHostIds", "entries", "_", "map", "parseInt", "h", "batchApplyPolicy", "hostIds", "emergencyReset", "togglePasswordVisibility", "passwordExpiryDate", "days", "text", "expiryDate", "now", "diffTime", "diffDays", "diffHours", "openAddAccountModal", "addAccount", "generatePasswordForNewAccount", "copyPassword", "tempInput", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "created"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"bg-white shadow rounded-lg p-4 mb-6\">\r\n      <div class=\"flex flex-wrap items-center justify-between\">\r\n        <div class=\"flex space-x-3 mb-2 sm:mb-0\">\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\r\n            @click=\"showEmergencyReset\">\r\n            <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2\" />\r\n            <span>紧急重置</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            @click=\"openBatchUpdateModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n            <span>批量更新密码</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\r\n            @click=\"openBatchApplyModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n            <span>批量应用策略</span>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"flex items-center space-x-4\">\r\n          <!-- 视图切换 -->\r\n          <div class=\"flex items-center border rounded-md overflow-hidden\">\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'table', 'bg-gray-100 text-gray-600': viewMode !== 'table' }\"\r\n              @click=\"viewMode = 'table'\">\r\n              <font-awesome-icon :icon=\"['fas', 'table']\" class=\"mr-1\" />\r\n              表格\r\n            </button>\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'card', 'bg-gray-100 text-gray-600': viewMode !== 'card' }\"\r\n              @click=\"viewMode = 'card'\">\r\n              <font-awesome-icon :icon=\"['fas', 'th-large']\" class=\"mr-1\" />\r\n              卡片\r\n            </button>\r\n          </div>\r\n\r\n          <!-- 筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"filterText\" placeholder=\"筛选主机...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 账号筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"accountFilterText\" placeholder=\"筛选账号...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'user']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 状态筛选 -->\r\n          <select v-model=\"statusFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有状态</option>\r\n            <option value=\"normal\">正常</option>\r\n            <option value=\"warning\">警告</option>\r\n            <option value=\"error\">错误</option>\r\n          </select>\r\n\r\n          <!-- 显示密码过期选项 -->\r\n          <select v-model=\"expiryFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有密码</option>\r\n            <option value=\"expired\">已过期</option>\r\n            <option value=\"expiring-soon\">即将过期</option>\r\n            <option value=\"valid\">有效期内</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <!-- 表格视图 -->\r\n    <div v-if=\"viewMode === 'table'\" class=\"bg-white rounded-lg shadow overflow-hidden\">\r\n      <!-- 账号计数和导出按钮 -->\r\n      <div class=\"px-4 py-3 bg-gray-50 border-b flex justify-between items-center\">\r\n        <div class=\"text-sm text-gray-700\">\r\n          显示 <span class=\"font-medium\">{{ filteredAccounts.length }}</span> 个账号\r\n          (共 <span class=\"font-medium\">{{ getAllAccounts.length }}</span> 个)\r\n        </div>\r\n        <div class=\"flex space-x-2\">\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\r\n            <font-awesome-icon :icon=\"['fas', 'file-export']\" class=\"mr-1\" />\r\n            导出\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\r\n            <font-awesome-icon :icon=\"['fas', 'print']\" class=\"mr-1\" />\r\n            打印\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <table class=\"min-w-full divide-y divide-gray-200\">\r\n        <thead class=\"bg-gray-50\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n                主机名\r\n              </CustomCheckbox>\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              IP地址\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              账号\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              最后密码修改时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码过期时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              状态\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              操作\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"bg-white divide-y divide-gray-200\">\r\n          <!-- 按主机分组显示 -->\r\n          <template v-for=\"(hostGroup, hostIndex) in groupedAccounts\" :key=\"hostGroup.hostId\">\r\n            <!-- 主机分组标题行 -->\r\n            <tr class=\"bg-gray-100\">\r\n              <td colspan=\"8\" class=\"px-6 py-2\">\r\n                <div class=\"flex items-center justify-between\">\r\n                  <div class=\"font-medium text-gray-700\">{{ hostGroup.hostName }} ({{ hostGroup.hostIp }})</div>\r\n                  <div>\r\n                    <button class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\"\r\n                      @click=\"openAddAccountModal(hostGroup.host)\">\r\n                      <font-awesome-icon :icon=\"['fas', 'plus']\" class=\"mr-1\" />\r\n                      添加账号\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n            <!-- 账号行 -->\r\n            <tr v-for=\"(account, accountIndex) in hostGroup.accounts\" :key=\"account.id\"\r\n              :class=\"{ 'bg-gray-50': accountIndex % 2 === 0, 'hover:bg-blue-50': true }\">\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <CustomCheckbox v-model=\"account.host.selected\" class=\"ml-4\">\r\n                    <span class=\"ml-2 font-medium text-gray-900\">{{ account.host.name }}</span>\r\n                  </CustomCheckbox>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-900\">{{ account.host.ip }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-500\">{{ account.lastPasswordChange || '-' }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div :class=\"{\r\n                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\r\n                  'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                  'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                  'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                }\">\r\n                  {{ isPasswordExpired(account).text }}\r\n                  <span\r\n                    v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                    class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                  </span>\r\n                  <span v-else-if=\"isPasswordExpired(account).status === 'warning'\" class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" />\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <div class=\"flex-grow\">\r\n                    <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\"\r\n                      readonly\r\n                      class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  </div>\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <StatusBadge :type=\"account.host.status\" />\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                <div class=\"flex space-x-2\">\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"openChangePasswordModal(account.host, account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                    修改密码\r\n                  </button>\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"copyPassword(account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'copy']\" class=\"mr-1\" />\r\n                    复制\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          </template>\r\n          <!-- 无数据显示 -->\r\n          <tr v-if=\"filteredAccounts.length === 0\">\r\n            <td colspan=\"8\" class=\"px-6 py-10 text-center\">\r\n              <div class=\"text-gray-500\">\r\n                <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-4xl mb-3\" />\r\n                <p>没有找到匹配的账号数据</p>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <!-- 卡片视图 -->\r\n    <div v-else class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\">\r\n      <div v-for=\"host in filteredHosts\" :key=\"host.id\" class=\"bg-white overflow-hidden shadow rounded-lg\">\r\n        <div class=\"px-4 py-5 sm:p-6\">\r\n          <!-- 主机头部 -->\r\n          <div class=\"flex justify-between items-start mb-4\">\r\n            <div class=\"flex items-center\">\r\n              <CustomCheckbox v-model=\"host.selected\" class=\"mr-2\" />\r\n              <div>\r\n                <h3 class=\"text-lg font-medium text-gray-900\">{{ host.name }}</h3>\r\n                <p class=\"text-sm text-gray-500\">{{ host.ip }}</p>\r\n              </div>\r\n            </div>\r\n            <StatusBadge :type=\"host.status\" />\r\n          </div>\r\n\r\n          <!-- 账号列表 -->\r\n          <div class=\"space-y-4\">\r\n            <div v-for=\"account in host.accounts\" :key=\"account.id\" class=\"border border-gray-200 rounded-lg p-3\"\r\n              :class=\"{ 'border-green-300 bg-green-50': account.isDefault }\">\r\n              <div class=\"flex justify-between items-center mb-2\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n                <button\r\n                  class=\"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                  @click=\"openChangePasswordModal(host, account)\">\r\n                  <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                  修改密码\r\n                </button>\r\n              </div>\r\n\r\n              <!-- 密码展示 -->\r\n              <div class=\"mb-2\">\r\n                <div class=\"text-xs font-medium text-gray-500 mb-1\">密码</div>\r\n                <div class=\"flex items-center\">\r\n                  <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\" readonly\r\n                    class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 密码信息区域 -->\r\n              <div class=\"grid grid-cols-2 gap-2 text-xs\">\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">最后修改时间</div>\r\n                  <div class=\"text-gray-900\">{{ account.lastPasswordChange || '-' }}</div>\r\n                </div>\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">密码过期</div>\r\n                  <div :class=\"{\r\n                    'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\r\n                    'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                    'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                    'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                  }\">\r\n                    {{ isPasswordExpired(account).text }}\r\n                    <span\r\n                      v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                      class=\"ml-1\">\r\n                      <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 添加账号按钮 -->\r\n          <div class=\"mt-4 flex justify-center\">\r\n            <button\r\n              class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n              @click=\"openAddAccountModal(host)\">\r\n              <font-awesome-icon :icon=\"['fas', 'plus']\" class=\"mr-1\" />\r\n              添加账号\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" @confirm=\"updatePassword\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n          <div><span class=\"font-medium\">账号:</span> {{ currentAccount.username }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-3\">\r\n          <button @click=\"changePasswordModal.method = 'auto'\"\r\n            class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n            :class=\"changePasswordModal.method === 'auto' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-2\" />\r\n            自动生成\r\n          </button>\r\n          <button @click=\"changePasswordModal.method = 'manual'\"\r\n            class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n            :class=\"changePasswordModal.method === 'manual' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n            <font-awesome-icon :icon=\"['fas', 'edit']\" class=\"mr-2\" />\r\n            手动输入\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-if=\"changePasswordModal.method === 'auto'\" class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"changePasswordModal.policyId\" class=\"form-select\" @change=\"generatePassword()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n\r\n        <div class=\"mt-3\">\r\n          <div class=\"flex justify-between mb-1\">\r\n            <label class=\"form-label\">生成的密码</label>\r\n            <button @click=\"generatePassword()\" type=\"button\"\r\n              class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n              重新生成\r\n            </button>\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.generated ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.generatedPassword\" readonly class=\"form-control flex-1 bg-gray-50\" />\r\n            <button @click=\"passwordVisibility.generated = !passwordVisibility.generated\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.generated ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else class=\"space-y-4\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.new ? 'text' : 'password'\" v-model=\"changePasswordModal.newPassword\"\r\n              class=\"form-control flex-1\" placeholder=\"输入新密码\" />\r\n            <button @click=\"passwordVisibility.new = !passwordVisibility.new\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.new ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.confirm ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.confirmPassword\" class=\"form-control flex-1\"\r\n              :class=\"{ 'border-red-500': passwordMismatch }\" placeholder=\"再次输入新密码\" />\r\n            <button @click=\"passwordVisibility.confirm = !passwordVisibility.confirm\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.confirm ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n          <div v-if=\"passwordMismatch\" class=\"text-sm text-red-500 mt-1\">两次输入的密码不一致</div>\r\n        </div>\r\n\r\n        <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n      </div>\r\n\r\n      <div class=\"space-y-2 mt-4\">\r\n        <div class=\"form-label font-medium\">执行选项</div>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          <span class=\"ml-2\">立即执行</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          <span class=\"ml-2\">保存历史记录</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          <span class=\"ml-2\">记录审计日志</span>\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 添加账号弹窗 -->\r\n    <BaseModal v-model=\"addAccountModal.show\" title=\"添加账号\" @confirm=\"addAccount\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">账号名称</label>\r\n        <input type=\"text\" v-model=\"addAccountModal.username\" class=\"form-control\" placeholder=\"输入账号名称\" />\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">设为默认账号</label>\r\n        <div class=\"relative inline-block w-10 mr-2 align-middle select-none\">\r\n          <input type=\"checkbox\" v-model=\"addAccountModal.isDefault\"\r\n            class=\"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\" />\r\n          <label class=\"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"></label>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"addAccountModal.policyId\" class=\"form-select\" @change=\"generatePasswordForNewAccount()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <div class=\"flex justify-between mb-1\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <button @click=\"generatePasswordForNewAccount()\" type=\"button\"\r\n            class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n            重新生成\r\n          </button>\r\n        </div>\r\n        <div class=\"flex items-center\">\r\n          <input :type=\"passwordVisibility.newAccount ? 'text' : 'password'\" v-model=\"addAccountModal.password\" readonly\r\n            class=\"form-control flex-1 bg-gray-50\" />\r\n          <button @click=\"passwordVisibility.newAccount = !passwordVisibility.newAccount\" type=\"button\"\r\n            class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', passwordVisibility.newAccount ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      currentAccount: {},\r\n      passwordVisibility: {\r\n        generated: false,\r\n        new: false,\r\n        confirm: false,\r\n        newAccount: false\r\n      },\r\n      viewMode: 'table',\r\n      filterText: '',\r\n      accountFilterText: '',\r\n      statusFilter: 'all',\r\n      expiryFilter: 'all',\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      },\r\n\r\n      // 添加账号弹窗\r\n      addAccountModal: {\r\n        show: false,\r\n        username: '',\r\n        password: '',\r\n        isDefault: false,\r\n        policyId: 1\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    },\r\n\r\n    // 过滤后的主机列表\r\n    filteredHosts() {\r\n      return this.hosts.filter(host => {\r\n        // 文本过滤\r\n        const textMatch = this.filterText === '' ||\r\n          host.name.toLowerCase().includes(this.filterText.toLowerCase()) ||\r\n          host.ip.includes(this.filterText);\r\n\r\n        // 状态过滤\r\n        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;\r\n\r\n        return textMatch && statusMatch;\r\n      });\r\n    },\r\n\r\n    // 获取所有账号（扁平化处理）\r\n    getAllAccounts() {\r\n      // 为每个账号添加主机引用\r\n      const accounts = [];\r\n      this.filteredHosts.forEach(host => {\r\n        host.accounts.forEach(account => {\r\n          accounts.push({\r\n            ...account,\r\n            host: host\r\n          });\r\n        });\r\n      });\r\n      return accounts;\r\n    },\r\n\r\n    // 筛选后的账号\r\n    filteredAccounts() {\r\n      return this.getAllAccounts.filter(account => {\r\n        // 账号名称筛选\r\n        const accountMatch = this.accountFilterText === '' ||\r\n          account.username.toLowerCase().includes(this.accountFilterText.toLowerCase());\r\n\r\n        // 密码过期筛选\r\n        let expiryMatch = true;\r\n        if (this.expiryFilter !== 'all') {\r\n          const expiryStatus = this.isPasswordExpired(account).status;\r\n          if (this.expiryFilter === 'expired') {\r\n            expiryMatch = expiryStatus === 'expired';\r\n          } else if (this.expiryFilter === 'expiring-soon') {\r\n            expiryMatch = expiryStatus === 'danger' || expiryStatus === 'warning';\r\n          } else if (this.expiryFilter === 'valid') {\r\n            expiryMatch = expiryStatus === 'normal';\r\n          }\r\n        }\r\n\r\n        return accountMatch && expiryMatch;\r\n      });\r\n    },\r\n\r\n    // 分组后的账号列表\r\n    groupedAccounts() {\r\n      // 按主机ID分组\r\n      const groups = {};\r\n      this.filteredAccounts.forEach(account => {\r\n        const hostId = account.host.id;\r\n        if (!groups[hostId]) {\r\n          groups[hostId] = {\r\n            hostId: hostId,\r\n            hostName: account.host.name,\r\n            hostIp: account.host.ip,\r\n            host: account.host,\r\n            accounts: []\r\n          };\r\n        }\r\n        groups[hostId].accounts.push(account);\r\n      });\r\n\r\n      // 转换为数组\r\n      return Object.values(groups);\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host, account) {\r\n      this.currentHost = host\r\n      this.currentAccount = account\r\n      this.changePasswordModal.show = true\r\n      this.changePasswordModal.generatedPassword = this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword(policy) {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policyObj ? policyObj.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      if (this.changePasswordModal && !policy) {\r\n        this.changePasswordModal.generatedPassword = password\r\n      }\r\n\r\n      return password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto'\r\n          ? this.changePasswordModal.generatedPassword\r\n          : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          accountId: this.currentAccount.id,\r\n          password: password,\r\n          policyId: this.changePasswordModal.policyId\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的 ${this.currentAccount.username} 账号密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取所选策略\r\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取紧急策略\r\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    togglePasswordVisibility(hostId) {\r\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]\r\n    },\r\n\r\n    isPasswordExpired(account) {\r\n      if (!account.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }\r\n\r\n      // 解析过期时间\r\n      const expiryDate = new Date(account.passwordExpiryDate)\r\n      const now = new Date()\r\n\r\n      // 如果已过期\r\n      if (expiryDate < now) {\r\n        return {\r\n          status: 'expired',\r\n          days: 0,\r\n          text: '已过期'\r\n        }\r\n      }\r\n\r\n      // 计算剩余天数和小时数\r\n      const diffTime = expiryDate - now\r\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))\r\n      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n\r\n      // 根据剩余时间确定状态\r\n      let status = 'normal'\r\n      if (diffDays < 7) {\r\n        status = 'danger'  // 少于7天\r\n      } else if (diffDays < 14) {\r\n        status = 'warning' // 少于14天\r\n      }\r\n\r\n      // 格式化显示文本\r\n      let text = ''\r\n      if (diffDays > 0) {\r\n        text += `${diffDays}天`\r\n      }\r\n      if (diffHours > 0 || diffDays === 0) {\r\n        text += `${diffHours}小时`\r\n      }\r\n\r\n      return { status, days: diffDays, text: `剩余${text}` }\r\n    },\r\n\r\n    openAddAccountModal(host) {\r\n      this.currentHost = host\r\n      this.addAccountModal.show = true\r\n    },\r\n\r\n    async addAccount() {\r\n      if (!this.addAccountModal.username || !this.addAccountModal.password) {\r\n        alert('请填写完整的账号信息！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('addHostAccount', {\r\n          hostId: this.currentHost.id,\r\n          username: this.addAccountModal.username,\r\n          password: this.addAccountModal.password,\r\n          policyId: this.addAccountModal.policyId,\r\n          isDefault: this.addAccountModal.isDefault\r\n        })\r\n\r\n        this.addAccountModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为主机 ${this.currentHost.name} 添加账号！`)\r\n      } catch (error) {\r\n        console.error('添加账号失败', error)\r\n        alert('添加账号失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    generatePasswordForNewAccount() {\r\n      this.addAccountModal.password = this.generatePassword()\r\n    },\r\n\r\n    // 复制密码到剪贴板\r\n    copyPassword(account) {\r\n      // 创建一个临时输入框\r\n      const tempInput = document.createElement('input');\r\n      tempInput.value = account.password;\r\n      document.body.appendChild(tempInput);\r\n      tempInput.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(tempInput);\r\n\r\n      // 显示提示\r\n      alert(`已复制 ${account.host.name} 的 ${account.username} 账号密码到剪贴板！`);\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": "AA4nBA,SAASA,QAAQ,EAAEC,UAAS,QAAS,MAAK;AAC1C,OAAOC,SAAQ,MAAO,4BAA2B;AACjD,OAAOC,WAAU,MAAO,8BAA6B;AACrD,OAAOC,cAAa,MAAO,iCAAgC;AAC3D,OAAOC,qBAAoB,MAAO,wCAAuC;AAEzE,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,UAAU,EAAE;IACVL,SAAS;IACTC,WAAW;IACXC,cAAc;IACdC;EACF,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,KAAK;MAChBC,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,CAAC,CAAC;MACfC,cAAc,EAAE,CAAC,CAAC;MAClBC,kBAAkB,EAAE;QAClBC,SAAS,EAAE,KAAK;QAChBC,GAAG,EAAE,KAAK;QACVC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE;MACd,CAAC;MACDC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE,EAAE;MACdC,iBAAiB,EAAE,EAAE;MACrBC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,KAAK;MAEnB;MACAC,mBAAmB,EAAE;QACnBC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE,CAAC;QACXC,iBAAiB,EAAE,cAAc;QACjCC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE,EAAE;QACnBC,kBAAkB,EAAE,IAAI;QACxBC,WAAW,EAAE,KAAK;QAClBC,QAAQ,EAAE;MACZ,CAAC;MAED;MACAC,gBAAgB,EAAE;QAChBT,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBR,QAAQ,EAAE,CAAC;QACXS,aAAa,EAAE,WAAW;QAC1BC,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,EAAE;QACjBC,YAAY,EAAE,IAAI;QAClBC,WAAW,EAAE,IAAI;QACjBC,gBAAgB,EAAE;MACpB,CAAC;MAED;MACAC,eAAe,EAAE;QACfjB,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBR,QAAQ,EAAE,CAAC;QACXgB,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;MAED;MACAC,mBAAmB,EAAE;QACnBpB,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBR,QAAQ,EAAE,CAAC;QAAE;QACbmB,MAAM,EAAE,mBAAmB;QAC3BC,WAAW,EAAE;MACf,CAAC;MAED;MACAC,eAAe,EAAE;QACfvB,IAAI,EAAE,KAAK;QACXwB,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,KAAK;QAChBxB,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;EACDyB,QAAQ,EAAE;IACR,GAAGpD,QAAQ,CAAC;MACVqD,KAAK,EAAEC,KAAI,IAAKA,KAAK,CAACD,KAAK;MAC3BE,QAAQ,EAAED,KAAI,IAAKA,KAAK,CAACC;IAC3B,CAAC,CAAC;IACF,GAAGtD,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC;IAEhCuD,gBAAgBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAAChC,mBAAmB,CAACK,WAAU,IACxC,IAAI,CAACL,mBAAmB,CAACM,eAAc,IACvC,IAAI,CAACN,mBAAmB,CAACK,WAAU,KAAM,IAAI,CAACL,mBAAmB,CAACM,eAAc;IACpF,CAAC;IAED2B,kBAAkBA,CAAA,EAAG;MACnB,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACzB,gBAAgB,CAACC,aAAa,CAAC,CAACyB,MAAM,CAACC,OAAO,CAAC,CAACC,MAAK;IACjF,CAAC;IAEDC,iBAAiBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACV,KAAK,CAACO,MAAM,CAACI,IAAG,IAAKA,IAAI,CAACC,QAAQ;IAChD,CAAC;IAEDC,iBAAiBA,CAAA,EAAG;MAClB;MACA,OAAO,IAAI,CAACX,QAAQ,CAACK,MAAM,CAACO,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,KAAKD,CAAC,CAACC,EAAC,KAAM,CAAC;IAC3D,CAAC;IAED;IACAC,aAAaA,CAAA,EAAG;MACd,OAAO,IAAI,CAAChB,KAAK,CAACO,MAAM,CAACI,IAAG,IAAK;QAC/B;QACA,MAAMM,SAAQ,GAAI,IAAI,CAAClD,UAAS,KAAM,EAAC,IACrC4C,IAAI,CAAC1D,IAAI,CAACiE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACpD,UAAU,CAACmD,WAAW,CAAC,CAAC,KAC9DP,IAAI,CAACS,EAAE,CAACD,QAAQ,CAAC,IAAI,CAACpD,UAAU,CAAC;;QAEnC;QACA,MAAMsD,WAAU,GAAI,IAAI,CAACpD,YAAW,KAAM,KAAI,IAAK0C,IAAI,CAACW,MAAK,KAAM,IAAI,CAACrD,YAAY;QAEpF,OAAOgD,SAAQ,IAAKI,WAAW;MACjC,CAAC,CAAC;IACJ,CAAC;IAED;IACAE,cAAcA,CAAA,EAAG;MACf;MACA,MAAMC,QAAO,GAAI,EAAE;MACnB,IAAI,CAACR,aAAa,CAACS,OAAO,CAACd,IAAG,IAAK;QACjCA,IAAI,CAACa,QAAQ,CAACC,OAAO,CAACC,OAAM,IAAK;UAC/BF,QAAQ,CAACG,IAAI,CAAC;YACZ,GAAGD,OAAO;YACVf,IAAI,EAAEA;UACR,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAOa,QAAQ;IACjB,CAAC;IAED;IACAI,gBAAgBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACL,cAAc,CAAChB,MAAM,CAACmB,OAAM,IAAK;QAC3C;QACA,MAAMG,YAAW,GAAI,IAAI,CAAC7D,iBAAgB,KAAM,EAAC,IAC/C0D,OAAO,CAAC9B,QAAQ,CAACsB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACnD,iBAAiB,CAACkD,WAAW,CAAC,CAAC,CAAC;;QAE/E;QACA,IAAIY,WAAU,GAAI,IAAI;QACtB,IAAI,IAAI,CAAC5D,YAAW,KAAM,KAAK,EAAE;UAC/B,MAAM6D,YAAW,GAAI,IAAI,CAACC,iBAAiB,CAACN,OAAO,CAAC,CAACJ,MAAM;UAC3D,IAAI,IAAI,CAACpD,YAAW,KAAM,SAAS,EAAE;YACnC4D,WAAU,GAAIC,YAAW,KAAM,SAAS;UAC1C,OAAO,IAAI,IAAI,CAAC7D,YAAW,KAAM,eAAe,EAAE;YAChD4D,WAAU,GAAIC,YAAW,KAAM,QAAO,IAAKA,YAAW,KAAM,SAAS;UACvE,OAAO,IAAI,IAAI,CAAC7D,YAAW,KAAM,OAAO,EAAE;YACxC4D,WAAU,GAAIC,YAAW,KAAM,QAAQ;UACzC;QACF;QAEA,OAAOF,YAAW,IAAKC,WAAW;MACpC,CAAC,CAAC;IACJ,CAAC;IAED;IACAG,eAAeA,CAAA,EAAG;MAChB;MACA,MAAMC,MAAK,GAAI,CAAC,CAAC;MACjB,IAAI,CAACN,gBAAgB,CAACH,OAAO,CAACC,OAAM,IAAK;QACvC,MAAMS,MAAK,GAAIT,OAAO,CAACf,IAAI,CAACI,EAAE;QAC9B,IAAI,CAACmB,MAAM,CAACC,MAAM,CAAC,EAAE;UACnBD,MAAM,CAACC,MAAM,IAAI;YACfA,MAAM,EAAEA,MAAM;YACdC,QAAQ,EAAEV,OAAO,CAACf,IAAI,CAAC1D,IAAI;YAC3BoF,MAAM,EAAEX,OAAO,CAACf,IAAI,CAACS,EAAE;YACvBT,IAAI,EAAEe,OAAO,CAACf,IAAI;YAClBa,QAAQ,EAAE;UACZ,CAAC;QACH;QACAU,MAAM,CAACC,MAAM,CAAC,CAACX,QAAQ,CAACG,IAAI,CAACD,OAAO,CAAC;MACvC,CAAC,CAAC;;MAEF;MACA,OAAOrB,MAAM,CAACC,MAAM,CAAC4B,MAAM,CAAC;IAC9B;EACF,CAAC;EACDI,OAAO,EAAE;IACPC,eAAeA,CAACC,KAAK,EAAE;MACrB,IAAI,CAACC,MAAM,CAACC,MAAM,CAAC,gBAAgB,EAAEF,KAAK;IAC5C,CAAC;IAEDG,oBAAoBA,CAACH,KAAK,EAAE;MAC1B,IAAI,CAACxC,KAAK,CAACyB,OAAO,CAACd,IAAG,IAAK;QACzB,IAAI,CAAC9B,gBAAgB,CAACC,aAAa,CAAC6B,IAAI,CAACI,EAAE,IAAIyB,KAAI;MACrD,CAAC;IACH,CAAC;IAEDI,uBAAuBA,CAACjC,IAAI,EAAEe,OAAO,EAAE;MACrC,IAAI,CAACnE,WAAU,GAAIoD,IAAG;MACtB,IAAI,CAACnD,cAAa,GAAIkE,OAAM;MAC5B,IAAI,CAACvD,mBAAmB,CAACC,IAAG,GAAI,IAAG;MACnC,IAAI,CAACD,mBAAmB,CAACI,iBAAgB,GAAI,IAAI,CAACsE,gBAAgB,CAAC;IACrE,CAAC;IAEDC,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAACjE,gBAAgB,CAACT,IAAG,GAAI,IAAG;;MAEhC;MACA,IAAI,CAAC4B,KAAK,CAACyB,OAAO,CAACd,IAAG,IAAK;QACzB,IAAI,CAAC9B,gBAAgB,CAACC,aAAa,CAAC6B,IAAI,CAACI,EAAE,IAAIJ,IAAI,CAACC,QAAO;MAC7D,CAAC;;MAED;MACA,MAAMmC,KAAI,GAAI,IAAIC,IAAI,CAAC;MACvB,IAAI,CAACnE,gBAAgB,CAACG,aAAY,GAAI+D,KAAK,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MACtE,IAAI,CAACrE,gBAAgB,CAACI,aAAY,GAAI,OAAM;IAC9C,CAAC;IAEDkE,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAAC9D,eAAe,CAACjB,IAAG,GAAI,IAAG;;MAE/B;MACA,IAAI,CAAC4B,KAAK,CAACyB,OAAO,CAACd,IAAG,IAAK;QACzB,IAAI,CAACtB,eAAe,CAACP,aAAa,CAAC6B,IAAI,CAACI,EAAE,IAAIJ,IAAI,CAACC,QAAO;MAC5D,CAAC;IACH,CAAC;IAEDwC,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAAC5D,mBAAmB,CAACpB,IAAG,GAAI,IAAG;;MAEnC;MACA,IAAI,CAAC4B,KAAK,CAACyB,OAAO,CAACd,IAAG,IAAK;QACzB,IAAI,CAACnB,mBAAmB,CAACV,aAAa,CAAC6B,IAAI,CAACI,EAAE,IAAIJ,IAAI,CAACC,QAAO;MAChE,CAAC;IACH,CAAC;IAEDiC,gBAAgBA,CAACQ,MAAM,EAAE;MACvB,MAAMC,KAAI,GAAI,0EAAyE;MACvF,IAAIzD,QAAO,GAAI,EAAC;;MAEhB;MACA,MAAM0D,SAAQ,GAAIF,MAAK,IAAK,IAAI,CAACnD,QAAQ,CAACsD,IAAI,CAAC1C,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,IAAI,CAAC5C,mBAAmB,CAACG,QAAQ;MAC9F,MAAMmF,SAAQ,GAAIF,SAAQ,GAAIA,SAAS,CAACE,SAAQ,GAAI,EAAC;;MAErD;MACA,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAID,SAAS,EAAEC,CAAC,EAAE,EAAE;QAClC7D,QAAO,IAAKyD,KAAK,CAACK,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIR,KAAK,CAAC7C,MAAM,CAAC;MACnE;MAEA,IAAI,IAAI,CAACtC,mBAAkB,IAAK,CAACkF,MAAM,EAAE;QACvC,IAAI,CAAClF,mBAAmB,CAACI,iBAAgB,GAAIsB,QAAO;MACtD;MAEA,OAAOA,QAAO;IAChB,CAAC;IAED,MAAMkE,cAAcA,CAAA,EAAG;MACrB,IAAI,IAAI,CAAC5F,mBAAmB,CAACE,MAAK,KAAM,QAAO,IAAK,IAAI,CAAC8B,gBAAgB,EAAE;QACzE;MACF;MAEA,IAAI,CAAC7C,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAMuC,QAAO,GAAI,IAAI,CAAC1B,mBAAmB,CAACE,MAAK,KAAM,MAAK,GACtD,IAAI,CAACF,mBAAmB,CAACI,iBAAgB,GACzC,IAAI,CAACJ,mBAAmB,CAACK,WAAU;QAEvC,MAAM,IAAI,CAACiE,MAAM,CAACuB,QAAQ,CAAC,oBAAoB,EAAE;UAC/C7B,MAAM,EAAE,IAAI,CAAC5E,WAAW,CAACwD,EAAE;UAC3BkD,SAAS,EAAE,IAAI,CAACzG,cAAc,CAACuD,EAAE;UACjClB,QAAQ,EAAEA,QAAQ;UAClBvB,QAAQ,EAAE,IAAI,CAACH,mBAAmB,CAACG;QACrC,CAAC;QAED,IAAI,CAACH,mBAAmB,CAACC,IAAG,GAAI,KAAI;;QAEpC;QACA8F,KAAK,CAAC,WAAW,IAAI,CAAC3G,WAAW,CAACN,IAAI,MAAM,IAAI,CAACO,cAAc,CAACoC,QAAQ,QAAQ;MAClF,EAAE,OAAOuE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAAC5G,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED,MAAM+G,oBAAoBA,CAAA,EAAG;MAC3B,MAAMC,eAAc,GAAIjE,MAAM,CAACkE,OAAO,CAAC,IAAI,CAAC1F,gBAAgB,CAACC,aAAa,EACvEyB,MAAM,CAAC,CAAC,CAACiE,CAAC,EAAE5D,QAAQ,CAAC,KAAKA,QAAQ,EAClC6D,GAAG,CAAC,CAAC,CAAC1D,EAAE,CAAC,KAAK2D,QAAQ,CAAC3D,EAAE,CAAC;MAE7B,IAAIuD,eAAe,CAAC7D,MAAK,KAAM,CAAC,EAAE;QAChCyD,KAAK,CAAC,YAAY;QAClB;MACF;MAEA,IAAI,IAAI,CAACrF,gBAAgB,CAACE,aAAY,KAAM,WAAW,EAAE;QACvD;QACAmF,KAAK,CAAC,cAAc;QACpB,IAAI,CAACrF,gBAAgB,CAACT,IAAG,GAAI,KAAI;QACjC;MACF;MAEA,IAAI,CAACd,UAAS,GAAI,IAAG;MAErB,IAAI;QACF;QACA,MAAM+F,MAAK,GAAI,IAAI,CAACnD,QAAQ,CAACsD,IAAI,CAAC1C,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,IAAI,CAAClC,gBAAgB,CAACP,QAAQ;;QAE9E;QACA,KAAK,MAAM6D,MAAK,IAAKmC,eAAe,EAAE;UACpC,MAAM3D,IAAG,GAAI,IAAI,CAACX,KAAK,CAACwD,IAAI,CAACmB,CAAA,IAAKA,CAAC,CAAC5D,EAAC,KAAMoB,MAAM;UACjD,IAAIxB,IAAI,EAAE;YACR,KAAK,MAAMe,OAAM,IAAKf,IAAI,CAACa,QAAQ,EAAE;cACnC,MAAMhD,WAAU,GAAI,IAAI,CAACqE,gBAAgB,CAACQ,MAAM;cAChD,MAAM,IAAI,CAACZ,MAAM,CAACuB,QAAQ,CAAC,oBAAoB,EAAE;gBAC/C7B,MAAM,EAAEA,MAAM;gBACd8B,SAAS,EAAEvC,OAAO,CAACX,EAAE;gBACrBlB,QAAQ,EAAErB,WAAW;gBACrBF,QAAQ,EAAE+E,MAAM,CAACtC;cACnB,CAAC;YACH;UACF;QACF;QAEA,IAAI,CAAClC,gBAAgB,CAACT,IAAG,GAAI,KAAI;;QAEjC;QACA8F,KAAK,CAAC,QAAQI,eAAe,CAAC7D,MAAM,gBAAgB;MACtD,EAAE,OAAO0D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK;QAC/BD,KAAK,CAAC,eAAe;MACvB,UAAU;QACR,IAAI,CAAC5G,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED,MAAMsH,gBAAgBA,CAAA,EAAG;MACvB,MAAMN,eAAc,GAAIjE,MAAM,CAACkE,OAAO,CAAC,IAAI,CAAClF,eAAe,CAACP,aAAa,EACtEyB,MAAM,CAAC,CAAC,CAACiE,CAAC,EAAE5D,QAAQ,CAAC,KAAKA,QAAQ,EAClC6D,GAAG,CAAC,CAAC,CAAC1D,EAAE,CAAC,KAAK2D,QAAQ,CAAC3D,EAAE,CAAC;MAE7B,IAAIuD,eAAe,CAAC7D,MAAK,KAAM,CAAC,EAAE;QAChCyD,KAAK,CAAC,YAAY;QAClB;MACF;MAEA,IAAI,CAAC5G,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAM,IAAI,CAACmF,MAAM,CAACuB,QAAQ,CAAC,oBAAoB,EAAE;UAC/C1F,QAAQ,EAAE,IAAI,CAACe,eAAe,CAACf,QAAQ;UACvCuG,OAAO,EAAEP;QACX,CAAC;QAED,IAAI,CAACjF,eAAe,CAACjB,IAAG,GAAI,KAAI;;QAEhC;QACA8F,KAAK,CAAC,QAAQI,eAAe,CAAC7D,MAAM,aAAa;MACnD,EAAE,OAAO0D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAAC5G,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED,MAAMwH,cAAcA,CAAA,EAAG;MACrB,MAAMR,eAAc,GAAIjE,MAAM,CAACkE,OAAO,CAAC,IAAI,CAAC/E,mBAAmB,CAACV,aAAa,EAC1EyB,MAAM,CAAC,CAAC,CAACiE,CAAC,EAAE5D,QAAQ,CAAC,KAAKA,QAAQ,EAClC6D,GAAG,CAAC,CAAC,CAAC1D,EAAE,CAAC,KAAK2D,QAAQ,CAAC3D,EAAE,CAAC;MAE7B,IAAIuD,eAAe,CAAC7D,MAAK,KAAM,CAAC,EAAE;QAChCyD,KAAK,CAAC,YAAY;QAClB;MACF;MAEA,IAAI,CAAC5G,UAAS,GAAI,IAAG;MAErB,IAAI;QACF;QACA,MAAM+F,MAAK,GAAI,IAAI,CAACnD,QAAQ,CAACsD,IAAI,CAAC1C,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,IAAI,CAACvB,mBAAmB,CAAClB,QAAQ;;QAEjF;QACA,KAAK,MAAM6D,MAAK,IAAKmC,eAAe,EAAE;UACpC,MAAM3D,IAAG,GAAI,IAAI,CAACX,KAAK,CAACwD,IAAI,CAACmB,CAAA,IAAKA,CAAC,CAAC5D,EAAC,KAAMoB,MAAM;UACjD,IAAIxB,IAAI,EAAE;YACR,KAAK,MAAMe,OAAM,IAAKf,IAAI,CAACa,QAAQ,EAAE;cACnC,MAAMhD,WAAU,GAAI,IAAI,CAACqE,gBAAgB,CAACQ,MAAM;cAChD,MAAM,IAAI,CAACZ,MAAM,CAACuB,QAAQ,CAAC,oBAAoB,EAAE;gBAC/C7B,MAAM,EAAEA,MAAM;gBACd8B,SAAS,EAAEvC,OAAO,CAACX,EAAE;gBACrBlB,QAAQ,EAAErB,WAAW;gBACrBF,QAAQ,EAAE+E,MAAM,CAACtC;cACnB,CAAC;YACH;UACF;QACF;QAEA,IAAI,CAACvB,mBAAmB,CAACpB,IAAG,GAAI,KAAI;;QAEpC;QACA8F,KAAK,CAAC,QAAQI,eAAe,CAAC7D,MAAM,oBAAoB;MAC1D,EAAE,OAAO0D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAAC5G,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAEDyH,wBAAwBA,CAAC5C,MAAM,EAAE;MAC/B,IAAI,CAAC1E,kBAAkB,CAAC0E,MAAM,IAAI,CAAC,IAAI,CAAC1E,kBAAkB,CAAC0E,MAAM;IACnE,CAAC;IAEDH,iBAAiBA,CAACN,OAAO,EAAE;MACzB,IAAI,CAACA,OAAO,CAACsD,kBAAkB,EAAE,OAAO;QAAE1D,MAAM,EAAE,QAAQ;QAAE2D,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAI;;MAElF;MACA,MAAMC,UAAS,GAAI,IAAInC,IAAI,CAACtB,OAAO,CAACsD,kBAAkB;MACtD,MAAMI,GAAE,GAAI,IAAIpC,IAAI,CAAC;;MAErB;MACA,IAAImC,UAAS,GAAIC,GAAG,EAAE;QACpB,OAAO;UACL9D,MAAM,EAAE,SAAS;UACjB2D,IAAI,EAAE,CAAC;UACPC,IAAI,EAAE;QACR;MACF;;MAEA;MACA,MAAMG,QAAO,GAAIF,UAAS,GAAIC,GAAE;MAChC,MAAME,QAAO,GAAI1B,IAAI,CAACC,KAAK,CAACwB,QAAO,IAAK,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,EAAE,CAAC;MAC5D,MAAME,SAAQ,GAAI3B,IAAI,CAACC,KAAK,CAAEwB,QAAO,IAAK,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,EAAE,CAAC,IAAK,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC;;MAElF;MACA,IAAI/D,MAAK,GAAI,QAAO;MACpB,IAAIgE,QAAO,GAAI,CAAC,EAAE;QAChBhE,MAAK,GAAI,QAAO,EAAG;MACrB,OAAO,IAAIgE,QAAO,GAAI,EAAE,EAAE;QACxBhE,MAAK,GAAI,SAAQ,EAAE;MACrB;;MAEA;MACA,IAAI4D,IAAG,GAAI,EAAC;MACZ,IAAII,QAAO,GAAI,CAAC,EAAE;QAChBJ,IAAG,IAAK,GAAGI,QAAQ,GAAE;MACvB;MACA,IAAIC,SAAQ,GAAI,KAAKD,QAAO,KAAM,CAAC,EAAE;QACnCJ,IAAG,IAAK,GAAGK,SAAS,IAAG;MACzB;MAEA,OAAO;QAAEjE,MAAM;QAAE2D,IAAI,EAAEK,QAAQ;QAAEJ,IAAI,EAAE,KAAKA,IAAI;MAAG;IACrD,CAAC;IAEDM,mBAAmBA,CAAC7E,IAAI,EAAE;MACxB,IAAI,CAACpD,WAAU,GAAIoD,IAAG;MACtB,IAAI,CAAChB,eAAe,CAACvB,IAAG,GAAI,IAAG;IACjC,CAAC;IAED,MAAMqH,UAAUA,CAAA,EAAG;MACjB,IAAI,CAAC,IAAI,CAAC9F,eAAe,CAACC,QAAO,IAAK,CAAC,IAAI,CAACD,eAAe,CAACE,QAAQ,EAAE;QACpEqE,KAAK,CAAC,aAAa;QACnB;MACF;MAEA,IAAI,CAAC5G,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAM,IAAI,CAACmF,MAAM,CAACuB,QAAQ,CAAC,gBAAgB,EAAE;UAC3C7B,MAAM,EAAE,IAAI,CAAC5E,WAAW,CAACwD,EAAE;UAC3BnB,QAAQ,EAAE,IAAI,CAACD,eAAe,CAACC,QAAQ;UACvCC,QAAQ,EAAE,IAAI,CAACF,eAAe,CAACE,QAAQ;UACvCvB,QAAQ,EAAE,IAAI,CAACqB,eAAe,CAACrB,QAAQ;UACvCwB,SAAS,EAAE,IAAI,CAACH,eAAe,CAACG;QAClC,CAAC;QAED,IAAI,CAACH,eAAe,CAACvB,IAAG,GAAI,KAAI;;QAEhC;QACA8F,KAAK,CAAC,UAAU,IAAI,CAAC3G,WAAW,CAACN,IAAI,QAAQ;MAC/C,EAAE,OAAOkH,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAAC5G,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAEDoI,6BAA6BA,CAAA,EAAG;MAC9B,IAAI,CAAC/F,eAAe,CAACE,QAAO,GAAI,IAAI,CAACgD,gBAAgB,CAAC;IACxD,CAAC;IAED;IACA8C,YAAYA,CAACjE,OAAO,EAAE;MACpB;MACA,MAAMkE,SAAQ,GAAIC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MACjDF,SAAS,CAACpD,KAAI,GAAId,OAAO,CAAC7B,QAAQ;MAClCgG,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,SAAS,CAAC;MACpCA,SAAS,CAACK,MAAM,CAAC,CAAC;MAClBJ,QAAQ,CAACK,WAAW,CAAC,MAAM,CAAC;MAC5BL,QAAQ,CAACE,IAAI,CAACI,WAAW,CAACP,SAAS,CAAC;;MAEpC;MACA1B,KAAK,CAAC,OAAOxC,OAAO,CAACf,IAAI,CAAC1D,IAAI,MAAMyE,OAAO,CAAC9B,QAAQ,YAAY,CAAC;IACnE;EACF,CAAC;EACDwG,OAAOA,CAAA,EAAG;IACR;IACA,MAAMrD,KAAI,GAAI,IAAIC,IAAI,CAAC;IACvB,IAAI,CAACnE,gBAAgB,CAACG,aAAY,GAAI+D,KAAK,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IACtE,IAAI,CAACrE,gBAAgB,CAACI,aAAY,GAAI,OAAM;EAC9C;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}