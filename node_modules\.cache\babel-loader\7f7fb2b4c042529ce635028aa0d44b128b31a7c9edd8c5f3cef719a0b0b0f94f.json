{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, vModelText as _vModelText, withDirectives as _withDirectives, vModelCheckbox as _vModelCheckbox, withCtx as _withCtx, createTextVNode as _createTextVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"flex justify-between items-center mb-6\"\n};\nconst _hoisted_2 = {\n  class: \"grid grid-cols-1 md:grid-cols-2 gap-6\"\n};\nconst _hoisted_3 = {\n  class: \"flex justify-between items-start mb-2\"\n};\nconst _hoisted_4 = {\n  class: \"text-lg font-medium\"\n};\nconst _hoisted_5 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_6 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_7 = {\n  class: \"grid grid-cols-2 gap-4 mb-4\"\n};\nconst _hoisted_8 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_9 = {\n  class: \"text-sm\"\n};\nconst _hoisted_10 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_11 = {\n  class: \"text-sm\"\n};\nconst _hoisted_12 = {\n  class: \"flex justify-end space-x-3\"\n};\nconst _hoisted_13 = [\"onClick\"];\nconst _hoisted_14 = [\"onClick\"];\nconst _hoisted_15 = {\n  class: \"form-group\"\n};\nconst _hoisted_16 = {\n  class: \"form-group\"\n};\nconst _hoisted_17 = {\n  class: \"form-group\"\n};\nconst _hoisted_18 = {\n  class: \"space-y-3\"\n};\nconst _hoisted_19 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_20 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_21 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_22 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_23 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_24 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_25 = {\n  class: \"form-group\"\n};\nconst _hoisted_26 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_27 = {\n  class: \"form-group\"\n};\nconst _hoisted_28 = {\n  class: \"flex items-center\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_BaseModal = _resolveComponent(\"BaseModal\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createElementVNode(\"div\", _hoisted_1, [_cache[14] || (_cache[14] = _createElementVNode(\"h2\", {\n    class: \"text-lg font-semibold\"\n  }, \"密码策略管理\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    class: \"btn-primary\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.showNewPolicyModal && $options.showNewPolicyModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'plus'],\n    class: \"mr-2\"\n  }), _cache[13] || (_cache[13] = _createElementVNode(\"span\", null, \"新建策略\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_2, [_createCommentVNode(\" 策略卡片 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: policy.id,\n      class: \"card\"\n    }, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", null, [_createElementVNode(\"h3\", _hoisted_4, _toDisplayString(policy.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_5, _toDisplayString(policy.description), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_6, _toDisplayString(policy.hostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'lock'],\n      class: \"text-gray-400 mr-2\"\n    }), _createElementVNode(\"span\", _hoisted_9, \"最小长度: \" + _toDisplayString(policy.minLength), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'calendar-alt'],\n      class: \"text-gray-400 mr-2\"\n    }), _createElementVNode(\"span\", _hoisted_11, \"过期时间: \" + _toDisplayString(policy.expiryDays) + \" 天\", 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"button\", {\n      class: \"text-gray-600 hover:text-gray-800 text-sm\",\n      onClick: $event => $options.editPolicy(policy)\n    }, \" 编辑 \", 8 /* PROPS */, _hoisted_13), _createElementVNode(\"button\", {\n      class: \"text-red-600 hover:text-red-800 text-sm\",\n      onClick: $event => $options.confirmDeletePolicy(policy)\n    }, \" 删除 \", 8 /* PROPS */, _hoisted_14)])]);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 新建/编辑策略弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.policyModal.show,\n    \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $data.policyModal.show = $event),\n    title: $data.policyModal.isEdit ? '编辑密码策略' : '新建密码策略',\n    \"confirm-text\": $data.policyModal.isEdit ? '保存更改' : '创建策略',\n    onConfirm: $options.savePolicyChanges,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_15, [_cache[15] || (_cache[15] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"策略名称\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.policyModal.form.name = $event),\n      class: \"form-control\",\n      placeholder: \"输入策略名称\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.policyModal.form.name]])]), _createElementVNode(\"div\", _hoisted_16, [_cache[16] || (_cache[16] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"策略描述\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.policyModal.form.description = $event),\n      class: \"form-control\",\n      placeholder: \"适用场景描述\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.policyModal.form.description]])]), _createElementVNode(\"div\", _hoisted_17, [_cache[23] || (_cache[23] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码规则\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_cache[17] || (_cache[17] = _createElementVNode(\"span\", {\n      class: \"w-32 text-sm\"\n    }, \"最小长度:\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"number\",\n      min: \"8\",\n      \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.policyModal.form.minLength = $event),\n      class: \"w-20 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.policyModal.form.minLength, void 0, {\n      number: true\n    }]])]), _createElementVNode(\"div\", _hoisted_20, [_cache[18] || (_cache[18] = _createElementVNode(\"span\", {\n      class: \"w-32 text-sm\"\n    }, \"必须包含大写字母:\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.policyModal.form.requireUppercase = $event)\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.policyModal.form.requireUppercase]])]), _createElementVNode(\"div\", _hoisted_21, [_cache[19] || (_cache[19] = _createElementVNode(\"span\", {\n      class: \"w-32 text-sm\"\n    }, \"必须包含小写字母:\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.policyModal.form.requireLowercase = $event)\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.policyModal.form.requireLowercase]])]), _createElementVNode(\"div\", _hoisted_22, [_cache[20] || (_cache[20] = _createElementVNode(\"span\", {\n      class: \"w-32 text-sm\"\n    }, \"必须包含数字:\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.policyModal.form.requireNumbers = $event)\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.policyModal.form.requireNumbers]])]), _createElementVNode(\"div\", _hoisted_23, [_cache[21] || (_cache[21] = _createElementVNode(\"span\", {\n      class: \"w-32 text-sm\"\n    }, \"必须包含特殊字符:\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.policyModal.form.requireSpecial = $event)\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.policyModal.form.requireSpecial]])]), _createElementVNode(\"div\", _hoisted_24, [_cache[22] || (_cache[22] = _createElementVNode(\"span\", {\n      class: \"w-32 text-sm\"\n    }, \"不能包含用户名:\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.policyModal.form.forbidUsername = $event)\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.policyModal.form.forbidUsername]])])])]), _createElementVNode(\"div\", _hoisted_25, [_cache[25] || (_cache[25] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码有效期\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_26, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"number\",\n      min: \"1\",\n      \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.policyModal.form.expiryDays = $event),\n      class: \"w-20 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.policyModal.form.expiryDays, void 0, {\n      number: true\n    }]]), _cache[24] || (_cache[24] = _createElementVNode(\"span\", {\n      class: \"ml-2\"\n    }, \"天\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_27, [_cache[28] || (_cache[28] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码历史\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_28, [_cache[26] || (_cache[26] = _createElementVNode(\"span\", {\n      class: \"text-sm\"\n    }, \"不能重复使用最近\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"number\",\n      min: \"1\",\n      \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.policyModal.form.historyCount = $event),\n      class: \"mx-2 w-16 px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.policyModal.form.historyCount, void 0, {\n      number: true\n    }]]), _cache[27] || (_cache[27] = _createElementVNode(\"span\", {\n      class: \"text-sm\"\n    }, \"次使用过的密码\", -1 /* HOISTED */))])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"title\", \"confirm-text\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 删除确认弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.deleteModal.show,\n    \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $data.deleteModal.show = $event),\n    title: \"确认删除策略\",\n    \"confirm-text\": \"删除\",\n    danger: \"\",\n    onConfirm: $options.deletePolicy,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"p\", null, [_cache[29] || (_cache[29] = _createTextVNode(\"您确定要删除策略 \")), _createElementVNode(\"strong\", null, _toDisplayString($data.deleteModal.policyName), 1 /* TEXT */), _cache[30] || (_cache[30] = _createTextVNode(\" 吗？\"))]), _cache[31] || (_cache[31] = _createElementVNode(\"p\", {\n      class: \"mt-2 text-red-600\"\n    }, \"此操作无法撤销，删除后将影响所有使用此策略的主机。\", -1 /* HOISTED */))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_createElementVNode", "_hoisted_1", "onClick", "_cache", "args", "$options", "showNewPolicyModal", "_createVNode", "_component_font_awesome_icon", "icon", "_hoisted_2", "_createCommentVNode", "_Fragment", "_renderList", "_ctx", "policies", "policy", "key", "id", "_hoisted_3", "_hoisted_4", "_toDisplayString", "name", "_hoisted_5", "description", "_hoisted_6", "hostsCount", "_hoisted_7", "_hoisted_8", "_hoisted_9", "<PERSON><PERSON><PERSON><PERSON>", "_hoisted_10", "_hoisted_11", "expiryDays", "_hoisted_12", "$event", "editPolicy", "_hoisted_13", "confirmDeletePolicy", "_hoisted_14", "_component_BaseModal", "modelValue", "$data", "policyModal", "show", "title", "isEdit", "onConfirm", "savePolicyChanges", "loading", "processing", "default", "_withCtx", "_hoisted_15", "type", "form", "placeholder", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "min", "number", "_hoisted_20", "requireUppercase", "_hoisted_21", "requireLowercase", "_hoisted_22", "requireNumbers", "_hoisted_23", "requireSpecial", "_hoisted_24", "forbidUsername", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "historyCount", "_", "deleteModal", "danger", "deletePolicy", "_createTextVNode", "policyName"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\PasswordPolicies.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"flex justify-between items-center mb-6\">\r\n      <h2 class=\"text-lg font-semibold\">密码策略管理</h2>\r\n      <button class=\"btn-primary\" @click=\"showNewPolicyModal\">\r\n        <font-awesome-icon :icon=\"['fas', 'plus']\" class=\"mr-2\" />\r\n        <span>新建策略</span>\r\n      </button>\r\n    </div>\r\n    \r\n    <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n      <!-- 策略卡片 -->\r\n      <div v-for=\"policy in policies\" :key=\"policy.id\" class=\"card\">\r\n        <div class=\"flex justify-between items-start mb-2\">\r\n          <div>\r\n            <h3 class=\"text-lg font-medium\">{{ policy.name }}</h3>\r\n            <p class=\"text-sm text-gray-500\">{{ policy.description }}</p>\r\n          </div>\r\n          <div class=\"text-sm text-gray-500\">{{ policy.hostsCount }} 台主机</div>\r\n        </div>\r\n        <div class=\"grid grid-cols-2 gap-4 mb-4\">\r\n          <div class=\"flex items-center\">\r\n            <font-awesome-icon :icon=\"['fas', 'lock']\" class=\"text-gray-400 mr-2\" />\r\n            <span class=\"text-sm\">最小长度: {{ policy.minLength }}</span>\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <font-awesome-icon :icon=\"['fas', 'calendar-alt']\" class=\"text-gray-400 mr-2\" />\r\n            <span class=\"text-sm\">过期时间: {{ policy.expiryDays }} 天</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"flex justify-end space-x-3\">\r\n          <button \r\n            class=\"text-gray-600 hover:text-gray-800 text-sm\"\r\n            @click=\"editPolicy(policy)\"\r\n          >\r\n            编辑\r\n          </button>\r\n          <button \r\n            class=\"text-red-600 hover:text-red-800 text-sm\"\r\n            @click=\"confirmDeletePolicy(policy)\"\r\n          >\r\n            删除\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 新建/编辑策略弹窗 -->\r\n    <BaseModal\r\n      v-model=\"policyModal.show\"\r\n      :title=\"policyModal.isEdit ? '编辑密码策略' : '新建密码策略'\"\r\n      :confirm-text=\"policyModal.isEdit ? '保存更改' : '创建策略'\"\r\n      @confirm=\"savePolicyChanges\"\r\n      :loading=\"processing\"\r\n    >\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">策略名称</label>\r\n        <input \r\n          type=\"text\" \r\n          v-model=\"policyModal.form.name\" \r\n          class=\"form-control\" \r\n          placeholder=\"输入策略名称\"\r\n        >\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">策略描述</label>\r\n        <input \r\n          type=\"text\" \r\n          v-model=\"policyModal.form.description\" \r\n          class=\"form-control\" \r\n          placeholder=\"适用场景描述\"\r\n        >\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码规则</label>\r\n        <div class=\"space-y-3\">\r\n          <div class=\"flex items-center\">\r\n            <span class=\"w-32 text-sm\">最小长度:</span>\r\n            <input \r\n              type=\"number\" \r\n              min=\"8\" \r\n              v-model.number=\"policyModal.form.minLength\" \r\n              class=\"w-20 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\"\r\n            >\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <span class=\"w-32 text-sm\">必须包含大写字母:</span>\r\n            <input type=\"checkbox\" v-model=\"policyModal.form.requireUppercase\">\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <span class=\"w-32 text-sm\">必须包含小写字母:</span>\r\n            <input type=\"checkbox\" v-model=\"policyModal.form.requireLowercase\">\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <span class=\"w-32 text-sm\">必须包含数字:</span>\r\n            <input type=\"checkbox\" v-model=\"policyModal.form.requireNumbers\">\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <span class=\"w-32 text-sm\">必须包含特殊字符:</span>\r\n            <input type=\"checkbox\" v-model=\"policyModal.form.requireSpecial\">\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <span class=\"w-32 text-sm\">不能包含用户名:</span>\r\n            <input type=\"checkbox\" v-model=\"policyModal.form.forbidUsername\">\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码有效期</label>\r\n        <div class=\"flex items-center\">\r\n          <input \r\n            type=\"number\" \r\n            min=\"1\" \r\n            v-model.number=\"policyModal.form.expiryDays\"\r\n            class=\"w-20 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\"\r\n          >\r\n          <span class=\"ml-2\">天</span>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码历史</label>\r\n        <div class=\"flex items-center\">\r\n          <span class=\"text-sm\">不能重复使用最近</span>\r\n          <input \r\n            type=\"number\" \r\n            min=\"1\" \r\n            v-model.number=\"policyModal.form.historyCount\"\r\n            class=\"mx-2 w-16 px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\"\r\n          >\r\n          <span class=\"text-sm\">次使用过的密码</span>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 删除确认弹窗 -->\r\n    <BaseModal\r\n      v-model=\"deleteModal.show\"\r\n      title=\"确认删除策略\"\r\n      confirm-text=\"删除\"\r\n      danger\r\n      @confirm=\"deletePolicy\"\r\n      :loading=\"processing\"\r\n    >\r\n      <p>您确定要删除策略 <strong>{{ deleteModal.policyName }}</strong> 吗？</p>\r\n      <p class=\"mt-2 text-red-600\">此操作无法撤销，删除后将影响所有使用此策略的主机。</p>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\n\r\nexport default {\r\n  name: 'PasswordPolicies',\r\n  components: {\r\n    BaseModal\r\n  },\r\n  data() {\r\n    return {\r\n      processing: false,\r\n      \r\n      // 新建/编辑策略弹窗\r\n      policyModal: {\r\n        show: false,\r\n        isEdit: false,\r\n        policyId: null,\r\n        form: {\r\n          name: '',\r\n          description: '',\r\n          minLength: 12,\r\n          expiryDays: 30,\r\n          requireUppercase: true,\r\n          requireLowercase: true,\r\n          requireNumbers: true,\r\n          requireSpecial: true,\r\n          forbidUsername: true,\r\n          historyCount: 5\r\n        }\r\n      },\r\n      \r\n      // 删除确认弹窗\r\n      deleteModal: {\r\n        show: false,\r\n        policyId: null,\r\n        policyName: ''\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      policies: state => state.policies\r\n    })\r\n  },\r\n  methods: {\r\n    showNewPolicyModal() {\r\n      this.policyModal.isEdit = false\r\n      this.policyModal.policyId = null\r\n      this.resetPolicyForm()\r\n      this.policyModal.show = true\r\n    },\r\n    \r\n    editPolicy(policy) {\r\n      this.policyModal.isEdit = true\r\n      this.policyModal.policyId = policy.id\r\n      this.policyModal.form = { ...policy }\r\n      this.policyModal.show = true\r\n    },\r\n    \r\n    confirmDeletePolicy(policy) {\r\n      this.deleteModal.policyId = policy.id\r\n      this.deleteModal.policyName = policy.name\r\n      this.deleteModal.show = true\r\n    },\r\n    \r\n    resetPolicyForm() {\r\n      this.policyModal.form = {\r\n        name: '',\r\n        description: '',\r\n        minLength: 12,\r\n        expiryDays: 30,\r\n        requireUppercase: true,\r\n        requireLowercase: true,\r\n        requireNumbers: true,\r\n        requireSpecial: true,\r\n        forbidUsername: true,\r\n        historyCount: 5\r\n      }\r\n    },\r\n    \r\n    validatePolicyForm() {\r\n      if (!this.policyModal.form.name) {\r\n        alert('请输入策略名称')\r\n        return false\r\n      }\r\n      \r\n      if (this.policyModal.form.minLength < 8) {\r\n        alert('密码最小长度不能小于8')\r\n        return false\r\n      }\r\n      \r\n      return true\r\n    },\r\n    \r\n    async savePolicyChanges() {\r\n      if (!this.validatePolicyForm()) {\r\n        return\r\n      }\r\n      \r\n      this.processing = true\r\n      \r\n      try {\r\n        if (this.policyModal.isEdit) {\r\n          this.$store.commit('updatePolicy', {\r\n            id: this.policyModal.policyId,\r\n            ...this.policyModal.form\r\n          })\r\n        } else {\r\n          this.$store.commit('addPolicy', { ...this.policyModal.form })\r\n        }\r\n        \r\n        this.policyModal.show = false\r\n        \r\n        // 提示用户操作成功\r\n        alert(`策略${this.policyModal.isEdit ? '更新' : '创建'}成功！`)\r\n      } catch (error) {\r\n        console.error('保存策略失败', error)\r\n        alert('保存策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n    \r\n    async deletePolicy() {\r\n      this.processing = true\r\n      \r\n      try {\r\n        this.$store.commit('deletePolicy', this.deleteModal.policyId)\r\n        this.deleteModal.show = false\r\n        \r\n        // 提示用户操作成功\r\n        alert('策略删除成功！')\r\n      } catch (error) {\r\n        console.error('删除策略失败', error)\r\n        alert('删除策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script> "], "mappings": ";;EAESA,KAAK,EAAC;AAAwC;;EAQ9CA,KAAK,EAAC;AAAuC;;EAGzCA,KAAK,EAAC;AAAuC;;EAE1CA,KAAK,EAAC;AAAqB;;EAC5BA,KAAK,EAAC;AAAuB;;EAE7BA,KAAK,EAAC;AAAuB;;EAE/BA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAAmB;;EAEtBA,KAAK,EAAC;AAAS;;EAElBA,KAAK,EAAC;AAAmB;;EAEtBA,KAAK,EAAC;AAAS;;EAGpBA,KAAK,EAAC;AAA4B;oBA9B/C;oBAAA;;EAuDWA,KAAK,EAAC;AAAY;;EAUlBA,KAAK,EAAC;AAAY;;EAUlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmB;;EASzBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EAO7BA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;;EAW3BA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;;;;uBA5HpCC,mBAAA,CAqJM,cApJJC,mBAAA,CAMM,OANNC,UAMM,G,4BALJD,mBAAA,CAA6C;IAAzCF,KAAK,EAAC;EAAuB,GAAC,QAAM,sBACxCE,mBAAA,CAGS;IAHDF,KAAK,EAAC,aAAa;IAAEI,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,kBAAA,IAAAD,QAAA,CAAAC,kBAAA,IAAAF,IAAA,CAAkB;MACpDG,YAAA,CAA0DC,4BAAA;IAAtCC,IAAI,EAAE,eAAe;IAAEX,KAAK,EAAC;kCACjDE,mBAAA,CAAiB,cAAX,MAAI,qB,KAIdA,mBAAA,CAmCM,OAnCNU,UAmCM,GAlCJC,mBAAA,UAAa,G,kBACbZ,mBAAA,CAgCMa,SAAA,QA5CZC,WAAA,CAY4BC,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;yBAAlBjB,mBAAA,CAgCM;MAhC2BkB,GAAG,EAAED,MAAM,CAACE,EAAE;MAAEpB,KAAK,EAAC;QACrDE,mBAAA,CAMM,OANNmB,UAMM,GALJnB,mBAAA,CAGM,cAFJA,mBAAA,CAAsD,MAAtDoB,UAAsD,EAAAC,gBAAA,CAAnBL,MAAM,CAACM,IAAI,kBAC9CtB,mBAAA,CAA6D,KAA7DuB,UAA6D,EAAAF,gBAAA,CAAzBL,MAAM,CAACQ,WAAW,iB,GAExDxB,mBAAA,CAAoE,OAApEyB,UAAoE,EAAAJ,gBAAA,CAA9BL,MAAM,CAACU,UAAU,IAAG,MAAI,gB,GAEhE1B,mBAAA,CASM,OATN2B,UASM,GARJ3B,mBAAA,CAGM,OAHN4B,UAGM,GAFJrB,YAAA,CAAwEC,4BAAA;MAApDC,IAAI,EAAE,eAAe;MAAEX,KAAK,EAAC;QACjDE,mBAAA,CAAyD,QAAzD6B,UAAyD,EAAnC,QAAM,GAAAR,gBAAA,CAAGL,MAAM,CAACc,SAAS,iB,GAEjD9B,mBAAA,CAGM,OAHN+B,WAGM,GAFJxB,YAAA,CAAgFC,4BAAA;MAA5DC,IAAI,EAAE,uBAAuB;MAAEX,KAAK,EAAC;QACzDE,mBAAA,CAA4D,QAA5DgC,WAA4D,EAAtC,QAAM,GAAAX,gBAAA,CAAGL,MAAM,CAACiB,UAAU,IAAG,IAAE,gB,KAGzDjC,mBAAA,CAaM,OAbNkC,WAaM,GAZJlC,mBAAA,CAKS;MAJPF,KAAK,EAAC,2CAA2C;MAChDI,OAAK,EAAAiC,MAAA,IAAE9B,QAAA,CAAA+B,UAAU,CAACpB,MAAM;OAC1B,MAED,iBApCVqB,WAAA,GAqCUrC,mBAAA,CAKS;MAJPF,KAAK,EAAC,yCAAyC;MAC9CI,OAAK,EAAAiC,MAAA,IAAE9B,QAAA,CAAAiC,mBAAmB,CAACtB,MAAM;OACnC,MAED,iBA1CVuB,WAAA,E;oCA+CI5B,mBAAA,eAAkB,EAClBJ,YAAA,CAwFYiC,oBAAA;IAxIhBC,UAAA,EAiDeC,KAAA,CAAAC,WAAW,CAACC,IAAI;IAjD/B,uBAAAzC,MAAA,SAAAA,MAAA,OAAAgC,MAAA,IAiDeO,KAAA,CAAAC,WAAW,CAACC,IAAI,GAAAT,MAAA;IACxBU,KAAK,EAAEH,KAAA,CAAAC,WAAW,CAACG,MAAM;IACzB,cAAY,EAAEJ,KAAA,CAAAC,WAAW,CAACG,MAAM;IAChCC,SAAO,EAAE1C,QAAA,CAAA2C,iBAAiB;IAC1BC,OAAO,EAAEP,KAAA,CAAAQ;;IArDhBC,OAAA,EAAAC,QAAA,CAuDM,MAQM,CARNpD,mBAAA,CAQM,OARNqD,WAQM,G,4BAPJrD,mBAAA,CAAsC;MAA/BF,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BE,mBAAA,CAKC;MAJCsD,IAAI,EAAC,MAAM;MA1DrB,uBAAAnD,MAAA,QAAAA,MAAA,MAAAgC,MAAA,IA2DmBO,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACjC,IAAI,GAAAa,MAAA;MAC9BrC,KAAK,EAAC,cAAc;MACpB0D,WAAW,EAAC;mDAFHd,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACjC,IAAI,E,KAMlCtB,mBAAA,CAQM,OARNyD,WAQM,G,4BAPJzD,mBAAA,CAAsC;MAA/BF,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BE,mBAAA,CAKC;MAJCsD,IAAI,EAAC,MAAM;MApErB,uBAAAnD,MAAA,QAAAA,MAAA,MAAAgC,MAAA,IAqEmBO,KAAA,CAAAC,WAAW,CAACY,IAAI,CAAC/B,WAAW,GAAAW,MAAA;MACrCrC,KAAK,EAAC,cAAc;MACpB0D,WAAW,EAAC;mDAFHd,KAAA,CAAAC,WAAW,CAACY,IAAI,CAAC/B,WAAW,E,KAMzCxB,mBAAA,CAiCM,OAjCN0D,WAiCM,G,4BAhCJ1D,mBAAA,CAAsC;MAA/BF,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BE,mBAAA,CA8BM,OA9BN2D,WA8BM,GA7BJ3D,mBAAA,CAQM,OARN4D,WAQM,G,4BAPJ5D,mBAAA,CAAuC;MAAjCF,KAAK,EAAC;IAAc,GAAC,OAAK,sB,gBAChCE,mBAAA,CAKC;MAJCsD,IAAI,EAAC,QAAQ;MACbO,GAAG,EAAC,GAAG;MAlFrB,uBAAA1D,MAAA,QAAAA,MAAA,MAAAgC,MAAA,IAmF8BO,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACzB,SAAS,GAAAK,MAAA;MAC1CrC,KAAK,EAAC;mDADU4C,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACzB,SAAS,E;MAAlCgC,MAAM,EAAd;IAA2C,E,MAI/C9D,mBAAA,CAGM,OAHN+D,WAGM,G,4BAFJ/D,mBAAA,CAA2C;MAArCF,KAAK,EAAC;IAAc,GAAC,WAAS,sB,gBACpCE,mBAAA,CAAmE;MAA5DsD,IAAI,EAAC,UAAU;MAzFlC,uBAAAnD,MAAA,QAAAA,MAAA,MAAAgC,MAAA,IAyF4CO,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACS,gBAAgB,GAAA7B,MAAA;uDAAjCO,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACS,gBAAgB,E,KAEnEhE,mBAAA,CAGM,OAHNiE,WAGM,G,4BAFJjE,mBAAA,CAA2C;MAArCF,KAAK,EAAC;IAAc,GAAC,WAAS,sB,gBACpCE,mBAAA,CAAmE;MAA5DsD,IAAI,EAAC,UAAU;MA7FlC,uBAAAnD,MAAA,QAAAA,MAAA,MAAAgC,MAAA,IA6F4CO,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACW,gBAAgB,GAAA/B,MAAA;uDAAjCO,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACW,gBAAgB,E,KAEnElE,mBAAA,CAGM,OAHNmE,WAGM,G,4BAFJnE,mBAAA,CAAyC;MAAnCF,KAAK,EAAC;IAAc,GAAC,SAAO,sB,gBAClCE,mBAAA,CAAiE;MAA1DsD,IAAI,EAAC,UAAU;MAjGlC,uBAAAnD,MAAA,QAAAA,MAAA,MAAAgC,MAAA,IAiG4CO,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACa,cAAc,GAAAjC,MAAA;uDAA/BO,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACa,cAAc,E,KAEjEpE,mBAAA,CAGM,OAHNqE,WAGM,G,4BAFJrE,mBAAA,CAA2C;MAArCF,KAAK,EAAC;IAAc,GAAC,WAAS,sB,gBACpCE,mBAAA,CAAiE;MAA1DsD,IAAI,EAAC,UAAU;MArGlC,uBAAAnD,MAAA,QAAAA,MAAA,MAAAgC,MAAA,IAqG4CO,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACe,cAAc,GAAAnC,MAAA;uDAA/BO,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACe,cAAc,E,KAEjEtE,mBAAA,CAGM,OAHNuE,WAGM,G,4BAFJvE,mBAAA,CAA0C;MAApCF,KAAK,EAAC;IAAc,GAAC,UAAQ,sB,gBACnCE,mBAAA,CAAiE;MAA1DsD,IAAI,EAAC,UAAU;MAzGlC,uBAAAnD,MAAA,QAAAA,MAAA,MAAAgC,MAAA,IAyG4CO,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACiB,cAAc,GAAArC,MAAA;uDAA/BO,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACiB,cAAc,E,SAKrExE,mBAAA,CAWM,OAXNyE,WAWM,G,4BAVJzE,mBAAA,CAAuC;MAAhCF,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BE,mBAAA,CAQM,OARN0E,WAQM,G,gBAPJ1E,mBAAA,CAKC;MAJCsD,IAAI,EAAC,QAAQ;MACbO,GAAG,EAAC,GAAG;MAnHnB,uBAAA1D,MAAA,QAAAA,MAAA,MAAAgC,MAAA,IAoH4BO,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACtB,UAAU,GAAAE,MAAA;MAC3CrC,KAAK,EAAC;mDADU4C,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACtB,UAAU,E;MAAnC6B,MAAM,EAAd;IAA4C,E,gCAG9C9D,mBAAA,CAA2B;MAArBF,KAAK,EAAC;IAAM,GAAC,GAAC,qB,KAIxBE,mBAAA,CAYM,OAZN2E,WAYM,G,4BAXJ3E,mBAAA,CAAsC;MAA/BF,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BE,mBAAA,CASM,OATN4E,WASM,G,4BARJ5E,mBAAA,CAAqC;MAA/BF,KAAK,EAAC;IAAS,GAAC,UAAQ,sB,gBAC9BE,mBAAA,CAKC;MAJCsD,IAAI,EAAC,QAAQ;MACbO,GAAG,EAAC,GAAG;MAjInB,uBAAA1D,MAAA,SAAAA,MAAA,OAAAgC,MAAA,IAkI4BO,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACsB,YAAY,GAAA1C,MAAA;MAC7CrC,KAAK,EAAC;mDADU4C,KAAA,CAAAC,WAAW,CAACY,IAAI,CAACsB,YAAY,E;MAArCf,MAAM,EAAd;IAA8C,E,gCAGhD9D,mBAAA,CAAoC;MAA9BF,KAAK,EAAC;IAAS,GAAC,SAAO,qB;IArIvCgF,CAAA;sFA0IInE,mBAAA,YAAe,EACfJ,YAAA,CAUYiC,oBAAA;IArJhBC,UAAA,EA4IeC,KAAA,CAAAqC,WAAW,CAACnC,IAAI;IA5I/B,uBAAAzC,MAAA,SAAAA,MAAA,OAAAgC,MAAA,IA4IeO,KAAA,CAAAqC,WAAW,CAACnC,IAAI,GAAAT,MAAA;IACzBU,KAAK,EAAC,QAAQ;IACd,cAAY,EAAC,IAAI;IACjBmC,MAAM,EAAN,EAAM;IACLjC,SAAO,EAAE1C,QAAA,CAAA4E,YAAY;IACrBhC,OAAO,EAAEP,KAAA,CAAAQ;;IAjJhBC,OAAA,EAAAC,QAAA,CAmJM,MAAgE,CAAhEpD,mBAAA,CAAgE,Y,4BAnJtEkF,gBAAA,CAmJS,WAAS,IAAAlF,mBAAA,CAA6C,gBAAAqB,gBAAA,CAAlCqB,KAAA,CAAAqC,WAAW,CAACI,UAAU,kB,4BAnJnDD,gBAAA,CAmJ+D,KAAG,G,+BAC5DlF,mBAAA,CAA0D;MAAvDF,KAAK,EAAC;IAAmB,GAAC,2BAAyB,qB;IApJ5DgF,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}