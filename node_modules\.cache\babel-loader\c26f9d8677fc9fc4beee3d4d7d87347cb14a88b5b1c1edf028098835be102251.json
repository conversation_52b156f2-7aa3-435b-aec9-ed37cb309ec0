{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, vModelText as _vModelText, withDirectives as _withDirectives, vModelSelect as _vModelSelect, toDisplayString as _toDisplayString, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, vModelDynamic as _vModelDynamic, vModelCheckbox as _vModelCheckbox, createBlock as _createBlock, vModelRadio as _vModelRadio } from \"vue\";\nconst _hoisted_1 = {\n  class: \"bg-white shadow rounded-lg p-4 mb-6\"\n};\nconst _hoisted_2 = {\n  class: \"flex flex-wrap items-center justify-between\"\n};\nconst _hoisted_3 = {\n  class: \"flex space-x-3 mb-2 sm:mb-0\"\n};\nconst _hoisted_4 = {\n  class: \"flex items-center space-x-4\"\n};\nconst _hoisted_5 = {\n  class: \"flex items-center border rounded-md overflow-hidden\"\n};\nconst _hoisted_6 = {\n  class: \"relative\"\n};\nconst _hoisted_7 = {\n  class: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n};\nconst _hoisted_8 = {\n  class: \"relative\"\n};\nconst _hoisted_9 = {\n  class: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n};\nconst _hoisted_10 = {\n  key: 0,\n  class: \"bg-white rounded-lg shadow overflow-hidden\"\n};\nconst _hoisted_11 = {\n  class: \"px-4 py-3 bg-gray-50 border-b flex justify-between items-center\"\n};\nconst _hoisted_12 = {\n  class: \"text-sm text-gray-700\"\n};\nconst _hoisted_13 = {\n  class: \"font-medium\"\n};\nconst _hoisted_14 = {\n  class: \"font-medium\"\n};\nconst _hoisted_15 = {\n  class: \"flex space-x-2\"\n};\nconst _hoisted_16 = {\n  class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n};\nconst _hoisted_17 = {\n  class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n};\nconst _hoisted_18 = {\n  class: \"min-w-full divide-y divide-gray-200\"\n};\nconst _hoisted_19 = {\n  class: \"bg-gray-50\"\n};\nconst _hoisted_20 = {\n  scope: \"col\",\n  class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n};\nconst _hoisted_21 = {\n  class: \"bg-white divide-y divide-gray-200\"\n};\nconst _hoisted_22 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_23 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_24 = {\n  class: \"ml-2 font-medium text-gray-900\"\n};\nconst _hoisted_25 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_26 = {\n  class: \"text-sm text-gray-900\"\n};\nconst _hoisted_27 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_28 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_29 = {\n  class: \"text-sm font-medium text-gray-900\"\n};\nconst _hoisted_30 = {\n  key: 0,\n  class: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\"\n};\nconst _hoisted_31 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_32 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_33 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_34 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_35 = {\n  key: 1,\n  class: \"ml-1\"\n};\nconst _hoisted_36 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_37 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_38 = {\n  class: \"flex-grow\"\n};\nconst _hoisted_39 = [\"type\", \"value\"];\nconst _hoisted_40 = [\"onClick\"];\nconst _hoisted_41 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_42 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_43 = {\n  class: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\"\n};\nconst _hoisted_44 = {\n  class: \"flex space-x-2\"\n};\nconst _hoisted_45 = [\"onClick\"];\nconst _hoisted_46 = [\"onClick\"];\nconst _hoisted_47 = {\n  key: 0\n};\nconst _hoisted_48 = {\n  colspan: \"8\",\n  class: \"px-6 py-10 text-center\"\n};\nconst _hoisted_49 = {\n  class: \"text-gray-500\"\n};\nconst _hoisted_50 = {\n  class: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\"\n};\nconst _hoisted_51 = {\n  class: \"px-4 py-5 sm:p-6\"\n};\nconst _hoisted_52 = {\n  class: \"flex justify-between items-start mb-4\"\n};\nconst _hoisted_53 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_54 = {\n  class: \"text-lg font-medium text-gray-900\"\n};\nconst _hoisted_55 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_56 = {\n  class: \"space-y-4\"\n};\nconst _hoisted_57 = {\n  class: \"flex justify-between items-center mb-2\"\n};\nconst _hoisted_58 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_59 = {\n  class: \"text-sm font-medium text-gray-900\"\n};\nconst _hoisted_60 = {\n  key: 0,\n  class: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\"\n};\nconst _hoisted_61 = [\"onClick\"];\nconst _hoisted_62 = {\n  class: \"mb-2\"\n};\nconst _hoisted_63 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_64 = [\"type\", \"value\"];\nconst _hoisted_65 = [\"onClick\"];\nconst _hoisted_66 = {\n  class: \"grid grid-cols-2 gap-2 text-xs\"\n};\nconst _hoisted_67 = {\n  class: \"text-gray-900\"\n};\nconst _hoisted_68 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_69 = {\n  class: \"col-span-2 mt-1\"\n};\nconst _hoisted_70 = {\n  class: \"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800\"\n};\nconst _hoisted_71 = {\n  class: \"mt-4 flex justify-center\"\n};\nconst _hoisted_72 = [\"onClick\"];\nconst _hoisted_73 = {\n  class: \"mb-4\"\n};\nconst _hoisted_74 = {\n  class: \"px-3 py-2 bg-gray-50 rounded-md\"\n};\nconst _hoisted_75 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_76 = {\n  class: \"flex space-x-3\"\n};\nconst _hoisted_77 = {\n  key: 0,\n  class: \"form-group mb-4\"\n};\nconst _hoisted_78 = [\"value\"];\nconst _hoisted_79 = {\n  class: \"mt-3\"\n};\nconst _hoisted_80 = {\n  class: \"flex justify-between mb-1\"\n};\nconst _hoisted_81 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_82 = [\"type\"];\nconst _hoisted_83 = {\n  key: 1,\n  class: \"space-y-4\"\n};\nconst _hoisted_84 = {\n  class: \"form-group\"\n};\nconst _hoisted_85 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_86 = [\"type\"];\nconst _hoisted_87 = {\n  class: \"form-group\"\n};\nconst _hoisted_88 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_89 = [\"type\"];\nconst _hoisted_90 = {\n  key: 0,\n  class: \"text-sm text-red-500 mt-1\"\n};\nconst _hoisted_91 = {\n  class: \"space-y-2 mt-4\"\n};\nconst _hoisted_92 = {\n  class: \"mb-4\"\n};\nconst _hoisted_93 = {\n  class: \"px-3 py-2 bg-gray-50 rounded-md\"\n};\nconst _hoisted_94 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_95 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_96 = {\n  class: \"relative inline-block w-10 mr-2 align-middle select-none\"\n};\nconst _hoisted_97 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_98 = [\"value\"];\nconst _hoisted_99 = {\n  class: \"form-group\"\n};\nconst _hoisted_100 = {\n  class: \"flex justify-between mb-1\"\n};\nconst _hoisted_101 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_102 = [\"type\"];\nconst _hoisted_103 = {\n  class: \"form-group\"\n};\nconst _hoisted_104 = {\n  class: \"mb-2\"\n};\nconst _hoisted_105 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_106 = {\n  class: \"form-text\"\n};\nconst _hoisted_107 = {\n  class: \"form-group\"\n};\nconst _hoisted_108 = [\"value\"];\nconst _hoisted_109 = {\n  class: \"form-group\"\n};\nconst _hoisted_110 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_111 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_112 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_113 = {\n  key: 0,\n  class: \"mt-3\"\n};\nconst _hoisted_114 = {\n  class: \"grid grid-cols-2 gap-4\"\n};\nconst _hoisted_115 = {\n  class: \"form-group\"\n};\nconst _hoisted_116 = {\n  class: \"form-group\"\n};\nconst _hoisted_117 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_118 = {\n  class: \"form-group\"\n};\nconst _hoisted_119 = [\"value\"];\nconst _hoisted_120 = {\n  class: \"form-group\"\n};\nconst _hoisted_121 = {\n  class: \"form-group\"\n};\nconst _hoisted_122 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_123 = {\n  class: \"form-group\"\n};\nconst _hoisted_124 = [\"value\"];\nconst _hoisted_125 = {\n  class: \"form-group\"\n};\nconst _hoisted_126 = {\n  class: \"form-group\"\n};\nconst _hoisted_127 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_128 = {\n  class: \"mb-2\"\n};\nconst _hoisted_129 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_130 = {\n  class: \"form-text\"\n};\nconst _hoisted_131 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_132 = {\n  class: \"p-4 border border-gray-200 rounded-md\"\n};\nconst _hoisted_133 = {\n  class: \"mb-3\"\n};\nconst _hoisted_134 = {\n  class: \"mb-3\"\n};\nconst _hoisted_135 = {\n  class: \"flex items-center mb-3\"\n};\nconst _hoisted_136 = {\n  class: \"inline-flex items-center\"\n};\nconst _hoisted_137 = {\n  class: \"mb-3\"\n};\nconst _hoisted_138 = {\n  class: \"flex space-x-3\"\n};\nconst _hoisted_139 = {\n  key: 0,\n  class: \"mb-3\"\n};\nconst _hoisted_140 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_141 = [\"type\"];\nconst _hoisted_142 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_143 = [\"value\"];\nconst _hoisted_144 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_145 = {\n  class: \"space-y-2\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_CustomCheckbox = _resolveComponent(\"CustomCheckbox\");\n  const _component_StatusBadge = _resolveComponent(\"StatusBadge\");\n  const _component_PasswordStrengthMeter = _resolveComponent(\"PasswordStrengthMeter\");\n  const _component_BaseModal = _resolveComponent(\"BaseModal\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createCommentVNode(\" 操作按钮 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.showEmergencyReset && $options.showEmergencyReset(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'exclamation-triangle'],\n    class: \"mr-2\"\n  }), _cache[66] || (_cache[66] = _createElementVNode(\"span\", null, \"紧急重置\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.openBatchUpdateModal && $options.openBatchUpdateModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'key'],\n    class: \"mr-2\"\n  }), _cache[67] || (_cache[67] = _createElementVNode(\"span\", null, \"批量更新密码\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.openBatchApplyModal && $options.openBatchApplyModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"mr-2\"\n  }), _cache[68] || (_cache[68] = _createElementVNode(\"span\", null, \"批量应用策略\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n    onClick: _cache[3] || (_cache[3] = (...args) => $options.openBatchAddAccountModal && $options.openBatchAddAccountModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'users'],\n    class: \"mr-2\"\n  }), _cache[69] || (_cache[69] = _createElementVNode(\"span\", null, \"批量添加账号\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" 视图切换 \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1 focus:outline-none\", {\n      'bg-blue-500 text-white': $data.viewMode === 'table',\n      'bg-gray-100 text-gray-600': $data.viewMode !== 'table'\n    }]),\n    onClick: _cache[4] || (_cache[4] = $event => $data.viewMode = 'table')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'table'],\n    class: \"mr-1\"\n  }), _cache[70] || (_cache[70] = _createTextVNode(\" 表格 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1 focus:outline-none\", {\n      'bg-blue-500 text-white': $data.viewMode === 'card',\n      'bg-gray-100 text-gray-600': $data.viewMode !== 'card'\n    }]),\n    onClick: _cache[5] || (_cache[5] = $event => $data.viewMode = 'card')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'th-large'],\n    class: \"mr-1\"\n  }), _cache[71] || (_cache[71] = _createTextVNode(\" 卡片 \"))], 2 /* CLASS */)]), _createCommentVNode(\" 筛选 \"), _createElementVNode(\"div\", _hoisted_6, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.filterText = $event),\n    placeholder: \"筛选主机...\",\n    class: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.filterText]]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"text-gray-400\"\n  })])]), _createCommentVNode(\" 账号筛选 \"), _createElementVNode(\"div\", _hoisted_8, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.accountFilterText = $event),\n    placeholder: \"筛选账号...\",\n    class: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.accountFilterText]]), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'user'],\n    class: \"text-gray-400\"\n  })])]), _createCommentVNode(\" 状态筛选 \"), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.statusFilter = $event),\n    class: \"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n  }, _cache[72] || (_cache[72] = [_createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有状态\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"normal\"\n  }, \"正常\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"warning\"\n  }, \"警告\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"error\"\n  }, \"错误\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.statusFilter]]), _createCommentVNode(\" 显示密码过期选项 \"), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.expiryFilter = $event),\n    class: \"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n  }, _cache[73] || (_cache[73] = [_createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有密码\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"expired\"\n  }, \"已过期\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"expiring-soon\"\n  }, \"即将过期\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"valid\"\n  }, \"有效期内\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.expiryFilter]])])])]), _createCommentVNode(\" 主机列表 \"), _createCommentVNode(\" 表格视图 \"), $data.viewMode === 'table' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createCommentVNode(\" 账号计数和导出按钮 \"), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_cache[74] || (_cache[74] = _createTextVNode(\" 显示 \")), _createElementVNode(\"span\", _hoisted_13, _toDisplayString($options.filteredAccounts.length), 1 /* TEXT */), _cache[75] || (_cache[75] = _createTextVNode(\" 个账号 (共 \")), _createElementVNode(\"span\", _hoisted_14, _toDisplayString($options.getAllAccounts.length), 1 /* TEXT */), _cache[76] || (_cache[76] = _createTextVNode(\" 个) \"))]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"button\", _hoisted_16, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'file-export'],\n    class: \"mr-1\"\n  }), _cache[77] || (_cache[77] = _createTextVNode(\" 导出 \"))]), _createElementVNode(\"button\", _hoisted_17, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'print'],\n    class: \"mr-1\"\n  }), _cache[78] || (_cache[78] = _createTextVNode(\" 打印 \"))])])]), _createElementVNode(\"table\", _hoisted_18, [_createElementVNode(\"thead\", _hoisted_19, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", _hoisted_20, [_createVNode(_component_CustomCheckbox, {\n    modelValue: $data.selectAll,\n    \"onUpdate:modelValue\": [_cache[10] || (_cache[10] = $event => $data.selectAll = $event), $options.toggleSelectAll]\n  }, {\n    default: _withCtx(() => _cache[79] || (_cache[79] = [_createTextVNode(\" 主机名 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _cache[80] || (_cache[80] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" IP地址 \", -1 /* HOISTED */)), _cache[81] || (_cache[81] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 账号 \", -1 /* HOISTED */)), _cache[82] || (_cache[82] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 最后密码修改时间 \", -1 /* HOISTED */)), _cache[83] || (_cache[83] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码过期时间 \", -1 /* HOISTED */)), _cache[84] || (_cache[84] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码 \", -1 /* HOISTED */)), _cache[85] || (_cache[85] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 状态 \", -1 /* HOISTED */)), _cache[86] || (_cache[86] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 策略 \", -1 /* HOISTED */)), _cache[87] || (_cache[87] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 操作 \", -1 /* HOISTED */))])]), _createElementVNode(\"tbody\", _hoisted_21, [_createCommentVNode(\" 按主机分组显示 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.groupedAccounts, hostGroup => {\n    return _openBlock(), _createElementBlock(_Fragment, {\n      key: hostGroup.hostId\n    }, [_createCommentVNode(\" 账号行 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(hostGroup.accounts, (account, accountIndex) => {\n      return _openBlock(), _createElementBlock(\"tr\", {\n        key: account.id,\n        class: _normalizeClass({\n          'bg-gray-50': accountIndex % 2 === 0,\n          'hover:bg-blue-50': true\n        })\n      }, [_createElementVNode(\"td\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_createVNode(_component_CustomCheckbox, {\n        modelValue: account.host.selected,\n        \"onUpdate:modelValue\": $event => account.host.selected = $event,\n        class: \"ml-4\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"span\", _hoisted_24, _toDisplayString(account.host.name), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])])]), _createElementVNode(\"td\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, _toDisplayString(account.host.ip), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"span\", _hoisted_29, _toDisplayString(account.username), 1 /* TEXT */), account.isDefault ? (_openBlock(), _createElementBlock(\"span\", _hoisted_30, \" 默认 \")) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"td\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, _toDisplayString(account.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_33, [_createElementVNode(\"div\", {\n        class: _normalizeClass({\n          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\n          'bg-red-100 text-red-800': $options.isPasswordExpired(account).status === 'danger' || $options.isPasswordExpired(account).status === 'expired',\n          'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(account).status === 'warning',\n          'bg-gray-100 text-gray-800': $options.isPasswordExpired(account).status === 'normal'\n        })\n      }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(account).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(account).status === 'expired' || $options.isPasswordExpired(account).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_34, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-triangle']\n      })])) : $options.isPasswordExpired(account).status === 'warning' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_35, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-circle']\n      })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]), _createElementVNode(\"td\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"input\", {\n        type: $data.passwordVisibility[account.id] ? 'text' : 'password',\n        value: account.password,\n        readonly: \"\",\n        class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n      }, null, 8 /* PROPS */, _hoisted_39)]), _createElementVNode(\"button\", {\n        onClick: $event => $options.togglePasswordVisibility(account.id),\n        class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $data.passwordVisibility[account.id] ? 'eye-slash' : 'eye'],\n        class: \"text-lg\"\n      }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_40)])]), _createElementVNode(\"td\", _hoisted_41, [_createVNode(_component_StatusBadge, {\n        type: account.host.status\n      }, null, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"td\", _hoisted_42, _toDisplayString($options.getPolicyName(account.policyId)), 1 /* TEXT */), _createElementVNode(\"td\", _hoisted_43, [_createElementVNode(\"div\", _hoisted_44, [_createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.openChangePasswordModal(account.host, account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'key'],\n        class: \"mr-1\"\n      }), _cache[88] || (_cache[88] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_45), _createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.copyPassword(account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'copy'],\n        class: \"mr-1\"\n      }), _cache[89] || (_cache[89] = _createTextVNode(\" 复制 \"))], 8 /* PROPS */, _hoisted_46)])])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))], 64 /* STABLE_FRAGMENT */);\n  }), 128 /* KEYED_FRAGMENT */)), _createCommentVNode(\" 无数据显示 \"), $options.filteredAccounts.length === 0 ? (_openBlock(), _createElementBlock(\"tr\", _hoisted_47, [_createElementVNode(\"td\", _hoisted_48, [_createElementVNode(\"div\", _hoisted_49, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"text-4xl mb-3\"\n  }), _cache[90] || (_cache[90] = _createElementVNode(\"p\", null, \"没有找到匹配的账号数据\", -1 /* HOISTED */))])])])) : _createCommentVNode(\"v-if\", true)])])])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 卡片视图 \"), _createElementVNode(\"div\", _hoisted_50, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredHosts, host => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: host.id,\n      class: \"bg-white overflow-hidden shadow rounded-lg\"\n    }, [_createElementVNode(\"div\", _hoisted_51, [_createCommentVNode(\" 主机头部 \"), _createElementVNode(\"div\", _hoisted_52, [_createElementVNode(\"div\", _hoisted_53, [_createVNode(_component_CustomCheckbox, {\n      modelValue: host.selected,\n      \"onUpdate:modelValue\": $event => host.selected = $event,\n      class: \"mr-2\"\n    }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"]), _createElementVNode(\"div\", null, [_createElementVNode(\"h3\", _hoisted_54, _toDisplayString(host.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_55, _toDisplayString(host.ip), 1 /* TEXT */)])]), _createVNode(_component_StatusBadge, {\n      type: host.status\n    }, null, 8 /* PROPS */, [\"type\"])]), _createCommentVNode(\" 账号列表 \"), _createElementVNode(\"div\", _hoisted_56, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(host.accounts, account => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: account.id,\n        class: _normalizeClass([\"border border-gray-200 rounded-lg p-3\", {\n          'border-green-300 bg-green-50': account.isDefault\n        }])\n      }, [_createElementVNode(\"div\", _hoisted_57, [_createElementVNode(\"div\", _hoisted_58, [_createElementVNode(\"span\", _hoisted_59, _toDisplayString(account.username), 1 /* TEXT */), account.isDefault ? (_openBlock(), _createElementBlock(\"span\", _hoisted_60, \" 默认 \")) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.openChangePasswordModal(host, account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'key'],\n        class: \"mr-1\"\n      }), _cache[91] || (_cache[91] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_61)]), _createCommentVNode(\" 密码展示 \"), _createElementVNode(\"div\", _hoisted_62, [_cache[92] || (_cache[92] = _createElementVNode(\"div\", {\n        class: \"text-xs font-medium text-gray-500 mb-1\"\n      }, \"密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_63, [_createElementVNode(\"input\", {\n        type: $data.passwordVisibility[account.id] ? 'text' : 'password',\n        value: account.password,\n        readonly: \"\",\n        class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n      }, null, 8 /* PROPS */, _hoisted_64), _createElementVNode(\"button\", {\n        onClick: $event => $options.togglePasswordVisibility(account.id),\n        class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $data.passwordVisibility[account.id] ? 'eye-slash' : 'eye'],\n        class: \"text-lg\"\n      }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_65)])]), _createCommentVNode(\" 密码信息区域 \"), _createElementVNode(\"div\", _hoisted_66, [_createElementVNode(\"div\", null, [_cache[93] || (_cache[93] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"最后修改时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_67, _toDisplayString(account.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[94] || (_cache[94] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"密码过期\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n        class: _normalizeClass({\n          'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\n          'bg-red-100 text-red-800': $options.isPasswordExpired(account).status === 'danger' || $options.isPasswordExpired(account).status === 'expired',\n          'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(account).status === 'warning',\n          'bg-gray-100 text-gray-800': $options.isPasswordExpired(account).status === 'normal'\n        })\n      }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(account).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(account).status === 'expired' || $options.isPasswordExpired(account).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_68, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-triangle']\n      })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_69, [_cache[95] || (_cache[95] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"密码策略\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_70, _toDisplayString($options.getPolicyName(account.policyId)), 1 /* TEXT */)])])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 添加账号按钮 \"), _createElementVNode(\"div\", _hoisted_71, [_createElementVNode(\"button\", {\n      class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n      onClick: $event => $options.openAddAccountModal(host)\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'plus'],\n      class: \"mr-1\"\n    }), _cache[96] || (_cache[96] = _createTextVNode(\" 添加账号 \"))], 8 /* PROPS */, _hoisted_72)])])]);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 修改密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.changePasswordModal.show,\n    \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.changePasswordModal.show = $event),\n    title: \"修改密码\",\n    onConfirm: $options.updatePassword,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_73, [_cache[100] || (_cache[100] = _createElementVNode(\"div\", {\n      class: \"font-medium mb-2\"\n    }, \"主机信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_74, [_createElementVNode(\"div\", null, [_cache[97] || (_cache[97] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"主机名:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[98] || (_cache[98] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"IP地址:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.ip), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[99] || (_cache[99] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"账号:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentAccount.username), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_75, [_cache[103] || (_cache[103] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_76, [_createElementVNode(\"button\", {\n      onClick: _cache[11] || (_cache[11] = $event => $data.changePasswordModal.method = 'auto'),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", $data.changePasswordModal.method === 'auto' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-2\"\n    }), _cache[101] || (_cache[101] = _createTextVNode(\" 自动生成 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n      onClick: _cache[12] || (_cache[12] = $event => $data.changePasswordModal.method = 'manual'),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", $data.changePasswordModal.method === 'manual' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'edit'],\n      class: \"mr-2\"\n    }), _cache[102] || (_cache[102] = _createTextVNode(\" 手动输入 \"))], 2 /* CLASS */)])]), $data.changePasswordModal.method === 'auto' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_77, [_cache[106] || (_cache[106] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.changePasswordModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[14] || (_cache[14] = $event => $options.generatePassword())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_78);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.changePasswordModal.policyId]]), _createElementVNode(\"div\", _hoisted_79, [_createElementVNode(\"div\", _hoisted_80, [_cache[105] || (_cache[105] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      onClick: _cache[15] || (_cache[15] = $event => $options.generatePassword()),\n      type: \"button\",\n      class: \"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[104] || (_cache[104] = _createTextVNode(\" 重新生成 \"))])]), _createElementVNode(\"div\", _hoisted_81, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.generated ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.changePasswordModal.generatedPassword = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_82), [[_vModelDynamic, $data.changePasswordModal.generatedPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[17] || (_cache[17] = $event => $data.passwordVisibility.generated = !$data.passwordVisibility.generated),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.generated ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_83, [_createElementVNode(\"div\", _hoisted_84, [_cache[107] || (_cache[107] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"新密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_85, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.new ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[18] || (_cache[18] = $event => $data.changePasswordModal.newPassword = $event),\n      class: \"form-control flex-1\",\n      placeholder: \"输入新密码\"\n    }, null, 8 /* PROPS */, _hoisted_86), [[_vModelDynamic, $data.changePasswordModal.newPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[19] || (_cache[19] = $event => $data.passwordVisibility.new = !$data.passwordVisibility.new),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.new ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])]), _createElementVNode(\"div\", _hoisted_87, [_cache[108] || (_cache[108] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"确认密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_88, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.confirm ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $data.changePasswordModal.confirmPassword = $event),\n      class: _normalizeClass([\"form-control flex-1\", {\n        'border-red-500': $options.passwordMismatch\n      }]),\n      placeholder: \"再次输入新密码\"\n    }, null, 10 /* CLASS, PROPS */, _hoisted_89), [[_vModelDynamic, $data.changePasswordModal.confirmPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[21] || (_cache[21] = $event => $data.passwordVisibility.confirm = !$data.passwordVisibility.confirm),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.confirm ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])]), $options.passwordMismatch ? (_openBlock(), _createElementBlock(\"div\", _hoisted_90, \"两次输入的密码不一致\")) : _createCommentVNode(\"v-if\", true)]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.newPassword\n    }, null, 8 /* PROPS */, [\"password\"])])), _createElementVNode(\"div\", _hoisted_91, [_cache[112] || (_cache[112] = _createElementVNode(\"div\", {\n      class: \"form-label font-medium\"\n    }, \"执行选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.executeImmediately,\n      \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $data.changePasswordModal.executeImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[109] || (_cache[109] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"立即执行\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.saveHistory,\n      \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.changePasswordModal.saveHistory = $event)\n    }, {\n      default: _withCtx(() => _cache[110] || (_cache[110] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"保存历史记录\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.logAudit,\n      \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.changePasswordModal.logAudit = $event)\n    }, {\n      default: _withCtx(() => _cache[111] || (_cache[111] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"记录审计日志\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 添加账号弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.addAccountModal.show,\n    \"onUpdate:modelValue\": _cache[33] || (_cache[33] = $event => $data.addAccountModal.show = $event),\n    title: \"添加账号\",\n    onConfirm: $options.addAccount,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_92, [_cache[115] || (_cache[115] = _createElementVNode(\"div\", {\n      class: \"font-medium mb-2\"\n    }, \"主机信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_93, [_createElementVNode(\"div\", null, [_cache[113] || (_cache[113] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"主机名:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[114] || (_cache[114] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"IP地址:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.ip), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_94, [_cache[116] || (_cache[116] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"账号名称\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.addAccountModal.username = $event),\n      class: \"form-control\",\n      placeholder: \"输入账号名称\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addAccountModal.username]])]), _createElementVNode(\"div\", _hoisted_95, [_cache[118] || (_cache[118] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"设为默认账号\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_96, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.addAccountModal.isDefault = $event),\n      class: \"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.addAccountModal.isDefault]]), _cache[117] || (_cache[117] = _createElementVNode(\"label\", {\n      class: \"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"\n    }, null, -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_97, [_cache[119] || (_cache[119] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $data.addAccountModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[29] || (_cache[29] = $event => $options.generatePasswordForNewAccount())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_98);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.addAccountModal.policyId]])]), _createElementVNode(\"div\", _hoisted_99, [_createElementVNode(\"div\", _hoisted_100, [_cache[121] || (_cache[121] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      onClick: _cache[30] || (_cache[30] = $event => $options.generatePasswordForNewAccount()),\n      type: \"button\",\n      class: \"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[120] || (_cache[120] = _createTextVNode(\" 重新生成 \"))])]), _createElementVNode(\"div\", _hoisted_101, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.newAccount ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[31] || (_cache[31] = $event => $data.addAccountModal.password = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_102), [[_vModelDynamic, $data.addAccountModal.password]]), _createElementVNode(\"button\", {\n      onClick: _cache[32] || (_cache[32] = $event => $data.passwordVisibility.newAccount = !$data.passwordVisibility.newAccount),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.newAccount ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量更新密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchUpdateModal.show,\n    \"onUpdate:modelValue\": _cache[43] || (_cache[43] = $event => $data.batchUpdateModal.show = $event),\n    title: \"批量更新密码\",\n    \"confirm-text\": \"开始更新\",\n    size: \"lg\",\n    onConfirm: $options.batchUpdatePasswords,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_103, [_cache[123] || (_cache[123] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_104, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatch,\n      \"onUpdate:modelValue\": [_cache[34] || (_cache[34] = $event => $data.selectAllBatch = $event), $options.toggleSelectAllBatch]\n    }, {\n      default: _withCtx(() => _cache[122] || (_cache[122] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_105, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchUpdateModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchUpdateModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_106, \"已选择 \" + _toDisplayString($options.selectedHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_107, [_cache[124] || (_cache[124] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[35] || (_cache[35] = $event => $data.batchUpdateModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_108);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchUpdateModal.policyId]])]), _createElementVNode(\"div\", _hoisted_109, [_cache[129] || (_cache[129] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_110, [_createElementVNode(\"label\", _hoisted_111, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[36] || (_cache[36] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"immediate\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[125] || (_cache[125] = _createElementVNode(\"span\", null, \"立即执行\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_112, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[37] || (_cache[37] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"scheduled\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[126] || (_cache[126] = _createElementVNode(\"span\", null, \"定时执行\", -1 /* HOISTED */))])]), $data.batchUpdateModal.executionTime === 'scheduled' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_113, [_createElementVNode(\"div\", _hoisted_114, [_createElementVNode(\"div\", null, [_cache[127] || (_cache[127] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"日期\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"date\",\n      \"onUpdate:modelValue\": _cache[38] || (_cache[38] = $event => $data.batchUpdateModal.scheduledDate = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledDate]])]), _createElementVNode(\"div\", null, [_cache[128] || (_cache[128] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"时间\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"time\",\n      \"onUpdate:modelValue\": _cache[39] || (_cache[39] = $event => $data.batchUpdateModal.scheduledTime = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledTime]])])])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_115, [_cache[133] || (_cache[133] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[40] || (_cache[40] = $event => $data.batchUpdateModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[130] || (_cache[130] = [_createTextVNode(\" 忽略错误继续执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.detailedLog,\n      \"onUpdate:modelValue\": _cache[41] || (_cache[41] = $event => $data.batchUpdateModal.detailedLog = $event)\n    }, {\n      default: _withCtx(() => _cache[131] || (_cache[131] = [_createTextVNode(\" 记录详细日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.sendNotification,\n      \"onUpdate:modelValue\": _cache[42] || (_cache[42] = $event => $data.batchUpdateModal.sendNotification = $event)\n    }, {\n      default: _withCtx(() => _cache[132] || (_cache[132] = [_createTextVNode(\" 执行完成后发送通知 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量应用策略弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchApplyModal.show,\n    \"onUpdate:modelValue\": _cache[47] || (_cache[47] = $event => $data.batchApplyModal.show = $event),\n    title: \"批量应用密码策略\",\n    \"confirm-text\": \"应用策略\",\n    onConfirm: $options.batchApplyPolicy,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_116, [_cache[134] || (_cache[134] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_117, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchApplyModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchApplyModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_118, [_cache[135] || (_cache[135] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[44] || (_cache[44] = $event => $data.batchApplyModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name), 9 /* TEXT, PROPS */, _hoisted_119);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchApplyModal.policyId]])]), _createElementVNode(\"div\", _hoisted_120, [_cache[138] || (_cache[138] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.updateImmediately,\n      \"onUpdate:modelValue\": _cache[45] || (_cache[45] = $event => $data.batchApplyModal.updateImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[136] || (_cache[136] = [_createTextVNode(\" 立即更新密码以符合策略 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.applyOnNextUpdate,\n      \"onUpdate:modelValue\": _cache[46] || (_cache[46] = $event => $data.batchApplyModal.applyOnNextUpdate = $event)\n    }, {\n      default: _withCtx(() => _cache[137] || (_cache[137] = [_createTextVNode(\" 下次密码更新时应用 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 紧急重置密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.emergencyResetModal.show,\n    \"onUpdate:modelValue\": _cache[51] || (_cache[51] = $event => $data.emergencyResetModal.show = $event),\n    title: \"紧急密码重置\",\n    \"confirm-text\": \"立即重置\",\n    icon: \"exclamation-triangle\",\n    danger: \"\",\n    onConfirm: $options.emergencyReset,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_cache[144] || (_cache[144] = _createElementVNode(\"div\", {\n      class: \"bg-red-50 text-red-700 p-3 rounded-md mb-4\"\n    }, [_createElementVNode(\"p\", null, \"紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_121, [_cache[139] || (_cache[139] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_122, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.emergencyResetModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.emergencyResetModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_123, [_cache[140] || (_cache[140] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用紧急策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[48] || (_cache[48] = $event => $data.emergencyResetModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.emergencyPolicies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_124);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.policyId]])]), _createElementVNode(\"div\", _hoisted_125, [_cache[142] || (_cache[142] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"操作原因\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[49] || (_cache[49] = $event => $data.emergencyResetModal.reason = $event),\n      class: \"form-select\"\n    }, _cache[141] || (_cache[141] = [_createElementVNode(\"option\", {\n      value: \"security_incident\"\n    }, \"安全事件响应\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"password_leak\"\n    }, \"密码泄露\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"abnormal_access\"\n    }, \"异常访问\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"compliance\"\n    }, \"合规要求\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"other\"\n    }, \"其他原因\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.reason]])]), _createElementVNode(\"div\", _hoisted_126, [_cache[143] || (_cache[143] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"附加说明\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n      \"onUpdate:modelValue\": _cache[50] || (_cache[50] = $event => $data.emergencyResetModal.description = $event),\n      class: \"form-control\",\n      rows: \"2\",\n      placeholder: \"请输入重置原因详细说明\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.emergencyResetModal.description]])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量添加账号弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchAddAccountModal.show,\n    \"onUpdate:modelValue\": _cache[65] || (_cache[65] = $event => $data.batchAddAccountModal.show = $event),\n    title: \"批量添加账号\",\n    size: \"lg\",\n    onConfirm: $options.batchAddAccounts,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_127, [_cache[146] || (_cache[146] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_128, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatchAdd,\n      \"onUpdate:modelValue\": [_cache[52] || (_cache[52] = $event => $data.selectAllBatchAdd = $event), $options.toggleSelectAllBatchAdd]\n    }, {\n      default: _withCtx(() => _cache[145] || (_cache[145] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_129, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchAddAccountModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchAddAccountModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_130, \"已选择 \" + _toDisplayString($options.selectedBatchAddHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_131, [_cache[157] || (_cache[157] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"账号信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_132, [_createElementVNode(\"div\", _hoisted_133, [_cache[147] || (_cache[147] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, [_createTextVNode(\"账号名称 \"), _createElementVNode(\"span\", {\n      class: \"text-red-500\"\n    }, \"*\")], -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[53] || (_cache[53] = $event => $data.batchAddAccountModal.username = $event),\n      class: \"form-control\",\n      placeholder: \"输入统一账号名称\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchAddAccountModal.username]]), _cache[148] || (_cache[148] = _createElementVNode(\"div\", {\n      class: \"text-xs text-gray-500 mt-1\"\n    }, \"将在所有选中主机上创建同名账号\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_134, [_cache[150] || (_cache[150] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"账号角色\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[54] || (_cache[54] = $event => $data.batchAddAccountModal.role = $event),\n      class: \"form-select\"\n    }, _cache[149] || (_cache[149] = [_createElementVNode(\"option\", {\n      value: \"admin\"\n    }, \"管理员\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"user\"\n    }, \"普通用户\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"service\"\n    }, \"服务账号\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"readonly\"\n    }, \"只读账号\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchAddAccountModal.role]])]), _createElementVNode(\"div\", _hoisted_135, [_createElementVNode(\"label\", _hoisted_136, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[55] || (_cache[55] = $event => $data.batchAddAccountModal.setAsDefault = $event),\n      class: \"form-checkbox\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.batchAddAccountModal.setAsDefault]]), _cache[151] || (_cache[151] = _createElementVNode(\"span\", {\n      class: \"ml-2\"\n    }, \"设为默认账号\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_137, [_cache[154] || (_cache[154] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_138, [_createElementVNode(\"button\", {\n      onClick: _cache[56] || (_cache[56] = $event => $data.batchAddAccountModal.useSamePassword = true),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", $data.batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'key'],\n      class: \"mr-2\"\n    }), _cache[152] || (_cache[152] = _createTextVNode(\" 同一密码 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n      onClick: _cache[57] || (_cache[57] = $event => $data.batchAddAccountModal.useSamePassword = false),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", !$data.batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'random'],\n      class: \"mr-2\"\n    }), _cache[153] || (_cache[153] = _createTextVNode(\" 随机密码 \"))], 2 /* CLASS */)])]), $data.batchAddAccountModal.useSamePassword ? (_openBlock(), _createElementBlock(\"div\", _hoisted_139, [_cache[156] || (_cache[156] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"统一密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_140, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.batchPassword ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[58] || (_cache[58] = $event => $data.batchAddAccountModal.password = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_141), [[_vModelDynamic, $data.batchAddAccountModal.password]]), _createElementVNode(\"button\", {\n      onClick: _cache[59] || (_cache[59] = $event => $data.passwordVisibility.batchPassword = !$data.passwordVisibility.batchPassword),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.batchPassword ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])]), _createElementVNode(\"button\", {\n      onClick: _cache[60] || (_cache[60] = $event => $options.generatePasswordForBatchAccount()),\n      type: \"button\",\n      class: \"ml-2 px-3 py-1.5 border border-gray-300 text-xs rounded-md\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[155] || (_cache[155] = _createTextVNode(\" 重新生成 \"))])])])) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_142, [_cache[158] || (_cache[158] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[61] || (_cache[61] = $event => $data.batchAddAccountModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[62] || (_cache[62] = $event => $options.generatePasswordForBatchAccount())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_143);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.batchAddAccountModal.policyId]])]), _createElementVNode(\"div\", _hoisted_144, [_cache[161] || (_cache[161] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_145, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchAddAccountModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[63] || (_cache[63] = $event => $data.batchAddAccountModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[159] || (_cache[159] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"忽略错误继续执行\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchAddAccountModal.generateReport,\n      \"onUpdate:modelValue\": _cache[64] || (_cache[64] = $event => $data.batchAddAccountModal.generateReport = $event)\n    }, {\n      default: _withCtx(() => _cache[160] || (_cache[160] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"生成账号创建报告\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "scope", "colspan", "_createElementBlock", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "args", "$options", "showEmergencyReset", "_createVNode", "_component_font_awesome_icon", "icon", "openBatchUpdateModal", "openBatchApplyModal", "openBatchAddAccountModal", "_hoisted_4", "_hoisted_5", "_normalizeClass", "$data", "viewMode", "$event", "_createTextVNode", "_hoisted_6", "type", "filterText", "placeholder", "_hoisted_7", "_hoisted_8", "accountFilterText", "_hoisted_9", "statusFilter", "value", "expiryFilter", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_toDisplayString", "filteredAccounts", "length", "_hoisted_14", "getAllAccounts", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_component_CustomCheckbox", "modelValue", "selectAll", "toggleSelectAll", "default", "_withCtx", "_", "_hoisted_21", "_Fragment", "_renderList", "groupedAccounts", "hostGroup", "hostId", "accounts", "account", "accountIndex", "id", "_hoisted_22", "_hoisted_23", "host", "selected", "_hoisted_24", "name", "_hoisted_25", "_hoisted_26", "ip", "_hoisted_27", "_hoisted_28", "_hoisted_29", "username", "isDefault", "_hoisted_30", "_hoisted_31", "_hoisted_32", "lastPasswordChange", "_hoisted_33", "isPasswordExpired", "status", "text", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "passwordVisibility", "password", "readonly", "_hoisted_39", "togglePasswordVisibility", "_hoisted_40", "_hoisted_41", "_component_StatusBadge", "_hoisted_42", "getPolicyName", "policyId", "_hoisted_43", "_hoisted_44", "openChangePasswordModal", "_hoisted_45", "copyPassword", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_hoisted_50", "filteredHosts", "_hoisted_51", "_hoisted_52", "_hoisted_53", "_hoisted_54", "_hoisted_55", "_hoisted_56", "_hoisted_57", "_hoisted_58", "_hoisted_59", "_hoisted_60", "_hoisted_61", "_hoisted_62", "_hoisted_63", "_hoisted_64", "_hoisted_65", "_hoisted_66", "_hoisted_67", "_hoisted_68", "_hoisted_69", "_hoisted_70", "_hoisted_71", "openAddAccountModal", "_hoisted_72", "_component_BaseModal", "changePasswordModal", "show", "title", "onConfirm", "updatePassword", "loading", "processing", "_hoisted_73", "_hoisted_74", "currentHost", "currentAccount", "_hoisted_75", "_hoisted_76", "method", "_hoisted_77", "onChange", "generatePassword", "_ctx", "policies", "policy", "<PERSON><PERSON><PERSON><PERSON>", "expiryDays", "_hoisted_78", "_hoisted_79", "_hoisted_80", "_hoisted_81", "generated", "generatedPassword", "_hoisted_82", "_hoisted_83", "_hoisted_84", "_hoisted_85", "new", "newPassword", "_hoisted_86", "_hoisted_87", "_hoisted_88", "confirm", "confirmPassword", "passwordMismatch", "_hoisted_89", "_hoisted_90", "_component_PasswordStrengthMeter", "_hoisted_91", "executeImmediately", "saveHistory", "logAudit", "addAccountModal", "addAccount", "_hoisted_92", "_hoisted_93", "_hoisted_94", "_hoisted_95", "_hoisted_96", "_hoisted_97", "generatePasswordForNewAccount", "_hoisted_98", "_hoisted_99", "_hoisted_100", "_hoisted_101", "newAccount", "_hoisted_102", "batchUpdateModal", "size", "batchUpdatePasswords", "_hoisted_103", "_hoisted_104", "selectAllBatch", "toggleSelectAllBatch", "_hoisted_105", "hosts", "_createBlock", "selectedHosts", "_hoisted_106", "selectedHostsCount", "_hoisted_107", "_hoisted_108", "_hoisted_109", "_hoisted_110", "_hoisted_111", "executionTime", "_hoisted_112", "_hoisted_113", "_hoisted_114", "scheduledDate", "scheduledTime", "_hoisted_115", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "batchApplyPolicy", "_hoisted_116", "_hoisted_117", "selectedHostsList", "_hoisted_118", "_hoisted_119", "_hoisted_120", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "danger", "emergencyReset", "_hoisted_121", "_hoisted_122", "_hoisted_123", "emergencyPolicies", "_hoisted_124", "_hoisted_125", "reason", "_hoisted_126", "description", "rows", "batchAddAccountModal", "batchAddAccounts", "_hoisted_127", "_hoisted_128", "selectAllBatchAdd", "toggleSelectAllBatchAdd", "_hoisted_129", "_hoisted_130", "selectedBatchAddHostsCount", "_hoisted_131", "_hoisted_132", "_hoisted_133", "_hoisted_134", "role", "_hoisted_135", "_hoisted_136", "setAsDefault", "_hoisted_137", "_hoisted_138", "useSamePassword", "_hoisted_139", "_hoisted_140", "batchPassword", "_hoisted_141", "generatePasswordForBatchAccount", "_hoisted_142", "_hoisted_143", "_hoisted_144", "_hoisted_145", "generateReport"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"bg-white shadow rounded-lg p-4 mb-6\">\r\n      <div class=\"flex flex-wrap items-center justify-between\">\r\n        <div class=\"flex space-x-3 mb-2 sm:mb-0\">\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\r\n            @click=\"showEmergencyReset\">\r\n            <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2\" />\r\n            <span>紧急重置</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            @click=\"openBatchUpdateModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n            <span>批量更新密码</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\r\n            @click=\"openBatchApplyModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n            <span>批量应用策略</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\r\n            @click=\"openBatchAddAccountModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'users']\" class=\"mr-2\" />\r\n            <span>批量添加账号</span>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"flex items-center space-x-4\">\r\n          <!-- 视图切换 -->\r\n          <div class=\"flex items-center border rounded-md overflow-hidden\">\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'table', 'bg-gray-100 text-gray-600': viewMode !== 'table' }\"\r\n              @click=\"viewMode = 'table'\">\r\n              <font-awesome-icon :icon=\"['fas', 'table']\" class=\"mr-1\" />\r\n              表格\r\n            </button>\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'card', 'bg-gray-100 text-gray-600': viewMode !== 'card' }\"\r\n              @click=\"viewMode = 'card'\">\r\n              <font-awesome-icon :icon=\"['fas', 'th-large']\" class=\"mr-1\" />\r\n              卡片\r\n            </button>\r\n          </div>\r\n\r\n          <!-- 筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"filterText\" placeholder=\"筛选主机...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 账号筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"accountFilterText\" placeholder=\"筛选账号...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'user']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 状态筛选 -->\r\n          <select v-model=\"statusFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有状态</option>\r\n            <option value=\"normal\">正常</option>\r\n            <option value=\"warning\">警告</option>\r\n            <option value=\"error\">错误</option>\r\n          </select>\r\n\r\n          <!-- 显示密码过期选项 -->\r\n          <select v-model=\"expiryFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有密码</option>\r\n            <option value=\"expired\">已过期</option>\r\n            <option value=\"expiring-soon\">即将过期</option>\r\n            <option value=\"valid\">有效期内</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <!-- 表格视图 -->\r\n    <div v-if=\"viewMode === 'table'\" class=\"bg-white rounded-lg shadow overflow-hidden\">\r\n      <!-- 账号计数和导出按钮 -->\r\n      <div class=\"px-4 py-3 bg-gray-50 border-b flex justify-between items-center\">\r\n        <div class=\"text-sm text-gray-700\">\r\n          显示 <span class=\"font-medium\">{{ filteredAccounts.length }}</span> 个账号\r\n          (共 <span class=\"font-medium\">{{ getAllAccounts.length }}</span> 个)\r\n        </div>\r\n        <div class=\"flex space-x-2\">\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\r\n            <font-awesome-icon :icon=\"['fas', 'file-export']\" class=\"mr-1\" />\r\n            导出\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\r\n            <font-awesome-icon :icon=\"['fas', 'print']\" class=\"mr-1\" />\r\n            打印\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <table class=\"min-w-full divide-y divide-gray-200\">\r\n        <thead class=\"bg-gray-50\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n                主机名\r\n              </CustomCheckbox>\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              IP地址\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              账号\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              最后密码修改时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码过期时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              状态\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              策略\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              操作\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"bg-white divide-y divide-gray-200\">\r\n          <!-- 按主机分组显示 -->\r\n          <template v-for=\"hostGroup in groupedAccounts\" :key=\"hostGroup.hostId\">\r\n            <!-- 账号行 -->\r\n            <tr v-for=\"(account, accountIndex) in hostGroup.accounts\" :key=\"account.id\"\r\n              :class=\"{ 'bg-gray-50': accountIndex % 2 === 0, 'hover:bg-blue-50': true }\">\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <CustomCheckbox v-model=\"account.host.selected\" class=\"ml-4\">\r\n                    <span class=\"ml-2 font-medium text-gray-900\">{{ account.host.name }}</span>\r\n                  </CustomCheckbox>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-900\">{{ account.host.ip }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-500\">{{ account.lastPasswordChange || '-' }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div :class=\"{\r\n                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\r\n                  'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                  'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                  'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                }\">\r\n                  {{ isPasswordExpired(account).text }}\r\n                  <span\r\n                    v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                    class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                  </span>\r\n                  <span v-else-if=\"isPasswordExpired(account).status === 'warning'\" class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" />\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <div class=\"flex-grow\">\r\n                    <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\"\r\n                      readonly\r\n                      class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  </div>\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <StatusBadge :type=\"account.host.status\" />\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                {{ getPolicyName(account.policyId) }}\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                <div class=\"flex space-x-2\">\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"openChangePasswordModal(account.host, account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                    修改密码\r\n                  </button>\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"copyPassword(account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'copy']\" class=\"mr-1\" />\r\n                    复制\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          </template>\r\n          <!-- 无数据显示 -->\r\n          <tr v-if=\"filteredAccounts.length === 0\">\r\n            <td colspan=\"8\" class=\"px-6 py-10 text-center\">\r\n              <div class=\"text-gray-500\">\r\n                <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-4xl mb-3\" />\r\n                <p>没有找到匹配的账号数据</p>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <!-- 卡片视图 -->\r\n    <div v-else class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\">\r\n      <div v-for=\"host in filteredHosts\" :key=\"host.id\" class=\"bg-white overflow-hidden shadow rounded-lg\">\r\n        <div class=\"px-4 py-5 sm:p-6\">\r\n          <!-- 主机头部 -->\r\n          <div class=\"flex justify-between items-start mb-4\">\r\n            <div class=\"flex items-center\">\r\n              <CustomCheckbox v-model=\"host.selected\" class=\"mr-2\" />\r\n              <div>\r\n                <h3 class=\"text-lg font-medium text-gray-900\">{{ host.name }}</h3>\r\n                <p class=\"text-sm text-gray-500\">{{ host.ip }}</p>\r\n              </div>\r\n            </div>\r\n            <StatusBadge :type=\"host.status\" />\r\n          </div>\r\n\r\n          <!-- 账号列表 -->\r\n          <div class=\"space-y-4\">\r\n            <div v-for=\"account in host.accounts\" :key=\"account.id\" class=\"border border-gray-200 rounded-lg p-3\"\r\n              :class=\"{ 'border-green-300 bg-green-50': account.isDefault }\">\r\n              <div class=\"flex justify-between items-center mb-2\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n                <button\r\n                  class=\"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                  @click=\"openChangePasswordModal(host, account)\">\r\n                  <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                  修改密码\r\n                </button>\r\n              </div>\r\n\r\n              <!-- 密码展示 -->\r\n              <div class=\"mb-2\">\r\n                <div class=\"text-xs font-medium text-gray-500 mb-1\">密码</div>\r\n                <div class=\"flex items-center\">\r\n                  <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\" readonly\r\n                    class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 密码信息区域 -->\r\n              <div class=\"grid grid-cols-2 gap-2 text-xs\">\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">最后修改时间</div>\r\n                  <div class=\"text-gray-900\">{{ account.lastPasswordChange || '-' }}</div>\r\n                </div>\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">密码过期</div>\r\n                  <div :class=\"{\r\n                    'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\r\n                    'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                    'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                    'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                  }\">\r\n                    {{ isPasswordExpired(account).text }}\r\n                    <span\r\n                      v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                      class=\"ml-1\">\r\n                      <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-span-2 mt-1\">\r\n                  <div class=\"font-medium text-gray-500 mb-1\">密码策略</div>\r\n                  <div class=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800\">\r\n                    {{ getPolicyName(account.policyId) }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 添加账号按钮 -->\r\n          <div class=\"mt-4 flex justify-center\">\r\n            <button\r\n              class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n              @click=\"openAddAccountModal(host)\">\r\n              <font-awesome-icon :icon=\"['fas', 'plus']\" class=\"mr-1\" />\r\n              添加账号\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" @confirm=\"updatePassword\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n          <div><span class=\"font-medium\">账号:</span> {{ currentAccount.username }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-3\">\r\n          <button @click=\"changePasswordModal.method = 'auto'\"\r\n            class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n            :class=\"changePasswordModal.method === 'auto' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-2\" />\r\n            自动生成\r\n          </button>\r\n          <button @click=\"changePasswordModal.method = 'manual'\"\r\n            class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n            :class=\"changePasswordModal.method === 'manual' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n            <font-awesome-icon :icon=\"['fas', 'edit']\" class=\"mr-2\" />\r\n            手动输入\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-if=\"changePasswordModal.method === 'auto'\" class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"changePasswordModal.policyId\" class=\"form-select\" @change=\"generatePassword()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n\r\n        <div class=\"mt-3\">\r\n          <div class=\"flex justify-between mb-1\">\r\n            <label class=\"form-label\">生成的密码</label>\r\n            <button @click=\"generatePassword()\" type=\"button\"\r\n              class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n              重新生成\r\n            </button>\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.generated ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.generatedPassword\" readonly class=\"form-control flex-1 bg-gray-50\" />\r\n            <button @click=\"passwordVisibility.generated = !passwordVisibility.generated\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.generated ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else class=\"space-y-4\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.new ? 'text' : 'password'\" v-model=\"changePasswordModal.newPassword\"\r\n              class=\"form-control flex-1\" placeholder=\"输入新密码\" />\r\n            <button @click=\"passwordVisibility.new = !passwordVisibility.new\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.new ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.confirm ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.confirmPassword\" class=\"form-control flex-1\"\r\n              :class=\"{ 'border-red-500': passwordMismatch }\" placeholder=\"再次输入新密码\" />\r\n            <button @click=\"passwordVisibility.confirm = !passwordVisibility.confirm\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.confirm ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n          <div v-if=\"passwordMismatch\" class=\"text-sm text-red-500 mt-1\">两次输入的密码不一致</div>\r\n        </div>\r\n\r\n        <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n      </div>\r\n\r\n      <div class=\"space-y-2 mt-4\">\r\n        <div class=\"form-label font-medium\">执行选项</div>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          <span class=\"ml-2\">立即执行</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          <span class=\"ml-2\">保存历史记录</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          <span class=\"ml-2\">记录审计日志</span>\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 添加账号弹窗 -->\r\n    <BaseModal v-model=\"addAccountModal.show\" title=\"添加账号\" @confirm=\"addAccount\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">账号名称</label>\r\n        <input type=\"text\" v-model=\"addAccountModal.username\" class=\"form-control\" placeholder=\"输入账号名称\" />\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">设为默认账号</label>\r\n        <div class=\"relative inline-block w-10 mr-2 align-middle select-none\">\r\n          <input type=\"checkbox\" v-model=\"addAccountModal.isDefault\"\r\n            class=\"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\" />\r\n          <label class=\"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"></label>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"addAccountModal.policyId\" class=\"form-select\" @change=\"generatePasswordForNewAccount()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <div class=\"flex justify-between mb-1\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <button @click=\"generatePasswordForNewAccount()\" type=\"button\"\r\n            class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n            重新生成\r\n          </button>\r\n        </div>\r\n        <div class=\"flex items-center\">\r\n          <input :type=\"passwordVisibility.newAccount ? 'text' : 'password'\" v-model=\"addAccountModal.password\" readonly\r\n            class=\"form-control flex-1 bg-gray-50\" />\r\n          <button @click=\"passwordVisibility.newAccount = !passwordVisibility.newAccount\" type=\"button\"\r\n            class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', passwordVisibility.newAccount ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量添加账号弹窗 -->\r\n    <BaseModal v-model=\"batchAddAccountModal.show\" title=\"批量添加账号\" size=\"lg\" @confirm=\"batchAddAccounts\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatchAdd\" @update:modelValue=\"toggleSelectAllBatchAdd\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchAddAccountModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedBatchAddHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">账号信息</label>\r\n        <div class=\"p-4 border border-gray-200 rounded-md\">\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">账号名称 <span class=\"text-red-500\">*</span></label>\r\n            <input type=\"text\" v-model=\"batchAddAccountModal.username\" class=\"form-control\" placeholder=\"输入统一账号名称\" />\r\n            <div class=\"text-xs text-gray-500 mt-1\">将在所有选中主机上创建同名账号</div>\r\n          </div>\r\n\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">账号角色</label>\r\n            <select v-model=\"batchAddAccountModal.role\" class=\"form-select\">\r\n              <option value=\"admin\">管理员</option>\r\n              <option value=\"user\">普通用户</option>\r\n              <option value=\"service\">服务账号</option>\r\n              <option value=\"readonly\">只读账号</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div class=\"flex items-center mb-3\">\r\n            <label class=\"inline-flex items-center\">\r\n              <input type=\"checkbox\" v-model=\"batchAddAccountModal.setAsDefault\" class=\"form-checkbox\">\r\n              <span class=\"ml-2\">设为默认账号</span>\r\n            </label>\r\n          </div>\r\n\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">密码生成方式</label>\r\n            <div class=\"flex space-x-3\">\r\n              <button @click=\"batchAddAccountModal.useSamePassword = true\"\r\n                class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n                :class=\"batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n                <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n                同一密码\r\n              </button>\r\n              <button @click=\"batchAddAccountModal.useSamePassword = false\"\r\n                class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n                :class=\"!batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n                <font-awesome-icon :icon=\"['fas', 'random']\" class=\"mr-2\" />\r\n                随机密码\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <div v-if=\"batchAddAccountModal.useSamePassword\" class=\"mb-3\">\r\n            <label class=\"form-label\">统一密码</label>\r\n            <div class=\"flex items-center\">\r\n              <input :type=\"passwordVisibility.batchPassword ? 'text' : 'password'\"\r\n                v-model=\"batchAddAccountModal.password\" readonly class=\"form-control flex-1 bg-gray-50\" />\r\n              <button @click=\"passwordVisibility.batchPassword = !passwordVisibility.batchPassword\" type=\"button\"\r\n                class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                <font-awesome-icon :icon=\"['fas', passwordVisibility.batchPassword ? 'eye-slash' : 'eye']\"\r\n                  class=\"text-lg\" />\r\n              </button>\r\n              <button @click=\"generatePasswordForBatchAccount()\" type=\"button\"\r\n                class=\"ml-2 px-3 py-1.5 border border-gray-300 text-xs rounded-md\">\r\n                <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n                重新生成\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchAddAccountModal.policyId\" class=\"form-select\" @change=\"generatePasswordForBatchAccount()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <div class=\"space-y-2\">\r\n          <CustomCheckbox v-model=\"batchAddAccountModal.ignoreErrors\">\r\n            <span class=\"ml-2\">忽略错误继续执行</span>\r\n          </CustomCheckbox>\r\n          <CustomCheckbox v-model=\"batchAddAccountModal.generateReport\">\r\n            <span class=\"ml-2\">生成账号创建报告</span>\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      selectAllBatchAdd: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      currentAccount: {},\r\n      passwordVisibility: {\r\n        generated: false,\r\n        new: false,\r\n        confirm: false,\r\n        newAccount: false,\r\n        batchPassword: false\r\n      },\r\n      viewMode: 'table',\r\n      filterText: '',\r\n      accountFilterText: '',\r\n      statusFilter: 'all',\r\n      expiryFilter: 'all',\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      },\r\n\r\n      // 添加账号弹窗\r\n      addAccountModal: {\r\n        show: false,\r\n        username: '',\r\n        password: '',\r\n        isDefault: false,\r\n        policyId: 1\r\n      },\r\n\r\n      // 批量添加账号弹窗\r\n      batchAddAccountModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        username: '',\r\n        password: '',\r\n        role: 'admin',\r\n        setAsDefault: false,\r\n        useSamePassword: true,\r\n        policyId: 1\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    // 获取策略名称\r\n    getPolicyName() {\r\n      return (policyId) => {\r\n        if (!policyId) return '无';\r\n        const policy = this.policies.find(p => p.id === policyId);\r\n        return policy ? policy.name : '无';\r\n      };\r\n    },\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    },\r\n\r\n    // 过滤后的主机列表\r\n    filteredHosts() {\r\n      return this.hosts.filter(host => {\r\n        // 文本过滤\r\n        const textMatch = this.filterText === '' ||\r\n          host.name.toLowerCase().includes(this.filterText.toLowerCase()) ||\r\n          host.ip.includes(this.filterText);\r\n\r\n        // 状态过滤\r\n        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;\r\n\r\n        return textMatch && statusMatch;\r\n      });\r\n    },\r\n\r\n    // 获取所有账号（扁平化处理）\r\n    getAllAccounts() {\r\n      // 为每个账号添加主机引用\r\n      const accounts = [];\r\n      this.filteredHosts.forEach(host => {\r\n        host.accounts.forEach(account => {\r\n          accounts.push({\r\n            ...account,\r\n            host: host\r\n          });\r\n        });\r\n      });\r\n      return accounts;\r\n    },\r\n\r\n    // 筛选后的账号\r\n    filteredAccounts() {\r\n      return this.getAllAccounts.filter(account => {\r\n        // 账号名称筛选\r\n        const accountMatch = this.accountFilterText === '' ||\r\n          account.username.toLowerCase().includes(this.accountFilterText.toLowerCase());\r\n\r\n        // 密码过期筛选\r\n        let expiryMatch = true;\r\n        if (this.expiryFilter !== 'all') {\r\n          const expiryStatus = this.isPasswordExpired(account).status;\r\n          if (this.expiryFilter === 'expired') {\r\n            expiryMatch = expiryStatus === 'expired';\r\n          } else if (this.expiryFilter === 'expiring-soon') {\r\n            expiryMatch = expiryStatus === 'danger' || expiryStatus === 'warning';\r\n          } else if (this.expiryFilter === 'valid') {\r\n            expiryMatch = expiryStatus === 'normal';\r\n          }\r\n        }\r\n\r\n        return accountMatch && expiryMatch;\r\n      });\r\n    },\r\n\r\n    // 分组后的账号列表\r\n    groupedAccounts() {\r\n      // 按主机ID分组\r\n      const groups = {};\r\n      this.filteredAccounts.forEach(account => {\r\n        const hostId = account.host.id;\r\n        if (!groups[hostId]) {\r\n          groups[hostId] = {\r\n            hostId: hostId,\r\n            hostName: account.host.name,\r\n            hostIp: account.host.ip,\r\n            host: account.host,\r\n            accounts: []\r\n          };\r\n        }\r\n        groups[hostId].accounts.push(account);\r\n      });\r\n\r\n      // 转换为数组\r\n      return Object.values(groups);\r\n    },\r\n\r\n    selectedBatchAddHostsCount() {\r\n      return Object.values(this.batchAddAccountModal.selectedHosts).filter(Boolean).length\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    toggleSelectAllBatchAdd(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchAddAccountModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host, account) {\r\n      this.currentHost = host\r\n      this.currentAccount = account\r\n      this.changePasswordModal.show = true\r\n      this.changePasswordModal.generatedPassword = this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword(policy) {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policyObj ? policyObj.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      if (this.changePasswordModal && !policy) {\r\n        this.changePasswordModal.generatedPassword = password\r\n      }\r\n\r\n      return password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto'\r\n          ? this.changePasswordModal.generatedPassword\r\n          : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          accountId: this.currentAccount.id,\r\n          password: password,\r\n          policyId: this.changePasswordModal.policyId\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的 ${this.currentAccount.username} 账号密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取所选策略\r\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取紧急策略\r\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    togglePasswordVisibility(hostId) {\r\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]\r\n    },\r\n\r\n    isPasswordExpired(account) {\r\n      if (!account.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }\r\n\r\n      // 解析过期时间\r\n      const expiryDate = new Date(account.passwordExpiryDate)\r\n      const now = new Date()\r\n\r\n      // 如果已过期\r\n      if (expiryDate < now) {\r\n        return {\r\n          status: 'expired',\r\n          days: 0,\r\n          text: '已过期'\r\n        }\r\n      }\r\n\r\n      // 计算剩余天数和小时数\r\n      const diffTime = expiryDate - now\r\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))\r\n      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n\r\n      // 根据剩余时间确定状态\r\n      let status = 'normal'\r\n      if (diffDays < 7) {\r\n        status = 'danger'  // 少于7天\r\n      } else if (diffDays < 14) {\r\n        status = 'warning' // 少于14天\r\n      }\r\n\r\n      // 格式化显示文本\r\n      let text = ''\r\n      if (diffDays > 0) {\r\n        text += `${diffDays}天`\r\n      }\r\n      if (diffHours > 0 || diffDays === 0) {\r\n        text += `${diffHours}小时`\r\n      }\r\n\r\n      return { status, days: diffDays, text: `剩余${text}` }\r\n    },\r\n\r\n    openAddAccountModal(host) {\r\n      this.currentHost = host\r\n      this.addAccountModal.show = true\r\n    },\r\n\r\n    async addAccount() {\r\n      if (!this.addAccountModal.username || !this.addAccountModal.password) {\r\n        alert('请填写完整的账号信息！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('addHostAccount', {\r\n          hostId: this.currentHost.id,\r\n          username: this.addAccountModal.username,\r\n          password: this.addAccountModal.password,\r\n          policyId: this.addAccountModal.policyId,\r\n          isDefault: this.addAccountModal.isDefault\r\n        })\r\n\r\n        this.addAccountModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为主机 ${this.currentHost.name} 添加账号！`)\r\n      } catch (error) {\r\n        console.error('添加账号失败', error)\r\n        alert('添加账号失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    generatePasswordForNewAccount() {\r\n      this.addAccountModal.password = this.generatePassword()\r\n    },\r\n\r\n    // 复制密码到剪贴板\r\n    copyPassword(account) {\r\n      // 创建一个临时输入框\r\n      const tempInput = document.createElement('input');\r\n      tempInput.value = account.password;\r\n      document.body.appendChild(tempInput);\r\n      tempInput.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(tempInput);\r\n\r\n      // 显示提示\r\n      alert(`已复制 ${account.host.name} 的 ${account.username} 账号密码到剪贴板！`);\r\n    },\r\n\r\n    generatePasswordForBatchAccount() {\r\n      this.batchAddAccountModal.password = this.generatePassword()\r\n    },\r\n\r\n    async batchAddAccounts() {\r\n      if (!this.batchAddAccountModal.username) {\r\n        alert('请填写账号名称！')\r\n        return\r\n      }\r\n\r\n      const selectedHostIds = Object.entries(this.batchAddAccountModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('batchAddAccounts', {\r\n          hostIds: selectedHostIds,\r\n          username: this.batchAddAccountModal.username,\r\n          password: this.batchAddAccountModal.useSamePassword ? this.batchAddAccountModal.password : null,\r\n          role: this.batchAddAccountModal.role,\r\n          isDefault: this.batchAddAccountModal.setAsDefault,\r\n          policyId: this.batchAddAccountModal.policyId\r\n        })\r\n\r\n        this.batchAddAccountModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机添加账号！`)\r\n      } catch (error) {\r\n        console.error('批量添加账号失败', error)\r\n        alert('批量添加账号失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    openBatchAddAccountModal() {\r\n      this.batchAddAccountModal.show = true\r\n      this.batchAddAccountModal.selectedHosts = {}\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchAddAccountModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 生成初始密码\r\n      this.generatePasswordForBatchAccount()\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": ";;EAGSA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAA6C;;EACjDA,KAAK,EAAC;AAA6B;;EA2BnCA,KAAK,EAAC;AAA6B;;EAEjCA,KAAK,EAAC;AAAqD;;EAgB3DA,KAAK,EAAC;AAAU;;EAGdA,KAAK,EAAC;AAAsE;;EAM9EA,KAAK,EAAC;AAAU;;EAGdA,KAAK,EAAC;AAAsE;;EA9D7FC,GAAA;EA0FqCD,KAAK,EAAC;;;EAEhCA,KAAK,EAAC;AAAiE;;EACrEA,KAAK,EAAC;AAAuB;;EACvBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EAEzBA,KAAK,EAAC;AAAgB;;EAEvBA,KAAK,EAAC;AAAsN;;EAK5NA,KAAK,EAAC;AAAsN;;EAO3NA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAAY;;EAEjBE,KAAK,EAAC,KAAK;EAACF,KAAK,EAAC;;;EA+BnBA,KAAK,EAAC;AAAmC;;EAMtCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EAEpBA,KAAK,EAAC;AAAgC;;EAI9CA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAmC;;EAnKjEC,GAAA;EAqKoBD,KAAK,EAAC;;;EAKRA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EA7KrDC,GAAA;EAuLoBD,KAAK,EAAC;;;EAvL1BC,GAAA;EA0LoFD,KAAK,EAAC;;;EAKxEA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAW;oBAjMxC;oBAAA;;EA6MkBA,KAAK,EAAC;AAA6B;;EAGnCA,KAAK,EAAC;AAA6B;;EAGnCA,KAAK,EAAC;AAAmD;;EACtDA,KAAK,EAAC;AAAgB;oBApN3C;oBAAA;;EAAAC,GAAA;AAAA;;EAuOgBE,OAAO,EAAC,GAAG;EAACH,KAAK,EAAC;;;EACfA,KAAK,EAAC;AAAe;;EAWxBA,KAAK,EAAC;AAAsD;;EAE/DA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAuC;;EAC3CA,KAAK,EAAC;AAAmB;;EAGtBA,KAAK,EAAC;AAAmC;;EAC1CA,KAAK,EAAC;AAAuB;;EAOjCA,KAAK,EAAC;AAAW;;EAGbA,KAAK,EAAC;AAAwC;;EAC5CA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAmC;;EAxQjEC,GAAA;EA0QoBD,KAAK,EAAC;;oBA1Q1B;;EAuRmBA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAmB;oBAzR9C;oBAAA;;EAqSmBA,KAAK,EAAC;AAAgC;;EAGlCA,KAAK,EAAC;AAAe;;EAxS5CC,GAAA;EAqTsBD,KAAK,EAAC;;;EAKPA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAA4F;;EAS1GA,KAAK,EAAC;AAA0B;oBArU/C;;EAmVWA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAiC;;EAOzCA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAgB;;EA9VnCC,GAAA;EA8WwDD,KAAK,EAAC;;oBA9W9D;;EAsXaA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAA2B;;EAQjCA,KAAK,EAAC;AAAmB;oBA/XxC;;EAAAC,GAAA;EA0YkBD,KAAK,EAAC;;;EACXA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;oBA7YxC;;EAuZaA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;oBAzZxC;;EAAAC,GAAA;EAkauCD,KAAK,EAAC;;;EAMlCA,KAAK,EAAC;AAAgB;;EAgBtBA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAiC;;EAMzCA,KAAK,EAAC;AAAiB;;EAKvBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAA0D;;EAOlEA,KAAK,EAAC;AAAiB;oBA9clC;;EAudWA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAA2B;;EAQjCA,KAAK,EAAC;AAAmB;qBAhetC;;EA8eWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAY;qBA7f7B;;EAsgBWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EA7gB1CC,GAAA;EAmhBmED,KAAK,EAAC;;;EAC1DA,KAAK,EAAC;AAAwB;;EAalCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;qBA5jB7B;;EAqkBWA,KAAK,EAAC;AAAY;;EAkBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;qBAjmB7B;;EA0mBWA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;EAUlBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAuC;;EAC3CA,KAAK,EAAC;AAAM;;EAMZA,KAAK,EAAC;AAAM;;EAUZA,KAAK,EAAC;AAAwB;;EAC1BA,KAAK,EAAC;AAA0B;;EAMpCA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAgB;;EA1qBvCC,GAAA;EA0rB2DD,KAAK,EAAC;;;EAEhDA,KAAK,EAAC;AAAmB;qBA5rB1C;;EA8sBWA,KAAK,EAAC;AAAiB;qBA9sBlC;;EAutBWA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAW;;;;;;;uBAxtB5BI,mBAAA,CAkuBM,cAjuBJC,mBAAA,UAAa,EACbC,mBAAA,CAmFM,OAnFNC,UAmFM,GAlFJD,mBAAA,CAiFM,OAjFNE,UAiFM,GAhFJF,mBAAA,CAyBM,OAzBNG,UAyBM,GAxBJH,mBAAA,CAKS;IAJPN,KAAK,EAAC,qNAAqN;IAC1NU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,kBAAA,IAAAD,QAAA,CAAAC,kBAAA,IAAAF,IAAA,CAAkB;MAC1BG,YAAA,CAA0EC,4BAAA;IAAtDC,IAAI,EAAE,+BAA+B;IAAEjB,KAAK,EAAC;kCACjEM,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAKS;IAJPN,KAAK,EAAC,wNAAwN;IAC7NU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAK,oBAAA,IAAAL,QAAA,CAAAK,oBAAA,IAAAN,IAAA,CAAoB;MAC5BG,YAAA,CAAyDC,4BAAA;IAArCC,IAAI,EAAE,cAAc;IAAEjB,KAAK,EAAC;kCAChDM,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAKS;IAJPN,KAAK,EAAC,8NAA8N;IACnOU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAM,mBAAA,IAAAN,QAAA,CAAAM,mBAAA,IAAAP,IAAA,CAAmB;MAC3BG,YAAA,CAAgEC,4BAAA;IAA5CC,IAAI,EAAE,qBAAqB;IAAEjB,KAAK,EAAC;kCACvDM,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAKS;IAJPN,KAAK,EAAC,2NAA2N;IAChOU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAO,wBAAA,IAAAP,QAAA,CAAAO,wBAAA,IAAAR,IAAA,CAAwB;MAChCG,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEjB,KAAK,EAAC;kCAClDM,mBAAA,CAAmB,cAAb,QAAM,qB,KAIhBA,mBAAA,CAoDM,OApDNe,UAoDM,GAnDJhB,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNgB,UAaM,GAZJhB,mBAAA,CAKS;IALDN,KAAK,EAnCzBuB,eAAA,EAmC0B,8BAA8B;MAAA,0BACNC,KAAA,CAAAC,QAAQ;MAAA,6BAA2CD,KAAA,CAAAC,QAAQ;IAAA;IAC9Ff,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAe,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAChBV,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEjB,KAAK,EAAC;kCAtChE2B,gBAAA,CAsCyE,MAE7D,G,kBACArB,mBAAA,CAKS;IALDN,KAAK,EAzCzBuB,eAAA,EAyC0B,8BAA8B;MAAA,0BACNC,KAAA,CAAAC,QAAQ;MAAA,6BAA0CD,KAAA,CAAAC,QAAQ;IAAA;IAC7Ff,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAe,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAChBV,YAAA,CAA8DC,4BAAA;IAA1CC,IAAI,EAAE,mBAAmB;IAAEjB,KAAK,EAAC;kCA5CnE2B,gBAAA,CA4C4E,MAEhE,G,oBAGFtB,mBAAA,QAAW,EACXC,mBAAA,CAMM,OANNsB,UAMM,G,gBALJtB,mBAAA,CAC2L;IADpLuB,IAAI,EAAC,MAAM;IAnD9B,uBAAAlB,MAAA,QAAAA,MAAA,MAAAe,MAAA,IAmDwCF,KAAA,CAAAM,UAAU,GAAAJ,MAAA;IAAEK,WAAW,EAAC,SAAS;IAC3D/B,KAAK,EAAC;iDADoBwB,KAAA,CAAAM,UAAU,E,GAEtCxB,mBAAA,CAEM,OAFN0B,UAEM,GADJjB,YAAA,CAAqEC,4BAAA;IAAjDC,IAAI,EAAE,iBAAiB;IAAEjB,KAAK,EAAC;UAIvDK,mBAAA,UAAa,EACbC,mBAAA,CAMM,OANN2B,UAMM,G,gBALJ3B,mBAAA,CAC2L;IADpLuB,IAAI,EAAC,MAAM;IA5D9B,uBAAAlB,MAAA,QAAAA,MAAA,MAAAe,MAAA,IA4DwCF,KAAA,CAAAU,iBAAiB,GAAAR,MAAA;IAAEK,WAAW,EAAC,SAAS;IAClE/B,KAAK,EAAC;iDADoBwB,KAAA,CAAAU,iBAAiB,E,GAE7C5B,mBAAA,CAEM,OAFN6B,UAEM,GADJpB,YAAA,CAAmEC,4BAAA;IAA/CC,IAAI,EAAE,eAAe;IAAEjB,KAAK,EAAC;UAIrDK,mBAAA,UAAa,E,gBACbC,mBAAA,CAMS;IA1EnB,uBAAAK,MAAA,QAAAA,MAAA,MAAAe,MAAA,IAoE2BF,KAAA,CAAAY,YAAY,GAAAV,MAAA;IAC3B1B,KAAK,EAAC;kCACNM,mBAAA,CAAiC;IAAzB+B,KAAK,EAAC;EAAK,GAAC,MAAI,qBACxB/B,mBAAA,CAAkC;IAA1B+B,KAAK,EAAC;EAAQ,GAAC,IAAE,qBACzB/B,mBAAA,CAAmC;IAA3B+B,KAAK,EAAC;EAAS,GAAC,IAAE,qBAC1B/B,mBAAA,CAAiC;IAAzB+B,KAAK,EAAC;EAAO,GAAC,IAAE,oB,2CALTb,KAAA,CAAAY,YAAY,E,GAQ7B/B,mBAAA,cAAiB,E,gBACjBC,mBAAA,CAMS;IAnFnB,uBAAAK,MAAA,QAAAA,MAAA,MAAAe,MAAA,IA6E2BF,KAAA,CAAAc,YAAY,GAAAZ,MAAA;IAC3B1B,KAAK,EAAC;kCACNM,mBAAA,CAAiC;IAAzB+B,KAAK,EAAC;EAAK,GAAC,MAAI,qBACxB/B,mBAAA,CAAoC;IAA5B+B,KAAK,EAAC;EAAS,GAAC,KAAG,qBAC3B/B,mBAAA,CAA2C;IAAnC+B,KAAK,EAAC;EAAe,GAAC,MAAI,qBAClC/B,mBAAA,CAAmC;IAA3B+B,KAAK,EAAC;EAAO,GAAC,MAAI,oB,2CALXb,KAAA,CAAAc,YAAY,E,SAWnCjC,mBAAA,UAAa,EACbA,mBAAA,UAAa,EACFmB,KAAA,CAAAC,QAAQ,gB,cAAnBrB,mBAAA,CAsJM,OAtJNmC,WAsJM,GArJJlC,mBAAA,eAAkB,EAClBC,mBAAA,CAiBM,OAjBNkC,WAiBM,GAhBJlC,mBAAA,CAGM,OAHNmC,WAGM,G,4BAhGdd,gBAAA,CA6F2C,MAC9B,IAAArB,mBAAA,CAA8D,QAA9DoC,WAA8D,EAAAC,gBAAA,CAAjC9B,QAAA,CAAA+B,gBAAgB,CAACC,MAAM,kB,4BA9FjElB,gBAAA,CA8F2E,UAC9D,IAAArB,mBAAA,CAA4D,QAA5DwC,WAA4D,EAAAH,gBAAA,CAA/B9B,QAAA,CAAAkC,cAAc,CAACF,MAAM,kB,4BA/F/DlB,gBAAA,CA+FyE,MACjE,G,GACArB,mBAAA,CAWM,OAXN0C,WAWM,GAVJ1C,mBAAA,CAIS,UAJT2C,WAIS,GAFPlC,YAAA,CAAiEC,4BAAA;IAA7CC,IAAI,EAAE,sBAAsB;IAAEjB,KAAK,EAAC;kCApGpE2B,gBAAA,CAoG6E,MAEnE,G,GACArB,mBAAA,CAIS,UAJT4C,WAIS,GAFPnC,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEjB,KAAK,EAAC;kCAzG9D2B,gBAAA,CAyGuE,MAE7D,G,OAIJrB,mBAAA,CAgIQ,SAhIR6C,WAgIQ,GA/HN7C,mBAAA,CAgCQ,SAhCR8C,WAgCQ,GA/BN9C,mBAAA,CA8BK,aA7BHA,mBAAA,CAIK,MAJL+C,WAIK,GAHHtC,YAAA,CAEiBuC,yBAAA;IArH/BC,UAAA,EAmHuC/B,KAAA,CAAAgC,SAAS;IAnHhD,wB,sCAmHuChC,KAAA,CAAAgC,SAAS,GAAA9B,MAAA,GAAsBb,QAAA,CAAA4C,eAAe;;IAnHrFC,OAAA,EAAAC,QAAA,CAmHuF,MAEzEhD,MAAA,SAAAA,MAAA,QArHdgB,gBAAA,CAmHuF,OAEzE,E;IArHdiC,CAAA;0FAuHYtD,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,QAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,YAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,UAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,qB,KAGJM,mBAAA,CA6FQ,SA7FRuD,WA6FQ,GA5FNxD,mBAAA,aAAgB,G,kBAChBD,mBAAA,CAiFW0D,SAAA,QApOrBC,WAAA,CAmJwClD,QAAA,CAAAmD,eAAe,EAA5BC,SAAS;yBAnJpC7D,mBAAA,CAAA0D,SAAA;MAAA7D,GAAA,EAmJ+DgE,SAAS,CAACC;QAC7D7D,mBAAA,SAAY,G,kBACZD,mBAAA,CA8EK0D,SAAA,QAnOjBC,WAAA,CAqJkDE,SAAS,CAACE,QAAQ,EArJpE,CAqJwBC,OAAO,EAAEC,YAAY;2BAAjCjE,mBAAA,CA8EK;QA9EsDH,GAAG,EAAEmE,OAAO,CAACE,EAAE;QACvEtE,KAAK,EAtJpBuB,eAAA;UAAA,cAsJsC8C,YAAY;UAAA;QAAA;UACpC/D,mBAAA,CAMK,MANLiE,WAMK,GALHjE,mBAAA,CAIM,OAJNkE,WAIM,GAHJzD,YAAA,CAEiBuC,yBAAA;QA3JnCC,UAAA,EAyJ2Ca,OAAO,CAACK,IAAI,CAACC,QAAQ;QAzJhE,uBAAAhD,MAAA,IAyJ2C0C,OAAO,CAACK,IAAI,CAACC,QAAQ,GAAAhD,MAAA;QAAE1B,KAAK,EAAC;;QAzJxE0D,OAAA,EAAAC,QAAA,CA0JoB,MAA2E,CAA3ErD,mBAAA,CAA2E,QAA3EqE,WAA2E,EAAAhC,gBAAA,CAA3ByB,OAAO,CAACK,IAAI,CAACG,IAAI,iB;QA1JrFhB,CAAA;sFA8JctD,mBAAA,CAEK,MAFLuE,WAEK,GADHvE,mBAAA,CAA8D,OAA9DwE,WAA8D,EAAAnC,gBAAA,CAAxByB,OAAO,CAACK,IAAI,CAACM,EAAE,iB,GAEvDzE,mBAAA,CAQK,MARL0E,WAQK,GAPH1E,mBAAA,CAMM,OANN2E,WAMM,GALJ3E,mBAAA,CAA6E,QAA7E4E,WAA6E,EAAAvC,gBAAA,CAA1ByB,OAAO,CAACe,QAAQ,kBACvDf,OAAO,CAACgB,SAAS,I,cAA7BhF,mBAAA,CAGO,QAHPiF,WAGO,EAFqG,MAE5G,KAvKlBhF,mBAAA,e,KA0KcC,mBAAA,CAEK,MAFLgF,WAEK,GADHhF,mBAAA,CAAgF,OAAhFiF,WAAgF,EAAA5C,gBAAA,CAA1CyB,OAAO,CAACoB,kBAAkB,wB,GAElElF,mBAAA,CAiBK,MAjBLmF,WAiBK,GAhBHnF,mBAAA,CAeM;QAfAN,KAAK,EA9K3BuB,eAAA;;qCA8KiLV,QAAA,CAAA6E,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM,iBAAiB9E,QAAA,CAAA6E,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM;2CAAoE9E,QAAA,CAAA6E,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM;uCAAgE9E,QAAA,CAAA6E,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM;;UA9K1chE,gBAAA,CAAAgB,gBAAA,CAoLqB9B,QAAA,CAAA6E,iBAAiB,CAACtB,OAAO,EAAEwB,IAAI,IAAG,GACrC,iBACQ/E,QAAA,CAAA6E,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM,kBAAkB9E,QAAA,CAAA6E,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM,iB,cAD5FvF,mBAAA,CAIO,QAJPyF,WAIO,GADL9E,YAAA,CAA6DC,4BAAA;QAAzCC,IAAI,EAAE;MAA+B,G,KAE1CJ,QAAA,CAAA6E,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM,kB,cAAlDvF,mBAAA,CAEO,QAFP0F,WAEO,GADL/E,YAAA,CAA2DC,4BAAA;QAAvCC,IAAI,EAAE;MAA6B,G,KA3L3EZ,mBAAA,e,oBA+LcC,mBAAA,CAaK,MAbLyF,WAaK,GAZHzF,mBAAA,CAWM,OAXN0F,WAWM,GAVJ1F,mBAAA,CAIM,OAJN2F,WAIM,GAHJ3F,mBAAA,CAEkG;QAF1FuB,IAAI,EAAEL,KAAA,CAAA0E,kBAAkB,CAAC9B,OAAO,CAACE,EAAE;QAA0BjC,KAAK,EAAE+B,OAAO,CAAC+B,QAAQ;QAC1FC,QAAQ,EAAR,EAAQ;QACRpG,KAAK,EAAC;8BApM5BqG,WAAA,E,GAsMkB/F,mBAAA,CAIS;QAJAI,OAAK,EAAAgB,MAAA,IAAEb,QAAA,CAAAyF,wBAAwB,CAAClC,OAAO,CAACE,EAAE;QACjDtE,KAAK,EAAC;UACNe,YAAA,CACoBC,4BAAA;QADAC,IAAI,UAAUO,KAAA,CAAA0E,kBAAkB,CAAC9B,OAAO,CAACE,EAAE;QAC7DtE,KAAK,EAAC;yDAzM5BuG,WAAA,E,KA6McjG,mBAAA,CAEK,MAFLkG,WAEK,GADHzF,YAAA,CAA2C0F,sBAAA;QAA7B5E,IAAI,EAAEuC,OAAO,CAACK,IAAI,CAACkB;2CAEnCrF,mBAAA,CAEK,MAFLoG,WAEK,EAAA/D,gBAAA,CADA9B,QAAA,CAAA8F,aAAa,CAACvC,OAAO,CAACwC,QAAQ,mBAEnCtG,mBAAA,CAeK,MAfLuG,WAeK,GAdHvG,mBAAA,CAaM,OAbNwG,WAaM,GAZJxG,mBAAA,CAKS;QAJPN,KAAK,EAAC,0NAA0N;QAC/NU,OAAK,EAAAgB,MAAA,IAAEb,QAAA,CAAAkG,uBAAuB,CAAC3C,OAAO,CAACK,IAAI,EAAEL,OAAO;UACrDrD,YAAA,CAAyDC,4BAAA;QAArCC,IAAI,EAAE,cAAc;QAAEjB,KAAK,EAAC;sCAxNpE2B,gBAAA,CAwN6E,QAE3D,G,iBA1NlBqF,WAAA,GA2NkB1G,mBAAA,CAKS;QAJPN,KAAK,EAAC,sNAAsN;QAC3NU,OAAK,EAAAgB,MAAA,IAAEb,QAAA,CAAAoG,YAAY,CAAC7C,OAAO;UAC5BrD,YAAA,CAA0DC,4BAAA;QAAtCC,IAAI,EAAE,eAAe;QAAEjB,KAAK,EAAC;sCA9NrE2B,gBAAA,CA8N8E,MAE5D,G,iBAhOlBuF,WAAA,E;;kCAqOU7G,mBAAA,WAAc,EACJQ,QAAA,CAAA+B,gBAAgB,CAACC,MAAM,U,cAAjCzC,mBAAA,CAOK,MA7Of+G,WAAA,GAuOY7G,mBAAA,CAKK,MALL8G,WAKK,GAJH9G,mBAAA,CAGM,OAHN+G,WAGM,GAFJtG,YAAA,CAAqEC,4BAAA;IAAjDC,IAAI,EAAE,iBAAiB;IAAEjB,KAAK,EAAC;kCACnDM,mBAAA,CAAkB,WAAf,aAAW,qB,SA1O9BD,mBAAA,e,wBAmPID,mBAAA,CA4FM0D,SAAA;IA/UV7D,GAAA;EAAA,IAkPII,mBAAA,UAAa,EACbC,mBAAA,CA4FM,OA5FNgH,WA4FM,I,kBA3FJlH,mBAAA,CA0FM0D,SAAA,QA9UZC,WAAA,CAoP0BlD,QAAA,CAAA0G,aAAa,EAArB9C,IAAI;yBAAhBrE,mBAAA,CA0FM;MA1F8BH,GAAG,EAAEwE,IAAI,CAACH,EAAE;MAAEtE,KAAK,EAAC;QACtDM,mBAAA,CAwFM,OAxFNkH,WAwFM,GAvFJnH,mBAAA,UAAa,EACbC,mBAAA,CASM,OATNmH,WASM,GARJnH,mBAAA,CAMM,OANNoH,WAMM,GALJ3G,YAAA,CAAuDuC,yBAAA;MAzPrEC,UAAA,EAyPuCkB,IAAI,CAACC,QAAQ;MAzPpD,uBAAAhD,MAAA,IAyPuC+C,IAAI,CAACC,QAAQ,GAAAhD,MAAA;MAAE1B,KAAK,EAAC;oEAC9CM,mBAAA,CAGM,cAFJA,mBAAA,CAAkE,MAAlEqH,WAAkE,EAAAhF,gBAAA,CAAjB8B,IAAI,CAACG,IAAI,kBAC1DtE,mBAAA,CAAkD,KAAlDsH,WAAkD,EAAAjF,gBAAA,CAAd8B,IAAI,CAACM,EAAE,iB,KAG/ChE,YAAA,CAAmC0F,sBAAA;MAArB5E,IAAI,EAAE4C,IAAI,CAACkB;yCAG3BtF,mBAAA,UAAa,EACbC,mBAAA,CA+DM,OA/DNuH,WA+DM,I,kBA9DJzH,mBAAA,CA6DM0D,SAAA,QAjUlBC,WAAA,CAoQmCU,IAAI,CAACN,QAAQ,EAAxBC,OAAO;2BAAnBhE,mBAAA,CA6DM;QA7DiCH,GAAG,EAAEmE,OAAO,CAACE,EAAE;QAAEtE,KAAK,EApQzEuB,eAAA,EAoQ0E,uCAAuC;UAAA,gCACzD6C,OAAO,CAACgB;QAAS;UAC3D9E,mBAAA,CAcM,OAdNwH,WAcM,GAbJxH,mBAAA,CAMM,OANNyH,WAMM,GALJzH,mBAAA,CAA6E,QAA7E0H,WAA6E,EAAArF,gBAAA,CAA1ByB,OAAO,CAACe,QAAQ,kBACvDf,OAAO,CAACgB,SAAS,I,cAA7BhF,mBAAA,CAGO,QAHP6H,WAGO,EAFqG,MAE5G,KA5QlB5H,mBAAA,e,GA8QgBC,mBAAA,CAKS;QAJPN,KAAK,EAAC,wNAAwN;QAC7NU,OAAK,EAAAgB,MAAA,IAAEb,QAAA,CAAAkG,uBAAuB,CAACtC,IAAI,EAAEL,OAAO;UAC7CrD,YAAA,CAAyDC,4BAAA;QAArCC,IAAI,EAAE,cAAc;QAAEjB,KAAK,EAAC;sCAjRlE2B,gBAAA,CAiR2E,QAE3D,G,iBAnRhBuG,WAAA,E,GAsRc7H,mBAAA,UAAa,EACbC,mBAAA,CAWM,OAXN6H,WAWM,G,4BAVJ7H,mBAAA,CAA4D;QAAvDN,KAAK,EAAC;MAAwC,GAAC,IAAE,sBACtDM,mBAAA,CAQM,OARN8H,WAQM,GAPJ9H,mBAAA,CACkG;QAD1FuB,IAAI,EAAEL,KAAA,CAAA0E,kBAAkB,CAAC9B,OAAO,CAACE,EAAE;QAA0BjC,KAAK,EAAE+B,OAAO,CAAC+B,QAAQ;QAAEC,QAAQ,EAAR,EAAQ;QACpGpG,KAAK,EAAC;8BA3R1BqI,WAAA,GA4RkB/H,mBAAA,CAIS;QAJAI,OAAK,EAAAgB,MAAA,IAAEb,QAAA,CAAAyF,wBAAwB,CAAClC,OAAO,CAACE,EAAE;QACjDtE,KAAK,EAAC;UACNe,YAAA,CACoBC,4BAAA;QADAC,IAAI,UAAUO,KAAA,CAAA0E,kBAAkB,CAAC9B,OAAO,CAACE,EAAE;QAC7DtE,KAAK,EAAC;yDA/R5BsI,WAAA,E,KAoScjI,mBAAA,YAAe,EACfC,mBAAA,CA2BM,OA3BNiI,WA2BM,GA1BJjI,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAwD;QAAnDN,KAAK,EAAC;MAAgC,GAAC,QAAM,sBAClDM,mBAAA,CAAwE,OAAxEkI,WAAwE,EAAA7F,gBAAA,CAA1CyB,OAAO,CAACoB,kBAAkB,wB,GAE1DlF,mBAAA,CAeM,c,4BAdJA,mBAAA,CAAsD;QAAjDN,KAAK,EAAC;MAAgC,GAAC,MAAI,sBAChDM,mBAAA,CAYM;QAZAN,KAAK,EA5S7BuB,eAAA;;qCA4SqLV,QAAA,CAAA6E,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM,iBAAiB9E,QAAA,CAAA6E,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM;2CAAsE9E,QAAA,CAAA6E,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM;uCAAkE9E,QAAA,CAAA6E,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM;;UA5SldhE,gBAAA,CAAAgB,gBAAA,CAkTuB9B,QAAA,CAAA6E,iBAAiB,CAACtB,OAAO,EAAEwB,IAAI,IAAG,GACrC,iBACQ/E,QAAA,CAAA6E,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM,kBAAkB9E,QAAA,CAAA6E,iBAAiB,CAACtB,OAAO,EAAEuB,MAAM,iB,cAD5FvF,mBAAA,CAIO,QAJPqI,WAIO,GADL1H,YAAA,CAA6DC,4BAAA;QAAzCC,IAAI,EAAE;MAA+B,G,KAtT/EZ,mBAAA,e,oBA0TgBC,mBAAA,CAKM,OALNoI,WAKM,G,4BAJJpI,mBAAA,CAAsD;QAAjDN,KAAK,EAAC;MAAgC,GAAC,MAAI,sBAChDM,mBAAA,CAEM,OAFNqI,WAEM,EAAAhG,gBAAA,CADD9B,QAAA,CAAA8F,aAAa,CAACvC,OAAO,CAACwC,QAAQ,kB;sCAO3CvG,mBAAA,YAAe,EACfC,mBAAA,CAOM,OAPNsI,WAOM,GANJtI,mBAAA,CAKS;MAJPN,KAAK,EAAC,sNAAsN;MAC3NU,OAAK,EAAAgB,MAAA,IAAEb,QAAA,CAAAgI,mBAAmB,CAACpE,IAAI;QAChC1D,YAAA,CAA0DC,4BAAA;MAAtCC,IAAI,EAAE,eAAe;MAAEjB,KAAK,EAAC;oCAzU/D2B,gBAAA,CAyUwE,QAE5D,G,iBA3UZmH,WAAA,E;sFAiVIzI,mBAAA,YAAe,EACfU,YAAA,CAkGYgI,oBAAA;IApbhBxF,UAAA,EAkVwB/B,KAAA,CAAAwH,mBAAmB,CAACC,IAAI;IAlVhD,uBAAAtI,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAkVwBF,KAAA,CAAAwH,mBAAmB,CAACC,IAAI,GAAAvH,MAAA;IAAEwH,KAAK,EAAC,MAAM;IAAEC,SAAO,EAAEtI,QAAA,CAAAuI,cAAc;IAAGC,OAAO,EAAE7H,KAAA,CAAA8H;;IAlVnG5F,OAAA,EAAAC,QAAA,CAmVM,MAOM,CAPNrD,mBAAA,CAOM,OAPNiJ,WAOM,G,8BANJjJ,mBAAA,CAAwC;MAAnCN,KAAK,EAAC;IAAkB,GAAC,MAAI,sBAClCM,mBAAA,CAIM,OAJNkJ,WAIM,GAHJlJ,mBAAA,CAAuE,c,4BAAlEA,mBAAA,CAAqC;MAA/BN,KAAK,EAAC;IAAa,GAAC,MAAI,sBAtV7C2B,gBAAA,CAsVoD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAiI,WAAW,CAAC7E,IAAI,iB,GAC9DtE,mBAAA,CAAsE,c,4BAAjEA,mBAAA,CAAsC;MAAhCN,KAAK,EAAC;IAAa,GAAC,OAAK,sBAvV9C2B,gBAAA,CAuVqD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAiI,WAAW,CAAC1E,EAAE,iB,GAC7DzE,mBAAA,CAA6E,c,4BAAxEA,mBAAA,CAAoC;MAA9BN,KAAK,EAAC;IAAa,GAAC,KAAG,sBAxV5C2B,gBAAA,CAwVmD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAkI,cAAc,CAACvE,QAAQ,iB,OAIxE7E,mBAAA,CAgBM,OAhBNqJ,WAgBM,G,8BAfJrJ,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAaM,OAbNsJ,WAaM,GAZJtJ,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAAwH,mBAAmB,CAACa,MAAM;MACxC7J,KAAK,EAhWjBuB,eAAA,EAgWkB,iFAAiF,EAC/EC,KAAA,CAAAwH,mBAAmB,CAACa,MAAM;QAClC9I,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;sCAlWjE2B,gBAAA,CAkW0E,QAEhE,G,kBACArB,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAAwH,mBAAmB,CAACa,MAAM;MACxC7J,KAAK,EAtWjBuB,eAAA,EAsWkB,iFAAiF,EAC/EC,KAAA,CAAAwH,mBAAmB,CAACa,MAAM;QAClC9I,YAAA,CAA0DC,4BAAA;MAAtCC,IAAI,EAAE,eAAe;MAAEjB,KAAK,EAAC;sCAxW7D2B,gBAAA,CAwWsE,QAE5D,G,sBAIOH,KAAA,CAAAwH,mBAAmB,CAACa,MAAM,e,cAArCzJ,mBAAA,CA0BM,OA1BN0J,WA0BM,G,8BAzBJxJ,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MApXjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAgXyBF,KAAA,CAAAwH,mBAAmB,CAACpC,QAAQ,GAAAlF,MAAA;MAAE1B,KAAK,EAAC,aAAa;MAAE+J,QAAM,EAAApJ,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEb,QAAA,CAAAmJ,gBAAgB;2BAC1F5J,mBAAA,CAES0D,SAAA,QAnXnBC,WAAA,CAiXmCkG,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArB/J,mBAAA,CAES;QAF2BH,GAAG,EAAEkK,MAAM,CAAC7F,EAAE;QAAGjC,KAAK,EAAE8H,MAAM,CAAC7F;0BAC9D6F,MAAM,CAACvF,IAAI,IAAG,UAAQ,GAAAjC,gBAAA,CAAGwH,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAzH,gBAAA,CAAGwH,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAnXVC,WAAA;6FAgXyB9I,KAAA,CAAAwH,mBAAmB,CAACpC,QAAQ,E,GAM7CtG,mBAAA,CAiBM,OAjBNiK,WAiBM,GAhBJjK,mBAAA,CAOM,OAPNkK,WAOM,G,8BANJlK,mBAAA,CAAuC;MAAhCN,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BM,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEb,QAAA,CAAAmJ,gBAAgB;MAAInI,IAAI,EAAC,QAAQ;MAC/C7B,KAAK,EAAC;QACNe,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;sCA3XnE2B,gBAAA,CA2X4E,QAEhE,G,KAEFrB,mBAAA,CAOM,OAPNmK,WAOM,G,gBANJnK,mBAAA,CACoG;MAD5FuB,IAAI,EAAEL,KAAA,CAAA0E,kBAAkB,CAACwE,SAAS;MAhYtD,uBAAA/J,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAiYuBF,KAAA,CAAAwH,mBAAmB,CAAC2B,iBAAiB,GAAAjJ,MAAA;MAAE0E,QAAQ,EAAR,EAAQ;MAACpG,KAAK,EAAC;4BAjY7E4K,WAAA,I,iBAiYuBpJ,KAAA,CAAAwH,mBAAmB,CAAC2B,iBAAiB,E,GAChDrK,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAA0E,kBAAkB,CAACwE,SAAS,IAAIlJ,KAAA,CAAA0E,kBAAkB,CAACwE,SAAS;MAAE7I,IAAI,EAAC,QAAQ;MACzF7B,KAAK,EAAC;QACNe,YAAA,CAAyGC,4BAAA;MAArFC,IAAI,UAAUO,KAAA,CAAA0E,kBAAkB,CAACwE,SAAS;MAAyB1K,KAAK,EAAC;gEAMrGI,mBAAA,CA4BM,OA5BNyK,WA4BM,GA3BJvK,mBAAA,CAUM,OAVNwK,WAUM,G,8BATJxK,mBAAA,CAAqC;MAA9BN,KAAK,EAAC;IAAY,GAAC,KAAG,sBAC7BM,mBAAA,CAOM,OAPNyK,WAOM,G,gBANJzK,mBAAA,CACoD;MAD5CuB,IAAI,EAAEL,KAAA,CAAA0E,kBAAkB,CAAC8E,GAAG;MA9YhD,uBAAArK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA8YiFF,KAAA,CAAAwH,mBAAmB,CAACiC,WAAW,GAAAvJ,MAAA;MAClG1B,KAAK,EAAC,qBAAqB;MAAC+B,WAAW,EAAC;4BA/YtDmJ,WAAA,I,iBA8YiF1J,KAAA,CAAAwH,mBAAmB,CAACiC,WAAW,E,GAEpG3K,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAA0E,kBAAkB,CAAC8E,GAAG,IAAIxJ,KAAA,CAAA0E,kBAAkB,CAAC8E,GAAG;MAAEnJ,IAAI,EAAC,QAAQ;MAC7E7B,KAAK,EAAC;QACNe,YAAA,CAAmGC,4BAAA;MAA/EC,IAAI,UAAUO,KAAA,CAAA0E,kBAAkB,CAAC8E,GAAG;MAAyBhL,KAAK,EAAC;6CAK7FM,mBAAA,CAYM,OAZN6K,WAYM,G,8BAXJ7K,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CAQM,OARN8K,WAQM,G,gBAPJ9K,mBAAA,CAE0E;MAFlEuB,IAAI,EAAEL,KAAA,CAAA0E,kBAAkB,CAACmF,OAAO;MA1ZpD,uBAAA1K,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA2ZuBF,KAAA,CAAAwH,mBAAmB,CAACsC,eAAe,GAAA5J,MAAA;MAAE1B,KAAK,EA3ZjEuB,eAAA,EA2ZkE,qBAAqB;QAAA,kBAC7CV,QAAA,CAAA0K;MAAgB;MAAIxJ,WAAW,EAAC;oCA5Z1EyJ,WAAA,I,iBA2ZuBhK,KAAA,CAAAwH,mBAAmB,CAACsC,eAAe,E,GAE9ChL,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAA0E,kBAAkB,CAACmF,OAAO,IAAI7J,KAAA,CAAA0E,kBAAkB,CAACmF,OAAO;MAAExJ,IAAI,EAAC,QAAQ;MACrF7B,KAAK,EAAC;QACNe,YAAA,CAAuGC,4BAAA;MAAnFC,IAAI,UAAUO,KAAA,CAAA0E,kBAAkB,CAACmF,OAAO;MAAyBrL,KAAK,EAAC;2CAGpFa,QAAA,CAAA0K,gBAAgB,I,cAA3BnL,mBAAA,CAA+E,OAA/EqL,WAA+E,EAAhB,YAAU,KAlanFpL,mBAAA,e,GAqaQU,YAAA,CAAqE2K,gCAAA;MAA7CvF,QAAQ,EAAE3E,KAAA,CAAAwH,mBAAmB,CAACiC;8CAGxD3K,mBAAA,CAWM,OAXNqL,WAWM,G,8BAVJrL,mBAAA,CAA8C;MAAzCN,KAAK,EAAC;IAAwB,GAAC,MAAI,sBACxCe,YAAA,CAEiBuC,yBAAA;MA5azBC,UAAA,EA0aiC/B,KAAA,CAAAwH,mBAAmB,CAAC4C,kBAAkB;MA1avE,uBAAAjL,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA0aiCF,KAAA,CAAAwH,mBAAmB,CAAC4C,kBAAkB,GAAAlK,MAAA;;MA1avEgC,OAAA,EAAAC,QAAA,CA2aU,MAA8BhD,MAAA,UAAAA,MAAA,SAA9BL,mBAAA,CAA8B;QAAxBN,KAAK,EAAC;MAAM,GAAC,MAAI,oB;MA3ajC4D,CAAA;uCA6aQ7C,YAAA,CAEiBuC,yBAAA;MA/azBC,UAAA,EA6aiC/B,KAAA,CAAAwH,mBAAmB,CAAC6C,WAAW;MA7ahE,uBAAAlL,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA6aiCF,KAAA,CAAAwH,mBAAmB,CAAC6C,WAAW,GAAAnK,MAAA;;MA7ahEgC,OAAA,EAAAC,QAAA,CA8aU,MAAgChD,MAAA,UAAAA,MAAA,SAAhCL,mBAAA,CAAgC;QAA1BN,KAAK,EAAC;MAAM,GAAC,QAAM,oB;MA9anC4D,CAAA;uCAgbQ7C,YAAA,CAEiBuC,yBAAA;MAlbzBC,UAAA,EAgbiC/B,KAAA,CAAAwH,mBAAmB,CAAC8C,QAAQ;MAhb7D,uBAAAnL,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAgbiCF,KAAA,CAAAwH,mBAAmB,CAAC8C,QAAQ,GAAApK,MAAA;;MAhb7DgC,OAAA,EAAAC,QAAA,CAibU,MAAgChD,MAAA,UAAAA,MAAA,SAAhCL,mBAAA,CAAgC;QAA1BN,KAAK,EAAC;MAAM,GAAC,QAAM,oB;MAjbnC4D,CAAA;;IAAAA,CAAA;6DAsbIvD,mBAAA,YAAe,EACfU,YAAA,CAkDYgI,oBAAA;IAzehBxF,UAAA,EAubwB/B,KAAA,CAAAuK,eAAe,CAAC9C,IAAI;IAvb5C,uBAAAtI,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAubwBF,KAAA,CAAAuK,eAAe,CAAC9C,IAAI,GAAAvH,MAAA;IAAEwH,KAAK,EAAC,MAAM;IAAEC,SAAO,EAAEtI,QAAA,CAAAmL,UAAU;IAAG3C,OAAO,EAAE7H,KAAA,CAAA8H;;IAvb3F5F,OAAA,EAAAC,QAAA,CAwbM,MAMM,CANNrD,mBAAA,CAMM,OANN2L,WAMM,G,8BALJ3L,mBAAA,CAAwC;MAAnCN,KAAK,EAAC;IAAkB,GAAC,MAAI,sBAClCM,mBAAA,CAGM,OAHN4L,WAGM,GAFJ5L,mBAAA,CAAuE,c,8BAAlEA,mBAAA,CAAqC;MAA/BN,KAAK,EAAC;IAAa,GAAC,MAAI,sBA3b7C2B,gBAAA,CA2boD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAiI,WAAW,CAAC7E,IAAI,iB,GAC9DtE,mBAAA,CAAsE,c,8BAAjEA,mBAAA,CAAsC;MAAhCN,KAAK,EAAC;IAAa,GAAC,OAAK,sBA5b9C2B,gBAAA,CA4bqD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAiI,WAAW,CAAC1E,EAAE,iB,OAIjEzE,mBAAA,CAGM,OAHN6L,WAGM,G,8BAFJ7L,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAAkG;MAA3FuB,IAAI,EAAC,MAAM;MAlc1B,uBAAAlB,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAkcoCF,KAAA,CAAAuK,eAAe,CAAC5G,QAAQ,GAAAzD,MAAA;MAAE1B,KAAK,EAAC,cAAc;MAAC+B,WAAW,EAAC;mDAA3DP,KAAA,CAAAuK,eAAe,CAAC5G,QAAQ,E,KAGtD7E,mBAAA,CAOM,OAPN8L,WAOM,G,8BANJ9L,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAIM,OAJN+L,WAIM,G,gBAHJ/L,mBAAA,CACoI;MAD7HuB,IAAI,EAAC,UAAU;MAxchC,uBAAAlB,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAwc0CF,KAAA,CAAAuK,eAAe,CAAC3G,SAAS,GAAA1D,MAAA;MACvD1B,KAAK,EAAC;uDADwBwB,KAAA,CAAAuK,eAAe,CAAC3G,SAAS,E,iCAEzD9E,mBAAA,CAAsG;MAA/FN,KAAK,EAAC;IAAgF,4B,KAIjGM,mBAAA,CAOM,OAPNgM,WAOM,G,8BANJhM,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MApdjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAgdyBF,KAAA,CAAAuK,eAAe,CAACnF,QAAQ,GAAAlF,MAAA;MAAE1B,KAAK,EAAC,aAAa;MAAE+J,QAAM,EAAApJ,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEb,QAAA,CAAA0L,6BAA6B;2BACnGnM,mBAAA,CAES0D,SAAA,QAndnBC,WAAA,CAidmCkG,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArB/J,mBAAA,CAES;QAF2BH,GAAG,EAAEkK,MAAM,CAAC7F,EAAE;QAAGjC,KAAK,EAAE8H,MAAM,CAAC7F;0BAC9D6F,MAAM,CAACvF,IAAI,IAAG,UAAQ,GAAAjC,gBAAA,CAAGwH,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAzH,gBAAA,CAAGwH,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAndVmC,WAAA;6FAgdyBhL,KAAA,CAAAuK,eAAe,CAACnF,QAAQ,E,KAO3CtG,mBAAA,CAiBM,OAjBNmM,WAiBM,GAhBJnM,mBAAA,CAOM,OAPNoM,YAOM,G,8BANJpM,mBAAA,CAAuC;MAAhCN,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BM,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEb,QAAA,CAAA0L,6BAA6B;MAAI1K,IAAI,EAAC,QAAQ;MAC5D7B,KAAK,EAAC;QACNe,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;sCA5djE2B,gBAAA,CA4d0E,QAEhE,G,KAEFrB,mBAAA,CAOM,OAPNqM,YAOM,G,gBANJrM,mBAAA,CAC2C;MADnCuB,IAAI,EAAEL,KAAA,CAAA0E,kBAAkB,CAAC0G,UAAU;MAjerD,uBAAAjM,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAiesFF,KAAA,CAAAuK,eAAe,CAAC5F,QAAQ,GAAAzE,MAAA;MAAE0E,QAAQ,EAAR,EAAQ;MAC5GpG,KAAK,EAAC;4BAlelB6M,YAAA,I,iBAiesFrL,KAAA,CAAAuK,eAAe,CAAC5F,QAAQ,E,GAEpG7F,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAA0E,kBAAkB,CAAC0G,UAAU,IAAIpL,KAAA,CAAA0E,kBAAkB,CAAC0G,UAAU;MAAE/K,IAAI,EAAC,QAAQ;MAC3F7B,KAAK,EAAC;QACNe,YAAA,CAA0GC,4BAAA;MAAtFC,IAAI,UAAUO,KAAA,CAAA0E,kBAAkB,CAAC0G,UAAU;MAAyB5M,KAAK,EAAC;;IAre1G4D,CAAA;6DA2eIvD,mBAAA,cAAiB,EACjBU,YAAA,CAiEYgI,oBAAA;IA7iBhBxF,UAAA,EA4ewB/B,KAAA,CAAAsL,gBAAgB,CAAC7D,IAAI;IA5e7C,uBAAAtI,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA4ewBF,KAAA,CAAAsL,gBAAgB,CAAC7D,IAAI,GAAAvH,MAAA;IAAEwH,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAAC6D,IAAI,EAAC,IAAI;IACpF5D,SAAO,EAAEtI,QAAA,CAAAmM,oBAAoB;IAAG3D,OAAO,EAAE7H,KAAA,CAAA8H;;IA7ehD5F,OAAA,EAAAC,QAAA,CA8eM,MAaM,CAbNrD,mBAAA,CAaM,OAbN2M,YAaM,G,8BAZJ3M,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAIM,OAJN4M,YAIM,GAHJnM,YAAA,CAEiBuC,yBAAA;MAnf3BC,UAAA,EAifmC/B,KAAA,CAAA2L,cAAc;MAjfjD,wB,sCAifmC3L,KAAA,CAAA2L,cAAc,GAAAzL,MAAA,GAAsBb,QAAA,CAAAuM,oBAAoB;;MAjf3F1J,OAAA,EAAAC,QAAA,CAif6F,MAEnFhD,MAAA,UAAAA,MAAA,SAnfVgB,gBAAA,CAif6F,MAEnF,E;MAnfViC,CAAA;gEAqfQtD,mBAAA,CAIM,OAJN+M,YAIM,I,kBAHJjN,mBAAA,CAEiB0D,SAAA,QAxf3BC,WAAA,CAsfyCkG,IAAA,CAAAqD,KAAK,EAAb7I,IAAI;2BAA3B8I,YAAA,CAEiBjK,yBAAA;QAFsBrD,GAAG,EAAEwE,IAAI,CAACH,EAAE;QAtf7Df,UAAA,EAsfwE/B,KAAA,CAAAsL,gBAAgB,CAACU,aAAa,CAAC/I,IAAI,CAACH,EAAE;QAtf9G,uBAAA5C,MAAA,IAsfwEF,KAAA,CAAAsL,gBAAgB,CAACU,aAAa,CAAC/I,IAAI,CAACH,EAAE,IAAA5C;;QAtf9GgC,OAAA,EAAAC,QAAA,CAufY,MAAe,CAvf3BhC,gBAAA,CAAAgB,gBAAA,CAufe8B,IAAI,CAACG,IAAI,IAAG,IAAE,GAAAjC,gBAAA,CAAG8B,IAAI,CAACM,EAAE,IAAG,IAChC,gB;QAxfVnB,CAAA;;sCA0fQtD,mBAAA,CAAyD,KAAzDmN,YAAyD,EAApC,MAAI,GAAA9K,gBAAA,CAAG9B,QAAA,CAAA6M,kBAAkB,IAAG,MAAI,gB,GAGvDpN,mBAAA,CAOM,OAPNqN,YAOM,G,8BANJrN,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MAngBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA+fyBF,KAAA,CAAAsL,gBAAgB,CAAClG,QAAQ,GAAAlF,MAAA;MAAE1B,KAAK,EAAC;2BAChDI,mBAAA,CAES0D,SAAA,QAlgBnBC,WAAA,CAggBmCkG,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArB/J,mBAAA,CAES;QAF2BH,GAAG,EAAEkK,MAAM,CAAC7F,EAAE;QAAGjC,KAAK,EAAE8H,MAAM,CAAC7F;0BAC9D6F,MAAM,CAACvF,IAAI,IAAG,UAAQ,GAAAjC,gBAAA,CAAGwH,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAzH,gBAAA,CAAGwH,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAlgBVuD,YAAA;6EA+fyBpM,KAAA,CAAAsL,gBAAgB,CAAClG,QAAQ,E,KAO5CtG,mBAAA,CAyBM,OAzBNuN,YAyBM,G,8BAxBJvN,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CASM,OATNwN,YASM,GARJxN,mBAAA,CAGQ,SAHRyN,YAGQ,G,gBAFNzN,mBAAA,CAA4F;MAArFuB,IAAI,EAAC,OAAO;MA1gB/B,uBAAAlB,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA0gByCF,KAAA,CAAAsL,gBAAgB,CAACkB,aAAa,GAAAtM,MAAA;MAAEW,KAAK,EAAC,WAAW;MAACrC,KAAK,EAAC;oDAAxDwB,KAAA,CAAAsL,gBAAgB,CAACkB,aAAa,E,iCAC3D1N,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHR2N,YAGQ,G,gBAFN3N,mBAAA,CAA4F;MAArFuB,IAAI,EAAC,OAAO;MA9gB/B,uBAAAlB,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA8gByCF,KAAA,CAAAsL,gBAAgB,CAACkB,aAAa,GAAAtM,MAAA;MAAEW,KAAK,EAAC,WAAW;MAACrC,KAAK,EAAC;oDAAxDwB,KAAA,CAAAsL,gBAAgB,CAACkB,aAAa,E,iCAC3D1N,mBAAA,CAAiB,cAAX,MAAI,qB,KAIHkB,KAAA,CAAAsL,gBAAgB,CAACkB,aAAa,oB,cAAzC5N,mBAAA,CAWM,OAXN8N,YAWM,GAVJ5N,mBAAA,CASM,OATN6N,YASM,GARJ7N,mBAAA,CAGM,c,8BAFJA,mBAAA,CAAoC;MAA7BN,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BM,mBAAA,CAAiF;MAA1EuB,IAAI,EAAC,MAAM;MAvhBhC,uBAAAlB,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAuhB0CF,KAAA,CAAAsL,gBAAgB,CAACsB,aAAa,GAAA1M,MAAA;MAAE1B,KAAK,EAAC;mDAAtCwB,KAAA,CAAAsL,gBAAgB,CAACsB,aAAa,E,KAE5D9N,mBAAA,CAGM,c,8BAFJA,mBAAA,CAAoC;MAA7BN,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BM,mBAAA,CAAiF;MAA1EuB,IAAI,EAAC,MAAM;MA3hBhC,uBAAAlB,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA2hB0CF,KAAA,CAAAsL,gBAAgB,CAACuB,aAAa,GAAA3M,MAAA;MAAE1B,KAAK,EAAC;mDAAtCwB,KAAA,CAAAsL,gBAAgB,CAACuB,aAAa,E,WA3hBxEhO,mBAAA,e,GAiiBMC,mBAAA,CAWM,OAXNgO,YAWM,G,8BAVJhO,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Be,YAAA,CAEiBuC,yBAAA;MAriBzBC,UAAA,EAmiBiC/B,KAAA,CAAAsL,gBAAgB,CAACyB,YAAY;MAniB9D,uBAAA5N,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAmiBiCF,KAAA,CAAAsL,gBAAgB,CAACyB,YAAY,GAAA7M,MAAA;;MAniB9DgC,OAAA,EAAAC,QAAA,CAmiBgE,MAExDhD,MAAA,UAAAA,MAAA,SAriBRgB,gBAAA,CAmiBgE,YAExD,E;MAriBRiC,CAAA;uCAsiBQ7C,YAAA,CAEiBuC,yBAAA;MAxiBzBC,UAAA,EAsiBiC/B,KAAA,CAAAsL,gBAAgB,CAAC0B,WAAW;MAtiB7D,uBAAA7N,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAsiBiCF,KAAA,CAAAsL,gBAAgB,CAAC0B,WAAW,GAAA9M,MAAA;;MAtiB7DgC,OAAA,EAAAC,QAAA,CAsiB+D,MAEvDhD,MAAA,UAAAA,MAAA,SAxiBRgB,gBAAA,CAsiB+D,UAEvD,E;MAxiBRiC,CAAA;uCAyiBQ7C,YAAA,CAEiBuC,yBAAA;MA3iBzBC,UAAA,EAyiBiC/B,KAAA,CAAAsL,gBAAgB,CAAC2B,gBAAgB;MAziBlE,uBAAA9N,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAyiBiCF,KAAA,CAAAsL,gBAAgB,CAAC2B,gBAAgB,GAAA/M,MAAA;;MAziBlEgC,OAAA,EAAAC,QAAA,CAyiBoE,MAE5DhD,MAAA,UAAAA,MAAA,SA3iBRgB,gBAAA,CAyiBoE,aAE5D,E;MA3iBRiC,CAAA;;IAAAA,CAAA;6DA+iBIvD,mBAAA,cAAiB,EACjBU,YAAA,CA8BYgI,oBAAA;IA9kBhBxF,UAAA,EAgjBwB/B,KAAA,CAAAkN,eAAe,CAACzF,IAAI;IAhjB5C,uBAAAtI,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAgjBwBF,KAAA,CAAAkN,eAAe,CAACzF,IAAI,GAAAvH,MAAA;IAAEwH,KAAK,EAAC,UAAU;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAEtI,QAAA,CAAA8N,gBAAgB;IACtGtF,OAAO,EAAE7H,KAAA,CAAA8H;;IAjjBhB5F,OAAA,EAAAC,QAAA,CAkjBM,MAQM,CARNrD,mBAAA,CAQM,OARNsO,YAQM,G,8BAPJtO,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAKM,OALNuO,YAKM,I,kBAJJzO,mBAAA,CAGiB0D,SAAA,QAxjB3BC,WAAA,CAqjByClD,QAAA,CAAAiO,iBAAiB,EAAzBrK,IAAI;2BAA3B8I,YAAA,CAGiBjK,yBAAA;QAHkCrD,GAAG,EAAEwE,IAAI,CAACH,EAAE;QArjBzEf,UAAA,EAsjBqB/B,KAAA,CAAAkN,eAAe,CAAClB,aAAa,CAAC/I,IAAI,CAACH,EAAE;QAtjB1D,uBAAA5C,MAAA,IAsjBqBF,KAAA,CAAAkN,eAAe,CAAClB,aAAa,CAAC/I,IAAI,CAACH,EAAE,IAAA5C;;QAtjB1DgC,OAAA,EAAAC,QAAA,CAujBY,MAAe,CAvjB3BhC,gBAAA,CAAAgB,gBAAA,CAujBe8B,IAAI,CAACG,IAAI,IAAG,IAAE,GAAAjC,gBAAA,CAAG8B,IAAI,CAACM,EAAE,IAAG,IAChC,gB;QAxjBVnB,CAAA;;wCA4jBMtD,mBAAA,CAOM,OAPNyO,YAOM,G,8BANJzO,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCM,mBAAA,CAIS;MAlkBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA8jByBF,KAAA,CAAAkN,eAAe,CAAC9H,QAAQ,GAAAlF,MAAA;MAAE1B,KAAK,EAAC;2BAC/CI,mBAAA,CAES0D,SAAA,QAjkBnBC,WAAA,CA+jBmCkG,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArB/J,mBAAA,CAES;QAF2BH,GAAG,EAAEkK,MAAM,CAAC7F,EAAE;QAAGjC,KAAK,EAAE8H,MAAM,CAAC7F;0BAC9D6F,MAAM,CAACvF,IAAI,wBAhkB1BoK,YAAA;6EA8jByBxN,KAAA,CAAAkN,eAAe,CAAC9H,QAAQ,E,KAO3CtG,mBAAA,CAQM,OARN2O,YAQM,G,8BAPJ3O,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Be,YAAA,CAEiBuC,yBAAA;MAzkBzBC,UAAA,EAukBiC/B,KAAA,CAAAkN,eAAe,CAACQ,iBAAiB;MAvkBlE,uBAAAvO,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAukBiCF,KAAA,CAAAkN,eAAe,CAACQ,iBAAiB,GAAAxN,MAAA;;MAvkBlEgC,OAAA,EAAAC,QAAA,CAukBoE,MAE5DhD,MAAA,UAAAA,MAAA,SAzkBRgB,gBAAA,CAukBoE,eAE5D,E;MAzkBRiC,CAAA;uCA0kBQ7C,YAAA,CAEiBuC,yBAAA;MA5kBzBC,UAAA,EA0kBiC/B,KAAA,CAAAkN,eAAe,CAACS,iBAAiB;MA1kBlE,uBAAAxO,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA0kBiCF,KAAA,CAAAkN,eAAe,CAACS,iBAAiB,GAAAzN,MAAA;;MA1kBlEgC,OAAA,EAAAC,QAAA,CA0kBoE,MAE5DhD,MAAA,UAAAA,MAAA,SA5kBRgB,gBAAA,CA0kBoE,aAE5D,E;MA5kBRiC,CAAA;;IAAAA,CAAA;6DAglBIvD,mBAAA,cAAiB,EACjBU,YAAA,CAyCYgI,oBAAA;IA1nBhBxF,UAAA,EAilBwB/B,KAAA,CAAA4N,mBAAmB,CAACnG,IAAI;IAjlBhD,uBAAAtI,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAilBwBF,KAAA,CAAA4N,mBAAmB,CAACnG,IAAI,GAAAvH,MAAA;IAAEwH,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAACjI,IAAI,EAAC,sBAAsB;IAACoO,MAAM,EAAN,EAAM;IAChHlG,SAAO,EAAEtI,QAAA,CAAAyO,cAAc;IAAGjG,OAAO,EAAE7H,KAAA,CAAA8H;;IAllB1C5F,OAAA,EAAAC,QAAA,CAmlBM,MAEM,C,8BAFNrD,mBAAA,CAEM;MAFDN,KAAK,EAAC;IAA4C,IACrDM,mBAAA,CAA+C,WAA5C,0CAAwC,E,sBAG7CA,mBAAA,CAQM,OARNiP,YAQM,G,8BAPJjP,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAKM,OALNkP,YAKM,I,kBAJJpP,mBAAA,CAGiB0D,SAAA,QA7lB3BC,WAAA,CA0lByClD,QAAA,CAAAiO,iBAAiB,EAAzBrK,IAAI;2BAA3B8I,YAAA,CAGiBjK,yBAAA;QAHkCrD,GAAG,EAAEwE,IAAI,CAACH,EAAE;QA1lBzEf,UAAA,EA2lBqB/B,KAAA,CAAA4N,mBAAmB,CAAC5B,aAAa,CAAC/I,IAAI,CAACH,EAAE;QA3lB9D,uBAAA5C,MAAA,IA2lBqBF,KAAA,CAAA4N,mBAAmB,CAAC5B,aAAa,CAAC/I,IAAI,CAACH,EAAE,IAAA5C;;QA3lB9DgC,OAAA,EAAAC,QAAA,CA4lBY,MAAe,CA5lB3BhC,gBAAA,CAAAgB,gBAAA,CA4lBe8B,IAAI,CAACG,IAAI,IAAG,IAAE,GAAAjC,gBAAA,CAAG8B,IAAI,CAACM,EAAE,IAAG,IAChC,gB;QA7lBVnB,CAAA;;wCAimBMtD,mBAAA,CAOM,OAPNmP,YAOM,G,8BANJnP,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCM,mBAAA,CAIS;MAvmBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAmmByBF,KAAA,CAAA4N,mBAAmB,CAACxI,QAAQ,GAAAlF,MAAA;MAAE1B,KAAK,EAAC;2BACnDI,mBAAA,CAES0D,SAAA,QAtmBnBC,WAAA,CAomBmClD,QAAA,CAAA6O,iBAAiB,EAA3BvF,MAAM;2BAArB/J,mBAAA,CAES;QAFoCH,GAAG,EAAEkK,MAAM,CAAC7F,EAAE;QAAGjC,KAAK,EAAE8H,MAAM,CAAC7F;0BACvE6F,MAAM,CAACvF,IAAI,IAAG,UAAQ,GAAAjC,gBAAA,CAAGwH,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAzH,gBAAA,CAAGwH,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAtmBVsF,YAAA;6EAmmByBnO,KAAA,CAAA4N,mBAAmB,CAACxI,QAAQ,E,KAO/CtG,mBAAA,CASM,OATNsP,YASM,G,8BARJtP,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAMS;MAlnBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA4mByBF,KAAA,CAAA4N,mBAAmB,CAACS,MAAM,GAAAnO,MAAA;MAAE1B,KAAK,EAAC;sCACjDM,mBAAA,CAAiD;MAAzC+B,KAAK,EAAC;IAAmB,GAAC,QAAM,qBACxC/B,mBAAA,CAA2C;MAAnC+B,KAAK,EAAC;IAAe,GAAC,MAAI,qBAClC/B,mBAAA,CAA6C;MAArC+B,KAAK,EAAC;IAAiB,GAAC,MAAI,qBACpC/B,mBAAA,CAAwC;MAAhC+B,KAAK,EAAC;IAAY,GAAC,MAAI,qBAC/B/B,mBAAA,CAAmC;MAA3B+B,KAAK,EAAC;IAAO,GAAC,MAAI,oB,2CALXb,KAAA,CAAA4N,mBAAmB,CAACS,MAAM,E,KAS7CvP,mBAAA,CAIM,OAJNwP,YAIM,G,8BAHJxP,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CACuC;MAxnB/C,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAunB2BF,KAAA,CAAA4N,mBAAmB,CAACW,WAAW,GAAArO,MAAA;MAAE1B,KAAK,EAAC,cAAc;MAACgQ,IAAI,EAAC,GAAG;MAC/EjO,WAAW,EAAC;mDADKP,KAAA,CAAA4N,mBAAmB,CAACW,WAAW,E;IAvnB1DnM,CAAA;6DA4nBIvD,mBAAA,cAAiB,EACjBU,YAAA,CAqGYgI,oBAAA;IAluBhBxF,UAAA,EA6nBwB/B,KAAA,CAAAyO,oBAAoB,CAAChH,IAAI;IA7nBjD,uBAAAtI,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA6nBwBF,KAAA,CAAAyO,oBAAoB,CAAChH,IAAI,GAAAvH,MAAA;IAAEwH,KAAK,EAAC,QAAQ;IAAC6D,IAAI,EAAC,IAAI;IAAE5D,SAAO,EAAEtI,QAAA,CAAAqP,gBAAgB;IAC/F7G,OAAO,EAAE7H,KAAA,CAAA8H;;IA9nBhB5F,OAAA,EAAAC,QAAA,CA+nBM,MAaM,CAbNrD,mBAAA,CAaM,OAbN6P,YAaM,G,8BAZJ7P,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAIM,OAJN8P,YAIM,GAHJrP,YAAA,CAEiBuC,yBAAA;MApoB3BC,UAAA,EAkoBmC/B,KAAA,CAAA6O,iBAAiB;MAloBpD,wB,sCAkoBmC7O,KAAA,CAAA6O,iBAAiB,GAAA3O,MAAA,GAAsBb,QAAA,CAAAyP,uBAAuB;;MAloBjG5M,OAAA,EAAAC,QAAA,CAkoBmG,MAEzFhD,MAAA,UAAAA,MAAA,SApoBVgB,gBAAA,CAkoBmG,MAEzF,E;MApoBViC,CAAA;gEAsoBQtD,mBAAA,CAIM,OAJNiQ,YAIM,I,kBAHJnQ,mBAAA,CAEiB0D,SAAA,QAzoB3BC,WAAA,CAuoByCkG,IAAA,CAAAqD,KAAK,EAAb7I,IAAI;2BAA3B8I,YAAA,CAEiBjK,yBAAA;QAFsBrD,GAAG,EAAEwE,IAAI,CAACH,EAAE;QAvoB7Df,UAAA,EAuoBwE/B,KAAA,CAAAyO,oBAAoB,CAACzC,aAAa,CAAC/I,IAAI,CAACH,EAAE;QAvoBlH,uBAAA5C,MAAA,IAuoBwEF,KAAA,CAAAyO,oBAAoB,CAACzC,aAAa,CAAC/I,IAAI,CAACH,EAAE,IAAA5C;;QAvoBlHgC,OAAA,EAAAC,QAAA,CAwoBY,MAAe,CAxoB3BhC,gBAAA,CAAAgB,gBAAA,CAwoBe8B,IAAI,CAACG,IAAI,IAAG,IAAE,GAAAjC,gBAAA,CAAG8B,IAAI,CAACM,EAAE,IAAG,IAChC,gB;QAzoBVnB,CAAA;;sCA2oBQtD,mBAAA,CAAiE,KAAjEkQ,YAAiE,EAA5C,MAAI,GAAA7N,gBAAA,CAAG9B,QAAA,CAAA4P,0BAA0B,IAAG,MAAI,gB,GAG/DnQ,mBAAA,CA8DM,OA9DNoQ,YA8DM,G,8BA7DJpQ,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CA2DM,OA3DNqQ,YA2DM,GA1DJrQ,mBAAA,CAIM,OAJNsQ,YAIM,G,8BAHJtQ,mBAAA,CAA0E;MAAnEN,KAAK,EAAC;IAAY,IAlpBrC2B,gBAAA,CAkpBsC,OAAK,GAAArB,mBAAA,CAAmC;MAA7BN,KAAK,EAAC;IAAc,GAAC,GAAC,E,sCAC3DM,mBAAA,CAAyG;MAAlGuB,IAAI,EAAC,MAAM;MAnpB9B,uBAAAlB,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAmpBwCF,KAAA,CAAAyO,oBAAoB,CAAC9K,QAAQ,GAAAzD,MAAA;MAAE1B,KAAK,EAAC,cAAc;MAAC+B,WAAW,EAAC;mDAAhEP,KAAA,CAAAyO,oBAAoB,CAAC9K,QAAQ,E,iCACzD7E,mBAAA,CAA6D;MAAxDN,KAAK,EAAC;IAA4B,GAAC,iBAAe,qB,GAGzDM,mBAAA,CAQM,OARNuQ,YAQM,G,8BAPJvQ,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAKS;MA9pBrB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAypB6BF,KAAA,CAAAyO,oBAAoB,CAACa,IAAI,GAAApP,MAAA;MAAE1B,KAAK,EAAC;sCAChDM,mBAAA,CAAkC;MAA1B+B,KAAK,EAAC;IAAO,GAAC,KAAG,qBACzB/B,mBAAA,CAAkC;MAA1B+B,KAAK,EAAC;IAAM,GAAC,MAAI,qBACzB/B,mBAAA,CAAqC;MAA7B+B,KAAK,EAAC;IAAS,GAAC,MAAI,qBAC5B/B,mBAAA,CAAsC;MAA9B+B,KAAK,EAAC;IAAU,GAAC,MAAI,oB,2CAJdb,KAAA,CAAAyO,oBAAoB,CAACa,IAAI,E,KAQ5CxQ,mBAAA,CAKM,OALNyQ,YAKM,GAJJzQ,mBAAA,CAGQ,SAHR0Q,YAGQ,G,gBAFN1Q,mBAAA,CAAyF;MAAlFuB,IAAI,EAAC,UAAU;MAnqBpC,uBAAAlB,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAmqB8CF,KAAA,CAAAyO,oBAAoB,CAACgB,YAAY,GAAAvP,MAAA;MAAE1B,KAAK,EAAC;uDAAzCwB,KAAA,CAAAyO,oBAAoB,CAACgB,YAAY,E,iCACjE3Q,mBAAA,CAAgC;MAA1BN,KAAK,EAAC;IAAM,GAAC,QAAM,qB,KAI7BM,mBAAA,CAgBM,OAhBN4Q,YAgBM,G,8BAfJ5Q,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAaM,OAbN6Q,YAaM,GAZJ7Q,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAAyO,oBAAoB,CAACmB,eAAe;MAClDpR,KAAK,EA5qBrBuB,eAAA,EA4qBsB,iFAAiF,EAC/EC,KAAA,CAAAyO,oBAAoB,CAACmB,eAAe;QAC5CrQ,YAAA,CAAyDC,4BAAA;MAArCC,IAAI,EAAE,cAAc;MAAEjB,KAAK,EAAC;sCA9qBhE2B,gBAAA,CA8qByE,QAE3D,G,kBACArB,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAAyO,oBAAoB,CAACmB,eAAe;MAClDpR,KAAK,EAlrBrBuB,eAAA,EAkrBsB,iFAAiF,GAC9EC,KAAA,CAAAyO,oBAAoB,CAACmB,eAAe;QAC7CrQ,YAAA,CAA4DC,4BAAA;MAAxCC,IAAI,EAAE,iBAAiB;MAAEjB,KAAK,EAAC;sCAprBnE2B,gBAAA,CAorB4E,QAE9D,G,sBAIOH,KAAA,CAAAyO,oBAAoB,CAACmB,eAAe,I,cAA/ChR,mBAAA,CAgBM,OAhBNiR,YAgBM,G,8BAfJ/Q,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CAaM,OAbNgR,YAaM,G,gBAZJhR,mBAAA,CAC4F;MADpFuB,IAAI,EAAEL,KAAA,CAAA0E,kBAAkB,CAACqL,aAAa;MA7rB5D,uBAAA5Q,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA8rByBF,KAAA,CAAAyO,oBAAoB,CAAC9J,QAAQ,GAAAzE,MAAA;MAAE0E,QAAQ,EAAR,EAAQ;MAACpG,KAAK,EAAC;4BA9rBvEwR,YAAA,I,iBA8rByBhQ,KAAA,CAAAyO,oBAAoB,CAAC9J,QAAQ,E,GACxC7F,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEF,KAAA,CAAA0E,kBAAkB,CAACqL,aAAa,IAAI/P,KAAA,CAAA0E,kBAAkB,CAACqL,aAAa;MAAE1P,IAAI,EAAC,QAAQ;MACjG7B,KAAK,EAAC;QACNe,YAAA,CACoBC,4BAAA;MADAC,IAAI,UAAUO,KAAA,CAAA0E,kBAAkB,CAACqL,aAAa;MAChEvR,KAAK,EAAC;yCAEVM,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEb,QAAA,CAAA4Q,+BAA+B;MAAI5P,IAAI,EAAC,QAAQ;MAC9D7B,KAAK,EAAC;QACNe,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;sCAtsBrE2B,gBAAA,CAssB8E,QAEhE,G,SAxsBdtB,mBAAA,e,KA8sBMC,mBAAA,CAOM,OAPNoR,YAOM,G,8BANJpR,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MAptBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAgtByBF,KAAA,CAAAyO,oBAAoB,CAACrJ,QAAQ,GAAAlF,MAAA;MAAE1B,KAAK,EAAC,aAAa;MAAE+J,QAAM,EAAApJ,MAAA,SAAAA,MAAA,OAAAe,MAAA,IAAEb,QAAA,CAAA4Q,+BAA+B;2BAC1GrR,mBAAA,CAES0D,SAAA,QAntBnBC,WAAA,CAitBmCkG,IAAA,CAAAC,QAAQ,EAAlBC,MAAM;2BAArB/J,mBAAA,CAES;QAF2BH,GAAG,EAAEkK,MAAM,CAAC7F,EAAE;QAAGjC,KAAK,EAAE8H,MAAM,CAAC7F;0BAC9D6F,MAAM,CAACvF,IAAI,IAAG,UAAQ,GAAAjC,gBAAA,CAAGwH,MAAM,CAACC,SAAS,IAAG,QAAM,GAAAzH,gBAAA,CAAGwH,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAntBVsH,YAAA;6FAgtByBnQ,KAAA,CAAAyO,oBAAoB,CAACrJ,QAAQ,E,KAOhDtG,mBAAA,CAUM,OAVNsR,YAUM,G,8BATJtR,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CAOM,OAPNuR,YAOM,GANJ9Q,YAAA,CAEiBuC,yBAAA;MA5tB3BC,UAAA,EA0tBmC/B,KAAA,CAAAyO,oBAAoB,CAAC1B,YAAY;MA1tBpE,uBAAA5N,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA0tBmCF,KAAA,CAAAyO,oBAAoB,CAAC1B,YAAY,GAAA7M,MAAA;;MA1tBpEgC,OAAA,EAAAC,QAAA,CA2tBY,MAAkChD,MAAA,UAAAA,MAAA,SAAlCL,mBAAA,CAAkC;QAA5BN,KAAK,EAAC;MAAM,GAAC,UAAQ,oB;MA3tBvC4D,CAAA;uCA6tBU7C,YAAA,CAEiBuC,yBAAA;MA/tB3BC,UAAA,EA6tBmC/B,KAAA,CAAAyO,oBAAoB,CAAC6B,cAAc;MA7tBtE,uBAAAnR,MAAA,SAAAA,MAAA,OAAAe,MAAA,IA6tBmCF,KAAA,CAAAyO,oBAAoB,CAAC6B,cAAc,GAAApQ,MAAA;;MA7tBtEgC,OAAA,EAAAC,QAAA,CA8tBY,MAAkChD,MAAA,UAAAA,MAAA,SAAlCL,mBAAA,CAAkC;QAA5BN,KAAK,EAAC;MAAM,GAAC,UAAQ,oB;MA9tBvC4D,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}