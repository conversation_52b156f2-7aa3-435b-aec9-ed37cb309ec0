<template>
  <div class="security-dashboard">
    <!-- 安全概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- 总主机数 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
              <font-awesome-icon :icon="['fas', 'server']" class="text-blue-600 dark:text-blue-400 text-xl" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">总主机数</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ totalHosts }}</p>
          </div>
        </div>
      </div>

      <!-- 密码过期警告 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
              <font-awesome-icon :icon="['fas', 'exclamation-triangle']" class="text-yellow-600 dark:text-yellow-400 text-xl" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">即将过期</p>
            <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ expiringPasswords }}</p>
          </div>
        </div>
      </div>

      <!-- 弱密码数量 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
              <font-awesome-icon :icon="['fas', 'shield-alt']" class="text-red-600 dark:text-red-400 text-xl" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">弱密码</p>
            <p class="text-2xl font-bold text-red-600 dark:text-red-400">{{ weakPasswords }}</p>
          </div>
        </div>
      </div>

      <!-- 安全评分 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
              <font-awesome-icon :icon="['fas', 'check-circle']" class="text-green-600 dark:text-green-400 text-xl" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">安全评分</p>
            <p class="text-2xl font-bold" :class="securityScoreClass">{{ securityScore }}/100</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 安全趋势图表和风险分析 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- 密码状态分布 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">密码状态分布</h3>
        <div class="space-y-4">
          <div v-for="status in passwordStatusData" :key="status.label" class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-4 h-4 rounded-full" :class="status.color"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ status.label }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ status.count }}</span>
              <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div class="h-2 rounded-full" :class="status.color" :style="{ width: status.percentage + '%' }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">最近活动</h3>
        <div class="space-y-4">
          <div v-for="activity in recentActivities" :key="activity.id" class="flex items-start space-x-3">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 rounded-full flex items-center justify-center" :class="activity.iconBg">
                <font-awesome-icon :icon="['fas', activity.icon]" :class="activity.iconColor" class="text-sm" />
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm text-gray-900 dark:text-white">{{ activity.message }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ activity.time }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 风险主机列表 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">高风险主机</h3>
        <button class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
          查看全部
        </button>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400">主机名</th>
              <th class="text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400">IP地址</th>
              <th class="text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400">风险等级</th>
              <th class="text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400">主要问题</th>
              <th class="text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400">操作</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="host in riskHosts" :key="host.id" class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
              <td class="py-3 px-4">
                <div class="flex items-center space-x-2">
                  <font-awesome-icon :icon="['fas', 'server']" class="text-gray-400" />
                  <span class="text-sm font-medium text-gray-900 dark:text-white">{{ host.name }}</span>
                </div>
              </td>
              <td class="py-3 px-4 text-sm text-gray-500 dark:text-gray-400">{{ host.ip }}</td>
              <td class="py-3 px-4">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" :class="host.riskLevelClass">
                  {{ host.riskLevel }}
                </span>
              </td>
              <td class="py-3 px-4 text-sm text-gray-500 dark:text-gray-400">{{ host.issues.join(', ') }}</td>
              <td class="py-3 px-4">
                <button class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                  立即修复
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SecurityDashboard',
  props: {
    hosts: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    totalHosts() {
      return this.hosts.length
    },
    
    expiringPasswords() {
      let count = 0
      this.hosts.forEach(host => {
        host.accounts.forEach(account => {
          if (this.isPasswordExpiringSoon(account)) {
            count++
          }
        })
      })
      return count
    },
    
    weakPasswords() {
      let count = 0
      this.hosts.forEach(host => {
        host.accounts.forEach(account => {
          if (this.isPasswordWeak(account.password)) {
            count++
          }
        })
      })
      return count
    },
    
    securityScore() {
      const total = this.totalHosts * 2 // 假设每台主机有2个账号
      if (total === 0) return 100
      
      const issues = this.expiringPasswords + this.weakPasswords
      const score = Math.max(0, Math.round((1 - issues / total) * 100))
      return score
    },
    
    securityScoreClass() {
      if (this.securityScore >= 80) return 'text-green-600 dark:text-green-400'
      if (this.securityScore >= 60) return 'text-yellow-600 dark:text-yellow-400'
      return 'text-red-600 dark:text-red-400'
    },
    
    passwordStatusData() {
      const total = this.totalHosts * 2
      const expired = Math.floor(total * 0.05)
      const expiring = this.expiringPasswords
      const weak = this.weakPasswords
      const good = total - expired - expiring - weak
      
      return [
        { label: '正常', count: good, percentage: (good / total) * 100, color: 'bg-green-500' },
        { label: '即将过期', count: expiring, percentage: (expiring / total) * 100, color: 'bg-yellow-500' },
        { label: '已过期', count: expired, percentage: (expired / total) * 100, color: 'bg-red-500' },
        { label: '弱密码', count: weak, percentage: (weak / total) * 100, color: 'bg-orange-500' }
      ]
    },
    
    recentActivities() {
      return [
        {
          id: 1,
          message: '批量更新了生产环境密码',
          time: '2分钟前',
          icon: 'key',
          iconColor: 'text-blue-600',
          iconBg: 'bg-blue-100 dark:bg-blue-900/30'
        },
        {
          id: 2,
          message: '检测到3个弱密码',
          time: '15分钟前',
          icon: 'exclamation-triangle',
          iconColor: 'text-yellow-600',
          iconBg: 'bg-yellow-100 dark:bg-yellow-900/30'
        },
        {
          id: 3,
          message: '新增密码策略"高强度策略"',
          time: '1小时前',
          icon: 'shield-alt',
          iconColor: 'text-green-600',
          iconBg: 'bg-green-100 dark:bg-green-900/30'
        },
        {
          id: 4,
          message: '定时任务执行完成',
          time: '2小时前',
          icon: 'clock',
          iconColor: 'text-purple-600',
          iconBg: 'bg-purple-100 dark:bg-purple-900/30'
        }
      ]
    },
    
    riskHosts() {
      return this.hosts.filter(host => {
        const hasExpiredPasswords = host.accounts.some(account => this.isPasswordExpired(account))
        const hasWeakPasswords = host.accounts.some(account => this.isPasswordWeak(account.password))
        return hasExpiredPasswords || hasWeakPasswords || host.status === 'error'
      }).map(host => {
        const issues = []
        if (host.accounts.some(account => this.isPasswordExpired(account))) {
          issues.push('密码过期')
        }
        if (host.accounts.some(account => this.isPasswordWeak(account.password))) {
          issues.push('弱密码')
        }
        if (host.status === 'error') {
          issues.push('连接异常')
        }
        
        const riskLevel = issues.length >= 2 ? '高' : '中'
        const riskLevelClass = riskLevel === '高' 
          ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
        
        return {
          ...host,
          riskLevel,
          riskLevelClass,
          issues
        }
      }).slice(0, 5) // 只显示前5个
    }
  },
  
  methods: {
    isPasswordExpiringSoon(account) {
      if (!account.passwordExpiryDate) return false
      
      const expiryDate = new Date(account.passwordExpiryDate)
      const now = new Date()
      const daysUntilExpiry = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24))
      
      return daysUntilExpiry <= 7 && daysUntilExpiry > 0
    },
    
    isPasswordExpired(account) {
      if (!account.passwordExpiryDate) return false
      
      const expiryDate = new Date(account.passwordExpiryDate)
      const now = new Date()
      
      return expiryDate < now
    },
    
    isPasswordWeak(password) {
      if (!password) return true
      
      // 简单的弱密码检测
      if (password.length < 8) return true
      if (!/[A-Z]/.test(password)) return true
      if (!/[a-z]/.test(password)) return true
      if (!/[0-9]/.test(password)) return true
      
      // 检测常见弱密码
      const weakPatterns = [
        /^password/i,
        /^123456/,
        /^admin/i,
        /^root/i,
        /^qwerty/i
      ]
      
      return weakPatterns.some(pattern => pattern.test(password))
    }
  }
}
</script>
