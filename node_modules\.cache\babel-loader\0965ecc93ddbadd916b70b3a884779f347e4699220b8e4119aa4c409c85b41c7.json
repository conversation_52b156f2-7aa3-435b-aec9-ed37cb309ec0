{"ast": null, "code": "/**\n * 通知工具类\n * 用于在Vue 3中替代$root.$emit的通知系统\n */\n\nimport { getCurrentInstance } from 'vue';\n\n/**\n * 获取事件总线实例\n */\nfunction getEventBus() {\n  const instance = getCurrentInstance();\n  if (instance && instance.appContext.config.globalProperties.$eventBus) {\n    return instance.appContext.config.globalProperties.$eventBus;\n  }\n\n  // 如果在组件外部调用，需要手动传入eventBus实例\n  console.warn('无法获取事件总线实例，请确保在Vue组件内部调用或手动传入eventBus');\n  return null;\n}\n\n/**\n * 显示通知\n * @param {Object} options 通知选项\n * @param {string} options.type 通知类型: success, error, warning, info\n * @param {string} options.title 通知标题\n * @param {string} options.message 通知消息\n * @param {Array} options.actions 操作按钮数组\n * @param {boolean} options.autoClose 是否自动关闭\n * @param {number} options.duration 显示时长(ms)\n */\nexport function showNotification(options) {\n  const eventBus = getEventBus();\n  if (eventBus) {\n    eventBus.emit('show-notification', options);\n  }\n}\n\n/**\n * 显示成功通知\n * @param {string} message 消息内容\n * @param {string} title 标题\n * @param {Object} options 其他选项\n */\nexport function showSuccess(message, title = '成功', options = {}) {\n  showNotification({\n    type: 'success',\n    title,\n    message,\n    ...options\n  });\n}\n\n/**\n * 显示错误通知\n * @param {string} message 消息内容\n * @param {string} title 标题\n * @param {Object} options 其他选项\n */\nexport function showError(message, title = '错误', options = {}) {\n  showNotification({\n    type: 'error',\n    title,\n    message,\n    autoClose: false,\n    // 错误通知默认不自动关闭\n    ...options\n  });\n}\n\n/**\n * 显示警告通知\n * @param {string} message 消息内容\n * @param {string} title 标题\n * @param {Object} options 其他选项\n */\nexport function showWarning(message, title = '警告', options = {}) {\n  showNotification({\n    type: 'warning',\n    title,\n    message,\n    ...options\n  });\n}\n\n/**\n * 显示信息通知\n * @param {string} message 消息内容\n * @param {string} title 标题\n * @param {Object} options 其他选项\n */\nexport function showInfo(message, title = '信息', options = {}) {\n  showNotification({\n    type: 'info',\n    title,\n    message,\n    ...options\n  });\n}\n\n/**\n * 在组件外部使用的通知方法\n * 需要手动传入eventBus实例\n */\nexport class NotificationManager {\n  constructor(eventBus) {\n    this.eventBus = eventBus;\n  }\n  showNotification(options) {\n    this.eventBus.emit('show-notification', options);\n  }\n  showSuccess(message, title = '成功', options = {}) {\n    this.showNotification({\n      type: 'success',\n      title,\n      message,\n      ...options\n    });\n  }\n  showError(message, title = '错误', options = {}) {\n    this.showNotification({\n      type: 'error',\n      title,\n      message,\n      autoClose: false,\n      ...options\n    });\n  }\n  showWarning(message, title = '警告', options = {}) {\n    this.showNotification({\n      type: 'warning',\n      title,\n      message,\n      ...options\n    });\n  }\n  showInfo(message, title = '信息', options = {}) {\n    this.showNotification({\n      type: 'info',\n      title,\n      message,\n      ...options\n    });\n  }\n}\n\n// 导出默认的通知方法\nexport default {\n  showNotification,\n  showSuccess,\n  showError,\n  showWarning,\n  showInfo,\n  NotificationManager\n};", "map": {"version": 3, "names": ["getCurrentInstance", "getEventBus", "instance", "appContext", "config", "globalProperties", "$eventBus", "console", "warn", "showNotification", "options", "eventBus", "emit", "showSuccess", "message", "title", "type", "showError", "autoClose", "showWarning", "showInfo", "NotificationManager", "constructor"], "sources": ["D:/demo/ooo/pass/src/utils/notification.js"], "sourcesContent": ["/**\n * 通知工具类\n * 用于在Vue 3中替代$root.$emit的通知系统\n */\n\nimport { getCurrentInstance } from 'vue'\n\n/**\n * 获取事件总线实例\n */\nfunction getEventBus() {\n  const instance = getCurrentInstance()\n  if (instance && instance.appContext.config.globalProperties.$eventBus) {\n    return instance.appContext.config.globalProperties.$eventBus\n  }\n  \n  // 如果在组件外部调用，需要手动传入eventBus实例\n  console.warn('无法获取事件总线实例，请确保在Vue组件内部调用或手动传入eventBus')\n  return null\n}\n\n/**\n * 显示通知\n * @param {Object} options 通知选项\n * @param {string} options.type 通知类型: success, error, warning, info\n * @param {string} options.title 通知标题\n * @param {string} options.message 通知消息\n * @param {Array} options.actions 操作按钮数组\n * @param {boolean} options.autoClose 是否自动关闭\n * @param {number} options.duration 显示时长(ms)\n */\nexport function showNotification(options) {\n  const eventBus = getEventBus()\n  if (eventBus) {\n    eventBus.emit('show-notification', options)\n  }\n}\n\n/**\n * 显示成功通知\n * @param {string} message 消息内容\n * @param {string} title 标题\n * @param {Object} options 其他选项\n */\nexport function showSuccess(message, title = '成功', options = {}) {\n  showNotification({\n    type: 'success',\n    title,\n    message,\n    ...options\n  })\n}\n\n/**\n * 显示错误通知\n * @param {string} message 消息内容\n * @param {string} title 标题\n * @param {Object} options 其他选项\n */\nexport function showError(message, title = '错误', options = {}) {\n  showNotification({\n    type: 'error',\n    title,\n    message,\n    autoClose: false, // 错误通知默认不自动关闭\n    ...options\n  })\n}\n\n/**\n * 显示警告通知\n * @param {string} message 消息内容\n * @param {string} title 标题\n * @param {Object} options 其他选项\n */\nexport function showWarning(message, title = '警告', options = {}) {\n  showNotification({\n    type: 'warning',\n    title,\n    message,\n    ...options\n  })\n}\n\n/**\n * 显示信息通知\n * @param {string} message 消息内容\n * @param {string} title 标题\n * @param {Object} options 其他选项\n */\nexport function showInfo(message, title = '信息', options = {}) {\n  showNotification({\n    type: 'info',\n    title,\n    message,\n    ...options\n  })\n}\n\n/**\n * 在组件外部使用的通知方法\n * 需要手动传入eventBus实例\n */\nexport class NotificationManager {\n  constructor(eventBus) {\n    this.eventBus = eventBus\n  }\n\n  showNotification(options) {\n    this.eventBus.emit('show-notification', options)\n  }\n\n  showSuccess(message, title = '成功', options = {}) {\n    this.showNotification({\n      type: 'success',\n      title,\n      message,\n      ...options\n    })\n  }\n\n  showError(message, title = '错误', options = {}) {\n    this.showNotification({\n      type: 'error',\n      title,\n      message,\n      autoClose: false,\n      ...options\n    })\n  }\n\n  showWarning(message, title = '警告', options = {}) {\n    this.showNotification({\n      type: 'warning',\n      title,\n      message,\n      ...options\n    })\n  }\n\n  showInfo(message, title = '信息', options = {}) {\n    this.showNotification({\n      type: 'info',\n      title,\n      message,\n      ...options\n    })\n  }\n}\n\n// 导出默认的通知方法\nexport default {\n  showNotification,\n  showSuccess,\n  showError,\n  showWarning,\n  showInfo,\n  NotificationManager\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAASA,kBAAkB,QAAQ,KAAK;;AAExC;AACA;AACA;AACA,SAASC,WAAWA,CAAA,EAAG;EACrB,MAAMC,QAAQ,GAAGF,kBAAkB,CAAC,CAAC;EACrC,IAAIE,QAAQ,IAAIA,QAAQ,CAACC,UAAU,CAACC,MAAM,CAACC,gBAAgB,CAACC,SAAS,EAAE;IACrE,OAAOJ,QAAQ,CAACC,UAAU,CAACC,MAAM,CAACC,gBAAgB,CAACC,SAAS;EAC9D;;EAEA;EACAC,OAAO,CAACC,IAAI,CAAC,uCAAuC,CAAC;EACrD,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EACxC,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,IAAIU,QAAQ,EAAE;IACZA,QAAQ,CAACC,IAAI,CAAC,mBAAmB,EAAEF,OAAO,CAAC;EAC7C;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,WAAWA,CAACC,OAAO,EAAEC,KAAK,GAAG,IAAI,EAAEL,OAAO,GAAG,CAAC,CAAC,EAAE;EAC/DD,gBAAgB,CAAC;IACfO,IAAI,EAAE,SAAS;IACfD,KAAK;IACLD,OAAO;IACP,GAAGJ;EACL,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASO,SAASA,CAACH,OAAO,EAAEC,KAAK,GAAG,IAAI,EAAEL,OAAO,GAAG,CAAC,CAAC,EAAE;EAC7DD,gBAAgB,CAAC;IACfO,IAAI,EAAE,OAAO;IACbD,KAAK;IACLD,OAAO;IACPI,SAAS,EAAE,KAAK;IAAE;IAClB,GAAGR;EACL,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,WAAWA,CAACL,OAAO,EAAEC,KAAK,GAAG,IAAI,EAAEL,OAAO,GAAG,CAAC,CAAC,EAAE;EAC/DD,gBAAgB,CAAC;IACfO,IAAI,EAAE,SAAS;IACfD,KAAK;IACLD,OAAO;IACP,GAAGJ;EACL,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASU,QAAQA,CAACN,OAAO,EAAEC,KAAK,GAAG,IAAI,EAAEL,OAAO,GAAG,CAAC,CAAC,EAAE;EAC5DD,gBAAgB,CAAC;IACfO,IAAI,EAAE,MAAM;IACZD,KAAK;IACLD,OAAO;IACP,GAAGJ;EACL,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMW,mBAAmB,CAAC;EAC/BC,WAAWA,CAACX,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC1B;EAEAF,gBAAgBA,CAACC,OAAO,EAAE;IACxB,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC,mBAAmB,EAAEF,OAAO,CAAC;EAClD;EAEAG,WAAWA,CAACC,OAAO,EAAEC,KAAK,GAAG,IAAI,EAAEL,OAAO,GAAG,CAAC,CAAC,EAAE;IAC/C,IAAI,CAACD,gBAAgB,CAAC;MACpBO,IAAI,EAAE,SAAS;MACfD,KAAK;MACLD,OAAO;MACP,GAAGJ;IACL,CAAC,CAAC;EACJ;EAEAO,SAASA,CAACH,OAAO,EAAEC,KAAK,GAAG,IAAI,EAAEL,OAAO,GAAG,CAAC,CAAC,EAAE;IAC7C,IAAI,CAACD,gBAAgB,CAAC;MACpBO,IAAI,EAAE,OAAO;MACbD,KAAK;MACLD,OAAO;MACPI,SAAS,EAAE,KAAK;MAChB,GAAGR;IACL,CAAC,CAAC;EACJ;EAEAS,WAAWA,CAACL,OAAO,EAAEC,KAAK,GAAG,IAAI,EAAEL,OAAO,GAAG,CAAC,CAAC,EAAE;IAC/C,IAAI,CAACD,gBAAgB,CAAC;MACpBO,IAAI,EAAE,SAAS;MACfD,KAAK;MACLD,OAAO;MACP,GAAGJ;IACL,CAAC,CAAC;EACJ;EAEAU,QAAQA,CAACN,OAAO,EAAEC,KAAK,GAAG,IAAI,EAAEL,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5C,IAAI,CAACD,gBAAgB,CAAC;MACpBO,IAAI,EAAE,MAAM;MACZD,KAAK;MACLD,OAAO;MACP,GAAGJ;IACL,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,eAAe;EACbD,gBAAgB;EAChBI,WAAW;EACXI,SAAS;EACTE,WAAW;EACXC,QAAQ;EACRC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}