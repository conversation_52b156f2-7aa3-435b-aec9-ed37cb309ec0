{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, vModelText as _vModelText, withKeys as _withKeys, withDirectives as _withDirectives, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass, createTextVNode as _createTextVNode, withCtx as _withCtx, createBlock as _createBlock, resolveDynamicComponent as _resolveDynamicComponent, Transition as _Transition } from \"vue\";\nconst _hoisted_1 = {\n  class: \"header\"\n};\nconst _hoisted_2 = {\n  class: \"container mx-auto px-6 py-4\"\n};\nconst _hoisted_3 = {\n  class: \"flex justify-between items-center\"\n};\nconst _hoisted_4 = {\n  class: \"flex items-center space-x-4\"\n};\nconst _hoisted_5 = {\n  class: \"flex items-center space-x-3\"\n};\nconst _hoisted_6 = {\n  class: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg\"\n};\nconst _hoisted_7 = {\n  class: \"flex items-center space-x-4\"\n};\nconst _hoisted_8 = {\n  class: \"relative hidden md:block\"\n};\nconst _hoisted_9 = {\n  class: \"relative\"\n};\nconst _hoisted_10 = {\n  key: 0,\n  class: \"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\"\n};\nconst _hoisted_11 = {\n  key: 0,\n  class: \"absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50\"\n};\nconst _hoisted_12 = {\n  class: \"max-h-64 overflow-y-auto\"\n};\nconst _hoisted_13 = {\n  class: \"flex items-start space-x-3\"\n};\nconst _hoisted_14 = {\n  class: \"flex-shrink-0\"\n};\nconst _hoisted_15 = {\n  class: \"flex-1 min-w-0\"\n};\nconst _hoisted_16 = {\n  class: \"text-sm font-medium text-gray-900 dark:text-white\"\n};\nconst _hoisted_17 = {\n  class: \"text-sm text-gray-500 dark:text-gray-400\"\n};\nconst _hoisted_18 = {\n  class: \"text-xs text-gray-400 dark:text-gray-500 mt-1\"\n};\nconst _hoisted_19 = {\n  class: \"relative\"\n};\nconst _hoisted_20 = {\n  key: 0,\n  class: \"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50\"\n};\nconst _hoisted_21 = {\n  class: \"py-2\"\n};\nconst _hoisted_22 = {\n  href: \"#\",\n  class: \"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\n};\nconst _hoisted_23 = {\n  href: \"#\",\n  class: \"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\n};\nconst _hoisted_24 = {\n  href: \"#\",\n  class: \"flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20\"\n};\nconst _hoisted_25 = {\n  class: \"flex-1 bg-gray-50 dark:bg-gray-900\"\n};\nconst _hoisted_26 = {\n  class: \"container mx-auto px-6 py-6\"\n};\nconst _hoisted_27 = {\n  class: \"nav-tabs\"\n};\nconst _hoisted_28 = {\n  key: 0,\n  class: \"ml-2 px-2 py-0.5 bg-red-500 text-white text-xs rounded-full\"\n};\nconst _hoisted_29 = {\n  key: 0,\n  class: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\"\n};\nconst _hoisted_30 = {\n  class: \"bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center space-x-4\"\n};\nconst _hoisted_31 = {\n  class: \"text-gray-700 dark:text-gray-300\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_router_link = _resolveComponent(\"router-link\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  const _component_NotificationSystem = _resolveComponent(\"NotificationSystem\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass([\"app-container\", {\n      'dark': $data.isDarkMode\n    }])\n  }, [_createCommentVNode(\" 顶部导航栏 \"), _createElementVNode(\"header\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" 左侧品牌区域 \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"text-white text-lg\"\n  })]), _cache[5] || (_cache[5] = _createElementVNode(\"div\", null, [_createElementVNode(\"h1\", {\n    class: \"text-xl font-bold text-gray-900 dark:text-white\"\n  }, \"SecurePass\"), _createElementVNode(\"p\", {\n    class: \"text-xs text-gray-500 dark:text-gray-400\"\n  }, \"企业密码管理平台\")], -1 /* HOISTED */))])]), _createCommentVNode(\" 右侧操作区域 \"), _createElementVNode(\"div\", _hoisted_7, [_createCommentVNode(\" 全局搜索 \"), _createElementVNode(\"div\", _hoisted_8, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.globalSearch = $event),\n    placeholder: \"全局搜索...\",\n    class: \"w-64 pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-700 border-0 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:text-white dark:placeholder-gray-400\",\n    onKeyup: _cache[1] || (_cache[1] = _withKeys((...args) => $options.performGlobalSearch && $options.performGlobalSearch(...args), [\"enter\"]))\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $data.globalSearch]]), _createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n  })]), _createCommentVNode(\" 通知中心 \"), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = $event => $data.showNotifications = !$data.showNotifications),\n    class: \"relative p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\"\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'bell'],\n    class: \"text-lg\"\n  }), $options.unreadNotifications > 0 ? (_openBlock(), _createElementBlock(\"span\", _hoisted_10, _toDisplayString($options.unreadNotifications), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 通知下拉菜单 \"), $data.showNotifications ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n    class: \"p-4 border-b border-gray-200 dark:border-gray-700\"\n  }, [_createElementVNode(\"h3\", {\n    class: \"font-semibold text-gray-900 dark:text-white\"\n  }, \"通知中心\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_12, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.notifications, notification => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: notification.id,\n      class: \"p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700\"\n    }, [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', notification.icon],\n      class: _normalizeClass(notification.iconColor)\n    }, null, 8 /* PROPS */, [\"icon\", \"class\"])]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"p\", _hoisted_16, _toDisplayString(notification.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_17, _toDisplayString(notification.message), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_18, _toDisplayString(notification.time), 1 /* TEXT */)])])]);\n  }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 主题切换 \"), _createElementVNode(\"button\", {\n    onClick: _cache[3] || (_cache[3] = (...args) => $options.toggleTheme && $options.toggleTheme(...args)),\n    class: \"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\"\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', $data.isDarkMode ? 'sun' : 'moon'],\n    class: \"text-lg\"\n  }, null, 8 /* PROPS */, [\"icon\"])]), _createCommentVNode(\" 用户菜单 \"), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"button\", {\n    onClick: _cache[4] || (_cache[4] = $event => $data.showUserMenu = !$data.showUserMenu),\n    class: \"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n  }, [_cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n    class: \"w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center\"\n  }, [_createElementVNode(\"span\", {\n    class: \"text-white text-sm font-semibold\"\n  }, \"管\")], -1 /* HOISTED */)), _cache[8] || (_cache[8] = _createElementVNode(\"span\", {\n    class: \"hidden md:block text-sm font-medium text-gray-700 dark:text-gray-300\"\n  }, \"管理员\", -1 /* HOISTED */)), _createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'chevron-down'],\n    class: \"text-xs text-gray-400\"\n  })]), _createCommentVNode(\" 用户下拉菜单 \"), $data.showUserMenu ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"a\", _hoisted_22, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'user'],\n    class: \"mr-3\"\n  }), _cache[9] || (_cache[9] = _createTextVNode(\" 个人设置 \"))]), _createElementVNode(\"a\", _hoisted_23, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'cog'],\n    class: \"mr-3\"\n  }), _cache[10] || (_cache[10] = _createTextVNode(\" 系统设置 \"))]), _cache[12] || (_cache[12] = _createElementVNode(\"hr\", {\n    class: \"my-2 border-gray-200 dark:border-gray-700\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"a\", _hoisted_24, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'sign-out-alt'],\n    class: \"mr-3\"\n  }), _cache[11] || (_cache[11] = _createTextVNode(\" 退出登录 \"))])])])) : _createCommentVNode(\"v-if\", true)])])])])]), _createCommentVNode(\" 主要内容区域 \"), _createElementVNode(\"main\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_createCommentVNode(\" 导航标签 \"), _createElementVNode(\"nav\", _hoisted_27, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.routes, route => {\n    return _openBlock(), _createBlock(_component_router_link, {\n      key: route.path,\n      to: route.path,\n      class: \"nav-item\",\n      \"active-class\": \"active\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', route.meta.icon],\n        class: \"mr-2\"\n      }, null, 8 /* PROPS */, [\"icon\"]), _createElementVNode(\"span\", null, _toDisplayString(route.meta.title), 1 /* TEXT */), route.meta.badge ? (_openBlock(), _createElementBlock(\"span\", _hoisted_28, _toDisplayString(route.meta.badge), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"to\"]);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 路由视图 \"), _createVNode(_component_router_view, null, {\n    default: _withCtx(({\n      Component\n    }) => [_createVNode(_Transition, {\n      name: \"fade\",\n      mode: \"out-in\"\n    }, {\n      default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(Component)))]),\n      _: 2 /* DYNAMIC */\n    }, 1024 /* DYNAMIC_SLOTS */)]),\n    _: 1 /* STABLE */\n  })])]), _createCommentVNode(\" 全局加载遮罩 \"), $data.globalLoading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_29, [_createElementVNode(\"div\", _hoisted_30, [_cache[13] || (_cache[13] = _createElementVNode(\"div\", {\n    class: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_31, _toDisplayString($data.loadingMessage), 1 /* TEXT */)])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 通知系统 \"), _createVNode(_component_NotificationSystem, {\n    ref: \"notificationSystem\"\n  }, null, 512 /* NEED_PATCH */)], 2 /* CLASS */);\n}", "map": {"version": 3, "names": ["class", "key", "href", "_createElementBlock", "_normalizeClass", "$data", "isDarkMode", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_createVNode", "_component_font_awesome_icon", "icon", "_hoisted_7", "_hoisted_8", "type", "_cache", "$event", "globalSearch", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "args", "$options", "performGlobalSearch", "_hoisted_9", "onClick", "showNotifications", "unreadNotifications", "_hoisted_10", "_toDisplayString", "_hoisted_11", "_hoisted_12", "_Fragment", "_renderList", "notifications", "notification", "id", "_hoisted_13", "_hoisted_14", "iconColor", "_hoisted_15", "_hoisted_16", "title", "_hoisted_17", "message", "_hoisted_18", "time", "toggleTheme", "_hoisted_19", "showUserMenu", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_createTextVNode", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "routes", "route", "_createBlock", "_component_router_link", "path", "to", "default", "_withCtx", "meta", "badge", "_hoisted_28", "_", "_component_router_view", "Component", "_Transition", "name", "mode", "_resolveDynamicComponent", "globalLoading", "_hoisted_29", "_hoisted_30", "_hoisted_31", "loadingMessage", "_component_NotificationSystem", "ref"], "sources": ["D:\\demo\\ooo\\pass\\src\\App.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\" :class=\"{ 'dark': isDarkMode }\">\r\n        <!-- 顶部导航栏 -->\r\n        <header class=\"header\">\r\n            <div class=\"container mx-auto px-6 py-4\">\r\n                <div class=\"flex justify-between items-center\">\r\n                    <!-- 左侧品牌区域 -->\r\n                    <div class=\"flex items-center space-x-4\">\r\n                        <div class=\"flex items-center space-x-3\">\r\n                            <div class=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg\">\r\n                                <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"text-white text-lg\" />\r\n                            </div>\r\n                            <div>\r\n                                <h1 class=\"text-xl font-bold text-gray-900 dark:text-white\">SecurePass</h1>\r\n                                <p class=\"text-xs text-gray-500 dark:text-gray-400\">企业密码管理平台</p>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- 右侧操作区域 -->\r\n                    <div class=\"flex items-center space-x-4\">\r\n                        <!-- 全局搜索 -->\r\n                        <div class=\"relative hidden md:block\">\r\n                            <input\r\n                                type=\"text\"\r\n                                v-model=\"globalSearch\"\r\n                                placeholder=\"全局搜索...\"\r\n                                class=\"w-64 pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-700 border-0 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:text-white dark:placeholder-gray-400\"\r\n                                @keyup.enter=\"performGlobalSearch\"\r\n                            />\r\n                            <font-awesome-icon :icon=\"['fas', 'search']\" class=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\r\n                        </div>\r\n\r\n                        <!-- 通知中心 -->\r\n                        <div class=\"relative\">\r\n                            <button\r\n                                @click=\"showNotifications = !showNotifications\"\r\n                                class=\"relative p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\"\r\n                            >\r\n                                <font-awesome-icon :icon=\"['fas', 'bell']\" class=\"text-lg\" />\r\n                                <span v-if=\"unreadNotifications > 0\" class=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\r\n                                    {{ unreadNotifications }}\r\n                                </span>\r\n                            </button>\r\n\r\n                            <!-- 通知下拉菜单 -->\r\n                            <div v-if=\"showNotifications\" class=\"absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50\">\r\n                                <div class=\"p-4 border-b border-gray-200 dark:border-gray-700\">\r\n                                    <h3 class=\"font-semibold text-gray-900 dark:text-white\">通知中心</h3>\r\n                                </div>\r\n                                <div class=\"max-h-64 overflow-y-auto\">\r\n                                    <div v-for=\"notification in notifications\" :key=\"notification.id\" class=\"p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                                        <div class=\"flex items-start space-x-3\">\r\n                                            <div class=\"flex-shrink-0\">\r\n                                                <font-awesome-icon :icon=\"['fas', notification.icon]\" :class=\"notification.iconColor\" />\r\n                                            </div>\r\n                                            <div class=\"flex-1 min-w-0\">\r\n                                                <p class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ notification.title }}</p>\r\n                                                <p class=\"text-sm text-gray-500 dark:text-gray-400\">{{ notification.message }}</p>\r\n                                                <p class=\"text-xs text-gray-400 dark:text-gray-500 mt-1\">{{ notification.time }}</p>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <!-- 主题切换 -->\r\n                        <button\r\n                            @click=\"toggleTheme\"\r\n                            class=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\"\r\n                        >\r\n                            <font-awesome-icon :icon=\"['fas', isDarkMode ? 'sun' : 'moon']\" class=\"text-lg\" />\r\n                        </button>\r\n\r\n                        <!-- 用户菜单 -->\r\n                        <div class=\"relative\">\r\n                            <button\r\n                                @click=\"showUserMenu = !showUserMenu\"\r\n                                class=\"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\r\n                            >\r\n                                <div class=\"w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center\">\r\n                                    <span class=\"text-white text-sm font-semibold\">管</span>\r\n                                </div>\r\n                                <span class=\"hidden md:block text-sm font-medium text-gray-700 dark:text-gray-300\">管理员</span>\r\n                                <font-awesome-icon :icon=\"['fas', 'chevron-down']\" class=\"text-xs text-gray-400\" />\r\n                            </button>\r\n\r\n                            <!-- 用户下拉菜单 -->\r\n                            <div v-if=\"showUserMenu\" class=\"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50\">\r\n                                <div class=\"py-2\">\r\n                                    <a href=\"#\" class=\"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\">\r\n                                        <font-awesome-icon :icon=\"['fas', 'user']\" class=\"mr-3\" />\r\n                                        个人设置\r\n                                    </a>\r\n                                    <a href=\"#\" class=\"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\">\r\n                                        <font-awesome-icon :icon=\"['fas', 'cog']\" class=\"mr-3\" />\r\n                                        系统设置\r\n                                    </a>\r\n                                    <hr class=\"my-2 border-gray-200 dark:border-gray-700\">\r\n                                    <a href=\"#\" class=\"flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20\">\r\n                                        <font-awesome-icon :icon=\"['fas', 'sign-out-alt']\" class=\"mr-3\" />\r\n                                        退出登录\r\n                                    </a>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n\r\n        <!-- 主要内容区域 -->\r\n        <main class=\"flex-1 bg-gray-50 dark:bg-gray-900\">\r\n            <div class=\"container mx-auto px-6 py-6\">\r\n                <!-- 导航标签 -->\r\n                <nav class=\"nav-tabs\">\r\n                    <router-link v-for=\"route in routes\" :key=\"route.path\" :to=\"route.path\" class=\"nav-item\"\r\n                        active-class=\"active\">\r\n                        <font-awesome-icon :icon=\"['fas', route.meta.icon]\" class=\"mr-2\" />\r\n                        <span>{{ route.meta.title }}</span>\r\n                        <span v-if=\"route.meta.badge\" class=\"ml-2 px-2 py-0.5 bg-red-500 text-white text-xs rounded-full\">\r\n                            {{ route.meta.badge }}\r\n                        </span>\r\n                    </router-link>\r\n                </nav>\r\n\r\n                <!-- 路由视图 -->\r\n                <router-view v-slot=\"{ Component }\">\r\n                    <transition name=\"fade\" mode=\"out-in\">\r\n                        <component :is=\"Component\" />\r\n                    </transition>\r\n                </router-view>\r\n            </div>\r\n        </main>\r\n\r\n        <!-- 全局加载遮罩 -->\r\n        <div v-if=\"globalLoading\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n            <div class=\"bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center space-x-4\">\r\n                <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\r\n                <span class=\"text-gray-700 dark:text-gray-300\">{{ loadingMessage }}</span>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 通知系统 -->\r\n        <NotificationSystem ref=\"notificationSystem\" />\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex'\r\nimport NotificationSystem from '@/components/NotificationSystem.vue'\r\n\r\nexport default {\r\n    name: 'App',\r\n    components: {\r\n        NotificationSystem\r\n    },\r\n    data() {\r\n        return {\r\n            isDarkMode: false,\r\n            globalSearch: '',\r\n            showNotifications: false,\r\n            showUserMenu: false,\r\n            globalLoading: false,\r\n            loadingMessage: '处理中...',\r\n            notifications: [\r\n                {\r\n                    id: 1,\r\n                    title: '密码即将过期',\r\n                    message: '5台服务器的密码将在3天内过期',\r\n                    time: '2分钟前',\r\n                    icon: 'exclamation-triangle',\r\n                    iconColor: 'text-yellow-500'\r\n                },\r\n                {\r\n                    id: 2,\r\n                    title: '批量更新完成',\r\n                    message: '生产环境密码更新任务已完成',\r\n                    time: '1小时前',\r\n                    icon: 'check-circle',\r\n                    iconColor: 'text-green-500'\r\n                },\r\n                {\r\n                    id: 3,\r\n                    title: '安全警告',\r\n                    message: '检测到弱密码，建议立即更新',\r\n                    time: '3小时前',\r\n                    icon: 'shield-alt',\r\n                    iconColor: 'text-red-500'\r\n                }\r\n            ]\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState(['hosts']),\r\n        routes() {\r\n            const baseRoutes = this.$router.options.routes.filter(route => route.meta && route.meta.title)\r\n\r\n            // 动态添加徽章信息\r\n            return baseRoutes.map(route => {\r\n                const routeCopy = { ...route }\r\n                if (route.path === '/hosts') {\r\n                    // 计算需要注意的主机数量\r\n                    const warningHosts = this.hosts.filter(host =>\r\n                        host.status === 'warning' || host.status === 'error'\r\n                    ).length\r\n                    if (warningHosts > 0) {\r\n                        routeCopy.meta = { ...route.meta, badge: warningHosts }\r\n                    }\r\n                }\r\n                return routeCopy\r\n            })\r\n        },\r\n        unreadNotifications() {\r\n            return this.notifications.length\r\n        }\r\n    },\r\n    mounted() {\r\n        // 初始化主题\r\n        this.initTheme()\r\n\r\n        // 点击外部关闭下拉菜单\r\n        document.addEventListener('click', this.handleClickOutside)\r\n    },\r\n    beforeUnmount() {\r\n        document.removeEventListener('click', this.handleClickOutside)\r\n    },\r\n    methods: {\r\n        initTheme() {\r\n            // 从本地存储读取主题设置\r\n            const savedTheme = localStorage.getItem('theme')\r\n            if (savedTheme) {\r\n                this.isDarkMode = savedTheme === 'dark'\r\n            } else {\r\n                // 检测系统主题偏好\r\n                this.isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches\r\n            }\r\n            this.applyTheme()\r\n        },\r\n\r\n        toggleTheme() {\r\n            this.isDarkMode = !this.isDarkMode\r\n            this.applyTheme()\r\n            localStorage.setItem('theme', this.isDarkMode ? 'dark' : 'light')\r\n        },\r\n\r\n        applyTheme() {\r\n            if (this.isDarkMode) {\r\n                document.documentElement.classList.add('dark')\r\n            } else {\r\n                document.documentElement.classList.remove('dark')\r\n            }\r\n        },\r\n\r\n        performGlobalSearch() {\r\n            if (this.globalSearch.trim()) {\r\n                // 实现全局搜索逻辑\r\n                console.log('搜索:', this.globalSearch)\r\n                // 这里可以跳转到搜索结果页面或在当前页面显示搜索结果\r\n            }\r\n        },\r\n\r\n        handleClickOutside(event) {\r\n            // 关闭通知下拉菜单\r\n            if (!event.target.closest('.relative') || !event.target.closest('[data-dropdown=\"notifications\"]')) {\r\n                this.showNotifications = false\r\n            }\r\n\r\n            // 关闭用户菜单\r\n            if (!event.target.closest('.relative') || !event.target.closest('[data-dropdown=\"user\"]')) {\r\n                this.showUserMenu = false\r\n            }\r\n        },\r\n\r\n        showGlobalLoading(message = '处理中...') {\r\n            this.loadingMessage = message\r\n            this.globalLoading = true\r\n        },\r\n\r\n        hideGlobalLoading() {\r\n            this.globalLoading = false\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 全局样式 */\r\n* {\r\n    box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n    font-family: \"Inter\", \"PingFang SC\", \"Microsoft YaHei\", -apple-system, BlinkMacSystemFont, sans-serif;\r\n    background-color: #f8fafc;\r\n    color: #1e293b;\r\n    margin: 0;\r\n    padding: 0;\r\n    line-height: 1.6;\r\n}\r\n\r\n.app-container {\r\n    min-height: 100vh;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n/* 暗色主题 */\r\n.dark body {\r\n    background-color: #0f172a;\r\n    color: #e2e8f0;\r\n}\r\n\r\n/* 头部样式 */\r\n.header {\r\n    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\r\n    border-bottom: 1px solid #e2e8f0;\r\n    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\r\n    position: sticky;\r\n    top: 0;\r\n    z-index: 40;\r\n}\r\n\r\n.dark .header {\r\n    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);\r\n    border-bottom-color: #334155;\r\n}\r\n\r\n/* 导航标签样式 */\r\n.nav-tabs {\r\n    display: flex;\r\n    background: white;\r\n    border-radius: 12px;\r\n    padding: 4px;\r\n    margin-bottom: 2rem;\r\n    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);\r\n    overflow-x: auto;\r\n}\r\n\r\n.dark .nav-tabs {\r\n    background: #1e293b;\r\n    border: 1px solid #334155;\r\n}\r\n\r\n.nav-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px 20px;\r\n    border-radius: 8px;\r\n    color: #64748b;\r\n    text-decoration: none;\r\n    font-weight: 500;\r\n    transition: all 0.2s ease;\r\n    white-space: nowrap;\r\n    position: relative;\r\n}\r\n\r\n.nav-item:hover:not(.active) {\r\n    color: #3b82f6;\r\n    background-color: #f1f5f9;\r\n}\r\n\r\n.dark .nav-item:hover:not(.active) {\r\n    color: #60a5fa;\r\n    background-color: #334155;\r\n}\r\n\r\n.nav-item.active {\r\n    color: #3b82f6;\r\n    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);\r\n    font-weight: 600;\r\n    box-shadow: 0 2px 4px 0 rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n.dark .nav-item.active {\r\n    color: #60a5fa;\r\n    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);\r\n}\r\n\r\n/* 过渡动画 */\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n}\r\n\r\n/* 滚动条样式 */\r\n::-webkit-scrollbar {\r\n    width: 6px;\r\n    height: 6px;\r\n}\r\n\r\n::-webkit-scrollbar-track {\r\n    background: #f1f5f9;\r\n    border-radius: 3px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb {\r\n    background: #cbd5e1;\r\n    border-radius: 3px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb:hover {\r\n    background: #94a3b8;\r\n}\r\n\r\n.dark ::-webkit-scrollbar-track {\r\n    background: #334155;\r\n}\r\n\r\n.dark ::-webkit-scrollbar-thumb {\r\n    background: #64748b;\r\n}\r\n\r\n.dark ::-webkit-scrollbar-thumb:hover {\r\n    background: #94a3b8;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n    .nav-tabs {\r\n        margin: 0 -1rem 2rem -1rem;\r\n        border-radius: 0;\r\n        padding: 4px 1rem;\r\n    }\r\n\r\n    .nav-item {\r\n        padding: 10px 16px;\r\n        font-size: 14px;\r\n    }\r\n}\r\n\r\n/* 动画关键帧 */\r\n@keyframes slideIn {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateX(-20px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateX(0);\r\n    }\r\n}\r\n\r\n@keyframes fadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(20px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* 工具类 */\r\n.animate-slide-in {\r\n    animation: slideIn 0.3s ease-out;\r\n}\r\n\r\n.animate-fade-in-up {\r\n    animation: fadeInUp 0.3s ease-out;\r\n}\r\n</style>"], "mappings": ";;EAGgBA,KAAK,EAAC;AAAQ;;EACbA,KAAK,EAAC;AAA6B;;EAC/BA,KAAK,EAAC;AAAmC;;EAErCA,KAAK,EAAC;AAA6B;;EAC/BA,KAAK,EAAC;AAA6B;;EAC/BA,KAAK,EAAC;AAA+G;;EAW7HA,KAAK,EAAC;AAA6B;;EAE/BA,KAAK,EAAC;AAA0B;;EAYhCA,KAAK,EAAC;AAAU;;EAlC7CC,GAAA;EAwCqED,KAAK,EAAC;;;EAxC3EC,GAAA;EA8C0DD,KAAK,EAAC;;;EAI3BA,KAAK,EAAC;AAA0B;;EAExBA,KAAK,EAAC;AAA4B;;EAC9BA,KAAK,EAAC;AAAe;;EAGrBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAmD;;EACzDA,KAAK,EAAC;AAA0C;;EAChDA,KAAK,EAAC;AAA+C;;EAiB3EA,KAAK,EAAC;AAAU;;EA5E7CC,GAAA;EAyFqDD,KAAK,EAAC;;;EACtBA,KAAK,EAAC;AAAM;;EACVE,IAAI,EAAC,GAAG;EAACF,KAAK,EAAC;;;EAIfE,IAAI,EAAC,GAAG;EAACF,KAAK,EAAC;;;EAKfE,IAAI,EAAC,GAAG;EAACF,KAAK,EAAC;;;EAaxCA,KAAK,EAAC;AAAoC;;EACvCA,KAAK,EAAC;AAA6B;;EAE/BA,KAAK,EAAC;AAAU;;EApHrCC,GAAA;EAyHsDD,KAAK,EAAC;;;EAzH5DC,GAAA;EAyIkCD,KAAK,EAAC;;;EACvBA,KAAK,EAAC;AAAsE;;EAEvEA,KAAK,EAAC;AAAkC;;;;;;uBA3I1DG,mBAAA,CAiJM;IAjJDH,KAAK,EADdI,eAAA,EACe,eAAe;MAAA,QAAmBC,KAAA,CAAAC;IAAU;MACnDC,mBAAA,WAAc,EACdC,mBAAA,CA2GS,UA3GTC,UA2GS,GA1GLD,mBAAA,CAyGM,OAzGNE,UAyGM,GAxGFF,mBAAA,CAuGM,OAvGNG,UAuGM,GAtGFJ,mBAAA,YAAe,EACfC,mBAAA,CAUM,OAVNI,UAUM,GATFJ,mBAAA,CAQM,OARNK,UAQM,GAPFL,mBAAA,CAEM,OAFNM,UAEM,GADFC,YAAA,CAA8EC,4BAAA;IAA1DC,IAAI,EAAE,qBAAqB;IAAEjB,KAAK,EAAC;kCAE3DQ,mBAAA,CAGM,cAFFA,mBAAA,CAA2E;IAAvER,KAAK,EAAC;EAAiD,GAAC,YAAU,GACtEQ,mBAAA,CAAgE;IAA7DR,KAAK,EAAC;EAA0C,GAAC,UAAQ,E,0BAKxEO,mBAAA,YAAe,EACfC,mBAAA,CAuFM,OAvFNU,UAuFM,GAtFFX,mBAAA,UAAa,EACbC,mBAAA,CASM,OATNW,UASM,G,gBARFX,mBAAA,CAME;IALEY,IAAI,EAAC,MAAM;IAxB3C,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAyByCjB,KAAA,CAAAkB,YAAY,GAAAD,MAAA;IACrBE,WAAW,EAAC,SAAS;IACrBxB,KAAK,EAAC,6KAA6K;IAClLyB,OAAK,EAAAJ,MAAA,QAAAA,MAAA,MA5BtCK,SAAA,KAAAC,IAAA,KA4B8CC,QAAA,CAAAC,mBAAA,IAAAD,QAAA,CAAAC,mBAAA,IAAAF,IAAA,CAAmB;iEAHxBtB,KAAA,CAAAkB,YAAY,E,GAKzBR,YAAA,CAAwHC,4BAAA;IAApGC,IAAI,EAAE,iBAAiB;IAAEjB,KAAK,EAAC;QAGvDO,mBAAA,UAAa,EACbC,mBAAA,CA+BM,OA/BNsB,UA+BM,GA9BFtB,mBAAA,CAQS;IAPJuB,OAAK,EAAAV,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEjB,KAAA,CAAA2B,iBAAiB,IAAI3B,KAAA,CAAA2B,iBAAiB;IAC9ChC,KAAK,EAAC;MAENe,YAAA,CAA6DC,4BAAA;IAAzCC,IAAI,EAAE,eAAe;IAAEjB,KAAK,EAAC;MACrC4B,QAAA,CAAAK,mBAAmB,Q,cAA/B9B,mBAAA,CAEO,QAFP+B,WAEO,EAAAC,gBAAA,CADAP,QAAA,CAAAK,mBAAmB,oBAzC1D1B,mBAAA,e,GA6C4BA,mBAAA,YAAe,EACJF,KAAA,CAAA2B,iBAAiB,I,cAA5B7B,mBAAA,CAkBM,OAlBNiC,WAkBM,G,0BAjBF5B,mBAAA,CAEM;IAFDR,KAAK,EAAC;EAAmD,IAC1DQ,mBAAA,CAAiE;IAA7DR,KAAK,EAAC;EAA6C,GAAC,MAAI,E,sBAEhEQ,mBAAA,CAaM,OAbN6B,WAaM,I,kBAZFlC,mBAAA,CAWMmC,SAAA,QA9D1CC,WAAA,CAmDgElC,KAAA,CAAAmC,aAAa,EAA7BC,YAAY;yBAAxBtC,mBAAA,CAWM;MAXsCF,GAAG,EAAEwC,YAAY,CAACC,EAAE;MAAE1C,KAAK,EAAC;QACpEQ,mBAAA,CASM,OATNmC,WASM,GARFnC,mBAAA,CAEM,OAFNoC,WAEM,GADF7B,YAAA,CAAwFC,4BAAA;MAApEC,IAAI,UAAUwB,YAAY,CAACxB,IAAI;MAAIjB,KAAK,EAtD5GI,eAAA,CAsD8GqC,YAAY,CAACI,SAAS;kDAExFrC,mBAAA,CAIM,OAJNsC,WAIM,GAHFtC,mBAAA,CAAyF,KAAzFuC,WAAyF,EAAAZ,gBAAA,CAAzBM,YAAY,CAACO,KAAK,kBAClFxC,mBAAA,CAAkF,KAAlFyC,WAAkF,EAAAd,gBAAA,CAA3BM,YAAY,CAACS,OAAO,kBAC3E1C,mBAAA,CAAoF,KAApF2C,WAAoF,EAAAhB,gBAAA,CAAxBM,YAAY,CAACW,IAAI,iB;wCA3D7H7C,mBAAA,e,GAmEwBA,mBAAA,UAAa,EACbC,mBAAA,CAKS;IAJJuB,OAAK,EAAAV,MAAA,QAAAA,MAAA,UAAAM,IAAA,KAAEC,QAAA,CAAAyB,WAAA,IAAAzB,QAAA,CAAAyB,WAAA,IAAA1B,IAAA,CAAW;IACnB3B,KAAK,EAAC;MAENe,YAAA,CAAkFC,4BAAA;IAA9DC,IAAI,UAAUZ,KAAA,CAAAC,UAAU;IAAoBN,KAAK,EAAC;uCAG1EO,mBAAA,UAAa,EACbC,mBAAA,CA8BM,OA9BN8C,WA8BM,GA7BF9C,mBAAA,CASS;IARJuB,OAAK,EAAAV,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEjB,KAAA,CAAAkD,YAAY,IAAIlD,KAAA,CAAAkD,YAAY;IACpCvD,KAAK,EAAC;gCAENQ,mBAAA,CAEM;IAFDR,KAAK,EAAC;EAAoG,IAC3GQ,mBAAA,CAAuD;IAAjDR,KAAK,EAAC;EAAkC,GAAC,GAAC,E,gDAEpDQ,mBAAA,CAA6F;IAAvFR,KAAK,EAAC;EAAsE,GAAC,KAAG,sBACtFe,YAAA,CAAmFC,4BAAA;IAA/DC,IAAI,EAAE,uBAAuB;IAAEjB,KAAK,EAAC;QAG7DO,mBAAA,YAAe,EACJF,KAAA,CAAAkD,YAAY,I,cAAvBpD,mBAAA,CAgBM,OAhBNqD,WAgBM,GAfFhD,mBAAA,CAcM,OAdNiD,WAcM,GAbFjD,mBAAA,CAGI,KAHJkD,WAGI,GAFA3C,YAAA,CAA0DC,4BAAA;IAAtCC,IAAI,EAAE,eAAe;IAAEjB,KAAK,EAAC;gCA5FzF2D,gBAAA,CA4FkG,QAE9D,G,GACAnD,mBAAA,CAGI,KAHJoD,WAGI,GAFA7C,YAAA,CAAyDC,4BAAA;IAArCC,IAAI,EAAE,cAAc;IAAEjB,KAAK,EAAC;kCAhGxF2D,gBAAA,CAgGiG,QAE7D,G,+BACAnD,mBAAA,CAAsD;IAAlDR,KAAK,EAAC;EAA2C,6BACrDQ,mBAAA,CAGI,KAHJqD,WAGI,GAFA9C,YAAA,CAAkEC,4BAAA;IAA9CC,IAAI,EAAE,uBAAuB;IAAEjB,KAAK,EAAC;kCArGjG2D,gBAAA,CAqG0G,QAEtE,G,SAvGpCpD,mBAAA,e,WAgHQA,mBAAA,YAAe,EACfC,mBAAA,CAqBO,QArBPsD,WAqBO,GApBHtD,mBAAA,CAmBM,OAnBNuD,WAmBM,GAlBFxD,mBAAA,UAAa,EACbC,mBAAA,CASM,OATNwD,WASM,I,kBARF7D,mBAAA,CAOcmC,SAAA,QA5HlCC,WAAA,CAqHiDX,QAAA,CAAAqC,MAAM,EAAfC,KAAK;yBAAzBC,YAAA,CAOcC,sBAAA;MAPwBnE,GAAG,EAAEiE,KAAK,CAACG,IAAI;MAAGC,EAAE,EAAEJ,KAAK,CAACG,IAAI;MAAErE,KAAK,EAAC,UAAU;MACpF,cAAY,EAAC;;MAtHrCuE,OAAA,EAAAC,QAAA,CAuHwB,MAAmE,CAAnEzD,YAAA,CAAmEC,4BAAA;QAA/CC,IAAI,UAAUiD,KAAK,CAACO,IAAI,CAACxD,IAAI;QAAGjB,KAAK,EAAC;yCAC1DQ,mBAAA,CAAmC,cAAA2B,gBAAA,CAA1B+B,KAAK,CAACO,IAAI,CAACzB,KAAK,kBACbkB,KAAK,CAACO,IAAI,CAACC,KAAK,I,cAA5BvE,mBAAA,CAEO,QAFPwE,WAEO,EAAAxC,gBAAA,CADA+B,KAAK,CAACO,IAAI,CAACC,KAAK,oBA1H/CnE,mBAAA,e;MAAAqE,CAAA;;oCA+HgBrE,mBAAA,UAAa,EACbQ,YAAA,CAIc8D,sBAAA;IApI9BN,OAAA,EAAAC,QAAA,CAiIoB,CAEa;MAHMM;IAAS,OAC5B/D,YAAA,CAEagE,WAAA;MAFDC,IAAI,EAAC,MAAM;MAACC,IAAI,EAAC;;MAjIjDV,OAAA,EAAAC,QAAA,CAkIwB,MAA6B,E,cAA7BL,YAAA,CAA6Be,wBAlIrD,CAkIwCJ,SAAS,I;MAlIjDF,CAAA;;IAAAA,CAAA;UAwIQrE,mBAAA,YAAe,EACJF,KAAA,CAAA8E,aAAa,I,cAAxBhF,mBAAA,CAKM,OALNiF,WAKM,GAJF5E,mBAAA,CAGM,OAHN6E,WAGM,G,4BAFF7E,mBAAA,CAAgF;IAA3ER,KAAK,EAAC;EAA8D,6BACzEQ,mBAAA,CAA0E,QAA1E8E,WAA0E,EAAAnD,gBAAA,CAAxB9B,KAAA,CAAAkF,cAAc,iB,OA5IhFhF,mBAAA,gBAgJQA,mBAAA,UAAa,EACbQ,YAAA,CAA+CyE,6BAAA;IAA3BC,GAAG,EAAC;EAAoB,+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}