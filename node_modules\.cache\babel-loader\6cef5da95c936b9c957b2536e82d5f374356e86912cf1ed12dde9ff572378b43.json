{"ast": null, "code": "export default {\n  name: 'AdvancedPasswordGenerator',\n  emits: ['password-generated'],\n  props: {\n    initialOptions: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      generatedPassword: '',\n      showPassword: false,\n      copied: false,\n      generating: false,\n      options: {\n        length: 16,\n        includeUppercase: true,\n        includeLowercase: true,\n        includeNumbers: true,\n        includeSymbols: true,\n        excludeSimilar: false,\n        excludeAmbiguous: false,\n        customChars: ''\n      },\n      templates: [{\n        name: '高强度',\n        options: {\n          length: 20,\n          includeUppercase: true,\n          includeLowercase: true,\n          includeNumbers: true,\n          includeSymbols: true\n        }\n      }, {\n        name: '标准',\n        options: {\n          length: 12,\n          includeUppercase: true,\n          includeLowercase: true,\n          includeNumbers: true,\n          includeSymbols: false\n        }\n      }, {\n        name: '简单',\n        options: {\n          length: 8,\n          includeUppercase: true,\n          includeLowercase: true,\n          includeNumbers: true,\n          includeSymbols: false\n        }\n      }, {\n        name: '数字密码',\n        options: {\n          length: 6,\n          includeUppercase: false,\n          includeLowercase: false,\n          includeNumbers: true,\n          includeSymbols: false\n        }\n      }]\n    };\n  },\n  computed: {\n    passwordStrength() {\n      return this.calculatePasswordStrength(this.generatedPassword);\n    },\n    passwordStrengthClass() {\n      const strength = this.passwordStrength.score;\n      if (strength >= 4) return 'border-green-500 dark:border-green-400';\n      if (strength >= 3) return 'border-yellow-500 dark:border-yellow-400';\n      if (strength >= 2) return 'border-orange-500 dark:border-orange-400';\n      return 'border-red-500 dark:border-red-400';\n    },\n    passwordStrengthTextClass() {\n      const strength = this.passwordStrength.score;\n      if (strength >= 4) return 'text-green-600 dark:text-green-400 font-medium';\n      if (strength >= 3) return 'text-yellow-600 dark:text-yellow-400 font-medium';\n      if (strength >= 2) return 'text-orange-600 dark:text-orange-400 font-medium';\n      return 'text-red-600 dark:text-red-400 font-medium';\n    },\n    passwordStrengthBarClass() {\n      const strength = this.passwordStrength.score;\n      if (strength >= 4) return 'bg-green-500';\n      if (strength >= 3) return 'bg-yellow-500';\n      if (strength >= 2) return 'bg-orange-500';\n      return 'bg-red-500';\n    },\n    passwordStrengthText() {\n      const strength = this.passwordStrength.score;\n      if (strength >= 4) return '非常强';\n      if (strength >= 3) return '强';\n      if (strength >= 2) return '中等';\n      return '弱';\n    },\n    passwordStrengthPercentage() {\n      return this.passwordStrength.score / 4 * 100;\n    }\n  },\n  mounted() {\n    // 应用初始选项\n    Object.assign(this.options, this.initialOptions);\n    this.generatePassword();\n  },\n  methods: {\n    generatePassword() {\n      this.generating = true;\n      setTimeout(() => {\n        let charset = '';\n        if (this.options.includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';\n        if (this.options.includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz';\n        if (this.options.includeNumbers) charset += '0123456789';\n        if (this.options.includeSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';\n        if (this.options.customChars) {\n          charset += this.options.customChars;\n        }\n        if (this.options.excludeSimilar) {\n          charset = charset.replace(/[0Ol1I]/g, '');\n        }\n        if (this.options.excludeAmbiguous) {\n          charset = charset.replace(/[{}[\\]()/\\\\'\"~,;.<>]/g, '');\n        }\n        if (!charset) {\n          this.generatedPassword = '';\n          this.generating = false;\n          return;\n        }\n        let password = '';\n        for (let i = 0; i < this.options.length; i++) {\n          password += charset.charAt(Math.floor(Math.random() * charset.length));\n        }\n        this.generatedPassword = password;\n        this.generating = false;\n\n        // 发出事件\n        this.$emit('password-generated', password);\n      }, 100);\n    },\n    togglePasswordVisibility() {\n      this.showPassword = !this.showPassword;\n    },\n    async copyPassword() {\n      try {\n        await navigator.clipboard.writeText(this.generatedPassword);\n        this.copied = true;\n        setTimeout(() => {\n          this.copied = false;\n        }, 2000);\n      } catch (err) {\n        console.error('复制失败:', err);\n      }\n    },\n    applyTemplate(template) {\n      Object.assign(this.options, template.options);\n      this.generatePassword();\n    },\n    calculatePasswordStrength(password) {\n      let score = 0;\n      let feedback = [];\n      if (!password) return {\n        score: 0,\n        feedback: ['请生成密码']\n      };\n\n      // 长度检查\n      if (password.length >= 12) score += 1;else if (password.length >= 8) score += 0.5;else feedback.push('密码长度至少8位');\n\n      // 字符类型检查\n      if (/[a-z]/.test(password)) score += 0.5;\n      if (/[A-Z]/.test(password)) score += 0.5;\n      if (/[0-9]/.test(password)) score += 0.5;\n      if (/[^a-zA-Z0-9]/.test(password)) score += 1;\n\n      // 复杂度检查\n      const uniqueChars = new Set(password).size;\n      if (uniqueChars >= password.length * 0.7) score += 0.5;\n      return {\n        score: Math.min(4, Math.round(score)),\n        feedback\n      };\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "emits", "props", "initialOptions", "type", "Object", "default", "data", "generatedPassword", "showPassword", "copied", "generating", "options", "length", "includeUppercase", "includeLowercase", "includeNumbers", "includeSymbols", "excludeS<PERSON><PERSON>r", "excludeAmbiguous", "customChars", "templates", "computed", "passwordStrength", "calculatePasswordStrength", "passwordStrengthClass", "strength", "score", "passwordStrengthTextClass", "passwordStrengthBarClass", "passwordStrengthText", "passwordStrengthPercentage", "mounted", "assign", "generatePassword", "methods", "setTimeout", "charset", "replace", "password", "i", "char<PERSON>t", "Math", "floor", "random", "$emit", "togglePasswordVisibility", "copyPassword", "navigator", "clipboard", "writeText", "err", "console", "error", "applyTemplate", "template", "feedback", "push", "test", "uniqueChars", "Set", "size", "min", "round"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\AdvancedPasswordGenerator.vue"], "sourcesContent": ["<template>\n  <div class=\"password-generator\">\n    <!-- 生成的密码显示 -->\n    <div class=\"mb-6\">\n      <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n        生成的密码\n      </label>\n      <div class=\"relative\">\n        <input\n          :type=\"showPassword ? 'text' : 'password'\"\n          :value=\"generatedPassword\"\n          readonly\n          class=\"w-full px-4 py-3 pr-20 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg font-mono text-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:text-white\"\n          :class=\"passwordStrengthClass\"\n        />\n        <div class=\"absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1\">\n          <button\n            @click=\"togglePasswordVisibility\"\n            class=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\"\n            type=\"button\"\n          >\n            <font-awesome-icon :icon=\"['fas', showPassword ? 'eye-slash' : 'eye']\" />\n          </button>\n          <button\n            @click=\"copyPassword\"\n            class=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\"\n            type=\"button\"\n          >\n            <font-awesome-icon :icon=\"['fas', copied ? 'check' : 'copy']\" :class=\"{ 'text-green-500': copied }\" />\n          </button>\n          <button\n            @click=\"generatePassword\"\n            class=\"p-2 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-200 transition-colors\"\n            type=\"button\"\n          >\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" :class=\"{ 'animate-spin': generating }\" />\n          </button>\n        </div>\n      </div>\n      \n      <!-- 密码强度指示器 -->\n      <div class=\"mt-2\">\n        <div class=\"flex items-center justify-between text-sm\">\n          <span class=\"text-gray-600 dark:text-gray-400\">密码强度</span>\n          <span :class=\"passwordStrengthTextClass\">{{ passwordStrengthText }}</span>\n        </div>\n        <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1\">\n          <div\n            class=\"h-2 rounded-full transition-all duration-300\"\n            :class=\"passwordStrengthBarClass\"\n            :style=\"{ width: passwordStrengthPercentage + '%' }\"\n          ></div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 密码选项 -->\n    <div class=\"space-y-4\">\n      <!-- 密码长度 -->\n      <div>\n        <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          密码长度: {{ options.length }}\n        </label>\n        <input\n          type=\"range\"\n          v-model.number=\"options.length\"\n          min=\"8\"\n          max=\"64\"\n          class=\"w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider\"\n          @input=\"generatePassword\"\n        />\n        <div class=\"flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1\">\n          <span>8</span>\n          <span>64</span>\n        </div>\n      </div>\n\n      <!-- 字符类型选项 -->\n      <div class=\"grid grid-cols-2 gap-4\">\n        <label class=\"flex items-center space-x-3 cursor-pointer\">\n          <input\n            type=\"checkbox\"\n            v-model=\"options.includeUppercase\"\n            @change=\"generatePassword\"\n            class=\"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n          />\n          <span class=\"text-sm text-gray-700 dark:text-gray-300\">大写字母 (A-Z)</span>\n        </label>\n        \n        <label class=\"flex items-center space-x-3 cursor-pointer\">\n          <input\n            type=\"checkbox\"\n            v-model=\"options.includeLowercase\"\n            @change=\"generatePassword\"\n            class=\"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n          />\n          <span class=\"text-sm text-gray-700 dark:text-gray-300\">小写字母 (a-z)</span>\n        </label>\n        \n        <label class=\"flex items-center space-x-3 cursor-pointer\">\n          <input\n            type=\"checkbox\"\n            v-model=\"options.includeNumbers\"\n            @change=\"generatePassword\"\n            class=\"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n          />\n          <span class=\"text-sm text-gray-700 dark:text-gray-300\">数字 (0-9)</span>\n        </label>\n        \n        <label class=\"flex items-center space-x-3 cursor-pointer\">\n          <input\n            type=\"checkbox\"\n            v-model=\"options.includeSymbols\"\n            @change=\"generatePassword\"\n            class=\"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n          />\n          <span class=\"text-sm text-gray-700 dark:text-gray-300\">特殊字符 (!@#$)</span>\n        </label>\n      </div>\n\n      <!-- 高级选项 -->\n      <div class=\"border-t border-gray-200 dark:border-gray-700 pt-4\">\n        <h4 class=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">高级选项</h4>\n        \n        <div class=\"space-y-3\">\n          <label class=\"flex items-center space-x-3 cursor-pointer\">\n            <input\n              type=\"checkbox\"\n              v-model=\"options.excludeSimilar\"\n              @change=\"generatePassword\"\n              class=\"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n            />\n            <span class=\"text-sm text-gray-700 dark:text-gray-300\">排除相似字符 (0, O, l, 1, I)</span>\n          </label>\n          \n          <label class=\"flex items-center space-x-3 cursor-pointer\">\n            <input\n              type=\"checkbox\"\n              v-model=\"options.excludeAmbiguous\"\n              @change=\"generatePassword\"\n              class=\"w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500\"\n            />\n            <span class=\"text-sm text-gray-700 dark:text-gray-300\">排除歧义字符 ({{ '{' }} {{ '}' }} [ ] ( ) / \\ ' \" ~ , ; . &lt; &gt;)</span>\n          </label>\n          \n          <div class=\"flex items-center space-x-3\">\n            <label class=\"text-sm text-gray-700 dark:text-gray-300\">自定义字符:</label>\n            <input\n              type=\"text\"\n              v-model=\"options.customChars\"\n              @input=\"generatePassword\"\n              placeholder=\"添加自定义字符\"\n              class=\"flex-1 px-3 py-1 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:text-white\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- 预设模板 -->\n      <div class=\"border-t border-gray-200 dark:border-gray-700 pt-4\">\n        <h4 class=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">快速模板</h4>\n        <div class=\"grid grid-cols-2 gap-2\">\n          <button\n            v-for=\"template in templates\"\n            :key=\"template.name\"\n            @click=\"applyTemplate(template)\"\n            class=\"px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors\"\n          >\n            {{ template.name }}\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'AdvancedPasswordGenerator',\n  emits: ['password-generated'],\n  props: {\n    initialOptions: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      generatedPassword: '',\n      showPassword: false,\n      copied: false,\n      generating: false,\n      options: {\n        length: 16,\n        includeUppercase: true,\n        includeLowercase: true,\n        includeNumbers: true,\n        includeSymbols: true,\n        excludeSimilar: false,\n        excludeAmbiguous: false,\n        customChars: ''\n      },\n      templates: [\n        {\n          name: '高强度',\n          options: { length: 20, includeUppercase: true, includeLowercase: true, includeNumbers: true, includeSymbols: true }\n        },\n        {\n          name: '标准',\n          options: { length: 12, includeUppercase: true, includeLowercase: true, includeNumbers: true, includeSymbols: false }\n        },\n        {\n          name: '简单',\n          options: { length: 8, includeUppercase: true, includeLowercase: true, includeNumbers: true, includeSymbols: false }\n        },\n        {\n          name: '数字密码',\n          options: { length: 6, includeUppercase: false, includeLowercase: false, includeNumbers: true, includeSymbols: false }\n        }\n      ]\n    }\n  },\n  computed: {\n    passwordStrength() {\n      return this.calculatePasswordStrength(this.generatedPassword)\n    },\n    passwordStrengthClass() {\n      const strength = this.passwordStrength.score\n      if (strength >= 4) return 'border-green-500 dark:border-green-400'\n      if (strength >= 3) return 'border-yellow-500 dark:border-yellow-400'\n      if (strength >= 2) return 'border-orange-500 dark:border-orange-400'\n      return 'border-red-500 dark:border-red-400'\n    },\n    passwordStrengthTextClass() {\n      const strength = this.passwordStrength.score\n      if (strength >= 4) return 'text-green-600 dark:text-green-400 font-medium'\n      if (strength >= 3) return 'text-yellow-600 dark:text-yellow-400 font-medium'\n      if (strength >= 2) return 'text-orange-600 dark:text-orange-400 font-medium'\n      return 'text-red-600 dark:text-red-400 font-medium'\n    },\n    passwordStrengthBarClass() {\n      const strength = this.passwordStrength.score\n      if (strength >= 4) return 'bg-green-500'\n      if (strength >= 3) return 'bg-yellow-500'\n      if (strength >= 2) return 'bg-orange-500'\n      return 'bg-red-500'\n    },\n    passwordStrengthText() {\n      const strength = this.passwordStrength.score\n      if (strength >= 4) return '非常强'\n      if (strength >= 3) return '强'\n      if (strength >= 2) return '中等'\n      return '弱'\n    },\n    passwordStrengthPercentage() {\n      return (this.passwordStrength.score / 4) * 100\n    }\n  },\n  mounted() {\n    // 应用初始选项\n    Object.assign(this.options, this.initialOptions)\n    this.generatePassword()\n  },\n  methods: {\n    generatePassword() {\n      this.generating = true\n      \n      setTimeout(() => {\n        let charset = ''\n        \n        if (this.options.includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'\n        if (this.options.includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz'\n        if (this.options.includeNumbers) charset += '0123456789'\n        if (this.options.includeSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?'\n        \n        if (this.options.customChars) {\n          charset += this.options.customChars\n        }\n        \n        if (this.options.excludeSimilar) {\n          charset = charset.replace(/[0Ol1I]/g, '')\n        }\n        \n        if (this.options.excludeAmbiguous) {\n          charset = charset.replace(/[{}[\\]()/\\\\'\"~,;.<>]/g, '')\n        }\n        \n        if (!charset) {\n          this.generatedPassword = ''\n          this.generating = false\n          return\n        }\n        \n        let password = ''\n        for (let i = 0; i < this.options.length; i++) {\n          password += charset.charAt(Math.floor(Math.random() * charset.length))\n        }\n        \n        this.generatedPassword = password\n        this.generating = false\n        \n        // 发出事件\n        this.$emit('password-generated', password)\n      }, 100)\n    },\n    \n    togglePasswordVisibility() {\n      this.showPassword = !this.showPassword\n    },\n    \n    async copyPassword() {\n      try {\n        await navigator.clipboard.writeText(this.generatedPassword)\n        this.copied = true\n        setTimeout(() => {\n          this.copied = false\n        }, 2000)\n      } catch (err) {\n        console.error('复制失败:', err)\n      }\n    },\n    \n    applyTemplate(template) {\n      Object.assign(this.options, template.options)\n      this.generatePassword()\n    },\n    \n    calculatePasswordStrength(password) {\n      let score = 0\n      let feedback = []\n      \n      if (!password) return { score: 0, feedback: ['请生成密码'] }\n      \n      // 长度检查\n      if (password.length >= 12) score += 1\n      else if (password.length >= 8) score += 0.5\n      else feedback.push('密码长度至少8位')\n      \n      // 字符类型检查\n      if (/[a-z]/.test(password)) score += 0.5\n      if (/[A-Z]/.test(password)) score += 0.5\n      if (/[0-9]/.test(password)) score += 0.5\n      if (/[^a-zA-Z0-9]/.test(password)) score += 1\n      \n      // 复杂度检查\n      const uniqueChars = new Set(password).size\n      if (uniqueChars >= password.length * 0.7) score += 0.5\n      \n      return {\n        score: Math.min(4, Math.round(score)),\n        feedback\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.slider::-webkit-slider-thumb {\n  appearance: none;\n  height: 20px;\n  width: 20px;\n  border-radius: 50%;\n  background: #3b82f6;\n  cursor: pointer;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n}\n\n.slider::-moz-range-thumb {\n  height: 20px;\n  width: 20px;\n  border-radius: 50%;\n  background: #3b82f6;\n  cursor: pointer;\n  border: none;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n}\n</style>\n"], "mappings": "AAiLA,eAAe;EACbA,IAAI,EAAE,2BAA2B;EACjCC,KAAK,EAAE,CAAC,oBAAoB,CAAC;EAC7BC,KAAK,EAAE;IACLC,cAAc,EAAE;MACdC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB;EACF,CAAC;EACDC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,iBAAiB,EAAE,EAAE;MACrBC,YAAY,EAAE,KAAK;MACnBC,MAAM,EAAE,KAAK;MACbC,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE;QACPC,MAAM,EAAE,EAAE;QACVC,gBAAgB,EAAE,IAAI;QACtBC,gBAAgB,EAAE,IAAI;QACtBC,cAAc,EAAE,IAAI;QACpBC,cAAc,EAAE,IAAI;QACpBC,cAAc,EAAE,KAAK;QACrBC,gBAAgB,EAAE,KAAK;QACvBC,WAAW,EAAE;MACf,CAAC;MACDC,SAAS,EAAE,CACT;QACErB,IAAI,EAAE,KAAK;QACXY,OAAO,EAAE;UAAEC,MAAM,EAAE,EAAE;UAAEC,gBAAgB,EAAE,IAAI;UAAEC,gBAAgB,EAAE,IAAI;UAAEC,cAAc,EAAE,IAAI;UAAEC,cAAc,EAAE;QAAK;MACpH,CAAC,EACD;QACEjB,IAAI,EAAE,IAAI;QACVY,OAAO,EAAE;UAAEC,MAAM,EAAE,EAAE;UAAEC,gBAAgB,EAAE,IAAI;UAAEC,gBAAgB,EAAE,IAAI;UAAEC,cAAc,EAAE,IAAI;UAAEC,cAAc,EAAE;QAAM;MACrH,CAAC,EACD;QACEjB,IAAI,EAAE,IAAI;QACVY,OAAO,EAAE;UAAEC,MAAM,EAAE,CAAC;UAAEC,gBAAgB,EAAE,IAAI;UAAEC,gBAAgB,EAAE,IAAI;UAAEC,cAAc,EAAE,IAAI;UAAEC,cAAc,EAAE;QAAM;MACpH,CAAC,EACD;QACEjB,IAAI,EAAE,MAAM;QACZY,OAAO,EAAE;UAAEC,MAAM,EAAE,CAAC;UAAEC,gBAAgB,EAAE,KAAK;UAAEC,gBAAgB,EAAE,KAAK;UAAEC,cAAc,EAAE,IAAI;UAAEC,cAAc,EAAE;QAAM;MACtH;IAEJ;EACF,CAAC;EACDK,QAAQ,EAAE;IACRC,gBAAgBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACC,yBAAyB,CAAC,IAAI,CAAChB,iBAAiB;IAC9D,CAAC;IACDiB,qBAAqBA,CAAA,EAAG;MACtB,MAAMC,QAAO,GAAI,IAAI,CAACH,gBAAgB,CAACI,KAAI;MAC3C,IAAID,QAAO,IAAK,CAAC,EAAE,OAAO,wCAAuC;MACjE,IAAIA,QAAO,IAAK,CAAC,EAAE,OAAO,0CAAyC;MACnE,IAAIA,QAAO,IAAK,CAAC,EAAE,OAAO,0CAAyC;MACnE,OAAO,oCAAmC;IAC5C,CAAC;IACDE,yBAAyBA,CAAA,EAAG;MAC1B,MAAMF,QAAO,GAAI,IAAI,CAACH,gBAAgB,CAACI,KAAI;MAC3C,IAAID,QAAO,IAAK,CAAC,EAAE,OAAO,gDAA+C;MACzE,IAAIA,QAAO,IAAK,CAAC,EAAE,OAAO,kDAAiD;MAC3E,IAAIA,QAAO,IAAK,CAAC,EAAE,OAAO,kDAAiD;MAC3E,OAAO,4CAA2C;IACpD,CAAC;IACDG,wBAAwBA,CAAA,EAAG;MACzB,MAAMH,QAAO,GAAI,IAAI,CAACH,gBAAgB,CAACI,KAAI;MAC3C,IAAID,QAAO,IAAK,CAAC,EAAE,OAAO,cAAa;MACvC,IAAIA,QAAO,IAAK,CAAC,EAAE,OAAO,eAAc;MACxC,IAAIA,QAAO,IAAK,CAAC,EAAE,OAAO,eAAc;MACxC,OAAO,YAAW;IACpB,CAAC;IACDI,oBAAoBA,CAAA,EAAG;MACrB,MAAMJ,QAAO,GAAI,IAAI,CAACH,gBAAgB,CAACI,KAAI;MAC3C,IAAID,QAAO,IAAK,CAAC,EAAE,OAAO,KAAI;MAC9B,IAAIA,QAAO,IAAK,CAAC,EAAE,OAAO,GAAE;MAC5B,IAAIA,QAAO,IAAK,CAAC,EAAE,OAAO,IAAG;MAC7B,OAAO,GAAE;IACX,CAAC;IACDK,0BAA0BA,CAAA,EAAG;MAC3B,OAAQ,IAAI,CAACR,gBAAgB,CAACI,KAAI,GAAI,CAAC,GAAI,GAAE;IAC/C;EACF,CAAC;EACDK,OAAOA,CAAA,EAAG;IACR;IACA3B,MAAM,CAAC4B,MAAM,CAAC,IAAI,CAACrB,OAAO,EAAE,IAAI,CAACT,cAAc;IAC/C,IAAI,CAAC+B,gBAAgB,CAAC;EACxB,CAAC;EACDC,OAAO,EAAE;IACPD,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACvB,UAAS,GAAI,IAAG;MAErByB,UAAU,CAAC,MAAM;QACf,IAAIC,OAAM,GAAI,EAAC;QAEf,IAAI,IAAI,CAACzB,OAAO,CAACE,gBAAgB,EAAEuB,OAAM,IAAK,4BAA2B;QACzE,IAAI,IAAI,CAACzB,OAAO,CAACG,gBAAgB,EAAEsB,OAAM,IAAK,4BAA2B;QACzE,IAAI,IAAI,CAACzB,OAAO,CAACI,cAAc,EAAEqB,OAAM,IAAK,YAAW;QACvD,IAAI,IAAI,CAACzB,OAAO,CAACK,cAAc,EAAEoB,OAAM,IAAK,4BAA2B;QAEvE,IAAI,IAAI,CAACzB,OAAO,CAACQ,WAAW,EAAE;UAC5BiB,OAAM,IAAK,IAAI,CAACzB,OAAO,CAACQ,WAAU;QACpC;QAEA,IAAI,IAAI,CAACR,OAAO,CAACM,cAAc,EAAE;UAC/BmB,OAAM,GAAIA,OAAO,CAACC,OAAO,CAAC,UAAU,EAAE,EAAE;QAC1C;QAEA,IAAI,IAAI,CAAC1B,OAAO,CAACO,gBAAgB,EAAE;UACjCkB,OAAM,GAAIA,OAAO,CAACC,OAAO,CAAC,uBAAuB,EAAE,EAAE;QACvD;QAEA,IAAI,CAACD,OAAO,EAAE;UACZ,IAAI,CAAC7B,iBAAgB,GAAI,EAAC;UAC1B,IAAI,CAACG,UAAS,GAAI,KAAI;UACtB;QACF;QAEA,IAAI4B,QAAO,GAAI,EAAC;QAChB,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAI,IAAI,CAAC5B,OAAO,CAACC,MAAM,EAAE2B,CAAC,EAAE,EAAE;UAC5CD,QAAO,IAAKF,OAAO,CAACI,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIP,OAAO,CAACxB,MAAM,CAAC;QACvE;QAEA,IAAI,CAACL,iBAAgB,GAAI+B,QAAO;QAChC,IAAI,CAAC5B,UAAS,GAAI,KAAI;;QAEtB;QACA,IAAI,CAACkC,KAAK,CAAC,oBAAoB,EAAEN,QAAQ;MAC3C,CAAC,EAAE,GAAG;IACR,CAAC;IAEDO,wBAAwBA,CAAA,EAAG;MACzB,IAAI,CAACrC,YAAW,GAAI,CAAC,IAAI,CAACA,YAAW;IACvC,CAAC;IAED,MAAMsC,YAAYA,CAAA,EAAG;MACnB,IAAI;QACF,MAAMC,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,CAAC1C,iBAAiB;QAC1D,IAAI,CAACE,MAAK,GAAI,IAAG;QACjB0B,UAAU,CAAC,MAAM;UACf,IAAI,CAAC1B,MAAK,GAAI,KAAI;QACpB,CAAC,EAAE,IAAI;MACT,EAAE,OAAOyC,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,OAAO,EAAEF,GAAG;MAC5B;IACF,CAAC;IAEDG,aAAaA,CAACC,QAAQ,EAAE;MACtBlD,MAAM,CAAC4B,MAAM,CAAC,IAAI,CAACrB,OAAO,EAAE2C,QAAQ,CAAC3C,OAAO;MAC5C,IAAI,CAACsB,gBAAgB,CAAC;IACxB,CAAC;IAEDV,yBAAyBA,CAACe,QAAQ,EAAE;MAClC,IAAIZ,KAAI,GAAI;MACZ,IAAI6B,QAAO,GAAI,EAAC;MAEhB,IAAI,CAACjB,QAAQ,EAAE,OAAO;QAAEZ,KAAK,EAAE,CAAC;QAAE6B,QAAQ,EAAE,CAAC,OAAO;MAAE;;MAEtD;MACA,IAAIjB,QAAQ,CAAC1B,MAAK,IAAK,EAAE,EAAEc,KAAI,IAAK,OAC/B,IAAIY,QAAQ,CAAC1B,MAAK,IAAK,CAAC,EAAEc,KAAI,IAAK,GAAE,MACrC6B,QAAQ,CAACC,IAAI,CAAC,UAAU;;MAE7B;MACA,IAAI,OAAO,CAACC,IAAI,CAACnB,QAAQ,CAAC,EAAEZ,KAAI,IAAK,GAAE;MACvC,IAAI,OAAO,CAAC+B,IAAI,CAACnB,QAAQ,CAAC,EAAEZ,KAAI,IAAK,GAAE;MACvC,IAAI,OAAO,CAAC+B,IAAI,CAACnB,QAAQ,CAAC,EAAEZ,KAAI,IAAK,GAAE;MACvC,IAAI,cAAc,CAAC+B,IAAI,CAACnB,QAAQ,CAAC,EAAEZ,KAAI,IAAK;;MAE5C;MACA,MAAMgC,WAAU,GAAI,IAAIC,GAAG,CAACrB,QAAQ,CAAC,CAACsB,IAAG;MACzC,IAAIF,WAAU,IAAKpB,QAAQ,CAAC1B,MAAK,GAAI,GAAG,EAAEc,KAAI,IAAK,GAAE;MAErD,OAAO;QACLA,KAAK,EAAEe,IAAI,CAACoB,GAAG,CAAC,CAAC,EAAEpB,IAAI,CAACqB,KAAK,CAACpC,KAAK,CAAC,CAAC;QACrC6B;MACF;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}