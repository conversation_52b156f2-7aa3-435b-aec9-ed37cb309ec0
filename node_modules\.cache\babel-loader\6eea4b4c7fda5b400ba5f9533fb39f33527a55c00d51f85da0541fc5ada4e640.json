{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeStyle as _normalizeStyle } from \"vue\";\nconst _hoisted_1 = {\n  class: \"security-dashboard\"\n};\nconst _hoisted_2 = {\n  class: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\"\n};\nconst _hoisted_3 = {\n  class: \"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\"\n};\nconst _hoisted_4 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_5 = {\n  class: \"flex-shrink-0\"\n};\nconst _hoisted_6 = {\n  class: \"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center\"\n};\nconst _hoisted_7 = {\n  class: \"ml-4\"\n};\nconst _hoisted_8 = {\n  class: \"text-2xl font-bold text-gray-900 dark:text-white\"\n};\nconst _hoisted_9 = {\n  class: \"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\"\n};\nconst _hoisted_10 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_11 = {\n  class: \"flex-shrink-0\"\n};\nconst _hoisted_12 = {\n  class: \"w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center\"\n};\nconst _hoisted_13 = {\n  class: \"ml-4\"\n};\nconst _hoisted_14 = {\n  class: \"text-2xl font-bold text-yellow-600 dark:text-yellow-400\"\n};\nconst _hoisted_15 = {\n  class: \"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\"\n};\nconst _hoisted_16 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_17 = {\n  class: \"flex-shrink-0\"\n};\nconst _hoisted_18 = {\n  class: \"w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center\"\n};\nconst _hoisted_19 = {\n  class: \"ml-4\"\n};\nconst _hoisted_20 = {\n  class: \"text-2xl font-bold text-red-600 dark:text-red-400\"\n};\nconst _hoisted_21 = {\n  class: \"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\"\n};\nconst _hoisted_22 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_23 = {\n  class: \"flex-shrink-0\"\n};\nconst _hoisted_24 = {\n  class: \"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center\"\n};\nconst _hoisted_25 = {\n  class: \"ml-4\"\n};\nconst _hoisted_26 = {\n  class: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\"\n};\nconst _hoisted_27 = {\n  class: \"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\"\n};\nconst _hoisted_28 = {\n  class: \"space-y-4\"\n};\nconst _hoisted_29 = {\n  class: \"flex items-center space-x-3\"\n};\nconst _hoisted_30 = {\n  class: \"text-sm text-gray-700 dark:text-gray-300\"\n};\nconst _hoisted_31 = {\n  class: \"flex items-center space-x-2\"\n};\nconst _hoisted_32 = {\n  class: \"text-sm font-medium text-gray-900 dark:text-white\"\n};\nconst _hoisted_33 = {\n  class: \"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\"\n};\nconst _hoisted_34 = {\n  class: \"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\"\n};\nconst _hoisted_35 = {\n  class: \"space-y-4\"\n};\nconst _hoisted_36 = {\n  class: \"flex-shrink-0\"\n};\nconst _hoisted_37 = {\n  class: \"flex-1 min-w-0\"\n};\nconst _hoisted_38 = {\n  class: \"text-sm text-gray-900 dark:text-white\"\n};\nconst _hoisted_39 = {\n  class: \"text-xs text-gray-500 dark:text-gray-400\"\n};\nconst _hoisted_40 = {\n  class: \"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\"\n};\nconst _hoisted_41 = {\n  class: \"overflow-x-auto\"\n};\nconst _hoisted_42 = {\n  class: \"min-w-full\"\n};\nconst _hoisted_43 = {\n  class: \"divide-y divide-gray-200 dark:divide-gray-700\"\n};\nconst _hoisted_44 = {\n  class: \"py-3 px-4\"\n};\nconst _hoisted_45 = {\n  class: \"flex items-center space-x-2\"\n};\nconst _hoisted_46 = {\n  class: \"text-sm font-medium text-gray-900 dark:text-white\"\n};\nconst _hoisted_47 = {\n  class: \"py-3 px-4 text-sm text-gray-500 dark:text-gray-400\"\n};\nconst _hoisted_48 = {\n  class: \"py-3 px-4\"\n};\nconst _hoisted_49 = {\n  class: \"py-3 px-4 text-sm text-gray-500 dark:text-gray-400\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 安全概览卡片 \"), _createElementVNode(\"div\", _hoisted_2, [_createCommentVNode(\" 总主机数 \"), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'server'],\n    class: \"text-blue-600 dark:text-blue-400 text-xl\"\n  })])]), _createElementVNode(\"div\", _hoisted_7, [_cache[0] || (_cache[0] = _createElementVNode(\"p\", {\n    class: \"text-sm font-medium text-gray-500 dark:text-gray-400\"\n  }, \"总主机数\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_8, _toDisplayString($options.totalHosts), 1 /* TEXT */)])])]), _createCommentVNode(\" 密码过期警告 \"), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'exclamation-triangle'],\n    class: \"text-yellow-600 dark:text-yellow-400 text-xl\"\n  })])]), _createElementVNode(\"div\", _hoisted_13, [_cache[1] || (_cache[1] = _createElementVNode(\"p\", {\n    class: \"text-sm font-medium text-gray-500 dark:text-gray-400\"\n  }, \"即将过期\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_14, _toDisplayString($options.expiringPasswords), 1 /* TEXT */)])])]), _createCommentVNode(\" 弱密码数量 \"), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"text-red-600 dark:text-red-400 text-xl\"\n  })])]), _createElementVNode(\"div\", _hoisted_19, [_cache[2] || (_cache[2] = _createElementVNode(\"p\", {\n    class: \"text-sm font-medium text-gray-500 dark:text-gray-400\"\n  }, \"弱密码\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_20, _toDisplayString($options.weakPasswords), 1 /* TEXT */)])])]), _createCommentVNode(\" 安全评分 \"), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'check-circle'],\n    class: \"text-green-600 dark:text-green-400 text-xl\"\n  })])]), _createElementVNode(\"div\", _hoisted_25, [_cache[3] || (_cache[3] = _createElementVNode(\"p\", {\n    class: \"text-sm font-medium text-gray-500 dark:text-gray-400\"\n  }, \"安全评分\", -1 /* HOISTED */)), _createElementVNode(\"p\", {\n    class: _normalizeClass([\"text-2xl font-bold\", $options.securityScoreClass])\n  }, _toDisplayString($options.securityScore) + \"/100\", 3 /* TEXT, CLASS */)])])])]), _createCommentVNode(\" 安全趋势图表和风险分析 \"), _createElementVNode(\"div\", _hoisted_26, [_createCommentVNode(\" 密码状态分布 \"), _createElementVNode(\"div\", _hoisted_27, [_cache[4] || (_cache[4] = _createElementVNode(\"h3\", {\n    class: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\"\n  }, \"密码状态分布\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_28, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.passwordStatusData, status => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: status.label,\n      class: \"flex items-center justify-between\"\n    }, [_createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"w-4 h-4 rounded-full\", status.color])\n    }, null, 2 /* CLASS */), _createElementVNode(\"span\", _hoisted_30, _toDisplayString(status.label), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"span\", _hoisted_32, _toDisplayString(status.count), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"h-2 rounded-full\", status.color]),\n      style: _normalizeStyle({\n        width: status.percentage + '%'\n      })\n    }, null, 6 /* CLASS, STYLE */)])])]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 最近活动 \"), _createElementVNode(\"div\", _hoisted_34, [_cache[5] || (_cache[5] = _createElementVNode(\"h3\", {\n    class: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\"\n  }, \"最近活动\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_35, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.recentActivities, activity => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: activity.id,\n      class: \"flex items-start space-x-3\"\n    }, [_createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"w-8 h-8 rounded-full flex items-center justify-center\", activity.iconBg])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', activity.icon],\n      class: _normalizeClass([activity.iconColor, \"text-sm\"])\n    }, null, 8 /* PROPS */, [\"icon\", \"class\"])], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"p\", _hoisted_38, _toDisplayString(activity.message), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_39, _toDisplayString(activity.time), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])]), _createCommentVNode(\" 风险主机列表 \"), _createElementVNode(\"div\", _hoisted_40, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n    class: \"flex items-center justify-between mb-4\"\n  }, [_createElementVNode(\"h3\", {\n    class: \"text-lg font-semibold text-gray-900 dark:text-white\"\n  }, \"高风险主机\"), _createElementVNode(\"button\", {\n    class: \"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300\"\n  }, \" 查看全部 \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_41, [_createElementVNode(\"table\", _hoisted_42, [_cache[7] || (_cache[7] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", {\n    class: \"border-b border-gray-200 dark:border-gray-700\"\n  }, [_createElementVNode(\"th\", {\n    class: \"text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400\"\n  }, \"主机名\"), _createElementVNode(\"th\", {\n    class: \"text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400\"\n  }, \"IP地址\"), _createElementVNode(\"th\", {\n    class: \"text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400\"\n  }, \"风险等级\"), _createElementVNode(\"th\", {\n    class: \"text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400\"\n  }, \"主要问题\"), _createElementVNode(\"th\", {\n    class: \"text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400\"\n  }, \"操作\")])], -1 /* HOISTED */)), _createElementVNode(\"tbody\", _hoisted_43, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.riskHosts, host => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: host.id,\n      class: \"hover:bg-gray-50 dark:hover:bg-gray-700/50\"\n    }, [_createElementVNode(\"td\", _hoisted_44, [_createElementVNode(\"div\", _hoisted_45, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'server'],\n      class: \"text-gray-400\"\n    }), _createElementVNode(\"span\", _hoisted_46, _toDisplayString(host.name), 1 /* TEXT */)])]), _createElementVNode(\"td\", _hoisted_47, _toDisplayString(host.ip), 1 /* TEXT */), _createElementVNode(\"td\", _hoisted_48, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\", host.riskLevelClass])\n    }, _toDisplayString(host.riskLevel), 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", _hoisted_49, _toDisplayString(host.issues.join(', ')), 1 /* TEXT */), _cache[6] || (_cache[6] = _createElementVNode(\"td\", {\n      class: \"py-3 px-4\"\n    }, [_createElementVNode(\"button\", {\n      class: \"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300\"\n    }, \" 立即修复 \")], -1 /* HOISTED */))]);\n  }), 128 /* KEYED_FRAGMENT */))])])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_createVNode", "_component_font_awesome_icon", "icon", "_hoisted_7", "_hoisted_8", "_toDisplayString", "$options", "totalHosts", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "expiringPasswords", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "weakPasswords", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_normalizeClass", "securityScoreClass", "securityScore", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_Fragment", "_renderList", "passwordStatusData", "status", "key", "label", "_hoisted_29", "color", "_hoisted_30", "_hoisted_31", "_hoisted_32", "count", "_hoisted_33", "style", "_normalizeStyle", "width", "percentage", "_hoisted_34", "_hoisted_35", "recentActivities", "activity", "id", "_hoisted_36", "iconBg", "iconColor", "_hoisted_37", "_hoisted_38", "message", "_hoisted_39", "time", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_hoisted_43", "riskHosts", "host", "_hoisted_44", "_hoisted_45", "_hoisted_46", "name", "_hoisted_47", "ip", "_hoisted_48", "riskLevelClass", "riskLevel", "_hoisted_49", "issues", "join"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\SecurityDashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"security-dashboard\">\n    <!-- 安全概览卡片 -->\n    <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n      <!-- 总主机数 -->\n      <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center\">\n              <font-awesome-icon :icon=\"['fas', 'server']\" class=\"text-blue-600 dark:text-blue-400 text-xl\" />\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">总主机数</p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ totalHosts }}</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- 密码过期警告 -->\n      <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center\">\n              <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"text-yellow-600 dark:text-yellow-400 text-xl\" />\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">即将过期</p>\n            <p class=\"text-2xl font-bold text-yellow-600 dark:text-yellow-400\">{{ expiringPasswords }}</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- 弱密码数量 -->\n      <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center\">\n              <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"text-red-600 dark:text-red-400 text-xl\" />\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">弱密码</p>\n            <p class=\"text-2xl font-bold text-red-600 dark:text-red-400\">{{ weakPasswords }}</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- 安全评分 -->\n      <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center\">\n              <font-awesome-icon :icon=\"['fas', 'check-circle']\" class=\"text-green-600 dark:text-green-400 text-xl\" />\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">安全评分</p>\n            <p class=\"text-2xl font-bold\" :class=\"securityScoreClass\">{{ securityScore }}/100</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 安全趋势图表和风险分析 -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n      <!-- 密码状态分布 -->\n      <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <h3 class=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">密码状态分布</h3>\n        <div class=\"space-y-4\">\n          <div v-for=\"status in passwordStatusData\" :key=\"status.label\" class=\"flex items-center justify-between\">\n            <div class=\"flex items-center space-x-3\">\n              <div class=\"w-4 h-4 rounded-full\" :class=\"status.color\"></div>\n              <span class=\"text-sm text-gray-700 dark:text-gray-300\">{{ status.label }}</span>\n            </div>\n            <div class=\"flex items-center space-x-2\">\n              <span class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ status.count }}</span>\n              <div class=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                <div class=\"h-2 rounded-full\" :class=\"status.color\" :style=\"{ width: status.percentage + '%' }\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 最近活动 -->\n      <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <h3 class=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">最近活动</h3>\n        <div class=\"space-y-4\">\n          <div v-for=\"activity in recentActivities\" :key=\"activity.id\" class=\"flex items-start space-x-3\">\n            <div class=\"flex-shrink-0\">\n              <div class=\"w-8 h-8 rounded-full flex items-center justify-center\" :class=\"activity.iconBg\">\n                <font-awesome-icon :icon=\"['fas', activity.icon]\" :class=\"activity.iconColor\" class=\"text-sm\" />\n              </div>\n            </div>\n            <div class=\"flex-1 min-w-0\">\n              <p class=\"text-sm text-gray-900 dark:text-white\">{{ activity.message }}</p>\n              <p class=\"text-xs text-gray-500 dark:text-gray-400\">{{ activity.time }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 风险主机列表 -->\n    <div class=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n      <div class=\"flex items-center justify-between mb-4\">\n        <h3 class=\"text-lg font-semibold text-gray-900 dark:text-white\">高风险主机</h3>\n        <button class=\"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300\">\n          查看全部\n        </button>\n      </div>\n      \n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full\">\n          <thead>\n            <tr class=\"border-b border-gray-200 dark:border-gray-700\">\n              <th class=\"text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400\">主机名</th>\n              <th class=\"text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400\">IP地址</th>\n              <th class=\"text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400\">风险等级</th>\n              <th class=\"text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400\">主要问题</th>\n              <th class=\"text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400\">操作</th>\n            </tr>\n          </thead>\n          <tbody class=\"divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-for=\"host in riskHosts\" :key=\"host.id\" class=\"hover:bg-gray-50 dark:hover:bg-gray-700/50\">\n              <td class=\"py-3 px-4\">\n                <div class=\"flex items-center space-x-2\">\n                  <font-awesome-icon :icon=\"['fas', 'server']\" class=\"text-gray-400\" />\n                  <span class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ host.name }}</span>\n                </div>\n              </td>\n              <td class=\"py-3 px-4 text-sm text-gray-500 dark:text-gray-400\">{{ host.ip }}</td>\n              <td class=\"py-3 px-4\">\n                <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\" :class=\"host.riskLevelClass\">\n                  {{ host.riskLevel }}\n                </span>\n              </td>\n              <td class=\"py-3 px-4 text-sm text-gray-500 dark:text-gray-400\">{{ host.issues.join(', ') }}</td>\n              <td class=\"py-3 px-4\">\n                <button class=\"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300\">\n                  立即修复\n                </button>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'SecurityDashboard',\n  props: {\n    hosts: {\n      type: Array,\n      default: () => []\n    }\n  },\n  computed: {\n    totalHosts() {\n      return this.hosts.length\n    },\n    \n    expiringPasswords() {\n      let count = 0\n      this.hosts.forEach(host => {\n        host.accounts.forEach(account => {\n          if (this.isPasswordExpiringSoon(account)) {\n            count++\n          }\n        })\n      })\n      return count\n    },\n    \n    weakPasswords() {\n      let count = 0\n      this.hosts.forEach(host => {\n        host.accounts.forEach(account => {\n          if (this.isPasswordWeak(account.password)) {\n            count++\n          }\n        })\n      })\n      return count\n    },\n    \n    securityScore() {\n      const total = this.totalHosts * 2 // 假设每台主机有2个账号\n      if (total === 0) return 100\n      \n      const issues = this.expiringPasswords + this.weakPasswords\n      const score = Math.max(0, Math.round((1 - issues / total) * 100))\n      return score\n    },\n    \n    securityScoreClass() {\n      if (this.securityScore >= 80) return 'text-green-600 dark:text-green-400'\n      if (this.securityScore >= 60) return 'text-yellow-600 dark:text-yellow-400'\n      return 'text-red-600 dark:text-red-400'\n    },\n    \n    passwordStatusData() {\n      const total = this.totalHosts * 2\n      const expired = Math.floor(total * 0.05)\n      const expiring = this.expiringPasswords\n      const weak = this.weakPasswords\n      const good = total - expired - expiring - weak\n      \n      return [\n        { label: '正常', count: good, percentage: (good / total) * 100, color: 'bg-green-500' },\n        { label: '即将过期', count: expiring, percentage: (expiring / total) * 100, color: 'bg-yellow-500' },\n        { label: '已过期', count: expired, percentage: (expired / total) * 100, color: 'bg-red-500' },\n        { label: '弱密码', count: weak, percentage: (weak / total) * 100, color: 'bg-orange-500' }\n      ]\n    },\n    \n    recentActivities() {\n      return [\n        {\n          id: 1,\n          message: '批量更新了生产环境密码',\n          time: '2分钟前',\n          icon: 'key',\n          iconColor: 'text-blue-600',\n          iconBg: 'bg-blue-100 dark:bg-blue-900/30'\n        },\n        {\n          id: 2,\n          message: '检测到3个弱密码',\n          time: '15分钟前',\n          icon: 'exclamation-triangle',\n          iconColor: 'text-yellow-600',\n          iconBg: 'bg-yellow-100 dark:bg-yellow-900/30'\n        },\n        {\n          id: 3,\n          message: '新增密码策略\"高强度策略\"',\n          time: '1小时前',\n          icon: 'shield-alt',\n          iconColor: 'text-green-600',\n          iconBg: 'bg-green-100 dark:bg-green-900/30'\n        },\n        {\n          id: 4,\n          message: '定时任务执行完成',\n          time: '2小时前',\n          icon: 'clock',\n          iconColor: 'text-purple-600',\n          iconBg: 'bg-purple-100 dark:bg-purple-900/30'\n        }\n      ]\n    },\n    \n    riskHosts() {\n      return this.hosts.filter(host => {\n        const hasExpiredPasswords = host.accounts.some(account => this.isPasswordExpired(account))\n        const hasWeakPasswords = host.accounts.some(account => this.isPasswordWeak(account.password))\n        return hasExpiredPasswords || hasWeakPasswords || host.status === 'error'\n      }).map(host => {\n        const issues = []\n        if (host.accounts.some(account => this.isPasswordExpired(account))) {\n          issues.push('密码过期')\n        }\n        if (host.accounts.some(account => this.isPasswordWeak(account.password))) {\n          issues.push('弱密码')\n        }\n        if (host.status === 'error') {\n          issues.push('连接异常')\n        }\n        \n        const riskLevel = issues.length >= 2 ? '高' : '中'\n        const riskLevelClass = riskLevel === '高' \n          ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'\n          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'\n        \n        return {\n          ...host,\n          riskLevel,\n          riskLevelClass,\n          issues\n        }\n      }).slice(0, 5) // 只显示前5个\n    }\n  },\n  \n  methods: {\n    isPasswordExpiringSoon(account) {\n      if (!account.passwordExpiryDate) return false\n      \n      const expiryDate = new Date(account.passwordExpiryDate)\n      const now = new Date()\n      const daysUntilExpiry = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24))\n      \n      return daysUntilExpiry <= 7 && daysUntilExpiry > 0\n    },\n    \n    isPasswordExpired(account) {\n      if (!account.passwordExpiryDate) return false\n      \n      const expiryDate = new Date(account.passwordExpiryDate)\n      const now = new Date()\n      \n      return expiryDate < now\n    },\n    \n    isPasswordWeak(password) {\n      if (!password) return true\n      \n      // 简单的弱密码检测\n      if (password.length < 8) return true\n      if (!/[A-Z]/.test(password)) return true\n      if (!/[a-z]/.test(password)) return true\n      if (!/[0-9]/.test(password)) return true\n      \n      // 检测常见弱密码\n      const weakPatterns = [\n        /^password/i,\n        /^123456/,\n        /^admin/i,\n        /^root/i,\n        /^qwerty/i\n      ]\n      \n      return weakPatterns.some(pattern => pattern.test(password))\n    }\n  }\n}\n</script>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAA2D;;EAE/DA,KAAK,EAAC;AAAgG;;EACpGA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAuF;;EAI/FA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAkD;;EAM5DA,KAAK,EAAC;AAAgG;;EACpGA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA2F;;EAInGA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAyD;;EAMnEA,KAAK,EAAC;AAAgG;;EACpGA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAqF;;EAI7FA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAmD;;EAM7DA,KAAK,EAAC;AAAgG;;EACpGA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAyF;;EAIjGA,KAAK,EAAC;AAAM;;EASlBA,KAAK,EAAC;AAA4C;;EAEhDA,KAAK,EAAC;AAAgG;;EAEpGA,KAAK,EAAC;AAAW;;EAEbA,KAAK,EAAC;AAA6B;;EAEhCA,KAAK,EAAC;AAA0C;;EAEnDA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmD;;EAC1DA,KAAK,EAAC;AAAoD;;EASlEA,KAAK,EAAC;AAAgG;;EAEpGA,KAAK,EAAC;AAAW;;EAEbA,KAAK,EAAC;AAAe;;EAKrBA,KAAK,EAAC;AAAgB;;EACtBA,KAAK,EAAC;AAAuC;;EAC7CA,KAAK,EAAC;AAA0C;;EAQxDA,KAAK,EAAC;AAAgG;;EAQpGA,KAAK,EAAC;AAAiB;;EACnBA,KAAK,EAAC;AAAY;;EAUhBA,KAAK,EAAC;AAA+C;;EAEpDA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAA6B;;EAEhCA,KAAK,EAAC;AAAmD;;EAG/DA,KAAK,EAAC;AAAoD;;EAC1DA,KAAK,EAAC;AAAW;;EAKjBA,KAAK,EAAC;AAAoD;;;uBA1I1EC,mBAAA,CAqJM,OArJNC,UAqJM,GApJJC,mBAAA,YAAe,EACfC,mBAAA,CA4DM,OA5DNC,UA4DM,GA3DJF,mBAAA,UAAa,EACbC,mBAAA,CAYM,OAZNE,UAYM,GAXJF,mBAAA,CAUM,OAVNG,UAUM,GATJH,mBAAA,CAIM,OAJNI,UAIM,GAHJJ,mBAAA,CAEM,OAFNK,UAEM,GADJC,YAAA,CAAgGC,4BAAA;IAA5EC,IAAI,EAAE,iBAAiB;IAAEZ,KAAK,EAAC;UAGvDI,mBAAA,CAGM,OAHNS,UAGM,G,0BAFJT,mBAAA,CAAwE;IAArEJ,KAAK,EAAC;EAAsD,GAAC,MAAI,sBACpEI,mBAAA,CAAgF,KAAhFU,UAAgF,EAAAC,gBAAA,CAAjBC,QAAA,CAAAC,UAAU,iB,OAK/Ed,mBAAA,YAAe,EACfC,mBAAA,CAYM,OAZNc,UAYM,GAXJd,mBAAA,CAUM,OAVNe,WAUM,GATJf,mBAAA,CAIM,OAJNgB,WAIM,GAHJhB,mBAAA,CAEM,OAFNiB,WAEM,GADJX,YAAA,CAAkHC,4BAAA;IAA9FC,IAAI,EAAE,+BAA+B;IAAEZ,KAAK,EAAC;UAGrEI,mBAAA,CAGM,OAHNkB,WAGM,G,0BAFJlB,mBAAA,CAAwE;IAArEJ,KAAK,EAAC;EAAsD,GAAC,MAAI,sBACpEI,mBAAA,CAA8F,KAA9FmB,WAA8F,EAAAR,gBAAA,CAAxBC,QAAA,CAAAQ,iBAAiB,iB,OAK7FrB,mBAAA,WAAc,EACdC,mBAAA,CAYM,OAZNqB,WAYM,GAXJrB,mBAAA,CAUM,OAVNsB,WAUM,GATJtB,mBAAA,CAIM,OAJNuB,WAIM,GAHJvB,mBAAA,CAEM,OAFNwB,WAEM,GADJlB,YAAA,CAAkGC,4BAAA;IAA9EC,IAAI,EAAE,qBAAqB;IAAEZ,KAAK,EAAC;UAG3DI,mBAAA,CAGM,OAHNyB,WAGM,G,0BAFJzB,mBAAA,CAAuE;IAApEJ,KAAK,EAAC;EAAsD,GAAC,KAAG,sBACnEI,mBAAA,CAAoF,KAApF0B,WAAoF,EAAAf,gBAAA,CAApBC,QAAA,CAAAe,aAAa,iB,OAKnF5B,mBAAA,UAAa,EACbC,mBAAA,CAYM,OAZN4B,WAYM,GAXJ5B,mBAAA,CAUM,OAVN6B,WAUM,GATJ7B,mBAAA,CAIM,OAJN8B,WAIM,GAHJ9B,mBAAA,CAEM,OAFN+B,WAEM,GADJzB,YAAA,CAAwGC,4BAAA;IAApFC,IAAI,EAAE,uBAAuB;IAAEZ,KAAK,EAAC;UAG7DI,mBAAA,CAGM,OAHNgC,WAGM,G,0BAFJhC,mBAAA,CAAwE;IAArEJ,KAAK,EAAC;EAAsD,GAAC,MAAI,sBACpEI,mBAAA,CAAqF;IAAlFJ,KAAK,EA3DpBqC,eAAA,EA2DqB,oBAAoB,EAASrB,QAAA,CAAAsB,kBAAkB;sBAAKtB,QAAA,CAAAuB,aAAa,IAAG,MAAI,uB,SAMzFpC,mBAAA,iBAAoB,EACpBC,mBAAA,CAqCM,OArCNoC,WAqCM,GApCJrC,mBAAA,YAAe,EACfC,mBAAA,CAgBM,OAhBNqC,WAgBM,G,0BAfJrC,mBAAA,CAAgF;IAA5EJ,KAAK,EAAC;EAA0D,GAAC,QAAM,sBAC3EI,mBAAA,CAaM,OAbNsC,WAaM,I,kBAZJzC,mBAAA,CAWM0C,SAAA,QAlFhBC,WAAA,CAuEgC5B,QAAA,CAAA6B,kBAAkB,EAA5BC,MAAM;yBAAlB7C,mBAAA,CAWM;MAXqC8C,GAAG,EAAED,MAAM,CAACE,KAAK;MAAEhD,KAAK,EAAC;QAClEI,mBAAA,CAGM,OAHN6C,WAGM,GAFJ7C,mBAAA,CAA8D;MAAzDJ,KAAK,EAzExBqC,eAAA,EAyEyB,sBAAsB,EAASS,MAAM,CAACI,KAAK;6BACtD9C,mBAAA,CAAgF,QAAhF+C,WAAgF,EAAApC,gBAAA,CAAtB+B,MAAM,CAACE,KAAK,iB,GAExE5C,mBAAA,CAKM,OALNgD,WAKM,GAJJhD,mBAAA,CAAyF,QAAzFiD,WAAyF,EAAAtC,gBAAA,CAAtB+B,MAAM,CAACQ,KAAK,kBAC/ElD,mBAAA,CAEM,OAFNmD,WAEM,GADJnD,mBAAA,CAAsG;MAAjGJ,KAAK,EA/E1BqC,eAAA,EA+E2B,kBAAkB,EAASS,MAAM,CAACI,KAAK;MAAGM,KAAK,EA/E1EC,eAAA;QAAAC,KAAA,EA+EqFZ,MAAM,CAACa,UAAU;MAAA;;sCAOhGxD,mBAAA,UAAa,EACbC,mBAAA,CAeM,OAfNwD,WAeM,G,0BAdJxD,mBAAA,CAA8E;IAA1EJ,KAAK,EAAC;EAA0D,GAAC,MAAI,sBACzEI,mBAAA,CAYM,OAZNyD,WAYM,I,kBAXJ5D,mBAAA,CAUM0C,SAAA,QApGhBC,WAAA,CA0FkC5B,QAAA,CAAA8C,gBAAgB,EAA5BC,QAAQ;yBAApB9D,mBAAA,CAUM;MAVqC8C,GAAG,EAAEgB,QAAQ,CAACC,EAAE;MAAEhE,KAAK,EAAC;QACjEI,mBAAA,CAIM,OAJN6D,WAIM,GAHJ7D,mBAAA,CAEM;MAFDJ,KAAK,EA5FxBqC,eAAA,EA4FyB,uDAAuD,EAAS0B,QAAQ,CAACG,MAAM;QACxFxD,YAAA,CAAgGC,4BAAA;MAA5EC,IAAI,UAAUmD,QAAQ,CAACnD,IAAI;MAAIZ,KAAK,EA7FxEqC,eAAA,EA6F0E0B,QAAQ,CAACI,SAAS,EAAQ,SAAS;mEAGjG/D,mBAAA,CAGM,OAHNgE,WAGM,GAFJhE,mBAAA,CAA2E,KAA3EiE,WAA2E,EAAAtD,gBAAA,CAAvBgD,QAAQ,CAACO,OAAO,kBACpElE,mBAAA,CAA2E,KAA3EmE,WAA2E,EAAAxD,gBAAA,CAApBgD,QAAQ,CAACS,IAAI,iB;wCAO9ErE,mBAAA,YAAe,EACfC,mBAAA,CA2CM,OA3CNqE,WA2CM,G,0BA1CJrE,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAAwC,IACjDI,mBAAA,CAA0E;IAAtEJ,KAAK,EAAC;EAAqD,GAAC,OAAK,GACrEI,mBAAA,CAES;IAFDJ,KAAK,EAAC;EAAuF,GAAC,QAEtG,E,sBAGFI,mBAAA,CAkCM,OAlCNsE,WAkCM,GAjCJtE,mBAAA,CAgCQ,SAhCRuE,WAgCQ,G,0BA/BNvE,mBAAA,CAQQ,gBAPNA,mBAAA,CAMK;IANDJ,KAAK,EAAC;EAA+C,IACvDI,mBAAA,CAA6F;IAAzFJ,KAAK,EAAC;EAA0E,GAAC,KAAG,GACxFI,mBAAA,CAA8F;IAA1FJ,KAAK,EAAC;EAA0E,GAAC,MAAI,GACzFI,mBAAA,CAA8F;IAA1FJ,KAAK,EAAC;EAA0E,GAAC,MAAI,GACzFI,mBAAA,CAA8F;IAA1FJ,KAAK,EAAC;EAA0E,GAAC,MAAI,GACzFI,mBAAA,CAA4F;IAAxFJ,KAAK,EAAC;EAA0E,GAAC,IAAE,E,wBAG3FI,mBAAA,CAqBQ,SArBRwE,WAqBQ,I,kBApBN3E,mBAAA,CAmBK0C,SAAA,QAjJjBC,WAAA,CA8H+B5B,QAAA,CAAA6D,SAAS,EAAjBC,IAAI;yBAAf7E,mBAAA,CAmBK;MAnB0B8C,GAAG,EAAE+B,IAAI,CAACd,EAAE;MAAEhE,KAAK,EAAC;QACjDI,mBAAA,CAKK,MALL2E,WAKK,GAJH3E,mBAAA,CAGM,OAHN4E,WAGM,GAFJtE,YAAA,CAAqEC,4BAAA;MAAjDC,IAAI,EAAE,iBAAiB;MAAEZ,KAAK,EAAC;QACnDI,mBAAA,CAAsF,QAAtF6E,WAAsF,EAAAlE,gBAAA,CAAnB+D,IAAI,CAACI,IAAI,iB,KAGhF9E,mBAAA,CAAiF,MAAjF+E,WAAiF,EAAApE,gBAAA,CAAf+D,IAAI,CAACM,EAAE,kBACzEhF,mBAAA,CAIK,MAJLiF,WAIK,GAHHjF,mBAAA,CAEO;MAFDJ,KAAK,EAvI3BqC,eAAA,EAuI4B,yEAAyE,EAASyC,IAAI,CAACQ,cAAc;wBAC5GR,IAAI,CAACS,SAAS,wB,GAGrBnF,mBAAA,CAAgG,MAAhGoF,WAAgG,EAAAzE,gBAAA,CAA9B+D,IAAI,CAACW,MAAM,CAACC,IAAI,wB,0BAClFtF,mBAAA,CAIK;MAJDJ,KAAK,EAAC;IAAW,IACnBI,mBAAA,CAES;MAFDJ,KAAK,EAAC;IAAuF,GAAC,QAEtG,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}