{"ast": null, "code": "import { mapState, mapGetters } from 'vuex';\nimport BaseModal from '@/components/BaseModal.vue';\nimport StatusBadge from '@/components/StatusBadge.vue';\nimport CustomCheckbox from '@/components/CustomCheckbox.vue';\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue';\nimport SecurityDashboard from '@/components/SecurityDashboard.vue';\nimport AdvancedPasswordGenerator from '@/components/AdvancedPasswordGenerator.vue';\nexport default {\n  name: 'HostManagement',\n  components: {\n    BaseModal,\n    StatusBadge,\n    CustomCheckbox,\n    PasswordStrengthMeter,\n    SecurityDashboard,\n    AdvancedPasswordGenerator\n  },\n  data() {\n    return {\n      selectAll: false,\n      selectAllBatch: false,\n      selectAllBatchAdd: false,\n      processing: false,\n      currentHost: {},\n      currentAccount: {},\n      passwordVisibility: {\n        generated: false,\n        new: false,\n        confirm: false,\n        newAccount: false,\n        batchPassword: false\n      },\n      viewMode: 'table',\n      filterText: '',\n      accountFilterText: '',\n      statusFilter: 'all',\n      expiryFilter: 'all',\n      policyFilter: 'all',\n      // 修改密码弹窗\n      changePasswordModal: {\n        show: false,\n        method: 'auto',\n        policyId: 1,\n        generatedPassword: 'aX7#9pQr$2Lm',\n        newPassword: '',\n        confirmPassword: '',\n        executeImmediately: true,\n        saveHistory: false,\n        logAudit: true\n      },\n      // 批量更新密码弹窗\n      batchUpdateModal: {\n        show: false,\n        selectedHosts: {},\n        policyId: 1,\n        executionTime: 'immediate',\n        scheduledDate: '',\n        scheduledTime: '',\n        ignoreErrors: true,\n        detailedLog: true,\n        sendNotification: false\n      },\n      // 批量应用策略弹窗\n      batchApplyModal: {\n        show: false,\n        selectedHosts: {},\n        policyId: 1,\n        updateImmediately: false,\n        applyOnNextUpdate: true\n      },\n      // 紧急重置密码弹窗\n      emergencyResetModal: {\n        show: false,\n        selectedHosts: {},\n        policyId: 3,\n        // 默认使用紧急策略\n        reason: 'security_incident',\n        description: ''\n      },\n      // 添加账号弹窗\n      addAccountModal: {\n        show: false,\n        username: '',\n        password: '',\n        isDefault: false,\n        policyId: 1\n      },\n      // 批量添加账号弹窗\n      batchAddAccountModal: {\n        show: false,\n        selectedHosts: {},\n        username: '',\n        password: '',\n        role: 'admin',\n        setAsDefault: false,\n        useSamePassword: true,\n        policyId: 1\n      }\n    };\n  },\n  computed: {\n    ...mapState({\n      hosts: state => state.hosts,\n      policies: state => state.policies\n    }),\n    ...mapGetters(['selectedHosts']),\n    // 获取策略名称\n    getPolicyName() {\n      return policyId => {\n        if (!policyId) return '无';\n        const policy = this.policies.find(p => p.id === policyId);\n        return policy ? policy.name : '无';\n      };\n    },\n    // 获取策略颜色类\n    getPolicyColorClass() {\n      return policyId => {\n        if (!policyId) return 'bg-gray-100 text-gray-800';\n\n        // 根据策略ID返回对应的颜色类\n        const colorMap = {\n          1: 'bg-red-100 text-red-800',\n          // 高强度策略\n          2: 'bg-blue-100 text-blue-800',\n          // 标准策略\n          3: 'bg-purple-100 text-purple-800' // 紧急策略\n        };\n        return colorMap[policyId] || 'bg-gray-100 text-gray-800';\n      };\n    },\n    passwordMismatch() {\n      return this.changePasswordModal.newPassword && this.changePasswordModal.confirmPassword && this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword;\n    },\n    selectedHostsCount() {\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length;\n    },\n    selectedHostsList() {\n      return this.hosts.filter(host => host.selected);\n    },\n    emergencyPolicies() {\n      // 返回紧急策略和高强度策略\n      return this.policies.filter(p => p.id === 3 || p.id === 1);\n    },\n    // 过滤后的主机列表\n    filteredHosts() {\n      return this.hosts.filter(host => {\n        // 文本过滤\n        const textMatch = this.filterText === '' || host.name.toLowerCase().includes(this.filterText.toLowerCase()) || host.ip.includes(this.filterText);\n\n        // 状态过滤\n        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;\n        return textMatch && statusMatch;\n      });\n    },\n    // 获取所有账号（扁平化处理）\n    getAllAccounts() {\n      // 为每个账号添加主机引用\n      const accounts = [];\n      this.filteredHosts.forEach(host => {\n        host.accounts.forEach(account => {\n          accounts.push({\n            ...account,\n            host: host\n          });\n        });\n      });\n      return accounts;\n    },\n    // 筛选后的账号\n    filteredAccounts() {\n      return this.getAllAccounts.filter(account => {\n        // 账号名称筛选\n        const accountMatch = this.accountFilterText === '' || account.username.toLowerCase().includes(this.accountFilterText.toLowerCase());\n\n        // 密码过期筛选\n        let expiryMatch = true;\n        if (this.expiryFilter !== 'all') {\n          const expiryStatus = this.isPasswordExpired(account).status;\n          if (this.expiryFilter === 'expired') {\n            expiryMatch = expiryStatus === 'expired';\n          } else if (this.expiryFilter === 'expiring-soon') {\n            expiryMatch = expiryStatus === 'danger' || expiryStatus === 'warning';\n          } else if (this.expiryFilter === 'valid') {\n            expiryMatch = expiryStatus === 'normal';\n          }\n        }\n\n        // 策略筛选\n        let policyMatch = true;\n        if (this.policyFilter !== 'all') {\n          if (this.policyFilter === 'none') {\n            policyMatch = !account.policyId;\n          } else {\n            policyMatch = account.policyId === parseInt(this.policyFilter);\n          }\n        }\n        return accountMatch && expiryMatch && policyMatch;\n      });\n    },\n    // 分组后的账号列表\n    groupedAccounts() {\n      // 按主机ID分组\n      const groups = {};\n      this.filteredAccounts.forEach(account => {\n        const hostId = account.host.id;\n        if (!groups[hostId]) {\n          groups[hostId] = {\n            hostId: hostId,\n            hostName: account.host.name,\n            hostIp: account.host.ip,\n            host: account.host,\n            accounts: []\n          };\n        }\n        groups[hostId].accounts.push(account);\n      });\n\n      // 转换为数组\n      return Object.values(groups);\n    },\n    selectedBatchAddHostsCount() {\n      return Object.values(this.batchAddAccountModal.selectedHosts).filter(Boolean).length;\n    }\n  },\n  methods: {\n    toggleSelectAll(value) {\n      this.$store.commit('selectAllHosts', value);\n    },\n    toggleSelectAllBatch(value) {\n      this.hosts.forEach(host => {\n        this.batchUpdateModal.selectedHosts[host.id] = value;\n      });\n    },\n    toggleSelectAllBatchAdd(value) {\n      this.hosts.forEach(host => {\n        this.batchAddAccountModal.selectedHosts[host.id] = value;\n      });\n    },\n    openChangePasswordModal(host, account) {\n      this.currentHost = host;\n      this.currentAccount = account;\n      this.changePasswordModal.show = true;\n      this.changePasswordModal.generatedPassword = this.generatePassword();\n    },\n    openBatchUpdateModal() {\n      this.batchUpdateModal.show = true;\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected;\n      });\n\n      // 设置默认值\n      const today = new Date();\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0];\n      this.batchUpdateModal.scheduledTime = '03:00';\n    },\n    openBatchApplyModal() {\n      this.batchApplyModal.show = true;\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.batchApplyModal.selectedHosts[host.id] = host.selected;\n      });\n    },\n    showEmergencyReset() {\n      this.emergencyResetModal.show = true;\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected;\n      });\n    },\n    generatePassword(policy) {\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';\n      let password = '';\n\n      // 获取所选策略的最小长度\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId);\n      const minLength = policyObj ? policyObj.minLength : 12;\n\n      // 生成随机密码\n      for (let i = 0; i < minLength; i++) {\n        password += chars.charAt(Math.floor(Math.random() * chars.length));\n      }\n      if (this.changePasswordModal && !policy) {\n        this.changePasswordModal.generatedPassword = password;\n      }\n      return password;\n    },\n    async updatePassword() {\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\n        return;\n      }\n      this.processing = true;\n      try {\n        const password = this.changePasswordModal.method === 'auto' ? this.changePasswordModal.generatedPassword : this.changePasswordModal.newPassword;\n        await this.$store.dispatch('updateHostPassword', {\n          hostId: this.currentHost.id,\n          accountId: this.currentAccount.id,\n          password: password,\n          policyId: this.changePasswordModal.policyId\n        });\n        this.changePasswordModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功更新主机 ${this.currentHost.name} 的 ${this.currentAccount.username} 账号密码！`);\n      } catch (error) {\n        console.error('更新密码失败', error);\n        alert('更新密码失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    async batchUpdatePasswords() {\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts).filter(([_, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        alert('请至少选择一台主机！');\n        return;\n      }\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\n        // 在实际应用中，这里会创建一个定时任务\n        alert('已创建定时密码更新任务！');\n        this.batchUpdateModal.show = false;\n        return;\n      }\n      this.processing = true;\n      try {\n        // 获取所选策略\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId);\n\n        // 为每台主机的每个账号更新密码\n        for (const hostId of selectedHostIds) {\n          const host = this.hosts.find(h => h.id === hostId);\n          if (host) {\n            for (const account of host.accounts) {\n              const newPassword = this.generatePassword(policy);\n              await this.$store.dispatch('updateHostPassword', {\n                hostId: hostId,\n                accountId: account.id,\n                password: newPassword,\n                policyId: policy.id\n              });\n            }\n          }\n        }\n        this.batchUpdateModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号更新密码！`);\n      } catch (error) {\n        console.error('批量更新密码失败', error);\n        alert('批量更新密码失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    async batchApplyPolicy() {\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts).filter(([_, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        alert('请至少选择一台主机！');\n        return;\n      }\n      this.processing = true;\n      try {\n        await this.$store.dispatch('applyPolicyToHosts', {\n          policyId: this.batchApplyModal.policyId,\n          hostIds: selectedHostIds\n        });\n        this.batchApplyModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`);\n      } catch (error) {\n        console.error('应用策略失败', error);\n        alert('应用策略失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    async emergencyReset() {\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts).filter(([_, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        alert('请至少选择一台主机！');\n        return;\n      }\n      this.processing = true;\n      try {\n        // 获取紧急策略\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId);\n\n        // 为每台主机的每个账号更新密码\n        for (const hostId of selectedHostIds) {\n          const host = this.hosts.find(h => h.id === hostId);\n          if (host) {\n            for (const account of host.accounts) {\n              const newPassword = this.generatePassword(policy);\n              await this.$store.dispatch('updateHostPassword', {\n                hostId: hostId,\n                accountId: account.id,\n                password: newPassword,\n                policyId: policy.id\n              });\n            }\n          }\n        }\n        this.emergencyResetModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号执行紧急密码重置！`);\n      } catch (error) {\n        console.error('紧急重置失败', error);\n        alert('紧急重置失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    togglePasswordVisibility(hostId) {\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId];\n    },\n    isPasswordExpired(account) {\n      if (!account.passwordExpiryDate) return {\n        status: 'normal',\n        days: null,\n        text: '-'\n      };\n\n      // 解析过期时间\n      const expiryDate = new Date(account.passwordExpiryDate);\n      const now = new Date();\n\n      // 如果已过期\n      if (expiryDate < now) {\n        return {\n          status: 'expired',\n          days: 0,\n          text: '已过期'\n        };\n      }\n\n      // 计算剩余天数和小时数\n      const diffTime = expiryDate - now;\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));\n      const diffHours = Math.floor(diffTime % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n\n      // 根据剩余时间确定状态\n      let status = 'normal';\n      if (diffDays < 7) {\n        status = 'danger'; // 少于7天\n      } else if (diffDays < 14) {\n        status = 'warning'; // 少于14天\n      }\n\n      // 格式化显示文本\n      let text = '';\n      if (diffDays > 0) {\n        text += `${diffDays}天`;\n      }\n      if (diffHours > 0 || diffDays === 0) {\n        text += `${diffHours}小时`;\n      }\n      return {\n        status,\n        days: diffDays,\n        text: `剩余${text}`\n      };\n    },\n    openAddAccountModal(host) {\n      this.currentHost = host;\n      this.addAccountModal.show = true;\n    },\n    async addAccount() {\n      if (!this.addAccountModal.username || !this.addAccountModal.password) {\n        alert('请填写完整的账号信息！');\n        return;\n      }\n      this.processing = true;\n      try {\n        await this.$store.dispatch('addHostAccount', {\n          hostId: this.currentHost.id,\n          username: this.addAccountModal.username,\n          password: this.addAccountModal.password,\n          policyId: this.addAccountModal.policyId,\n          isDefault: this.addAccountModal.isDefault\n        });\n        this.addAccountModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为主机 ${this.currentHost.name} 添加账号！`);\n      } catch (error) {\n        console.error('添加账号失败', error);\n        alert('添加账号失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    generatePasswordForNewAccount() {\n      this.addAccountModal.password = this.generatePassword();\n    },\n    // 复制密码到剪贴板\n    copyPassword(account) {\n      // 创建一个临时输入框\n      const tempInput = document.createElement('input');\n      tempInput.value = account.password;\n      document.body.appendChild(tempInput);\n      tempInput.select();\n      document.execCommand('copy');\n      document.body.removeChild(tempInput);\n\n      // 显示提示\n      alert(`已复制 ${account.host.name} 的 ${account.username} 账号密码到剪贴板！`);\n    },\n    // 导出密码数据为CSV\n    exportPasswordsToCSV() {\n      // 准备CSV标题行\n      const headers = ['主机名', 'IP地址', '账号', '密码', '最后修改时间', '过期时间', '状态', '策略'];\n      const csvRows = [headers];\n\n      // 添加数据行\n      this.filteredAccounts.forEach(account => {\n        const row = [account.host.name, account.host.ip, account.username, account.password, account.lastPasswordChange || '-', account.passwordExpiryDate || '-', account.host.status, this.getPolicyName(account.policyId)];\n        csvRows.push(row);\n      });\n\n      // 转换为CSV格式\n      const csvContent = csvRows.map(row => row.map(cell => `\"${String(cell).replace(/\"/g, '\"\"')}\"`).join(',')).join('\\n');\n\n      // 创建下载链接\n      const blob = new Blob([csvContent], {\n        type: 'text/csv;charset=utf-8;'\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.setAttribute('href', url);\n      link.setAttribute('download', `密码数据_${new Date().toISOString().slice(0, 10)}.csv`);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    },\n    generatePasswordForBatchAccount() {\n      this.batchAddAccountModal.password = this.generatePassword();\n    },\n    async batchAddAccounts() {\n      if (!this.batchAddAccountModal.username) {\n        alert('请填写账号名称！');\n        return;\n      }\n      const selectedHostIds = Object.entries(this.batchAddAccountModal.selectedHosts).filter(([_, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        alert('请至少选择一台主机！');\n        return;\n      }\n      this.processing = true;\n      try {\n        await this.$store.dispatch('batchAddAccounts', {\n          hostIds: selectedHostIds,\n          username: this.batchAddAccountModal.username,\n          password: this.batchAddAccountModal.useSamePassword ? this.batchAddAccountModal.password : null,\n          role: this.batchAddAccountModal.role,\n          isDefault: this.batchAddAccountModal.setAsDefault,\n          policyId: this.batchAddAccountModal.policyId\n        });\n        this.batchAddAccountModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为 ${selectedHostIds.length} 台主机添加账号！`);\n      } catch (error) {\n        console.error('批量添加账号失败', error);\n        alert('批量添加账号失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    openBatchAddAccountModal() {\n      this.batchAddAccountModal.show = true;\n      this.batchAddAccountModal.selectedHosts = {};\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.batchAddAccountModal.selectedHosts[host.id] = host.selected;\n      });\n\n      // 生成初始密码\n      this.generatePasswordForBatchAccount();\n    },\n    printPasswordData() {\n      // 使用纯JavaScript方式创建打印内容，避免Vue模板编译问题\n      const printWindow = window.open(\"\", \"_blank\");\n      if (!printWindow) {\n        alert(\"无法打开打印窗口，请允许弹出窗口\");\n        return;\n      }\n      const doc = printWindow.document;\n\n      // 清空文档\n      doc.open();\n      doc.write(\"<!DOCTYPE html>\");\n      doc.write(\"<html>\");\n      doc.write(\"<head>\");\n      doc.write(\"<title>主机账号密码报表</title>\");\n\n      // 添加样式\n      doc.write(\"<style>\");\n      doc.write(\"body { font-family: Arial, sans-serif; }\");\n      doc.write(\"table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\");\n      doc.write(\"th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\");\n      doc.write(\"th { background-color: #f2f2f2; }\");\n      doc.write(\".print-header { text-align: center; margin-bottom: 20px; }\");\n      doc.write(\".print-footer { text-align: center; font-size: 12px; margin-top: 30px; }\");\n      doc.write(\".status-normal { color: green; }\");\n      doc.write(\".status-warning { color: orange; }\");\n      doc.write(\".status-error { color: red; }\");\n      doc.write(\"@page { margin: 1cm; }\");\n      doc.write(\"</style>\");\n      doc.write(\"</head>\");\n      doc.write(\"<body>\");\n\n      // 添加标题\n      doc.write(\"<div class=\\\"print-header\\\">\");\n      doc.write(\"<h1>主机账号密码报表</h1>\");\n      doc.write(\"<p>生成时间: \" + new Date().toLocaleString(\"zh-CN\") + \"</p>\");\n      doc.write(\"</div>\");\n\n      // 添加表格\n      doc.write(\"<table>\");\n\n      // 表头\n      doc.write(\"<thead><tr>\");\n      [\"主机名\", \"IP地址\", \"账号\", \"最后密码修改时间\", \"密码过期时间\", \"状态\", \"策略\"].forEach(header => {\n        doc.write(\"<th>\" + header + \"</th>\");\n      });\n      doc.write(\"</tr></thead>\");\n\n      // 表格内容\n      doc.write(\"<tbody>\");\n      this.filteredAccounts.forEach(account => {\n        const statusClass = \"status-\" + account.host.status;\n        doc.write(\"<tr>\");\n        doc.write(\"<td>\" + account.host.name + \"</td>\");\n        doc.write(\"<td>\" + account.host.ip + \"</td>\");\n        doc.write(\"<td>\" + account.username + (account.isDefault ? \" (默认)\" : \"\") + \"</td>\");\n        doc.write(\"<td>\" + (account.lastPasswordChange || \"-\") + \"</td>\");\n        doc.write(\"<td>\" + (account.passwordExpiryDate || \"-\") + \"</td>\");\n        doc.write(\"<td class=\\\"\" + statusClass + \"\\\">\" + account.host.status + \"</td>\");\n        doc.write(\"<td>\" + this.getPolicyName(account.policyId) + \"</td>\");\n        doc.write(\"</tr>\");\n      });\n      doc.write(\"</tbody>\");\n      doc.write(\"</table>\");\n\n      // 添加页脚\n      doc.write(\"<div class=\\\"print-footer\\\">\");\n      doc.write(\"<p>注意：本文档包含敏感信息，请妥善保管</p>\");\n      doc.write(\"</div>\");\n\n      // 添加自动打印脚本\n      doc.write(\"<\" + \"script\" + \">\");\n      doc.write(\"window.onload = function() { window.print(); }\");\n      doc.write(\"</\" + \"script\" + \">\");\n      doc.write(\"</body>\");\n      doc.write(\"</html>\");\n      doc.close();\n    },\n    // 检查密码是否符合策略要求\n    isPasswordCompliant(account) {\n      if (!account.policyId) return {\n        compliant: true,\n        text: '无策略'\n      };\n      const password = account.password;\n      const policy = this.policies.find(p => p.id === account.policyId);\n      if (!policy) return {\n        compliant: true,\n        text: '无策略'\n      };\n      const checks = [];\n\n      // 检查长度\n      if (password.length < policy.minLength) {\n        checks.push(`密码长度不足 ${policy.minLength} 位`);\n      }\n\n      // 检查大写字母\n      if (policy.requireUppercase && !/[A-Z]/.test(password)) {\n        checks.push('缺少大写字母');\n      }\n\n      // 检查小写字母\n      if (policy.requireLowercase && !/[a-z]/.test(password)) {\n        checks.push('缺少小写字母');\n      }\n\n      // 检查数字\n      if (policy.requireNumbers && !/[0-9]/.test(password)) {\n        checks.push('缺少数字');\n      }\n\n      // 检查特殊字符\n      if (policy.requireSpecial && !/[^A-Za-z0-9]/.test(password)) {\n        checks.push('缺少特殊字符');\n      }\n\n      // 检查是否包含用户名\n      if (policy.forbidUsername && password.toLowerCase().includes(account.username.toLowerCase())) {\n        checks.push('密码包含用户名');\n      }\n      return {\n        compliant: checks.length === 0,\n        text: checks.length === 0 ? '符合策略' : `不符合: ${checks.join(', ')}`,\n        details: checks\n      };\n    }\n  },\n  created() {\n    // 初始化日期和时间\n    const today = new Date();\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0];\n    this.batchUpdateModal.scheduledTime = '03:00';\n  }\n};", "map": {"version": 3, "names": ["mapState", "mapGetters", "BaseModal", "StatusBadge", "CustomCheckbox", "PasswordStrengthMeter", "SecurityDashboard", "AdvancedPasswordGenerator", "name", "components", "data", "selectAll", "selectAllBatch", "selectAllBatchAdd", "processing", "currentHost", "currentAccount", "passwordVisibility", "generated", "new", "confirm", "newAccount", "batchPassword", "viewMode", "filterText", "accountFilterText", "statusFilter", "expiryFilter", "policyFilter", "changePasswordModal", "show", "method", "policyId", "generatedPassword", "newPassword", "confirmPassword", "executeImmediately", "saveHistory", "logAudit", "batchUpdateModal", "selectedHosts", "executionTime", "scheduledDate", "scheduledTime", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "reason", "description", "addAccountModal", "username", "password", "isDefault", "batchAddAccountModal", "role", "setAsDefault", "useSamePassword", "computed", "hosts", "state", "policies", "getPolicyName", "policy", "find", "p", "id", "getPolicyColorClass", "colorMap", "passwordMismatch", "selectedHostsCount", "Object", "values", "filter", "Boolean", "length", "selectedHostsList", "host", "selected", "emergencyPolicies", "filteredHosts", "textMatch", "toLowerCase", "includes", "ip", "statusMatch", "status", "getAllAccounts", "accounts", "for<PERSON>ach", "account", "push", "filteredAccounts", "accountMatch", "expiryMatch", "expiry<PERSON>tatus", "isPasswordExpired", "policyMatch", "parseInt", "groupedAccounts", "groups", "hostId", "hostName", "hostIp", "selectedBatchAddHostsCount", "methods", "toggleSelectAll", "value", "$store", "commit", "toggleSelectAllBatch", "toggleSelectAllBatchAdd", "openChangePasswordModal", "generatePassword", "openBatchUpdateModal", "today", "Date", "toISOString", "split", "openBatchApplyModal", "showEmergencyReset", "chars", "policyObj", "<PERSON><PERSON><PERSON><PERSON>", "i", "char<PERSON>t", "Math", "floor", "random", "updatePassword", "dispatch", "accountId", "alert", "error", "console", "batchUpdatePasswords", "selectedHostIds", "entries", "_", "map", "h", "batchApplyPolicy", "hostIds", "emergencyReset", "togglePasswordVisibility", "passwordExpiryDate", "days", "text", "expiryDate", "now", "diffTime", "diffDays", "diffHours", "openAddAccountModal", "addAccount", "generatePasswordForNewAccount", "copyPassword", "tempInput", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "exportPasswordsToCSV", "headers", "csvRows", "row", "lastPasswordChange", "csv<PERSON><PERSON>nt", "cell", "String", "replace", "join", "blob", "Blob", "type", "url", "URL", "createObjectURL", "link", "setAttribute", "slice", "style", "visibility", "click", "generatePasswordForBatchAccount", "batchAddAccounts", "openBatchAddAccountModal", "printPasswordData", "printWindow", "window", "open", "doc", "write", "toLocaleString", "header", "statusClass", "close", "isPasswordCompliant", "compliant", "checks", "requireUppercase", "test", "requireLowercase", "requireNumbers", "requireSpecial", "forbidUsername", "details", "created"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div class=\"space-y-6\">\r\n    <!-- 安全仪表板 -->\r\n    <SecurityDashboard :hosts=\"hosts\" />\r\n\r\n    <!-- 操作工具栏 -->\r\n    <div class=\"bg-white dark:bg-gray-800 shadow-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6\">\r\n      <div class=\"flex flex-wrap items-center justify-between gap-4\">\r\n        <!-- 主要操作按钮 -->\r\n        <div class=\"flex flex-wrap gap-3\">\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200\"\r\n            @click=\"showEmergencyReset\">\r\n            <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2\" />\r\n            <span>紧急重置</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200\"\r\n            @click=\"openBatchUpdateModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n            <span>批量更新密码</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200\"\r\n            @click=\"openBatchApplyModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n            <span>批量应用策略</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200\"\r\n            @click=\"openBatchAddAccountModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'users']\" class=\"mr-2\" />\r\n            <span>批量添加账号</span>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- 筛选和视图控制 -->\r\n        <div class=\"flex flex-wrap items-center gap-3\">\r\n          <!-- 视图切换 -->\r\n          <div class=\"flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1\">\r\n            <button\r\n              class=\"px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200 focus:outline-none\"\r\n              :class=\"viewMode === 'table'\r\n                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\r\n                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'\"\r\n              @click=\"viewMode = 'table'\">\r\n              <font-awesome-icon :icon=\"['fas', 'table']\" class=\"mr-1.5\" />\r\n              表格\r\n            </button>\r\n            <button\r\n              class=\"px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200 focus:outline-none\"\r\n              :class=\"viewMode === 'card'\r\n                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\r\n                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'\"\r\n              @click=\"viewMode = 'card'\">\r\n              <font-awesome-icon :icon=\"['fas', 'th-large']\" class=\"mr-1.5\" />\r\n              卡片\r\n            </button>\r\n          </div>\r\n\r\n          <!-- 搜索框 -->\r\n          <div class=\"relative\">\r\n            <input\r\n              type=\"text\"\r\n              v-model=\"filterText\"\r\n              placeholder=\"搜索主机...\"\r\n              class=\"w-48 pl-10 pr-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white dark:placeholder-gray-400\"\r\n            />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 账号筛选 -->\r\n          <div class=\"relative\">\r\n            <input\r\n              type=\"text\"\r\n              v-model=\"accountFilterText\"\r\n              placeholder=\"搜索账号...\"\r\n              class=\"w-48 pl-10 pr-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white dark:placeholder-gray-400\"\r\n            />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'user']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 筛选下拉菜单 -->\r\n          <div class=\"flex gap-2\">\r\n            <select\r\n              v-model=\"statusFilter\"\r\n              class=\"px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white\"\r\n            >\r\n              <option value=\"all\">所有状态</option>\r\n              <option value=\"normal\">正常</option>\r\n              <option value=\"warning\">警告</option>\r\n              <option value=\"error\">错误</option>\r\n            </select>\r\n\r\n            <select\r\n              v-model=\"expiryFilter\"\r\n              class=\"px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white\"\r\n            >\r\n              <option value=\"all\">所有密码</option>\r\n              <option value=\"expired\">已过期</option>\r\n              <option value=\"expiring-soon\">即将过期</option>\r\n              <option value=\"valid\">有效期内</option>\r\n            </select>\r\n\r\n            <select\r\n              v-model=\"policyFilter\"\r\n              class=\"px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white\"\r\n            >\r\n              <option value=\"all\">所有策略</option>\r\n              <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n                {{ policy.name }}\r\n              </option>\r\n              <option value=\"none\">无策略</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <!-- 表格视图 -->\r\n    <div v-if=\"viewMode === 'table'\" class=\"bg-white rounded-lg shadow overflow-hidden\">\r\n      <!-- 账号计数和导出按钮 -->\r\n      <div class=\"px-4 py-3 bg-gray-50 border-b flex justify-between items-center\">\r\n        <div class=\"text-sm text-gray-700\">\r\n          显示 <span class=\"font-medium\">{{ filteredAccounts.length }}</span> 个账号\r\n          (共 <span class=\"font-medium\">{{ getAllAccounts.length }}</span> 个)\r\n        </div>\r\n        <div class=\"flex space-x-2\">\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            @click=\"exportPasswordsToCSV\">\r\n            <font-awesome-icon :icon=\"['fas', 'file-export']\" class=\"mr-1\" />\r\n            导出\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            @click=\"printPasswordData\">\r\n            <font-awesome-icon :icon=\"['fas', 'print']\" class=\"mr-1\" />\r\n            打印\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <table class=\"min-w-full divide-y divide-gray-200\">\r\n        <thead class=\"bg-gray-50\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n                主机名\r\n              </CustomCheckbox>\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              IP地址\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              账号\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              最后密码修改时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码过期时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              状态\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              策略\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              操作\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"bg-white divide-y divide-gray-200\">\r\n          <!-- 按主机分组显示 -->\r\n          <template v-for=\"hostGroup in groupedAccounts\" :key=\"hostGroup.hostId\">\r\n            <!-- 账号行 -->\r\n            <tr v-for=\"(account, accountIndex) in hostGroup.accounts\" :key=\"account.id\"\r\n              :class=\"{ 'bg-gray-50': accountIndex % 2 === 0, 'hover:bg-blue-50': true }\">\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <CustomCheckbox v-model=\"account.host.selected\" class=\"ml-4\">\r\n                    <span class=\"ml-2 font-medium text-gray-900\">{{ account.host.name }}</span>\r\n                  </CustomCheckbox>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-900\">{{ account.host.ip }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-500\">{{ account.lastPasswordChange || '-' }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div :class=\"{\r\n                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\r\n                  'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                  'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                  'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                }\">\r\n                  {{ isPasswordExpired(account).text }}\r\n                  <span\r\n                    v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                    class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                  </span>\r\n                  <span v-else-if=\"isPasswordExpired(account).status === 'warning'\" class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" />\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <div class=\"flex-grow\">\r\n                    <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\"\r\n                      readonly\r\n                      class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  </div>\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                  <div class=\"ml-2\" v-tooltip=\"isPasswordCompliant(account).text\">\r\n                    <font-awesome-icon \r\n                      :icon=\"['fas', isPasswordCompliant(account).compliant ? 'check-circle' : 'exclamation-circle']\" \r\n                      :class=\"isPasswordCompliant(account).compliant ? 'text-green-500' : 'text-red-500'\" \r\n                    />\r\n                  </div>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <StatusBadge :type=\"account.host.status\" />\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div :class=\"['inline-flex items-center px-2 py-0.5 rounded text-xs font-medium', getPolicyColorClass(account.policyId)]\">\r\n                  {{ getPolicyName(account.policyId) }}\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                <div class=\"flex space-x-2\">\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"openChangePasswordModal(account.host, account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                    修改密码\r\n                  </button>\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"copyPassword(account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'copy']\" class=\"mr-1\" />\r\n                    复制\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          </template>\r\n          <!-- 无数据显示 -->\r\n          <tr v-if=\"filteredAccounts.length === 0\">\r\n            <td colspan=\"9\" class=\"px-6 py-10 text-center\">\r\n              <div class=\"text-gray-500\">\r\n                <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-4xl mb-3\" />\r\n                <p>没有找到匹配的账号数据</p>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <!-- 卡片视图 -->\r\n    <div v-else class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\">\r\n      <div v-for=\"host in filteredHosts\" :key=\"host.id\" class=\"bg-white overflow-hidden shadow rounded-lg\">\r\n        <div class=\"px-4 py-5 sm:p-6\">\r\n          <!-- 主机头部 -->\r\n          <div class=\"flex flex-wrap justify-between items-start mb-4\">\r\n            <div class=\"flex items-center mb-2 sm:mb-0\">\r\n              <CustomCheckbox v-model=\"host.selected\" class=\"mr-2\" />\r\n              <div>\r\n                <h3 class=\"text-lg font-medium text-gray-900\">{{ host.name }}</h3>\r\n                <p class=\"text-sm text-gray-500\">{{ host.ip }}</p>\r\n              </div>\r\n            </div>\r\n            <StatusBadge :type=\"host.status\" />\r\n          </div>\r\n\r\n          <!-- 账号列表 -->\r\n          <div class=\"space-y-4\">\r\n            <div v-for=\"account in host.accounts\" :key=\"account.id\" class=\"border border-gray-200 rounded-lg p-3\"\r\n              :class=\"{ 'border-green-300 bg-green-50': account.isDefault }\">\r\n              <div class=\"flex flex-wrap justify-between items-center mb-2\">\r\n                <div class=\"flex items-center mb-2 sm:mb-0\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n                <button\r\n                  class=\"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                  @click=\"openChangePasswordModal(host, account)\">\r\n                  <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                  修改密码\r\n                </button>\r\n              </div>\r\n\r\n              <!-- 密码展示 -->\r\n              <div class=\"mb-2\">\r\n                <div class=\"text-xs font-medium text-gray-500 mb-1\">密码</div>\r\n                <div class=\"flex items-center\">\r\n                  <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\" readonly\r\n                    class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                  <div class=\"ml-2\" v-tooltip=\"isPasswordCompliant(account).text\">\r\n                    <font-awesome-icon \r\n                      :icon=\"['fas', isPasswordCompliant(account).compliant ? 'check-circle' : 'exclamation-circle']\" \r\n                      :class=\"isPasswordCompliant(account).compliant ? 'text-green-500' : 'text-red-500'\" \r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 密码信息区域 -->\r\n              <div class=\"grid grid-cols-2 gap-2 text-xs\">\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">最后修改时间</div>\r\n                  <div class=\"text-gray-900\">{{ account.lastPasswordChange || '-' }}</div>\r\n                </div>\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">密码过期</div>\r\n                  <div :class=\"{\r\n                    'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\r\n                    'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                    'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                    'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                  }\">\r\n                    {{ isPasswordExpired(account).text }}\r\n                    <span\r\n                      v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                      class=\"ml-1\">\r\n                      <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-span-2 mt-1\">\r\n                  <div class=\"font-medium text-gray-500 mb-1\">密码策略</div>\r\n                  <div :class=\"['inline-flex items-center px-2 py-0.5 rounded text-xs font-medium', getPolicyColorClass(account.policyId)]\">\r\n                    {{ getPolicyName(account.policyId) }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 添加账号按钮 -->\r\n          <div class=\"mt-4 flex justify-center\">\r\n            <button\r\n              class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n              @click=\"openAddAccountModal(host)\">\r\n              <font-awesome-icon :icon=\"['fas', 'plus']\" class=\"mr-1\" />\r\n              添加账号\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" size=\"lg\" @confirm=\"updatePassword\" :loading=\"processing\">\r\n      <!-- 主机信息卡片 -->\r\n      <div class=\"mb-6\">\r\n        <div class=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\r\n          <div class=\"flex items-center space-x-3\">\r\n            <div class=\"w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center\">\r\n              <font-awesome-icon :icon=\"['fas', 'server']\" class=\"text-blue-600 dark:text-blue-400\" />\r\n            </div>\r\n            <div>\r\n              <h4 class=\"font-semibold text-gray-900 dark:text-white\">{{ currentHost.name }}</h4>\r\n              <div class=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                <span>{{ currentHost.ip }}</span> • <span>账号: {{ currentAccount.username }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 密码生成方式选择 -->\r\n      <div class=\"mb-6\">\r\n        <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">密码生成方式</label>\r\n        <div class=\"grid grid-cols-2 gap-3\">\r\n          <button\r\n            @click=\"changePasswordModal.method = 'auto'\"\r\n            class=\"flex items-center justify-center px-4 py-3 border-2 rounded-lg transition-all duration-200 focus:outline-none\"\r\n            :class=\"changePasswordModal.method === 'auto'\r\n              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'\r\n              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 text-gray-700 dark:text-gray-300'\"\r\n          >\r\n            <font-awesome-icon :icon=\"['fas', 'magic']\" class=\"mr-2\" />\r\n            <span class=\"font-medium\">智能生成</span>\r\n          </button>\r\n          <button\r\n            @click=\"changePasswordModal.method = 'manual'\"\r\n            class=\"flex items-center justify-center px-4 py-3 border-2 rounded-lg transition-all duration-200 focus:outline-none\"\r\n            :class=\"changePasswordModal.method === 'manual'\r\n              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'\r\n              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 text-gray-700 dark:text-gray-300'\"\r\n          >\r\n            <font-awesome-icon :icon=\"['fas', 'edit']\" class=\"mr-2\" />\r\n            <span class=\"font-medium\">手动输入</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 智能生成模式 -->\r\n      <div v-if=\"changePasswordModal.method === 'auto'\" class=\"mb-6\">\r\n        <div class=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700\">\r\n          <AdvancedPasswordGenerator\r\n            :initial-options=\"getPasswordGeneratorOptions()\"\r\n            @password-generated=\"onPasswordGenerated\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else class=\"space-y-4\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.new ? 'text' : 'password'\" v-model=\"changePasswordModal.newPassword\"\r\n              class=\"form-control flex-1\" placeholder=\"输入新密码\" />\r\n            <button @click=\"passwordVisibility.new = !passwordVisibility.new\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.new ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.confirm ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.confirmPassword\" class=\"form-control flex-1\"\r\n              :class=\"{ 'border-red-500': passwordMismatch }\" placeholder=\"再次输入新密码\" />\r\n            <button @click=\"passwordVisibility.confirm = !passwordVisibility.confirm\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.confirm ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n          <div v-if=\"passwordMismatch\" class=\"text-sm text-red-500 mt-1\">两次输入的密码不一致</div>\r\n        </div>\r\n\r\n        <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n      </div>\r\n\r\n      <div class=\"space-y-2 mt-4\">\r\n        <div class=\"form-label font-medium\">执行选项</div>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          <span class=\"ml-2\">立即执行</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          <span class=\"ml-2\">保存历史记录</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          <span class=\"ml-2\">记录审计日志</span>\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 添加账号弹窗 -->\r\n    <BaseModal v-model=\"addAccountModal.show\" title=\"添加账号\" @confirm=\"addAccount\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">账号名称</label>\r\n        <input type=\"text\" v-model=\"addAccountModal.username\" class=\"form-control\" placeholder=\"输入账号名称\" />\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">设为默认账号</label>\r\n        <div class=\"relative inline-block w-10 mr-2 align-middle select-none\">\r\n          <input type=\"checkbox\" v-model=\"addAccountModal.isDefault\"\r\n            class=\"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\" />\r\n          <label class=\"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"></label>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"addAccountModal.policyId\" class=\"form-select\" @change=\"generatePasswordForNewAccount()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <div class=\"flex justify-between mb-1\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <button @click=\"generatePasswordForNewAccount()\" type=\"button\"\r\n            class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n            重新生成\r\n          </button>\r\n        </div>\r\n        <div class=\"flex items-center\">\r\n          <input :type=\"passwordVisibility.newAccount ? 'text' : 'password'\" v-model=\"addAccountModal.password\" readonly\r\n            class=\"form-control flex-1 bg-gray-50\" />\r\n          <button @click=\"passwordVisibility.newAccount = !passwordVisibility.newAccount\" type=\"button\"\r\n            class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', passwordVisibility.newAccount ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量添加账号弹窗 -->\r\n    <BaseModal v-model=\"batchAddAccountModal.show\" title=\"批量添加账号\" size=\"lg\" @confirm=\"batchAddAccounts\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatchAdd\" @update:modelValue=\"toggleSelectAllBatchAdd\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchAddAccountModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedBatchAddHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">账号信息</label>\r\n        <div class=\"p-4 border border-gray-200 rounded-md\">\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">账号名称 <span class=\"text-red-500\">*</span></label>\r\n            <input type=\"text\" v-model=\"batchAddAccountModal.username\" class=\"form-control\" placeholder=\"输入统一账号名称\" />\r\n            <div class=\"text-xs text-gray-500 mt-1\">将在所有选中主机上创建同名账号</div>\r\n          </div>\r\n\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">账号角色</label>\r\n            <select v-model=\"batchAddAccountModal.role\" class=\"form-select\">\r\n              <option value=\"admin\">管理员</option>\r\n              <option value=\"user\">普通用户</option>\r\n              <option value=\"service\">服务账号</option>\r\n              <option value=\"readonly\">只读账号</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div class=\"flex items-center mb-3\">\r\n            <label class=\"inline-flex items-center\">\r\n              <input type=\"checkbox\" v-model=\"batchAddAccountModal.setAsDefault\" class=\"form-checkbox\">\r\n              <span class=\"ml-2\">设为默认账号</span>\r\n            </label>\r\n          </div>\r\n\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">密码生成方式</label>\r\n            <div class=\"flex space-x-3\">\r\n              <button @click=\"batchAddAccountModal.useSamePassword = true\"\r\n                class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n                :class=\"batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n                <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n                同一密码\r\n              </button>\r\n              <button @click=\"batchAddAccountModal.useSamePassword = false\"\r\n                class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n                :class=\"!batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n                <font-awesome-icon :icon=\"['fas', 'random']\" class=\"mr-2\" />\r\n                随机密码\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <div v-if=\"batchAddAccountModal.useSamePassword\" class=\"mb-3\">\r\n            <label class=\"form-label\">统一密码</label>\r\n            <div class=\"flex items-center\">\r\n              <input :type=\"passwordVisibility.batchPassword ? 'text' : 'password'\"\r\n                v-model=\"batchAddAccountModal.password\" readonly class=\"form-control flex-1 bg-gray-50\" />\r\n              <button @click=\"passwordVisibility.batchPassword = !passwordVisibility.batchPassword\" type=\"button\"\r\n                class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                <font-awesome-icon :icon=\"['fas', passwordVisibility.batchPassword ? 'eye-slash' : 'eye']\"\r\n                  class=\"text-lg\" />\r\n              </button>\r\n              <button @click=\"generatePasswordForBatchAccount()\" type=\"button\"\r\n                class=\"ml-2 px-3 py-1.5 border border-gray-300 text-xs rounded-md\">\r\n                <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n                重新生成\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchAddAccountModal.policyId\" class=\"form-select\" @change=\"generatePasswordForBatchAccount()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <div class=\"space-y-2\">\r\n          <CustomCheckbox v-model=\"batchAddAccountModal.ignoreErrors\">\r\n            <span class=\"ml-2\">忽略错误继续执行</span>\r\n          </CustomCheckbox>\r\n          <CustomCheckbox v-model=\"batchAddAccountModal.generateReport\">\r\n            <span class=\"ml-2\">生成账号创建报告</span>\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\nimport SecurityDashboard from '@/components/SecurityDashboard.vue'\r\nimport AdvancedPasswordGenerator from '@/components/AdvancedPasswordGenerator.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter,\r\n    SecurityDashboard,\r\n    AdvancedPasswordGenerator\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      selectAllBatchAdd: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      currentAccount: {},\r\n      passwordVisibility: {\r\n        generated: false,\r\n        new: false,\r\n        confirm: false,\r\n        newAccount: false,\r\n        batchPassword: false\r\n      },\r\n      viewMode: 'table',\r\n      filterText: '',\r\n      accountFilterText: '',\r\n      statusFilter: 'all',\r\n      expiryFilter: 'all',\r\n      policyFilter: 'all',\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      },\r\n\r\n      // 添加账号弹窗\r\n      addAccountModal: {\r\n        show: false,\r\n        username: '',\r\n        password: '',\r\n        isDefault: false,\r\n        policyId: 1\r\n      },\r\n\r\n      // 批量添加账号弹窗\r\n      batchAddAccountModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        username: '',\r\n        password: '',\r\n        role: 'admin',\r\n        setAsDefault: false,\r\n        useSamePassword: true,\r\n        policyId: 1\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    // 获取策略名称\r\n    getPolicyName() {\r\n      return (policyId) => {\r\n        if (!policyId) return '无';\r\n        const policy = this.policies.find(p => p.id === policyId);\r\n        return policy ? policy.name : '无';\r\n      };\r\n    },\r\n\r\n    // 获取策略颜色类\r\n    getPolicyColorClass() {\r\n      return (policyId) => {\r\n        if (!policyId) return 'bg-gray-100 text-gray-800';\r\n        \r\n        // 根据策略ID返回对应的颜色类\r\n        const colorMap = {\r\n          1: 'bg-red-100 text-red-800',    // 高强度策略\r\n          2: 'bg-blue-100 text-blue-800',  // 标准策略\r\n          3: 'bg-purple-100 text-purple-800' // 紧急策略\r\n        };\r\n        \r\n        return colorMap[policyId] || 'bg-gray-100 text-gray-800';\r\n      };\r\n    },\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    },\r\n\r\n    // 过滤后的主机列表\r\n    filteredHosts() {\r\n      return this.hosts.filter(host => {\r\n        // 文本过滤\r\n        const textMatch = this.filterText === '' ||\r\n          host.name.toLowerCase().includes(this.filterText.toLowerCase()) ||\r\n          host.ip.includes(this.filterText);\r\n\r\n        // 状态过滤\r\n        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;\r\n\r\n        return textMatch && statusMatch;\r\n      });\r\n    },\r\n\r\n    // 获取所有账号（扁平化处理）\r\n    getAllAccounts() {\r\n      // 为每个账号添加主机引用\r\n      const accounts = [];\r\n      this.filteredHosts.forEach(host => {\r\n        host.accounts.forEach(account => {\r\n          accounts.push({\r\n            ...account,\r\n            host: host\r\n          });\r\n        });\r\n      });\r\n      return accounts;\r\n    },\r\n\r\n    // 筛选后的账号\r\n    filteredAccounts() {\r\n      return this.getAllAccounts.filter(account => {\r\n        // 账号名称筛选\r\n        const accountMatch = this.accountFilterText === '' ||\r\n          account.username.toLowerCase().includes(this.accountFilterText.toLowerCase());\r\n\r\n        // 密码过期筛选\r\n        let expiryMatch = true;\r\n        if (this.expiryFilter !== 'all') {\r\n          const expiryStatus = this.isPasswordExpired(account).status;\r\n          if (this.expiryFilter === 'expired') {\r\n            expiryMatch = expiryStatus === 'expired';\r\n          } else if (this.expiryFilter === 'expiring-soon') {\r\n            expiryMatch = expiryStatus === 'danger' || expiryStatus === 'warning';\r\n          } else if (this.expiryFilter === 'valid') {\r\n            expiryMatch = expiryStatus === 'normal';\r\n          }\r\n        }\r\n\r\n        // 策略筛选\r\n        let policyMatch = true;\r\n        if (this.policyFilter !== 'all') {\r\n          if (this.policyFilter === 'none') {\r\n            policyMatch = !account.policyId;\r\n          } else {\r\n            policyMatch = account.policyId === parseInt(this.policyFilter);\r\n          }\r\n        }\r\n\r\n        return accountMatch && expiryMatch && policyMatch;\r\n      });\r\n    },\r\n\r\n    // 分组后的账号列表\r\n    groupedAccounts() {\r\n      // 按主机ID分组\r\n      const groups = {};\r\n      this.filteredAccounts.forEach(account => {\r\n        const hostId = account.host.id;\r\n        if (!groups[hostId]) {\r\n          groups[hostId] = {\r\n            hostId: hostId,\r\n            hostName: account.host.name,\r\n            hostIp: account.host.ip,\r\n            host: account.host,\r\n            accounts: []\r\n          };\r\n        }\r\n        groups[hostId].accounts.push(account);\r\n      });\r\n\r\n      // 转换为数组\r\n      return Object.values(groups);\r\n    },\r\n\r\n    selectedBatchAddHostsCount() {\r\n      return Object.values(this.batchAddAccountModal.selectedHosts).filter(Boolean).length\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    toggleSelectAllBatchAdd(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchAddAccountModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host, account) {\r\n      this.currentHost = host\r\n      this.currentAccount = account\r\n      this.changePasswordModal.show = true\r\n      this.changePasswordModal.generatedPassword = this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword(policy) {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policyObj ? policyObj.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      if (this.changePasswordModal && !policy) {\r\n        this.changePasswordModal.generatedPassword = password\r\n      }\r\n\r\n      return password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto'\r\n          ? this.changePasswordModal.generatedPassword\r\n          : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          accountId: this.currentAccount.id,\r\n          password: password,\r\n          policyId: this.changePasswordModal.policyId\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的 ${this.currentAccount.username} 账号密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取所选策略\r\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取紧急策略\r\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    togglePasswordVisibility(hostId) {\r\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]\r\n    },\r\n\r\n    isPasswordExpired(account) {\r\n      if (!account.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }\r\n\r\n      // 解析过期时间\r\n      const expiryDate = new Date(account.passwordExpiryDate)\r\n      const now = new Date()\r\n\r\n      // 如果已过期\r\n      if (expiryDate < now) {\r\n        return {\r\n          status: 'expired',\r\n          days: 0,\r\n          text: '已过期'\r\n        }\r\n      }\r\n\r\n      // 计算剩余天数和小时数\r\n      const diffTime = expiryDate - now\r\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))\r\n      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n\r\n      // 根据剩余时间确定状态\r\n      let status = 'normal'\r\n      if (diffDays < 7) {\r\n        status = 'danger'  // 少于7天\r\n      } else if (diffDays < 14) {\r\n        status = 'warning' // 少于14天\r\n      }\r\n\r\n      // 格式化显示文本\r\n      let text = ''\r\n      if (diffDays > 0) {\r\n        text += `${diffDays}天`\r\n      }\r\n      if (diffHours > 0 || diffDays === 0) {\r\n        text += `${diffHours}小时`\r\n      }\r\n\r\n      return { status, days: diffDays, text: `剩余${text}` }\r\n    },\r\n\r\n    openAddAccountModal(host) {\r\n      this.currentHost = host\r\n      this.addAccountModal.show = true\r\n    },\r\n\r\n    async addAccount() {\r\n      if (!this.addAccountModal.username || !this.addAccountModal.password) {\r\n        alert('请填写完整的账号信息！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('addHostAccount', {\r\n          hostId: this.currentHost.id,\r\n          username: this.addAccountModal.username,\r\n          password: this.addAccountModal.password,\r\n          policyId: this.addAccountModal.policyId,\r\n          isDefault: this.addAccountModal.isDefault\r\n        })\r\n\r\n        this.addAccountModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为主机 ${this.currentHost.name} 添加账号！`)\r\n      } catch (error) {\r\n        console.error('添加账号失败', error)\r\n        alert('添加账号失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    generatePasswordForNewAccount() {\r\n      this.addAccountModal.password = this.generatePassword()\r\n    },\r\n\r\n    // 复制密码到剪贴板\r\n    copyPassword(account) {\r\n      // 创建一个临时输入框\r\n      const tempInput = document.createElement('input');\r\n      tempInput.value = account.password;\r\n      document.body.appendChild(tempInput);\r\n      tempInput.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(tempInput);\r\n\r\n      // 显示提示\r\n      alert(`已复制 ${account.host.name} 的 ${account.username} 账号密码到剪贴板！`);\r\n    },\r\n\r\n    // 导出密码数据为CSV\r\n    exportPasswordsToCSV() {\r\n      // 准备CSV标题行\r\n      const headers = ['主机名', 'IP地址', '账号', '密码', '最后修改时间', '过期时间', '状态', '策略'];\r\n      const csvRows = [headers];\r\n      \r\n      // 添加数据行\r\n      this.filteredAccounts.forEach(account => {\r\n        const row = [\r\n          account.host.name,\r\n          account.host.ip,\r\n          account.username,\r\n          account.password,\r\n          account.lastPasswordChange || '-',\r\n          account.passwordExpiryDate || '-',\r\n          account.host.status,\r\n          this.getPolicyName(account.policyId)\r\n        ];\r\n        csvRows.push(row);\r\n      });\r\n      \r\n      // 转换为CSV格式\r\n      const csvContent = csvRows.map(row => row.map(cell => \r\n        `\"${String(cell).replace(/\"/g, '\"\"')}\"`).join(',')).join('\\n');\r\n      \r\n      // 创建下载链接\r\n      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n      const url = URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.setAttribute('href', url);\r\n      link.setAttribute('download', `密码数据_${new Date().toISOString().slice(0,10)}.csv`);\r\n      link.style.visibility = 'hidden';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    },\r\n\r\n    generatePasswordForBatchAccount() {\r\n      this.batchAddAccountModal.password = this.generatePassword()\r\n    },\r\n\r\n    async batchAddAccounts() {\r\n      if (!this.batchAddAccountModal.username) {\r\n        alert('请填写账号名称！')\r\n        return\r\n      }\r\n\r\n      const selectedHostIds = Object.entries(this.batchAddAccountModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('batchAddAccounts', {\r\n          hostIds: selectedHostIds,\r\n          username: this.batchAddAccountModal.username,\r\n          password: this.batchAddAccountModal.useSamePassword ? this.batchAddAccountModal.password : null,\r\n          role: this.batchAddAccountModal.role,\r\n          isDefault: this.batchAddAccountModal.setAsDefault,\r\n          policyId: this.batchAddAccountModal.policyId\r\n        })\r\n\r\n        this.batchAddAccountModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机添加账号！`)\r\n      } catch (error) {\r\n        console.error('批量添加账号失败', error)\r\n        alert('批量添加账号失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    openBatchAddAccountModal() {\r\n      this.batchAddAccountModal.show = true\r\n      this.batchAddAccountModal.selectedHosts = {}\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchAddAccountModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 生成初始密码\r\n      this.generatePasswordForBatchAccount()\r\n    },\r\n\r\n    printPasswordData() {\r\n      // 使用纯JavaScript方式创建打印内容，避免Vue模板编译问题\r\n      const printWindow = window.open(\"\", \"_blank\");\r\n      if (!printWindow) {\r\n        alert(\"无法打开打印窗口，请允许弹出窗口\");\r\n        return;\r\n      }\r\n      \r\n      const doc = printWindow.document;\r\n      \r\n      // 清空文档\r\n      doc.open();\r\n      doc.write(\"<!DOCTYPE html>\");\r\n      doc.write(\"<html>\");\r\n      doc.write(\"<head>\");\r\n      doc.write(\"<title>主机账号密码报表</title>\");\r\n      \r\n      // 添加样式\r\n      doc.write(\"<style>\");\r\n      doc.write(\"body { font-family: Arial, sans-serif; }\");\r\n      doc.write(\"table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\");\r\n      doc.write(\"th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\");\r\n      doc.write(\"th { background-color: #f2f2f2; }\");\r\n      doc.write(\".print-header { text-align: center; margin-bottom: 20px; }\");\r\n      doc.write(\".print-footer { text-align: center; font-size: 12px; margin-top: 30px; }\");\r\n      doc.write(\".status-normal { color: green; }\");\r\n      doc.write(\".status-warning { color: orange; }\");\r\n      doc.write(\".status-error { color: red; }\");\r\n      doc.write(\"@page { margin: 1cm; }\");\r\n      doc.write(\"</style>\");\r\n      doc.write(\"</head>\");\r\n      doc.write(\"<body>\");\r\n      \r\n      // 添加标题\r\n      doc.write(\"<div class=\\\"print-header\\\">\");\r\n      doc.write(\"<h1>主机账号密码报表</h1>\");\r\n      doc.write(\"<p>生成时间: \" + new Date().toLocaleString(\"zh-CN\") + \"</p>\");\r\n      doc.write(\"</div>\");\r\n      \r\n      // 添加表格\r\n      doc.write(\"<table>\");\r\n      \r\n      // 表头\r\n      doc.write(\"<thead><tr>\");\r\n      [\"主机名\", \"IP地址\", \"账号\", \"最后密码修改时间\", \"密码过期时间\", \"状态\", \"策略\"].forEach(header => {\r\n        doc.write(\"<th>\" + header + \"</th>\");\r\n      });\r\n      doc.write(\"</tr></thead>\");\r\n      \r\n      // 表格内容\r\n      doc.write(\"<tbody>\");\r\n      this.filteredAccounts.forEach(account => {\r\n        const statusClass = \"status-\" + account.host.status;\r\n        doc.write(\"<tr>\");\r\n        doc.write(\"<td>\" + account.host.name + \"</td>\");\r\n        doc.write(\"<td>\" + account.host.ip + \"</td>\");\r\n        doc.write(\"<td>\" + account.username + (account.isDefault ? \" (默认)\" : \"\") + \"</td>\");\r\n        doc.write(\"<td>\" + (account.lastPasswordChange || \"-\") + \"</td>\");\r\n        doc.write(\"<td>\" + (account.passwordExpiryDate || \"-\") + \"</td>\");\r\n        doc.write(\"<td class=\\\"\" + statusClass + \"\\\">\" + account.host.status + \"</td>\");\r\n        doc.write(\"<td>\" + this.getPolicyName(account.policyId) + \"</td>\");\r\n        doc.write(\"</tr>\");\r\n      });\r\n      doc.write(\"</tbody>\");\r\n      doc.write(\"</table>\");\r\n      \r\n      // 添加页脚\r\n      doc.write(\"<div class=\\\"print-footer\\\">\");\r\n      doc.write(\"<p>注意：本文档包含敏感信息，请妥善保管</p>\");\r\n      doc.write(\"</div>\");\r\n      \r\n      // 添加自动打印脚本\r\n      doc.write(\"<\" + \"script\" + \">\");\r\n      doc.write(\"window.onload = function() { window.print(); }\");\r\n      doc.write(\"</\" + \"script\" + \">\");\r\n      \r\n      doc.write(\"</body>\");\r\n      doc.write(\"</html>\");\r\n      doc.close();\r\n    },\r\n\r\n    // 检查密码是否符合策略要求\r\n    isPasswordCompliant(account) {\r\n      if (!account.policyId) return { compliant: true, text: '无策略' };\r\n      \r\n      const password = account.password;\r\n      const policy = this.policies.find(p => p.id === account.policyId);\r\n      \r\n      if (!policy) return { compliant: true, text: '无策略' };\r\n      \r\n      const checks = [];\r\n      \r\n      // 检查长度\r\n      if (password.length < policy.minLength) {\r\n        checks.push(`密码长度不足 ${policy.minLength} 位`);\r\n      }\r\n      \r\n      // 检查大写字母\r\n      if (policy.requireUppercase && !/[A-Z]/.test(password)) {\r\n        checks.push('缺少大写字母');\r\n      }\r\n      \r\n      // 检查小写字母\r\n      if (policy.requireLowercase && !/[a-z]/.test(password)) {\r\n        checks.push('缺少小写字母');\r\n      }\r\n      \r\n      // 检查数字\r\n      if (policy.requireNumbers && !/[0-9]/.test(password)) {\r\n        checks.push('缺少数字');\r\n      }\r\n      \r\n      // 检查特殊字符\r\n      if (policy.requireSpecial && !/[^A-Za-z0-9]/.test(password)) {\r\n        checks.push('缺少特殊字符');\r\n      }\r\n      \r\n      // 检查是否包含用户名\r\n      if (policy.forbidUsername && password.toLowerCase().includes(account.username.toLowerCase())) {\r\n        checks.push('密码包含用户名');\r\n      }\r\n      \r\n      return {\r\n        compliant: checks.length === 0,\r\n        text: checks.length === 0 ? '符合策略' : `不符合: ${checks.join(', ')}`,\r\n        details: checks\r\n      };\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": "AAyxBA,SAASA,QAAQ,EAAEC,UAAS,QAAS,MAAK;AAC1C,OAAOC,SAAQ,MAAO,4BAA2B;AACjD,OAAOC,WAAU,MAAO,8BAA6B;AACrD,OAAOC,cAAa,MAAO,iCAAgC;AAC3D,OAAOC,qBAAoB,MAAO,wCAAuC;AACzE,OAAOC,iBAAgB,MAAO,oCAAmC;AACjE,OAAOC,yBAAwB,MAAO,4CAA2C;AAEjF,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,UAAU,EAAE;IACVP,SAAS;IACTC,WAAW;IACXC,cAAc;IACdC,qBAAqB;IACrBC,iBAAiB;IACjBC;EACF,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,KAAK;MAChBC,cAAc,EAAE,KAAK;MACrBC,iBAAiB,EAAE,KAAK;MACxBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,CAAC,CAAC;MACfC,cAAc,EAAE,CAAC,CAAC;MAClBC,kBAAkB,EAAE;QAClBC,SAAS,EAAE,KAAK;QAChBC,GAAG,EAAE,KAAK;QACVC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE,KAAK;QACjBC,aAAa,EAAE;MACjB,CAAC;MACDC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE,EAAE;MACdC,iBAAiB,EAAE,EAAE;MACrBC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,KAAK;MAEnB;MACAC,mBAAmB,EAAE;QACnBC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE,CAAC;QACXC,iBAAiB,EAAE,cAAc;QACjCC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE,EAAE;QACnBC,kBAAkB,EAAE,IAAI;QACxBC,WAAW,EAAE,KAAK;QAClBC,QAAQ,EAAE;MACZ,CAAC;MAED;MACAC,gBAAgB,EAAE;QAChBT,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBR,QAAQ,EAAE,CAAC;QACXS,aAAa,EAAE,WAAW;QAC1BC,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,EAAE;QACjBC,YAAY,EAAE,IAAI;QAClBC,WAAW,EAAE,IAAI;QACjBC,gBAAgB,EAAE;MACpB,CAAC;MAED;MACAC,eAAe,EAAE;QACfjB,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBR,QAAQ,EAAE,CAAC;QACXgB,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;MAED;MACAC,mBAAmB,EAAE;QACnBpB,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBR,QAAQ,EAAE,CAAC;QAAE;QACbmB,MAAM,EAAE,mBAAmB;QAC3BC,WAAW,EAAE;MACf,CAAC;MAED;MACAC,eAAe,EAAE;QACfvB,IAAI,EAAE,KAAK;QACXwB,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,KAAK;QAChBxB,QAAQ,EAAE;MACZ,CAAC;MAED;MACAyB,oBAAoB,EAAE;QACpB3B,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBc,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZG,IAAI,EAAE,OAAO;QACbC,YAAY,EAAE,KAAK;QACnBC,eAAe,EAAE,IAAI;QACrB5B,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;EACD6B,QAAQ,EAAE;IACR,GAAG7D,QAAQ,CAAC;MACV8D,KAAK,EAAEC,KAAI,IAAKA,KAAK,CAACD,KAAK;MAC3BE,QAAQ,EAAED,KAAI,IAAKA,KAAK,CAACC;IAC3B,CAAC,CAAC;IACF,GAAG/D,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC;IAEhC;IACAgE,aAAaA,CAAA,EAAG;MACd,OAAQjC,QAAQ,IAAK;QACnB,IAAI,CAACA,QAAQ,EAAE,OAAO,GAAG;QACzB,MAAMkC,MAAK,GAAI,IAAI,CAACF,QAAQ,CAACG,IAAI,CAACC,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAMrC,QAAQ,CAAC;QACzD,OAAOkC,MAAK,GAAIA,MAAM,CAAC1D,IAAG,GAAI,GAAG;MACnC,CAAC;IACH,CAAC;IAED;IACA8D,mBAAmBA,CAAA,EAAG;MACpB,OAAQtC,QAAQ,IAAK;QACnB,IAAI,CAACA,QAAQ,EAAE,OAAO,2BAA2B;;QAEjD;QACA,MAAMuC,QAAO,GAAI;UACf,CAAC,EAAE,yBAAyB;UAAK;UACjC,CAAC,EAAE,2BAA2B;UAAG;UACjC,CAAC,EAAE,+BAA8B,CAAE;QACrC,CAAC;QAED,OAAOA,QAAQ,CAACvC,QAAQ,KAAK,2BAA2B;MAC1D,CAAC;IACH,CAAC;IAEDwC,gBAAgBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAAC3C,mBAAmB,CAACK,WAAU,IACxC,IAAI,CAACL,mBAAmB,CAACM,eAAc,IACvC,IAAI,CAACN,mBAAmB,CAACK,WAAU,KAAM,IAAI,CAACL,mBAAmB,CAACM,eAAc;IACpF,CAAC;IAEDsC,kBAAkBA,CAAA,EAAG;MACnB,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACpC,gBAAgB,CAACC,aAAa,CAAC,CAACoC,MAAM,CAACC,OAAO,CAAC,CAACC,MAAK;IACjF,CAAC;IAEDC,iBAAiBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACjB,KAAK,CAACc,MAAM,CAACI,IAAG,IAAKA,IAAI,CAACC,QAAQ;IAChD,CAAC;IAEDC,iBAAiBA,CAAA,EAAG;MAClB;MACA,OAAO,IAAI,CAAClB,QAAQ,CAACY,MAAM,CAACR,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,KAAKD,CAAC,CAACC,EAAC,KAAM,CAAC;IAC3D,CAAC;IAED;IACAc,aAAaA,CAAA,EAAG;MACd,OAAO,IAAI,CAACrB,KAAK,CAACc,MAAM,CAACI,IAAG,IAAK;QAC/B;QACA,MAAMI,SAAQ,GAAI,IAAI,CAAC5D,UAAS,KAAM,EAAC,IACrCwD,IAAI,CAACxE,IAAI,CAAC6E,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC9D,UAAU,CAAC6D,WAAW,CAAC,CAAC,KAC9DL,IAAI,CAACO,EAAE,CAACD,QAAQ,CAAC,IAAI,CAAC9D,UAAU,CAAC;;QAEnC;QACA,MAAMgE,WAAU,GAAI,IAAI,CAAC9D,YAAW,KAAM,KAAI,IAAKsD,IAAI,CAACS,MAAK,KAAM,IAAI,CAAC/D,YAAY;QAEpF,OAAO0D,SAAQ,IAAKI,WAAW;MACjC,CAAC,CAAC;IACJ,CAAC;IAED;IACAE,cAAcA,CAAA,EAAG;MACf;MACA,MAAMC,QAAO,GAAI,EAAE;MACnB,IAAI,CAACR,aAAa,CAACS,OAAO,CAACZ,IAAG,IAAK;QACjCA,IAAI,CAACW,QAAQ,CAACC,OAAO,CAACC,OAAM,IAAK;UAC/BF,QAAQ,CAACG,IAAI,CAAC;YACZ,GAAGD,OAAO;YACVb,IAAI,EAAEA;UACR,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAOW,QAAQ;IACjB,CAAC;IAED;IACAI,gBAAgBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACL,cAAc,CAACd,MAAM,CAACiB,OAAM,IAAK;QAC3C;QACA,MAAMG,YAAW,GAAI,IAAI,CAACvE,iBAAgB,KAAM,EAAC,IAC/CoE,OAAO,CAACvC,QAAQ,CAAC+B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC7D,iBAAiB,CAAC4D,WAAW,CAAC,CAAC,CAAC;;QAE/E;QACA,IAAIY,WAAU,GAAI,IAAI;QACtB,IAAI,IAAI,CAACtE,YAAW,KAAM,KAAK,EAAE;UAC/B,MAAMuE,YAAW,GAAI,IAAI,CAACC,iBAAiB,CAACN,OAAO,CAAC,CAACJ,MAAM;UAC3D,IAAI,IAAI,CAAC9D,YAAW,KAAM,SAAS,EAAE;YACnCsE,WAAU,GAAIC,YAAW,KAAM,SAAS;UAC1C,OAAO,IAAI,IAAI,CAACvE,YAAW,KAAM,eAAe,EAAE;YAChDsE,WAAU,GAAIC,YAAW,KAAM,QAAO,IAAKA,YAAW,KAAM,SAAS;UACvE,OAAO,IAAI,IAAI,CAACvE,YAAW,KAAM,OAAO,EAAE;YACxCsE,WAAU,GAAIC,YAAW,KAAM,QAAQ;UACzC;QACF;;QAEA;QACA,IAAIE,WAAU,GAAI,IAAI;QACtB,IAAI,IAAI,CAACxE,YAAW,KAAM,KAAK,EAAE;UAC/B,IAAI,IAAI,CAACA,YAAW,KAAM,MAAM,EAAE;YAChCwE,WAAU,GAAI,CAACP,OAAO,CAAC7D,QAAQ;UACjC,OAAO;YACLoE,WAAU,GAAIP,OAAO,CAAC7D,QAAO,KAAMqE,QAAQ,CAAC,IAAI,CAACzE,YAAY,CAAC;UAChE;QACF;QAEA,OAAOoE,YAAW,IAAKC,WAAU,IAAKG,WAAW;MACnD,CAAC,CAAC;IACJ,CAAC;IAED;IACAE,eAAeA,CAAA,EAAG;MAChB;MACA,MAAMC,MAAK,GAAI,CAAC,CAAC;MACjB,IAAI,CAACR,gBAAgB,CAACH,OAAO,CAACC,OAAM,IAAK;QACvC,MAAMW,MAAK,GAAIX,OAAO,CAACb,IAAI,CAACX,EAAE;QAC9B,IAAI,CAACkC,MAAM,CAACC,MAAM,CAAC,EAAE;UACnBD,MAAM,CAACC,MAAM,IAAI;YACfA,MAAM,EAAEA,MAAM;YACdC,QAAQ,EAAEZ,OAAO,CAACb,IAAI,CAACxE,IAAI;YAC3BkG,MAAM,EAAEb,OAAO,CAACb,IAAI,CAACO,EAAE;YACvBP,IAAI,EAAEa,OAAO,CAACb,IAAI;YAClBW,QAAQ,EAAE;UACZ,CAAC;QACH;QACAY,MAAM,CAACC,MAAM,CAAC,CAACb,QAAQ,CAACG,IAAI,CAACD,OAAO,CAAC;MACvC,CAAC,CAAC;;MAEF;MACA,OAAOnB,MAAM,CAACC,MAAM,CAAC4B,MAAM,CAAC;IAC9B,CAAC;IAEDI,0BAA0BA,CAAA,EAAG;MAC3B,OAAOjC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAClB,oBAAoB,CAACjB,aAAa,CAAC,CAACoC,MAAM,CAACC,OAAO,CAAC,CAACC,MAAK;IACrF;EACF,CAAC;EACD8B,OAAO,EAAE;IACPC,eAAeA,CAACC,KAAK,EAAE;MACrB,IAAI,CAACC,MAAM,CAACC,MAAM,CAAC,gBAAgB,EAAEF,KAAK;IAC5C,CAAC;IAEDG,oBAAoBA,CAACH,KAAK,EAAE;MAC1B,IAAI,CAAChD,KAAK,CAAC8B,OAAO,CAACZ,IAAG,IAAK;QACzB,IAAI,CAACzC,gBAAgB,CAACC,aAAa,CAACwC,IAAI,CAACX,EAAE,IAAIyC,KAAI;MACrD,CAAC;IACH,CAAC;IAEDI,uBAAuBA,CAACJ,KAAK,EAAE;MAC7B,IAAI,CAAChD,KAAK,CAAC8B,OAAO,CAACZ,IAAG,IAAK;QACzB,IAAI,CAACvB,oBAAoB,CAACjB,aAAa,CAACwC,IAAI,CAACX,EAAE,IAAIyC,KAAI;MACzD,CAAC;IACH,CAAC;IAEDK,uBAAuBA,CAACnC,IAAI,EAAEa,OAAO,EAAE;MACrC,IAAI,CAAC9E,WAAU,GAAIiE,IAAG;MACtB,IAAI,CAAChE,cAAa,GAAI6E,OAAM;MAC5B,IAAI,CAAChE,mBAAmB,CAACC,IAAG,GAAI,IAAG;MACnC,IAAI,CAACD,mBAAmB,CAACI,iBAAgB,GAAI,IAAI,CAACmF,gBAAgB,CAAC;IACrE,CAAC;IAEDC,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAAC9E,gBAAgB,CAACT,IAAG,GAAI,IAAG;;MAEhC;MACA,IAAI,CAACgC,KAAK,CAAC8B,OAAO,CAACZ,IAAG,IAAK;QACzB,IAAI,CAACzC,gBAAgB,CAACC,aAAa,CAACwC,IAAI,CAACX,EAAE,IAAIW,IAAI,CAACC,QAAO;MAC7D,CAAC;;MAED;MACA,MAAMqC,KAAI,GAAI,IAAIC,IAAI,CAAC;MACvB,IAAI,CAAChF,gBAAgB,CAACG,aAAY,GAAI4E,KAAK,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MACtE,IAAI,CAAClF,gBAAgB,CAACI,aAAY,GAAI,OAAM;IAC9C,CAAC;IAED+E,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAAC3E,eAAe,CAACjB,IAAG,GAAI,IAAG;;MAE/B;MACA,IAAI,CAACgC,KAAK,CAAC8B,OAAO,CAACZ,IAAG,IAAK;QACzB,IAAI,CAACjC,eAAe,CAACP,aAAa,CAACwC,IAAI,CAACX,EAAE,IAAIW,IAAI,CAACC,QAAO;MAC5D,CAAC;IACH,CAAC;IAED0C,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAACzE,mBAAmB,CAACpB,IAAG,GAAI,IAAG;;MAEnC;MACA,IAAI,CAACgC,KAAK,CAAC8B,OAAO,CAACZ,IAAG,IAAK;QACzB,IAAI,CAAC9B,mBAAmB,CAACV,aAAa,CAACwC,IAAI,CAACX,EAAE,IAAIW,IAAI,CAACC,QAAO;MAChE,CAAC;IACH,CAAC;IAEDmC,gBAAgBA,CAAClD,MAAM,EAAE;MACvB,MAAM0D,KAAI,GAAI,0EAAyE;MACvF,IAAIrE,QAAO,GAAI,EAAC;;MAEhB;MACA,MAAMsE,SAAQ,GAAI3D,MAAK,IAAK,IAAI,CAACF,QAAQ,CAACG,IAAI,CAACC,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,IAAI,CAACxC,mBAAmB,CAACG,QAAQ;MAC9F,MAAM8F,SAAQ,GAAID,SAAQ,GAAIA,SAAS,CAACC,SAAQ,GAAI,EAAC;;MAErD;MACA,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAID,SAAS,EAAEC,CAAC,EAAE,EAAE;QAClCxE,QAAO,IAAKqE,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIP,KAAK,CAAC9C,MAAM,CAAC;MACnE;MAEA,IAAI,IAAI,CAACjD,mBAAkB,IAAK,CAACqC,MAAM,EAAE;QACvC,IAAI,CAACrC,mBAAmB,CAACI,iBAAgB,GAAIsB,QAAO;MACtD;MAEA,OAAOA,QAAO;IAChB,CAAC;IAED,MAAM6E,cAAcA,CAAA,EAAG;MACrB,IAAI,IAAI,CAACvG,mBAAmB,CAACE,MAAK,KAAM,QAAO,IAAK,IAAI,CAACyC,gBAAgB,EAAE;QACzE;MACF;MAEA,IAAI,CAAC1D,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAMyC,QAAO,GAAI,IAAI,CAAC1B,mBAAmB,CAACE,MAAK,KAAM,MAAK,GACtD,IAAI,CAACF,mBAAmB,CAACI,iBAAgB,GACzC,IAAI,CAACJ,mBAAmB,CAACK,WAAU;QAEvC,MAAM,IAAI,CAAC6E,MAAM,CAACsB,QAAQ,CAAC,oBAAoB,EAAE;UAC/C7B,MAAM,EAAE,IAAI,CAACzF,WAAW,CAACsD,EAAE;UAC3BiE,SAAS,EAAE,IAAI,CAACtH,cAAc,CAACqD,EAAE;UACjCd,QAAQ,EAAEA,QAAQ;UAClBvB,QAAQ,EAAE,IAAI,CAACH,mBAAmB,CAACG;QACrC,CAAC;QAED,IAAI,CAACH,mBAAmB,CAACC,IAAG,GAAI,KAAI;;QAEpC;QACAyG,KAAK,CAAC,WAAW,IAAI,CAACxH,WAAW,CAACP,IAAI,MAAM,IAAI,CAACQ,cAAc,CAACsC,QAAQ,QAAQ;MAClF,EAAE,OAAOkF,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAACzH,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED,MAAM4H,oBAAoBA,CAAA,EAAG;MAC3B,MAAMC,eAAc,GAAIjE,MAAM,CAACkE,OAAO,CAAC,IAAI,CAACrG,gBAAgB,CAACC,aAAa,EACvEoC,MAAM,CAAC,CAAC,CAACiE,CAAC,EAAE5D,QAAQ,CAAC,KAAKA,QAAQ,EAClC6D,GAAG,CAAC,CAAC,CAACzE,EAAE,CAAC,KAAKgC,QAAQ,CAAChC,EAAE,CAAC;MAE7B,IAAIsE,eAAe,CAAC7D,MAAK,KAAM,CAAC,EAAE;QAChCyD,KAAK,CAAC,YAAY;QAClB;MACF;MAEA,IAAI,IAAI,CAAChG,gBAAgB,CAACE,aAAY,KAAM,WAAW,EAAE;QACvD;QACA8F,KAAK,CAAC,cAAc;QACpB,IAAI,CAAChG,gBAAgB,CAACT,IAAG,GAAI,KAAI;QACjC;MACF;MAEA,IAAI,CAAChB,UAAS,GAAI,IAAG;MAErB,IAAI;QACF;QACA,MAAMoD,MAAK,GAAI,IAAI,CAACF,QAAQ,CAACG,IAAI,CAACC,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,IAAI,CAAC9B,gBAAgB,CAACP,QAAQ;;QAE9E;QACA,KAAK,MAAMwE,MAAK,IAAKmC,eAAe,EAAE;UACpC,MAAM3D,IAAG,GAAI,IAAI,CAAClB,KAAK,CAACK,IAAI,CAAC4E,CAAA,IAAKA,CAAC,CAAC1E,EAAC,KAAMmC,MAAM;UACjD,IAAIxB,IAAI,EAAE;YACR,KAAK,MAAMa,OAAM,IAAKb,IAAI,CAACW,QAAQ,EAAE;cACnC,MAAMzD,WAAU,GAAI,IAAI,CAACkF,gBAAgB,CAAClD,MAAM;cAChD,MAAM,IAAI,CAAC6C,MAAM,CAACsB,QAAQ,CAAC,oBAAoB,EAAE;gBAC/C7B,MAAM,EAAEA,MAAM;gBACd8B,SAAS,EAAEzC,OAAO,CAACxB,EAAE;gBACrBd,QAAQ,EAAErB,WAAW;gBACrBF,QAAQ,EAAEkC,MAAM,CAACG;cACnB,CAAC;YACH;UACF;QACF;QAEA,IAAI,CAAC9B,gBAAgB,CAACT,IAAG,GAAI,KAAI;;QAEjC;QACAyG,KAAK,CAAC,QAAQI,eAAe,CAAC7D,MAAM,gBAAgB;MACtD,EAAE,OAAO0D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK;QAC/BD,KAAK,CAAC,eAAe;MACvB,UAAU;QACR,IAAI,CAACzH,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED,MAAMkI,gBAAgBA,CAAA,EAAG;MACvB,MAAML,eAAc,GAAIjE,MAAM,CAACkE,OAAO,CAAC,IAAI,CAAC7F,eAAe,CAACP,aAAa,EACtEoC,MAAM,CAAC,CAAC,CAACiE,CAAC,EAAE5D,QAAQ,CAAC,KAAKA,QAAQ,EAClC6D,GAAG,CAAC,CAAC,CAACzE,EAAE,CAAC,KAAKgC,QAAQ,CAAChC,EAAE,CAAC;MAE7B,IAAIsE,eAAe,CAAC7D,MAAK,KAAM,CAAC,EAAE;QAChCyD,KAAK,CAAC,YAAY;QAClB;MACF;MAEA,IAAI,CAACzH,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAM,IAAI,CAACiG,MAAM,CAACsB,QAAQ,CAAC,oBAAoB,EAAE;UAC/CrG,QAAQ,EAAE,IAAI,CAACe,eAAe,CAACf,QAAQ;UACvCiH,OAAO,EAAEN;QACX,CAAC;QAED,IAAI,CAAC5F,eAAe,CAACjB,IAAG,GAAI,KAAI;;QAEhC;QACAyG,KAAK,CAAC,QAAQI,eAAe,CAAC7D,MAAM,aAAa;MACnD,EAAE,OAAO0D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAACzH,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED,MAAMoI,cAAcA,CAAA,EAAG;MACrB,MAAMP,eAAc,GAAIjE,MAAM,CAACkE,OAAO,CAAC,IAAI,CAAC1F,mBAAmB,CAACV,aAAa,EAC1EoC,MAAM,CAAC,CAAC,CAACiE,CAAC,EAAE5D,QAAQ,CAAC,KAAKA,QAAQ,EAClC6D,GAAG,CAAC,CAAC,CAACzE,EAAE,CAAC,KAAKgC,QAAQ,CAAChC,EAAE,CAAC;MAE7B,IAAIsE,eAAe,CAAC7D,MAAK,KAAM,CAAC,EAAE;QAChCyD,KAAK,CAAC,YAAY;QAClB;MACF;MAEA,IAAI,CAACzH,UAAS,GAAI,IAAG;MAErB,IAAI;QACF;QACA,MAAMoD,MAAK,GAAI,IAAI,CAACF,QAAQ,CAACG,IAAI,CAACC,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,IAAI,CAACnB,mBAAmB,CAAClB,QAAQ;;QAEjF;QACA,KAAK,MAAMwE,MAAK,IAAKmC,eAAe,EAAE;UACpC,MAAM3D,IAAG,GAAI,IAAI,CAAClB,KAAK,CAACK,IAAI,CAAC4E,CAAA,IAAKA,CAAC,CAAC1E,EAAC,KAAMmC,MAAM;UACjD,IAAIxB,IAAI,EAAE;YACR,KAAK,MAAMa,OAAM,IAAKb,IAAI,CAACW,QAAQ,EAAE;cACnC,MAAMzD,WAAU,GAAI,IAAI,CAACkF,gBAAgB,CAAClD,MAAM;cAChD,MAAM,IAAI,CAAC6C,MAAM,CAACsB,QAAQ,CAAC,oBAAoB,EAAE;gBAC/C7B,MAAM,EAAEA,MAAM;gBACd8B,SAAS,EAAEzC,OAAO,CAACxB,EAAE;gBACrBd,QAAQ,EAAErB,WAAW;gBACrBF,QAAQ,EAAEkC,MAAM,CAACG;cACnB,CAAC;YACH;UACF;QACF;QAEA,IAAI,CAACnB,mBAAmB,CAACpB,IAAG,GAAI,KAAI;;QAEpC;QACAyG,KAAK,CAAC,QAAQI,eAAe,CAAC7D,MAAM,oBAAoB;MAC1D,EAAE,OAAO0D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAACzH,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAEDqI,wBAAwBA,CAAC3C,MAAM,EAAE;MAC/B,IAAI,CAACvF,kBAAkB,CAACuF,MAAM,IAAI,CAAC,IAAI,CAACvF,kBAAkB,CAACuF,MAAM;IACnE,CAAC;IAEDL,iBAAiBA,CAACN,OAAO,EAAE;MACzB,IAAI,CAACA,OAAO,CAACuD,kBAAkB,EAAE,OAAO;QAAE3D,MAAM,EAAE,QAAQ;QAAE4D,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAI;;MAElF;MACA,MAAMC,UAAS,GAAI,IAAIhC,IAAI,CAAC1B,OAAO,CAACuD,kBAAkB;MACtD,MAAMI,GAAE,GAAI,IAAIjC,IAAI,CAAC;;MAErB;MACA,IAAIgC,UAAS,GAAIC,GAAG,EAAE;QACpB,OAAO;UACL/D,MAAM,EAAE,SAAS;UACjB4D,IAAI,EAAE,CAAC;UACPC,IAAI,EAAE;QACR;MACF;;MAEA;MACA,MAAMG,QAAO,GAAIF,UAAS,GAAIC,GAAE;MAChC,MAAME,QAAO,GAAIzB,IAAI,CAACC,KAAK,CAACuB,QAAO,IAAK,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,EAAE,CAAC;MAC5D,MAAME,SAAQ,GAAI1B,IAAI,CAACC,KAAK,CAAEuB,QAAO,IAAK,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,EAAE,CAAC,IAAK,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC;;MAElF;MACA,IAAIhE,MAAK,GAAI,QAAO;MACpB,IAAIiE,QAAO,GAAI,CAAC,EAAE;QAChBjE,MAAK,GAAI,QAAO,EAAG;MACrB,OAAO,IAAIiE,QAAO,GAAI,EAAE,EAAE;QACxBjE,MAAK,GAAI,SAAQ,EAAE;MACrB;;MAEA;MACA,IAAI6D,IAAG,GAAI,EAAC;MACZ,IAAII,QAAO,GAAI,CAAC,EAAE;QAChBJ,IAAG,IAAK,GAAGI,QAAQ,GAAE;MACvB;MACA,IAAIC,SAAQ,GAAI,KAAKD,QAAO,KAAM,CAAC,EAAE;QACnCJ,IAAG,IAAK,GAAGK,SAAS,IAAG;MACzB;MAEA,OAAO;QAAElE,MAAM;QAAE4D,IAAI,EAAEK,QAAQ;QAAEJ,IAAI,EAAE,KAAKA,IAAI;MAAG;IACrD,CAAC;IAEDM,mBAAmBA,CAAC5E,IAAI,EAAE;MACxB,IAAI,CAACjE,WAAU,GAAIiE,IAAG;MACtB,IAAI,CAAC3B,eAAe,CAACvB,IAAG,GAAI,IAAG;IACjC,CAAC;IAED,MAAM+H,UAAUA,CAAA,EAAG;MACjB,IAAI,CAAC,IAAI,CAACxG,eAAe,CAACC,QAAO,IAAK,CAAC,IAAI,CAACD,eAAe,CAACE,QAAQ,EAAE;QACpEgF,KAAK,CAAC,aAAa;QACnB;MACF;MAEA,IAAI,CAACzH,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAM,IAAI,CAACiG,MAAM,CAACsB,QAAQ,CAAC,gBAAgB,EAAE;UAC3C7B,MAAM,EAAE,IAAI,CAACzF,WAAW,CAACsD,EAAE;UAC3Bf,QAAQ,EAAE,IAAI,CAACD,eAAe,CAACC,QAAQ;UACvCC,QAAQ,EAAE,IAAI,CAACF,eAAe,CAACE,QAAQ;UACvCvB,QAAQ,EAAE,IAAI,CAACqB,eAAe,CAACrB,QAAQ;UACvCwB,SAAS,EAAE,IAAI,CAACH,eAAe,CAACG;QAClC,CAAC;QAED,IAAI,CAACH,eAAe,CAACvB,IAAG,GAAI,KAAI;;QAEhC;QACAyG,KAAK,CAAC,UAAU,IAAI,CAACxH,WAAW,CAACP,IAAI,QAAQ;MAC/C,EAAE,OAAOgI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAACzH,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAEDgJ,6BAA6BA,CAAA,EAAG;MAC9B,IAAI,CAACzG,eAAe,CAACE,QAAO,GAAI,IAAI,CAAC6D,gBAAgB,CAAC;IACxD,CAAC;IAED;IACA2C,YAAYA,CAAClE,OAAO,EAAE;MACpB;MACA,MAAMmE,SAAQ,GAAIC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MACjDF,SAAS,CAAClD,KAAI,GAAIjB,OAAO,CAACtC,QAAQ;MAClC0G,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,SAAS,CAAC;MACpCA,SAAS,CAACK,MAAM,CAAC,CAAC;MAClBJ,QAAQ,CAACK,WAAW,CAAC,MAAM,CAAC;MAC5BL,QAAQ,CAACE,IAAI,CAACI,WAAW,CAACP,SAAS,CAAC;;MAEpC;MACAzB,KAAK,CAAC,OAAO1C,OAAO,CAACb,IAAI,CAACxE,IAAI,MAAMqF,OAAO,CAACvC,QAAQ,YAAY,CAAC;IACnE,CAAC;IAED;IACAkH,oBAAoBA,CAAA,EAAG;MACrB;MACA,MAAMC,OAAM,GAAI,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;MACzE,MAAMC,OAAM,GAAI,CAACD,OAAO,CAAC;;MAEzB;MACA,IAAI,CAAC1E,gBAAgB,CAACH,OAAO,CAACC,OAAM,IAAK;QACvC,MAAM8E,GAAE,GAAI,CACV9E,OAAO,CAACb,IAAI,CAACxE,IAAI,EACjBqF,OAAO,CAACb,IAAI,CAACO,EAAE,EACfM,OAAO,CAACvC,QAAQ,EAChBuC,OAAO,CAACtC,QAAQ,EAChBsC,OAAO,CAAC+E,kBAAiB,IAAK,GAAG,EACjC/E,OAAO,CAACuD,kBAAiB,IAAK,GAAG,EACjCvD,OAAO,CAACb,IAAI,CAACS,MAAM,EACnB,IAAI,CAACxB,aAAa,CAAC4B,OAAO,CAAC7D,QAAQ,EACpC;QACD0I,OAAO,CAAC5E,IAAI,CAAC6E,GAAG,CAAC;MACnB,CAAC,CAAC;;MAEF;MACA,MAAME,UAAS,GAAIH,OAAO,CAAC5B,GAAG,CAAC6B,GAAE,IAAKA,GAAG,CAAC7B,GAAG,CAACgC,IAAG,IAC/C,IAAIC,MAAM,CAACD,IAAI,CAAC,CAACE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;;MAEhE;MACA,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAAC,CAACN,UAAU,CAAC,EAAE;QAAEO,IAAI,EAAE;MAA0B,CAAC,CAAC;MACxE,MAAMC,GAAE,GAAIC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MACrC,MAAMM,IAAG,GAAIvB,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCsB,IAAI,CAACC,YAAY,CAAC,MAAM,EAAEJ,GAAG,CAAC;MAC9BG,IAAI,CAACC,YAAY,CAAC,UAAU,EAAE,QAAQ,IAAIlE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACkE,KAAK,CAAC,CAAC,EAAC,EAAE,CAAC,MAAM,CAAC;MACjFF,IAAI,CAACG,KAAK,CAACC,UAAS,GAAI,QAAQ;MAChC3B,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACoB,IAAI,CAAC;MAC/BA,IAAI,CAACK,KAAK,CAAC,CAAC;MACZ5B,QAAQ,CAACE,IAAI,CAACI,WAAW,CAACiB,IAAI,CAAC;IACjC,CAAC;IAEDM,+BAA+BA,CAAA,EAAG;MAChC,IAAI,CAACrI,oBAAoB,CAACF,QAAO,GAAI,IAAI,CAAC6D,gBAAgB,CAAC;IAC7D,CAAC;IAED,MAAM2E,gBAAgBA,CAAA,EAAG;MACvB,IAAI,CAAC,IAAI,CAACtI,oBAAoB,CAACH,QAAQ,EAAE;QACvCiF,KAAK,CAAC,UAAU;QAChB;MACF;MAEA,MAAMI,eAAc,GAAIjE,MAAM,CAACkE,OAAO,CAAC,IAAI,CAACnF,oBAAoB,CAACjB,aAAa,EAC3EoC,MAAM,CAAC,CAAC,CAACiE,CAAC,EAAE5D,QAAQ,CAAC,KAAKA,QAAQ,EAClC6D,GAAG,CAAC,CAAC,CAACzE,EAAE,CAAC,KAAKgC,QAAQ,CAAChC,EAAE,CAAC;MAE7B,IAAIsE,eAAe,CAAC7D,MAAK,KAAM,CAAC,EAAE;QAChCyD,KAAK,CAAC,YAAY;QAClB;MACF;MAEA,IAAI,CAACzH,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAM,IAAI,CAACiG,MAAM,CAACsB,QAAQ,CAAC,kBAAkB,EAAE;UAC7CY,OAAO,EAAEN,eAAe;UACxBrF,QAAQ,EAAE,IAAI,CAACG,oBAAoB,CAACH,QAAQ;UAC5CC,QAAQ,EAAE,IAAI,CAACE,oBAAoB,CAACG,eAAc,GAAI,IAAI,CAACH,oBAAoB,CAACF,QAAO,GAAI,IAAI;UAC/FG,IAAI,EAAE,IAAI,CAACD,oBAAoB,CAACC,IAAI;UACpCF,SAAS,EAAE,IAAI,CAACC,oBAAoB,CAACE,YAAY;UACjD3B,QAAQ,EAAE,IAAI,CAACyB,oBAAoB,CAACzB;QACtC,CAAC;QAED,IAAI,CAACyB,oBAAoB,CAAC3B,IAAG,GAAI,KAAI;;QAErC;QACAyG,KAAK,CAAC,QAAQI,eAAe,CAAC7D,MAAM,WAAW;MACjD,EAAE,OAAO0D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK;QAC/BD,KAAK,CAAC,eAAe;MACvB,UAAU;QACR,IAAI,CAACzH,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAEDkL,wBAAwBA,CAAA,EAAG;MACzB,IAAI,CAACvI,oBAAoB,CAAC3B,IAAG,GAAI,IAAG;MACpC,IAAI,CAAC2B,oBAAoB,CAACjB,aAAY,GAAI,CAAC;;MAE3C;MACA,IAAI,CAACsB,KAAK,CAAC8B,OAAO,CAACZ,IAAG,IAAK;QACzB,IAAI,CAACvB,oBAAoB,CAACjB,aAAa,CAACwC,IAAI,CAACX,EAAE,IAAIW,IAAI,CAACC,QAAO;MACjE,CAAC;;MAED;MACA,IAAI,CAAC6G,+BAA+B,CAAC;IACvC,CAAC;IAEDG,iBAAiBA,CAAA,EAAG;MAClB;MACA,MAAMC,WAAU,GAAIC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC7C,IAAI,CAACF,WAAW,EAAE;QAChB3D,KAAK,CAAC,kBAAkB,CAAC;QACzB;MACF;MAEA,MAAM8D,GAAE,GAAIH,WAAW,CAACjC,QAAQ;;MAEhC;MACAoC,GAAG,CAACD,IAAI,CAAC,CAAC;MACVC,GAAG,CAACC,KAAK,CAAC,iBAAiB,CAAC;MAC5BD,GAAG,CAACC,KAAK,CAAC,QAAQ,CAAC;MACnBD,GAAG,CAACC,KAAK,CAAC,QAAQ,CAAC;MACnBD,GAAG,CAACC,KAAK,CAAC,yBAAyB,CAAC;;MAEpC;MACAD,GAAG,CAACC,KAAK,CAAC,SAAS,CAAC;MACpBD,GAAG,CAACC,KAAK,CAAC,0CAA0C,CAAC;MACrDD,GAAG,CAACC,KAAK,CAAC,wEAAwE,CAAC;MACnFD,GAAG,CAACC,KAAK,CAAC,oEAAoE,CAAC;MAC/ED,GAAG,CAACC,KAAK,CAAC,mCAAmC,CAAC;MAC9CD,GAAG,CAACC,KAAK,CAAC,4DAA4D,CAAC;MACvED,GAAG,CAACC,KAAK,CAAC,0EAA0E,CAAC;MACrFD,GAAG,CAACC,KAAK,CAAC,kCAAkC,CAAC;MAC7CD,GAAG,CAACC,KAAK,CAAC,oCAAoC,CAAC;MAC/CD,GAAG,CAACC,KAAK,CAAC,+BAA+B,CAAC;MAC1CD,GAAG,CAACC,KAAK,CAAC,wBAAwB,CAAC;MACnCD,GAAG,CAACC,KAAK,CAAC,UAAU,CAAC;MACrBD,GAAG,CAACC,KAAK,CAAC,SAAS,CAAC;MACpBD,GAAG,CAACC,KAAK,CAAC,QAAQ,CAAC;;MAEnB;MACAD,GAAG,CAACC,KAAK,CAAC,8BAA8B,CAAC;MACzCD,GAAG,CAACC,KAAK,CAAC,mBAAmB,CAAC;MAC9BD,GAAG,CAACC,KAAK,CAAC,WAAU,GAAI,IAAI/E,IAAI,CAAC,CAAC,CAACgF,cAAc,CAAC,OAAO,IAAI,MAAM,CAAC;MACpEF,GAAG,CAACC,KAAK,CAAC,QAAQ,CAAC;;MAEnB;MACAD,GAAG,CAACC,KAAK,CAAC,SAAS,CAAC;;MAEpB;MACAD,GAAG,CAACC,KAAK,CAAC,aAAa,CAAC;MACxB,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC1G,OAAO,CAAC4G,MAAK,IAAK;QACxEH,GAAG,CAACC,KAAK,CAAC,MAAK,GAAIE,MAAK,GAAI,OAAO,CAAC;MACtC,CAAC,CAAC;MACFH,GAAG,CAACC,KAAK,CAAC,eAAe,CAAC;;MAE1B;MACAD,GAAG,CAACC,KAAK,CAAC,SAAS,CAAC;MACpB,IAAI,CAACvG,gBAAgB,CAACH,OAAO,CAACC,OAAM,IAAK;QACvC,MAAM4G,WAAU,GAAI,SAAQ,GAAI5G,OAAO,CAACb,IAAI,CAACS,MAAM;QACnD4G,GAAG,CAACC,KAAK,CAAC,MAAM,CAAC;QACjBD,GAAG,CAACC,KAAK,CAAC,MAAK,GAAIzG,OAAO,CAACb,IAAI,CAACxE,IAAG,GAAI,OAAO,CAAC;QAC/C6L,GAAG,CAACC,KAAK,CAAC,MAAK,GAAIzG,OAAO,CAACb,IAAI,CAACO,EAAC,GAAI,OAAO,CAAC;QAC7C8G,GAAG,CAACC,KAAK,CAAC,MAAK,GAAIzG,OAAO,CAACvC,QAAO,IAAKuC,OAAO,CAACrC,SAAQ,GAAI,OAAM,GAAI,EAAE,IAAI,OAAO,CAAC;QACnF6I,GAAG,CAACC,KAAK,CAAC,MAAK,IAAKzG,OAAO,CAAC+E,kBAAiB,IAAK,GAAG,IAAI,OAAO,CAAC;QACjEyB,GAAG,CAACC,KAAK,CAAC,MAAK,IAAKzG,OAAO,CAACuD,kBAAiB,IAAK,GAAG,IAAI,OAAO,CAAC;QACjEiD,GAAG,CAACC,KAAK,CAAC,cAAa,GAAIG,WAAU,GAAI,KAAI,GAAI5G,OAAO,CAACb,IAAI,CAACS,MAAK,GAAI,OAAO,CAAC;QAC/E4G,GAAG,CAACC,KAAK,CAAC,MAAK,GAAI,IAAI,CAACrI,aAAa,CAAC4B,OAAO,CAAC7D,QAAQ,IAAI,OAAO,CAAC;QAClEqK,GAAG,CAACC,KAAK,CAAC,OAAO,CAAC;MACpB,CAAC,CAAC;MACFD,GAAG,CAACC,KAAK,CAAC,UAAU,CAAC;MACrBD,GAAG,CAACC,KAAK,CAAC,UAAU,CAAC;;MAErB;MACAD,GAAG,CAACC,KAAK,CAAC,8BAA8B,CAAC;MACzCD,GAAG,CAACC,KAAK,CAAC,2BAA2B,CAAC;MACtCD,GAAG,CAACC,KAAK,CAAC,QAAQ,CAAC;;MAEnB;MACAD,GAAG,CAACC,KAAK,CAAC,GAAE,GAAI,QAAO,GAAI,GAAG,CAAC;MAC/BD,GAAG,CAACC,KAAK,CAAC,gDAAgD,CAAC;MAC3DD,GAAG,CAACC,KAAK,CAAC,IAAG,GAAI,QAAO,GAAI,GAAG,CAAC;MAEhCD,GAAG,CAACC,KAAK,CAAC,SAAS,CAAC;MACpBD,GAAG,CAACC,KAAK,CAAC,SAAS,CAAC;MACpBD,GAAG,CAACK,KAAK,CAAC,CAAC;IACb,CAAC;IAED;IACAC,mBAAmBA,CAAC9G,OAAO,EAAE;MAC3B,IAAI,CAACA,OAAO,CAAC7D,QAAQ,EAAE,OAAO;QAAE4K,SAAS,EAAE,IAAI;QAAEtD,IAAI,EAAE;MAAM,CAAC;MAE9D,MAAM/F,QAAO,GAAIsC,OAAO,CAACtC,QAAQ;MACjC,MAAMW,MAAK,GAAI,IAAI,CAACF,QAAQ,CAACG,IAAI,CAACC,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAMwB,OAAO,CAAC7D,QAAQ,CAAC;MAEjE,IAAI,CAACkC,MAAM,EAAE,OAAO;QAAE0I,SAAS,EAAE,IAAI;QAAEtD,IAAI,EAAE;MAAM,CAAC;MAEpD,MAAMuD,MAAK,GAAI,EAAE;;MAEjB;MACA,IAAItJ,QAAQ,CAACuB,MAAK,GAAIZ,MAAM,CAAC4D,SAAS,EAAE;QACtC+E,MAAM,CAAC/G,IAAI,CAAC,UAAU5B,MAAM,CAAC4D,SAAS,IAAI,CAAC;MAC7C;;MAEA;MACA,IAAI5D,MAAM,CAAC4I,gBAAe,IAAK,CAAC,OAAO,CAACC,IAAI,CAACxJ,QAAQ,CAAC,EAAE;QACtDsJ,MAAM,CAAC/G,IAAI,CAAC,QAAQ,CAAC;MACvB;;MAEA;MACA,IAAI5B,MAAM,CAAC8I,gBAAe,IAAK,CAAC,OAAO,CAACD,IAAI,CAACxJ,QAAQ,CAAC,EAAE;QACtDsJ,MAAM,CAAC/G,IAAI,CAAC,QAAQ,CAAC;MACvB;;MAEA;MACA,IAAI5B,MAAM,CAAC+I,cAAa,IAAK,CAAC,OAAO,CAACF,IAAI,CAACxJ,QAAQ,CAAC,EAAE;QACpDsJ,MAAM,CAAC/G,IAAI,CAAC,MAAM,CAAC;MACrB;;MAEA;MACA,IAAI5B,MAAM,CAACgJ,cAAa,IAAK,CAAC,cAAc,CAACH,IAAI,CAACxJ,QAAQ,CAAC,EAAE;QAC3DsJ,MAAM,CAAC/G,IAAI,CAAC,QAAQ,CAAC;MACvB;;MAEA;MACA,IAAI5B,MAAM,CAACiJ,cAAa,IAAK5J,QAAQ,CAAC8B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACO,OAAO,CAACvC,QAAQ,CAAC+B,WAAW,CAAC,CAAC,CAAC,EAAE;QAC5FwH,MAAM,CAAC/G,IAAI,CAAC,SAAS,CAAC;MACxB;MAEA,OAAO;QACL8G,SAAS,EAAEC,MAAM,CAAC/H,MAAK,KAAM,CAAC;QAC9BwE,IAAI,EAAEuD,MAAM,CAAC/H,MAAK,KAAM,IAAI,MAAK,GAAI,QAAQ+H,MAAM,CAAC5B,IAAI,CAAC,IAAI,CAAC,EAAE;QAChEmC,OAAO,EAAEP;MACX,CAAC;IACH;EACF,CAAC;EACDQ,OAAOA,CAAA,EAAG;IACR;IACA,MAAM/F,KAAI,GAAI,IAAIC,IAAI,CAAC;IACvB,IAAI,CAAChF,gBAAgB,CAACG,aAAY,GAAI4E,KAAK,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IACtE,IAAI,CAAClF,gBAAgB,CAACI,aAAY,GAAI,OAAM;EAC9C;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}