{"name": "password-management-system", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/vue-fontawesome": "^3.0.3", "core-js": "^3.30.1", "mitt": "^3.0.1", "pinia": "^3.0.2", "tailwindcss": "^3.3.2", "vue": "^3.2.47", "vue-router": "^4.1.6", "vuex": "^4.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/compiler-sfc": "^3.2.47", "autoprefixer": "^10.4.14", "babel-eslint": "^10.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^9.11.0", "postcss": "^8.4.23"}}