import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import './assets/styles/index.css'
import { library } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import {
    faShieldAlt, faServer, faKey, faClock, faCheck,
    faCog, faCheckSquare, faCalendarAlt, faTimes,
    faHistory, faPlus, faSyncAlt, faExclamationTriangle,
    faLock, faCalendarCheck, faEye, faEyeSlash, faExclamationCircle,
    faTable, faThLarge, faSearch, faEdit, faTrashAlt,
    // 新增的图标
    faBell, faUser, faChevronDown, faSignOutAlt, faSun, faMoon,
    faTachometerAlt, faUsers, faMagic, faCopy, faCheckCircle,
    faInfoCircle, faDownload, faLightbulb,
    faTasks, faPlay, faPause, faTrash
} from '@fortawesome/free-solid-svg-icons'

// 创建全局事件总线
import mitt from 'mitt'
const eventBus = mitt()

library.add(
    faShieldAlt, faServer, faKey, faClock, faCheck,
    faCog, faCheckSquare, faCalendarAlt, faTimes,
    faHistory, faPlus, faSyncAlt, faExclamationTriangle,
    faLock, faCalendarCheck, faEye, faEyeSlash, faExclamationCircle,
    faTable, faThLarge, faSearch, faEdit, faTrashAlt,
    // 新增的图标
    faBell, faUser, faChevronDown, faSignOutAlt, faSun, faMoon,
    faTachometerAlt, faUsers, faMagic, faCopy, faCheckCircle,
    faInfoCircle, faDownload, faLightbulb,
    faTasks, faPlay, faPause, faTrash
)

const app = createApp(App)
app.use(router)
app.use(store)
app.component('font-awesome-icon', FontAwesomeIcon)

// 将事件总线添加到全局属性
app.config.globalProperties.$eventBus = eventBus

app.mount('#app')