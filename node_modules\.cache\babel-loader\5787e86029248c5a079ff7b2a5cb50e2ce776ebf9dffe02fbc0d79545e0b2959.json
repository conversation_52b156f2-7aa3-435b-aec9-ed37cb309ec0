{"ast": null, "code": "import { mapState, mapGetters } from 'vuex';\nimport BaseModal from '@/components/BaseModal.vue';\nimport StatusBadge from '@/components/StatusBadge.vue';\nimport CustomCheckbox from '@/components/CustomCheckbox.vue';\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue';\nexport default {\n  name: 'HostManagement',\n  components: {\n    BaseModal,\n    StatusBadge,\n    CustomCheckbox,\n    PasswordStrengthMeter\n  },\n  data() {\n    return {\n      selectAll: false,\n      selectAllBatch: false,\n      processing: false,\n      currentHost: {},\n      currentAccount: {},\n      passwordVisibility: {\n        generated: false,\n        new: false,\n        confirm: false,\n        newAccount: false,\n        batchPassword: false\n      },\n      viewMode: 'table',\n      filterText: '',\n      accountFilterText: '',\n      statusFilter: 'all',\n      expiryFilter: 'all',\n      // 修改密码弹窗\n      changePasswordModal: {\n        show: false,\n        method: 'auto',\n        policyId: 1,\n        generatedPassword: 'aX7#9pQr$2Lm',\n        newPassword: '',\n        confirmPassword: '',\n        executeImmediately: true,\n        saveHistory: false,\n        logAudit: true\n      },\n      // 批量更新密码弹窗\n      batchUpdateModal: {\n        show: false,\n        selectedHosts: {},\n        policyId: 1,\n        executionTime: 'immediate',\n        scheduledDate: '',\n        scheduledTime: '',\n        ignoreErrors: true,\n        detailedLog: true,\n        sendNotification: false\n      },\n      // 批量应用策略弹窗\n      batchApplyModal: {\n        show: false,\n        selectedHosts: {},\n        policyId: 1,\n        updateImmediately: false,\n        applyOnNextUpdate: true\n      },\n      // 紧急重置密码弹窗\n      emergencyResetModal: {\n        show: false,\n        selectedHosts: {},\n        policyId: 3,\n        // 默认使用紧急策略\n        reason: 'security_incident',\n        description: ''\n      },\n      // 添加账号弹窗\n      addAccountModal: {\n        show: false,\n        username: '',\n        password: '',\n        isDefault: false,\n        policyId: 1\n      },\n      // 批量添加账号弹窗\n      batchAddAccountModal: {\n        show: false,\n        selectedHosts: {},\n        username: '',\n        password: '',\n        role: 'admin',\n        setAsDefault: false,\n        useSamePassword: true,\n        policyId: 1\n      }\n    };\n  },\n  computed: {\n    ...mapState({\n      hosts: state => state.hosts,\n      policies: state => state.policies\n    }),\n    ...mapGetters(['selectedHosts']),\n    passwordMismatch() {\n      return this.changePasswordModal.newPassword && this.changePasswordModal.confirmPassword && this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword;\n    },\n    selectedHostsCount() {\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length;\n    },\n    selectedHostsList() {\n      return this.hosts.filter(host => host.selected);\n    },\n    emergencyPolicies() {\n      // 返回紧急策略和高强度策略\n      return this.policies.filter(p => p.id === 3 || p.id === 1);\n    },\n    // 过滤后的主机列表\n    filteredHosts() {\n      return this.hosts.filter(host => {\n        // 文本过滤\n        const textMatch = this.filterText === '' || host.name.toLowerCase().includes(this.filterText.toLowerCase()) || host.ip.includes(this.filterText);\n\n        // 状态过滤\n        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;\n        return textMatch && statusMatch;\n      });\n    },\n    // 获取所有账号（扁平化处理）\n    getAllAccounts() {\n      // 为每个账号添加主机引用\n      const accounts = [];\n      this.filteredHosts.forEach(host => {\n        host.accounts.forEach(account => {\n          accounts.push({\n            ...account,\n            host: host\n          });\n        });\n      });\n      return accounts;\n    },\n    // 筛选后的账号\n    filteredAccounts() {\n      return this.getAllAccounts.filter(account => {\n        // 账号名称筛选\n        const accountMatch = this.accountFilterText === '' || account.username.toLowerCase().includes(this.accountFilterText.toLowerCase());\n\n        // 密码过期筛选\n        let expiryMatch = true;\n        if (this.expiryFilter !== 'all') {\n          const expiryStatus = this.isPasswordExpired(account).status;\n          if (this.expiryFilter === 'expired') {\n            expiryMatch = expiryStatus === 'expired';\n          } else if (this.expiryFilter === 'expiring-soon') {\n            expiryMatch = expiryStatus === 'danger' || expiryStatus === 'warning';\n          } else if (this.expiryFilter === 'valid') {\n            expiryMatch = expiryStatus === 'normal';\n          }\n        }\n        return accountMatch && expiryMatch;\n      });\n    },\n    // 分组后的账号列表\n    groupedAccounts() {\n      // 按主机ID分组\n      const groups = {};\n      this.filteredAccounts.forEach(account => {\n        const hostId = account.host.id;\n        if (!groups[hostId]) {\n          groups[hostId] = {\n            hostId: hostId,\n            hostName: account.host.name,\n            hostIp: account.host.ip,\n            host: account.host,\n            accounts: []\n          };\n        }\n        groups[hostId].accounts.push(account);\n      });\n\n      // 转换为数组\n      return Object.values(groups);\n    },\n    selectedBatchAddHostsCount() {\n      return Object.values(this.batchAddAccountModal.selectedHosts).filter(Boolean).length;\n    }\n  },\n  methods: {\n    toggleSelectAll(value) {\n      this.$store.commit('selectAllHosts', value);\n    },\n    toggleSelectAllBatch(value) {\n      this.hosts.forEach(host => {\n        this.batchAddAccountModal.selectedHosts[host.id] = value;\n      });\n    },\n    openChangePasswordModal(host, account) {\n      this.currentHost = host;\n      this.currentAccount = account;\n      this.changePasswordModal.show = true;\n      this.changePasswordModal.generatedPassword = this.generatePassword();\n    },\n    openBatchUpdateModal() {\n      this.batchUpdateModal.show = true;\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected;\n      });\n\n      // 设置默认值\n      const today = new Date();\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0];\n      this.batchUpdateModal.scheduledTime = '03:00';\n    },\n    openBatchApplyModal() {\n      this.batchApplyModal.show = true;\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.batchApplyModal.selectedHosts[host.id] = host.selected;\n      });\n    },\n    showEmergencyReset() {\n      this.emergencyResetModal.show = true;\n\n      // 初始化选中状态\n      this.hosts.forEach(host => {\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected;\n      });\n    },\n    generatePassword(policy) {\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';\n      let password = '';\n\n      // 获取所选策略的最小长度\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId);\n      const minLength = policyObj ? policyObj.minLength : 12;\n\n      // 生成随机密码\n      for (let i = 0; i < minLength; i++) {\n        password += chars.charAt(Math.floor(Math.random() * chars.length));\n      }\n      if (this.changePasswordModal && !policy) {\n        this.changePasswordModal.generatedPassword = password;\n      }\n      return password;\n    },\n    async updatePassword() {\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\n        return;\n      }\n      this.processing = true;\n      try {\n        const password = this.changePasswordModal.method === 'auto' ? this.changePasswordModal.generatedPassword : this.changePasswordModal.newPassword;\n        await this.$store.dispatch('updateHostPassword', {\n          hostId: this.currentHost.id,\n          accountId: this.currentAccount.id,\n          password: password,\n          policyId: this.changePasswordModal.policyId\n        });\n        this.changePasswordModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功更新主机 ${this.currentHost.name} 的 ${this.currentAccount.username} 账号密码！`);\n      } catch (error) {\n        console.error('更新密码失败', error);\n        alert('更新密码失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    async batchUpdatePasswords() {\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts).filter(([_, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        alert('请至少选择一台主机！');\n        return;\n      }\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\n        // 在实际应用中，这里会创建一个定时任务\n        alert('已创建定时密码更新任务！');\n        this.batchUpdateModal.show = false;\n        return;\n      }\n      this.processing = true;\n      try {\n        // 获取所选策略\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId);\n\n        // 为每台主机的每个账号更新密码\n        for (const hostId of selectedHostIds) {\n          const host = this.hosts.find(h => h.id === hostId);\n          if (host) {\n            for (const account of host.accounts) {\n              const newPassword = this.generatePassword(policy);\n              await this.$store.dispatch('updateHostPassword', {\n                hostId: hostId,\n                accountId: account.id,\n                password: newPassword,\n                policyId: policy.id\n              });\n            }\n          }\n        }\n        this.batchUpdateModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号更新密码！`);\n      } catch (error) {\n        console.error('批量更新密码失败', error);\n        alert('批量更新密码失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    async batchApplyPolicy() {\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts).filter(([_, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        alert('请至少选择一台主机！');\n        return;\n      }\n      this.processing = true;\n      try {\n        await this.$store.dispatch('applyPolicyToHosts', {\n          policyId: this.batchApplyModal.policyId,\n          hostIds: selectedHostIds\n        });\n        this.batchApplyModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`);\n      } catch (error) {\n        console.error('应用策略失败', error);\n        alert('应用策略失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    async emergencyReset() {\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts).filter(([_, selected]) => selected).map(([id]) => parseInt(id));\n      if (selectedHostIds.length === 0) {\n        alert('请至少选择一台主机！');\n        return;\n      }\n      this.processing = true;\n      try {\n        // 获取紧急策略\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId);\n\n        // 为每台主机的每个账号更新密码\n        for (const hostId of selectedHostIds) {\n          const host = this.hosts.find(h => h.id === hostId);\n          if (host) {\n            for (const account of host.accounts) {\n              const newPassword = this.generatePassword(policy);\n              await this.$store.dispatch('updateHostPassword', {\n                hostId: hostId,\n                accountId: account.id,\n                password: newPassword,\n                policyId: policy.id\n              });\n            }\n          }\n        }\n        this.emergencyResetModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号执行紧急密码重置！`);\n      } catch (error) {\n        console.error('紧急重置失败', error);\n        alert('紧急重置失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    togglePasswordVisibility(hostId) {\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId];\n    },\n    isPasswordExpired(account) {\n      if (!account.passwordExpiryDate) return {\n        status: 'normal',\n        days: null,\n        text: '-'\n      };\n\n      // 解析过期时间\n      const expiryDate = new Date(account.passwordExpiryDate);\n      const now = new Date();\n\n      // 如果已过期\n      if (expiryDate < now) {\n        return {\n          status: 'expired',\n          days: 0,\n          text: '已过期'\n        };\n      }\n\n      // 计算剩余天数和小时数\n      const diffTime = expiryDate - now;\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));\n      const diffHours = Math.floor(diffTime % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n\n      // 根据剩余时间确定状态\n      let status = 'normal';\n      if (diffDays < 7) {\n        status = 'danger'; // 少于7天\n      } else if (diffDays < 14) {\n        status = 'warning'; // 少于14天\n      }\n\n      // 格式化显示文本\n      let text = '';\n      if (diffDays > 0) {\n        text += `${diffDays}天`;\n      }\n      if (diffHours > 0 || diffDays === 0) {\n        text += `${diffHours}小时`;\n      }\n      return {\n        status,\n        days: diffDays,\n        text: `剩余${text}`\n      };\n    },\n    openAddAccountModal(host) {\n      this.currentHost = host;\n      this.addAccountModal.show = true;\n    },\n    async addAccount() {\n      if (!this.addAccountModal.username || !this.addAccountModal.password) {\n        alert('请填写完整的账号信息！');\n        return;\n      }\n      this.processing = true;\n      try {\n        await this.$store.dispatch('addHostAccount', {\n          hostId: this.currentHost.id,\n          username: this.addAccountModal.username,\n          password: this.addAccountModal.password,\n          policyId: this.addAccountModal.policyId,\n          isDefault: this.addAccountModal.isDefault\n        });\n        this.addAccountModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为主机 ${this.currentHost.name} 添加账号！`);\n      } catch (error) {\n        console.error('添加账号失败', error);\n        alert('添加账号失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    },\n    generatePasswordForNewAccount() {\n      this.addAccountModal.password = this.generatePassword();\n    },\n    // 复制密码到剪贴板\n    copyPassword(account) {\n      // 创建一个临时输入框\n      const tempInput = document.createElement('input');\n      tempInput.value = account.password;\n      document.body.appendChild(tempInput);\n      tempInput.select();\n      document.execCommand('copy');\n      document.body.removeChild(tempInput);\n\n      // 显示提示\n      alert(`已复制 ${account.host.name} 的 ${account.username} 账号密码到剪贴板！`);\n    },\n    generatePasswordForBatchAccount() {\n      this.batchAddAccountModal.password = this.generatePassword();\n    },\n    async batchAddAccounts() {\n      if (!this.batchAddAccountModal.username) {\n        alert('请填写账号名称！');\n        return;\n      }\n      this.processing = true;\n      try {\n        await this.$store.dispatch('batchAddAccounts', {\n          hostIds: Object.keys(this.batchAddAccountModal.selectedHosts).map(id => parseInt(id)),\n          username: this.batchAddAccountModal.username,\n          password: this.batchAddAccountModal.password,\n          role: this.batchAddAccountModal.role,\n          isDefault: this.batchAddAccountModal.setAsDefault,\n          policyId: this.batchAddAccountModal.policyId\n        });\n        this.batchAddAccountModal.show = false;\n\n        // 提示用户操作成功\n        alert(`已成功为选中的 ${Object.keys(this.batchAddAccountModal.selectedHosts).length} 台主机添加账号！`);\n      } catch (error) {\n        console.error('批量添加账号失败', error);\n        alert('批量添加账号失败，请重试！');\n      } finally {\n        this.processing = false;\n      }\n    }\n  },\n  created() {\n    // 初始化日期和时间\n    const today = new Date();\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0];\n    this.batchUpdateModal.scheduledTime = '03:00';\n  }\n};", "map": {"version": 3, "names": ["mapState", "mapGetters", "BaseModal", "StatusBadge", "CustomCheckbox", "PasswordStrengthMeter", "name", "components", "data", "selectAll", "selectAllBatch", "processing", "currentHost", "currentAccount", "passwordVisibility", "generated", "new", "confirm", "newAccount", "batchPassword", "viewMode", "filterText", "accountFilterText", "statusFilter", "expiryFilter", "changePasswordModal", "show", "method", "policyId", "generatedPassword", "newPassword", "confirmPassword", "executeImmediately", "saveHistory", "logAudit", "batchUpdateModal", "selectedHosts", "executionTime", "scheduledDate", "scheduledTime", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "reason", "description", "addAccountModal", "username", "password", "isDefault", "batchAddAccountModal", "role", "setAsDefault", "useSamePassword", "computed", "hosts", "state", "policies", "passwordMismatch", "selectedHostsCount", "Object", "values", "filter", "Boolean", "length", "selectedHostsList", "host", "selected", "emergencyPolicies", "p", "id", "filteredHosts", "textMatch", "toLowerCase", "includes", "ip", "statusMatch", "status", "getAllAccounts", "accounts", "for<PERSON>ach", "account", "push", "filteredAccounts", "accountMatch", "expiryMatch", "expiry<PERSON>tatus", "isPasswordExpired", "groupedAccounts", "groups", "hostId", "hostName", "hostIp", "selectedBatchAddHostsCount", "methods", "toggleSelectAll", "value", "$store", "commit", "toggleSelectAllBatch", "openChangePasswordModal", "generatePassword", "openBatchUpdateModal", "today", "Date", "toISOString", "split", "openBatchApplyModal", "showEmergencyReset", "policy", "chars", "policyObj", "find", "<PERSON><PERSON><PERSON><PERSON>", "i", "char<PERSON>t", "Math", "floor", "random", "updatePassword", "dispatch", "accountId", "alert", "error", "console", "batchUpdatePasswords", "selectedHostIds", "entries", "_", "map", "parseInt", "h", "batchApplyPolicy", "hostIds", "emergencyReset", "togglePasswordVisibility", "passwordExpiryDate", "days", "text", "expiryDate", "now", "diffTime", "diffDays", "diffHours", "openAddAccountModal", "addAccount", "generatePasswordForNewAccount", "copyPassword", "tempInput", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "generatePasswordForBatchAccount", "batchAddAccounts", "keys", "created"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"bg-white shadow rounded-lg p-4 mb-6\">\r\n      <div class=\"flex flex-wrap items-center justify-between\">\r\n        <div class=\"flex space-x-3 mb-2 sm:mb-0\">\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\r\n            @click=\"showEmergencyReset\">\r\n            <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2\" />\r\n            <span>紧急重置</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            @click=\"openBatchUpdateModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n            <span>批量更新密码</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\r\n            @click=\"openBatchApplyModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n            <span>批量应用策略</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\r\n            @click=\"openBatchAddAccountModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'users']\" class=\"mr-2\" />\r\n            <span>批量添加账号</span>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"flex items-center space-x-4\">\r\n          <!-- 视图切换 -->\r\n          <div class=\"flex items-center border rounded-md overflow-hidden\">\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'table', 'bg-gray-100 text-gray-600': viewMode !== 'table' }\"\r\n              @click=\"viewMode = 'table'\">\r\n              <font-awesome-icon :icon=\"['fas', 'table']\" class=\"mr-1\" />\r\n              表格\r\n            </button>\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'card', 'bg-gray-100 text-gray-600': viewMode !== 'card' }\"\r\n              @click=\"viewMode = 'card'\">\r\n              <font-awesome-icon :icon=\"['fas', 'th-large']\" class=\"mr-1\" />\r\n              卡片\r\n            </button>\r\n          </div>\r\n\r\n          <!-- 筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"filterText\" placeholder=\"筛选主机...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 账号筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"accountFilterText\" placeholder=\"筛选账号...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'user']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 状态筛选 -->\r\n          <select v-model=\"statusFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有状态</option>\r\n            <option value=\"normal\">正常</option>\r\n            <option value=\"warning\">警告</option>\r\n            <option value=\"error\">错误</option>\r\n          </select>\r\n\r\n          <!-- 显示密码过期选项 -->\r\n          <select v-model=\"expiryFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有密码</option>\r\n            <option value=\"expired\">已过期</option>\r\n            <option value=\"expiring-soon\">即将过期</option>\r\n            <option value=\"valid\">有效期内</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <!-- 表格视图 -->\r\n    <div v-if=\"viewMode === 'table'\" class=\"bg-white rounded-lg shadow overflow-hidden\">\r\n      <!-- 账号计数和导出按钮 -->\r\n      <div class=\"px-4 py-3 bg-gray-50 border-b flex justify-between items-center\">\r\n        <div class=\"text-sm text-gray-700\">\r\n          显示 <span class=\"font-medium\">{{ filteredAccounts.length }}</span> 个账号\r\n          (共 <span class=\"font-medium\">{{ getAllAccounts.length }}</span> 个)\r\n        </div>\r\n        <div class=\"flex space-x-2\">\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\r\n            <font-awesome-icon :icon=\"['fas', 'file-export']\" class=\"mr-1\" />\r\n            导出\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\r\n            <font-awesome-icon :icon=\"['fas', 'print']\" class=\"mr-1\" />\r\n            打印\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <table class=\"min-w-full divide-y divide-gray-200\">\r\n        <thead class=\"bg-gray-50\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n                主机名\r\n              </CustomCheckbox>\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              IP地址\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              账号\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              最后密码修改时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码过期时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              状态\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              操作\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"bg-white divide-y divide-gray-200\">\r\n          <!-- 按主机分组显示 -->\r\n          <template v-for=\"hostGroup in groupedAccounts\" :key=\"hostGroup.hostId\">\r\n            <!-- 主机分组标题行 -->\r\n            <tr class=\"bg-gray-100\">\r\n              <td colspan=\"8\" class=\"px-6 py-2\">\r\n                <div class=\"flex items-center justify-between\">\r\n                  <div class=\"font-medium text-gray-700\">{{ hostGroup.hostName }} ({{ hostGroup.hostIp }})</div>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n            <!-- 账号行 -->\r\n            <tr v-for=\"(account, accountIndex) in hostGroup.accounts\" :key=\"account.id\"\r\n              :class=\"{ 'bg-gray-50': accountIndex % 2 === 0, 'hover:bg-blue-50': true }\">\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <CustomCheckbox v-model=\"account.host.selected\" class=\"ml-4\">\r\n                    <span class=\"ml-2 font-medium text-gray-900\">{{ account.host.name }}</span>\r\n                  </CustomCheckbox>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-900\">{{ account.host.ip }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-500\">{{ account.lastPasswordChange || '-' }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div :class=\"{\r\n                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\r\n                  'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                  'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                  'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                }\">\r\n                  {{ isPasswordExpired(account).text }}\r\n                  <span\r\n                    v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                    class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                  </span>\r\n                  <span v-else-if=\"isPasswordExpired(account).status === 'warning'\" class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" />\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <div class=\"flex-grow\">\r\n                    <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\"\r\n                      readonly\r\n                      class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  </div>\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <StatusBadge :type=\"account.host.status\" />\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                <div class=\"flex space-x-2\">\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"openChangePasswordModal(account.host, account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                    修改密码\r\n                  </button>\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"copyPassword(account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'copy']\" class=\"mr-1\" />\r\n                    复制\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          </template>\r\n          <!-- 无数据显示 -->\r\n          <tr v-if=\"filteredAccounts.length === 0\">\r\n            <td colspan=\"8\" class=\"px-6 py-10 text-center\">\r\n              <div class=\"text-gray-500\">\r\n                <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-4xl mb-3\" />\r\n                <p>没有找到匹配的账号数据</p>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <!-- 卡片视图 -->\r\n    <div v-else class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\">\r\n      <div v-for=\"host in filteredHosts\" :key=\"host.id\" class=\"bg-white overflow-hidden shadow rounded-lg\">\r\n        <div class=\"px-4 py-5 sm:p-6\">\r\n          <!-- 主机头部 -->\r\n          <div class=\"flex justify-between items-start mb-4\">\r\n            <div class=\"flex items-center\">\r\n              <CustomCheckbox v-model=\"host.selected\" class=\"mr-2\" />\r\n              <div>\r\n                <h3 class=\"text-lg font-medium text-gray-900\">{{ host.name }}</h3>\r\n                <p class=\"text-sm text-gray-500\">{{ host.ip }}</p>\r\n              </div>\r\n            </div>\r\n            <StatusBadge :type=\"host.status\" />\r\n          </div>\r\n\r\n          <!-- 账号列表 -->\r\n          <div class=\"space-y-4\">\r\n            <div v-for=\"account in host.accounts\" :key=\"account.id\" class=\"border border-gray-200 rounded-lg p-3\"\r\n              :class=\"{ 'border-green-300 bg-green-50': account.isDefault }\">\r\n              <div class=\"flex justify-between items-center mb-2\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n                <button\r\n                  class=\"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                  @click=\"openChangePasswordModal(host, account)\">\r\n                  <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                  修改密码\r\n                </button>\r\n              </div>\r\n\r\n              <!-- 密码展示 -->\r\n              <div class=\"mb-2\">\r\n                <div class=\"text-xs font-medium text-gray-500 mb-1\">密码</div>\r\n                <div class=\"flex items-center\">\r\n                  <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\" readonly\r\n                    class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 密码信息区域 -->\r\n              <div class=\"grid grid-cols-2 gap-2 text-xs\">\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">最后修改时间</div>\r\n                  <div class=\"text-gray-900\">{{ account.lastPasswordChange || '-' }}</div>\r\n                </div>\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">密码过期</div>\r\n                  <div :class=\"{\r\n                    'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\r\n                    'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                    'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                    'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                  }\">\r\n                    {{ isPasswordExpired(account).text }}\r\n                    <span\r\n                      v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                      class=\"ml-1\">\r\n                      <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 添加账号按钮 -->\r\n          <div class=\"mt-4 flex justify-center\">\r\n            <button\r\n              class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n              @click=\"openAddAccountModal(host)\">\r\n              <font-awesome-icon :icon=\"['fas', 'plus']\" class=\"mr-1\" />\r\n              添加账号\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" @confirm=\"updatePassword\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n          <div><span class=\"font-medium\">账号:</span> {{ currentAccount.username }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-3\">\r\n          <button @click=\"changePasswordModal.method = 'auto'\"\r\n            class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n            :class=\"changePasswordModal.method === 'auto' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-2\" />\r\n            自动生成\r\n          </button>\r\n          <button @click=\"changePasswordModal.method = 'manual'\"\r\n            class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n            :class=\"changePasswordModal.method === 'manual' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n            <font-awesome-icon :icon=\"['fas', 'edit']\" class=\"mr-2\" />\r\n            手动输入\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-if=\"changePasswordModal.method === 'auto'\" class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"changePasswordModal.policyId\" class=\"form-select\" @change=\"generatePassword()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n\r\n        <div class=\"mt-3\">\r\n          <div class=\"flex justify-between mb-1\">\r\n            <label class=\"form-label\">生成的密码</label>\r\n            <button @click=\"generatePassword()\" type=\"button\"\r\n              class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n              重新生成\r\n            </button>\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.generated ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.generatedPassword\" readonly class=\"form-control flex-1 bg-gray-50\" />\r\n            <button @click=\"passwordVisibility.generated = !passwordVisibility.generated\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.generated ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else class=\"space-y-4\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.new ? 'text' : 'password'\" v-model=\"changePasswordModal.newPassword\"\r\n              class=\"form-control flex-1\" placeholder=\"输入新密码\" />\r\n            <button @click=\"passwordVisibility.new = !passwordVisibility.new\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.new ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.confirm ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.confirmPassword\" class=\"form-control flex-1\"\r\n              :class=\"{ 'border-red-500': passwordMismatch }\" placeholder=\"再次输入新密码\" />\r\n            <button @click=\"passwordVisibility.confirm = !passwordVisibility.confirm\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.confirm ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n          <div v-if=\"passwordMismatch\" class=\"text-sm text-red-500 mt-1\">两次输入的密码不一致</div>\r\n        </div>\r\n\r\n        <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n      </div>\r\n\r\n      <div class=\"space-y-2 mt-4\">\r\n        <div class=\"form-label font-medium\">执行选项</div>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          <span class=\"ml-2\">立即执行</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          <span class=\"ml-2\">保存历史记录</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          <span class=\"ml-2\">记录审计日志</span>\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 添加账号弹窗 -->\r\n    <BaseModal v-model=\"addAccountModal.show\" title=\"添加账号\" @confirm=\"addAccount\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">账号名称</label>\r\n        <input type=\"text\" v-model=\"addAccountModal.username\" class=\"form-control\" placeholder=\"输入账号名称\" />\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">设为默认账号</label>\r\n        <div class=\"relative inline-block w-10 mr-2 align-middle select-none\">\r\n          <input type=\"checkbox\" v-model=\"addAccountModal.isDefault\"\r\n            class=\"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\" />\r\n          <label class=\"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"></label>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"addAccountModal.policyId\" class=\"form-select\" @change=\"generatePasswordForNewAccount()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <div class=\"flex justify-between mb-1\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <button @click=\"generatePasswordForNewAccount()\" type=\"button\"\r\n            class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n            重新生成\r\n          </button>\r\n        </div>\r\n        <div class=\"flex items-center\">\r\n          <input :type=\"passwordVisibility.newAccount ? 'text' : 'password'\" v-model=\"addAccountModal.password\" readonly\r\n            class=\"form-control flex-1 bg-gray-50\" />\r\n          <button @click=\"passwordVisibility.newAccount = !passwordVisibility.newAccount\" type=\"button\"\r\n            class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', passwordVisibility.newAccount ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量添加账号弹窗 -->\r\n    <BaseModal v-model=\"batchAddAccountModal.show\" title=\"批量添加账号\" size=\"lg\" @confirm=\"batchAddAccounts\" :loading=\"processing\">\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatchAdd\" @update:modelValue=\"toggleSelectAllBatchAdd\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchAddAccountModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedBatchAddHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">账号信息</label>\r\n        <div class=\"p-4 border border-gray-200 rounded-md\">\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">账号名称 <span class=\"text-red-500\">*</span></label>\r\n            <input type=\"text\" v-model=\"batchAddAccountModal.username\" class=\"form-control\" placeholder=\"输入统一账号名称\" />\r\n            <div class=\"text-xs text-gray-500 mt-1\">将在所有选中主机上创建同名账号</div>\r\n          </div>\r\n\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">账号角色</label>\r\n            <select v-model=\"batchAddAccountModal.role\" class=\"form-select\">\r\n              <option value=\"admin\">管理员</option>\r\n              <option value=\"user\">普通用户</option>\r\n              <option value=\"service\">服务账号</option>\r\n              <option value=\"readonly\">只读账号</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div class=\"flex items-center mb-3\">\r\n            <label class=\"inline-flex items-center\">\r\n              <input type=\"checkbox\" v-model=\"batchAddAccountModal.setAsDefault\" class=\"form-checkbox\">\r\n              <span class=\"ml-2\">设为默认账号</span>\r\n            </label>\r\n          </div>\r\n\r\n          <div class=\"mb-3\">\r\n            <label class=\"form-label\">密码生成方式</label>\r\n            <div class=\"flex space-x-3\">\r\n              <button @click=\"batchAddAccountModal.useSamePassword = true\"\r\n                class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n                :class=\"batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n                <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n                同一密码\r\n              </button>\r\n              <button @click=\"batchAddAccountModal.useSamePassword = false\"\r\n                class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n                :class=\"!batchAddAccountModal.useSamePassword ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n                <font-awesome-icon :icon=\"['fas', 'random']\" class=\"mr-2\" />\r\n                随机密码\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <div v-if=\"batchAddAccountModal.useSamePassword\" class=\"mb-3\">\r\n            <label class=\"form-label\">统一密码</label>\r\n            <div class=\"flex items-center\">\r\n              <input :type=\"passwordVisibility.batchPassword ? 'text' : 'password'\" v-model=\"batchAddAccountModal.password\"\r\n                readonly class=\"form-control flex-1 bg-gray-50\" />\r\n              <button @click=\"passwordVisibility.batchPassword = !passwordVisibility.batchPassword\" type=\"button\"\r\n                class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                <font-awesome-icon :icon=\"['fas', passwordVisibility.batchPassword ? 'eye-slash' : 'eye']\"\r\n                  class=\"text-lg\" />\r\n              </button>\r\n              <button @click=\"generatePasswordForBatchAccount()\" type=\"button\"\r\n                class=\"ml-2 px-3 py-1.5 border border-gray-300 text-xs rounded-md\">\r\n                <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n                重新生成\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchAddAccountModal.policyId\" class=\"form-select\" @change=\"generatePasswordForBatchAccount()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <div class=\"space-y-2\">\r\n          <CustomCheckbox v-model=\"batchAddAccountModal.ignoreErrors\">\r\n            <span class=\"ml-2\">忽略错误继续执行</span>\r\n          </CustomCheckbox>\r\n          <CustomCheckbox v-model=\"batchAddAccountModal.generateReport\">\r\n            <span class=\"ml-2\">生成账号创建报告</span>\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      currentAccount: {},\r\n      passwordVisibility: {\r\n        generated: false,\r\n        new: false,\r\n        confirm: false,\r\n        newAccount: false,\r\n        batchPassword: false\r\n      },\r\n      viewMode: 'table',\r\n      filterText: '',\r\n      accountFilterText: '',\r\n      statusFilter: 'all',\r\n      expiryFilter: 'all',\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      },\r\n\r\n      // 添加账号弹窗\r\n      addAccountModal: {\r\n        show: false,\r\n        username: '',\r\n        password: '',\r\n        isDefault: false,\r\n        policyId: 1\r\n      },\r\n\r\n      // 批量添加账号弹窗\r\n      batchAddAccountModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        username: '',\r\n        password: '',\r\n        role: 'admin',\r\n        setAsDefault: false,\r\n        useSamePassword: true,\r\n        policyId: 1\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    },\r\n\r\n    // 过滤后的主机列表\r\n    filteredHosts() {\r\n      return this.hosts.filter(host => {\r\n        // 文本过滤\r\n        const textMatch = this.filterText === '' ||\r\n          host.name.toLowerCase().includes(this.filterText.toLowerCase()) ||\r\n          host.ip.includes(this.filterText);\r\n\r\n        // 状态过滤\r\n        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;\r\n\r\n        return textMatch && statusMatch;\r\n      });\r\n    },\r\n\r\n    // 获取所有账号（扁平化处理）\r\n    getAllAccounts() {\r\n      // 为每个账号添加主机引用\r\n      const accounts = [];\r\n      this.filteredHosts.forEach(host => {\r\n        host.accounts.forEach(account => {\r\n          accounts.push({\r\n            ...account,\r\n            host: host\r\n          });\r\n        });\r\n      });\r\n      return accounts;\r\n    },\r\n\r\n    // 筛选后的账号\r\n    filteredAccounts() {\r\n      return this.getAllAccounts.filter(account => {\r\n        // 账号名称筛选\r\n        const accountMatch = this.accountFilterText === '' ||\r\n          account.username.toLowerCase().includes(this.accountFilterText.toLowerCase());\r\n\r\n        // 密码过期筛选\r\n        let expiryMatch = true;\r\n        if (this.expiryFilter !== 'all') {\r\n          const expiryStatus = this.isPasswordExpired(account).status;\r\n          if (this.expiryFilter === 'expired') {\r\n            expiryMatch = expiryStatus === 'expired';\r\n          } else if (this.expiryFilter === 'expiring-soon') {\r\n            expiryMatch = expiryStatus === 'danger' || expiryStatus === 'warning';\r\n          } else if (this.expiryFilter === 'valid') {\r\n            expiryMatch = expiryStatus === 'normal';\r\n          }\r\n        }\r\n\r\n        return accountMatch && expiryMatch;\r\n      });\r\n    },\r\n\r\n    // 分组后的账号列表\r\n    groupedAccounts() {\r\n      // 按主机ID分组\r\n      const groups = {};\r\n      this.filteredAccounts.forEach(account => {\r\n        const hostId = account.host.id;\r\n        if (!groups[hostId]) {\r\n          groups[hostId] = {\r\n            hostId: hostId,\r\n            hostName: account.host.name,\r\n            hostIp: account.host.ip,\r\n            host: account.host,\r\n            accounts: []\r\n          };\r\n        }\r\n        groups[hostId].accounts.push(account);\r\n      });\r\n\r\n      // 转换为数组\r\n      return Object.values(groups);\r\n    },\r\n\r\n    selectedBatchAddHostsCount() {\r\n      return Object.values(this.batchAddAccountModal.selectedHosts).filter(Boolean).length\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchAddAccountModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host, account) {\r\n      this.currentHost = host\r\n      this.currentAccount = account\r\n      this.changePasswordModal.show = true\r\n      this.changePasswordModal.generatedPassword = this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword(policy) {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policyObj ? policyObj.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      if (this.changePasswordModal && !policy) {\r\n        this.changePasswordModal.generatedPassword = password\r\n      }\r\n\r\n      return password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto'\r\n          ? this.changePasswordModal.generatedPassword\r\n          : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          accountId: this.currentAccount.id,\r\n          password: password,\r\n          policyId: this.changePasswordModal.policyId\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的 ${this.currentAccount.username} 账号密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取所选策略\r\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取紧急策略\r\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    togglePasswordVisibility(hostId) {\r\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]\r\n    },\r\n\r\n    isPasswordExpired(account) {\r\n      if (!account.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }\r\n\r\n      // 解析过期时间\r\n      const expiryDate = new Date(account.passwordExpiryDate)\r\n      const now = new Date()\r\n\r\n      // 如果已过期\r\n      if (expiryDate < now) {\r\n        return {\r\n          status: 'expired',\r\n          days: 0,\r\n          text: '已过期'\r\n        }\r\n      }\r\n\r\n      // 计算剩余天数和小时数\r\n      const diffTime = expiryDate - now\r\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))\r\n      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n\r\n      // 根据剩余时间确定状态\r\n      let status = 'normal'\r\n      if (diffDays < 7) {\r\n        status = 'danger'  // 少于7天\r\n      } else if (diffDays < 14) {\r\n        status = 'warning' // 少于14天\r\n      }\r\n\r\n      // 格式化显示文本\r\n      let text = ''\r\n      if (diffDays > 0) {\r\n        text += `${diffDays}天`\r\n      }\r\n      if (diffHours > 0 || diffDays === 0) {\r\n        text += `${diffHours}小时`\r\n      }\r\n\r\n      return { status, days: diffDays, text: `剩余${text}` }\r\n    },\r\n\r\n    openAddAccountModal(host) {\r\n      this.currentHost = host\r\n      this.addAccountModal.show = true\r\n    },\r\n\r\n    async addAccount() {\r\n      if (!this.addAccountModal.username || !this.addAccountModal.password) {\r\n        alert('请填写完整的账号信息！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('addHostAccount', {\r\n          hostId: this.currentHost.id,\r\n          username: this.addAccountModal.username,\r\n          password: this.addAccountModal.password,\r\n          policyId: this.addAccountModal.policyId,\r\n          isDefault: this.addAccountModal.isDefault\r\n        })\r\n\r\n        this.addAccountModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为主机 ${this.currentHost.name} 添加账号！`)\r\n      } catch (error) {\r\n        console.error('添加账号失败', error)\r\n        alert('添加账号失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    generatePasswordForNewAccount() {\r\n      this.addAccountModal.password = this.generatePassword()\r\n    },\r\n\r\n    // 复制密码到剪贴板\r\n    copyPassword(account) {\r\n      // 创建一个临时输入框\r\n      const tempInput = document.createElement('input');\r\n      tempInput.value = account.password;\r\n      document.body.appendChild(tempInput);\r\n      tempInput.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(tempInput);\r\n\r\n      // 显示提示\r\n      alert(`已复制 ${account.host.name} 的 ${account.username} 账号密码到剪贴板！`);\r\n    },\r\n\r\n    generatePasswordForBatchAccount() {\r\n      this.batchAddAccountModal.password = this.generatePassword()\r\n    },\r\n\r\n    async batchAddAccounts() {\r\n      if (!this.batchAddAccountModal.username) {\r\n        alert('请填写账号名称！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('batchAddAccounts', {\r\n          hostIds: Object.keys(this.batchAddAccountModal.selectedHosts).map(id => parseInt(id)),\r\n          username: this.batchAddAccountModal.username,\r\n          password: this.batchAddAccountModal.password,\r\n          role: this.batchAddAccountModal.role,\r\n          isDefault: this.batchAddAccountModal.setAsDefault,\r\n          policyId: this.batchAddAccountModal.policyId\r\n        })\r\n\r\n        this.batchAddAccountModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为选中的 ${Object.keys(this.batchAddAccountModal.selectedHosts).length} 台主机添加账号！`)\r\n      } catch (error) {\r\n        console.error('批量添加账号失败', error)\r\n        alert('批量添加账号失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": "AAkuBA,SAASA,QAAQ,EAAEC,UAAS,QAAS,MAAK;AAC1C,OAAOC,SAAQ,MAAO,4BAA2B;AACjD,OAAOC,WAAU,MAAO,8BAA6B;AACrD,OAAOC,cAAa,MAAO,iCAAgC;AAC3D,OAAOC,qBAAoB,MAAO,wCAAuC;AAEzE,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,UAAU,EAAE;IACVL,SAAS;IACTC,WAAW;IACXC,cAAc;IACdC;EACF,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,KAAK;MAChBC,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,CAAC,CAAC;MACfC,cAAc,EAAE,CAAC,CAAC;MAClBC,kBAAkB,EAAE;QAClBC,SAAS,EAAE,KAAK;QAChBC,GAAG,EAAE,KAAK;QACVC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE,KAAK;QACjBC,aAAa,EAAE;MACjB,CAAC;MACDC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE,EAAE;MACdC,iBAAiB,EAAE,EAAE;MACrBC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,KAAK;MAEnB;MACAC,mBAAmB,EAAE;QACnBC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE,CAAC;QACXC,iBAAiB,EAAE,cAAc;QACjCC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE,EAAE;QACnBC,kBAAkB,EAAE,IAAI;QACxBC,WAAW,EAAE,KAAK;QAClBC,QAAQ,EAAE;MACZ,CAAC;MAED;MACAC,gBAAgB,EAAE;QAChBT,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBR,QAAQ,EAAE,CAAC;QACXS,aAAa,EAAE,WAAW;QAC1BC,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,EAAE;QACjBC,YAAY,EAAE,IAAI;QAClBC,WAAW,EAAE,IAAI;QACjBC,gBAAgB,EAAE;MACpB,CAAC;MAED;MACAC,eAAe,EAAE;QACfjB,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBR,QAAQ,EAAE,CAAC;QACXgB,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;MAED;MACAC,mBAAmB,EAAE;QACnBpB,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBR,QAAQ,EAAE,CAAC;QAAE;QACbmB,MAAM,EAAE,mBAAmB;QAC3BC,WAAW,EAAE;MACf,CAAC;MAED;MACAC,eAAe,EAAE;QACfvB,IAAI,EAAE,KAAK;QACXwB,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,KAAK;QAChBxB,QAAQ,EAAE;MACZ,CAAC;MAED;MACAyB,oBAAoB,EAAE;QACpB3B,IAAI,EAAE,KAAK;QACXU,aAAa,EAAE,CAAC,CAAC;QACjBc,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZG,IAAI,EAAE,OAAO;QACbC,YAAY,EAAE,KAAK;QACnBC,eAAe,EAAE,IAAI;QACrB5B,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;EACD6B,QAAQ,EAAE;IACR,GAAGzD,QAAQ,CAAC;MACV0D,KAAK,EAAEC,KAAI,IAAKA,KAAK,CAACD,KAAK;MAC3BE,QAAQ,EAAED,KAAI,IAAKA,KAAK,CAACC;IAC3B,CAAC,CAAC;IACF,GAAG3D,UAAU,CAAC,CAAC,eAAe,CAAC,CAAC;IAEhC4D,gBAAgBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACpC,mBAAmB,CAACK,WAAU,IACxC,IAAI,CAACL,mBAAmB,CAACM,eAAc,IACvC,IAAI,CAACN,mBAAmB,CAACK,WAAU,KAAM,IAAI,CAACL,mBAAmB,CAACM,eAAc;IACpF,CAAC;IAED+B,kBAAkBA,CAAA,EAAG;MACnB,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC7B,gBAAgB,CAACC,aAAa,CAAC,CAAC6B,MAAM,CAACC,OAAO,CAAC,CAACC,MAAK;IACjF,CAAC;IAEDC,iBAAiBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACV,KAAK,CAACO,MAAM,CAACI,IAAG,IAAKA,IAAI,CAACC,QAAQ;IAChD,CAAC;IAEDC,iBAAiBA,CAAA,EAAG;MAClB;MACA,OAAO,IAAI,CAACX,QAAQ,CAACK,MAAM,CAACO,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,KAAKD,CAAC,CAACC,EAAC,KAAM,CAAC;IAC3D,CAAC;IAED;IACAC,aAAaA,CAAA,EAAG;MACd,OAAO,IAAI,CAAChB,KAAK,CAACO,MAAM,CAACI,IAAG,IAAK;QAC/B;QACA,MAAMM,SAAQ,GAAI,IAAI,CAACtD,UAAS,KAAM,EAAC,IACrCgD,IAAI,CAAC/D,IAAI,CAACsE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACxD,UAAU,CAACuD,WAAW,CAAC,CAAC,KAC9DP,IAAI,CAACS,EAAE,CAACD,QAAQ,CAAC,IAAI,CAACxD,UAAU,CAAC;;QAEnC;QACA,MAAM0D,WAAU,GAAI,IAAI,CAACxD,YAAW,KAAM,KAAI,IAAK8C,IAAI,CAACW,MAAK,KAAM,IAAI,CAACzD,YAAY;QAEpF,OAAOoD,SAAQ,IAAKI,WAAW;MACjC,CAAC,CAAC;IACJ,CAAC;IAED;IACAE,cAAcA,CAAA,EAAG;MACf;MACA,MAAMC,QAAO,GAAI,EAAE;MACnB,IAAI,CAACR,aAAa,CAACS,OAAO,CAACd,IAAG,IAAK;QACjCA,IAAI,CAACa,QAAQ,CAACC,OAAO,CAACC,OAAM,IAAK;UAC/BF,QAAQ,CAACG,IAAI,CAAC;YACZ,GAAGD,OAAO;YACVf,IAAI,EAAEA;UACR,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAOa,QAAQ;IACjB,CAAC;IAED;IACAI,gBAAgBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACL,cAAc,CAAChB,MAAM,CAACmB,OAAM,IAAK;QAC3C;QACA,MAAMG,YAAW,GAAI,IAAI,CAACjE,iBAAgB,KAAM,EAAC,IAC/C8D,OAAO,CAAClC,QAAQ,CAAC0B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACvD,iBAAiB,CAACsD,WAAW,CAAC,CAAC,CAAC;;QAE/E;QACA,IAAIY,WAAU,GAAI,IAAI;QACtB,IAAI,IAAI,CAAChE,YAAW,KAAM,KAAK,EAAE;UAC/B,MAAMiE,YAAW,GAAI,IAAI,CAACC,iBAAiB,CAACN,OAAO,CAAC,CAACJ,MAAM;UAC3D,IAAI,IAAI,CAACxD,YAAW,KAAM,SAAS,EAAE;YACnCgE,WAAU,GAAIC,YAAW,KAAM,SAAS;UAC1C,OAAO,IAAI,IAAI,CAACjE,YAAW,KAAM,eAAe,EAAE;YAChDgE,WAAU,GAAIC,YAAW,KAAM,QAAO,IAAKA,YAAW,KAAM,SAAS;UACvE,OAAO,IAAI,IAAI,CAACjE,YAAW,KAAM,OAAO,EAAE;YACxCgE,WAAU,GAAIC,YAAW,KAAM,QAAQ;UACzC;QACF;QAEA,OAAOF,YAAW,IAAKC,WAAW;MACpC,CAAC,CAAC;IACJ,CAAC;IAED;IACAG,eAAeA,CAAA,EAAG;MAChB;MACA,MAAMC,MAAK,GAAI,CAAC,CAAC;MACjB,IAAI,CAACN,gBAAgB,CAACH,OAAO,CAACC,OAAM,IAAK;QACvC,MAAMS,MAAK,GAAIT,OAAO,CAACf,IAAI,CAACI,EAAE;QAC9B,IAAI,CAACmB,MAAM,CAACC,MAAM,CAAC,EAAE;UACnBD,MAAM,CAACC,MAAM,IAAI;YACfA,MAAM,EAAEA,MAAM;YACdC,QAAQ,EAAEV,OAAO,CAACf,IAAI,CAAC/D,IAAI;YAC3ByF,MAAM,EAAEX,OAAO,CAACf,IAAI,CAACS,EAAE;YACvBT,IAAI,EAAEe,OAAO,CAACf,IAAI;YAClBa,QAAQ,EAAE;UACZ,CAAC;QACH;QACAU,MAAM,CAACC,MAAM,CAAC,CAACX,QAAQ,CAACG,IAAI,CAACD,OAAO,CAAC;MACvC,CAAC,CAAC;;MAEF;MACA,OAAOrB,MAAM,CAACC,MAAM,CAAC4B,MAAM,CAAC;IAC9B,CAAC;IAEDI,0BAA0BA,CAAA,EAAG;MAC3B,OAAOjC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACX,oBAAoB,CAACjB,aAAa,CAAC,CAAC6B,MAAM,CAACC,OAAO,CAAC,CAACC,MAAK;IACrF;EACF,CAAC;EACD8B,OAAO,EAAE;IACPC,eAAeA,CAACC,KAAK,EAAE;MACrB,IAAI,CAACC,MAAM,CAACC,MAAM,CAAC,gBAAgB,EAAEF,KAAK;IAC5C,CAAC;IAEDG,oBAAoBA,CAACH,KAAK,EAAE;MAC1B,IAAI,CAACzC,KAAK,CAACyB,OAAO,CAACd,IAAG,IAAK;QACzB,IAAI,CAAChB,oBAAoB,CAACjB,aAAa,CAACiC,IAAI,CAACI,EAAE,IAAI0B,KAAI;MACzD,CAAC;IACH,CAAC;IAEDI,uBAAuBA,CAAClC,IAAI,EAAEe,OAAO,EAAE;MACrC,IAAI,CAACxE,WAAU,GAAIyD,IAAG;MACtB,IAAI,CAACxD,cAAa,GAAIuE,OAAM;MAC5B,IAAI,CAAC3D,mBAAmB,CAACC,IAAG,GAAI,IAAG;MACnC,IAAI,CAACD,mBAAmB,CAACI,iBAAgB,GAAI,IAAI,CAAC2E,gBAAgB,CAAC;IACrE,CAAC;IAEDC,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAACtE,gBAAgB,CAACT,IAAG,GAAI,IAAG;;MAEhC;MACA,IAAI,CAACgC,KAAK,CAACyB,OAAO,CAACd,IAAG,IAAK;QACzB,IAAI,CAAClC,gBAAgB,CAACC,aAAa,CAACiC,IAAI,CAACI,EAAE,IAAIJ,IAAI,CAACC,QAAO;MAC7D,CAAC;;MAED;MACA,MAAMoC,KAAI,GAAI,IAAIC,IAAI,CAAC;MACvB,IAAI,CAACxE,gBAAgB,CAACG,aAAY,GAAIoE,KAAK,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MACtE,IAAI,CAAC1E,gBAAgB,CAACI,aAAY,GAAI,OAAM;IAC9C,CAAC;IAEDuE,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACnE,eAAe,CAACjB,IAAG,GAAI,IAAG;;MAE/B;MACA,IAAI,CAACgC,KAAK,CAACyB,OAAO,CAACd,IAAG,IAAK;QACzB,IAAI,CAAC1B,eAAe,CAACP,aAAa,CAACiC,IAAI,CAACI,EAAE,IAAIJ,IAAI,CAACC,QAAO;MAC5D,CAAC;IACH,CAAC;IAEDyC,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAACjE,mBAAmB,CAACpB,IAAG,GAAI,IAAG;;MAEnC;MACA,IAAI,CAACgC,KAAK,CAACyB,OAAO,CAACd,IAAG,IAAK;QACzB,IAAI,CAACvB,mBAAmB,CAACV,aAAa,CAACiC,IAAI,CAACI,EAAE,IAAIJ,IAAI,CAACC,QAAO;MAChE,CAAC;IACH,CAAC;IAEDkC,gBAAgBA,CAACQ,MAAM,EAAE;MACvB,MAAMC,KAAI,GAAI,0EAAyE;MACvF,IAAI9D,QAAO,GAAI,EAAC;;MAEhB;MACA,MAAM+D,SAAQ,GAAIF,MAAK,IAAK,IAAI,CAACpD,QAAQ,CAACuD,IAAI,CAAC3C,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,IAAI,CAAChD,mBAAmB,CAACG,QAAQ;MAC9F,MAAMwF,SAAQ,GAAIF,SAAQ,GAAIA,SAAS,CAACE,SAAQ,GAAI,EAAC;;MAErD;MACA,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAID,SAAS,EAAEC,CAAC,EAAE,EAAE;QAClClE,QAAO,IAAK8D,KAAK,CAACK,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIR,KAAK,CAAC9C,MAAM,CAAC;MACnE;MAEA,IAAI,IAAI,CAAC1C,mBAAkB,IAAK,CAACuF,MAAM,EAAE;QACvC,IAAI,CAACvF,mBAAmB,CAACI,iBAAgB,GAAIsB,QAAO;MACtD;MAEA,OAAOA,QAAO;IAChB,CAAC;IAED,MAAMuE,cAAcA,CAAA,EAAG;MACrB,IAAI,IAAI,CAACjG,mBAAmB,CAACE,MAAK,KAAM,QAAO,IAAK,IAAI,CAACkC,gBAAgB,EAAE;QACzE;MACF;MAEA,IAAI,CAAClD,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAMwC,QAAO,GAAI,IAAI,CAAC1B,mBAAmB,CAACE,MAAK,KAAM,MAAK,GACtD,IAAI,CAACF,mBAAmB,CAACI,iBAAgB,GACzC,IAAI,CAACJ,mBAAmB,CAACK,WAAU;QAEvC,MAAM,IAAI,CAACsE,MAAM,CAACuB,QAAQ,CAAC,oBAAoB,EAAE;UAC/C9B,MAAM,EAAE,IAAI,CAACjF,WAAW,CAAC6D,EAAE;UAC3BmD,SAAS,EAAE,IAAI,CAAC/G,cAAc,CAAC4D,EAAE;UACjCtB,QAAQ,EAAEA,QAAQ;UAClBvB,QAAQ,EAAE,IAAI,CAACH,mBAAmB,CAACG;QACrC,CAAC;QAED,IAAI,CAACH,mBAAmB,CAACC,IAAG,GAAI,KAAI;;QAEpC;QACAmG,KAAK,CAAC,WAAW,IAAI,CAACjH,WAAW,CAACN,IAAI,MAAM,IAAI,CAACO,cAAc,CAACqC,QAAQ,QAAQ;MAClF,EAAE,OAAO4E,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAAClH,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED,MAAMqH,oBAAoBA,CAAA,EAAG;MAC3B,MAAMC,eAAc,GAAIlE,MAAM,CAACmE,OAAO,CAAC,IAAI,CAAC/F,gBAAgB,CAACC,aAAa,EACvE6B,MAAM,CAAC,CAAC,CAACkE,CAAC,EAAE7D,QAAQ,CAAC,KAAKA,QAAQ,EAClC8D,GAAG,CAAC,CAAC,CAAC3D,EAAE,CAAC,KAAK4D,QAAQ,CAAC5D,EAAE,CAAC;MAE7B,IAAIwD,eAAe,CAAC9D,MAAK,KAAM,CAAC,EAAE;QAChC0D,KAAK,CAAC,YAAY;QAClB;MACF;MAEA,IAAI,IAAI,CAAC1F,gBAAgB,CAACE,aAAY,KAAM,WAAW,EAAE;QACvD;QACAwF,KAAK,CAAC,cAAc;QACpB,IAAI,CAAC1F,gBAAgB,CAACT,IAAG,GAAI,KAAI;QACjC;MACF;MAEA,IAAI,CAACf,UAAS,GAAI,IAAG;MAErB,IAAI;QACF;QACA,MAAMqG,MAAK,GAAI,IAAI,CAACpD,QAAQ,CAACuD,IAAI,CAAC3C,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,IAAI,CAACtC,gBAAgB,CAACP,QAAQ;;QAE9E;QACA,KAAK,MAAMiE,MAAK,IAAKoC,eAAe,EAAE;UACpC,MAAM5D,IAAG,GAAI,IAAI,CAACX,KAAK,CAACyD,IAAI,CAACmB,CAAA,IAAKA,CAAC,CAAC7D,EAAC,KAAMoB,MAAM;UACjD,IAAIxB,IAAI,EAAE;YACR,KAAK,MAAMe,OAAM,IAAKf,IAAI,CAACa,QAAQ,EAAE;cACnC,MAAMpD,WAAU,GAAI,IAAI,CAAC0E,gBAAgB,CAACQ,MAAM;cAChD,MAAM,IAAI,CAACZ,MAAM,CAACuB,QAAQ,CAAC,oBAAoB,EAAE;gBAC/C9B,MAAM,EAAEA,MAAM;gBACd+B,SAAS,EAAExC,OAAO,CAACX,EAAE;gBACrBtB,QAAQ,EAAErB,WAAW;gBACrBF,QAAQ,EAAEoF,MAAM,CAACvC;cACnB,CAAC;YACH;UACF;QACF;QAEA,IAAI,CAACtC,gBAAgB,CAACT,IAAG,GAAI,KAAI;;QAEjC;QACAmG,KAAK,CAAC,QAAQI,eAAe,CAAC9D,MAAM,gBAAgB;MACtD,EAAE,OAAO2D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK;QAC/BD,KAAK,CAAC,eAAe;MACvB,UAAU;QACR,IAAI,CAAClH,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED,MAAM4H,gBAAgBA,CAAA,EAAG;MACvB,MAAMN,eAAc,GAAIlE,MAAM,CAACmE,OAAO,CAAC,IAAI,CAACvF,eAAe,CAACP,aAAa,EACtE6B,MAAM,CAAC,CAAC,CAACkE,CAAC,EAAE7D,QAAQ,CAAC,KAAKA,QAAQ,EAClC8D,GAAG,CAAC,CAAC,CAAC3D,EAAE,CAAC,KAAK4D,QAAQ,CAAC5D,EAAE,CAAC;MAE7B,IAAIwD,eAAe,CAAC9D,MAAK,KAAM,CAAC,EAAE;QAChC0D,KAAK,CAAC,YAAY;QAClB;MACF;MAEA,IAAI,CAAClH,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAM,IAAI,CAACyF,MAAM,CAACuB,QAAQ,CAAC,oBAAoB,EAAE;UAC/C/F,QAAQ,EAAE,IAAI,CAACe,eAAe,CAACf,QAAQ;UACvC4G,OAAO,EAAEP;QACX,CAAC;QAED,IAAI,CAACtF,eAAe,CAACjB,IAAG,GAAI,KAAI;;QAEhC;QACAmG,KAAK,CAAC,QAAQI,eAAe,CAAC9D,MAAM,aAAa;MACnD,EAAE,OAAO2D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAAClH,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED,MAAM8H,cAAcA,CAAA,EAAG;MACrB,MAAMR,eAAc,GAAIlE,MAAM,CAACmE,OAAO,CAAC,IAAI,CAACpF,mBAAmB,CAACV,aAAa,EAC1E6B,MAAM,CAAC,CAAC,CAACkE,CAAC,EAAE7D,QAAQ,CAAC,KAAKA,QAAQ,EAClC8D,GAAG,CAAC,CAAC,CAAC3D,EAAE,CAAC,KAAK4D,QAAQ,CAAC5D,EAAE,CAAC;MAE7B,IAAIwD,eAAe,CAAC9D,MAAK,KAAM,CAAC,EAAE;QAChC0D,KAAK,CAAC,YAAY;QAClB;MACF;MAEA,IAAI,CAAClH,UAAS,GAAI,IAAG;MAErB,IAAI;QACF;QACA,MAAMqG,MAAK,GAAI,IAAI,CAACpD,QAAQ,CAACuD,IAAI,CAAC3C,CAAA,IAAKA,CAAC,CAACC,EAAC,KAAM,IAAI,CAAC3B,mBAAmB,CAAClB,QAAQ;;QAEjF;QACA,KAAK,MAAMiE,MAAK,IAAKoC,eAAe,EAAE;UACpC,MAAM5D,IAAG,GAAI,IAAI,CAACX,KAAK,CAACyD,IAAI,CAACmB,CAAA,IAAKA,CAAC,CAAC7D,EAAC,KAAMoB,MAAM;UACjD,IAAIxB,IAAI,EAAE;YACR,KAAK,MAAMe,OAAM,IAAKf,IAAI,CAACa,QAAQ,EAAE;cACnC,MAAMpD,WAAU,GAAI,IAAI,CAAC0E,gBAAgB,CAACQ,MAAM;cAChD,MAAM,IAAI,CAACZ,MAAM,CAACuB,QAAQ,CAAC,oBAAoB,EAAE;gBAC/C9B,MAAM,EAAEA,MAAM;gBACd+B,SAAS,EAAExC,OAAO,CAACX,EAAE;gBACrBtB,QAAQ,EAAErB,WAAW;gBACrBF,QAAQ,EAAEoF,MAAM,CAACvC;cACnB,CAAC;YACH;UACF;QACF;QAEA,IAAI,CAAC3B,mBAAmB,CAACpB,IAAG,GAAI,KAAI;;QAEpC;QACAmG,KAAK,CAAC,QAAQI,eAAe,CAAC9D,MAAM,oBAAoB;MAC1D,EAAE,OAAO2D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAAClH,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED+H,wBAAwBA,CAAC7C,MAAM,EAAE;MAC/B,IAAI,CAAC/E,kBAAkB,CAAC+E,MAAM,IAAI,CAAC,IAAI,CAAC/E,kBAAkB,CAAC+E,MAAM;IACnE,CAAC;IAEDH,iBAAiBA,CAACN,OAAO,EAAE;MACzB,IAAI,CAACA,OAAO,CAACuD,kBAAkB,EAAE,OAAO;QAAE3D,MAAM,EAAE,QAAQ;QAAE4D,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAI;;MAElF;MACA,MAAMC,UAAS,GAAI,IAAInC,IAAI,CAACvB,OAAO,CAACuD,kBAAkB;MACtD,MAAMI,GAAE,GAAI,IAAIpC,IAAI,CAAC;;MAErB;MACA,IAAImC,UAAS,GAAIC,GAAG,EAAE;QACpB,OAAO;UACL/D,MAAM,EAAE,SAAS;UACjB4D,IAAI,EAAE,CAAC;UACPC,IAAI,EAAE;QACR;MACF;;MAEA;MACA,MAAMG,QAAO,GAAIF,UAAS,GAAIC,GAAE;MAChC,MAAME,QAAO,GAAI1B,IAAI,CAACC,KAAK,CAACwB,QAAO,IAAK,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,EAAE,CAAC;MAC5D,MAAME,SAAQ,GAAI3B,IAAI,CAACC,KAAK,CAAEwB,QAAO,IAAK,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,EAAE,CAAC,IAAK,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC;;MAElF;MACA,IAAIhE,MAAK,GAAI,QAAO;MACpB,IAAIiE,QAAO,GAAI,CAAC,EAAE;QAChBjE,MAAK,GAAI,QAAO,EAAG;MACrB,OAAO,IAAIiE,QAAO,GAAI,EAAE,EAAE;QACxBjE,MAAK,GAAI,SAAQ,EAAE;MACrB;;MAEA;MACA,IAAI6D,IAAG,GAAI,EAAC;MACZ,IAAII,QAAO,GAAI,CAAC,EAAE;QAChBJ,IAAG,IAAK,GAAGI,QAAQ,GAAE;MACvB;MACA,IAAIC,SAAQ,GAAI,KAAKD,QAAO,KAAM,CAAC,EAAE;QACnCJ,IAAG,IAAK,GAAGK,SAAS,IAAG;MACzB;MAEA,OAAO;QAAElE,MAAM;QAAE4D,IAAI,EAAEK,QAAQ;QAAEJ,IAAI,EAAE,KAAKA,IAAI;MAAG;IACrD,CAAC;IAEDM,mBAAmBA,CAAC9E,IAAI,EAAE;MACxB,IAAI,CAACzD,WAAU,GAAIyD,IAAG;MACtB,IAAI,CAACpB,eAAe,CAACvB,IAAG,GAAI,IAAG;IACjC,CAAC;IAED,MAAM0H,UAAUA,CAAA,EAAG;MACjB,IAAI,CAAC,IAAI,CAACnG,eAAe,CAACC,QAAO,IAAK,CAAC,IAAI,CAACD,eAAe,CAACE,QAAQ,EAAE;QACpE0E,KAAK,CAAC,aAAa;QACnB;MACF;MAEA,IAAI,CAAClH,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAM,IAAI,CAACyF,MAAM,CAACuB,QAAQ,CAAC,gBAAgB,EAAE;UAC3C9B,MAAM,EAAE,IAAI,CAACjF,WAAW,CAAC6D,EAAE;UAC3BvB,QAAQ,EAAE,IAAI,CAACD,eAAe,CAACC,QAAQ;UACvCC,QAAQ,EAAE,IAAI,CAACF,eAAe,CAACE,QAAQ;UACvCvB,QAAQ,EAAE,IAAI,CAACqB,eAAe,CAACrB,QAAQ;UACvCwB,SAAS,EAAE,IAAI,CAACH,eAAe,CAACG;QAClC,CAAC;QAED,IAAI,CAACH,eAAe,CAACvB,IAAG,GAAI,KAAI;;QAEhC;QACAmG,KAAK,CAAC,UAAU,IAAI,CAACjH,WAAW,CAACN,IAAI,QAAQ;MAC/C,EAAE,OAAOwH,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7BD,KAAK,CAAC,aAAa;MACrB,UAAU;QACR,IAAI,CAAClH,UAAS,GAAI,KAAI;MACxB;IACF,CAAC;IAED0I,6BAA6BA,CAAA,EAAG;MAC9B,IAAI,CAACpG,eAAe,CAACE,QAAO,GAAI,IAAI,CAACqD,gBAAgB,CAAC;IACxD,CAAC;IAED;IACA8C,YAAYA,CAAClE,OAAO,EAAE;MACpB;MACA,MAAMmE,SAAQ,GAAIC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MACjDF,SAAS,CAACpD,KAAI,GAAIf,OAAO,CAACjC,QAAQ;MAClCqG,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,SAAS,CAAC;MACpCA,SAAS,CAACK,MAAM,CAAC,CAAC;MAClBJ,QAAQ,CAACK,WAAW,CAAC,MAAM,CAAC;MAC5BL,QAAQ,CAACE,IAAI,CAACI,WAAW,CAACP,SAAS,CAAC;;MAEpC;MACA1B,KAAK,CAAC,OAAOzC,OAAO,CAACf,IAAI,CAAC/D,IAAI,MAAM8E,OAAO,CAAClC,QAAQ,YAAY,CAAC;IACnE,CAAC;IAED6G,+BAA+BA,CAAA,EAAG;MAChC,IAAI,CAAC1G,oBAAoB,CAACF,QAAO,GAAI,IAAI,CAACqD,gBAAgB,CAAC;IAC7D,CAAC;IAED,MAAMwD,gBAAgBA,CAAA,EAAG;MACvB,IAAI,CAAC,IAAI,CAAC3G,oBAAoB,CAACH,QAAQ,EAAE;QACvC2E,KAAK,CAAC,UAAU;QAChB;MACF;MAEA,IAAI,CAAClH,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAM,IAAI,CAACyF,MAAM,CAACuB,QAAQ,CAAC,kBAAkB,EAAE;UAC7Ca,OAAO,EAAEzE,MAAM,CAACkG,IAAI,CAAC,IAAI,CAAC5G,oBAAoB,CAACjB,aAAa,CAAC,CAACgG,GAAG,CAAC3D,EAAC,IAAK4D,QAAQ,CAAC5D,EAAE,CAAC,CAAC;UACrFvB,QAAQ,EAAE,IAAI,CAACG,oBAAoB,CAACH,QAAQ;UAC5CC,QAAQ,EAAE,IAAI,CAACE,oBAAoB,CAACF,QAAQ;UAC5CG,IAAI,EAAE,IAAI,CAACD,oBAAoB,CAACC,IAAI;UACpCF,SAAS,EAAE,IAAI,CAACC,oBAAoB,CAACE,YAAY;UACjD3B,QAAQ,EAAE,IAAI,CAACyB,oBAAoB,CAACzB;QACtC,CAAC;QAED,IAAI,CAACyB,oBAAoB,CAAC3B,IAAG,GAAI,KAAI;;QAErC;QACAmG,KAAK,CAAC,WAAW9D,MAAM,CAACkG,IAAI,CAAC,IAAI,CAAC5G,oBAAoB,CAACjB,aAAa,CAAC,CAAC+B,MAAM,WAAW;MACzF,EAAE,OAAO2D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK;QAC/BD,KAAK,CAAC,eAAe;MACvB,UAAU;QACR,IAAI,CAAClH,UAAS,GAAI,KAAI;MACxB;IACF;EACF,CAAC;EACDuJ,OAAOA,CAAA,EAAG;IACR;IACA,MAAMxD,KAAI,GAAI,IAAIC,IAAI,CAAC;IACvB,IAAI,CAACxE,gBAAgB,CAACG,aAAY,GAAIoE,KAAK,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IACtE,IAAI,CAAC1E,gBAAgB,CAACI,aAAY,GAAI,OAAM;EAC9C;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}