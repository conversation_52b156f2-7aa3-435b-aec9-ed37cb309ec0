{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, vModelRadio as _vModelRadio, withDirectives as _withDirectives, vModelSelect as _vModelSelect, vModelText as _vModelText, normalizeClass as _normalizeClass, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"flex space-x-3 mb-6\"\n};\nconst _hoisted_2 = {\n  class: \"data-table\"\n};\nconst _hoisted_3 = [\"onClick\"];\nconst _hoisted_4 = {\n  class: \"form-group\"\n};\nconst _hoisted_5 = {\n  class: \"form-label\"\n};\nconst _hoisted_6 = {\n  class: \"form-group\"\n};\nconst _hoisted_7 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_8 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_9 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_10 = {\n  key: 0\n};\nconst _hoisted_11 = {\n  class: \"form-group\"\n};\nconst _hoisted_12 = [\"value\"];\nconst _hoisted_13 = {\n  class: \"form-group\"\n};\nconst _hoisted_14 = {\n  class: \"flex\"\n};\nconst _hoisted_15 = {\n  key: 1\n};\nconst _hoisted_16 = {\n  class: \"form-group\"\n};\nconst _hoisted_17 = {\n  class: \"form-group\"\n};\nconst _hoisted_18 = {\n  key: 0,\n  class: \"text-red-500 text-xs mt-1\"\n};\nconst _hoisted_19 = {\n  class: \"form-group\"\n};\nconst _hoisted_20 = {\n  class: \"form-group\"\n};\nconst _hoisted_21 = {\n  class: \"mb-2\"\n};\nconst _hoisted_22 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_23 = {\n  class: \"form-text\"\n};\nconst _hoisted_24 = {\n  class: \"form-group\"\n};\nconst _hoisted_25 = [\"value\"];\nconst _hoisted_26 = {\n  class: \"form-group\"\n};\nconst _hoisted_27 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_28 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_29 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_30 = {\n  key: 0,\n  class: \"mt-3\"\n};\nconst _hoisted_31 = {\n  class: \"grid grid-cols-2 gap-4\"\n};\nconst _hoisted_32 = {\n  class: \"form-group\"\n};\nconst _hoisted_33 = {\n  class: \"form-group\"\n};\nconst _hoisted_34 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_35 = {\n  class: \"form-group\"\n};\nconst _hoisted_36 = [\"value\"];\nconst _hoisted_37 = {\n  class: \"form-group\"\n};\nconst _hoisted_38 = {\n  class: \"form-group\"\n};\nconst _hoisted_39 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_40 = {\n  class: \"form-group\"\n};\nconst _hoisted_41 = [\"value\"];\nconst _hoisted_42 = {\n  class: \"form-group\"\n};\nconst _hoisted_43 = {\n  class: \"form-group\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_CustomCheckbox = _resolveComponent(\"CustomCheckbox\");\n  const _component_StatusBadge = _resolveComponent(\"StatusBadge\");\n  const _component_PasswordStrengthMeter = _resolveComponent(\"PasswordStrengthMeter\");\n  const _component_BaseModal = _resolveComponent(\"BaseModal\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createCommentVNode(\" 操作按钮 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"button\", {\n    class: \"btn-outline\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.showEmergencyReset && $options.showEmergencyReset(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'exclamation-triangle'],\n    class: \"mr-2 text-red-600\"\n  }), _cache[33] || (_cache[33] = _createElementVNode(\"span\", null, \"紧急重置\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"btn-outline\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.openBatchUpdateModal && $options.openBatchUpdateModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'key'],\n    class: \"mr-2\"\n  }), _cache[34] || (_cache[34] = _createElementVNode(\"span\", null, \"批量更新密码\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"btn-outline\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.openBatchApplyModal && $options.openBatchApplyModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"mr-2\"\n  }), _cache[35] || (_cache[35] = _createElementVNode(\"span\", null, \"批量应用策略\", -1 /* HOISTED */))])]), _createCommentVNode(\" 主机列表 \"), _createElementVNode(\"table\", _hoisted_2, [_createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, [_createVNode(_component_CustomCheckbox, {\n    modelValue: $data.selectAll,\n    \"onUpdate:modelValue\": [_cache[3] || (_cache[3] = $event => $data.selectAll = $event), $options.toggleSelectAll]\n  }, {\n    default: _withCtx(() => _cache[36] || (_cache[36] = [_createTextVNode(\" 主机名 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _cache[37] || (_cache[37] = _createElementVNode(\"th\", null, \"IP地址\", -1 /* HOISTED */)), _cache[38] || (_cache[38] = _createElementVNode(\"th\", null, \"状态\", -1 /* HOISTED */)), _cache[39] || (_cache[39] = _createElementVNode(\"th\", null, \"操作\", -1 /* HOISTED */))])]), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: host.id\n    }, [_createElementVNode(\"td\", null, [_createVNode(_component_CustomCheckbox, {\n      modelValue: host.selected,\n      \"onUpdate:modelValue\": $event => host.selected = $event\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name), 1 /* TEXT */)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"td\", null, _toDisplayString(host.ip), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createVNode(_component_StatusBadge, {\n      type: host.status\n    }, null, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"td\", null, [_createElementVNode(\"button\", {\n      class: \"text-blue-600 hover:text-blue-800\",\n      onClick: $event => $options.openChangePasswordModal(host)\n    }, \" 修改密码 \", 8 /* PROPS */, _hoisted_3)])]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 修改密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.changePasswordModal.show,\n    \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.changePasswordModal.show = $event),\n    title: \"修改密码\",\n    \"confirm-text\": \"确认更新\",\n    onConfirm: $options.updatePassword,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"label\", _hoisted_5, \"服务器: \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_6, [_cache[42] || (_cache[42] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"label\", _hoisted_8, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.changePasswordModal.method = $event),\n      value: \"auto\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.changePasswordModal.method]]), _cache[40] || (_cache[40] = _createElementVNode(\"span\", null, \"自动生成\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_9, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.changePasswordModal.method = $event),\n      value: \"manual\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.changePasswordModal.method]]), _cache[41] || (_cache[41] = _createElementVNode(\"span\", null, \"手动输入\", -1 /* HOISTED */))])])]), $data.changePasswordModal.method === 'auto' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_cache[43] || (_cache[43] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.changePasswordModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_12);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.changePasswordModal.policyId]])]), _createElementVNode(\"div\", _hoisted_13, [_cache[44] || (_cache[44] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_14, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.changePasswordModal.generatedPassword = $event),\n      class: \"form-control rounded-r-none\",\n      readonly: \"\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.changePasswordModal.generatedPassword]]), _createElementVNode(\"button\", {\n      class: \"bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded-r-md\",\n      onClick: _cache[8] || (_cache[8] = (...args) => $options.generatePassword && $options.generatePassword(...args))\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt']\n    })])]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.generatedPassword\n    }, null, 8 /* PROPS */, [\"password\"])])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_cache[45] || (_cache[45] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"新密码\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"password\",\n      \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.changePasswordModal.newPassword = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.changePasswordModal.newPassword]]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.newPassword\n    }, null, 8 /* PROPS */, [\"password\"])]), _createElementVNode(\"div\", _hoisted_17, [_cache[46] || (_cache[46] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"确认密码\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"password\",\n      \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.changePasswordModal.confirmPassword = $event),\n      class: _normalizeClass([\"form-control\", {\n        'border-red-500': $options.passwordMismatch\n      }])\n    }, null, 2 /* CLASS */), [[_vModelText, $data.changePasswordModal.confirmPassword]]), $options.passwordMismatch ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, \" 两次输入的密码不一致 \")) : _createCommentVNode(\"v-if\", true)])])), _createElementVNode(\"div\", _hoisted_19, [_cache[50] || (_cache[50] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.executeImmediately,\n      \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $data.changePasswordModal.executeImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[47] || (_cache[47] = [_createTextVNode(\" 立即执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.saveHistory,\n      \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $data.changePasswordModal.saveHistory = $event)\n    }, {\n      default: _withCtx(() => _cache[48] || (_cache[48] = [_createTextVNode(\" 保存密码历史记录 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.logAudit,\n      \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.changePasswordModal.logAudit = $event)\n    }, {\n      default: _withCtx(() => _cache[49] || (_cache[49] = [_createTextVNode(\" 记录审计日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量更新密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchUpdateModal.show,\n    \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.batchUpdateModal.show = $event),\n    title: \"批量更新密码\",\n    \"confirm-text\": \"开始更新\",\n    size: \"lg\",\n    onConfirm: $options.batchUpdatePasswords,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_20, [_cache[52] || (_cache[52] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatch,\n      \"onUpdate:modelValue\": [_cache[15] || (_cache[15] = $event => $data.selectAllBatch = $event), $options.toggleSelectAllBatch]\n    }, {\n      default: _withCtx(() => _cache[51] || (_cache[51] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_22, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchUpdateModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchUpdateModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_23, \"已选择 \" + _toDisplayString($options.selectedHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_24, [_cache[53] || (_cache[53] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.batchUpdateModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_25);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchUpdateModal.policyId]])]), _createElementVNode(\"div\", _hoisted_26, [_cache[58] || (_cache[58] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"label\", _hoisted_28, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"immediate\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[54] || (_cache[54] = _createElementVNode(\"span\", null, \"立即执行\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_29, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[18] || (_cache[18] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"scheduled\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[55] || (_cache[55] = _createElementVNode(\"span\", null, \"定时执行\", -1 /* HOISTED */))])]), $data.batchUpdateModal.executionTime === 'scheduled' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_30, [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", null, [_cache[56] || (_cache[56] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"日期\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"date\",\n      \"onUpdate:modelValue\": _cache[19] || (_cache[19] = $event => $data.batchUpdateModal.scheduledDate = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledDate]])]), _createElementVNode(\"div\", null, [_cache[57] || (_cache[57] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"时间\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"time\",\n      \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $data.batchUpdateModal.scheduledTime = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledTime]])])])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_32, [_cache[62] || (_cache[62] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[21] || (_cache[21] = $event => $data.batchUpdateModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[59] || (_cache[59] = [_createTextVNode(\" 忽略错误继续执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.detailedLog,\n      \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $data.batchUpdateModal.detailedLog = $event)\n    }, {\n      default: _withCtx(() => _cache[60] || (_cache[60] = [_createTextVNode(\" 记录详细日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.sendNotification,\n      \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.batchUpdateModal.sendNotification = $event)\n    }, {\n      default: _withCtx(() => _cache[61] || (_cache[61] = [_createTextVNode(\" 执行完成后发送通知 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量应用策略弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchApplyModal.show,\n    \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $data.batchApplyModal.show = $event),\n    title: \"批量应用密码策略\",\n    \"confirm-text\": \"应用策略\",\n    onConfirm: $options.batchApplyPolicy,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_33, [_cache[63] || (_cache[63] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_34, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchApplyModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchApplyModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_35, [_cache[64] || (_cache[64] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.batchApplyModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name), 9 /* TEXT, PROPS */, _hoisted_36);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchApplyModal.policyId]])]), _createElementVNode(\"div\", _hoisted_37, [_cache[67] || (_cache[67] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.updateImmediately,\n      \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.batchApplyModal.updateImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[65] || (_cache[65] = [_createTextVNode(\" 立即更新密码以符合策略 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.applyOnNextUpdate,\n      \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.batchApplyModal.applyOnNextUpdate = $event)\n    }, {\n      default: _withCtx(() => _cache[66] || (_cache[66] = [_createTextVNode(\" 下次密码更新时应用 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 紧急重置密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.emergencyResetModal.show,\n    \"onUpdate:modelValue\": _cache[32] || (_cache[32] = $event => $data.emergencyResetModal.show = $event),\n    title: \"紧急密码重置\",\n    \"confirm-text\": \"立即重置\",\n    icon: \"exclamation-triangle\",\n    danger: \"\",\n    onConfirm: $options.emergencyReset,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_cache[73] || (_cache[73] = _createElementVNode(\"div\", {\n      class: \"bg-red-50 text-red-700 p-3 rounded-md mb-4\"\n    }, [_createElementVNode(\"p\", null, \"紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_38, [_cache[68] || (_cache[68] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_39, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.emergencyResetModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.emergencyResetModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_40, [_cache[69] || (_cache[69] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用紧急策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[29] || (_cache[29] = $event => $data.emergencyResetModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.emergencyPolicies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_41);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.policyId]])]), _createElementVNode(\"div\", _hoisted_42, [_cache[71] || (_cache[71] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"操作原因\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[30] || (_cache[30] = $event => $data.emergencyResetModal.reason = $event),\n      class: \"form-select\"\n    }, _cache[70] || (_cache[70] = [_createElementVNode(\"option\", {\n      value: \"security_incident\"\n    }, \"安全事件响应\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"password_leak\"\n    }, \"密码泄露\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"abnormal_access\"\n    }, \"异常访问\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"compliance\"\n    }, \"合规要求\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"other\"\n    }, \"其他原因\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.reason]])]), _createElementVNode(\"div\", _hoisted_43, [_cache[72] || (_cache[72] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"附加说明\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n      \"onUpdate:modelValue\": _cache[31] || (_cache[31] = $event => $data.emergencyResetModal.description = $event),\n      class: \"form-control\",\n      rows: \"2\",\n      placeholder: \"请输入重置原因详细说明\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.emergencyResetModal.description]])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "onClick", "_cache", "args", "$options", "showEmergencyReset", "_createVNode", "_component_font_awesome_icon", "icon", "openBatchUpdateModal", "openBatchApplyModal", "_hoisted_2", "_component_CustomCheckbox", "modelValue", "$data", "selectAll", "$event", "toggleSelectAll", "default", "_withCtx", "_createTextVNode", "_", "_Fragment", "_renderList", "_ctx", "hosts", "host", "id", "selected", "_toDisplayString", "name", "ip", "_component_StatusBadge", "type", "status", "openChangePasswordModal", "_hoisted_3", "_component_BaseModal", "changePasswordModal", "show", "title", "onConfirm", "updatePassword", "loading", "processing", "_hoisted_4", "_hoisted_5", "currentHost", "_hoisted_6", "_hoisted_7", "_hoisted_8", "method", "value", "_hoisted_9", "_hoisted_10", "_hoisted_11", "policyId", "policies", "policy", "<PERSON><PERSON><PERSON><PERSON>", "expiryDays", "_hoisted_12", "_hoisted_13", "_hoisted_14", "generatedPassword", "readonly", "generatePassword", "_component_PasswordStrengthMeter", "password", "_hoisted_15", "_hoisted_16", "newPassword", "_hoisted_17", "confirmPassword", "_normalizeClass", "passwordMismatch", "_hoisted_18", "_hoisted_19", "executeImmediately", "saveHistory", "logAudit", "batchUpdateModal", "size", "batchUpdatePasswords", "_hoisted_20", "_hoisted_21", "selectAllBatch", "toggleSelectAllBatch", "_hoisted_22", "_createBlock", "selectedHosts", "_hoisted_23", "selectedHostsCount", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "executionTime", "_hoisted_29", "_hoisted_30", "_hoisted_31", "scheduledDate", "scheduledTime", "_hoisted_32", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "batchApplyPolicy", "_hoisted_33", "_hoisted_34", "selectedHostsList", "_hoisted_35", "_hoisted_36", "_hoisted_37", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "danger", "emergencyReset", "_hoisted_38", "_hoisted_39", "_hoisted_40", "emergencyPolicies", "_hoisted_41", "_hoisted_42", "reason", "_hoisted_43", "description", "rows", "placeholder"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"flex space-x-3 mb-6\">\r\n      <button class=\"btn-outline\" @click=\"showEmergencyReset\">\r\n        <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2 text-red-600\" />\r\n        <span>紧急重置</span>\r\n      </button>\r\n      <button class=\"btn-outline\" @click=\"openBatchUpdateModal\">\r\n        <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n        <span>批量更新密码</span>\r\n      </button>\r\n      <button class=\"btn-outline\" @click=\"openBatchApplyModal\">\r\n        <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n        <span>批量应用策略</span>\r\n      </button>\r\n    </div>\r\n    \r\n    <!-- 主机列表 -->\r\n    <table class=\"data-table\">\r\n      <thead>\r\n        <tr>\r\n          <th>\r\n            <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n              主机名\r\n            </CustomCheckbox>\r\n          </th>\r\n          <th>IP地址</th>\r\n          <th>状态</th>\r\n          <th>操作</th>\r\n        </tr>\r\n      </thead>\r\n      <tbody>\r\n        <tr v-for=\"host in hosts\" :key=\"host.id\">\r\n          <td>\r\n            <CustomCheckbox v-model=\"host.selected\">\r\n              {{ host.name }}\r\n            </CustomCheckbox>\r\n          </td>\r\n          <td>{{ host.ip }}</td>\r\n          <td>\r\n            <StatusBadge :type=\"host.status\" />\r\n          </td>\r\n          <td>\r\n            <button \r\n              class=\"text-blue-600 hover:text-blue-800\"\r\n              @click=\"openChangePasswordModal(host)\"\r\n            >\r\n              修改密码\r\n            </button>\r\n          </td>\r\n        </tr>\r\n      </tbody>\r\n    </table>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal\r\n      v-model=\"changePasswordModal.show\"\r\n      title=\"修改密码\"\r\n      confirm-text=\"确认更新\"\r\n      @confirm=\"updatePassword\"\r\n      :loading=\"processing\"\r\n    >\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">服务器: {{ currentHost.name }}</label>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input \r\n              type=\"radio\" \r\n              v-model=\"changePasswordModal.method\" \r\n              value=\"auto\" \r\n              class=\"mr-2\"\r\n            >\r\n            <span>自动生成</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input \r\n              type=\"radio\"\r\n              v-model=\"changePasswordModal.method\" \r\n              value=\"manual\" \r\n              class=\"mr-2\"\r\n            >\r\n            <span>手动输入</span>\r\n          </label>\r\n        </div>\r\n      </div>\r\n      \r\n      <div v-if=\"changePasswordModal.method === 'auto'\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">选择密码策略</label>\r\n          <select v-model=\"changePasswordModal.policyId\" class=\"form-select\">\r\n            <option \r\n              v-for=\"policy in policies\" \r\n              :key=\"policy.id\" \r\n              :value=\"policy.id\"\r\n            >\r\n              {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n            </option>\r\n          </select>\r\n        </div>\r\n        \r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <div class=\"flex\">\r\n            <input \r\n              type=\"text\" \r\n              v-model=\"changePasswordModal.generatedPassword\" \r\n              class=\"form-control rounded-r-none\" \r\n              readonly\r\n            >\r\n            <button \r\n              class=\"bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded-r-md\"\r\n              @click=\"generatePassword\"\r\n            >\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" />\r\n            </button>\r\n          </div>\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.generatedPassword\" />\r\n        </div>\r\n      </div>\r\n      \r\n      <div v-else>\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <input \r\n            type=\"password\" \r\n            v-model=\"changePasswordModal.newPassword\" \r\n            class=\"form-control\"\r\n          >\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n        </div>\r\n        \r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <input \r\n            type=\"password\" \r\n            v-model=\"changePasswordModal.confirmPassword\" \r\n            class=\"form-control\"\r\n            :class=\"{ 'border-red-500': passwordMismatch }\"\r\n          >\r\n          <div v-if=\"passwordMismatch\" class=\"text-red-500 text-xs mt-1\">\r\n            两次输入的密码不一致\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行选项</label>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          立即执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          保存密码历史记录\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          记录审计日志\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal\r\n      v-model=\"batchUpdateModal.show\"\r\n      title=\"批量更新密码\"\r\n      confirm-text=\"开始更新\"\r\n      size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\"\r\n      :loading=\"processing\"\r\n    >\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox \r\n            v-for=\"host in hosts\" \r\n            :key=\"host.id\"\r\n            v-model=\"batchUpdateModal.selectedHosts[host.id]\"\r\n          >\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option \r\n            v-for=\"policy in policies\" \r\n            :key=\"policy.id\" \r\n            :value=\"policy.id\"\r\n          >\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input \r\n              type=\"radio\" \r\n              v-model=\"batchUpdateModal.executionTime\" \r\n              value=\"immediate\" \r\n              class=\"mr-2\"\r\n            >\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input \r\n              type=\"radio\"\r\n              v-model=\"batchUpdateModal.executionTime\" \r\n              value=\"scheduled\" \r\n              class=\"mr-2\"\r\n            >\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n        \r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input \r\n                type=\"date\" \r\n                v-model=\"batchUpdateModal.scheduledDate\" \r\n                class=\"form-control\"\r\n              >\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input \r\n                type=\"time\" \r\n                v-model=\"batchUpdateModal.scheduledTime\" \r\n                class=\"form-control\"\r\n              >\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal\r\n      v-model=\"batchApplyModal.show\"\r\n      title=\"批量应用密码策略\"\r\n      confirm-text=\"应用策略\"\r\n      @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\"\r\n    >\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox \r\n            v-for=\"host in selectedHostsList\" \r\n            :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\"\r\n          >\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option \r\n            v-for=\"policy in policies\" \r\n            :key=\"policy.id\" \r\n            :value=\"policy.id\"\r\n          >\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal\r\n      v-model=\"emergencyResetModal.show\"\r\n      title=\"紧急密码重置\"\r\n      confirm-text=\"立即重置\"\r\n      icon=\"exclamation-triangle\"\r\n      danger\r\n      @confirm=\"emergencyReset\"\r\n      :loading=\"processing\"\r\n    >\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox \r\n            v-for=\"host in selectedHostsList\" \r\n            :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\"\r\n          >\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option \r\n            v-for=\"policy in emergencyPolicies\" \r\n            :key=\"policy.id\" \r\n            :value=\"policy.id\"\r\n          >\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n      \r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea \r\n          v-model=\"emergencyResetModal.description\" \r\n          class=\"form-control\" \r\n          rows=\"2\" \r\n          placeholder=\"请输入重置原因详细说明\"\r\n        ></textarea>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      \r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n      \r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n      \r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n      \r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n    \r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword && \r\n        this.changePasswordModal.confirmPassword && \r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n    \r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n    \r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n    \r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n    \r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n    \r\n    openChangePasswordModal(host) {\r\n      this.currentHost = host\r\n      this.changePasswordModal.show = true\r\n      this.generatePassword()\r\n    },\r\n    \r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n      \r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n      \r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n    \r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n      \r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n    \r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n      \r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n    \r\n    generatePassword() {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n      \r\n      // 获取所选策略的最小长度\r\n      const policy = this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policy ? policy.minLength : 12\r\n      \r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n      \r\n      this.changePasswordModal.generatedPassword = password\r\n    },\r\n    \r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n      \r\n      this.processing = true\r\n      \r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto' \r\n          ? this.changePasswordModal.generatedPassword \r\n          : this.changePasswordModal.newPassword\r\n        \r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          newStatus: 'normal'\r\n        })\r\n        \r\n        this.changePasswordModal.show = false\r\n        \r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n    \r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n      \r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n      \r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n      \r\n      this.processing = true\r\n      \r\n      try {\r\n        await this.$store.dispatch('batchUpdateHostPasswords', {\r\n          hostIds: selectedHostIds,\r\n          status: 'normal'\r\n        })\r\n        \r\n        this.batchUpdateModal.show = false\r\n        \r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n    \r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n      \r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n      \r\n      this.processing = true\r\n      \r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n        \r\n        this.batchApplyModal.show = false\r\n        \r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n    \r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n      \r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n      \r\n      this.processing = true\r\n      \r\n      try {\r\n        await this.$store.dispatch('batchUpdateHostPasswords', {\r\n          hostIds: selectedHostIds,\r\n          status: 'normal'\r\n        })\r\n        \r\n        this.emergencyResetModal.show = false\r\n        \r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script> "], "mappings": ";;EAGSA,KAAK,EAAC;AAAqB;;EAgBzBA,KAAK,EAAC;AAAY;mBAnB7B;;EA+DWA,KAAK,EAAC;AAAY;;EACdA,KAAK,EAAC;AAAY;;EAGtBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EASzBA,KAAK,EAAC;AAAmB;;EA/E1CC,GAAA;AAAA;;EA4FaD,KAAK,EAAC;AAAY;oBA5F/B;;EAyGaA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EA3G3BC,GAAA;AAAA;;EA8HaD,KAAK,EAAC;AAAY;;EAUlBA,KAAK,EAAC;AAAY;;EAxI/BC,GAAA;EAgJuCD,KAAK,EAAC;;;EAMlCA,KAAK,EAAC;AAAY;;EAuBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EASxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAY;oBAhM7B;;EA6MWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EASzBA,KAAK,EAAC;AAAmB;;EAzN1CC,GAAA;EAoOmED,KAAK,EAAC;;;EAC1DA,KAAK,EAAC;AAAwB;;EAqBlCA,KAAK,EAAC;AAAY;;EAsBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAWxEA,KAAK,EAAC;AAAY;oBA7R7B;;EA0SWA,KAAK,EAAC;AAAY;;EAyBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAWxEA,KAAK,EAAC;AAAY;oBAhV7B;;EA6VWA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;;;;;;uBAvW3BE,mBAAA,CAiXM,cAhXJC,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNC,UAaM,GAZJD,mBAAA,CAGS;IAHDJ,KAAK,EAAC,aAAa;IAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,kBAAA,IAAAD,QAAA,CAAAC,kBAAA,IAAAF,IAAA,CAAkB;MACpDG,YAAA,CAAuFC,4BAAA;IAAnEC,IAAI,EAAE,+BAA+B;IAAEb,KAAK,EAAC;kCACjEI,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGS;IAHDJ,KAAK,EAAC,aAAa;IAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAK,oBAAA,IAAAL,QAAA,CAAAK,oBAAA,IAAAN,IAAA,CAAoB;MACtDG,YAAA,CAAyDC,4BAAA;IAArCC,IAAI,EAAE,cAAc;IAAEb,KAAK,EAAC;kCAChDI,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAGS;IAHDJ,KAAK,EAAC,aAAa;IAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAM,mBAAA,IAAAN,QAAA,CAAAM,mBAAA,IAAAP,IAAA,CAAmB;MACrDG,YAAA,CAAgEC,4BAAA;IAA5CC,IAAI,EAAE,qBAAqB;IAAEb,KAAK,EAAC;kCACvDI,mBAAA,CAAmB,cAAb,QAAM,qB,KAIhBD,mBAAA,UAAa,EACbC,mBAAA,CAkCQ,SAlCRY,UAkCQ,GAjCNZ,mBAAA,CAWQ,gBAVNA,mBAAA,CASK,aARHA,mBAAA,CAIK,aAHHO,YAAA,CAEiBM,yBAAA;IAzB7BC,UAAA,EAuBqCC,KAAA,CAAAC,SAAS;IAvB9C,wB,oCAuBqCD,KAAA,CAAAC,SAAS,GAAAC,MAAA,GAAsBZ,QAAA,CAAAa,eAAe;;IAvBnFC,OAAA,EAAAC,QAAA,CAuBqF,MAEzEjB,MAAA,SAAAA,MAAA,QAzBZkB,gBAAA,CAuBqF,OAEzE,E;IAzBZC,CAAA;0FA2BUtB,mBAAA,CAAa,YAAT,MAAI,sB,4BACRA,mBAAA,CAAW,YAAP,IAAE,sB,4BACNA,mBAAA,CAAW,YAAP,IAAE,qB,KAGVA,mBAAA,CAoBQ,iB,kBAnBNF,mBAAA,CAkBKyB,SAAA,QAnDbC,WAAA,CAiC2BC,IAAA,CAAAC,KAAK,EAAbC,IAAI;yBAAf7B,mBAAA,CAkBK;MAlBsBD,GAAG,EAAE8B,IAAI,CAACC;QACnC5B,mBAAA,CAIK,aAHHO,YAAA,CAEiBM,yBAAA;MArC7BC,UAAA,EAmCqCa,IAAI,CAACE,QAAQ;MAnClD,uBAAAZ,MAAA,IAmCqCU,IAAI,CAACE,QAAQ,GAAAZ;;MAnClDE,OAAA,EAAAC,QAAA,CAoCc,MAAe,CApC7BC,gBAAA,CAAAS,gBAAA,CAoCiBH,IAAI,CAACI,IAAI,iB;MApC1BT,CAAA;kFAuCUtB,mBAAA,CAAsB,YAAA8B,gBAAA,CAAfH,IAAI,CAACK,EAAE,kBACdhC,mBAAA,CAEK,aADHO,YAAA,CAAmC0B,sBAAA;MAArBC,IAAI,EAAEP,IAAI,CAACQ;yCAE3BnC,mBAAA,CAOK,aANHA,mBAAA,CAKS;MAJPJ,KAAK,EAAC,mCAAmC;MACxCM,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAA+B,uBAAuB,CAACT,IAAI;OACrC,QAED,iBAjDZU,UAAA,E;sCAuDItC,mBAAA,YAAe,EACfQ,YAAA,CA0GY+B,oBAAA;IAlKhBxB,UAAA,EAyDeC,KAAA,CAAAwB,mBAAmB,CAACC,IAAI;IAzDvC,uBAAArC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAyDeF,KAAA,CAAAwB,mBAAmB,CAACC,IAAI,GAAAvB,MAAA;IACjCwB,KAAK,EAAC,MAAM;IACZ,cAAY,EAAC,MAAM;IAClBC,SAAO,EAAErC,QAAA,CAAAsC,cAAc;IACvBC,OAAO,EAAE7B,KAAA,CAAA8B;;IA7DhB1B,OAAA,EAAAC,QAAA,CA+DM,MAEM,CAFNpB,mBAAA,CAEM,OAFN8C,UAEM,GADJ9C,mBAAA,CAA6D,SAA7D+C,UAA6D,EAAnC,OAAK,GAAAjB,gBAAA,CAAGf,KAAA,CAAAiC,WAAW,CAACjB,IAAI,iB,GAGpD/B,mBAAA,CAsBM,OAtBNiD,UAsBM,G,4BArBJjD,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCI,mBAAA,CAmBM,OAnBNkD,UAmBM,GAlBJlD,mBAAA,CAQQ,SARRmD,UAQQ,G,gBAPNnD,mBAAA,CAKC;MAJCkC,IAAI,EAAC,OAAO;MAxE1B,uBAAA/B,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAyEuBF,KAAA,CAAAwB,mBAAmB,CAACa,MAAM,GAAAnC,MAAA;MACnCoC,KAAK,EAAC,MAAM;MACZzD,KAAK,EAAC;oDAFGmB,KAAA,CAAAwB,mBAAmB,CAACa,MAAM,E,+BAIrCpD,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAQQ,SARRsD,UAQQ,G,gBAPNtD,mBAAA,CAKC;MAJCkC,IAAI,EAAC,OAAO;MAjF1B,uBAAA/B,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAkFuBF,KAAA,CAAAwB,mBAAmB,CAACa,MAAM,GAAAnC,MAAA;MACnCoC,KAAK,EAAC,QAAQ;MACdzD,KAAK,EAAC;oDAFGmB,KAAA,CAAAwB,mBAAmB,CAACa,MAAM,E,+BAIrCpD,mBAAA,CAAiB,cAAX,MAAI,qB,OAKLe,KAAA,CAAAwB,mBAAmB,CAACa,MAAM,e,cAArCtD,mBAAA,CAgCM,OA3HZyD,WAAA,GA4FQvD,mBAAA,CAWM,OAXNwD,WAWM,G,4BAVJxD,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCI,mBAAA,CAQS;MAtGnB,uBAAAG,MAAA,QAAAA,MAAA,MAAAc,MAAA,IA8F2BF,KAAA,CAAAwB,mBAAmB,CAACkB,QAAQ,GAAAxC,MAAA;MAAErB,KAAK,EAAC;2BACnDE,mBAAA,CAMSyB,SAAA,QArGrBC,WAAA,CAgG+BC,IAAA,CAAAiC,QAAQ,EAAlBC,MAAM;2BADf7D,mBAAA,CAMS;QAJND,GAAG,EAAE8D,MAAM,CAAC/B,EAAE;QACdyB,KAAK,EAAEM,MAAM,CAAC/B;0BAEZ+B,MAAM,CAAC5B,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAG6B,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA9B,gBAAA,CAAG6B,MAAM,CAACE,UAAU,IAAG,KAC9E,uBArGZC,WAAA;6EA8F2B/C,KAAA,CAAAwB,mBAAmB,CAACkB,QAAQ,E,KAW/CzD,mBAAA,CAiBM,OAjBN+D,WAiBM,G,4BAhBJ/D,mBAAA,CAAuC;MAAhCJ,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BI,mBAAA,CAaM,OAbNgE,WAaM,G,gBAZJhE,mBAAA,CAKC;MAJCkC,IAAI,EAAC,MAAM;MA7GzB,uBAAA/B,MAAA,QAAAA,MAAA,MAAAc,MAAA,IA8GuBF,KAAA,CAAAwB,mBAAmB,CAAC0B,iBAAiB,GAAAhD,MAAA;MAC9CrB,KAAK,EAAC,6BAA6B;MACnCsE,QAAQ,EAAR;mDAFSnD,KAAA,CAAAwB,mBAAmB,CAAC0B,iBAAiB,E,GAIhDjE,mBAAA,CAKS;MAJPJ,KAAK,EAAC,sDAAsD;MAC3DM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAA8D,gBAAA,IAAA9D,QAAA,CAAA8D,gBAAA,IAAA/D,IAAA,CAAgB;QAExBG,YAAA,CAAiDC,4BAAA;MAA7BC,IAAI,EAAE;IAAmB,G,KAGjDF,YAAA,CAA2E6D,gCAAA;MAAnDC,QAAQ,EAAEtD,KAAA,CAAAwB,mBAAmB,CAAC0B;gEAI1DnE,mBAAA,CAuBM,OApJZwE,WAAA,GA8HQtE,mBAAA,CAQM,OARNuE,WAQM,G,4BAPJvE,mBAAA,CAAqC;MAA9BJ,KAAK,EAAC;IAAY,GAAC,KAAG,sB,gBAC7BI,mBAAA,CAIC;MAHCkC,IAAI,EAAC,UAAU;MAjI3B,uBAAA/B,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAkIqBF,KAAA,CAAAwB,mBAAmB,CAACiC,WAAW,GAAAvD,MAAA;MACxCrB,KAAK,EAAC;mDADGmB,KAAA,CAAAwB,mBAAmB,CAACiC,WAAW,E,GAG1CjE,YAAA,CAAqE6D,gCAAA;MAA7CC,QAAQ,EAAEtD,KAAA,CAAAwB,mBAAmB,CAACiC;6CAGxDxE,mBAAA,CAWM,OAXNyE,WAWM,G,4BAVJzE,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BI,mBAAA,CAKC;MAJCkC,IAAI,EAAC,UAAU;MA3I3B,uBAAA/B,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4IqBF,KAAA,CAAAwB,mBAAmB,CAACmC,eAAe,GAAAzD,MAAA;MAC5CrB,KAAK,EA7IjB+E,eAAA,EA6IkB,cAAc;QAAA,kBACQtE,QAAA,CAAAuE;MAAgB;4CAFnC7D,KAAA,CAAAwB,mBAAmB,CAACmC,eAAe,E,GAInCrE,QAAA,CAAAuE,gBAAgB,I,cAA3B9E,mBAAA,CAEM,OAFN+E,WAEM,EAFyD,cAE/D,KAlJV9E,mBAAA,e,MAsJMC,mBAAA,CAWM,OAXN8E,WAWM,G,4BAVJ9E,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BW,YAAA,CAEiBM,yBAAA;MA1JzBC,UAAA,EAwJiCC,KAAA,CAAAwB,mBAAmB,CAACwC,kBAAkB;MAxJvE,uBAAA5E,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAwJiCF,KAAA,CAAAwB,mBAAmB,CAACwC,kBAAkB,GAAA9D,MAAA;;MAxJvEE,OAAA,EAAAC,QAAA,CAwJyE,MAEjEjB,MAAA,SAAAA,MAAA,QA1JRkB,gBAAA,CAwJyE,QAEjE,E;MA1JRC,CAAA;uCA2JQf,YAAA,CAEiBM,yBAAA;MA7JzBC,UAAA,EA2JiCC,KAAA,CAAAwB,mBAAmB,CAACyC,WAAW;MA3JhE,uBAAA7E,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA2JiCF,KAAA,CAAAwB,mBAAmB,CAACyC,WAAW,GAAA/D,MAAA;;MA3JhEE,OAAA,EAAAC,QAAA,CA2JkE,MAE1DjB,MAAA,SAAAA,MAAA,QA7JRkB,gBAAA,CA2JkE,YAE1D,E;MA7JRC,CAAA;uCA8JQf,YAAA,CAEiBM,yBAAA;MAhKzBC,UAAA,EA8JiCC,KAAA,CAAAwB,mBAAmB,CAAC0C,QAAQ;MA9J7D,uBAAA9E,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8JiCF,KAAA,CAAAwB,mBAAmB,CAAC0C,QAAQ,GAAAhE,MAAA;;MA9J7DE,OAAA,EAAAC,QAAA,CA8J+D,MAEvDjB,MAAA,SAAAA,MAAA,QAhKRkB,gBAAA,CA8J+D,UAEvD,E;MAhKRC,CAAA;;IAAAA,CAAA;6DAoKIvB,mBAAA,cAAiB,EACjBQ,YAAA,CAiGY+B,oBAAA;IAtQhBxB,UAAA,EAsKeC,KAAA,CAAAmE,gBAAgB,CAAC1C,IAAI;IAtKpC,uBAAArC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAsKeF,KAAA,CAAAmE,gBAAgB,CAAC1C,IAAI,GAAAvB,MAAA;IAC9BwB,KAAK,EAAC,QAAQ;IACd,cAAY,EAAC,MAAM;IACnB0C,IAAI,EAAC,IAAI;IACRzC,SAAO,EAAErC,QAAA,CAAA+E,oBAAoB;IAC7BxC,OAAO,EAAE7B,KAAA,CAAA8B;;IA3KhB1B,OAAA,EAAAC,QAAA,CA6KM,MAiBM,CAjBNpB,mBAAA,CAiBM,OAjBNqF,WAiBM,G,4BAhBJrF,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCI,mBAAA,CAIM,OAJNsF,WAIM,GAHJ/E,YAAA,CAEiBM,yBAAA;MAlL3BC,UAAA,EAgLmCC,KAAA,CAAAwE,cAAc;MAhLjD,wB,sCAgLmCxE,KAAA,CAAAwE,cAAc,GAAAtE,MAAA,GAAsBZ,QAAA,CAAAmF,oBAAoB;;MAhL3FrE,OAAA,EAAAC,QAAA,CAgL6F,MAEnFjB,MAAA,SAAAA,MAAA,QAlLVkB,gBAAA,CAgL6F,MAEnF,E;MAlLVC,CAAA;gEAoLQtB,mBAAA,CAQM,OARNyF,WAQM,I,kBAPJ3F,mBAAA,CAMiByB,SAAA,QA3L3BC,WAAA,CAsL2BC,IAAA,CAAAC,KAAK,EAAbC,IAAI;2BADb+D,YAAA,CAMiB7E,yBAAA;QAJdhB,GAAG,EAAE8B,IAAI,CAACC,EAAE;QAvLzBd,UAAA,EAwLqBC,KAAA,CAAAmE,gBAAgB,CAACS,aAAa,CAAChE,IAAI,CAACC,EAAE;QAxL3D,uBAAAX,MAAA,IAwLqBF,KAAA,CAAAmE,gBAAgB,CAACS,aAAa,CAAChE,IAAI,CAACC,EAAE,IAAAX;;QAxL3DE,OAAA,EAAAC,QAAA,CA0LY,MAAe,CA1L3BC,gBAAA,CAAAS,gBAAA,CA0LeH,IAAI,CAACI,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGH,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QA3LVV,CAAA;;sCA6LQtB,mBAAA,CAAyD,KAAzD4F,WAAyD,EAApC,MAAI,GAAA9D,gBAAA,CAAGzB,QAAA,CAAAwF,kBAAkB,IAAG,MAAI,gB,GAGvD7F,mBAAA,CAWM,OAXN8F,WAWM,G,4BAVJ9F,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BI,mBAAA,CAQS;MA1MjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAkMyBF,KAAA,CAAAmE,gBAAgB,CAACzB,QAAQ,GAAAxC,MAAA;MAAErB,KAAK,EAAC;2BAChDE,mBAAA,CAMSyB,SAAA,QAzMnBC,WAAA,CAoM6BC,IAAA,CAAAiC,QAAQ,EAAlBC,MAAM;2BADf7D,mBAAA,CAMS;QAJND,GAAG,EAAE8D,MAAM,CAAC/B,EAAE;QACdyB,KAAK,EAAEM,MAAM,CAAC/B;0BAEZ+B,MAAM,CAAC5B,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAG6B,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA9B,gBAAA,CAAG6B,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAzMVkC,WAAA;6EAkMyBhF,KAAA,CAAAmE,gBAAgB,CAACzB,QAAQ,E,KAW5CzD,mBAAA,CA2CM,OA3CNgG,WA2CM,G,4BA1CJhG,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BI,mBAAA,CAmBM,OAnBNiG,WAmBM,GAlBJjG,mBAAA,CAQQ,SARRkG,WAQQ,G,gBAPNlG,mBAAA,CAKC;MAJCkC,IAAI,EAAC,OAAO;MAlN1B,uBAAA/B,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAmNuBF,KAAA,CAAAmE,gBAAgB,CAACiB,aAAa,GAAAlF,MAAA;MACvCoC,KAAK,EAAC,WAAW;MACjBzD,KAAK,EAAC;oDAFGmB,KAAA,CAAAmE,gBAAgB,CAACiB,aAAa,E,+BAIzCnG,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAQQ,SARRoG,WAQQ,G,gBAPNpG,mBAAA,CAKC;MAJCkC,IAAI,EAAC,OAAO;MA3N1B,uBAAA/B,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4NuBF,KAAA,CAAAmE,gBAAgB,CAACiB,aAAa,GAAAlF,MAAA;MACvCoC,KAAK,EAAC,WAAW;MACjBzD,KAAK,EAAC;oDAFGmB,KAAA,CAAAmE,gBAAgB,CAACiB,aAAa,E,+BAIzCnG,mBAAA,CAAiB,cAAX,MAAI,qB,KAIHe,KAAA,CAAAmE,gBAAgB,CAACiB,aAAa,oB,cAAzCrG,mBAAA,CAmBM,OAnBNuG,WAmBM,GAlBJrG,mBAAA,CAiBM,OAjBNsG,WAiBM,GAhBJtG,mBAAA,CAOM,c,4BANJA,mBAAA,CAAoC;MAA7BJ,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BI,mBAAA,CAIC;MAHCkC,IAAI,EAAC,MAAM;MAzO3B,uBAAA/B,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA0OyBF,KAAA,CAAAmE,gBAAgB,CAACqB,aAAa,GAAAtF,MAAA;MACvCrB,KAAK,EAAC;mDADGmB,KAAA,CAAAmE,gBAAgB,CAACqB,aAAa,E,KAI3CvG,mBAAA,CAOM,c,4BANJA,mBAAA,CAAoC;MAA7BJ,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BI,mBAAA,CAIC;MAHCkC,IAAI,EAAC,MAAM;MAjP3B,uBAAA/B,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAkPyBF,KAAA,CAAAmE,gBAAgB,CAACsB,aAAa,GAAAvF,MAAA;MACvCrB,KAAK,EAAC;mDADGmB,KAAA,CAAAmE,gBAAgB,CAACsB,aAAa,E,WAlPvDzG,mBAAA,e,GA0PMC,mBAAA,CAWM,OAXNyG,WAWM,G,4BAVJzG,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BW,YAAA,CAEiBM,yBAAA;MA9PzBC,UAAA,EA4PiCC,KAAA,CAAAmE,gBAAgB,CAACwB,YAAY;MA5P9D,uBAAAvG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4PiCF,KAAA,CAAAmE,gBAAgB,CAACwB,YAAY,GAAAzF,MAAA;;MA5P9DE,OAAA,EAAAC,QAAA,CA4PgE,MAExDjB,MAAA,SAAAA,MAAA,QA9PRkB,gBAAA,CA4PgE,YAExD,E;MA9PRC,CAAA;uCA+PQf,YAAA,CAEiBM,yBAAA;MAjQzBC,UAAA,EA+PiCC,KAAA,CAAAmE,gBAAgB,CAACyB,WAAW;MA/P7D,uBAAAxG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA+PiCF,KAAA,CAAAmE,gBAAgB,CAACyB,WAAW,GAAA1F,MAAA;;MA/P7DE,OAAA,EAAAC,QAAA,CA+P+D,MAEvDjB,MAAA,SAAAA,MAAA,QAjQRkB,gBAAA,CA+P+D,UAEvD,E;MAjQRC,CAAA;uCAkQQf,YAAA,CAEiBM,yBAAA;MApQzBC,UAAA,EAkQiCC,KAAA,CAAAmE,gBAAgB,CAAC0B,gBAAgB;MAlQlE,uBAAAzG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAkQiCF,KAAA,CAAAmE,gBAAgB,CAAC0B,gBAAgB,GAAA3F,MAAA;;MAlQlEE,OAAA,EAAAC,QAAA,CAkQoE,MAE5DjB,MAAA,SAAAA,MAAA,QApQRkB,gBAAA,CAkQoE,aAE5D,E;MApQRC,CAAA;;IAAAA,CAAA;6DAwQIvB,mBAAA,cAAiB,EACjBQ,YAAA,CA0CY+B,oBAAA;IAnThBxB,UAAA,EA0QeC,KAAA,CAAA8F,eAAe,CAACrE,IAAI;IA1QnC,uBAAArC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA0QeF,KAAA,CAAA8F,eAAe,CAACrE,IAAI,GAAAvB,MAAA;IAC7BwB,KAAK,EAAC,UAAU;IAChB,cAAY,EAAC,MAAM;IAClBC,SAAO,EAAErC,QAAA,CAAAyG,gBAAgB;IACzBlE,OAAO,EAAE7B,KAAA,CAAA8B;;IA9QhB1B,OAAA,EAAAC,QAAA,CAgRM,MAWM,CAXNpB,mBAAA,CAWM,OAXN+G,WAWM,G,4BAVJ/G,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCI,mBAAA,CAQM,OARNgH,WAQM,I,kBAPJlH,mBAAA,CAMiByB,SAAA,QAzR3BC,WAAA,CAoR2BnB,QAAA,CAAA4G,iBAAiB,EAAzBtF,IAAI;2BADb+D,YAAA,CAMiB7E,yBAAA;QAJdhB,GAAG,EAAE8B,IAAI,CAACC,EAAE;QArRzBd,UAAA,EAsRqBC,KAAA,CAAA8F,eAAe,CAAClB,aAAa,CAAChE,IAAI,CAACC,EAAE;QAtR1D,uBAAAX,MAAA,IAsRqBF,KAAA,CAAA8F,eAAe,CAAClB,aAAa,CAAChE,IAAI,CAACC,EAAE,IAAAX;;QAtR1DE,OAAA,EAAAC,QAAA,CAwRY,MAAe,CAxR3BC,gBAAA,CAAAS,gBAAA,CAwReH,IAAI,CAACI,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGH,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QAzRVV,CAAA;;wCA6RMtB,mBAAA,CAWM,OAXNkH,WAWM,G,4BAVJlH,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCI,mBAAA,CAQS;MAvSjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA+RyBF,KAAA,CAAA8F,eAAe,CAACpD,QAAQ,GAAAxC,MAAA;MAAErB,KAAK,EAAC;2BAC/CE,mBAAA,CAMSyB,SAAA,QAtSnBC,WAAA,CAiS6BC,IAAA,CAAAiC,QAAQ,EAAlBC,MAAM;2BADf7D,mBAAA,CAMS;QAJND,GAAG,EAAE8D,MAAM,CAAC/B,EAAE;QACdyB,KAAK,EAAEM,MAAM,CAAC/B;0BAEZ+B,MAAM,CAAC5B,IAAI,wBArS1BoF,WAAA;6EA+RyBpG,KAAA,CAAA8F,eAAe,CAACpD,QAAQ,E,KAW3CzD,mBAAA,CAQM,OARNoH,WAQM,G,4BAPJpH,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BW,YAAA,CAEiBM,yBAAA;MA9SzBC,UAAA,EA4SiCC,KAAA,CAAA8F,eAAe,CAACQ,iBAAiB;MA5SlE,uBAAAlH,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4SiCF,KAAA,CAAA8F,eAAe,CAACQ,iBAAiB,GAAApG,MAAA;;MA5SlEE,OAAA,EAAAC,QAAA,CA4SoE,MAE5DjB,MAAA,SAAAA,MAAA,QA9SRkB,gBAAA,CA4SoE,eAE5D,E;MA9SRC,CAAA;uCA+SQf,YAAA,CAEiBM,yBAAA;MAjTzBC,UAAA,EA+SiCC,KAAA,CAAA8F,eAAe,CAACS,iBAAiB;MA/SlE,uBAAAnH,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA+SiCF,KAAA,CAAA8F,eAAe,CAACS,iBAAiB,GAAArG,MAAA;;MA/SlEE,OAAA,EAAAC,QAAA,CA+SoE,MAE5DjB,MAAA,SAAAA,MAAA,QAjTRkB,gBAAA,CA+SoE,aAE5D,E;MAjTRC,CAAA;;IAAAA,CAAA;6DAqTIvB,mBAAA,cAAiB,EACjBQ,YAAA,CA2DY+B,oBAAA;IAjXhBxB,UAAA,EAuTeC,KAAA,CAAAwG,mBAAmB,CAAC/E,IAAI;IAvTvC,uBAAArC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAuTeF,KAAA,CAAAwG,mBAAmB,CAAC/E,IAAI,GAAAvB,MAAA;IACjCwB,KAAK,EAAC,QAAQ;IACd,cAAY,EAAC,MAAM;IACnBhC,IAAI,EAAC,sBAAsB;IAC3B+G,MAAM,EAAN,EAAM;IACL9E,SAAO,EAAErC,QAAA,CAAAoH,cAAc;IACvB7E,OAAO,EAAE7B,KAAA,CAAA8B;;IA7ThB1B,OAAA,EAAAC,QAAA,CA+TM,MAEM,C,4BAFNpB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAA4C,IACrDI,mBAAA,CAA+C,WAA5C,0CAAwC,E,sBAG7CA,mBAAA,CAWM,OAXN0H,WAWM,G,4BAVJ1H,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCI,mBAAA,CAQM,OARN2H,WAQM,I,kBAPJ7H,mBAAA,CAMiByB,SAAA,QA5U3BC,WAAA,CAuU2BnB,QAAA,CAAA4G,iBAAiB,EAAzBtF,IAAI;2BADb+D,YAAA,CAMiB7E,yBAAA;QAJdhB,GAAG,EAAE8B,IAAI,CAACC,EAAE;QAxUzBd,UAAA,EAyUqBC,KAAA,CAAAwG,mBAAmB,CAAC5B,aAAa,CAAChE,IAAI,CAACC,EAAE;QAzU9D,uBAAAX,MAAA,IAyUqBF,KAAA,CAAAwG,mBAAmB,CAAC5B,aAAa,CAAChE,IAAI,CAACC,EAAE,IAAAX;;QAzU9DE,OAAA,EAAAC,QAAA,CA2UY,MAAe,CA3U3BC,gBAAA,CAAAS,gBAAA,CA2UeH,IAAI,CAACI,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGH,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QA5UVV,CAAA;;wCAgVMtB,mBAAA,CAWM,OAXN4H,WAWM,G,4BAVJ5H,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCI,mBAAA,CAQS;MA1VjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAkVyBF,KAAA,CAAAwG,mBAAmB,CAAC9D,QAAQ,GAAAxC,MAAA;MAAErB,KAAK,EAAC;2BACnDE,mBAAA,CAMSyB,SAAA,QAzVnBC,WAAA,CAoV6BnB,QAAA,CAAAwH,iBAAiB,EAA3BlE,MAAM;2BADf7D,mBAAA,CAMS;QAJND,GAAG,EAAE8D,MAAM,CAAC/B,EAAE;QACdyB,KAAK,EAAEM,MAAM,CAAC/B;0BAEZ+B,MAAM,CAAC5B,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAG6B,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA9B,gBAAA,CAAG6B,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAzVViE,WAAA;6EAkVyB/G,KAAA,CAAAwG,mBAAmB,CAAC9D,QAAQ,E,KAW/CzD,mBAAA,CASM,OATN+H,WASM,G,4BARJ/H,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BI,mBAAA,CAMS;MArWjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA+VyBF,KAAA,CAAAwG,mBAAmB,CAACS,MAAM,GAAA/G,MAAA;MAAErB,KAAK,EAAC;oCACjDI,mBAAA,CAAiD;MAAzCqD,KAAK,EAAC;IAAmB,GAAC,QAAM,qBACxCrD,mBAAA,CAA2C;MAAnCqD,KAAK,EAAC;IAAe,GAAC,MAAI,qBAClCrD,mBAAA,CAA6C;MAArCqD,KAAK,EAAC;IAAiB,GAAC,MAAI,qBACpCrD,mBAAA,CAAwC;MAAhCqD,KAAK,EAAC;IAAY,GAAC,MAAI,qBAC/BrD,mBAAA,CAAmC;MAA3BqD,KAAK,EAAC;IAAO,GAAC,MAAI,oB,2CALXtC,KAAA,CAAAwG,mBAAmB,CAACS,MAAM,E,KAS7ChI,mBAAA,CAQM,OARNiI,WAQM,G,4BAPJjI,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BI,mBAAA,CAKY;MA/WpB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA2WmBF,KAAA,CAAAwG,mBAAmB,CAACW,WAAW,GAAAjH,MAAA;MACxCrB,KAAK,EAAC,cAAc;MACpBuI,IAAI,EAAC,GAAG;MACRC,WAAW,EAAC;mDAHHrH,KAAA,CAAAwG,mBAAmB,CAACW,WAAW,E;IA3WlD5G,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}