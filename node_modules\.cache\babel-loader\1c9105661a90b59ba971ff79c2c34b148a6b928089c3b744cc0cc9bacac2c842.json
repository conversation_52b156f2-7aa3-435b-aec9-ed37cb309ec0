{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderSlot as _renderSlot, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"checkbox-wrapper\"\n};\nconst _hoisted_2 = [\"checked\"];\nconst _hoisted_3 = {\n  class: \"checkbox-label\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"label\", _hoisted_1, [_createElementVNode(\"input\", {\n    type: \"checkbox\",\n    checked: $props.modelValue,\n    onChange: _cache[0] || (_cache[0] = $event => _ctx.$emit('update:modelValue', $event.target.checked))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_2), _cache[1] || (_cache[1] = _createElementVNode(\"span\", {\n    class: \"checkbox-mark\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_3, [_renderSlot(_ctx.$slots, \"default\")])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "type", "checked", "$props", "modelValue", "onChange", "_cache", "$event", "_ctx", "$emit", "target", "_hoisted_2", "_hoisted_3", "_renderSlot", "$slots"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\CustomCheckbox.vue"], "sourcesContent": ["<template>\r\n  <label class=\"checkbox-wrapper\">\r\n    <input\r\n      type=\"checkbox\"\r\n      :checked=\"modelValue\"\r\n      @change=\"$emit('update:modelValue', $event.target.checked)\"\r\n    />\r\n    <span class=\"checkbox-mark\"></span>\r\n    <span class=\"checkbox-label\">\r\n      <slot></slot>\r\n    </span>\r\n  </label>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CustomCheckbox',\r\n  props: {\r\n    modelValue: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  emits: ['update:modelValue']\r\n}\r\n</script> "], "mappings": ";;EACSA,KAAK,EAAC;AAAkB;mBADjC;;EAQUA,KAAK,EAAC;AAAgB;;uBAP9BC,mBAAA,CAUQ,SAVRC,UAUQ,GATNC,mBAAA,CAIE;IAHAC,IAAI,EAAC,UAAU;IACdC,OAAO,EAAEC,MAAA,CAAAC,UAAU;IACnBC,QAAM,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,KAAK,sBAAsBF,MAAM,CAACG,MAAM,CAACR,OAAO;2CAL/DS,UAAA,G,0BAOIX,mBAAA,CAAmC;IAA7BH,KAAK,EAAC;EAAe,6BAC3BG,mBAAA,CAEO,QAFPY,UAEO,GADLC,WAAA,CAAaL,IAAA,CAAAM,MAAA,a", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}