[{"D:\\demo\\ooo\\pass\\src\\main.js": "1", "D:\\demo\\ooo\\pass\\src\\App.vue": "2", "D:\\demo\\ooo\\pass\\src\\router\\index.js": "3", "D:\\demo\\ooo\\pass\\src\\store\\index.js": "4", "D:\\demo\\ooo\\pass\\src\\views\\PasswordPolicies.vue": "5", "D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue": "6", "D:\\demo\\ooo\\pass\\src\\views\\SecurityOverview.vue": "7", "D:\\demo\\ooo\\pass\\src\\views\\ScheduledTasks.vue": "8", "D:\\demo\\ooo\\pass\\src\\components\\NotificationSystem.vue": "9", "D:\\demo\\ooo\\pass\\src\\components\\StatusBadge.vue": "10", "D:\\demo\\ooo\\pass\\src\\components\\BaseModal.vue": "11", "D:\\demo\\ooo\\pass\\src\\components\\PasswordStrengthMeter.vue": "12", "D:\\demo\\ooo\\pass\\src\\components\\SecurityDashboard.vue": "13", "D:\\demo\\ooo\\pass\\src\\components\\AdvancedPasswordGenerator.vue": "14", "D:\\demo\\ooo\\pass\\src\\components\\CustomCheckbox.vue": "15", "D:\\demo\\ooo\\pass\\src\\utils\\notification.js": "16", "D:\\demo\\ooo\\pass\\src\\views\\NotificationTest.vue": "17"}, {"size": 1709, "mtime": 1749111612365, "results": "18", "hashOfConfig": "19"}, {"size": 17590, "mtime": 1749110423026, "results": "20", "hashOfConfig": "19"}, {"size": 1574, "mtime": 1749111951640, "results": "21", "hashOfConfig": "19"}, {"size": 14072, "mtime": 1745398750821, "results": "22", "hashOfConfig": "19"}, {"size": 16724, "mtime": 1745393320344, "results": "23", "hashOfConfig": "19"}, {"size": 71652, "mtime": 1749110392708, "results": "24", "hashOfConfig": "19"}, {"size": 13193, "mtime": 1749111864932, "results": "25", "hashOfConfig": "19"}, {"size": 20744, "mtime": 1745394543431, "results": "26", "hashOfConfig": "19"}, {"size": 8478, "mtime": 1749111643746, "results": "27", "hashOfConfig": "19"}, {"size": 639, "mtime": 1745389673039, "results": "28", "hashOfConfig": "19"}, {"size": 5242, "mtime": 1745394639794, "results": "29", "hashOfConfig": "19"}, {"size": 2124, "mtime": 1745389651932, "results": "30", "hashOfConfig": "19"}, {"size": 13264, "mtime": 1749110134045, "results": "31", "hashOfConfig": "19"}, {"size": 13483, "mtime": 1749110759479, "results": "32", "hashOfConfig": "19"}, {"size": 512, "mtime": 1745389662414, "results": "33", "hashOfConfig": "19"}, {"size": 3458, "mtime": 1749111680030, "results": "34", "hashOfConfig": "19"}, {"size": 8783, "mtime": 1749111925030, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "16pt2e3", {"filePath": "38", "messages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\demo\\ooo\\pass\\src\\main.js", [], "D:\\demo\\ooo\\pass\\src\\App.vue", [], "D:\\demo\\ooo\\pass\\src\\router\\index.js", [], "D:\\demo\\ooo\\pass\\src\\store\\index.js", [], "D:\\demo\\ooo\\pass\\src\\views\\PasswordPolicies.vue", [], "D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue", [], "D:\\demo\\ooo\\pass\\src\\views\\SecurityOverview.vue", [], "D:\\demo\\ooo\\pass\\src\\views\\ScheduledTasks.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\NotificationSystem.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\StatusBadge.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\BaseModal.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\PasswordStrengthMeter.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\SecurityDashboard.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\AdvancedPasswordGenerator.vue", [], "D:\\demo\\ooo\\pass\\src\\components\\CustomCheckbox.vue", [], "D:\\demo\\ooo\\pass\\src\\utils\\notification.js", [], "D:\\demo\\ooo\\pass\\src\\views\\NotificationTest.vue", []]