{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport HostManagement from '../views/HostManagement.vue';\nimport PasswordPolicies from '../views/PasswordPolicies.vue';\nimport ScheduledTasks from '../views/ScheduledTasks.vue';\nconst routes = [{\n  path: '/',\n  redirect: '/hosts'\n}, {\n  path: '/hosts',\n  name: 'HostManagement',\n  component: HostManagement,\n  meta: {\n    title: '主机管理',\n    icon: 'server'\n  }\n}, {\n  path: '/policies',\n  name: 'PasswordPolicies',\n  component: PasswordPolicies,\n  meta: {\n    title: '密码策略',\n    icon: 'key'\n  }\n}, {\n  path: '/tasks',\n  name: 'ScheduledTasks',\n  component: ScheduledTasks,\n  meta: {\n    title: '定时任务',\n    icon: 'clock'\n  }\n}];\nconst router = createRouter({\n  history: createWebHistory(),\n  routes\n});\nrouter.beforeEach((to, from, next) => {\n  // 设置文档标题\n  document.title = `${to.meta.title || '主页'} - 密码管理系统`;\n  next();\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "HostManagement", "PasswordPolicies", "ScheduledTasks", "routes", "path", "redirect", "name", "component", "meta", "title", "icon", "router", "history", "beforeEach", "to", "from", "next", "document"], "sources": ["D:/demo/ooo/pass/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\r\nimport HostManagement from '../views/HostManagement.vue'\r\nimport PasswordPolicies from '../views/PasswordPolicies.vue'\r\nimport ScheduledTasks from '../views/ScheduledTasks.vue'\r\n\r\nconst routes = [\r\n    {\r\n        path: '/',\r\n        redirect: '/hosts'\r\n    },\r\n    {\r\n        path: '/hosts',\r\n        name: 'HostManagement',\r\n        component: HostManagement,\r\n        meta: { title: '主机管理', icon: 'server' }\r\n    },\r\n    {\r\n        path: '/policies',\r\n        name: 'PasswordPolicies',\r\n        component: PasswordPolicies,\r\n        meta: { title: '密码策略', icon: 'key' }\r\n    },\r\n    {\r\n        path: '/tasks',\r\n        name: 'ScheduledTasks',\r\n        component: ScheduledTasks,\r\n        meta: { title: '定时任务', icon: 'clock' }\r\n    }\r\n]\r\n\r\nconst router = createRouter({\r\n    history: createWebHistory(),\r\n    routes\r\n})\r\n\r\nrouter.beforeEach((to, from, next) => {\r\n    // 设置文档标题\r\n    document.title = `${to.meta.title || '主页'} - 密码管理系统`\r\n    next()\r\n})\r\n\r\nexport default router "], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,cAAc,MAAM,6BAA6B;AAExD,MAAMC,MAAM,GAAG,CACX;EACIC,IAAI,EAAE,GAAG;EACTC,QAAQ,EAAE;AACd,CAAC,EACD;EACID,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEP,cAAc;EACzBQ,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAS;AAC1C,CAAC,EACD;EACIN,IAAI,EAAE,WAAW;EACjBE,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAEN,gBAAgB;EAC3BO,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAM;AACvC,CAAC,EACD;EACIN,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEL,cAAc;EACzBM,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAQ;AACzC,CAAC,CACJ;AAED,MAAMC,MAAM,GAAGb,YAAY,CAAC;EACxBc,OAAO,EAAEb,gBAAgB,CAAC,CAAC;EAC3BI;AACJ,CAAC,CAAC;AAEFQ,MAAM,CAACE,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EAClC;EACAC,QAAQ,CAACR,KAAK,GAAG,GAAGK,EAAE,CAACN,IAAI,CAACC,KAAK,IAAI,IAAI,WAAW;EACpDO,IAAI,CAAC,CAAC;AACV,CAAC,CAAC;AAEF,eAAeL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}