{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, vModelRadio as _vModelRadio, withDirectives as _withDirectives, vModelSelect as _vModelSelect, vModelText as _vModelText, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"flex space-x-3 mb-6\"\n};\nconst _hoisted_2 = {\n  class: \"data-table\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"text-red-600 ml-1\"\n};\nconst _hoisted_4 = {\n  key: 1,\n  class: \"text-yellow-500 ml-1\"\n};\nconst _hoisted_5 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_6 = [\"type\", \"value\"];\nconst _hoisted_7 = [\"onClick\"];\nconst _hoisted_8 = [\"onClick\"];\nconst _hoisted_9 = {\n  class: \"form-group\"\n};\nconst _hoisted_10 = {\n  class: \"form-label\"\n};\nconst _hoisted_11 = {\n  class: \"form-group\"\n};\nconst _hoisted_12 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_13 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_14 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_15 = {\n  key: 0\n};\nconst _hoisted_16 = {\n  class: \"form-group\"\n};\nconst _hoisted_17 = [\"value\"];\nconst _hoisted_18 = {\n  class: \"form-group\"\n};\nconst _hoisted_19 = {\n  class: \"flex\"\n};\nconst _hoisted_20 = {\n  key: 1\n};\nconst _hoisted_21 = {\n  class: \"form-group\"\n};\nconst _hoisted_22 = {\n  class: \"form-group\"\n};\nconst _hoisted_23 = {\n  key: 0,\n  class: \"text-red-500 text-xs mt-1\"\n};\nconst _hoisted_24 = {\n  class: \"form-group\"\n};\nconst _hoisted_25 = {\n  class: \"form-group\"\n};\nconst _hoisted_26 = {\n  class: \"mb-2\"\n};\nconst _hoisted_27 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_28 = {\n  class: \"form-text\"\n};\nconst _hoisted_29 = {\n  class: \"form-group\"\n};\nconst _hoisted_30 = [\"value\"];\nconst _hoisted_31 = {\n  class: \"form-group\"\n};\nconst _hoisted_32 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_33 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_34 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_35 = {\n  key: 0,\n  class: \"mt-3\"\n};\nconst _hoisted_36 = {\n  class: \"grid grid-cols-2 gap-4\"\n};\nconst _hoisted_37 = {\n  class: \"form-group\"\n};\nconst _hoisted_38 = {\n  class: \"form-group\"\n};\nconst _hoisted_39 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_40 = {\n  class: \"form-group\"\n};\nconst _hoisted_41 = [\"value\"];\nconst _hoisted_42 = {\n  class: \"form-group\"\n};\nconst _hoisted_43 = {\n  class: \"form-group\"\n};\nconst _hoisted_44 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_45 = {\n  class: \"form-group\"\n};\nconst _hoisted_46 = [\"value\"];\nconst _hoisted_47 = {\n  class: \"form-group\"\n};\nconst _hoisted_48 = {\n  class: \"form-group\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_CustomCheckbox = _resolveComponent(\"CustomCheckbox\");\n  const _component_StatusBadge = _resolveComponent(\"StatusBadge\");\n  const _component_PasswordStrengthMeter = _resolveComponent(\"PasswordStrengthMeter\");\n  const _component_BaseModal = _resolveComponent(\"BaseModal\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createCommentVNode(\" 操作按钮 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"button\", {\n    class: \"btn-outline\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.showEmergencyReset && $options.showEmergencyReset(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'exclamation-triangle'],\n    class: \"mr-2 text-red-600\"\n  }), _cache[33] || (_cache[33] = _createElementVNode(\"span\", null, \"紧急重置\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"btn-outline\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.openBatchUpdateModal && $options.openBatchUpdateModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'key'],\n    class: \"mr-2\"\n  }), _cache[34] || (_cache[34] = _createElementVNode(\"span\", null, \"批量更新密码\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"btn-outline\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.openBatchApplyModal && $options.openBatchApplyModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"mr-2\"\n  }), _cache[35] || (_cache[35] = _createElementVNode(\"span\", null, \"批量应用策略\", -1 /* HOISTED */))])]), _createCommentVNode(\" 主机列表 \"), _createElementVNode(\"table\", _hoisted_2, [_createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, [_createVNode(_component_CustomCheckbox, {\n    modelValue: $data.selectAll,\n    \"onUpdate:modelValue\": [_cache[3] || (_cache[3] = $event => $data.selectAll = $event), $options.toggleSelectAll]\n  }, {\n    default: _withCtx(() => _cache[36] || (_cache[36] = [_createTextVNode(\" 主机名 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _cache[37] || (_cache[37] = _createElementVNode(\"th\", null, \"IP地址\", -1 /* HOISTED */)), _cache[38] || (_cache[38] = _createElementVNode(\"th\", null, \"最后密码修改时间\", -1 /* HOISTED */)), _cache[39] || (_cache[39] = _createElementVNode(\"th\", null, \"密码过期时间\", -1 /* HOISTED */)), _cache[40] || (_cache[40] = _createElementVNode(\"th\", null, \"密码\", -1 /* HOISTED */)), _cache[41] || (_cache[41] = _createElementVNode(\"th\", null, \"状态\", -1 /* HOISTED */)), _cache[42] || (_cache[42] = _createElementVNode(\"th\", null, \"操作\", -1 /* HOISTED */))])]), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: host.id\n    }, [_createElementVNode(\"td\", null, [_createVNode(_component_CustomCheckbox, {\n      modelValue: host.selected,\n      \"onUpdate:modelValue\": $event => host.selected = $event\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name), 1 /* TEXT */)]),\n      _: 2 /* DYNAMIC */\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"td\", null, _toDisplayString(host.ip), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(host.lastPasswordChange || '-'), 1 /* TEXT */), _createElementVNode(\"td\", {\n      class: _normalizeClass({\n        'text-red-600 font-bold': $options.isPasswordExpired(host).status === 'danger' || $options.isPasswordExpired(host).status === 'expired',\n        'text-yellow-500 font-medium': $options.isPasswordExpired(host).status === 'warning',\n        'text-gray-500': $options.isPasswordExpired(host).status === 'normal'\n      })\n    }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(host).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(host).status === 'expired' || $options.isPasswordExpired(host).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_3, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'exclamation-triangle']\n    })])) : $options.isPasswordExpired(host).status === 'warning' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_4, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'exclamation-circle']\n    })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */), _createElementVNode(\"td\", null, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"input\", {\n      type: $data.passwordVisibility[host.id] ? 'text' : 'password',\n      value: host.password,\n      readonly: \"\",\n      class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n    }, null, 8 /* PROPS */, _hoisted_6), _createElementVNode(\"button\", {\n      onClick: $event => $options.togglePasswordVisibility(host.id),\n      class: \"ml-2 text-gray-600 hover:text-blue-700\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility[host.id] ? 'eye-slash' : 'eye']\n    }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_7)])]), _createElementVNode(\"td\", null, [_createVNode(_component_StatusBadge, {\n      type: host.status\n    }, null, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"td\", null, [_createElementVNode(\"button\", {\n      class: \"text-blue-600 hover:text-blue-800\",\n      onClick: $event => $options.openChangePasswordModal(host)\n    }, \" 修改密码 \", 8 /* PROPS */, _hoisted_8)])]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 修改密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.changePasswordModal.show,\n    \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.changePasswordModal.show = $event),\n    title: \"修改密码\",\n    \"confirm-text\": \"确认更新\",\n    onConfirm: $options.updatePassword,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"label\", _hoisted_10, \"服务器: \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_11, [_cache[45] || (_cache[45] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"label\", _hoisted_13, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.changePasswordModal.method = $event),\n      value: \"auto\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.changePasswordModal.method]]), _cache[43] || (_cache[43] = _createElementVNode(\"span\", null, \"自动生成\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_14, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.changePasswordModal.method = $event),\n      value: \"manual\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.changePasswordModal.method]]), _cache[44] || (_cache[44] = _createElementVNode(\"span\", null, \"手动输入\", -1 /* HOISTED */))])])]), $data.changePasswordModal.method === 'auto' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_cache[46] || (_cache[46] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.changePasswordModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_17);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.changePasswordModal.policyId]])]), _createElementVNode(\"div\", _hoisted_18, [_cache[47] || (_cache[47] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_19, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.changePasswordModal.generatedPassword = $event),\n      class: \"form-control rounded-r-none\",\n      readonly: \"\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.changePasswordModal.generatedPassword]]), _createElementVNode(\"button\", {\n      class: \"bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded-r-md\",\n      onClick: _cache[8] || (_cache[8] = (...args) => $options.generatePassword && $options.generatePassword(...args))\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt']\n    })])]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.generatedPassword\n    }, null, 8 /* PROPS */, [\"password\"])])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_cache[48] || (_cache[48] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"新密码\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"password\",\n      \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.changePasswordModal.newPassword = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.changePasswordModal.newPassword]]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.newPassword\n    }, null, 8 /* PROPS */, [\"password\"])]), _createElementVNode(\"div\", _hoisted_22, [_cache[49] || (_cache[49] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"确认密码\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"password\",\n      \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.changePasswordModal.confirmPassword = $event),\n      class: _normalizeClass([\"form-control\", {\n        'border-red-500': $options.passwordMismatch\n      }])\n    }, null, 2 /* CLASS */), [[_vModelText, $data.changePasswordModal.confirmPassword]]), $options.passwordMismatch ? (_openBlock(), _createElementBlock(\"div\", _hoisted_23, \" 两次输入的密码不一致 \")) : _createCommentVNode(\"v-if\", true)])])), _createElementVNode(\"div\", _hoisted_24, [_cache[53] || (_cache[53] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.executeImmediately,\n      \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $data.changePasswordModal.executeImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[50] || (_cache[50] = [_createTextVNode(\" 立即执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.saveHistory,\n      \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $data.changePasswordModal.saveHistory = $event)\n    }, {\n      default: _withCtx(() => _cache[51] || (_cache[51] = [_createTextVNode(\" 保存密码历史记录 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.logAudit,\n      \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.changePasswordModal.logAudit = $event)\n    }, {\n      default: _withCtx(() => _cache[52] || (_cache[52] = [_createTextVNode(\" 记录审计日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量更新密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchUpdateModal.show,\n    \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.batchUpdateModal.show = $event),\n    title: \"批量更新密码\",\n    \"confirm-text\": \"开始更新\",\n    size: \"lg\",\n    onConfirm: $options.batchUpdatePasswords,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_25, [_cache[55] || (_cache[55] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_26, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatch,\n      \"onUpdate:modelValue\": [_cache[15] || (_cache[15] = $event => $data.selectAllBatch = $event), $options.toggleSelectAllBatch]\n    }, {\n      default: _withCtx(() => _cache[54] || (_cache[54] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_27, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchUpdateModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchUpdateModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_28, \"已选择 \" + _toDisplayString($options.selectedHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_29, [_cache[56] || (_cache[56] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.batchUpdateModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_30);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchUpdateModal.policyId]])]), _createElementVNode(\"div\", _hoisted_31, [_cache[61] || (_cache[61] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"label\", _hoisted_33, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"immediate\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[57] || (_cache[57] = _createElementVNode(\"span\", null, \"立即执行\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_34, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[18] || (_cache[18] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"scheduled\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[58] || (_cache[58] = _createElementVNode(\"span\", null, \"定时执行\", -1 /* HOISTED */))])]), $data.batchUpdateModal.executionTime === 'scheduled' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_35, [_createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", null, [_cache[59] || (_cache[59] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"日期\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"date\",\n      \"onUpdate:modelValue\": _cache[19] || (_cache[19] = $event => $data.batchUpdateModal.scheduledDate = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledDate]])]), _createElementVNode(\"div\", null, [_cache[60] || (_cache[60] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"时间\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"time\",\n      \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $data.batchUpdateModal.scheduledTime = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledTime]])])])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_37, [_cache[65] || (_cache[65] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[21] || (_cache[21] = $event => $data.batchUpdateModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[62] || (_cache[62] = [_createTextVNode(\" 忽略错误继续执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.detailedLog,\n      \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $data.batchUpdateModal.detailedLog = $event)\n    }, {\n      default: _withCtx(() => _cache[63] || (_cache[63] = [_createTextVNode(\" 记录详细日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.sendNotification,\n      \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.batchUpdateModal.sendNotification = $event)\n    }, {\n      default: _withCtx(() => _cache[64] || (_cache[64] = [_createTextVNode(\" 执行完成后发送通知 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量应用策略弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchApplyModal.show,\n    \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $data.batchApplyModal.show = $event),\n    title: \"批量应用密码策略\",\n    \"confirm-text\": \"应用策略\",\n    onConfirm: $options.batchApplyPolicy,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_38, [_cache[66] || (_cache[66] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_39, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchApplyModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchApplyModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_40, [_cache[67] || (_cache[67] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.batchApplyModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name), 9 /* TEXT, PROPS */, _hoisted_41);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchApplyModal.policyId]])]), _createElementVNode(\"div\", _hoisted_42, [_cache[70] || (_cache[70] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.updateImmediately,\n      \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.batchApplyModal.updateImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[68] || (_cache[68] = [_createTextVNode(\" 立即更新密码以符合策略 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.applyOnNextUpdate,\n      \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.batchApplyModal.applyOnNextUpdate = $event)\n    }, {\n      default: _withCtx(() => _cache[69] || (_cache[69] = [_createTextVNode(\" 下次密码更新时应用 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 紧急重置密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.emergencyResetModal.show,\n    \"onUpdate:modelValue\": _cache[32] || (_cache[32] = $event => $data.emergencyResetModal.show = $event),\n    title: \"紧急密码重置\",\n    \"confirm-text\": \"立即重置\",\n    icon: \"exclamation-triangle\",\n    danger: \"\",\n    onConfirm: $options.emergencyReset,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_cache[76] || (_cache[76] = _createElementVNode(\"div\", {\n      class: \"bg-red-50 text-red-700 p-3 rounded-md mb-4\"\n    }, [_createElementVNode(\"p\", null, \"紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_43, [_cache[71] || (_cache[71] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_44, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.emergencyResetModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.emergencyResetModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_45, [_cache[72] || (_cache[72] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用紧急策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[29] || (_cache[29] = $event => $data.emergencyResetModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.emergencyPolicies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_46);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.policyId]])]), _createElementVNode(\"div\", _hoisted_47, [_cache[74] || (_cache[74] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"操作原因\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[30] || (_cache[30] = $event => $data.emergencyResetModal.reason = $event),\n      class: \"form-select\"\n    }, _cache[73] || (_cache[73] = [_createElementVNode(\"option\", {\n      value: \"security_incident\"\n    }, \"安全事件响应\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"password_leak\"\n    }, \"密码泄露\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"abnormal_access\"\n    }, \"异常访问\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"compliance\"\n    }, \"合规要求\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"other\"\n    }, \"其他原因\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.reason]])]), _createElementVNode(\"div\", _hoisted_48, [_cache[75] || (_cache[75] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"附加说明\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n      \"onUpdate:modelValue\": _cache[31] || (_cache[31] = $event => $data.emergencyResetModal.description = $event),\n      class: \"form-control\",\n      rows: \"2\",\n      placeholder: \"请输入重置原因详细说明\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.emergencyResetModal.description]])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "onClick", "_cache", "args", "$options", "showEmergencyReset", "_createVNode", "_component_font_awesome_icon", "icon", "openBatchUpdateModal", "openBatchApplyModal", "_hoisted_2", "_component_CustomCheckbox", "modelValue", "$data", "selectAll", "$event", "toggleSelectAll", "default", "_withCtx", "_createTextVNode", "_", "_Fragment", "_renderList", "_ctx", "hosts", "host", "id", "selected", "_toDisplayString", "name", "ip", "lastPasswordChange", "_normalizeClass", "isPasswordExpired", "status", "text", "_hoisted_3", "_hoisted_4", "_hoisted_5", "type", "passwordVisibility", "value", "password", "readonly", "_hoisted_6", "togglePasswordVisibility", "_hoisted_7", "_component_StatusBadge", "openChangePasswordModal", "_hoisted_8", "_component_BaseModal", "changePasswordModal", "show", "title", "onConfirm", "updatePassword", "loading", "processing", "_hoisted_9", "_hoisted_10", "currentHost", "_hoisted_11", "_hoisted_12", "_hoisted_13", "method", "_hoisted_14", "_hoisted_15", "_hoisted_16", "policyId", "policies", "policy", "<PERSON><PERSON><PERSON><PERSON>", "expiryDays", "_hoisted_17", "_hoisted_18", "_hoisted_19", "generatedPassword", "generatePassword", "_component_PasswordStrengthMeter", "_hoisted_20", "_hoisted_21", "newPassword", "_hoisted_22", "confirmPassword", "passwordMismatch", "_hoisted_23", "_hoisted_24", "executeImmediately", "saveHistory", "logAudit", "batchUpdateModal", "size", "batchUpdatePasswords", "_hoisted_25", "_hoisted_26", "selectAllBatch", "toggleSelectAllBatch", "_hoisted_27", "_createBlock", "selectedHosts", "_hoisted_28", "selectedHostsCount", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "_hoisted_33", "executionTime", "_hoisted_34", "_hoisted_35", "_hoisted_36", "scheduledDate", "scheduledTime", "_hoisted_37", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "batchApplyPolicy", "_hoisted_38", "_hoisted_39", "selectedHostsList", "_hoisted_40", "_hoisted_41", "_hoisted_42", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "danger", "emergencyReset", "_hoisted_43", "_hoisted_44", "_hoisted_45", "emergencyPolicies", "_hoisted_46", "_hoisted_47", "reason", "_hoisted_48", "description", "rows", "placeholder"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"flex space-x-3 mb-6\">\r\n      <button class=\"btn-outline\" @click=\"showEmergencyReset\">\r\n        <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2 text-red-600\" />\r\n        <span>紧急重置</span>\r\n      </button>\r\n      <button class=\"btn-outline\" @click=\"openBatchUpdateModal\">\r\n        <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n        <span>批量更新密码</span>\r\n      </button>\r\n      <button class=\"btn-outline\" @click=\"openBatchApplyModal\">\r\n        <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n        <span>批量应用策略</span>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <table class=\"data-table\">\r\n      <thead>\r\n        <tr>\r\n          <th>\r\n            <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n              主机名\r\n            </CustomCheckbox>\r\n          </th>\r\n          <th>IP地址</th>\r\n          <th>最后密码修改时间</th>\r\n          <th>密码过期时间</th>\r\n          <th>密码</th>\r\n          <th>状态</th>\r\n          <th>操作</th>\r\n        </tr>\r\n      </thead>\r\n      <tbody>\r\n        <tr v-for=\"host in hosts\" :key=\"host.id\">\r\n          <td>\r\n            <CustomCheckbox v-model=\"host.selected\">\r\n              {{ host.name }}\r\n            </CustomCheckbox>\r\n          </td>\r\n          <td>{{ host.ip }}</td>\r\n          <td>{{ host.lastPasswordChange || '-' }}</td>\r\n          <td :class=\"{\r\n            'text-red-600 font-bold': isPasswordExpired(host).status === 'danger' || isPasswordExpired(host).status === 'expired',\r\n            'text-yellow-500 font-medium': isPasswordExpired(host).status === 'warning',\r\n            'text-gray-500': isPasswordExpired(host).status === 'normal'\r\n          }\">\r\n            {{ isPasswordExpired(host).text }}\r\n            <span v-if=\"isPasswordExpired(host).status === 'expired' || isPasswordExpired(host).status === 'danger'\" class=\"text-red-600 ml-1\">\r\n              <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n            </span>\r\n            <span v-else-if=\"isPasswordExpired(host).status === 'warning'\" class=\"text-yellow-500 ml-1\">\r\n              <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" />\r\n            </span>\r\n          </td>\r\n          <td>\r\n            <div class=\"flex items-center\">\r\n              <input :type=\"passwordVisibility[host.id] ? 'text' : 'password'\" :value=\"host.password\" readonly\r\n                class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n              <button @click=\"togglePasswordVisibility(host.id)\" class=\"ml-2 text-gray-600 hover:text-blue-700\">\r\n                <font-awesome-icon :icon=\"['fas', passwordVisibility[host.id] ? 'eye-slash' : 'eye']\" />\r\n              </button>\r\n            </div>\r\n          </td>\r\n          <td>\r\n            <StatusBadge :type=\"host.status\" />\r\n          </td>\r\n          <td>\r\n            <button class=\"text-blue-600 hover:text-blue-800\" @click=\"openChangePasswordModal(host)\">\r\n              修改密码\r\n            </button>\r\n          </td>\r\n        </tr>\r\n      </tbody>\r\n    </table>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" confirm-text=\"确认更新\" @confirm=\"updatePassword\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">服务器: {{ currentHost.name }}</label>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"changePasswordModal.method\" value=\"auto\" class=\"mr-2\">\r\n            <span>自动生成</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"changePasswordModal.method\" value=\"manual\" class=\"mr-2\">\r\n            <span>手动输入</span>\r\n          </label>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-if=\"changePasswordModal.method === 'auto'\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">选择密码策略</label>\r\n          <select v-model=\"changePasswordModal.policyId\" class=\"form-select\">\r\n            <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n              {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n            </option>\r\n          </select>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <div class=\"flex\">\r\n            <input type=\"text\" v-model=\"changePasswordModal.generatedPassword\" class=\"form-control rounded-r-none\"\r\n              readonly>\r\n            <button class=\"bg-gray-200 hover:bg-gray-300 px-3 py-2 rounded-r-md\" @click=\"generatePassword\">\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" />\r\n            </button>\r\n          </div>\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.generatedPassword\" />\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else>\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <input type=\"password\" v-model=\"changePasswordModal.newPassword\" class=\"form-control\">\r\n          <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <input type=\"password\" v-model=\"changePasswordModal.confirmPassword\" class=\"form-control\"\r\n            :class=\"{ 'border-red-500': passwordMismatch }\">\r\n          <div v-if=\"passwordMismatch\" class=\"text-red-500 text-xs mt-1\">\r\n            两次输入的密码不一致\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行选项</label>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          立即执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          保存密码历史记录\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          记录审计日志\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      passwordVisibility: {},\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host) {\r\n      this.currentHost = host\r\n      this.changePasswordModal.show = true\r\n      this.changePasswordModal.generatedPassword = this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword(policy) {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policyObj ? policyObj.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      if (this.changePasswordModal && !policy) {\r\n        this.changePasswordModal.generatedPassword = password\r\n      }\r\n\r\n      return password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto'\r\n          ? this.changePasswordModal.generatedPassword\r\n          : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          password: password,\r\n          policyId: this.changePasswordModal.policyId\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取所选策略\r\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)\r\n\r\n        // 为每台主机生成并更新密码\r\n        await Promise.all(\r\n          selectedHostIds.map(async (hostId) => {\r\n            const newPassword = this.generatePassword(policy)\r\n            return this.$store.dispatch('updateHostPassword', {\r\n              hostId: hostId,\r\n              password: newPassword,\r\n              policyId: policy.id\r\n            })\r\n          })\r\n        )\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取紧急策略\r\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)\r\n\r\n        // 为每台主机生成并更新密码\r\n        await Promise.all(\r\n          selectedHostIds.map(async (hostId) => {\r\n            const newPassword = this.generatePassword(policy)\r\n            return this.$store.dispatch('updateHostPassword', {\r\n              hostId: hostId,\r\n              password: newPassword,\r\n              policyId: policy.id\r\n            })\r\n          })\r\n        )\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    togglePasswordVisibility(hostId) {\r\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]\r\n    },\r\n\r\n    isPasswordExpired(host) {\r\n      if (!host.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }\r\n\r\n      // 解析过期时间\r\n      const expiryDate = new Date(host.passwordExpiryDate)\r\n      const now = new Date()\r\n\r\n      // 如果已过期\r\n      if (expiryDate < now) {\r\n        return {\r\n          status: 'expired',\r\n          days: 0,\r\n          text: '已过期'\r\n        }\r\n      }\r\n\r\n      // 计算剩余天数和小时数\r\n      const diffTime = expiryDate - now\r\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))\r\n      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n\r\n      // 根据剩余时间确定状态\r\n      let status = 'normal'\r\n      if (diffDays < 7) {\r\n        status = 'danger'  // 少于7天\r\n      } else if (diffDays < 14) {\r\n        status = 'warning' // 少于14天\r\n      }\r\n\r\n      // 格式化显示文本\r\n      let text = ''\r\n      if (diffDays > 0) {\r\n        text += `${diffDays}天`\r\n      }\r\n      if (diffHours > 0 || diffDays === 0) {\r\n        text += `${diffHours}小时`\r\n      }\r\n\r\n      return { status, days: diffDays, text: `剩余${text}` }\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": ";;EAGSA,KAAK,EAAC;AAAqB;;EAgBzBA,KAAK,EAAC;AAAY;;EAnB7BC,GAAA;EAkDqHD,KAAK,EAAC;;;EAlD3HC,GAAA;EAqD2ED,KAAK,EAAC;;;EAKhEA,KAAK,EAAC;AAAmB;mBA1D1C;mBAAA;mBAAA;;EAiFWA,KAAK,EAAC;AAAY;;EACdA,KAAK,EAAC;AAAY;;EAGtBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EA5F1CC,GAAA;AAAA;;EAoGaD,KAAK,EAAC;AAAY;oBApG/B;;EA6GaA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EA/G3BC,GAAA;AAAA;;EA2HaD,KAAK,EAAC;AAAY;;EAMlBA,KAAK,EAAC;AAAY;;EAjI/BC,GAAA;EAqIuCD,KAAK,EAAC;;;EAMlCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAY;oBA3K7B;;EAoLWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EA3L1CC,GAAA;EAiMmED,KAAK,EAAC;;;EAC1DA,KAAK,EAAC;AAAwB;;EAalCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;oBA1O7B;;EAmPWA,KAAK,EAAC;AAAY;;EAkBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;oBA/Q7B;;EAwRWA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;;;;;;uBAlS3BE,mBAAA,CAwSM,cAvSJC,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNC,UAaM,GAZJD,mBAAA,CAGS;IAHDJ,KAAK,EAAC,aAAa;IAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,kBAAA,IAAAD,QAAA,CAAAC,kBAAA,IAAAF,IAAA,CAAkB;MACpDG,YAAA,CAAuFC,4BAAA;IAAnEC,IAAI,EAAE,+BAA+B;IAAEb,KAAK,EAAC;kCACjEI,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGS;IAHDJ,KAAK,EAAC,aAAa;IAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAK,oBAAA,IAAAL,QAAA,CAAAK,oBAAA,IAAAN,IAAA,CAAoB;MACtDG,YAAA,CAAyDC,4BAAA;IAArCC,IAAI,EAAE,cAAc;IAAEb,KAAK,EAAC;kCAChDI,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAGS;IAHDJ,KAAK,EAAC,aAAa;IAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAM,mBAAA,IAAAN,QAAA,CAAAM,mBAAA,IAAAP,IAAA,CAAmB;MACrDG,YAAA,CAAgEC,4BAAA;IAA5CC,IAAI,EAAE,qBAAqB;IAAEb,KAAK,EAAC;kCACvDI,mBAAA,CAAmB,cAAb,QAAM,qB,KAIhBD,mBAAA,UAAa,EACbC,mBAAA,CAyDQ,SAzDRY,UAyDQ,GAxDNZ,mBAAA,CAcQ,gBAbNA,mBAAA,CAYK,aAXHA,mBAAA,CAIK,aAHHO,YAAA,CAEiBM,yBAAA;IAzB7BC,UAAA,EAuBqCC,KAAA,CAAAC,SAAS;IAvB9C,wB,oCAuBqCD,KAAA,CAAAC,SAAS,GAAAC,MAAA,GAAsBZ,QAAA,CAAAa,eAAe;;IAvBnFC,OAAA,EAAAC,QAAA,CAuBqF,MAEzEjB,MAAA,SAAAA,MAAA,QAzBZkB,gBAAA,CAuBqF,OAEzE,E;IAzBZC,CAAA;0FA2BUtB,mBAAA,CAAa,YAAT,MAAI,sB,4BACRA,mBAAA,CAAiB,YAAb,UAAQ,sB,4BACZA,mBAAA,CAAe,YAAX,QAAM,sB,4BACVA,mBAAA,CAAW,YAAP,IAAE,sB,4BACNA,mBAAA,CAAW,YAAP,IAAE,sB,4BACNA,mBAAA,CAAW,YAAP,IAAE,qB,KAGVA,mBAAA,CAwCQ,iB,kBAvCNF,mBAAA,CAsCKyB,SAAA,QA1EbC,WAAA,CAoC2BC,IAAA,CAAAC,KAAK,EAAbC,IAAI;yBAAf7B,mBAAA,CAsCK;MAtCsBD,GAAG,EAAE8B,IAAI,CAACC;QACnC5B,mBAAA,CAIK,aAHHO,YAAA,CAEiBM,yBAAA;MAxC7BC,UAAA,EAsCqCa,IAAI,CAACE,QAAQ;MAtClD,uBAAAZ,MAAA,IAsCqCU,IAAI,CAACE,QAAQ,GAAAZ;;MAtClDE,OAAA,EAAAC,QAAA,CAuCc,MAAe,CAvC7BC,gBAAA,CAAAS,gBAAA,CAuCiBH,IAAI,CAACI,IAAI,iB;MAvC1BT,CAAA;kFA0CUtB,mBAAA,CAAsB,YAAA8B,gBAAA,CAAfH,IAAI,CAACK,EAAE,kBACdhC,mBAAA,CAA6C,YAAA8B,gBAAA,CAAtCH,IAAI,CAACM,kBAAkB,yBAC9BjC,mBAAA,CAYK;MAZAJ,KAAK,EA5CpBsC,eAAA;kCA4C+D7B,QAAA,CAAA8B,iBAAiB,CAACR,IAAI,EAAES,MAAM,iBAAiB/B,QAAA,CAAA8B,iBAAiB,CAACR,IAAI,EAAES,MAAM;uCAA4D/B,QAAA,CAAA8B,iBAAiB,CAACR,IAAI,EAAES,MAAM;yBAA8C/B,QAAA,CAAA8B,iBAAiB,CAACR,IAAI,EAAES,MAAM;;QA5ClTf,gBAAA,CAAAS,gBAAA,CAiDezB,QAAA,CAAA8B,iBAAiB,CAACR,IAAI,EAAEU,IAAI,IAAG,GAClC,iBAAYhC,QAAA,CAAA8B,iBAAiB,CAACR,IAAI,EAAES,MAAM,kBAAkB/B,QAAA,CAAA8B,iBAAiB,CAACR,IAAI,EAAES,MAAM,iB,cAA1FtC,mBAAA,CAEO,QAFPwC,UAEO,GADL/B,YAAA,CAA6DC,4BAAA;MAAzCC,IAAI,EAAE;IAA+B,G,KAE1CJ,QAAA,CAAA8B,iBAAiB,CAACR,IAAI,EAAES,MAAM,kB,cAA/CtC,mBAAA,CAEO,QAFPyC,UAEO,GADLhC,YAAA,CAA2DC,4BAAA;MAAvCC,IAAI,EAAE;IAA6B,G,KAtDrEV,mBAAA,e,kBAyDUC,mBAAA,CAQK,aAPHA,mBAAA,CAMM,OANNwC,UAMM,GALJxC,mBAAA,CACkG;MAD1FyC,IAAI,EAAE1B,KAAA,CAAA2B,kBAAkB,CAACf,IAAI,CAACC,EAAE;MAA0Be,KAAK,EAAEhB,IAAI,CAACiB,QAAQ;MAAEC,QAAQ,EAAR,EAAQ;MAC9FjD,KAAK,EAAC;4BA5DtBkD,UAAA,GA6Dc9C,mBAAA,CAES;MAFAE,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAA0C,wBAAwB,CAACpB,IAAI,CAACC,EAAE;MAAGhC,KAAK,EAAC;QACvDW,YAAA,CAAwFC,4BAAA;MAApEC,IAAI,UAAUM,KAAA,CAAA2B,kBAAkB,CAACf,IAAI,CAACC,EAAE;uDA9D5EoB,UAAA,E,KAkEUhD,mBAAA,CAEK,aADHO,YAAA,CAAmC0C,sBAAA;MAArBR,IAAI,EAAEd,IAAI,CAACS;yCAE3BpC,mBAAA,CAIK,aAHHA,mBAAA,CAES;MAFDJ,KAAK,EAAC,mCAAmC;MAAEM,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAA6C,uBAAuB,CAACvB,IAAI;OAAG,QAEzF,iBAxEZwB,UAAA,E;sCA8EIpD,mBAAA,YAAe,EACfQ,YAAA,CAwEY6C,oBAAA;IAvJhBtC,UAAA,EA+EwBC,KAAA,CAAAsC,mBAAmB,CAACC,IAAI;IA/EhD,uBAAAnD,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA+EwBF,KAAA,CAAAsC,mBAAmB,CAACC,IAAI,GAAArC,MAAA;IAAEsC,KAAK,EAAC,MAAM;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAEnD,QAAA,CAAAoD,cAAc;IACpGC,OAAO,EAAE3C,KAAA,CAAA4C;;IAhFhBxC,OAAA,EAAAC,QAAA,CAiFM,MAEM,CAFNpB,mBAAA,CAEM,OAFN4D,UAEM,GADJ5D,mBAAA,CAA6D,SAA7D6D,WAA6D,EAAnC,OAAK,GAAA/B,gBAAA,CAAGf,KAAA,CAAA+C,WAAW,CAAC/B,IAAI,iB,GAGpD/B,mBAAA,CAYM,OAZN+D,WAYM,G,4BAXJ/D,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCI,mBAAA,CASM,OATNgE,WASM,GARJhE,mBAAA,CAGQ,SAHRiE,WAGQ,G,gBAFNjE,mBAAA,CAAmF;MAA5EyC,IAAI,EAAC,OAAO;MAzF/B,uBAAAtC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAyFyCF,KAAA,CAAAsC,mBAAmB,CAACa,MAAM,GAAAjD,MAAA;MAAE0B,KAAK,EAAC,MAAM;MAAC/C,KAAK,EAAC;oDAA/CmB,KAAA,CAAAsC,mBAAmB,CAACa,MAAM,E,+BACvDlE,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHRmE,WAGQ,G,gBAFNnE,mBAAA,CAAqF;MAA9EyC,IAAI,EAAC,OAAO;MA7F/B,uBAAAtC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IA6FyCF,KAAA,CAAAsC,mBAAmB,CAACa,MAAM,GAAAjD,MAAA;MAAE0B,KAAK,EAAC,QAAQ;MAAC/C,KAAK,EAAC;oDAAjDmB,KAAA,CAAAsC,mBAAmB,CAACa,MAAM,E,+BACvDlE,mBAAA,CAAiB,cAAX,MAAI,qB,OAKLe,KAAA,CAAAsC,mBAAmB,CAACa,MAAM,e,cAArCpE,mBAAA,CAqBM,OAxHZsE,WAAA,GAoGQpE,mBAAA,CAOM,OAPNqE,WAOM,G,4BANJrE,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCI,mBAAA,CAIS;MA1GnB,uBAAAG,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAsG2BF,KAAA,CAAAsC,mBAAmB,CAACiB,QAAQ,GAAArD,MAAA;MAAErB,KAAK,EAAC;2BACnDE,mBAAA,CAESyB,SAAA,QAzGrBC,WAAA,CAuGqCC,IAAA,CAAA8C,QAAQ,EAAlBC,MAAM;2BAArB1E,mBAAA,CAES;QAF2BD,GAAG,EAAE2E,MAAM,CAAC5C,EAAE;QAAGe,KAAK,EAAE6B,MAAM,CAAC5C;0BAC9D4C,MAAM,CAACzC,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAG0C,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA3C,gBAAA,CAAG0C,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAzGZC,WAAA;6EAsG2B5D,KAAA,CAAAsC,mBAAmB,CAACiB,QAAQ,E,KAO/CtE,mBAAA,CAUM,OAVN4E,WAUM,G,4BATJ5E,mBAAA,CAAuC;MAAhCJ,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BI,mBAAA,CAMM,OANN6E,WAMM,G,gBALJ7E,mBAAA,CACW;MADJyC,IAAI,EAAC,MAAM;MAhH9B,uBAAAtC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAgHwCF,KAAA,CAAAsC,mBAAmB,CAACyB,iBAAiB,GAAA7D,MAAA;MAAErB,KAAK,EAAC,6BAA6B;MACpGiD,QAAQ,EAAR;mDAD0B9B,KAAA,CAAAsC,mBAAmB,CAACyB,iBAAiB,E,GAEjE9E,mBAAA,CAES;MAFDJ,KAAK,EAAC,sDAAsD;MAAEM,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAA0E,gBAAA,IAAA1E,QAAA,CAAA0E,gBAAA,IAAA3E,IAAA,CAAgB;QAC3FG,YAAA,CAAiDC,4BAAA;MAA7BC,IAAI,EAAE;IAAmB,G,KAGjDF,YAAA,CAA2EyE,gCAAA;MAAnDpC,QAAQ,EAAE7B,KAAA,CAAAsC,mBAAmB,CAACyB;gEAI1DhF,mBAAA,CAeM,OAzIZmF,WAAA,GA2HQjF,mBAAA,CAIM,OAJNkF,WAIM,G,4BAHJlF,mBAAA,CAAqC;MAA9BJ,KAAK,EAAC;IAAY,GAAC,KAAG,sB,gBAC7BI,mBAAA,CAAsF;MAA/EyC,IAAI,EAAC,UAAU;MA7HhC,uBAAAtC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IA6H0CF,KAAA,CAAAsC,mBAAmB,CAAC8B,WAAW,GAAAlE,MAAA;MAAErB,KAAK,EAAC;mDAAvCmB,KAAA,CAAAsC,mBAAmB,CAAC8B,WAAW,E,GAC/D5E,YAAA,CAAqEyE,gCAAA;MAA7CpC,QAAQ,EAAE7B,KAAA,CAAAsC,mBAAmB,CAAC8B;6CAGxDnF,mBAAA,CAOM,OAPNoF,WAOM,G,4BANJpF,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BI,mBAAA,CACkD;MAD3CyC,IAAI,EAAC,UAAU;MAnIhC,uBAAAtC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAmI0CF,KAAA,CAAAsC,mBAAmB,CAACgC,eAAe,GAAApE,MAAA;MAAErB,KAAK,EAnIpFsC,eAAA,EAmIqF,cAAc;QAAA,kBAC3D7B,QAAA,CAAAiF;MAAgB;4CADdvE,KAAA,CAAAsC,mBAAmB,CAACgC,eAAe,E,GAExDhF,QAAA,CAAAiF,gBAAgB,I,cAA3BxF,mBAAA,CAEM,OAFNyF,WAEM,EAFyD,cAE/D,KAvIVxF,mBAAA,e,MA2IMC,mBAAA,CAWM,OAXNwF,WAWM,G,4BAVJxF,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BW,YAAA,CAEiBM,yBAAA;MA/IzBC,UAAA,EA6IiCC,KAAA,CAAAsC,mBAAmB,CAACoC,kBAAkB;MA7IvE,uBAAAtF,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA6IiCF,KAAA,CAAAsC,mBAAmB,CAACoC,kBAAkB,GAAAxE,MAAA;;MA7IvEE,OAAA,EAAAC,QAAA,CA6IyE,MAEjEjB,MAAA,SAAAA,MAAA,QA/IRkB,gBAAA,CA6IyE,QAEjE,E;MA/IRC,CAAA;uCAgJQf,YAAA,CAEiBM,yBAAA;MAlJzBC,UAAA,EAgJiCC,KAAA,CAAAsC,mBAAmB,CAACqC,WAAW;MAhJhE,uBAAAvF,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAgJiCF,KAAA,CAAAsC,mBAAmB,CAACqC,WAAW,GAAAzE,MAAA;;MAhJhEE,OAAA,EAAAC,QAAA,CAgJkE,MAE1DjB,MAAA,SAAAA,MAAA,QAlJRkB,gBAAA,CAgJkE,YAE1D,E;MAlJRC,CAAA;uCAmJQf,YAAA,CAEiBM,yBAAA;MArJzBC,UAAA,EAmJiCC,KAAA,CAAAsC,mBAAmB,CAACsC,QAAQ;MAnJ7D,uBAAAxF,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAmJiCF,KAAA,CAAAsC,mBAAmB,CAACsC,QAAQ,GAAA1E,MAAA;;MAnJ7DE,OAAA,EAAAC,QAAA,CAmJ+D,MAEvDjB,MAAA,SAAAA,MAAA,QArJRkB,gBAAA,CAmJ+D,UAEvD,E;MArJRC,CAAA;;IAAAA,CAAA;6DAyJIvB,mBAAA,cAAiB,EACjBQ,YAAA,CAiEY6C,oBAAA;IA3NhBtC,UAAA,EA0JwBC,KAAA,CAAA6E,gBAAgB,CAACtC,IAAI;IA1J7C,uBAAAnD,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA0JwBF,KAAA,CAAA6E,gBAAgB,CAACtC,IAAI,GAAArC,MAAA;IAAEsC,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAACsC,IAAI,EAAC,IAAI;IACpFrC,SAAO,EAAEnD,QAAA,CAAAyF,oBAAoB;IAAGpC,OAAO,EAAE3C,KAAA,CAAA4C;;IA3JhDxC,OAAA,EAAAC,QAAA,CA4JM,MAaM,CAbNpB,mBAAA,CAaM,OAbN+F,WAaM,G,4BAZJ/F,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCI,mBAAA,CAIM,OAJNgG,WAIM,GAHJzF,YAAA,CAEiBM,yBAAA;MAjK3BC,UAAA,EA+JmCC,KAAA,CAAAkF,cAAc;MA/JjD,wB,sCA+JmClF,KAAA,CAAAkF,cAAc,GAAAhF,MAAA,GAAsBZ,QAAA,CAAA6F,oBAAoB;;MA/J3F/E,OAAA,EAAAC,QAAA,CA+J6F,MAEnFjB,MAAA,SAAAA,MAAA,QAjKVkB,gBAAA,CA+J6F,MAEnF,E;MAjKVC,CAAA;gEAmKQtB,mBAAA,CAIM,OAJNmG,WAIM,I,kBAHJrG,mBAAA,CAEiByB,SAAA,QAtK3BC,WAAA,CAoKyCC,IAAA,CAAAC,KAAK,EAAbC,IAAI;2BAA3ByE,YAAA,CAEiBvF,yBAAA;QAFsBhB,GAAG,EAAE8B,IAAI,CAACC,EAAE;QApK7Dd,UAAA,EAoKwEC,KAAA,CAAA6E,gBAAgB,CAACS,aAAa,CAAC1E,IAAI,CAACC,EAAE;QApK9G,uBAAAX,MAAA,IAoKwEF,KAAA,CAAA6E,gBAAgB,CAACS,aAAa,CAAC1E,IAAI,CAACC,EAAE,IAAAX;;QApK9GE,OAAA,EAAAC,QAAA,CAqKY,MAAe,CArK3BC,gBAAA,CAAAS,gBAAA,CAqKeH,IAAI,CAACI,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGH,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QAtKVV,CAAA;;sCAwKQtB,mBAAA,CAAyD,KAAzDsG,WAAyD,EAApC,MAAI,GAAAxE,gBAAA,CAAGzB,QAAA,CAAAkG,kBAAkB,IAAG,MAAI,gB,GAGvDvG,mBAAA,CAOM,OAPNwG,WAOM,G,4BANJxG,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BI,mBAAA,CAIS;MAjLjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA6KyBF,KAAA,CAAA6E,gBAAgB,CAACtB,QAAQ,GAAArD,MAAA;MAAErB,KAAK,EAAC;2BAChDE,mBAAA,CAESyB,SAAA,QAhLnBC,WAAA,CA8KmCC,IAAA,CAAA8C,QAAQ,EAAlBC,MAAM;2BAArB1E,mBAAA,CAES;QAF2BD,GAAG,EAAE2E,MAAM,CAAC5C,EAAE;QAAGe,KAAK,EAAE6B,MAAM,CAAC5C;0BAC9D4C,MAAM,CAACzC,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAG0C,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA3C,gBAAA,CAAG0C,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAhLV+B,WAAA;6EA6KyB1F,KAAA,CAAA6E,gBAAgB,CAACtB,QAAQ,E,KAO5CtE,mBAAA,CAyBM,OAzBN0G,WAyBM,G,4BAxBJ1G,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BI,mBAAA,CASM,OATN2G,WASM,GARJ3G,mBAAA,CAGQ,SAHR4G,WAGQ,G,gBAFN5G,mBAAA,CAA4F;MAArFyC,IAAI,EAAC,OAAO;MAxL/B,uBAAAtC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAwLyCF,KAAA,CAAA6E,gBAAgB,CAACiB,aAAa,GAAA5F,MAAA;MAAE0B,KAAK,EAAC,WAAW;MAAC/C,KAAK,EAAC;oDAAxDmB,KAAA,CAAA6E,gBAAgB,CAACiB,aAAa,E,+BAC3D7G,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHR8G,WAGQ,G,gBAFN9G,mBAAA,CAA4F;MAArFyC,IAAI,EAAC,OAAO;MA5L/B,uBAAAtC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4LyCF,KAAA,CAAA6E,gBAAgB,CAACiB,aAAa,GAAA5F,MAAA;MAAE0B,KAAK,EAAC,WAAW;MAAC/C,KAAK,EAAC;oDAAxDmB,KAAA,CAAA6E,gBAAgB,CAACiB,aAAa,E,+BAC3D7G,mBAAA,CAAiB,cAAX,MAAI,qB,KAIHe,KAAA,CAAA6E,gBAAgB,CAACiB,aAAa,oB,cAAzC/G,mBAAA,CAWM,OAXNiH,WAWM,GAVJ/G,mBAAA,CASM,OATNgH,WASM,GARJhH,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAoC;MAA7BJ,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BI,mBAAA,CAAiF;MAA1EyC,IAAI,EAAC,MAAM;MArMhC,uBAAAtC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAqM0CF,KAAA,CAAA6E,gBAAgB,CAACqB,aAAa,GAAAhG,MAAA;MAAErB,KAAK,EAAC;mDAAtCmB,KAAA,CAAA6E,gBAAgB,CAACqB,aAAa,E,KAE5DjH,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAoC;MAA7BJ,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BI,mBAAA,CAAiF;MAA1EyC,IAAI,EAAC,MAAM;MAzMhC,uBAAAtC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAyM0CF,KAAA,CAAA6E,gBAAgB,CAACsB,aAAa,GAAAjG,MAAA;MAAErB,KAAK,EAAC;mDAAtCmB,KAAA,CAAA6E,gBAAgB,CAACsB,aAAa,E,WAzMxEnH,mBAAA,e,GA+MMC,mBAAA,CAWM,OAXNmH,WAWM,G,4BAVJnH,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BW,YAAA,CAEiBM,yBAAA;MAnNzBC,UAAA,EAiNiCC,KAAA,CAAA6E,gBAAgB,CAACwB,YAAY;MAjN9D,uBAAAjH,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAiNiCF,KAAA,CAAA6E,gBAAgB,CAACwB,YAAY,GAAAnG,MAAA;;MAjN9DE,OAAA,EAAAC,QAAA,CAiNgE,MAExDjB,MAAA,SAAAA,MAAA,QAnNRkB,gBAAA,CAiNgE,YAExD,E;MAnNRC,CAAA;uCAoNQf,YAAA,CAEiBM,yBAAA;MAtNzBC,UAAA,EAoNiCC,KAAA,CAAA6E,gBAAgB,CAACyB,WAAW;MApN7D,uBAAAlH,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAoNiCF,KAAA,CAAA6E,gBAAgB,CAACyB,WAAW,GAAApG,MAAA;;MApN7DE,OAAA,EAAAC,QAAA,CAoN+D,MAEvDjB,MAAA,SAAAA,MAAA,QAtNRkB,gBAAA,CAoN+D,UAEvD,E;MAtNRC,CAAA;uCAuNQf,YAAA,CAEiBM,yBAAA;MAzNzBC,UAAA,EAuNiCC,KAAA,CAAA6E,gBAAgB,CAAC0B,gBAAgB;MAvNlE,uBAAAnH,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAuNiCF,KAAA,CAAA6E,gBAAgB,CAAC0B,gBAAgB,GAAArG,MAAA;;MAvNlEE,OAAA,EAAAC,QAAA,CAuNoE,MAE5DjB,MAAA,SAAAA,MAAA,QAzNRkB,gBAAA,CAuNoE,aAE5D,E;MAzNRC,CAAA;;IAAAA,CAAA;6DA6NIvB,mBAAA,cAAiB,EACjBQ,YAAA,CA8BY6C,oBAAA;IA5PhBtC,UAAA,EA8NwBC,KAAA,CAAAwG,eAAe,CAACjE,IAAI;IA9N5C,uBAAAnD,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8NwBF,KAAA,CAAAwG,eAAe,CAACjE,IAAI,GAAArC,MAAA;IAAEsC,KAAK,EAAC,UAAU;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAEnD,QAAA,CAAAmH,gBAAgB;IACtG9D,OAAO,EAAE3C,KAAA,CAAA4C;;IA/NhBxC,OAAA,EAAAC,QAAA,CAgOM,MAQM,CARNpB,mBAAA,CAQM,OARNyH,WAQM,G,4BAPJzH,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCI,mBAAA,CAKM,OALN0H,WAKM,I,kBAJJ5H,mBAAA,CAGiByB,SAAA,QAtO3BC,WAAA,CAmOyCnB,QAAA,CAAAsH,iBAAiB,EAAzBhG,IAAI;2BAA3ByE,YAAA,CAGiBvF,yBAAA;QAHkChB,GAAG,EAAE8B,IAAI,CAACC,EAAE;QAnOzEd,UAAA,EAoOqBC,KAAA,CAAAwG,eAAe,CAAClB,aAAa,CAAC1E,IAAI,CAACC,EAAE;QApO1D,uBAAAX,MAAA,IAoOqBF,KAAA,CAAAwG,eAAe,CAAClB,aAAa,CAAC1E,IAAI,CAACC,EAAE,IAAAX;;QApO1DE,OAAA,EAAAC,QAAA,CAqOY,MAAe,CArO3BC,gBAAA,CAAAS,gBAAA,CAqOeH,IAAI,CAACI,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGH,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QAtOVV,CAAA;;wCA0OMtB,mBAAA,CAOM,OAPN4H,WAOM,G,4BANJ5H,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCI,mBAAA,CAIS;MAhPjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4OyBF,KAAA,CAAAwG,eAAe,CAACjD,QAAQ,GAAArD,MAAA;MAAErB,KAAK,EAAC;2BAC/CE,mBAAA,CAESyB,SAAA,QA/OnBC,WAAA,CA6OmCC,IAAA,CAAA8C,QAAQ,EAAlBC,MAAM;2BAArB1E,mBAAA,CAES;QAF2BD,GAAG,EAAE2E,MAAM,CAAC5C,EAAE;QAAGe,KAAK,EAAE6B,MAAM,CAAC5C;0BAC9D4C,MAAM,CAACzC,IAAI,wBA9O1B8F,WAAA;6EA4OyB9G,KAAA,CAAAwG,eAAe,CAACjD,QAAQ,E,KAO3CtE,mBAAA,CAQM,OARN8H,WAQM,G,4BAPJ9H,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BW,YAAA,CAEiBM,yBAAA;MAvPzBC,UAAA,EAqPiCC,KAAA,CAAAwG,eAAe,CAACQ,iBAAiB;MArPlE,uBAAA5H,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAqPiCF,KAAA,CAAAwG,eAAe,CAACQ,iBAAiB,GAAA9G,MAAA;;MArPlEE,OAAA,EAAAC,QAAA,CAqPoE,MAE5DjB,MAAA,SAAAA,MAAA,QAvPRkB,gBAAA,CAqPoE,eAE5D,E;MAvPRC,CAAA;uCAwPQf,YAAA,CAEiBM,yBAAA;MA1PzBC,UAAA,EAwPiCC,KAAA,CAAAwG,eAAe,CAACS,iBAAiB;MAxPlE,uBAAA7H,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAwPiCF,KAAA,CAAAwG,eAAe,CAACS,iBAAiB,GAAA/G,MAAA;;MAxPlEE,OAAA,EAAAC,QAAA,CAwPoE,MAE5DjB,MAAA,SAAAA,MAAA,QA1PRkB,gBAAA,CAwPoE,aAE5D,E;MA1PRC,CAAA;;IAAAA,CAAA;6DA8PIvB,mBAAA,cAAiB,EACjBQ,YAAA,CAyCY6C,oBAAA;IAxShBtC,UAAA,EA+PwBC,KAAA,CAAAkH,mBAAmB,CAAC3E,IAAI;IA/PhD,uBAAAnD,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA+PwBF,KAAA,CAAAkH,mBAAmB,CAAC3E,IAAI,GAAArC,MAAA;IAAEsC,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAAC9C,IAAI,EAAC,sBAAsB;IAACyH,MAAM,EAAN,EAAM;IAChH1E,SAAO,EAAEnD,QAAA,CAAA8H,cAAc;IAAGzE,OAAO,EAAE3C,KAAA,CAAA4C;;IAhQ1CxC,OAAA,EAAAC,QAAA,CAiQM,MAEM,C,4BAFNpB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAA4C,IACrDI,mBAAA,CAA+C,WAA5C,0CAAwC,E,sBAG7CA,mBAAA,CAQM,OARNoI,WAQM,G,4BAPJpI,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCI,mBAAA,CAKM,OALNqI,WAKM,I,kBAJJvI,mBAAA,CAGiByB,SAAA,QA3Q3BC,WAAA,CAwQyCnB,QAAA,CAAAsH,iBAAiB,EAAzBhG,IAAI;2BAA3ByE,YAAA,CAGiBvF,yBAAA;QAHkChB,GAAG,EAAE8B,IAAI,CAACC,EAAE;QAxQzEd,UAAA,EAyQqBC,KAAA,CAAAkH,mBAAmB,CAAC5B,aAAa,CAAC1E,IAAI,CAACC,EAAE;QAzQ9D,uBAAAX,MAAA,IAyQqBF,KAAA,CAAAkH,mBAAmB,CAAC5B,aAAa,CAAC1E,IAAI,CAACC,EAAE,IAAAX;;QAzQ9DE,OAAA,EAAAC,QAAA,CA0QY,MAAe,CA1Q3BC,gBAAA,CAAAS,gBAAA,CA0QeH,IAAI,CAACI,IAAI,IAAG,IAAE,GAAAD,gBAAA,CAAGH,IAAI,CAACK,EAAE,IAAG,IAChC,gB;QA3QVV,CAAA;;wCA+QMtB,mBAAA,CAOM,OAPNsI,WAOM,G,4BANJtI,mBAAA,CAAwC;MAAjCJ,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCI,mBAAA,CAIS;MArRjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAiRyBF,KAAA,CAAAkH,mBAAmB,CAAC3D,QAAQ,GAAArD,MAAA;MAAErB,KAAK,EAAC;2BACnDE,mBAAA,CAESyB,SAAA,QApRnBC,WAAA,CAkRmCnB,QAAA,CAAAkI,iBAAiB,EAA3B/D,MAAM;2BAArB1E,mBAAA,CAES;QAFoCD,GAAG,EAAE2E,MAAM,CAAC5C,EAAE;QAAGe,KAAK,EAAE6B,MAAM,CAAC5C;0BACvE4C,MAAM,CAACzC,IAAI,IAAG,UAAQ,GAAAD,gBAAA,CAAG0C,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA3C,gBAAA,CAAG0C,MAAM,CAACE,UAAU,IAAG,KAC9E,uBApRV8D,WAAA;6EAiRyBzH,KAAA,CAAAkH,mBAAmB,CAAC3D,QAAQ,E,KAO/CtE,mBAAA,CASM,OATNyI,WASM,G,4BARJzI,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BI,mBAAA,CAMS;MAhSjB,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA0RyBF,KAAA,CAAAkH,mBAAmB,CAACS,MAAM,GAAAzH,MAAA;MAAErB,KAAK,EAAC;oCACjDI,mBAAA,CAAiD;MAAzC2C,KAAK,EAAC;IAAmB,GAAC,QAAM,qBACxC3C,mBAAA,CAA2C;MAAnC2C,KAAK,EAAC;IAAe,GAAC,MAAI,qBAClC3C,mBAAA,CAA6C;MAArC2C,KAAK,EAAC;IAAiB,GAAC,MAAI,qBACpC3C,mBAAA,CAAwC;MAAhC2C,KAAK,EAAC;IAAY,GAAC,MAAI,qBAC/B3C,mBAAA,CAAmC;MAA3B2C,KAAK,EAAC;IAAO,GAAC,MAAI,oB,2CALX5B,KAAA,CAAAkH,mBAAmB,CAACS,MAAM,E,KAS7C1I,mBAAA,CAIM,OAJN2I,WAIM,G,4BAHJ3I,mBAAA,CAAsC;MAA/BJ,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BI,mBAAA,CACuC;MAtS/C,uBAAAG,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAqS2BF,KAAA,CAAAkH,mBAAmB,CAACW,WAAW,GAAA3H,MAAA;MAAErB,KAAK,EAAC,cAAc;MAACiJ,IAAI,EAAC,GAAG;MAC/EC,WAAW,EAAC;mDADK/H,KAAA,CAAAkH,mBAAmB,CAACW,WAAW,E;IArS1DtH,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}