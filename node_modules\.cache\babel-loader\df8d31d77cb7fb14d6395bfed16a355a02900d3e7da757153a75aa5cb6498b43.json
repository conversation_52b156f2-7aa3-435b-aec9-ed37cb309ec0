{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport SecurityOverview from '../views/SecurityOverview.vue';\nimport HostManagement from '../views/HostManagement.vue';\nimport PasswordPolicies from '../views/PasswordPolicies.vue';\nimport ScheduledTasks from '../views/ScheduledTasks.vue';\nimport NotificationTest from '../views/NotificationTest.vue';\nconst routes = [{\n  path: '/',\n  redirect: '/overview'\n}, {\n  path: '/overview',\n  name: 'SecurityOverview',\n  component: SecurityOverview,\n  meta: {\n    title: '安全概览',\n    icon: 'tachometer-alt'\n  }\n}, {\n  path: '/hosts',\n  name: 'HostManagement',\n  component: HostManagement,\n  meta: {\n    title: '主机管理',\n    icon: 'server'\n  }\n}, {\n  path: '/policies',\n  name: 'PasswordPolicies',\n  component: PasswordPolicies,\n  meta: {\n    title: '密码策略',\n    icon: 'shield-alt'\n  }\n}, {\n  path: '/tasks',\n  name: 'ScheduledTasks',\n  component: ScheduledTasks,\n  meta: {\n    title: '定时任务',\n    icon: 'clock'\n  }\n}];\nconst router = createRouter({\n  history: createWebHistory(),\n  routes\n});\nrouter.beforeEach((to, from, next) => {\n  // 设置文档标题\n  document.title = `${to.meta.title || '主页'} - 密码管理系统`;\n  next();\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "SecurityOverview", "HostManagement", "PasswordPolicies", "ScheduledTasks", "NotificationTest", "routes", "path", "redirect", "name", "component", "meta", "title", "icon", "router", "history", "beforeEach", "to", "from", "next", "document"], "sources": ["D:/demo/ooo/pass/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\r\nimport SecurityOverview from '../views/SecurityOverview.vue'\r\nimport HostManagement from '../views/HostManagement.vue'\r\nimport PasswordPolicies from '../views/PasswordPolicies.vue'\r\nimport ScheduledTasks from '../views/ScheduledTasks.vue'\r\nimport NotificationTest from '../views/NotificationTest.vue'\r\n\r\nconst routes = [\r\n    {\r\n        path: '/',\r\n        redirect: '/overview'\r\n    },\r\n    {\r\n        path: '/overview',\r\n        name: 'SecurityOverview',\r\n        component: SecurityOverview,\r\n        meta: { title: '安全概览', icon: 'tachometer-alt' }\r\n    },\r\n    {\r\n        path: '/hosts',\r\n        name: 'HostManagement',\r\n        component: HostManagement,\r\n        meta: { title: '主机管理', icon: 'server' }\r\n    },\r\n    {\r\n        path: '/policies',\r\n        name: 'PasswordPolicies',\r\n        component: PasswordPolicies,\r\n        meta: { title: '密码策略', icon: 'shield-alt' }\r\n    },\r\n    {\r\n        path: '/tasks',\r\n        name: 'ScheduledTasks',\r\n        component: ScheduledTasks,\r\n        meta: { title: '定时任务', icon: 'clock' }\r\n    }\r\n]\r\n\r\nconst router = createRouter({\r\n    history: createWebHistory(),\r\n    routes\r\n})\r\n\r\nrouter.beforeEach((to, from, next) => {\r\n    // 设置文档标题\r\n    document.title = `${to.meta.title || '主页'} - 密码管理系统`\r\n    next()\r\n})\r\n\r\nexport default router "], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,gBAAgB,MAAM,+BAA+B;AAE5D,MAAMC,MAAM,GAAG,CACX;EACIC,IAAI,EAAE,GAAG;EACTC,QAAQ,EAAE;AACd,CAAC,EACD;EACID,IAAI,EAAE,WAAW;EACjBE,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAET,gBAAgB;EAC3BU,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAiB;AAClD,CAAC,EACD;EACIN,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAER,cAAc;EACzBS,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAS;AAC1C,CAAC,EACD;EACIN,IAAI,EAAE,WAAW;EACjBE,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAEP,gBAAgB;EAC3BQ,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAa;AAC9C,CAAC,EACD;EACIN,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEN,cAAc;EACzBO,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAQ;AACzC,CAAC,CACJ;AAED,MAAMC,MAAM,GAAGf,YAAY,CAAC;EACxBgB,OAAO,EAAEf,gBAAgB,CAAC,CAAC;EAC3BM;AACJ,CAAC,CAAC;AAEFQ,MAAM,CAACE,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EAClC;EACAC,QAAQ,CAACR,KAAK,GAAG,GAAGK,EAAE,CAACN,IAAI,CAACC,KAAK,IAAI,IAAI,WAAW;EACpDO,IAAI,CAAC,CAAC;AACV,CAAC,CAAC;AAEF,eAAeL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}