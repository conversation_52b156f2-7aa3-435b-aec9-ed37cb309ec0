{"ast": null, "code": "import { createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", null, [_createElementVNode(\"div\", {\n    class: _normalizeClass([\"password-strength\", {\n      'password-strength-low': $options.score === 1,\n      'password-strength-medium': $options.score === 2,\n      'password-strength-high': $options.score === 3 || $options.score === 4\n    }])\n  }, _cache[0] || (_cache[0] = [_createElementVNode(\"div\", {\n    class: \"password-strength-segment segment-1\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"password-strength-segment segment-2\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"password-strength-segment segment-3\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"password-strength-segment segment-4\"\n  }, null, -1 /* HOISTED */)]), 2 /* CLASS */), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"text-right text-xs mt-1\", $options.strengthTextClass])\n  }, _toDisplayString($options.strengthText), 3 /* TEXT, CLASS */)]);\n}", "map": {"version": 3, "names": ["_createElementBlock", "_createElementVNode", "class", "_normalizeClass", "$options", "score", "strengthTextClass", "strengthText"], "sources": ["D:\\demo\\ooo\\pass\\src\\components\\PasswordStrengthMeter.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div \r\n      class=\"password-strength\" \r\n      :class=\"{\r\n        'password-strength-low': score === 1,\r\n        'password-strength-medium': score === 2,\r\n        'password-strength-high': score === 3 || score === 4\r\n      }\"\r\n    >\r\n      <div class=\"password-strength-segment segment-1\"></div>\r\n      <div class=\"password-strength-segment segment-2\"></div>\r\n      <div class=\"password-strength-segment segment-3\"></div>\r\n      <div class=\"password-strength-segment segment-4\"></div>\r\n    </div>\r\n    <div class=\"text-right text-xs mt-1\" :class=\"strengthTextClass\">\r\n      {{ strengthText }}\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'PasswordStrengthMeter',\r\n  props: {\r\n    password: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  computed: {\r\n    score() {\r\n      if (!this.password) return 0\r\n      \r\n      // 简单评分算法，实际应用中可使用更复杂的算法\r\n      const length = this.password.length\r\n      const hasLowerCase = /[a-z]/.test(this.password)\r\n      const hasUpperCase = /[A-Z]/.test(this.password)\r\n      const hasNumbers = /\\d/.test(this.password)\r\n      const hasSpecialChars = /[^A-Za-z0-9]/.test(this.password)\r\n      \r\n      let score = 0\r\n      \r\n      if (length >= 8) score += 1\r\n      if (length >= 12) score += 1\r\n      if (hasLowerCase && hasUpperCase) score += 1\r\n      if (hasNumbers) score += 1\r\n      if (hasSpecialChars) score += 1\r\n      \r\n      // 最高分为4\r\n      return Math.min(4, Math.floor(score / 1.25))\r\n    },\r\n    \r\n    strengthText() {\r\n      switch (this.score) {\r\n        case 0: return '请输入密码'\r\n        case 1: return '弱'\r\n        case 2: return '中等'\r\n        case 3: return '强'\r\n        case 4: return '非常强'\r\n        default: return ''\r\n      }\r\n    },\r\n    \r\n    strengthTextClass() {\r\n      switch (this.score) {\r\n        case 0: return 'text-gray-500'\r\n        case 1: return 'text-red-600'\r\n        case 2: return 'text-yellow-600'\r\n        case 3:\r\n        case 4: return 'text-green-600'\r\n        default: return ''\r\n      }\r\n    }\r\n  }\r\n}\r\n</script> "], "mappings": ";;uBACEA,mBAAA,CAiBM,cAhBJC,mBAAA,CAYM;IAXJC,KAAK,EAHXC,eAAA,EAGY,mBAAmB;+BACmBC,QAAA,CAAAC,KAAK;kCAA6CD,QAAA,CAAAC,KAAK;gCAA2CD,QAAA,CAAAC,KAAK,UAAUD,QAAA,CAAAC,KAAK;;gCAMlKJ,mBAAA,CAAuD;IAAlDC,KAAK,EAAC;EAAqC,4BAChDD,mBAAA,CAAuD;IAAlDC,KAAK,EAAC;EAAqC,4BAChDD,mBAAA,CAAuD;IAAlDC,KAAK,EAAC;EAAqC,4BAChDD,mBAAA,CAAuD;IAAlDC,KAAK,EAAC;EAAqC,2B,mBAElDD,mBAAA,CAEM;IAFDC,KAAK,EAfdC,eAAA,EAee,yBAAyB,EAASC,QAAA,CAAAE,iBAAiB;sBACzDF,QAAA,CAAAG,YAAY,wB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}