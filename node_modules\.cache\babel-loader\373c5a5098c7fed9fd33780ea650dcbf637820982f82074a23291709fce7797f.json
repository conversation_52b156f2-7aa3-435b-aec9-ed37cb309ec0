{"ast": null, "code": "export default {\n  name: 'App',\n  computed: {\n    routes() {\n      return this.$router.options.routes.filter(route => route.meta && route.meta.title);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "computed", "routes", "$router", "options", "filter", "route", "meta", "title"], "sources": ["D:\\demo\\ooo\\pass\\src\\App.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <header class=\"header\">\r\n            <div class=\"container mx-auto px-4 py-3 flex justify-between items-center\">\r\n                <div class=\"flex items-center\">\r\n                    <div class=\"text-blue-600 mr-2\">\r\n                        <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"text-2xl\" />\r\n                    </div>\r\n                    <h1 class=\"text-xl font-semibold\">密码管理系统</h1>\r\n                </div>\r\n                <div>\r\n                    <button class=\"p-2 text-gray-500 hover:text-gray-700\">\r\n                        <font-awesome-icon :icon=\"['fas', 'cog']\" />\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </header>\r\n\r\n        <main class=\"container mx-auto px-4 py-6\">\r\n            <!-- 导航标签 -->\r\n            <nav class=\"nav-tabs\">\r\n                <router-link v-for=\"route in routes\" :key=\"route.path\" :to=\"route.path\" class=\"nav-item\"\r\n                    active-class=\"active\">\r\n                    <font-awesome-icon :icon=\"['fas', route.meta.icon]\" class=\"mr-2\" />\r\n                    <span>{{ route.meta.title }}</span>\r\n                </router-link>\r\n            </nav>\r\n\r\n            <!-- 路由视图 -->\r\n            <router-view v-slot=\"{ Component }\">\r\n                <transition name=\"fade\" mode=\"out-in\">\r\n                    <component :is=\"Component\" />\r\n                </transition>\r\n            </router-view>\r\n        </main>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: 'App',\r\n    computed: {\r\n        routes() {\r\n            return this.$router.options.routes.filter(route => route.meta && route.meta.title)\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style>\r\nbody {\r\n    font-family: \"PingFang SC\", \"Microsoft YaHei\", sans-serif;\r\n    background-color: #f8f9fa;\r\n    color: #333;\r\n}\r\n\r\n.header {\r\n    background-color: white;\r\n    border-bottom: 1px solid #e5e7eb;\r\n}\r\n\r\n.nav-tabs {\r\n    display: flex;\r\n    border-bottom: 1px solid #e5e7eb;\r\n    margin-bottom: 1.5rem;\r\n}\r\n\r\n.nav-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px 16px;\r\n    border-bottom: 2px solid transparent;\r\n    color: #666;\r\n    text-decoration: none;\r\n}\r\n\r\n.nav-item.active {\r\n    color: #2563eb;\r\n    border-bottom-color: #2563eb;\r\n    font-weight: 500;\r\n}\r\n\r\n.nav-item:hover:not(.active) {\r\n    color: #1e40af;\r\n    background-color: #f3f4f6;\r\n}\r\n\r\n/* 过渡效果 */\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n    transition: opacity 0.2s ease;\r\n}\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n    opacity: 0;\r\n}\r\n</style>"], "mappings": "AAuCA,eAAe;EACXA,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE;IACNC,MAAMA,CAAA,EAAG;MACL,OAAO,IAAI,CAACC,OAAO,CAACC,OAAO,CAACF,MAAM,CAACG,MAAM,CAACC,KAAI,IAAKA,KAAK,CAACC,IAAG,IAAKD,KAAK,CAACC,IAAI,CAACC,KAAK;IACrF;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}