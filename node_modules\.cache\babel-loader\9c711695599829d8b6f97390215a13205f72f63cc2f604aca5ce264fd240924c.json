{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, vModelText as _vModelText, withDirectives as _withDirectives, vModelSelect as _vModelSelect, toDisplayString as _toDisplayString, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, vModelDynamic as _vModelDynamic, vModelCheckbox as _vModelCheckbox, createBlock as _createBlock, vModelRadio as _vModelRadio } from \"vue\";\nconst _hoisted_1 = {\n  class: \"bg-white shadow rounded-lg p-4 mb-6\"\n};\nconst _hoisted_2 = {\n  class: \"flex flex-wrap items-center justify-between\"\n};\nconst _hoisted_3 = {\n  class: \"flex space-x-3 mb-2 sm:mb-0\"\n};\nconst _hoisted_4 = {\n  class: \"flex items-center space-x-4\"\n};\nconst _hoisted_5 = {\n  class: \"flex items-center border rounded-md overflow-hidden\"\n};\nconst _hoisted_6 = {\n  class: \"relative\"\n};\nconst _hoisted_7 = {\n  class: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n};\nconst _hoisted_8 = {\n  class: \"relative\"\n};\nconst _hoisted_9 = {\n  class: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n};\nconst _hoisted_10 = {\n  key: 0,\n  class: \"bg-white rounded-lg shadow overflow-hidden\"\n};\nconst _hoisted_11 = {\n  class: \"px-4 py-3 bg-gray-50 border-b flex justify-between items-center\"\n};\nconst _hoisted_12 = {\n  class: \"text-sm text-gray-700\"\n};\nconst _hoisted_13 = {\n  class: \"font-medium\"\n};\nconst _hoisted_14 = {\n  class: \"font-medium\"\n};\nconst _hoisted_15 = {\n  class: \"flex space-x-2\"\n};\nconst _hoisted_16 = {\n  class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n};\nconst _hoisted_17 = {\n  class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n};\nconst _hoisted_18 = {\n  class: \"min-w-full divide-y divide-gray-200\"\n};\nconst _hoisted_19 = {\n  class: \"bg-gray-50\"\n};\nconst _hoisted_20 = {\n  scope: \"col\",\n  class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n};\nconst _hoisted_21 = {\n  class: \"bg-white divide-y divide-gray-200\"\n};\nconst _hoisted_22 = {\n  class: \"bg-gray-100\"\n};\nconst _hoisted_23 = {\n  colspan: \"8\",\n  class: \"px-6 py-2\"\n};\nconst _hoisted_24 = {\n  class: \"flex items-center justify-between\"\n};\nconst _hoisted_25 = {\n  class: \"font-medium text-gray-700\"\n};\nconst _hoisted_26 = [\"onClick\"];\nconst _hoisted_27 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_28 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_29 = {\n  class: \"ml-2 font-medium text-gray-900\"\n};\nconst _hoisted_30 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_31 = {\n  class: \"text-sm text-gray-900\"\n};\nconst _hoisted_32 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_33 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_34 = {\n  class: \"text-sm font-medium text-gray-900\"\n};\nconst _hoisted_35 = {\n  key: 0,\n  class: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\"\n};\nconst _hoisted_36 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_37 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_38 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_39 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_40 = {\n  key: 1,\n  class: \"ml-1\"\n};\nconst _hoisted_41 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_42 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_43 = {\n  class: \"flex-grow\"\n};\nconst _hoisted_44 = [\"type\", \"value\"];\nconst _hoisted_45 = [\"onClick\"];\nconst _hoisted_46 = {\n  class: \"px-6 py-4 whitespace-nowrap\"\n};\nconst _hoisted_47 = {\n  class: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\"\n};\nconst _hoisted_48 = {\n  class: \"flex space-x-2\"\n};\nconst _hoisted_49 = [\"onClick\"];\nconst _hoisted_50 = [\"onClick\"];\nconst _hoisted_51 = {\n  key: 0\n};\nconst _hoisted_52 = {\n  colspan: \"8\",\n  class: \"px-6 py-10 text-center\"\n};\nconst _hoisted_53 = {\n  class: \"text-gray-500\"\n};\nconst _hoisted_54 = {\n  class: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\"\n};\nconst _hoisted_55 = {\n  class: \"px-4 py-5 sm:p-6\"\n};\nconst _hoisted_56 = {\n  class: \"flex justify-between items-start mb-4\"\n};\nconst _hoisted_57 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_58 = {\n  class: \"text-lg font-medium text-gray-900\"\n};\nconst _hoisted_59 = {\n  class: \"text-sm text-gray-500\"\n};\nconst _hoisted_60 = {\n  class: \"space-y-4\"\n};\nconst _hoisted_61 = {\n  class: \"flex justify-between items-center mb-2\"\n};\nconst _hoisted_62 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_63 = {\n  class: \"text-sm font-medium text-gray-900\"\n};\nconst _hoisted_64 = {\n  key: 0,\n  class: \"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\"\n};\nconst _hoisted_65 = [\"onClick\"];\nconst _hoisted_66 = {\n  class: \"mb-2\"\n};\nconst _hoisted_67 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_68 = [\"type\", \"value\"];\nconst _hoisted_69 = [\"onClick\"];\nconst _hoisted_70 = {\n  class: \"grid grid-cols-2 gap-2 text-xs\"\n};\nconst _hoisted_71 = {\n  class: \"text-gray-900\"\n};\nconst _hoisted_72 = {\n  key: 0,\n  class: \"ml-1\"\n};\nconst _hoisted_73 = {\n  class: \"mt-4 flex justify-center\"\n};\nconst _hoisted_74 = [\"onClick\"];\nconst _hoisted_75 = {\n  class: \"mb-4\"\n};\nconst _hoisted_76 = {\n  class: \"px-3 py-2 bg-gray-50 rounded-md\"\n};\nconst _hoisted_77 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_78 = {\n  class: \"flex space-x-3\"\n};\nconst _hoisted_79 = {\n  key: 0,\n  class: \"form-group mb-4\"\n};\nconst _hoisted_80 = [\"value\"];\nconst _hoisted_81 = {\n  class: \"mt-3\"\n};\nconst _hoisted_82 = {\n  class: \"flex justify-between mb-1\"\n};\nconst _hoisted_83 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_84 = [\"type\"];\nconst _hoisted_85 = {\n  key: 1,\n  class: \"space-y-4\"\n};\nconst _hoisted_86 = {\n  class: \"form-group\"\n};\nconst _hoisted_87 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_88 = [\"type\"];\nconst _hoisted_89 = {\n  class: \"form-group\"\n};\nconst _hoisted_90 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_91 = [\"type\"];\nconst _hoisted_92 = {\n  key: 0,\n  class: \"text-sm text-red-500 mt-1\"\n};\nconst _hoisted_93 = {\n  class: \"space-y-2 mt-4\"\n};\nconst _hoisted_94 = {\n  class: \"mb-4\"\n};\nconst _hoisted_95 = {\n  class: \"px-3 py-2 bg-gray-50 rounded-md\"\n};\nconst _hoisted_96 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_97 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_98 = {\n  class: \"relative inline-block w-10 mr-2 align-middle select-none\"\n};\nconst _hoisted_99 = {\n  class: \"form-group mb-4\"\n};\nconst _hoisted_100 = [\"value\"];\nconst _hoisted_101 = {\n  class: \"form-group\"\n};\nconst _hoisted_102 = {\n  class: \"flex justify-between mb-1\"\n};\nconst _hoisted_103 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_104 = [\"type\"];\nconst _hoisted_105 = {\n  class: \"form-group\"\n};\nconst _hoisted_106 = {\n  class: \"mb-2\"\n};\nconst _hoisted_107 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_108 = {\n  class: \"form-text\"\n};\nconst _hoisted_109 = {\n  class: \"form-group\"\n};\nconst _hoisted_110 = [\"value\"];\nconst _hoisted_111 = {\n  class: \"form-group\"\n};\nconst _hoisted_112 = {\n  class: \"flex space-x-4\"\n};\nconst _hoisted_113 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_114 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_115 = {\n  key: 0,\n  class: \"mt-3\"\n};\nconst _hoisted_116 = {\n  class: \"grid grid-cols-2 gap-4\"\n};\nconst _hoisted_117 = {\n  class: \"form-group\"\n};\nconst _hoisted_118 = {\n  class: \"form-group\"\n};\nconst _hoisted_119 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_120 = {\n  class: \"form-group\"\n};\nconst _hoisted_121 = [\"value\"];\nconst _hoisted_122 = {\n  class: \"form-group\"\n};\nconst _hoisted_123 = {\n  class: \"form-group\"\n};\nconst _hoisted_124 = {\n  class: \"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\"\n};\nconst _hoisted_125 = {\n  class: \"form-group\"\n};\nconst _hoisted_126 = [\"value\"];\nconst _hoisted_127 = {\n  class: \"form-group\"\n};\nconst _hoisted_128 = {\n  class: \"form-group\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\");\n  const _component_CustomCheckbox = _resolveComponent(\"CustomCheckbox\");\n  const _component_StatusBadge = _resolveComponent(\"StatusBadge\");\n  const _component_PasswordStrengthMeter = _resolveComponent(\"PasswordStrengthMeter\");\n  const _component_BaseModal = _resolveComponent(\"BaseModal\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createCommentVNode(\" 操作按钮 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.showEmergencyReset && $options.showEmergencyReset(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'exclamation-triangle'],\n    class: \"mr-2\"\n  }), _cache[51] || (_cache[51] = _createElementVNode(\"span\", null, \"紧急重置\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.openBatchUpdateModal && $options.openBatchUpdateModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'key'],\n    class: \"mr-2\"\n  }), _cache[52] || (_cache[52] = _createElementVNode(\"span\", null, \"批量更新密码\", -1 /* HOISTED */))]), _createElementVNode(\"button\", {\n    class: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.openBatchApplyModal && $options.openBatchApplyModal(...args))\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'shield-alt'],\n    class: \"mr-2\"\n  }), _cache[53] || (_cache[53] = _createElementVNode(\"span\", null, \"批量应用策略\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" 视图切换 \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1 focus:outline-none\", {\n      'bg-blue-500 text-white': $data.viewMode === 'table',\n      'bg-gray-100 text-gray-600': $data.viewMode !== 'table'\n    }]),\n    onClick: _cache[3] || (_cache[3] = $event => $data.viewMode = 'table')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'table'],\n    class: \"mr-1\"\n  }), _cache[54] || (_cache[54] = _createTextVNode(\" 表格 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n    class: _normalizeClass([\"px-3 py-1 focus:outline-none\", {\n      'bg-blue-500 text-white': $data.viewMode === 'card',\n      'bg-gray-100 text-gray-600': $data.viewMode !== 'card'\n    }]),\n    onClick: _cache[4] || (_cache[4] = $event => $data.viewMode = 'card')\n  }, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'th-large'],\n    class: \"mr-1\"\n  }), _cache[55] || (_cache[55] = _createTextVNode(\" 卡片 \"))], 2 /* CLASS */)]), _createCommentVNode(\" 筛选 \"), _createElementVNode(\"div\", _hoisted_6, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.filterText = $event),\n    placeholder: \"筛选主机...\",\n    class: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.filterText]]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"text-gray-400\"\n  })])]), _createCommentVNode(\" 账号筛选 \"), _createElementVNode(\"div\", _hoisted_8, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.accountFilterText = $event),\n    placeholder: \"筛选账号...\",\n    class: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.accountFilterText]]), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'user'],\n    class: \"text-gray-400\"\n  })])]), _createCommentVNode(\" 状态筛选 \"), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.statusFilter = $event),\n    class: \"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n  }, _cache[56] || (_cache[56] = [_createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有状态\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"normal\"\n  }, \"正常\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"warning\"\n  }, \"警告\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"error\"\n  }, \"错误\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.statusFilter]]), _createCommentVNode(\" 显示密码过期选项 \"), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.expiryFilter = $event),\n    class: \"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n  }, _cache[57] || (_cache[57] = [_createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"所有密码\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"expired\"\n  }, \"已过期\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"expiring-soon\"\n  }, \"即将过期\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"valid\"\n  }, \"有效期内\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.expiryFilter]])])])]), _createCommentVNode(\" 主机列表 \"), _createCommentVNode(\" 表格视图 \"), $data.viewMode === 'table' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createCommentVNode(\" 账号计数和导出按钮 \"), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_cache[58] || (_cache[58] = _createTextVNode(\" 显示 \")), _createElementVNode(\"span\", _hoisted_13, _toDisplayString(_ctx.filteredAccounts.length), 1 /* TEXT */), _cache[59] || (_cache[59] = _createTextVNode(\" 个账号 (共 \")), _createElementVNode(\"span\", _hoisted_14, _toDisplayString($options.getAllAccounts.length), 1 /* TEXT */), _cache[60] || (_cache[60] = _createTextVNode(\" 个) \"))]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"button\", _hoisted_16, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'file-export'],\n    class: \"mr-1\"\n  }), _cache[61] || (_cache[61] = _createTextVNode(\" 导出 \"))]), _createElementVNode(\"button\", _hoisted_17, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'print'],\n    class: \"mr-1\"\n  }), _cache[62] || (_cache[62] = _createTextVNode(\" 打印 \"))])])]), _createElementVNode(\"table\", _hoisted_18, [_createElementVNode(\"thead\", _hoisted_19, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", _hoisted_20, [_createVNode(_component_CustomCheckbox, {\n    modelValue: $data.selectAll,\n    \"onUpdate:modelValue\": [_cache[9] || (_cache[9] = $event => $data.selectAll = $event), $options.toggleSelectAll]\n  }, {\n    default: _withCtx(() => _cache[63] || (_cache[63] = [_createTextVNode(\" 主机名 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _cache[64] || (_cache[64] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" IP地址 \", -1 /* HOISTED */)), _cache[65] || (_cache[65] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 账号 \", -1 /* HOISTED */)), _cache[66] || (_cache[66] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 最后密码修改时间 \", -1 /* HOISTED */)), _cache[67] || (_cache[67] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码过期时间 \", -1 /* HOISTED */)), _cache[68] || (_cache[68] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 密码 \", -1 /* HOISTED */)), _cache[69] || (_cache[69] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 状态 \", -1 /* HOISTED */)), _cache[70] || (_cache[70] = _createElementVNode(\"th\", {\n    scope: \"col\",\n    class: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n  }, \" 操作 \", -1 /* HOISTED */))])]), _createElementVNode(\"tbody\", _hoisted_21, [_createCommentVNode(\" 按主机分组显示 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.groupedAccounts, (hostGroup, hostIndex) => {\n    return _openBlock(), _createElementBlock(_Fragment, {\n      key: hostGroup.hostId\n    }, [_createCommentVNode(\" 主机分组标题行 \"), _createElementVNode(\"tr\", _hoisted_22, [_createElementVNode(\"td\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, _toDisplayString(hostGroup.hostName) + \" (\" + _toDisplayString(hostGroup.hostIp) + \")\", 1 /* TEXT */), _createElementVNode(\"div\", null, [_createElementVNode(\"button\", {\n      class: \"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\",\n      onClick: $event => $options.openAddAccountModal(hostGroup.host)\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'plus'],\n      class: \"mr-1\"\n    }), _cache[71] || (_cache[71] = _createTextVNode(\" 添加账号 \"))], 8 /* PROPS */, _hoisted_26)])])])]), _createCommentVNode(\" 账号行 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(hostGroup.accounts, (account, accountIndex) => {\n      return _openBlock(), _createElementBlock(\"tr\", {\n        key: account.id,\n        class: _normalizeClass({\n          'bg-gray-50': accountIndex % 2 === 0,\n          'hover:bg-blue-50': true\n        })\n      }, [_createElementVNode(\"td\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_createVNode(_component_CustomCheckbox, {\n        modelValue: account.host.selected,\n        \"onUpdate:modelValue\": $event => account.host.selected = $event,\n        class: \"ml-4\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"span\", _hoisted_29, _toDisplayString(account.host.name), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])])]), _createElementVNode(\"td\", _hoisted_30, [_createElementVNode(\"div\", _hoisted_31, _toDisplayString(account.host.ip), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"span\", _hoisted_34, _toDisplayString(account.username), 1 /* TEXT */), account.isDefault ? (_openBlock(), _createElementBlock(\"span\", _hoisted_35, \" 默认 \")) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"td\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, _toDisplayString(account.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"td\", _hoisted_38, [_createElementVNode(\"div\", {\n        class: _normalizeClass({\n          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\n          'bg-red-100 text-red-800': $options.isPasswordExpired(account).status === 'danger' || $options.isPasswordExpired(account).status === 'expired',\n          'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(account).status === 'warning',\n          'bg-gray-100 text-gray-800': $options.isPasswordExpired(account).status === 'normal'\n        })\n      }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(account).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(account).status === 'expired' || $options.isPasswordExpired(account).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_39, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-triangle']\n      })])) : $options.isPasswordExpired(account).status === 'warning' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_40, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-circle']\n      })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]), _createElementVNode(\"td\", _hoisted_41, [_createElementVNode(\"div\", _hoisted_42, [_createElementVNode(\"div\", _hoisted_43, [_createElementVNode(\"input\", {\n        type: $data.passwordVisibility[account.id] ? 'text' : 'password',\n        value: account.password,\n        readonly: \"\",\n        class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n      }, null, 8 /* PROPS */, _hoisted_44)]), _createElementVNode(\"button\", {\n        onClick: $event => $options.togglePasswordVisibility(account.id),\n        class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $data.passwordVisibility[account.id] ? 'eye-slash' : 'eye'],\n        class: \"text-lg\"\n      }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_45)])]), _createElementVNode(\"td\", _hoisted_46, [_createVNode(_component_StatusBadge, {\n        type: account.host.status\n      }, null, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"td\", _hoisted_47, [_createElementVNode(\"div\", _hoisted_48, [_createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.openChangePasswordModal(account.host, account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'key'],\n        class: \"mr-1\"\n      }), _cache[72] || (_cache[72] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_49), _createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => _ctx.copyPassword(account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'copy'],\n        class: \"mr-1\"\n      }), _cache[73] || (_cache[73] = _createTextVNode(\" 复制 \"))], 8 /* PROPS */, _hoisted_50)])])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))], 64 /* STABLE_FRAGMENT */);\n  }), 128 /* KEYED_FRAGMENT */)), _createCommentVNode(\" 无数据显示 \"), _ctx.filteredAccounts.length === 0 ? (_openBlock(), _createElementBlock(\"tr\", _hoisted_51, [_createElementVNode(\"td\", _hoisted_52, [_createElementVNode(\"div\", _hoisted_53, [_createVNode(_component_font_awesome_icon, {\n    icon: ['fas', 'search'],\n    class: \"text-4xl mb-3\"\n  }), _cache[74] || (_cache[74] = _createElementVNode(\"p\", null, \"没有找到匹配的账号数据\", -1 /* HOISTED */))])])])) : _createCommentVNode(\"v-if\", true)])])])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 卡片视图 \"), _createElementVNode(\"div\", _hoisted_54, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredHosts, host => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: host.id,\n      class: \"bg-white overflow-hidden shadow rounded-lg\"\n    }, [_createElementVNode(\"div\", _hoisted_55, [_createCommentVNode(\" 主机头部 \"), _createElementVNode(\"div\", _hoisted_56, [_createElementVNode(\"div\", _hoisted_57, [_createVNode(_component_CustomCheckbox, {\n      modelValue: host.selected,\n      \"onUpdate:modelValue\": $event => host.selected = $event,\n      class: \"mr-2\"\n    }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"]), _createElementVNode(\"div\", null, [_createElementVNode(\"h3\", _hoisted_58, _toDisplayString(host.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_59, _toDisplayString(host.ip), 1 /* TEXT */)])]), _createVNode(_component_StatusBadge, {\n      type: host.status\n    }, null, 8 /* PROPS */, [\"type\"])]), _createCommentVNode(\" 账号列表 \"), _createElementVNode(\"div\", _hoisted_60, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(host.accounts, account => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: account.id,\n        class: _normalizeClass([\"border border-gray-200 rounded-lg p-3\", {\n          'border-green-300 bg-green-50': account.isDefault\n        }])\n      }, [_createElementVNode(\"div\", _hoisted_61, [_createElementVNode(\"div\", _hoisted_62, [_createElementVNode(\"span\", _hoisted_63, _toDisplayString(account.username), 1 /* TEXT */), account.isDefault ? (_openBlock(), _createElementBlock(\"span\", _hoisted_64, \" 默认 \")) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"button\", {\n        class: \"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n        onClick: $event => $options.openChangePasswordModal(host, account)\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'key'],\n        class: \"mr-1\"\n      }), _cache[75] || (_cache[75] = _createTextVNode(\" 修改密码 \"))], 8 /* PROPS */, _hoisted_65)]), _createCommentVNode(\" 密码展示 \"), _createElementVNode(\"div\", _hoisted_66, [_cache[76] || (_cache[76] = _createElementVNode(\"div\", {\n        class: \"text-xs font-medium text-gray-500 mb-1\"\n      }, \"密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_67, [_createElementVNode(\"input\", {\n        type: $data.passwordVisibility[account.id] ? 'text' : 'password',\n        value: account.password,\n        readonly: \"\",\n        class: \"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\"\n      }, null, 8 /* PROPS */, _hoisted_68), _createElementVNode(\"button\", {\n        onClick: $event => $options.togglePasswordVisibility(account.id),\n        class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n      }, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', $data.passwordVisibility[account.id] ? 'eye-slash' : 'eye'],\n        class: \"text-lg\"\n      }, null, 8 /* PROPS */, [\"icon\"])], 8 /* PROPS */, _hoisted_69)])]), _createCommentVNode(\" 密码信息区域 \"), _createElementVNode(\"div\", _hoisted_70, [_createElementVNode(\"div\", null, [_cache[77] || (_cache[77] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"最后修改时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_71, _toDisplayString(account.lastPasswordChange || '-'), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[78] || (_cache[78] = _createElementVNode(\"div\", {\n        class: \"font-medium text-gray-500 mb-1\"\n      }, \"密码过期\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n        class: _normalizeClass({\n          'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\n          'bg-red-100 text-red-800': $options.isPasswordExpired(account).status === 'danger' || $options.isPasswordExpired(account).status === 'expired',\n          'bg-yellow-100 text-yellow-800': $options.isPasswordExpired(account).status === 'warning',\n          'bg-gray-100 text-gray-800': $options.isPasswordExpired(account).status === 'normal'\n        })\n      }, [_createTextVNode(_toDisplayString($options.isPasswordExpired(account).text) + \" \", 1 /* TEXT */), $options.isPasswordExpired(account).status === 'expired' || $options.isPasswordExpired(account).status === 'danger' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_72, [_createVNode(_component_font_awesome_icon, {\n        icon: ['fas', 'exclamation-triangle']\n      })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)])])], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 添加账号按钮 \"), _createElementVNode(\"div\", _hoisted_73, [_createElementVNode(\"button\", {\n      class: \"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n      onClick: $event => $options.openAddAccountModal(host)\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'plus'],\n      class: \"mr-1\"\n    }), _cache[79] || (_cache[79] = _createTextVNode(\" 添加账号 \"))], 8 /* PROPS */, _hoisted_74)])])]);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 修改密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.changePasswordModal.show,\n    \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.changePasswordModal.show = $event),\n    title: \"修改密码\",\n    onConfirm: $options.updatePassword,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_75, [_cache[83] || (_cache[83] = _createElementVNode(\"div\", {\n      class: \"font-medium mb-2\"\n    }, \"主机信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_76, [_createElementVNode(\"div\", null, [_cache[80] || (_cache[80] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"主机名:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[81] || (_cache[81] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"IP地址:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.ip), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[82] || (_cache[82] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"账号:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentAccount.username), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_77, [_cache[86] || (_cache[86] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码生成方式\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_78, [_createElementVNode(\"button\", {\n      onClick: _cache[10] || (_cache[10] = $event => $data.changePasswordModal.method = 'auto'),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", $data.changePasswordModal.method === 'auto' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-2\"\n    }), _cache[84] || (_cache[84] = _createTextVNode(\" 自动生成 \"))], 2 /* CLASS */), _createElementVNode(\"button\", {\n      onClick: _cache[11] || (_cache[11] = $event => $data.changePasswordModal.method = 'manual'),\n      class: _normalizeClass([\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\", $data.changePasswordModal.method === 'manual' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'])\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'edit'],\n      class: \"mr-2\"\n    }), _cache[85] || (_cache[85] = _createTextVNode(\" 手动输入 \"))], 2 /* CLASS */)])]), $data.changePasswordModal.method === 'auto' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_79, [_cache[89] || (_cache[89] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $data.changePasswordModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[13] || (_cache[13] = $event => $options.generatePassword())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_80);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.changePasswordModal.policyId]]), _createElementVNode(\"div\", _hoisted_81, [_createElementVNode(\"div\", _hoisted_82, [_cache[88] || (_cache[88] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      onClick: _cache[14] || (_cache[14] = $event => $options.generatePassword()),\n      type: \"button\",\n      class: \"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[87] || (_cache[87] = _createTextVNode(\" 重新生成 \"))])]), _createElementVNode(\"div\", _hoisted_83, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.generated ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => $data.changePasswordModal.generatedPassword = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_84), [[_vModelDynamic, $data.changePasswordModal.generatedPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[16] || (_cache[16] = $event => $data.passwordVisibility.generated = !$data.passwordVisibility.generated),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.generated ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_85, [_createElementVNode(\"div\", _hoisted_86, [_cache[90] || (_cache[90] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"新密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_87, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.new ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.changePasswordModal.newPassword = $event),\n      class: \"form-control flex-1\",\n      placeholder: \"输入新密码\"\n    }, null, 8 /* PROPS */, _hoisted_88), [[_vModelDynamic, $data.changePasswordModal.newPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[18] || (_cache[18] = $event => $data.passwordVisibility.new = !$data.passwordVisibility.new),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.new ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])]), _createElementVNode(\"div\", _hoisted_89, [_cache[91] || (_cache[91] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"确认密码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_90, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.confirm ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[19] || (_cache[19] = $event => $data.changePasswordModal.confirmPassword = $event),\n      class: _normalizeClass([\"form-control flex-1\", {\n        'border-red-500': $options.passwordMismatch\n      }]),\n      placeholder: \"再次输入新密码\"\n    }, null, 10 /* CLASS, PROPS */, _hoisted_91), [[_vModelDynamic, $data.changePasswordModal.confirmPassword]]), _createElementVNode(\"button\", {\n      onClick: _cache[20] || (_cache[20] = $event => $data.passwordVisibility.confirm = !$data.passwordVisibility.confirm),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.confirm ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])]), $options.passwordMismatch ? (_openBlock(), _createElementBlock(\"div\", _hoisted_92, \"两次输入的密码不一致\")) : _createCommentVNode(\"v-if\", true)]), _createVNode(_component_PasswordStrengthMeter, {\n      password: $data.changePasswordModal.newPassword\n    }, null, 8 /* PROPS */, [\"password\"])])), _createElementVNode(\"div\", _hoisted_93, [_cache[95] || (_cache[95] = _createElementVNode(\"div\", {\n      class: \"form-label font-medium\"\n    }, \"执行选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.executeImmediately,\n      \"onUpdate:modelValue\": _cache[21] || (_cache[21] = $event => $data.changePasswordModal.executeImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[92] || (_cache[92] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"立即执行\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.saveHistory,\n      \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $data.changePasswordModal.saveHistory = $event)\n    }, {\n      default: _withCtx(() => _cache[93] || (_cache[93] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"保存历史记录\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.changePasswordModal.logAudit,\n      \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.changePasswordModal.logAudit = $event)\n    }, {\n      default: _withCtx(() => _cache[94] || (_cache[94] = [_createElementVNode(\"span\", {\n        class: \"ml-2\"\n      }, \"记录审计日志\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 添加账号弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.addAccountModal.show,\n    \"onUpdate:modelValue\": _cache[32] || (_cache[32] = $event => $data.addAccountModal.show = $event),\n    title: \"添加账号\",\n    onConfirm: $options.addAccount,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_94, [_cache[98] || (_cache[98] = _createElementVNode(\"div\", {\n      class: \"font-medium mb-2\"\n    }, \"主机信息\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_95, [_createElementVNode(\"div\", null, [_cache[96] || (_cache[96] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"主机名:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.name), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_cache[97] || (_cache[97] = _createElementVNode(\"span\", {\n      class: \"font-medium\"\n    }, \"IP地址:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.currentHost.ip), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_96, [_cache[99] || (_cache[99] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"账号名称\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.addAccountModal.username = $event),\n      class: \"form-control\",\n      placeholder: \"输入账号名称\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addAccountModal.username]])]), _createElementVNode(\"div\", _hoisted_97, [_cache[101] || (_cache[101] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"设为默认账号\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_98, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.addAccountModal.isDefault = $event),\n      class: \"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.addAccountModal.isDefault]]), _cache[100] || (_cache[100] = _createElementVNode(\"label\", {\n      class: \"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"\n    }, null, -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_99, [_cache[102] || (_cache[102] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.addAccountModal.policyId = $event),\n      class: \"form-select\",\n      onChange: _cache[28] || (_cache[28] = $event => $options.generatePasswordForNewAccount())\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_100);\n    }), 128 /* KEYED_FRAGMENT */))], 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.addAccountModal.policyId]])]), _createElementVNode(\"div\", _hoisted_101, [_createElementVNode(\"div\", _hoisted_102, [_cache[104] || (_cache[104] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"生成的密码\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n      onClick: _cache[29] || (_cache[29] = $event => $options.generatePasswordForNewAccount()),\n      type: \"button\",\n      class: \"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', 'sync-alt'],\n      class: \"mr-1\"\n    }), _cache[103] || (_cache[103] = _createTextVNode(\" 重新生成 \"))])]), _createElementVNode(\"div\", _hoisted_103, [_withDirectives(_createElementVNode(\"input\", {\n      type: $data.passwordVisibility.newAccount ? 'text' : 'password',\n      \"onUpdate:modelValue\": _cache[30] || (_cache[30] = $event => $data.addAccountModal.password = $event),\n      readonly: \"\",\n      class: \"form-control flex-1 bg-gray-50\"\n    }, null, 8 /* PROPS */, _hoisted_104), [[_vModelDynamic, $data.addAccountModal.password]]), _createElementVNode(\"button\", {\n      onClick: _cache[31] || (_cache[31] = $event => $data.passwordVisibility.newAccount = !$data.passwordVisibility.newAccount),\n      type: \"button\",\n      class: \"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\"\n    }, [_createVNode(_component_font_awesome_icon, {\n      icon: ['fas', $data.passwordVisibility.newAccount ? 'eye-slash' : 'eye'],\n      class: \"text-lg\"\n    }, null, 8 /* PROPS */, [\"icon\"])])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量更新密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchUpdateModal.show,\n    \"onUpdate:modelValue\": _cache[42] || (_cache[42] = $event => $data.batchUpdateModal.show = $event),\n    title: \"批量更新密码\",\n    \"confirm-text\": \"开始更新\",\n    size: \"lg\",\n    onConfirm: $options.batchUpdatePasswords,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_105, [_cache[106] || (_cache[106] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_106, [_createVNode(_component_CustomCheckbox, {\n      modelValue: $data.selectAllBatch,\n      \"onUpdate:modelValue\": [_cache[33] || (_cache[33] = $event => $data.selectAllBatch = $event), $options.toggleSelectAllBatch]\n    }, {\n      default: _withCtx(() => _cache[105] || (_cache[105] = [_createTextVNode(\" 全选 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_107, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.hosts, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchUpdateModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchUpdateModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"p\", _hoisted_108, \"已选择 \" + _toDisplayString($options.selectedHostsCount) + \" 台主机\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_109, [_cache[107] || (_cache[107] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[34] || (_cache[34] = $event => $data.batchUpdateModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_110);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchUpdateModal.policyId]])]), _createElementVNode(\"div\", _hoisted_111, [_cache[112] || (_cache[112] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"执行时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_112, [_createElementVNode(\"label\", _hoisted_113, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[35] || (_cache[35] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"immediate\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[108] || (_cache[108] = _createElementVNode(\"span\", null, \"立即执行\", -1 /* HOISTED */))]), _createElementVNode(\"label\", _hoisted_114, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"radio\",\n      \"onUpdate:modelValue\": _cache[36] || (_cache[36] = $event => $data.batchUpdateModal.executionTime = $event),\n      value: \"scheduled\",\n      class: \"mr-2\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.batchUpdateModal.executionTime]]), _cache[109] || (_cache[109] = _createElementVNode(\"span\", null, \"定时执行\", -1 /* HOISTED */))])]), $data.batchUpdateModal.executionTime === 'scheduled' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_115, [_createElementVNode(\"div\", _hoisted_116, [_createElementVNode(\"div\", null, [_cache[110] || (_cache[110] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"日期\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"date\",\n      \"onUpdate:modelValue\": _cache[37] || (_cache[37] = $event => $data.batchUpdateModal.scheduledDate = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledDate]])]), _createElementVNode(\"div\", null, [_cache[111] || (_cache[111] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"时间\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n      type: \"time\",\n      \"onUpdate:modelValue\": _cache[38] || (_cache[38] = $event => $data.batchUpdateModal.scheduledTime = $event),\n      class: \"form-control\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.batchUpdateModal.scheduledTime]])])])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_117, [_cache[116] || (_cache[116] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"高级选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.ignoreErrors,\n      \"onUpdate:modelValue\": _cache[39] || (_cache[39] = $event => $data.batchUpdateModal.ignoreErrors = $event)\n    }, {\n      default: _withCtx(() => _cache[113] || (_cache[113] = [_createTextVNode(\" 忽略错误继续执行 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.detailedLog,\n      \"onUpdate:modelValue\": _cache[40] || (_cache[40] = $event => $data.batchUpdateModal.detailedLog = $event)\n    }, {\n      default: _withCtx(() => _cache[114] || (_cache[114] = [_createTextVNode(\" 记录详细日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchUpdateModal.sendNotification,\n      \"onUpdate:modelValue\": _cache[41] || (_cache[41] = $event => $data.batchUpdateModal.sendNotification = $event)\n    }, {\n      default: _withCtx(() => _cache[115] || (_cache[115] = [_createTextVNode(\" 执行完成后发送通知 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 批量应用策略弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.batchApplyModal.show,\n    \"onUpdate:modelValue\": _cache[46] || (_cache[46] = $event => $data.batchApplyModal.show = $event),\n    title: \"批量应用密码策略\",\n    \"confirm-text\": \"应用策略\",\n    onConfirm: $options.batchApplyPolicy,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_118, [_cache[117] || (_cache[117] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_119, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.batchApplyModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.batchApplyModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_120, [_cache[118] || (_cache[118] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择密码策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[43] || (_cache[43] = $event => $data.batchApplyModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.policies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name), 9 /* TEXT, PROPS */, _hoisted_121);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.batchApplyModal.policyId]])]), _createElementVNode(\"div\", _hoisted_122, [_cache[121] || (_cache[121] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用选项\", -1 /* HOISTED */)), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.updateImmediately,\n      \"onUpdate:modelValue\": _cache[44] || (_cache[44] = $event => $data.batchApplyModal.updateImmediately = $event)\n    }, {\n      default: _withCtx(() => _cache[119] || (_cache[119] = [_createTextVNode(\" 立即更新密码以符合策略 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_CustomCheckbox, {\n      modelValue: $data.batchApplyModal.applyOnNextUpdate,\n      \"onUpdate:modelValue\": _cache[45] || (_cache[45] = $event => $data.batchApplyModal.applyOnNextUpdate = $event)\n    }, {\n      default: _withCtx(() => _cache[120] || (_cache[120] = [_createTextVNode(\" 下次密码更新时应用 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"]), _createCommentVNode(\" 紧急重置密码弹窗 \"), _createVNode(_component_BaseModal, {\n    modelValue: $data.emergencyResetModal.show,\n    \"onUpdate:modelValue\": _cache[50] || (_cache[50] = $event => $data.emergencyResetModal.show = $event),\n    title: \"紧急密码重置\",\n    \"confirm-text\": \"立即重置\",\n    icon: \"exclamation-triangle\",\n    danger: \"\",\n    onConfirm: $options.emergencyReset,\n    loading: $data.processing\n  }, {\n    default: _withCtx(() => [_cache[127] || (_cache[127] = _createElementVNode(\"div\", {\n      class: \"bg-red-50 text-red-700 p-3 rounded-md mb-4\"\n    }, [_createElementVNode(\"p\", null, \"紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_123, [_cache[122] || (_cache[122] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"选择目标主机\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_124, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.selectedHostsList, host => {\n      return _openBlock(), _createBlock(_component_CustomCheckbox, {\n        key: host.id,\n        modelValue: $data.emergencyResetModal.selectedHosts[host.id],\n        \"onUpdate:modelValue\": $event => $data.emergencyResetModal.selectedHosts[host.id] = $event\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(host.name) + \" (\" + _toDisplayString(host.ip) + \") \", 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"]);\n    }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_125, [_cache[123] || (_cache[123] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"应用紧急策略\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[47] || (_cache[47] = $event => $data.emergencyResetModal.policyId = $event),\n      class: \"form-select\"\n    }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.emergencyPolicies, policy => {\n      return _openBlock(), _createElementBlock(\"option\", {\n        key: policy.id,\n        value: policy.id\n      }, _toDisplayString(policy.name) + \" (最小长度: \" + _toDisplayString(policy.minLength) + \", 过期: \" + _toDisplayString(policy.expiryDays) + \"天) \", 9 /* TEXT, PROPS */, _hoisted_126);\n    }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.policyId]])]), _createElementVNode(\"div\", _hoisted_127, [_cache[125] || (_cache[125] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"操作原因\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n      \"onUpdate:modelValue\": _cache[48] || (_cache[48] = $event => $data.emergencyResetModal.reason = $event),\n      class: \"form-select\"\n    }, _cache[124] || (_cache[124] = [_createElementVNode(\"option\", {\n      value: \"security_incident\"\n    }, \"安全事件响应\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"password_leak\"\n    }, \"密码泄露\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"abnormal_access\"\n    }, \"异常访问\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"compliance\"\n    }, \"合规要求\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n      value: \"other\"\n    }, \"其他原因\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.emergencyResetModal.reason]])]), _createElementVNode(\"div\", _hoisted_128, [_cache[126] || (_cache[126] = _createElementVNode(\"label\", {\n      class: \"form-label\"\n    }, \"附加说明\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n      \"onUpdate:modelValue\": _cache[49] || (_cache[49] = $event => $data.emergencyResetModal.description = $event),\n      class: \"form-control\",\n      rows: \"2\",\n      placeholder: \"请输入重置原因详细说明\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.emergencyResetModal.description]])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onConfirm\", \"loading\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "scope", "colspan", "_createElementBlock", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "args", "$options", "showEmergencyReset", "_createVNode", "_component_font_awesome_icon", "icon", "openBatchUpdateModal", "openBatchApplyModal", "_hoisted_4", "_hoisted_5", "_normalizeClass", "$data", "viewMode", "$event", "_createTextVNode", "_hoisted_6", "type", "filterText", "placeholder", "_hoisted_7", "_hoisted_8", "accountFilterText", "_hoisted_9", "statusFilter", "value", "expiryFilter", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_toDisplayString", "_ctx", "filteredAccounts", "length", "_hoisted_14", "getAllAccounts", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_component_CustomCheckbox", "modelValue", "selectAll", "toggleSelectAll", "default", "_withCtx", "_", "_hoisted_21", "_Fragment", "_renderList", "groupedAccounts", "hostGroup", "hostIndex", "hostId", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "hostName", "hostIp", "openAddAccountModal", "host", "_hoisted_26", "accounts", "account", "accountIndex", "id", "_hoisted_27", "_hoisted_28", "selected", "_hoisted_29", "name", "_hoisted_30", "_hoisted_31", "ip", "_hoisted_32", "_hoisted_33", "_hoisted_34", "username", "isDefault", "_hoisted_35", "_hoisted_36", "_hoisted_37", "lastPasswordChange", "_hoisted_38", "isPasswordExpired", "status", "text", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_hoisted_43", "passwordVisibility", "password", "readonly", "_hoisted_44", "togglePasswordVisibility", "_hoisted_45", "_hoisted_46", "_component_StatusBadge", "_hoisted_47", "_hoisted_48", "openChangePasswordModal", "_hoisted_49", "copyPassword", "_hoisted_50", "_hoisted_51", "_hoisted_52", "_hoisted_53", "_hoisted_54", "filteredHosts", "_hoisted_55", "_hoisted_56", "_hoisted_57", "_hoisted_58", "_hoisted_59", "_hoisted_60", "_hoisted_61", "_hoisted_62", "_hoisted_63", "_hoisted_64", "_hoisted_65", "_hoisted_66", "_hoisted_67", "_hoisted_68", "_hoisted_69", "_hoisted_70", "_hoisted_71", "_hoisted_72", "_hoisted_73", "_hoisted_74", "_component_BaseModal", "changePasswordModal", "show", "title", "onConfirm", "updatePassword", "loading", "processing", "_hoisted_75", "_hoisted_76", "currentHost", "currentAccount", "_hoisted_77", "_hoisted_78", "method", "_hoisted_79", "policyId", "onChange", "generatePassword", "policies", "policy", "<PERSON><PERSON><PERSON><PERSON>", "expiryDays", "_hoisted_80", "_hoisted_81", "_hoisted_82", "_hoisted_83", "generated", "generatedPassword", "_hoisted_84", "_hoisted_85", "_hoisted_86", "_hoisted_87", "new", "newPassword", "_hoisted_88", "_hoisted_89", "_hoisted_90", "confirm", "confirmPassword", "passwordMismatch", "_hoisted_91", "_hoisted_92", "_component_PasswordStrengthMeter", "_hoisted_93", "executeImmediately", "saveHistory", "logAudit", "addAccountModal", "addAccount", "_hoisted_94", "_hoisted_95", "_hoisted_96", "_hoisted_97", "_hoisted_98", "_hoisted_99", "generatePasswordForNewAccount", "_hoisted_100", "_hoisted_101", "_hoisted_102", "_hoisted_103", "newAccount", "_hoisted_104", "batchUpdateModal", "size", "batchUpdatePasswords", "_hoisted_105", "_hoisted_106", "selectAllBatch", "toggleSelectAllBatch", "_hoisted_107", "hosts", "_createBlock", "selectedHosts", "_hoisted_108", "selectedHostsCount", "_hoisted_109", "_hoisted_110", "_hoisted_111", "_hoisted_112", "_hoisted_113", "executionTime", "_hoisted_114", "_hoisted_115", "_hoisted_116", "scheduledDate", "scheduledTime", "_hoisted_117", "ignoreErrors", "detailedLog", "sendNotification", "batchApplyModal", "batchApplyPolicy", "_hoisted_118", "_hoisted_119", "selectedHostsList", "_hoisted_120", "_hoisted_121", "_hoisted_122", "updateImmediately", "applyOnNextUpdate", "emergencyResetModal", "danger", "emergencyReset", "_hoisted_123", "_hoisted_124", "_hoisted_125", "emergencyPolicies", "_hoisted_126", "_hoisted_127", "reason", "_hoisted_128", "description", "rows"], "sources": ["D:\\demo\\ooo\\pass\\src\\views\\HostManagement.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 操作按钮 -->\r\n    <div class=\"bg-white shadow rounded-lg p-4 mb-6\">\r\n      <div class=\"flex flex-wrap items-center justify-between\">\r\n        <div class=\"flex space-x-3 mb-2 sm:mb-0\">\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\r\n            @click=\"showEmergencyReset\">\r\n            <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" class=\"mr-2\" />\r\n            <span>紧急重置</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n            @click=\"openBatchUpdateModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-2\" />\r\n            <span>批量更新密码</span>\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\r\n            @click=\"openBatchApplyModal\">\r\n            <font-awesome-icon :icon=\"['fas', 'shield-alt']\" class=\"mr-2\" />\r\n            <span>批量应用策略</span>\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"flex items-center space-x-4\">\r\n          <!-- 视图切换 -->\r\n          <div class=\"flex items-center border rounded-md overflow-hidden\">\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'table', 'bg-gray-100 text-gray-600': viewMode !== 'table' }\"\r\n              @click=\"viewMode = 'table'\">\r\n              <font-awesome-icon :icon=\"['fas', 'table']\" class=\"mr-1\" />\r\n              表格\r\n            </button>\r\n            <button class=\"px-3 py-1 focus:outline-none\"\r\n              :class=\"{ 'bg-blue-500 text-white': viewMode === 'card', 'bg-gray-100 text-gray-600': viewMode !== 'card' }\"\r\n              @click=\"viewMode = 'card'\">\r\n              <font-awesome-icon :icon=\"['fas', 'th-large']\" class=\"mr-1\" />\r\n              卡片\r\n            </button>\r\n          </div>\r\n\r\n          <!-- 筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"filterText\" placeholder=\"筛选主机...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 账号筛选 -->\r\n          <div class=\"relative\">\r\n            <input type=\"text\" v-model=\"accountFilterText\" placeholder=\"筛选账号...\"\r\n              class=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\" />\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'user']\" class=\"text-gray-400\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 状态筛选 -->\r\n          <select v-model=\"statusFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有状态</option>\r\n            <option value=\"normal\">正常</option>\r\n            <option value=\"warning\">警告</option>\r\n            <option value=\"error\">错误</option>\r\n          </select>\r\n\r\n          <!-- 显示密码过期选项 -->\r\n          <select v-model=\"expiryFilter\"\r\n            class=\"block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\">\r\n            <option value=\"all\">所有密码</option>\r\n            <option value=\"expired\">已过期</option>\r\n            <option value=\"expiring-soon\">即将过期</option>\r\n            <option value=\"valid\">有效期内</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主机列表 -->\r\n    <!-- 表格视图 -->\r\n    <div v-if=\"viewMode === 'table'\" class=\"bg-white rounded-lg shadow overflow-hidden\">\r\n      <!-- 账号计数和导出按钮 -->\r\n      <div class=\"px-4 py-3 bg-gray-50 border-b flex justify-between items-center\">\r\n        <div class=\"text-sm text-gray-700\">\r\n          显示 <span class=\"font-medium\">{{ filteredAccounts.length }}</span> 个账号\r\n          (共 <span class=\"font-medium\">{{ getAllAccounts.length }}</span> 个)\r\n        </div>\r\n        <div class=\"flex space-x-2\">\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\r\n            <font-awesome-icon :icon=\"['fas', 'file-export']\" class=\"mr-1\" />\r\n            导出\r\n          </button>\r\n          <button\r\n            class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\r\n            <font-awesome-icon :icon=\"['fas', 'print']\" class=\"mr-1\" />\r\n            打印\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <table class=\"min-w-full divide-y divide-gray-200\">\r\n        <thead class=\"bg-gray-50\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              <CustomCheckbox v-model=\"selectAll\" @update:modelValue=\"toggleSelectAll\">\r\n                主机名\r\n              </CustomCheckbox>\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              IP地址\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              账号\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              最后密码修改时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码过期时间\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              密码\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              状态\r\n            </th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n              操作\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"bg-white divide-y divide-gray-200\">\r\n          <!-- 按主机分组显示 -->\r\n          <template v-for=\"(hostGroup, hostIndex) in groupedAccounts\" :key=\"hostGroup.hostId\">\r\n            <!-- 主机分组标题行 -->\r\n            <tr class=\"bg-gray-100\">\r\n              <td colspan=\"8\" class=\"px-6 py-2\">\r\n                <div class=\"flex items-center justify-between\">\r\n                  <div class=\"font-medium text-gray-700\">{{ hostGroup.hostName }} ({{ hostGroup.hostIp }})</div>\r\n                  <div>\r\n                    <button class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\"\r\n                      @click=\"openAddAccountModal(hostGroup.host)\">\r\n                      <font-awesome-icon :icon=\"['fas', 'plus']\" class=\"mr-1\" />\r\n                      添加账号\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n            <!-- 账号行 -->\r\n            <tr v-for=\"(account, accountIndex) in hostGroup.accounts\" :key=\"account.id\"\r\n              :class=\"{ 'bg-gray-50': accountIndex % 2 === 0, 'hover:bg-blue-50': true }\">\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <CustomCheckbox v-model=\"account.host.selected\" class=\"ml-4\">\r\n                    <span class=\"ml-2 font-medium text-gray-900\">{{ account.host.name }}</span>\r\n                  </CustomCheckbox>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-900\">{{ account.host.ip }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"text-sm text-gray-500\">{{ account.lastPasswordChange || '-' }}</div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div :class=\"{\r\n                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,\r\n                  'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                  'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                  'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                }\">\r\n                  {{ isPasswordExpired(account).text }}\r\n                  <span\r\n                    v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                    class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                  </span>\r\n                  <span v-else-if=\"isPasswordExpired(account).status === 'warning'\" class=\"ml-1\">\r\n                    <font-awesome-icon :icon=\"['fas', 'exclamation-circle']\" />\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <div class=\"flex items-center\">\r\n                  <div class=\"flex-grow\">\r\n                    <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\"\r\n                      readonly\r\n                      class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  </div>\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                <StatusBadge :type=\"account.host.status\" />\r\n              </td>\r\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                <div class=\"flex space-x-2\">\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"openChangePasswordModal(account.host, account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                    修改密码\r\n                  </button>\r\n                  <button\r\n                    class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                    @click=\"copyPassword(account)\">\r\n                    <font-awesome-icon :icon=\"['fas', 'copy']\" class=\"mr-1\" />\r\n                    复制\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          </template>\r\n          <!-- 无数据显示 -->\r\n          <tr v-if=\"filteredAccounts.length === 0\">\r\n            <td colspan=\"8\" class=\"px-6 py-10 text-center\">\r\n              <div class=\"text-gray-500\">\r\n                <font-awesome-icon :icon=\"['fas', 'search']\" class=\"text-4xl mb-3\" />\r\n                <p>没有找到匹配的账号数据</p>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <!-- 卡片视图 -->\r\n    <div v-else class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\">\r\n      <div v-for=\"host in filteredHosts\" :key=\"host.id\" class=\"bg-white overflow-hidden shadow rounded-lg\">\r\n        <div class=\"px-4 py-5 sm:p-6\">\r\n          <!-- 主机头部 -->\r\n          <div class=\"flex justify-between items-start mb-4\">\r\n            <div class=\"flex items-center\">\r\n              <CustomCheckbox v-model=\"host.selected\" class=\"mr-2\" />\r\n              <div>\r\n                <h3 class=\"text-lg font-medium text-gray-900\">{{ host.name }}</h3>\r\n                <p class=\"text-sm text-gray-500\">{{ host.ip }}</p>\r\n              </div>\r\n            </div>\r\n            <StatusBadge :type=\"host.status\" />\r\n          </div>\r\n\r\n          <!-- 账号列表 -->\r\n          <div class=\"space-y-4\">\r\n            <div v-for=\"account in host.accounts\" :key=\"account.id\" class=\"border border-gray-200 rounded-lg p-3\"\r\n              :class=\"{ 'border-green-300 bg-green-50': account.isDefault }\">\r\n              <div class=\"flex justify-between items-center mb-2\">\r\n                <div class=\"flex items-center\">\r\n                  <span class=\"text-sm font-medium text-gray-900\">{{ account.username }}</span>\r\n                  <span v-if=\"account.isDefault\"\r\n                    class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800\">\r\n                    默认\r\n                  </span>\r\n                </div>\r\n                <button\r\n                  class=\"inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                  @click=\"openChangePasswordModal(host, account)\">\r\n                  <font-awesome-icon :icon=\"['fas', 'key']\" class=\"mr-1\" />\r\n                  修改密码\r\n                </button>\r\n              </div>\r\n\r\n              <!-- 密码展示 -->\r\n              <div class=\"mb-2\">\r\n                <div class=\"text-xs font-medium text-gray-500 mb-1\">密码</div>\r\n                <div class=\"flex items-center\">\r\n                  <input :type=\"passwordVisibility[account.id] ? 'text' : 'password'\" :value=\"account.password\" readonly\r\n                    class=\"bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-1.5\" />\r\n                  <button @click=\"togglePasswordVisibility(account.id)\"\r\n                    class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n                    <font-awesome-icon :icon=\"['fas', passwordVisibility[account.id] ? 'eye-slash' : 'eye']\"\r\n                      class=\"text-lg\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 密码信息区域 -->\r\n              <div class=\"grid grid-cols-2 gap-2 text-xs\">\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">最后修改时间</div>\r\n                  <div class=\"text-gray-900\">{{ account.lastPasswordChange || '-' }}</div>\r\n                </div>\r\n                <div>\r\n                  <div class=\"font-medium text-gray-500 mb-1\">密码过期</div>\r\n                  <div :class=\"{\r\n                    'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium': true,\r\n                    'bg-red-100 text-red-800': isPasswordExpired(account).status === 'danger' || isPasswordExpired(account).status === 'expired',\r\n                    'bg-yellow-100 text-yellow-800': isPasswordExpired(account).status === 'warning',\r\n                    'bg-gray-100 text-gray-800': isPasswordExpired(account).status === 'normal'\r\n                  }\">\r\n                    {{ isPasswordExpired(account).text }}\r\n                    <span\r\n                      v-if=\"isPasswordExpired(account).status === 'expired' || isPasswordExpired(account).status === 'danger'\"\r\n                      class=\"ml-1\">\r\n                      <font-awesome-icon :icon=\"['fas', 'exclamation-triangle']\" />\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 添加账号按钮 -->\r\n          <div class=\"mt-4 flex justify-center\">\r\n            <button\r\n              class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n              @click=\"openAddAccountModal(host)\">\r\n              <font-awesome-icon :icon=\"['fas', 'plus']\" class=\"mr-1\" />\r\n              添加账号\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 修改密码弹窗 -->\r\n    <BaseModal v-model=\"changePasswordModal.show\" title=\"修改密码\" @confirm=\"updatePassword\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n          <div><span class=\"font-medium\">账号:</span> {{ currentAccount.username }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码生成方式</label>\r\n        <div class=\"flex space-x-3\">\r\n          <button @click=\"changePasswordModal.method = 'auto'\"\r\n            class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n            :class=\"changePasswordModal.method === 'auto' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-2\" />\r\n            自动生成\r\n          </button>\r\n          <button @click=\"changePasswordModal.method = 'manual'\"\r\n            class=\"flex-1 px-3 py-2 text-sm border rounded-md focus:outline-none transition-colors\"\r\n            :class=\"changePasswordModal.method === 'manual' ? 'bg-blue-50 border-blue-500 text-blue-700' : 'border-gray-300 hover:bg-gray-50'\">\r\n            <font-awesome-icon :icon=\"['fas', 'edit']\" class=\"mr-2\" />\r\n            手动输入\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-if=\"changePasswordModal.method === 'auto'\" class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"changePasswordModal.policyId\" class=\"form-select\" @change=\"generatePassword()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n\r\n        <div class=\"mt-3\">\r\n          <div class=\"flex justify-between mb-1\">\r\n            <label class=\"form-label\">生成的密码</label>\r\n            <button @click=\"generatePassword()\" type=\"button\"\r\n              class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n              重新生成\r\n            </button>\r\n          </div>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.generated ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.generatedPassword\" readonly class=\"form-control flex-1 bg-gray-50\" />\r\n            <button @click=\"passwordVisibility.generated = !passwordVisibility.generated\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.generated ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div v-else class=\"space-y-4\">\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">新密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.new ? 'text' : 'password'\" v-model=\"changePasswordModal.newPassword\"\r\n              class=\"form-control flex-1\" placeholder=\"输入新密码\" />\r\n            <button @click=\"passwordVisibility.new = !passwordVisibility.new\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.new ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label class=\"form-label\">确认密码</label>\r\n          <div class=\"flex items-center\">\r\n            <input :type=\"passwordVisibility.confirm ? 'text' : 'password'\"\r\n              v-model=\"changePasswordModal.confirmPassword\" class=\"form-control flex-1\"\r\n              :class=\"{ 'border-red-500': passwordMismatch }\" placeholder=\"再次输入新密码\" />\r\n            <button @click=\"passwordVisibility.confirm = !passwordVisibility.confirm\" type=\"button\"\r\n              class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n              <font-awesome-icon :icon=\"['fas', passwordVisibility.confirm ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n            </button>\r\n          </div>\r\n          <div v-if=\"passwordMismatch\" class=\"text-sm text-red-500 mt-1\">两次输入的密码不一致</div>\r\n        </div>\r\n\r\n        <PasswordStrengthMeter :password=\"changePasswordModal.newPassword\" />\r\n      </div>\r\n\r\n      <div class=\"space-y-2 mt-4\">\r\n        <div class=\"form-label font-medium\">执行选项</div>\r\n        <CustomCheckbox v-model=\"changePasswordModal.executeImmediately\">\r\n          <span class=\"ml-2\">立即执行</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.saveHistory\">\r\n          <span class=\"ml-2\">保存历史记录</span>\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"changePasswordModal.logAudit\">\r\n          <span class=\"ml-2\">记录审计日志</span>\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 添加账号弹窗 -->\r\n    <BaseModal v-model=\"addAccountModal.show\" title=\"添加账号\" @confirm=\"addAccount\" :loading=\"processing\">\r\n      <div class=\"mb-4\">\r\n        <div class=\"font-medium mb-2\">主机信息</div>\r\n        <div class=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n          <div><span class=\"font-medium\">主机名:</span> {{ currentHost.name }}</div>\r\n          <div><span class=\"font-medium\">IP地址:</span> {{ currentHost.ip }}</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">账号名称</label>\r\n        <input type=\"text\" v-model=\"addAccountModal.username\" class=\"form-control\" placeholder=\"输入账号名称\" />\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">设为默认账号</label>\r\n        <div class=\"relative inline-block w-10 mr-2 align-middle select-none\">\r\n          <input type=\"checkbox\" v-model=\"addAccountModal.isDefault\"\r\n            class=\"toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer focus:outline-none\" />\r\n          <label class=\"toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer\"></label>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group mb-4\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"addAccountModal.policyId\" class=\"form-select\" @change=\"generatePasswordForNewAccount()\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <div class=\"flex justify-between mb-1\">\r\n          <label class=\"form-label\">生成的密码</label>\r\n          <button @click=\"generatePasswordForNewAccount()\" type=\"button\"\r\n            class=\"text-xs text-blue-600 hover:text-blue-800 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', 'sync-alt']\" class=\"mr-1\" />\r\n            重新生成\r\n          </button>\r\n        </div>\r\n        <div class=\"flex items-center\">\r\n          <input :type=\"passwordVisibility.newAccount ? 'text' : 'password'\" v-model=\"addAccountModal.password\" readonly\r\n            class=\"form-control flex-1 bg-gray-50\" />\r\n          <button @click=\"passwordVisibility.newAccount = !passwordVisibility.newAccount\" type=\"button\"\r\n            class=\"ml-2 text-gray-600 hover:text-blue-700 focus:outline-none\">\r\n            <font-awesome-icon :icon=\"['fas', passwordVisibility.newAccount ? 'eye-slash' : 'eye']\" class=\"text-lg\" />\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量更新密码弹窗 -->\r\n    <BaseModal v-model=\"batchUpdateModal.show\" title=\"批量更新密码\" confirm-text=\"开始更新\" size=\"lg\"\r\n      @confirm=\"batchUpdatePasswords\" :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"mb-2\">\r\n          <CustomCheckbox v-model=\"selectAllBatch\" @update:modelValue=\"toggleSelectAllBatch\">\r\n            全选\r\n          </CustomCheckbox>\r\n        </div>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in hosts\" :key=\"host.id\" v-model=\"batchUpdateModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n        <p class=\"form-text\">已选择 {{ selectedHostsCount }} 台主机</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">密码策略</label>\r\n        <select v-model=\"batchUpdateModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">执行时间</label>\r\n        <div class=\"flex space-x-4\">\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"immediate\" class=\"mr-2\">\r\n            <span>立即执行</span>\r\n          </label>\r\n          <label class=\"flex items-center\">\r\n            <input type=\"radio\" v-model=\"batchUpdateModal.executionTime\" value=\"scheduled\" class=\"mr-2\">\r\n            <span>定时执行</span>\r\n          </label>\r\n        </div>\r\n\r\n        <div v-if=\"batchUpdateModal.executionTime === 'scheduled'\" class=\"mt-3\">\r\n          <div class=\"grid grid-cols-2 gap-4\">\r\n            <div>\r\n              <label class=\"form-label\">日期</label>\r\n              <input type=\"date\" v-model=\"batchUpdateModal.scheduledDate\" class=\"form-control\">\r\n            </div>\r\n            <div>\r\n              <label class=\"form-label\">时间</label>\r\n              <input type=\"time\" v-model=\"batchUpdateModal.scheduledTime\" class=\"form-control\">\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">高级选项</label>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.ignoreErrors\">\r\n          忽略错误继续执行\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.detailedLog\">\r\n          记录详细日志\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchUpdateModal.sendNotification\">\r\n          执行完成后发送通知\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 批量应用策略弹窗 -->\r\n    <BaseModal v-model=\"batchApplyModal.show\" title=\"批量应用密码策略\" confirm-text=\"应用策略\" @confirm=\"batchApplyPolicy\"\r\n      :loading=\"processing\">\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"batchApplyModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择密码策略</label>\r\n        <select v-model=\"batchApplyModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in policies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }}\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用选项</label>\r\n        <CustomCheckbox v-model=\"batchApplyModal.updateImmediately\">\r\n          立即更新密码以符合策略\r\n        </CustomCheckbox>\r\n        <CustomCheckbox v-model=\"batchApplyModal.applyOnNextUpdate\">\r\n          下次密码更新时应用\r\n        </CustomCheckbox>\r\n      </div>\r\n    </BaseModal>\r\n\r\n    <!-- 紧急重置密码弹窗 -->\r\n    <BaseModal v-model=\"emergencyResetModal.show\" title=\"紧急密码重置\" confirm-text=\"立即重置\" icon=\"exclamation-triangle\" danger\r\n      @confirm=\"emergencyReset\" :loading=\"processing\">\r\n      <div class=\"bg-red-50 text-red-700 p-3 rounded-md mb-4\">\r\n        <p>紧急重置将立即生成强密码并应用到所选主机。此操作优先级最高，将中断其他密码操作。</p>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">选择目标主机</label>\r\n        <div class=\"max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2\">\r\n          <CustomCheckbox v-for=\"host in selectedHostsList\" :key=\"host.id\"\r\n            v-model=\"emergencyResetModal.selectedHosts[host.id]\">\r\n            {{ host.name }} ({{ host.ip }})\r\n          </CustomCheckbox>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">应用紧急策略</label>\r\n        <select v-model=\"emergencyResetModal.policyId\" class=\"form-select\">\r\n          <option v-for=\"policy in emergencyPolicies\" :key=\"policy.id\" :value=\"policy.id\">\r\n            {{ policy.name }} (最小长度: {{ policy.minLength }}, 过期: {{ policy.expiryDays }}天)\r\n          </option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">操作原因</label>\r\n        <select v-model=\"emergencyResetModal.reason\" class=\"form-select\">\r\n          <option value=\"security_incident\">安全事件响应</option>\r\n          <option value=\"password_leak\">密码泄露</option>\r\n          <option value=\"abnormal_access\">异常访问</option>\r\n          <option value=\"compliance\">合规要求</option>\r\n          <option value=\"other\">其他原因</option>\r\n        </select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">附加说明</label>\r\n        <textarea v-model=\"emergencyResetModal.description\" class=\"form-control\" rows=\"2\"\r\n          placeholder=\"请输入重置原因详细说明\"></textarea>\r\n      </div>\r\n    </BaseModal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState, mapGetters } from 'vuex'\r\nimport BaseModal from '@/components/BaseModal.vue'\r\nimport StatusBadge from '@/components/StatusBadge.vue'\r\nimport CustomCheckbox from '@/components/CustomCheckbox.vue'\r\nimport PasswordStrengthMeter from '@/components/PasswordStrengthMeter.vue'\r\n\r\nexport default {\r\n  name: 'HostManagement',\r\n  components: {\r\n    BaseModal,\r\n    StatusBadge,\r\n    CustomCheckbox,\r\n    PasswordStrengthMeter\r\n  },\r\n  data() {\r\n    return {\r\n      selectAll: false,\r\n      selectAllBatch: false,\r\n      processing: false,\r\n      currentHost: {},\r\n      currentAccount: {},\r\n      passwordVisibility: {\r\n        generated: false,\r\n        new: false,\r\n        confirm: false,\r\n        newAccount: false\r\n      },\r\n      viewMode: 'table',\r\n      filterText: '',\r\n      accountFilterText: '',\r\n      statusFilter: 'all',\r\n      expiryFilter: 'all',\r\n\r\n      // 修改密码弹窗\r\n      changePasswordModal: {\r\n        show: false,\r\n        method: 'auto',\r\n        policyId: 1,\r\n        generatedPassword: 'aX7#9pQr$2Lm',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        executeImmediately: true,\r\n        saveHistory: false,\r\n        logAudit: true\r\n      },\r\n\r\n      // 批量更新密码弹窗\r\n      batchUpdateModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        executionTime: 'immediate',\r\n        scheduledDate: '',\r\n        scheduledTime: '',\r\n        ignoreErrors: true,\r\n        detailedLog: true,\r\n        sendNotification: false\r\n      },\r\n\r\n      // 批量应用策略弹窗\r\n      batchApplyModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 1,\r\n        updateImmediately: false,\r\n        applyOnNextUpdate: true\r\n      },\r\n\r\n      // 紧急重置密码弹窗\r\n      emergencyResetModal: {\r\n        show: false,\r\n        selectedHosts: {},\r\n        policyId: 3, // 默认使用紧急策略\r\n        reason: 'security_incident',\r\n        description: ''\r\n      },\r\n\r\n      // 添加账号弹窗\r\n      addAccountModal: {\r\n        show: false,\r\n        username: '',\r\n        password: '',\r\n        isDefault: false,\r\n        policyId: 1\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      hosts: state => state.hosts,\r\n      policies: state => state.policies\r\n    }),\r\n    ...mapGetters(['selectedHosts']),\r\n\r\n    passwordMismatch() {\r\n      return this.changePasswordModal.newPassword &&\r\n        this.changePasswordModal.confirmPassword &&\r\n        this.changePasswordModal.newPassword !== this.changePasswordModal.confirmPassword\r\n    },\r\n\r\n    selectedHostsCount() {\r\n      return Object.values(this.batchUpdateModal.selectedHosts).filter(Boolean).length\r\n    },\r\n\r\n    selectedHostsList() {\r\n      return this.hosts.filter(host => host.selected)\r\n    },\r\n\r\n    emergencyPolicies() {\r\n      // 返回紧急策略和高强度策略\r\n      return this.policies.filter(p => p.id === 3 || p.id === 1)\r\n    },\r\n\r\n    // 过滤后的主机列表\r\n    filteredHosts() {\r\n      return this.hosts.filter(host => {\r\n        // 文本过滤\r\n        const textMatch = this.filterText === '' ||\r\n          host.name.toLowerCase().includes(this.filterText.toLowerCase()) ||\r\n          host.ip.includes(this.filterText);\r\n\r\n        // 状态过滤\r\n        const statusMatch = this.statusFilter === 'all' || host.status === this.statusFilter;\r\n\r\n        return textMatch && statusMatch;\r\n      });\r\n    },\r\n\r\n    // 获取所有账号（扁平化处理）\r\n    getAllAccounts() {\r\n      // 为每个账号添加主机引用\r\n      const accounts = [];\r\n      this.filteredHosts.forEach(host => {\r\n        host.accounts.forEach(account => {\r\n          accounts.push({\r\n            ...account,\r\n            host: host\r\n          });\r\n        });\r\n      });\r\n      return accounts;\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSelectAll(value) {\r\n      this.$store.commit('selectAllHosts', value)\r\n    },\r\n\r\n    toggleSelectAllBatch(value) {\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = value\r\n      })\r\n    },\r\n\r\n    openChangePasswordModal(host, account) {\r\n      this.currentHost = host\r\n      this.currentAccount = account\r\n      this.changePasswordModal.show = true\r\n      this.changePasswordModal.generatedPassword = this.generatePassword()\r\n    },\r\n\r\n    openBatchUpdateModal() {\r\n      this.batchUpdateModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchUpdateModal.selectedHosts[host.id] = host.selected\r\n      })\r\n\r\n      // 设置默认值\r\n      const today = new Date()\r\n      this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n      this.batchUpdateModal.scheduledTime = '03:00'\r\n    },\r\n\r\n    openBatchApplyModal() {\r\n      this.batchApplyModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.batchApplyModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    showEmergencyReset() {\r\n      this.emergencyResetModal.show = true\r\n\r\n      // 初始化选中状态\r\n      this.hosts.forEach(host => {\r\n        this.emergencyResetModal.selectedHosts[host.id] = host.selected\r\n      })\r\n    },\r\n\r\n    generatePassword(policy) {\r\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()'\r\n      let password = ''\r\n\r\n      // 获取所选策略的最小长度\r\n      const policyObj = policy || this.policies.find(p => p.id === this.changePasswordModal.policyId)\r\n      const minLength = policyObj ? policyObj.minLength : 12\r\n\r\n      // 生成随机密码\r\n      for (let i = 0; i < minLength; i++) {\r\n        password += chars.charAt(Math.floor(Math.random() * chars.length))\r\n      }\r\n\r\n      if (this.changePasswordModal && !policy) {\r\n        this.changePasswordModal.generatedPassword = password\r\n      }\r\n\r\n      return password\r\n    },\r\n\r\n    async updatePassword() {\r\n      if (this.changePasswordModal.method === 'manual' && this.passwordMismatch) {\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        const password = this.changePasswordModal.method === 'auto'\r\n          ? this.changePasswordModal.generatedPassword\r\n          : this.changePasswordModal.newPassword\r\n\r\n        await this.$store.dispatch('updateHostPassword', {\r\n          hostId: this.currentHost.id,\r\n          accountId: this.currentAccount.id,\r\n          password: password,\r\n          policyId: this.changePasswordModal.policyId\r\n        })\r\n\r\n        this.changePasswordModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功更新主机 ${this.currentHost.name} 的 ${this.currentAccount.username} 账号密码！`)\r\n      } catch (error) {\r\n        console.error('更新密码失败', error)\r\n        alert('更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchUpdatePasswords() {\r\n      const selectedHostIds = Object.entries(this.batchUpdateModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      if (this.batchUpdateModal.executionTime === 'scheduled') {\r\n        // 在实际应用中，这里会创建一个定时任务\r\n        alert('已创建定时密码更新任务！')\r\n        this.batchUpdateModal.show = false\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取所选策略\r\n        const policy = this.policies.find(p => p.id === this.batchUpdateModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.batchUpdateModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号更新密码！`)\r\n      } catch (error) {\r\n        console.error('批量更新密码失败', error)\r\n        alert('批量更新密码失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async batchApplyPolicy() {\r\n      const selectedHostIds = Object.entries(this.batchApplyModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('applyPolicyToHosts', {\r\n          policyId: this.batchApplyModal.policyId,\r\n          hostIds: selectedHostIds\r\n        })\r\n\r\n        this.batchApplyModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机应用密码策略！`)\r\n      } catch (error) {\r\n        console.error('应用策略失败', error)\r\n        alert('应用策略失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    async emergencyReset() {\r\n      const selectedHostIds = Object.entries(this.emergencyResetModal.selectedHosts)\r\n        .filter(([_, selected]) => selected)\r\n        .map(([id]) => parseInt(id))\r\n\r\n      if (selectedHostIds.length === 0) {\r\n        alert('请至少选择一台主机！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        // 获取紧急策略\r\n        const policy = this.policies.find(p => p.id === this.emergencyResetModal.policyId)\r\n\r\n        // 为每台主机的每个账号更新密码\r\n        for (const hostId of selectedHostIds) {\r\n          const host = this.hosts.find(h => h.id === hostId)\r\n          if (host) {\r\n            for (const account of host.accounts) {\r\n              const newPassword = this.generatePassword(policy)\r\n              await this.$store.dispatch('updateHostPassword', {\r\n                hostId: hostId,\r\n                accountId: account.id,\r\n                password: newPassword,\r\n                policyId: policy.id\r\n              })\r\n            }\r\n          }\r\n        }\r\n\r\n        this.emergencyResetModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为 ${selectedHostIds.length} 台主机的所有账号执行紧急密码重置！`)\r\n      } catch (error) {\r\n        console.error('紧急重置失败', error)\r\n        alert('紧急重置失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    togglePasswordVisibility(hostId) {\r\n      this.passwordVisibility[hostId] = !this.passwordVisibility[hostId]\r\n    },\r\n\r\n    isPasswordExpired(account) {\r\n      if (!account.passwordExpiryDate) return { status: 'normal', days: null, text: '-' }\r\n\r\n      // 解析过期时间\r\n      const expiryDate = new Date(account.passwordExpiryDate)\r\n      const now = new Date()\r\n\r\n      // 如果已过期\r\n      if (expiryDate < now) {\r\n        return {\r\n          status: 'expired',\r\n          days: 0,\r\n          text: '已过期'\r\n        }\r\n      }\r\n\r\n      // 计算剩余天数和小时数\r\n      const diffTime = expiryDate - now\r\n      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))\r\n      const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n\r\n      // 根据剩余时间确定状态\r\n      let status = 'normal'\r\n      if (diffDays < 7) {\r\n        status = 'danger'  // 少于7天\r\n      } else if (diffDays < 14) {\r\n        status = 'warning' // 少于14天\r\n      }\r\n\r\n      // 格式化显示文本\r\n      let text = ''\r\n      if (diffDays > 0) {\r\n        text += `${diffDays}天`\r\n      }\r\n      if (diffHours > 0 || diffDays === 0) {\r\n        text += `${diffHours}小时`\r\n      }\r\n\r\n      return { status, days: diffDays, text: `剩余${text}` }\r\n    },\r\n\r\n    openAddAccountModal(host) {\r\n      this.currentHost = host\r\n      this.addAccountModal.show = true\r\n    },\r\n\r\n    async addAccount() {\r\n      if (!this.addAccountModal.username || !this.addAccountModal.password) {\r\n        alert('请填写完整的账号信息！')\r\n        return\r\n      }\r\n\r\n      this.processing = true\r\n\r\n      try {\r\n        await this.$store.dispatch('addHostAccount', {\r\n          hostId: this.currentHost.id,\r\n          username: this.addAccountModal.username,\r\n          password: this.addAccountModal.password,\r\n          policyId: this.addAccountModal.policyId,\r\n          isDefault: this.addAccountModal.isDefault\r\n        })\r\n\r\n        this.addAccountModal.show = false\r\n\r\n        // 提示用户操作成功\r\n        alert(`已成功为主机 ${this.currentHost.name} 添加账号！`)\r\n      } catch (error) {\r\n        console.error('添加账号失败', error)\r\n        alert('添加账号失败，请重试！')\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    },\r\n\r\n    generatePasswordForNewAccount() {\r\n      this.addAccountModal.password = this.generatePassword()\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化日期和时间\r\n    const today = new Date()\r\n    this.batchUpdateModal.scheduledDate = today.toISOString().split('T')[0]\r\n    this.batchUpdateModal.scheduledTime = '03:00'\r\n  }\r\n}\r\n</script>"], "mappings": ";;EAGSA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAA6C;;EACjDA,KAAK,EAAC;AAA6B;;EAqBnCA,KAAK,EAAC;AAA6B;;EAEjCA,KAAK,EAAC;AAAqD;;EAgB3DA,KAAK,EAAC;AAAU;;EAGdA,KAAK,EAAC;AAAsE;;EAM9EA,KAAK,EAAC;AAAU;;EAGdA,KAAK,EAAC;AAAsE;;EAxD7FC,GAAA;EAoFqCD,KAAK,EAAC;;;EAEhCA,KAAK,EAAC;AAAiE;;EACrEA,KAAK,EAAC;AAAuB;;EACvBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EAEzBA,KAAK,EAAC;AAAgB;;EAEvBA,KAAK,EAAC;AAAsN;;EAK5NA,KAAK,EAAC;AAAsN;;EAO3NA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAAY;;EAEjBE,KAAK,EAAC,KAAK;EAACF,KAAK,EAAC;;;EA4BnBA,KAAK,EAAC;AAAmC;;EAIxCA,KAAK,EAAC;AAAa;;EACjBG,OAAO,EAAC,GAAG;EAACH,KAAK,EAAC;;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAA2B;oBA/IxD;;EA6JkBA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EAEpBA,KAAK,EAAC;AAAgC;;EAI9CA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAmC;;EAzKjEC,GAAA;EA2KoBD,KAAK,EAAC;;;EAKRA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAuB;;EAEhCA,KAAK,EAAC;AAA6B;;EAnLrDC,GAAA;EA6LoBD,KAAK,EAAC;;;EA7L1BC,GAAA;EAgMoFD,KAAK,EAAC;;;EAKxEA,KAAK,EAAC;AAA6B;;EAChCA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAW;oBAvMxC;oBAAA;;EAmNkBA,KAAK,EAAC;AAA6B;;EAGnCA,KAAK,EAAC;AAAmD;;EACtDA,KAAK,EAAC;AAAgB;oBAvN3C;oBAAA;;EAAAC,GAAA;AAAA;;EA0OgBE,OAAO,EAAC,GAAG;EAACH,KAAK,EAAC;;;EACfA,KAAK,EAAC;AAAe;;EAWxBA,KAAK,EAAC;AAAsD;;EAE/DA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAuC;;EAC3CA,KAAK,EAAC;AAAmB;;EAGtBA,KAAK,EAAC;AAAmC;;EAC1CA,KAAK,EAAC;AAAuB;;EAOjCA,KAAK,EAAC;AAAW;;EAGbA,KAAK,EAAC;AAAwC;;EAC5CA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAmC;;EA3QjEC,GAAA;EA6QoBD,KAAK,EAAC;;oBA7Q1B;;EA0RmBA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAmB;oBA5R9C;oBAAA;;EAwSmBA,KAAK,EAAC;AAAgC;;EAGlCA,KAAK,EAAC;AAAe;;EA3S5CC,GAAA;EAwTsBD,KAAK,EAAC;;;EAUbA,KAAK,EAAC;AAA0B;oBAlU/C;;EAgVWA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAiC;;EAOzCA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAgB;;EA3VnCC,GAAA;EA2WwDD,KAAK,EAAC;;oBA3W9D;;EAmXaA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAA2B;;EAQjCA,KAAK,EAAC;AAAmB;oBA5XxC;;EAAAC,GAAA;EAuYkBD,KAAK,EAAC;;;EACXA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;oBA1YxC;;EAoZaA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAmB;oBAtZxC;;EAAAC,GAAA;EA+ZuCD,KAAK,EAAC;;;EAMlCA,KAAK,EAAC;AAAgB;;EAgBtBA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAiC;;EAMzCA,KAAK,EAAC;AAAiB;;EAKvBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAA0D;;EAOlEA,KAAK,EAAC;AAAiB;qBA3clC;;EAodWA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAA2B;;EAQjCA,KAAK,EAAC;AAAmB;qBA7dtC;;EA2eWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAM;;EAKZA,KAAK,EAAC;AAAgE;;EAKxEA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAY;qBA1f7B;;EAmgBWA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAmB;;EA1gB1CC,GAAA;EAghBmED,KAAK,EAAC;;;EAC1DA,KAAK,EAAC;AAAwB;;EAalCA,KAAK,EAAC;AAAY;;EAiBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;qBAzjB7B;;EAkkBWA,KAAK,EAAC;AAAY;;EAkBlBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgE;;EAQxEA,KAAK,EAAC;AAAY;qBA9lB7B;;EAumBWA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;;;;;;uBAjnB3BI,mBAAA,CAunBM,cAtnBJC,mBAAA,UAAa,EACbC,mBAAA,CA6EM,OA7ENC,UA6EM,GA5EJD,mBAAA,CA2EM,OA3ENE,UA2EM,GA1EJF,mBAAA,CAmBM,OAnBNG,UAmBM,GAlBJH,mBAAA,CAKS;IAJPN,KAAK,EAAC,qNAAqN;IAC1NU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,kBAAA,IAAAD,QAAA,CAAAC,kBAAA,IAAAF,IAAA,CAAkB;MAC1BG,YAAA,CAA0EC,4BAAA;IAAtDC,IAAI,EAAE,+BAA+B;IAAEjB,KAAK,EAAC;kCACjEM,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAKS;IAJPN,KAAK,EAAC,wNAAwN;IAC7NU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAK,oBAAA,IAAAL,QAAA,CAAAK,oBAAA,IAAAN,IAAA,CAAoB;MAC5BG,YAAA,CAAyDC,4BAAA;IAArCC,IAAI,EAAE,cAAc;IAAEjB,KAAK,EAAC;kCAChDM,mBAAA,CAAmB,cAAb,QAAM,qB,GAEdA,mBAAA,CAKS;IAJPN,KAAK,EAAC,8NAA8N;IACnOU,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAM,mBAAA,IAAAN,QAAA,CAAAM,mBAAA,IAAAP,IAAA,CAAmB;MAC3BG,YAAA,CAAgEC,4BAAA;IAA5CC,IAAI,EAAE,qBAAqB;IAAEjB,KAAK,EAAC;kCACvDM,mBAAA,CAAmB,cAAb,QAAM,qB,KAIhBA,mBAAA,CAoDM,OApDNc,UAoDM,GAnDJf,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNe,UAaM,GAZJf,mBAAA,CAKS;IALDN,KAAK,EA7BzBsB,eAAA,EA6B0B,8BAA8B;MAAA,0BACNC,KAAA,CAAAC,QAAQ;MAAA,6BAA2CD,KAAA,CAAAC,QAAQ;IAAA;IAC9Fd,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAChBT,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEjB,KAAK,EAAC;kCAhChE0B,gBAAA,CAgCyE,MAE7D,G,kBACApB,mBAAA,CAKS;IALDN,KAAK,EAnCzBsB,eAAA,EAmC0B,8BAA8B;MAAA,0BACNC,KAAA,CAAAC,QAAQ;MAAA,6BAA0CD,KAAA,CAAAC,QAAQ;IAAA;IAC7Fd,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAAEF,KAAA,CAAAC,QAAQ;MAChBT,YAAA,CAA8DC,4BAAA;IAA1CC,IAAI,EAAE,mBAAmB;IAAEjB,KAAK,EAAC;kCAtCnE0B,gBAAA,CAsC4E,MAEhE,G,oBAGFrB,mBAAA,QAAW,EACXC,mBAAA,CAMM,OANNqB,UAMM,G,gBALJrB,mBAAA,CAC2L;IADpLsB,IAAI,EAAC,MAAM;IA7C9B,uBAAAjB,MAAA,QAAAA,MAAA,MAAAc,MAAA,IA6CwCF,KAAA,CAAAM,UAAU,GAAAJ,MAAA;IAAEK,WAAW,EAAC,SAAS;IAC3D9B,KAAK,EAAC;iDADoBuB,KAAA,CAAAM,UAAU,E,GAEtCvB,mBAAA,CAEM,OAFNyB,UAEM,GADJhB,YAAA,CAAqEC,4BAAA;IAAjDC,IAAI,EAAE,iBAAiB;IAAEjB,KAAK,EAAC;UAIvDK,mBAAA,UAAa,EACbC,mBAAA,CAMM,OANN0B,UAMM,G,gBALJ1B,mBAAA,CAC2L;IADpLsB,IAAI,EAAC,MAAM;IAtD9B,uBAAAjB,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAsDwCF,KAAA,CAAAU,iBAAiB,GAAAR,MAAA;IAAEK,WAAW,EAAC,SAAS;IAClE9B,KAAK,EAAC;iDADoBuB,KAAA,CAAAU,iBAAiB,E,GAE7C3B,mBAAA,CAEM,OAFN4B,UAEM,GADJnB,YAAA,CAAmEC,4BAAA;IAA/CC,IAAI,EAAE,eAAe;IAAEjB,KAAK,EAAC;UAIrDK,mBAAA,UAAa,E,gBACbC,mBAAA,CAMS;IApEnB,uBAAAK,MAAA,QAAAA,MAAA,MAAAc,MAAA,IA8D2BF,KAAA,CAAAY,YAAY,GAAAV,MAAA;IAC3BzB,KAAK,EAAC;kCACNM,mBAAA,CAAiC;IAAzB8B,KAAK,EAAC;EAAK,GAAC,MAAI,qBACxB9B,mBAAA,CAAkC;IAA1B8B,KAAK,EAAC;EAAQ,GAAC,IAAE,qBACzB9B,mBAAA,CAAmC;IAA3B8B,KAAK,EAAC;EAAS,GAAC,IAAE,qBAC1B9B,mBAAA,CAAiC;IAAzB8B,KAAK,EAAC;EAAO,GAAC,IAAE,oB,2CALTb,KAAA,CAAAY,YAAY,E,GAQ7B9B,mBAAA,cAAiB,E,gBACjBC,mBAAA,CAMS;IA7EnB,uBAAAK,MAAA,QAAAA,MAAA,MAAAc,MAAA,IAuE2BF,KAAA,CAAAc,YAAY,GAAAZ,MAAA;IAC3BzB,KAAK,EAAC;kCACNM,mBAAA,CAAiC;IAAzB8B,KAAK,EAAC;EAAK,GAAC,MAAI,qBACxB9B,mBAAA,CAAoC;IAA5B8B,KAAK,EAAC;EAAS,GAAC,KAAG,qBAC3B9B,mBAAA,CAA2C;IAAnC8B,KAAK,EAAC;EAAe,GAAC,MAAI,qBAClC9B,mBAAA,CAAmC;IAA3B8B,KAAK,EAAC;EAAO,GAAC,MAAI,oB,2CALXb,KAAA,CAAAc,YAAY,E,SAWnChC,mBAAA,UAAa,EACbA,mBAAA,UAAa,EACFkB,KAAA,CAAAC,QAAQ,gB,cAAnBpB,mBAAA,CA+JM,OA/JNkC,WA+JM,GA9JJjC,mBAAA,eAAkB,EAClBC,mBAAA,CAiBM,OAjBNiC,WAiBM,GAhBJjC,mBAAA,CAGM,OAHNkC,WAGM,G,4BA1Fdd,gBAAA,CAuF2C,MAC9B,IAAApB,mBAAA,CAA8D,QAA9DmC,WAA8D,EAAAC,gBAAA,CAAjCC,IAAA,CAAAC,gBAAgB,CAACC,MAAM,kB,4BAxFjEnB,gBAAA,CAwF2E,UAC9D,IAAApB,mBAAA,CAA4D,QAA5DwC,WAA4D,EAAAJ,gBAAA,CAA/B7B,QAAA,CAAAkC,cAAc,CAACF,MAAM,kB,4BAzF/DnB,gBAAA,CAyFyE,MACjE,G,GACApB,mBAAA,CAWM,OAXN0C,WAWM,GAVJ1C,mBAAA,CAIS,UAJT2C,WAIS,GAFPlC,YAAA,CAAiEC,4BAAA;IAA7CC,IAAI,EAAE,sBAAsB;IAAEjB,KAAK,EAAC;kCA9FpE0B,gBAAA,CA8F6E,MAEnE,G,GACApB,mBAAA,CAIS,UAJT4C,WAIS,GAFPnC,YAAA,CAA2DC,4BAAA;IAAvCC,IAAI,EAAE,gBAAgB;IAAEjB,KAAK,EAAC;kCAnG9D0B,gBAAA,CAmGuE,MAE7D,G,OAIJpB,mBAAA,CAyIQ,SAzIR6C,WAyIQ,GAxIN7C,mBAAA,CA6BQ,SA7BR8C,WA6BQ,GA5BN9C,mBAAA,CA2BK,aA1BHA,mBAAA,CAIK,MAJL+C,WAIK,GAHHtC,YAAA,CAEiBuC,yBAAA;IA/G/BC,UAAA,EA6GuChC,KAAA,CAAAiC,SAAS;IA7GhD,wB,oCA6GuCjC,KAAA,CAAAiC,SAAS,GAAA/B,MAAA,GAAsBZ,QAAA,CAAA4C,eAAe;;IA7GrFC,OAAA,EAAAC,QAAA,CA6GuF,MAEzEhD,MAAA,SAAAA,MAAA,QA/Gde,gBAAA,CA6GuF,OAEzE,E;IA/GdkC,CAAA;0FAiHYtD,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,QAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,YAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,UAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,sB,4BACAM,mBAAA,CAEK;IAFDJ,KAAK,EAAC,KAAK;IAACF,KAAK,EAAC;KAAiF,MAEvG,qB,KAGJM,mBAAA,CAyGQ,SAzGRuD,WAyGQ,GAxGNxD,mBAAA,aAAgB,G,kBAChBD,mBAAA,CA6FW0D,SAAA,QAvOrBC,WAAA,CA0IqDpB,IAAA,CAAAqB,eAAe,EA1IpE,CA0I4BC,SAAS,EAAEC,SAAS;yBA1IhD9D,mBAAA,CAAA0D,SAAA;MAAA7D,GAAA,EA0I4EgE,SAAS,CAACE;QAC1E9D,mBAAA,aAAgB,EAChBC,mBAAA,CAaK,MAbL8D,WAaK,GAZH9D,mBAAA,CAWK,MAXL+D,WAWK,GAVH/D,mBAAA,CASM,OATNgE,WASM,GARJhE,mBAAA,CAA8F,OAA9FiE,WAA8F,EAAA7B,gBAAA,CAApDuB,SAAS,CAACO,QAAQ,IAAG,IAAE,GAAA9B,gBAAA,CAAGuB,SAAS,CAACQ,MAAM,IAAG,GAAC,iBACxFnE,mBAAA,CAMM,cALJA,mBAAA,CAIS;MAJDN,KAAK,EAAC,8DAA8D;MACzEU,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAA6D,mBAAmB,CAACT,SAAS,CAACU,IAAI;QAC1C5D,YAAA,CAA0DC,4BAAA;MAAtCC,IAAI,EAAE,eAAe;MAAEjB,KAAK,EAAC;oCAnJvE0B,gBAAA,CAmJgF,QAE5D,G,iBArJpBkD,WAAA,E,SA0JYvE,mBAAA,SAAY,G,kBACZD,mBAAA,CA2EK0D,SAAA,QAtOjBC,WAAA,CA2JkDE,SAAS,CAACY,QAAQ,EA3JpE,CA2JwBC,OAAO,EAAEC,YAAY;2BAAjC3E,mBAAA,CA2EK;QA3EsDH,GAAG,EAAE6E,OAAO,CAACE,EAAE;QACvEhF,KAAK,EA5JpBsB,eAAA;UAAA,cA4JsCyD,YAAY;UAAA;QAAA;UACpCzE,mBAAA,CAMK,MANL2E,WAMK,GALH3E,mBAAA,CAIM,OAJN4E,WAIM,GAHJnE,YAAA,CAEiBuC,yBAAA;QAjKnCC,UAAA,EA+J2CuB,OAAO,CAACH,IAAI,CAACQ,QAAQ;QA/JhE,uBAAA1D,MAAA,IA+J2CqD,OAAO,CAACH,IAAI,CAACQ,QAAQ,GAAA1D,MAAA;QAAEzB,KAAK,EAAC;;QA/JxE0D,OAAA,EAAAC,QAAA,CAgKoB,MAA2E,CAA3ErD,mBAAA,CAA2E,QAA3E8E,WAA2E,EAAA1C,gBAAA,CAA3BoC,OAAO,CAACH,IAAI,CAACU,IAAI,iB;QAhKrFzB,CAAA;sFAoKctD,mBAAA,CAEK,MAFLgF,WAEK,GADHhF,mBAAA,CAA8D,OAA9DiF,WAA8D,EAAA7C,gBAAA,CAAxBoC,OAAO,CAACH,IAAI,CAACa,EAAE,iB,GAEvDlF,mBAAA,CAQK,MARLmF,WAQK,GAPHnF,mBAAA,CAMM,OANNoF,WAMM,GALJpF,mBAAA,CAA6E,QAA7EqF,WAA6E,EAAAjD,gBAAA,CAA1BoC,OAAO,CAACc,QAAQ,kBACvDd,OAAO,CAACe,SAAS,I,cAA7BzF,mBAAA,CAGO,QAHP0F,WAGO,EAFqG,MAE5G,KA7KlBzF,mBAAA,e,KAgLcC,mBAAA,CAEK,MAFLyF,WAEK,GADHzF,mBAAA,CAAgF,OAAhF0F,WAAgF,EAAAtD,gBAAA,CAA1CoC,OAAO,CAACmB,kBAAkB,wB,GAElE3F,mBAAA,CAiBK,MAjBL4F,WAiBK,GAhBH5F,mBAAA,CAeM;QAfAN,KAAK,EApL3BsB,eAAA;;qCAoLiLT,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM,iBAAiBvF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM;2CAAoEvF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM;uCAAgEvF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM;;UApL1c1E,gBAAA,CAAAgB,gBAAA,CA0LqB7B,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEuB,IAAI,IAAG,GACrC,iBACQxF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM,kBAAkBvF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM,iB,cAD5FhG,mBAAA,CAIO,QAJPkG,WAIO,GADLvF,YAAA,CAA6DC,4BAAA;QAAzCC,IAAI,EAAE;MAA+B,G,KAE1CJ,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM,kB,cAAlDhG,mBAAA,CAEO,QAFPmG,WAEO,GADLxF,YAAA,CAA2DC,4BAAA;QAAvCC,IAAI,EAAE;MAA6B,G,KAjM3EZ,mBAAA,e,oBAqMcC,mBAAA,CAaK,MAbLkG,WAaK,GAZHlG,mBAAA,CAWM,OAXNmG,WAWM,GAVJnG,mBAAA,CAIM,OAJNoG,WAIM,GAHJpG,mBAAA,CAEkG;QAF1FsB,IAAI,EAAEL,KAAA,CAAAoF,kBAAkB,CAAC7B,OAAO,CAACE,EAAE;QAA0B5C,KAAK,EAAE0C,OAAO,CAAC8B,QAAQ;QAC1FC,QAAQ,EAAR,EAAQ;QACR7G,KAAK,EAAC;8BA1M5B8G,WAAA,E,GA4MkBxG,mBAAA,CAIS;QAJAI,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAAkG,wBAAwB,CAACjC,OAAO,CAACE,EAAE;QACjDhF,KAAK,EAAC;UACNe,YAAA,CACoBC,4BAAA;QADAC,IAAI,UAAUM,KAAA,CAAAoF,kBAAkB,CAAC7B,OAAO,CAACE,EAAE;QAC7DhF,KAAK,EAAC;yDA/M5BgH,WAAA,E,KAmNc1G,mBAAA,CAEK,MAFL2G,WAEK,GADHlG,YAAA,CAA2CmG,sBAAA;QAA7BtF,IAAI,EAAEkD,OAAO,CAACH,IAAI,CAACyB;2CAEnC9F,mBAAA,CAeK,MAfL6G,WAeK,GAdH7G,mBAAA,CAaM,OAbN8G,WAaM,GAZJ9G,mBAAA,CAKS;QAJPN,KAAK,EAAC,0NAA0N;QAC/NU,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAAwG,uBAAuB,CAACvC,OAAO,CAACH,IAAI,EAAEG,OAAO;UACrD/D,YAAA,CAAyDC,4BAAA;QAArCC,IAAI,EAAE,cAAc;QAAEjB,KAAK,EAAC;sCA3NpE0B,gBAAA,CA2N6E,QAE3D,G,iBA7NlB4F,WAAA,GA8NkBhH,mBAAA,CAKS;QAJPN,KAAK,EAAC,sNAAsN;QAC3NU,OAAK,EAAAe,MAAA,IAAEkB,IAAA,CAAA4E,YAAY,CAACzC,OAAO;UAC5B/D,YAAA,CAA0DC,4BAAA;QAAtCC,IAAI,EAAE,eAAe;QAAEjB,KAAK,EAAC;sCAjOrE0B,gBAAA,CAiO8E,MAE5D,G,iBAnOlB8F,WAAA,E;;kCAwOUnH,mBAAA,WAAc,EACJsC,IAAA,CAAAC,gBAAgB,CAACC,MAAM,U,cAAjCzC,mBAAA,CAOK,MAhPfqH,WAAA,GA0OYnH,mBAAA,CAKK,MALLoH,WAKK,GAJHpH,mBAAA,CAGM,OAHNqH,WAGM,GAFJ5G,YAAA,CAAqEC,4BAAA;IAAjDC,IAAI,EAAE,iBAAiB;IAAEjB,KAAK,EAAC;kCACnDM,mBAAA,CAAkB,WAAf,aAAW,qB,SA7O9BD,mBAAA,e,wBAsPID,mBAAA,CAsFM0D,SAAA;IA5UV7D,GAAA;EAAA,IAqPII,mBAAA,UAAa,EACbC,mBAAA,CAsFM,OAtFNsH,WAsFM,I,kBArFJxH,mBAAA,CAoFM0D,SAAA,QA3UZC,WAAA,CAuP0BlD,QAAA,CAAAgH,aAAa,EAArBlD,IAAI;yBAAhBvE,mBAAA,CAoFM;MApF8BH,GAAG,EAAE0E,IAAI,CAACK,EAAE;MAAEhF,KAAK,EAAC;QACtDM,mBAAA,CAkFM,OAlFNwH,WAkFM,GAjFJzH,mBAAA,UAAa,EACbC,mBAAA,CASM,OATNyH,WASM,GARJzH,mBAAA,CAMM,OANN0H,WAMM,GALJjH,YAAA,CAAuDuC,yBAAA;MA5PrEC,UAAA,EA4PuCoB,IAAI,CAACQ,QAAQ;MA5PpD,uBAAA1D,MAAA,IA4PuCkD,IAAI,CAACQ,QAAQ,GAAA1D,MAAA;MAAEzB,KAAK,EAAC;oEAC9CM,mBAAA,CAGM,cAFJA,mBAAA,CAAkE,MAAlE2H,WAAkE,EAAAvF,gBAAA,CAAjBiC,IAAI,CAACU,IAAI,kBAC1D/E,mBAAA,CAAkD,KAAlD4H,WAAkD,EAAAxF,gBAAA,CAAdiC,IAAI,CAACa,EAAE,iB,KAG/CzE,YAAA,CAAmCmG,sBAAA;MAArBtF,IAAI,EAAE+C,IAAI,CAACyB;yCAG3B/F,mBAAA,UAAa,EACbC,mBAAA,CAyDM,OAzDN6H,WAyDM,I,kBAxDJ/H,mBAAA,CAuDM0D,SAAA,QA9TlBC,WAAA,CAuQmCY,IAAI,CAACE,QAAQ,EAAxBC,OAAO;2BAAnB1E,mBAAA,CAuDM;QAvDiCH,GAAG,EAAE6E,OAAO,CAACE,EAAE;QAAEhF,KAAK,EAvQzEsB,eAAA,EAuQ0E,uCAAuC;UAAA,gCACzDwD,OAAO,CAACe;QAAS;UAC3DvF,mBAAA,CAcM,OAdN8H,WAcM,GAbJ9H,mBAAA,CAMM,OANN+H,WAMM,GALJ/H,mBAAA,CAA6E,QAA7EgI,WAA6E,EAAA5F,gBAAA,CAA1BoC,OAAO,CAACc,QAAQ,kBACvDd,OAAO,CAACe,SAAS,I,cAA7BzF,mBAAA,CAGO,QAHPmI,WAGO,EAFqG,MAE5G,KA/QlBlI,mBAAA,e,GAiRgBC,mBAAA,CAKS;QAJPN,KAAK,EAAC,wNAAwN;QAC7NU,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAAwG,uBAAuB,CAAC1C,IAAI,EAAEG,OAAO;UAC7C/D,YAAA,CAAyDC,4BAAA;QAArCC,IAAI,EAAE,cAAc;QAAEjB,KAAK,EAAC;sCApRlE0B,gBAAA,CAoR2E,QAE3D,G,iBAtRhB8G,WAAA,E,GAyRcnI,mBAAA,UAAa,EACbC,mBAAA,CAWM,OAXNmI,WAWM,G,4BAVJnI,mBAAA,CAA4D;QAAvDN,KAAK,EAAC;MAAwC,GAAC,IAAE,sBACtDM,mBAAA,CAQM,OARNoI,WAQM,GAPJpI,mBAAA,CACkG;QAD1FsB,IAAI,EAAEL,KAAA,CAAAoF,kBAAkB,CAAC7B,OAAO,CAACE,EAAE;QAA0B5C,KAAK,EAAE0C,OAAO,CAAC8B,QAAQ;QAAEC,QAAQ,EAAR,EAAQ;QACpG7G,KAAK,EAAC;8BA9R1B2I,WAAA,GA+RkBrI,mBAAA,CAIS;QAJAI,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAAkG,wBAAwB,CAACjC,OAAO,CAACE,EAAE;QACjDhF,KAAK,EAAC;UACNe,YAAA,CACoBC,4BAAA;QADAC,IAAI,UAAUM,KAAA,CAAAoF,kBAAkB,CAAC7B,OAAO,CAACE,EAAE;QAC7DhF,KAAK,EAAC;yDAlS5B4I,WAAA,E,KAuScvI,mBAAA,YAAe,EACfC,mBAAA,CAqBM,OArBNuI,WAqBM,GApBJvI,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAwD;QAAnDN,KAAK,EAAC;MAAgC,GAAC,QAAM,sBAClDM,mBAAA,CAAwE,OAAxEwI,WAAwE,EAAApG,gBAAA,CAA1CoC,OAAO,CAACmB,kBAAkB,wB,GAE1D3F,mBAAA,CAeM,c,4BAdJA,mBAAA,CAAsD;QAAjDN,KAAK,EAAC;MAAgC,GAAC,MAAI,sBAChDM,mBAAA,CAYM;QAZAN,KAAK,EA/S7BsB,eAAA;;qCA+SqLT,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM,iBAAiBvF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM;2CAAsEvF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM;uCAAkEvF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM;;UA/Sld1E,gBAAA,CAAAgB,gBAAA,CAqTuB7B,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEuB,IAAI,IAAG,GACrC,iBACQxF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM,kBAAkBvF,QAAA,CAAAsF,iBAAiB,CAACrB,OAAO,EAAEsB,MAAM,iB,cAD5FhG,mBAAA,CAIO,QAJP2I,WAIO,GADLhI,YAAA,CAA6DC,4BAAA;QAAzCC,IAAI,EAAE;MAA+B,G,KAzT/EZ,mBAAA,e;sCAiUUA,mBAAA,YAAe,EACfC,mBAAA,CAOM,OAPN0I,WAOM,GANJ1I,mBAAA,CAKS;MAJPN,KAAK,EAAC,sNAAsN;MAC3NU,OAAK,EAAAe,MAAA,IAAEZ,QAAA,CAAA6D,mBAAmB,CAACC,IAAI;QAChC5D,YAAA,CAA0DC,4BAAA;MAAtCC,IAAI,EAAE,eAAe;MAAEjB,KAAK,EAAC;oCAtU/D0B,gBAAA,CAsUwE,QAE5D,G,iBAxUZuH,WAAA,E;sFA8UI5I,mBAAA,YAAe,EACfU,YAAA,CAkGYmI,oBAAA;IAjbhB3F,UAAA,EA+UwBhC,KAAA,CAAA4H,mBAAmB,CAACC,IAAI;IA/UhD,uBAAAzI,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA+UwBF,KAAA,CAAA4H,mBAAmB,CAACC,IAAI,GAAA3H,MAAA;IAAE4H,KAAK,EAAC,MAAM;IAAEC,SAAO,EAAEzI,QAAA,CAAA0I,cAAc;IAAGC,OAAO,EAAEjI,KAAA,CAAAkI;;IA/UnG/F,OAAA,EAAAC,QAAA,CAgVM,MAOM,CAPNrD,mBAAA,CAOM,OAPNoJ,WAOM,G,4BANJpJ,mBAAA,CAAwC;MAAnCN,KAAK,EAAC;IAAkB,GAAC,MAAI,sBAClCM,mBAAA,CAIM,OAJNqJ,WAIM,GAHJrJ,mBAAA,CAAuE,c,4BAAlEA,mBAAA,CAAqC;MAA/BN,KAAK,EAAC;IAAa,GAAC,MAAI,sBAnV7C0B,gBAAA,CAmVoD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAqI,WAAW,CAACvE,IAAI,iB,GAC9D/E,mBAAA,CAAsE,c,4BAAjEA,mBAAA,CAAsC;MAAhCN,KAAK,EAAC;IAAa,GAAC,OAAK,sBApV9C0B,gBAAA,CAoVqD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAqI,WAAW,CAACpE,EAAE,iB,GAC7DlF,mBAAA,CAA6E,c,4BAAxEA,mBAAA,CAAoC;MAA9BN,KAAK,EAAC;IAAa,GAAC,KAAG,sBArV5C0B,gBAAA,CAqVmD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAsI,cAAc,CAACjE,QAAQ,iB,OAIxEtF,mBAAA,CAgBM,OAhBNwJ,WAgBM,G,4BAfJxJ,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAaM,OAbNyJ,WAaM,GAZJzJ,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAA4H,mBAAmB,CAACa,MAAM;MACxChK,KAAK,EA7VjBsB,eAAA,EA6VkB,iFAAiF,EAC/EC,KAAA,CAAA4H,mBAAmB,CAACa,MAAM;QAClCjJ,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;oCA/VjE0B,gBAAA,CA+V0E,QAEhE,G,kBACApB,mBAAA,CAKS;MALAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAA4H,mBAAmB,CAACa,MAAM;MACxChK,KAAK,EAnWjBsB,eAAA,EAmWkB,iFAAiF,EAC/EC,KAAA,CAAA4H,mBAAmB,CAACa,MAAM;QAClCjJ,YAAA,CAA0DC,4BAAA;MAAtCC,IAAI,EAAE,eAAe;MAAEjB,KAAK,EAAC;oCArW7D0B,gBAAA,CAqWsE,QAE5D,G,sBAIOH,KAAA,CAAA4H,mBAAmB,CAACa,MAAM,e,cAArC5J,mBAAA,CA0BM,OA1BN6J,WA0BM,G,4BAzBJ3J,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MAjXjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA6WyBF,KAAA,CAAA4H,mBAAmB,CAACe,QAAQ,GAAAzI,MAAA;MAAEzB,KAAK,EAAC,aAAa;MAAEmK,QAAM,EAAAxJ,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEZ,QAAA,CAAAuJ,gBAAgB;2BAC1FhK,mBAAA,CAES0D,SAAA,QAhXnBC,WAAA,CA8WmCpB,IAAA,CAAA0H,QAAQ,EAAlBC,MAAM;2BAArBlK,mBAAA,CAES;QAF2BH,GAAG,EAAEqK,MAAM,CAACtF,EAAE;QAAG5C,KAAK,EAAEkI,MAAM,CAACtF;0BAC9DsF,MAAM,CAACjF,IAAI,IAAG,UAAQ,GAAA3C,gBAAA,CAAG4H,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA7H,gBAAA,CAAG4H,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAhXVC,WAAA;6FA6WyBlJ,KAAA,CAAA4H,mBAAmB,CAACe,QAAQ,E,GAM7C5J,mBAAA,CAiBM,OAjBNoK,WAiBM,GAhBJpK,mBAAA,CAOM,OAPNqK,WAOM,G,4BANJrK,mBAAA,CAAuC;MAAhCN,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BM,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEZ,QAAA,CAAAuJ,gBAAgB;MAAIxI,IAAI,EAAC,QAAQ;MAC/C5B,KAAK,EAAC;QACNe,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;oCAxXnE0B,gBAAA,CAwX4E,QAEhE,G,KAEFpB,mBAAA,CAOM,OAPNsK,WAOM,G,gBANJtK,mBAAA,CACoG;MAD5FsB,IAAI,EAAEL,KAAA,CAAAoF,kBAAkB,CAACkE,SAAS;MA7XtD,uBAAAlK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8XuBF,KAAA,CAAA4H,mBAAmB,CAAC2B,iBAAiB,GAAArJ,MAAA;MAAEoF,QAAQ,EAAR,EAAQ;MAAC7G,KAAK,EAAC;4BA9X7E+K,WAAA,I,iBA8XuBxJ,KAAA,CAAA4H,mBAAmB,CAAC2B,iBAAiB,E,GAChDxK,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAAoF,kBAAkB,CAACkE,SAAS,IAAItJ,KAAA,CAAAoF,kBAAkB,CAACkE,SAAS;MAAEjJ,IAAI,EAAC,QAAQ;MACzF5B,KAAK,EAAC;QACNe,YAAA,CAAyGC,4BAAA;MAArFC,IAAI,UAAUM,KAAA,CAAAoF,kBAAkB,CAACkE,SAAS;MAAyB7K,KAAK,EAAC;gEAMrGI,mBAAA,CA4BM,OA5BN4K,WA4BM,GA3BJ1K,mBAAA,CAUM,OAVN2K,WAUM,G,4BATJ3K,mBAAA,CAAqC;MAA9BN,KAAK,EAAC;IAAY,GAAC,KAAG,sBAC7BM,mBAAA,CAOM,OAPN4K,WAOM,G,gBANJ5K,mBAAA,CACoD;MAD5CsB,IAAI,EAAEL,KAAA,CAAAoF,kBAAkB,CAACwE,GAAG;MA3YhD,uBAAAxK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA2YiFF,KAAA,CAAA4H,mBAAmB,CAACiC,WAAW,GAAA3J,MAAA;MAClGzB,KAAK,EAAC,qBAAqB;MAAC8B,WAAW,EAAC;4BA5YtDuJ,WAAA,I,iBA2YiF9J,KAAA,CAAA4H,mBAAmB,CAACiC,WAAW,E,GAEpG9K,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAAoF,kBAAkB,CAACwE,GAAG,IAAI5J,KAAA,CAAAoF,kBAAkB,CAACwE,GAAG;MAAEvJ,IAAI,EAAC,QAAQ;MAC7E5B,KAAK,EAAC;QACNe,YAAA,CAAmGC,4BAAA;MAA/EC,IAAI,UAAUM,KAAA,CAAAoF,kBAAkB,CAACwE,GAAG;MAAyBnL,KAAK,EAAC;6CAK7FM,mBAAA,CAYM,OAZNgL,WAYM,G,4BAXJhL,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CAQM,OARNiL,WAQM,G,gBAPJjL,mBAAA,CAE0E;MAFlEsB,IAAI,EAAEL,KAAA,CAAAoF,kBAAkB,CAAC6E,OAAO;MAvZpD,uBAAA7K,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAwZuBF,KAAA,CAAA4H,mBAAmB,CAACsC,eAAe,GAAAhK,MAAA;MAAEzB,KAAK,EAxZjEsB,eAAA,EAwZkE,qBAAqB;QAAA,kBAC7CT,QAAA,CAAA6K;MAAgB;MAAI5J,WAAW,EAAC;oCAzZ1E6J,WAAA,I,iBAwZuBpK,KAAA,CAAA4H,mBAAmB,CAACsC,eAAe,E,GAE9CnL,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAAoF,kBAAkB,CAAC6E,OAAO,IAAIjK,KAAA,CAAAoF,kBAAkB,CAAC6E,OAAO;MAAE5J,IAAI,EAAC,QAAQ;MACrF5B,KAAK,EAAC;QACNe,YAAA,CAAuGC,4BAAA;MAAnFC,IAAI,UAAUM,KAAA,CAAAoF,kBAAkB,CAAC6E,OAAO;MAAyBxL,KAAK,EAAC;2CAGpFa,QAAA,CAAA6K,gBAAgB,I,cAA3BtL,mBAAA,CAA+E,OAA/EwL,WAA+E,EAAhB,YAAU,KA/ZnFvL,mBAAA,e,GAkaQU,YAAA,CAAqE8K,gCAAA;MAA7CjF,QAAQ,EAAErF,KAAA,CAAA4H,mBAAmB,CAACiC;8CAGxD9K,mBAAA,CAWM,OAXNwL,WAWM,G,4BAVJxL,mBAAA,CAA8C;MAAzCN,KAAK,EAAC;IAAwB,GAAC,MAAI,sBACxCe,YAAA,CAEiBuC,yBAAA;MAzazBC,UAAA,EAuaiChC,KAAA,CAAA4H,mBAAmB,CAAC4C,kBAAkB;MAvavE,uBAAApL,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAuaiCF,KAAA,CAAA4H,mBAAmB,CAAC4C,kBAAkB,GAAAtK,MAAA;;MAvavEiC,OAAA,EAAAC,QAAA,CAwaU,MAA8BhD,MAAA,SAAAA,MAAA,QAA9BL,mBAAA,CAA8B;QAAxBN,KAAK,EAAC;MAAM,GAAC,MAAI,oB;MAxajC4D,CAAA;uCA0aQ7C,YAAA,CAEiBuC,yBAAA;MA5azBC,UAAA,EA0aiChC,KAAA,CAAA4H,mBAAmB,CAAC6C,WAAW;MA1ahE,uBAAArL,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA0aiCF,KAAA,CAAA4H,mBAAmB,CAAC6C,WAAW,GAAAvK,MAAA;;MA1ahEiC,OAAA,EAAAC,QAAA,CA2aU,MAAgChD,MAAA,SAAAA,MAAA,QAAhCL,mBAAA,CAAgC;QAA1BN,KAAK,EAAC;MAAM,GAAC,QAAM,oB;MA3anC4D,CAAA;uCA6aQ7C,YAAA,CAEiBuC,yBAAA;MA/azBC,UAAA,EA6aiChC,KAAA,CAAA4H,mBAAmB,CAAC8C,QAAQ;MA7a7D,uBAAAtL,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA6aiCF,KAAA,CAAA4H,mBAAmB,CAAC8C,QAAQ,GAAAxK,MAAA;;MA7a7DiC,OAAA,EAAAC,QAAA,CA8aU,MAAgChD,MAAA,SAAAA,MAAA,QAAhCL,mBAAA,CAAgC;QAA1BN,KAAK,EAAC;MAAM,GAAC,QAAM,oB;MA9anC4D,CAAA;;IAAAA,CAAA;6DAmbIvD,mBAAA,YAAe,EACfU,YAAA,CAkDYmI,oBAAA;IAtehB3F,UAAA,EAobwBhC,KAAA,CAAA2K,eAAe,CAAC9C,IAAI;IApb5C,uBAAAzI,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAobwBF,KAAA,CAAA2K,eAAe,CAAC9C,IAAI,GAAA3H,MAAA;IAAE4H,KAAK,EAAC,MAAM;IAAEC,SAAO,EAAEzI,QAAA,CAAAsL,UAAU;IAAG3C,OAAO,EAAEjI,KAAA,CAAAkI;;IApb3F/F,OAAA,EAAAC,QAAA,CAqbM,MAMM,CANNrD,mBAAA,CAMM,OANN8L,WAMM,G,4BALJ9L,mBAAA,CAAwC;MAAnCN,KAAK,EAAC;IAAkB,GAAC,MAAI,sBAClCM,mBAAA,CAGM,OAHN+L,WAGM,GAFJ/L,mBAAA,CAAuE,c,4BAAlEA,mBAAA,CAAqC;MAA/BN,KAAK,EAAC;IAAa,GAAC,MAAI,sBAxb7C0B,gBAAA,CAwboD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAqI,WAAW,CAACvE,IAAI,iB,GAC9D/E,mBAAA,CAAsE,c,4BAAjEA,mBAAA,CAAsC;MAAhCN,KAAK,EAAC;IAAa,GAAC,OAAK,sBAzb9C0B,gBAAA,CAybqD,GAAC,GAAAgB,gBAAA,CAAGnB,KAAA,CAAAqI,WAAW,CAACpE,EAAE,iB,OAIjElF,mBAAA,CAGM,OAHNgM,WAGM,G,4BAFJhM,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAAkG;MAA3FsB,IAAI,EAAC,MAAM;MA/b1B,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA+boCF,KAAA,CAAA2K,eAAe,CAACtG,QAAQ,GAAAnE,MAAA;MAAEzB,KAAK,EAAC,cAAc;MAAC8B,WAAW,EAAC;mDAA3DP,KAAA,CAAA2K,eAAe,CAACtG,QAAQ,E,KAGtDtF,mBAAA,CAOM,OAPNiM,WAOM,G,8BANJjM,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAIM,OAJNkM,WAIM,G,gBAHJlM,mBAAA,CACoI;MAD7HsB,IAAI,EAAC,UAAU;MArchC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAqc0CF,KAAA,CAAA2K,eAAe,CAACrG,SAAS,GAAApE,MAAA;MACvDzB,KAAK,EAAC;uDADwBuB,KAAA,CAAA2K,eAAe,CAACrG,SAAS,E,iCAEzDvF,mBAAA,CAAsG;MAA/FN,KAAK,EAAC;IAAgF,4B,KAIjGM,mBAAA,CAOM,OAPNmM,WAOM,G,8BANJnM,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MAjdjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA6cyBF,KAAA,CAAA2K,eAAe,CAAChC,QAAQ,GAAAzI,MAAA;MAAEzB,KAAK,EAAC,aAAa;MAAEmK,QAAM,EAAAxJ,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEZ,QAAA,CAAA6L,6BAA6B;2BACnGtM,mBAAA,CAES0D,SAAA,QAhdnBC,WAAA,CA8cmCpB,IAAA,CAAA0H,QAAQ,EAAlBC,MAAM;2BAArBlK,mBAAA,CAES;QAF2BH,GAAG,EAAEqK,MAAM,CAACtF,EAAE;QAAG5C,KAAK,EAAEkI,MAAM,CAACtF;0BAC9DsF,MAAM,CAACjF,IAAI,IAAG,UAAQ,GAAA3C,gBAAA,CAAG4H,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA7H,gBAAA,CAAG4H,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAhdVmC,YAAA;6FA6cyBpL,KAAA,CAAA2K,eAAe,CAAChC,QAAQ,E,KAO3C5J,mBAAA,CAiBM,OAjBNsM,YAiBM,GAhBJtM,mBAAA,CAOM,OAPNuM,YAOM,G,8BANJvM,mBAAA,CAAuC;MAAhCN,KAAK,EAAC;IAAY,GAAC,OAAK,sBAC/BM,mBAAA,CAIS;MAJAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEZ,QAAA,CAAA6L,6BAA6B;MAAI9K,IAAI,EAAC,QAAQ;MAC5D5B,KAAK,EAAC;QACNe,YAAA,CAA8DC,4BAAA;MAA1CC,IAAI,EAAE,mBAAmB;MAAEjB,KAAK,EAAC;sCAzdjE0B,gBAAA,CAyd0E,QAEhE,G,KAEFpB,mBAAA,CAOM,OAPNwM,YAOM,G,gBANJxM,mBAAA,CAC2C;MADnCsB,IAAI,EAAEL,KAAA,CAAAoF,kBAAkB,CAACoG,UAAU;MA9drD,uBAAApM,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8dsFF,KAAA,CAAA2K,eAAe,CAACtF,QAAQ,GAAAnF,MAAA;MAAEoF,QAAQ,EAAR,EAAQ;MAC5G7G,KAAK,EAAC;4BA/dlBgN,YAAA,I,iBA8dsFzL,KAAA,CAAA2K,eAAe,CAACtF,QAAQ,E,GAEpGtG,mBAAA,CAGS;MAHAI,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAAEF,KAAA,CAAAoF,kBAAkB,CAACoG,UAAU,IAAIxL,KAAA,CAAAoF,kBAAkB,CAACoG,UAAU;MAAEnL,IAAI,EAAC,QAAQ;MAC3F5B,KAAK,EAAC;QACNe,YAAA,CAA0GC,4BAAA;MAAtFC,IAAI,UAAUM,KAAA,CAAAoF,kBAAkB,CAACoG,UAAU;MAAyB/M,KAAK,EAAC;;IAle1G4D,CAAA;6DAweIvD,mBAAA,cAAiB,EACjBU,YAAA,CAiEYmI,oBAAA;IA1iBhB3F,UAAA,EAyewBhC,KAAA,CAAA0L,gBAAgB,CAAC7D,IAAI;IAze7C,uBAAAzI,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAyewBF,KAAA,CAAA0L,gBAAgB,CAAC7D,IAAI,GAAA3H,MAAA;IAAE4H,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAAC6D,IAAI,EAAC,IAAI;IACpF5D,SAAO,EAAEzI,QAAA,CAAAsM,oBAAoB;IAAG3D,OAAO,EAAEjI,KAAA,CAAAkI;;IA1ehD/F,OAAA,EAAAC,QAAA,CA2eM,MAaM,CAbNrD,mBAAA,CAaM,OAbN8M,YAaM,G,8BAZJ9M,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAIM,OAJN+M,YAIM,GAHJtM,YAAA,CAEiBuC,yBAAA;MAhf3BC,UAAA,EA8emChC,KAAA,CAAA+L,cAAc;MA9ejD,wB,sCA8emC/L,KAAA,CAAA+L,cAAc,GAAA7L,MAAA,GAAsBZ,QAAA,CAAA0M,oBAAoB;;MA9e3F7J,OAAA,EAAAC,QAAA,CA8e6F,MAEnFhD,MAAA,UAAAA,MAAA,SAhfVe,gBAAA,CA8e6F,MAEnF,E;MAhfVkC,CAAA;gEAkfQtD,mBAAA,CAIM,OAJNkN,YAIM,I,kBAHJpN,mBAAA,CAEiB0D,SAAA,QArf3BC,WAAA,CAmfyCpB,IAAA,CAAA8K,KAAK,EAAb9I,IAAI;2BAA3B+I,YAAA,CAEiBpK,yBAAA;QAFsBrD,GAAG,EAAE0E,IAAI,CAACK,EAAE;QAnf7DzB,UAAA,EAmfwEhC,KAAA,CAAA0L,gBAAgB,CAACU,aAAa,CAAChJ,IAAI,CAACK,EAAE;QAnf9G,uBAAAvD,MAAA,IAmfwEF,KAAA,CAAA0L,gBAAgB,CAACU,aAAa,CAAChJ,IAAI,CAACK,EAAE,IAAAvD;;QAnf9GiC,OAAA,EAAAC,QAAA,CAofY,MAAe,CApf3BjC,gBAAA,CAAAgB,gBAAA,CAofeiC,IAAI,CAACU,IAAI,IAAG,IAAE,GAAA3C,gBAAA,CAAGiC,IAAI,CAACa,EAAE,IAAG,IAChC,gB;QArfV5B,CAAA;;sCAufQtD,mBAAA,CAAyD,KAAzDsN,YAAyD,EAApC,MAAI,GAAAlL,gBAAA,CAAG7B,QAAA,CAAAgN,kBAAkB,IAAG,MAAI,gB,GAGvDvN,mBAAA,CAOM,OAPNwN,YAOM,G,8BANJxN,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAIS;MAhgBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA4fyBF,KAAA,CAAA0L,gBAAgB,CAAC/C,QAAQ,GAAAzI,MAAA;MAAEzB,KAAK,EAAC;2BAChDI,mBAAA,CAES0D,SAAA,QA/fnBC,WAAA,CA6fmCpB,IAAA,CAAA0H,QAAQ,EAAlBC,MAAM;2BAArBlK,mBAAA,CAES;QAF2BH,GAAG,EAAEqK,MAAM,CAACtF,EAAE;QAAG5C,KAAK,EAAEkI,MAAM,CAACtF;0BAC9DsF,MAAM,CAACjF,IAAI,IAAG,UAAQ,GAAA3C,gBAAA,CAAG4H,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA7H,gBAAA,CAAG4H,MAAM,CAACE,UAAU,IAAG,KAC9E,uBA/fVuD,YAAA;6EA4fyBxM,KAAA,CAAA0L,gBAAgB,CAAC/C,QAAQ,E,KAO5C5J,mBAAA,CAyBM,OAzBN0N,YAyBM,G,8BAxBJ1N,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9BM,mBAAA,CASM,OATN2N,YASM,GARJ3N,mBAAA,CAGQ,SAHR4N,YAGQ,G,gBAFN5N,mBAAA,CAA4F;MAArFsB,IAAI,EAAC,OAAO;MAvgB/B,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAugByCF,KAAA,CAAA0L,gBAAgB,CAACkB,aAAa,GAAA1M,MAAA;MAAEW,KAAK,EAAC,WAAW;MAACpC,KAAK,EAAC;oDAAxDuB,KAAA,CAAA0L,gBAAgB,CAACkB,aAAa,E,iCAC3D7N,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAGQ,SAHR8N,YAGQ,G,gBAFN9N,mBAAA,CAA4F;MAArFsB,IAAI,EAAC,OAAO;MA3gB/B,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA2gByCF,KAAA,CAAA0L,gBAAgB,CAACkB,aAAa,GAAA1M,MAAA;MAAEW,KAAK,EAAC,WAAW;MAACpC,KAAK,EAAC;oDAAxDuB,KAAA,CAAA0L,gBAAgB,CAACkB,aAAa,E,iCAC3D7N,mBAAA,CAAiB,cAAX,MAAI,qB,KAIHiB,KAAA,CAAA0L,gBAAgB,CAACkB,aAAa,oB,cAAzC/N,mBAAA,CAWM,OAXNiO,YAWM,GAVJ/N,mBAAA,CASM,OATNgO,YASM,GARJhO,mBAAA,CAGM,c,8BAFJA,mBAAA,CAAoC;MAA7BN,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BM,mBAAA,CAAiF;MAA1EsB,IAAI,EAAC,MAAM;MAphBhC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAohB0CF,KAAA,CAAA0L,gBAAgB,CAACsB,aAAa,GAAA9M,MAAA;MAAEzB,KAAK,EAAC;mDAAtCuB,KAAA,CAAA0L,gBAAgB,CAACsB,aAAa,E,KAE5DjO,mBAAA,CAGM,c,8BAFJA,mBAAA,CAAoC;MAA7BN,KAAK,EAAC;IAAY,GAAC,IAAE,sB,gBAC5BM,mBAAA,CAAiF;MAA1EsB,IAAI,EAAC,MAAM;MAxhBhC,uBAAAjB,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAwhB0CF,KAAA,CAAA0L,gBAAgB,CAACuB,aAAa,GAAA/M,MAAA;MAAEzB,KAAK,EAAC;mDAAtCuB,KAAA,CAAA0L,gBAAgB,CAACuB,aAAa,E,WAxhBxEnO,mBAAA,e,GA8hBMC,mBAAA,CAWM,OAXNmO,YAWM,G,8BAVJnO,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Be,YAAA,CAEiBuC,yBAAA;MAliBzBC,UAAA,EAgiBiChC,KAAA,CAAA0L,gBAAgB,CAACyB,YAAY;MAhiB9D,uBAAA/N,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAgiBiCF,KAAA,CAAA0L,gBAAgB,CAACyB,YAAY,GAAAjN,MAAA;;MAhiB9DiC,OAAA,EAAAC,QAAA,CAgiBgE,MAExDhD,MAAA,UAAAA,MAAA,SAliBRe,gBAAA,CAgiBgE,YAExD,E;MAliBRkC,CAAA;uCAmiBQ7C,YAAA,CAEiBuC,yBAAA;MAriBzBC,UAAA,EAmiBiChC,KAAA,CAAA0L,gBAAgB,CAAC0B,WAAW;MAniB7D,uBAAAhO,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAmiBiCF,KAAA,CAAA0L,gBAAgB,CAAC0B,WAAW,GAAAlN,MAAA;;MAniB7DiC,OAAA,EAAAC,QAAA,CAmiB+D,MAEvDhD,MAAA,UAAAA,MAAA,SAriBRe,gBAAA,CAmiB+D,UAEvD,E;MAriBRkC,CAAA;uCAsiBQ7C,YAAA,CAEiBuC,yBAAA;MAxiBzBC,UAAA,EAsiBiChC,KAAA,CAAA0L,gBAAgB,CAAC2B,gBAAgB;MAtiBlE,uBAAAjO,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAsiBiCF,KAAA,CAAA0L,gBAAgB,CAAC2B,gBAAgB,GAAAnN,MAAA;;MAtiBlEiC,OAAA,EAAAC,QAAA,CAsiBoE,MAE5DhD,MAAA,UAAAA,MAAA,SAxiBRe,gBAAA,CAsiBoE,aAE5D,E;MAxiBRkC,CAAA;;IAAAA,CAAA;6DA4iBIvD,mBAAA,cAAiB,EACjBU,YAAA,CA8BYmI,oBAAA;IA3kBhB3F,UAAA,EA6iBwBhC,KAAA,CAAAsN,eAAe,CAACzF,IAAI;IA7iB5C,uBAAAzI,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA6iBwBF,KAAA,CAAAsN,eAAe,CAACzF,IAAI,GAAA3H,MAAA;IAAE4H,KAAK,EAAC,UAAU;IAAC,cAAY,EAAC,MAAM;IAAEC,SAAO,EAAEzI,QAAA,CAAAiO,gBAAgB;IACtGtF,OAAO,EAAEjI,KAAA,CAAAkI;;IA9iBhB/F,OAAA,EAAAC,QAAA,CA+iBM,MAQM,CARNrD,mBAAA,CAQM,OARNyO,YAQM,G,8BAPJzO,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAKM,OALN0O,YAKM,I,kBAJJ5O,mBAAA,CAGiB0D,SAAA,QArjB3BC,WAAA,CAkjByClD,QAAA,CAAAoO,iBAAiB,EAAzBtK,IAAI;2BAA3B+I,YAAA,CAGiBpK,yBAAA;QAHkCrD,GAAG,EAAE0E,IAAI,CAACK,EAAE;QAljBzEzB,UAAA,EAmjBqBhC,KAAA,CAAAsN,eAAe,CAAClB,aAAa,CAAChJ,IAAI,CAACK,EAAE;QAnjB1D,uBAAAvD,MAAA,IAmjBqBF,KAAA,CAAAsN,eAAe,CAAClB,aAAa,CAAChJ,IAAI,CAACK,EAAE,IAAAvD;;QAnjB1DiC,OAAA,EAAAC,QAAA,CAojBY,MAAe,CApjB3BjC,gBAAA,CAAAgB,gBAAA,CAojBeiC,IAAI,CAACU,IAAI,IAAG,IAAE,GAAA3C,gBAAA,CAAGiC,IAAI,CAACa,EAAE,IAAG,IAChC,gB;QArjBV5B,CAAA;;wCAyjBMtD,mBAAA,CAOM,OAPN4O,YAOM,G,8BANJ5O,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCM,mBAAA,CAIS;MA/jBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA2jByBF,KAAA,CAAAsN,eAAe,CAAC3E,QAAQ,GAAAzI,MAAA;MAAEzB,KAAK,EAAC;2BAC/CI,mBAAA,CAES0D,SAAA,QA9jBnBC,WAAA,CA4jBmCpB,IAAA,CAAA0H,QAAQ,EAAlBC,MAAM;2BAArBlK,mBAAA,CAES;QAF2BH,GAAG,EAAEqK,MAAM,CAACtF,EAAE;QAAG5C,KAAK,EAAEkI,MAAM,CAACtF;0BAC9DsF,MAAM,CAACjF,IAAI,wBA7jB1B8J,YAAA;6EA2jByB5N,KAAA,CAAAsN,eAAe,CAAC3E,QAAQ,E,KAO3C5J,mBAAA,CAQM,OARN8O,YAQM,G,8BAPJ9O,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sBAC9Be,YAAA,CAEiBuC,yBAAA;MAtkBzBC,UAAA,EAokBiChC,KAAA,CAAAsN,eAAe,CAACQ,iBAAiB;MApkBlE,uBAAA1O,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAokBiCF,KAAA,CAAAsN,eAAe,CAACQ,iBAAiB,GAAA5N,MAAA;;MApkBlEiC,OAAA,EAAAC,QAAA,CAokBoE,MAE5DhD,MAAA,UAAAA,MAAA,SAtkBRe,gBAAA,CAokBoE,eAE5D,E;MAtkBRkC,CAAA;uCAukBQ7C,YAAA,CAEiBuC,yBAAA;MAzkBzBC,UAAA,EAukBiChC,KAAA,CAAAsN,eAAe,CAACS,iBAAiB;MAvkBlE,uBAAA3O,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAukBiCF,KAAA,CAAAsN,eAAe,CAACS,iBAAiB,GAAA7N,MAAA;;MAvkBlEiC,OAAA,EAAAC,QAAA,CAukBoE,MAE5DhD,MAAA,UAAAA,MAAA,SAzkBRe,gBAAA,CAukBoE,aAE5D,E;MAzkBRkC,CAAA;;IAAAA,CAAA;6DA6kBIvD,mBAAA,cAAiB,EACjBU,YAAA,CAyCYmI,oBAAA;IAvnBhB3F,UAAA,EA8kBwBhC,KAAA,CAAAgO,mBAAmB,CAACnG,IAAI;IA9kBhD,uBAAAzI,MAAA,SAAAA,MAAA,OAAAc,MAAA,IA8kBwBF,KAAA,CAAAgO,mBAAmB,CAACnG,IAAI,GAAA3H,MAAA;IAAE4H,KAAK,EAAC,QAAQ;IAAC,cAAY,EAAC,MAAM;IAACpI,IAAI,EAAC,sBAAsB;IAACuO,MAAM,EAAN,EAAM;IAChHlG,SAAO,EAAEzI,QAAA,CAAA4O,cAAc;IAAGjG,OAAO,EAAEjI,KAAA,CAAAkI;;IA/kB1C/F,OAAA,EAAAC,QAAA,CAglBM,MAEM,C,8BAFNrD,mBAAA,CAEM;MAFDN,KAAK,EAAC;IAA4C,IACrDM,mBAAA,CAA+C,WAA5C,0CAAwC,E,sBAG7CA,mBAAA,CAQM,OARNoP,YAQM,G,8BAPJpP,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sBAChCM,mBAAA,CAKM,OALNqP,YAKM,I,kBAJJvP,mBAAA,CAGiB0D,SAAA,QA1lB3BC,WAAA,CAulByClD,QAAA,CAAAoO,iBAAiB,EAAzBtK,IAAI;2BAA3B+I,YAAA,CAGiBpK,yBAAA;QAHkCrD,GAAG,EAAE0E,IAAI,CAACK,EAAE;QAvlBzEzB,UAAA,EAwlBqBhC,KAAA,CAAAgO,mBAAmB,CAAC5B,aAAa,CAAChJ,IAAI,CAACK,EAAE;QAxlB9D,uBAAAvD,MAAA,IAwlBqBF,KAAA,CAAAgO,mBAAmB,CAAC5B,aAAa,CAAChJ,IAAI,CAACK,EAAE,IAAAvD;;QAxlB9DiC,OAAA,EAAAC,QAAA,CAylBY,MAAe,CAzlB3BjC,gBAAA,CAAAgB,gBAAA,CAylBeiC,IAAI,CAACU,IAAI,IAAG,IAAE,GAAA3C,gBAAA,CAAGiC,IAAI,CAACa,EAAE,IAAG,IAChC,gB;QA1lBV5B,CAAA;;wCA8lBMtD,mBAAA,CAOM,OAPNsP,YAOM,G,8BANJtP,mBAAA,CAAwC;MAAjCN,KAAK,EAAC;IAAY,GAAC,QAAM,sB,gBAChCM,mBAAA,CAIS;MApmBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAgmByBF,KAAA,CAAAgO,mBAAmB,CAACrF,QAAQ,GAAAzI,MAAA;MAAEzB,KAAK,EAAC;2BACnDI,mBAAA,CAES0D,SAAA,QAnmBnBC,WAAA,CAimBmClD,QAAA,CAAAgP,iBAAiB,EAA3BvF,MAAM;2BAArBlK,mBAAA,CAES;QAFoCH,GAAG,EAAEqK,MAAM,CAACtF,EAAE;QAAG5C,KAAK,EAAEkI,MAAM,CAACtF;0BACvEsF,MAAM,CAACjF,IAAI,IAAG,UAAQ,GAAA3C,gBAAA,CAAG4H,MAAM,CAACC,SAAS,IAAG,QAAM,GAAA7H,gBAAA,CAAG4H,MAAM,CAACE,UAAU,IAAG,KAC9E,uBAnmBVsF,YAAA;6EAgmByBvO,KAAA,CAAAgO,mBAAmB,CAACrF,QAAQ,E,KAO/C5J,mBAAA,CASM,OATNyP,YASM,G,8BARJzP,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CAMS;MA/mBjB,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAymByBF,KAAA,CAAAgO,mBAAmB,CAACS,MAAM,GAAAvO,MAAA;MAAEzB,KAAK,EAAC;sCACjDM,mBAAA,CAAiD;MAAzC8B,KAAK,EAAC;IAAmB,GAAC,QAAM,qBACxC9B,mBAAA,CAA2C;MAAnC8B,KAAK,EAAC;IAAe,GAAC,MAAI,qBAClC9B,mBAAA,CAA6C;MAArC8B,KAAK,EAAC;IAAiB,GAAC,MAAI,qBACpC9B,mBAAA,CAAwC;MAAhC8B,KAAK,EAAC;IAAY,GAAC,MAAI,qBAC/B9B,mBAAA,CAAmC;MAA3B8B,KAAK,EAAC;IAAO,GAAC,MAAI,oB,2CALXb,KAAA,CAAAgO,mBAAmB,CAACS,MAAM,E,KAS7C1P,mBAAA,CAIM,OAJN2P,YAIM,G,8BAHJ3P,mBAAA,CAAsC;MAA/BN,KAAK,EAAC;IAAY,GAAC,MAAI,sB,gBAC9BM,mBAAA,CACuC;MArnB/C,uBAAAK,MAAA,SAAAA,MAAA,OAAAc,MAAA,IAonB2BF,KAAA,CAAAgO,mBAAmB,CAACW,WAAW,GAAAzO,MAAA;MAAEzB,KAAK,EAAC,cAAc;MAACmQ,IAAI,EAAC,GAAG;MAC/ErO,WAAW,EAAC;mDADKP,KAAA,CAAAgO,mBAAmB,CAACW,WAAW,E;IApnB1DtM,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}