/**
 * 通知工具类
 * 用于在Vue 3中替代$root.$emit的通知系统
 */

import { getCurrentInstance } from 'vue'

/**
 * 获取事件总线实例
 */
function getEventBus() {
  const instance = getCurrentInstance()
  if (instance && instance.appContext.config.globalProperties.$eventBus) {
    return instance.appContext.config.globalProperties.$eventBus
  }
  
  // 如果在组件外部调用，需要手动传入eventBus实例
  console.warn('无法获取事件总线实例，请确保在Vue组件内部调用或手动传入eventBus')
  return null
}

/**
 * 显示通知
 * @param {Object} options 通知选项
 * @param {string} options.type 通知类型: success, error, warning, info
 * @param {string} options.title 通知标题
 * @param {string} options.message 通知消息
 * @param {Array} options.actions 操作按钮数组
 * @param {boolean} options.autoClose 是否自动关闭
 * @param {number} options.duration 显示时长(ms)
 */
export function showNotification(options) {
  const eventBus = getEventBus()
  if (eventBus) {
    eventBus.emit('show-notification', options)
  }
}

/**
 * 显示成功通知
 * @param {string} message 消息内容
 * @param {string} title 标题
 * @param {Object} options 其他选项
 */
export function showSuccess(message, title = '成功', options = {}) {
  showNotification({
    type: 'success',
    title,
    message,
    ...options
  })
}

/**
 * 显示错误通知
 * @param {string} message 消息内容
 * @param {string} title 标题
 * @param {Object} options 其他选项
 */
export function showError(message, title = '错误', options = {}) {
  showNotification({
    type: 'error',
    title,
    message,
    autoClose: false, // 错误通知默认不自动关闭
    ...options
  })
}

/**
 * 显示警告通知
 * @param {string} message 消息内容
 * @param {string} title 标题
 * @param {Object} options 其他选项
 */
export function showWarning(message, title = '警告', options = {}) {
  showNotification({
    type: 'warning',
    title,
    message,
    ...options
  })
}

/**
 * 显示信息通知
 * @param {string} message 消息内容
 * @param {string} title 标题
 * @param {Object} options 其他选项
 */
export function showInfo(message, title = '信息', options = {}) {
  showNotification({
    type: 'info',
    title,
    message,
    ...options
  })
}

/**
 * 在组件外部使用的通知方法
 * 需要手动传入eventBus实例
 */
export class NotificationManager {
  constructor(eventBus) {
    this.eventBus = eventBus
  }

  showNotification(options) {
    this.eventBus.emit('show-notification', options)
  }

  showSuccess(message, title = '成功', options = {}) {
    this.showNotification({
      type: 'success',
      title,
      message,
      ...options
    })
  }

  showError(message, title = '错误', options = {}) {
    this.showNotification({
      type: 'error',
      title,
      message,
      autoClose: false,
      ...options
    })
  }

  showWarning(message, title = '警告', options = {}) {
    this.showNotification({
      type: 'warning',
      title,
      message,
      ...options
    })
  }

  showInfo(message, title = '信息', options = {}) {
    this.showNotification({
      type: 'info',
      title,
      message,
      ...options
    })
  }
}

// 导出默认的通知方法
export default {
  showNotification,
  showSuccess,
  showError,
  showWarning,
  showInfo,
  NotificationManager
}
